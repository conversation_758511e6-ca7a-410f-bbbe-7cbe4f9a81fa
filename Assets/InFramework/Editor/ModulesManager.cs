using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class ModulesManager : Editor {
    private const string mainMenu = "InWave/Modules/";
    #region LunarConsole

    private const string menuLogs =mainMenu+ "Enable Logs";
    private const string defineEnableLogs = "ENABLE_LOGS";
    private const string defineAnalyticsDisabled = "LUNAR_CONSOLE_ANALYTICS_DISABLED";
    private const string folderLunarConsole = "LunarConsole";

    [MenuItem(menuLogs, true)]
    public static bool DoEnableLogsValidate() {
        bool isDefine = IsContainsDefine(defineEnableLogs);
        UnityEditor.Menu.SetChecked(menuLogs, isDefine);
        return true;
    }

    [MenuItem(menuLogs)]
    static void DoEnableLogs() {
        try {
            if (UnityEditor.Menu.GetChecked(menuLogs)) { //Uncheck => disable logs
                Debug.Log("Remove define: " + defineEnableLogs);
                AddDefine(defineEnableLogs, false);
                AddDefine(defineAnalyticsDisabled, false);

                Debug.Log("Disable folder: " + folderLunarConsole);
                EnableFolder(folderLunarConsole, false);

            } else { //Check => enable logs
                Debug.Log("Add define: " + defineEnableLogs);
                AddDefine(defineEnableLogs, true);
                AddDefine(defineAnalyticsDisabled, true);

                Debug.Log("Enable folder: " + folderLunarConsole);
                EnableFolder(folderLunarConsole, true);
            }

        } catch (Exception e) {
            Debug.LogException(e);
        }
    }

    #endregion

    #region Core

    private static void EnableFolder(string folderName, bool isEnable) {
        string OnLinarConsole = Path.Combine(Application.dataPath, folderName);
        string OffLinarConsole = Path.Combine(Application.dataPath, "." + folderName);

        if (isEnable) {
            if (Directory.Exists(OffLinarConsole)) {
                Directory.Move(OffLinarConsole, OnLinarConsole);
            }
        } else {
            if (Directory.Exists(OnLinarConsole)) {
                Directory.Move(OnLinarConsole, OffLinarConsole);
            }
        }

        AssetDatabase.Refresh();
    }

    private static void AddDefine(string define, bool isAdd) {
        string definesString =
            PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
        List<string> allDefines = definesString.Split(';').ToList();

        if (isAdd) {
            if (!allDefines.Contains(define)) {
                allDefines.Add(define);
            }
        } else {
            if (allDefines.Contains(define)) {
                allDefines.Remove(define);
            }
        }

        PlayerSettings.SetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup,
            string.Join(";", allDefines.ToArray()));

        AssetDatabase.SaveAssets();
    }

    private static bool IsContainsDefine(string define) {
        string definesString =
            PlayerSettings.GetScriptingDefineSymbolsForGroup(EditorUserBuildSettings.selectedBuildTargetGroup);
        List<string> allDefines = definesString.Split(';').ToList();
        return allDefines.Contains(define);
    }

    #endregion
}