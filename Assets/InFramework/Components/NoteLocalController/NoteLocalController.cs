using RhythmTool;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using GameCore.Scripts;
using GamePlay.Levels;
using UnityEngine.Networking;

public class NoteLocalController : Singleton<NoteLocalController> {
    //public
    [SerializeField]
    private RhythmAnalyzer analyzer;

    [SerializeField]
    private OnsetDetector onsetDetector;

    [SerializeField]
    private BeatTracker beatTracker;
    
    //private
    [HideInInspector]
    public bool isLoading;

    private RhythmData rhythmData;
    private Coroutine getNoteLocal;
    private Coroutine getNoteData;

    private void ReadLocalNote(AudioClip audioClip, Action<float> percentProcessed, Action<List<float>> timeNotes,
        Action<string> err = null) {

        //active onsetDetector
        onsetDetector.enabled = true;
        beatTracker.enabled = false;
        
        rhythmData = analyzer.Analyze(audioClip);
        if (getNoteLocal != null)
            StopCoroutine(getNoteLocal);
        getNoteLocal = StartCoroutine(GetNoteLocal(percentProcessed, timeNotes, err));
    }

    private readonly WaitForSeconds waitAnalyzerDone = new WaitForSeconds(0.2f);

    IEnumerator GetNoteLocal(Action<float> percentProcessed, Action<List<float>> timeNotes, Action<string> err = null) {
        while (!analyzer.isDone) {
            percentProcessed?.Invoke(analyzer.progress);
            yield return waitAnalyzerDone;
        }

        List<float> timeLocalSong = new List<float>();
        try {
            Track<Onset> track = rhythmData.GetTrack<Onset>();
            Debug.Log("Track Finish " + track.count);

            int length = track.count;
            for (int i = 0; i < length; i++) {
                timeLocalSong.Add(track[i].timestamp);
            }

        } catch (Exception e) {
            err?.Invoke(e.ToString());
        }

        timeNotes?.Invoke(timeLocalSong);
    }

    public void StartLoadFromLocalPath(string pathLocal, Action<float> percentProcessed, Action<List<NoteData>> notes,
        Action<string> err) {
        isLoading = true;

        if (getNoteData != null) {
            StopCoroutine(getNoteData);
        }

        pathLocal = "file://" + pathLocal;
        getNoteData = StartCoroutine(GetNoteData(pathLocal, percentProcessed, notes, err));
    }

    private IEnumerator GetNoteData(string path, Action<float> percentProcessed, Action<List<NoteData>> notes,
        Action<string> err) {
        if (string.IsNullOrEmpty(path)) {
            isLoading = false;
            notes?.Invoke(null);
            err?.Invoke("[GetNoteData] path null or empty");
            yield break;
        }

        UnityWebRequest www = UnityWebRequestMultimedia.GetAudioClip(path, AudioType.UNKNOWN);
        yield return www.SendWebRequest();

        if (www.isNetworkError) {
            isLoading = false;
            notes?.Invoke(null);
            err?.Invoke("[GetNoteData] Network error!");
            yield break;
        }

        AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
        if (clip.Equals(null) || clip.samples == 0 || clip.length <= 0) {
            isLoading = false;
            notes?.Invoke(null);
            err?.Invoke("[GetNoteData] Clip is NULL");
            yield break;
        }

        ReadLocalNote(clip, percentProcessed, (listTime) => {
            if (listTime.Count == 0) {
                isLoading = false;
                notes?.Invoke(null);
                err?.Invoke("[GetNoteData] Total note time = 0!");

            } else {
                List<NoteData> notesData = new List<NoteData>();
                int totalNotes = listTime.Count;
                for (int index = 0; index < totalNotes; index++) {
                    NoteData noteDataLocal = new NoteData {timeAppear = listTime[index] - RemoteConfigBase.instance.rhythmToolDelay }; //Fix Delay
                    notesData.Add(noteDataLocal);
                }

                isLoading = false;
                notes?.Invoke(notesData);
                Debug.Log("[GetNoteData] total note of " + path + " is " + notesData.Count);
            }
        }, err);
    }

    #region GetBPMData
    public IEnumerator GetBPMData(string path, Action<float> percentProcessed, Action<float[]> dataBPM, Action<string> err) {
        if (string.IsNullOrEmpty(path)) {
            dataBPM?.Invoke(null);
            err?.Invoke("[GetBeatData] path null or empty");
            yield break;
        }
        
        path = Util.EncodePath(path);

		UnityWebRequest www = UnityWebRequestMultimedia.GetAudioClip(path, AudioType.UNKNOWN);
        yield return www.SendWebRequest();

        if (www.result != UnityWebRequest.Result.Success) {
            dataBPM?.Invoke(null);
            err?.Invoke("[GetBeatData] Error:" + www.error);
            yield break;
        }

        AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
        if (clip == null || clip.samples == 0 || clip.length <= 0) {
            dataBPM?.Invoke(null);
            err?.Invoke("[GetBeatData] Clip is NULL");
            yield break;
        }

        StartCoroutine(ReadLocalBeat(clip, percentProcessed, (bpm) => {
            if (bpm == null) {
                dataBPM?.Invoke(null);
                err?.Invoke("[GetBeatData] Total note time = 0!");

            } else {
                dataBPM?.Invoke(bpm);
                Debug.Log("[GetBeatData] bpm of " + path + " is " + bpm[0]);
            }
        }, err));
    }

    private IEnumerator ReadLocalBeat(AudioClip audioClip, Action<float> percentProcessed, Action<float[]> bpm,
        Action<string> err = null) {
        
        //active beatTracker
        onsetDetector.enabled = false;
        beatTracker.enabled = true;
        
        rhythmData = analyzer.Analyze(audioClip);
        
        while (!analyzer.isDone) {
            percentProcessed?.Invoke(analyzer.progress);
            yield return waitAnalyzerDone;
        }

        try {
            Track<Beat> beats = rhythmData.GetTrack<Beat>();
            Debug.Log("Track Finish " + beats.count);

            int count = beats.count;
            float totalBPM = 0;
            for (int i = 0; i < count; i++) {
                totalBPM += beats[i].bpm;
            }

            float tmpBpm = totalBPM / count;
            float startTime = beats[0].timestamp;

            bpm?.Invoke(new[] {tmpBpm, startTime});

        } catch (Exception e) {
            err?.Invoke(e.ToString());
            bpm?.Invoke(null);
        }
    }
    #endregion

    public static NoteLocalController Load() {
        if (isInstanced) {
            return instance;
        }

        NoteLocalController load = ResourcesManager.LoadComponent<NoteLocalController>(ResourcesPath.NoteLocalController);
        return load;
    }
}