using System.Collections;
using System.Globalization;
using UnityEngine;
using UnityEngine.UI;

public class ProcessBarScript : MonoBehaviour {
    #region Fields

    //public
    [SerializeField]
    private Text txtTitle;

    [SerializeField]
    private Text txtProcess;

    [SerializeField]
    private Slider sliderProcess;

    //private
    private Coroutine startAnimTitle;
    private readonly NumberFormatInfo numberFormatInfo = NumberFormatInfo.CurrentInfo;
    private const string FormatPercent = "P";

    #endregion

    #region Unity Method

    private void OnDisable() {
        if (startAnimTitle != null) {
            StopCoroutine(startAnimTitle);
        }
    }

    #endregion

    #region Method

    public void Show(float percent = 0) {
        gameObject.SetActive(true);
        txtTitle.text = "Importing";

        if (startAnimTitle != null) {
            StopCoroutine(startAnimTitle);
        }

        startAnimTitle = StartCoroutine(AnimTitle(txtTitle));

        SetProcess(percent);
    }

    public void Hide() {
        if (startAnimTitle != null) {
            StopCoroutine(startAnimTitle);
        }

        gameObject.SetActive(false);
    }

    public void SetProcess(float percent) {
        percent = Mathf.Clamp01(percent);
        if (txtProcess) {
            txtProcess.text = percent.ToString(FormatPercent, numberFormatInfo);
        }

        if (sliderProcess) {
            sliderProcess.value = percent;
        }
    }

    private IEnumerator AnimTitle(Text text) {
        yield return null;

        WaitForSeconds waitForSeconds = new WaitForSeconds(0.5f);
        string origText = text.text.Replace(".", "");
        string str01 = origText + ".";
        string str02 = origText + "..";
        string str03 = origText + "...";

        int index = 0;
        while (true) {
            switch (index % 3) {
                case 0:
                    text.text = str01;
                    break;
                case 1:
                    text.text = str02;
                    break;
                case 2:
                    text.text = str03;
                    break;
            }

            index++;
            yield return waitForSeconds;
        }
    }

    #endregion
}