<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sirenix.Utilities.Editor</name>
    </assembly>
    <members>
        <member name="T:Sirenix.Utilities.Editor.EditorIcon">
            <summary>
            Icon for using in editor GUI.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcon.Raw">
            <summary>
            Gets the raw input icon texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcon.Highlighted">
            <summary>
            Gets the icon's highlighted texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcon.Active">
            <summary>
            Gets the icon's active texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcon.Inactive">
            <summary>
            Gets the icon's inactive texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcon.ActiveGUIContent">
            <summary>
            Gets a GUIContent object with the active texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcon.InactiveGUIContent">
            <summary>
            Gets a GUIContent object with the inactive texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcon.HighlightedGUIContent">
            <summary>
            Gets a GUIContent object with the highlighted texture.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EditorIcon.Draw(UnityEngine.Rect)">
            <summary>
            Draws the icon in a square rect, with a custom shader that makes the icon look better when down-scaled.
            This also handles mouseover effects, and linier color spacing.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EditorIcon.Draw(UnityEngine.Rect,System.Single)">
            <summary>
            Draws the icon in a square rect, with a custom shader that makes the icon look better when down-scaled.
            This also handles mouseover effects, and linier color spacing.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EditorIcon.Draw(UnityEngine.Rect,UnityEngine.Texture)">
            <summary>
            Draws the icon in a square rect, with a custom shader that makes the icon look better when down-scaled.
            This also handles mouseover effects, and linier color spacing.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.EditorIcons">
            <summary>
            Collection of EditorIcons for use in GUI drawing.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Airplane">
            <summary>
            Gets an icon of an airplane symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.AlertCircle">
            <summary>
            Gets an icon of an alert circle symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.AlertTriangle">
            <summary>
            Gets an icon of an alert triangle symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ArrowDown">
            <summary>
            Gets an icon of an arrow down symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ArrowLeft">
            <summary>
            Gets an icon of an arrow left symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ArrowRight">
            <summary>
            Gets an icon of an arrow right symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ArrowUp">
            <summary>
            Gets an icon of an arrow up symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Bell">
            <summary>
            Gets an icon of a bell symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Car">
            <summary>
            Gets an icon of a car symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Char1">
            <summary>
            Gets an icon of a char1 symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Char2">
            <summary>
            Gets an icon of a char2 symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Char3">
            <summary>
            Gets an icon of a char3 symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.CharGraph">
            <summary>
            Gets an icon of a char graph symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Checkmark">
            <summary>
            Gets an icon of a checkmark symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Clock">
            <summary>
            Gets an icon of a clock symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Clouds">
            <summary>
            Gets an icon of a clouds symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.CloudsRainy">
            <summary>
            Gets an icon of a clouds rainy symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.CloudsRainySunny">
            <summary>
            Gets an icon of a clouds rainy sunny symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.CloudsRainyThunder">
            <summary>
            Gets an icon of a clouds rainy thunder symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.CloudsThunder">
            <summary>
            Gets an icon of a clouds thunder symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Crosshair">
            <summary>
            Gets an icon of a crosshair symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Cut">
            <summary>
            Gets an icon of a cut symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.DayCalendar">
            <summary>
            Gets an icon of a day calendar symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Download">
            <summary>
            Gets an icon of a download symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Eject">
            <summary>
            Gets an icon of an eject symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.EyeDropper">
            <summary>
            Gets an icon of an eye dropper symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Female">
            <summary>
            Gets an icon of a female symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.File">
            <summary>
            Gets an icon of a file symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.FileCabinet">
            <summary>
            Gets an icon of a file cabinet symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.FinnishBanner">
            <summary>
            Gets an icon of a finnish banner symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Flag">
            <summary>
            Gets an icon of a flag symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.FlagFinnish">
            <summary>
            Gets an icon of a flag finnish symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Folder">
            <summary>
            Gets an icon of a folder symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.FolderBack">
            <summary>
            Gets an icon of a folder back symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.GKey">
            <summary>
            Gets an icon of a gKey symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Globe">
            <summary>
            Gets an icon of a globe symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.GridBlocks">
            <summary>
            Gets an icon of a grid blocks symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.GridImageText">
            <summary>
            Gets an icon of a grid image text symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.GridImageTextList">
            <summary>
            Gets an icon of a grid image text list symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.GridLayout">
            <summary>
            Gets an icon of a grid layout symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.HamburgerMenu">
            <summary>
            Gets an icon of a hamburger menu symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.House">
            <summary>
            Gets an icon of a house symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Image">
            <summary>
            Gets an icon of an image symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ImageCollection">
            <summary>
            Gets an icon of an image collection symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Info">
            <summary>
            Gets an icon of an info symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Letter">
            <summary>
            Gets an icon of a letter symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.LightBulb">
            <summary>
            Gets an icon of a light bulb symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Link">
            <summary>
            Gets an icon of a link symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.List">
            <summary>
            Gets an icon of a list symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.LoadingBar">
            <summary>
            Gets an icon of a loading bar symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.LockLocked">
            <summary>
            Gets an icon of a lock locked symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.LockUnlocked">
            <summary>
            Gets an icon of a lock unlocked symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.LockUnloacked">
            <summary>
            Gets an icon of a lock unlocked symbol. Obsolete; use the correctly spelled LockUnlocked instead.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.MagnifyingGlass">
            <summary>
            Gets an icon of a magnifying glass symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Male">
            <summary>
            Gets an icon of a male symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Marker">
            <summary>
            Gets an icon of a marker symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Maximize">
            <summary>
            Gets an icon of a maximize symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Microphone">
            <summary>
            Gets an icon of a microphone symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Minimize">
            <summary>
            Gets an icon of a minimize symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Minus">
            <summary>
            Gets an icon of a minus symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.MobilePhone">
            <summary>
            Gets an icon of a mobile phone symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Money">
            <summary>
            Gets an icon of a money symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Move">
            <summary>
            Gets an icon of a move symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.MultiUser">
            <summary>
            Gets an icon of a multi user symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Next">
            <summary>
            Gets an icon of a next symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.PacmanGhost">
            <summary>
            Gets an icon of a pacman ghost symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Paperclip">
            <summary>
            Gets an icon of a paperclip symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Pause">
            <summary>
            Gets an icon of a pause symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Pen">
            <summary>
            Gets an icon of a pen symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.PenAdd">
            <summary>
            Gets an icon of a pen add symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.PenMinus">
            <summary>
            Gets an icon of a pen minus symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Play">
            <summary>
            Gets an icon of a play symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Plus">
            <summary>
            Gets an icon of a plus symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Podium">
            <summary>
            Gets an icon of a podium symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Previous">
            <summary>
            Gets an icon of a previous symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ReceptionSignal">
            <summary>
            Gets an icon of a reception signal symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Redo">
            <summary>
            Gets an icon of a redo symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Refresh">
            <summary>
            Gets an icon of a refresh symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Rotate">
            <summary>
            Gets an icon of a rotate symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Ruler">
            <summary>
            Gets an icon of a ruler symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.RulerRect">
            <summary>
            Gets an icon of a ruler rect symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.SettingsCog">
            <summary>
            Gets an icon of a settings cog symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ShoppingBasket">
            <summary>
            Gets an icon of a shopping basket symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ShoppingCart">
            <summary>
            Gets an icon of a shopping cart symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.SingleUser">
            <summary>
            Gets an icon of a single user symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.SmartPhone">
            <summary>
            Gets an icon of a smart phone symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Sound">
            <summary>
            Gets an icon of a sound symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.SpeechBubbleRound">
            <summary>
            Gets an icon of a speech bubble round symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.SpeechBubbleSquare">
            <summary>
            Gets an icon of a speech bubble square symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.SpeechBubblesRound">
            <summary>
            Gets an icon of a speech bubbles round symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.SpeechBubblesSquare">
            <summary>
            Gets an icon of a speech bubbles square symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.StarPointer">
            <summary>
            Gets an icon of a star pointer symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Stop">
            <summary>
            Gets an icon of a stop symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Stretch">
            <summary>
            Gets an icon of a stretch symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Table">
            <summary>
            Gets an icon of a table symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Tag">
            <summary>
            Gets an icon of a tag symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TestTube">
            <summary>
            Gets an icon of a test tube symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Timer">
            <summary>
            Gets an icon of a timer symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TrafficStopLight">
            <summary>
            Gets an icon of a traffic stop light symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Transparent">
            <summary>
            Gets an icon of a transparent symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Tree">
            <summary>
            Gets an icon of a tree symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TriangleDown">
            <summary>
            Gets an icon of a triangle down symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TriangleLeft">
            <summary>
            Gets an icon of a triangle left symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TriangleRight">
            <summary>
            Gets an icon of a triangle right symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TriangleUp">
            <summary>
            Gets an icon of a triangle up symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Undo">
            <summary>
            Gets an icon of an undo symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.Upload">
            <summary>
            Gets an icon of an upload symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.WifiSignal">
            <summary>
            Gets an icon of a wifi signal symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.X">
            <summary>
            Gets an icon of an x symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TestInconclusive">
            <summary>
            Gets a texture of a test inconclusive symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TestFailed">
            <summary>
            Gets a texture of a test failed symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TestNormal">
            <summary>
            Gets a texture of a test normal symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.TestPassed">
            <summary>
            Gets a texture of a test passed symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ConsoleInfoIcon">
            <summary>
            Gets a texture of a console info icon symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ConsoleWarnicon">
            <summary>
            Gets a texture of a console warnicon symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.ConsoleErroricon">
            <summary>
            Gets a texture of a console error icon symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.OdinInspectorLogo">
            <summary>
            Gets a texture of an odin inspector logo symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.UnityLogo">
            <summary>
            Gets a texture of a scene asset icon symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.UnityGameObjectIcon">
            <summary>
            Gets an icon representing a GameObject.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.UnityInfoIcon">
            <summary>
            Gets an icon of a unity info icon.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.UnityWarningIcon">
            <summary>
            Gets an icon of a unity warning icon.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.UnityErrorIcon">
            <summary>
            Gets an icon of a unity error icon.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.EditorIcons.UnityFolderIcon">
            <summary>
            Gets an icon of a unity folder.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.LazyEditorIcon">
            <summary>
            Lazy loading Editor Icon.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.LazyEditorIcon.#ctor(System.Int32,System.Int32,System.String)">
            <summary>
            Loads an EditorIcon from the spritesheet.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.LazyEditorIcon.Highlighted">
            <summary>
            Gets the icon's highlight texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.LazyEditorIcon.Active">
            <summary>
            Gets the icon's active texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.LazyEditorIcon.Inactive">
            <summary>
            Gets the icon's inactive texture.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.LazyEditorIcon.Raw">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.OdinEditorResources.OdinLogo">
            <summary>
            Gets a texture of an odin logo symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.OdinEditorResources.OdinInspectorLogo">
            <summary>
            Gets a texture of an odin inspector logo symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.OdinEditorResources.OdinSerializerLogo">
            <summary>
            Gets a texture of an odin serializer logo symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.OdinEditorResources.OdinValidatorLogo">
            <summary>
            Gets a texture of an odin validator logo symbol.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.OdinEditorResources.OdinValidatorLogoBlack">
            <summary>
            Gets a texture of an odin validator black symbol.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.Expressions.ExpressionUtility">
            <summary>
            Utility for parsing and emitting expression delegates.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ExpressionCacheClearTimeSeconds">
            <summary>
            The time that the expression cache waits to clear expressions
            since the last time they have been used.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseExpression(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and tries to emit a delegate method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted delegate if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseExpression(System.String,System.Boolean,System.Type,System.Type[],System.String@,System.Boolean)">
            <summary>Parses an expression and tries to emit a delegate method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="parameters">The parameters of the expression delegate.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted delegate if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseExpression(System.String,System.Boolean,System.Type,System.Type[],System.String[],System.String@,System.Boolean)">
            <summary>Parses an expression and tries to emit a delegate method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="parameters">The parameters of the expression delegate.</param>
            <param name="parameterNames">The names of the expression's parameters, for use with the named parameter syntax.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted delegate if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseExpression(System.String,Sirenix.Utilities.Editor.Expressions.EmitContext,System.String@,System.Boolean)">
            <summary>Parses an expression and tries to emit a delegate method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="context">The emit context.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted delegate if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseExpression(System.String,Sirenix.Utilities.Editor.Expressions.EmitContext,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and tries to emit a delegate of the specified type.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="context">The emit context.</param>
            <param name="delegateType">The type of the delegate to emit.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted delegate if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``1(System.String,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``2(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``3(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``4(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``5(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``6(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``7(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``8(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``9(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseFunc``10(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionFunc method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionFunc if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction(System.String,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``1(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``2(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``3(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``4(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``5(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``6(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``7(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``8(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Expressions.ExpressionUtility.ParseAction``9(System.String,System.Boolean,System.Type,System.String@,System.Boolean)">
            <summary>Parses an expression and emits an ExpressionAction method.</summary>
            <param name="expression">The expression to parse.</param>
            <param name="isStatic">Indicates if the expression should be static instead of instanced.</param>
            <param name="contextType">The context type for the execution of the expression.</param>
            <param name="errorMessage">Output for any errors that may occur.</param>
            <param name="richTextError">If <c>true</c> then error message will be formatted with color tags. Otherwise, the error message will be formatted with text only.</param>
            <returns>Returns the emitted ExpressionAction if the expression is compiled successfully. Otherwise, null.</returns>
        </member>
        <member name="T:Sirenix.Utilities.Editor.AsyncProgressBar">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.AsyncProgressBar.Progress">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.AsyncProgressBar.ProgressInfo">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.AsyncProgressBar.IsShowing">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AsyncProgressBar.Display(System.String,System.Single)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AsyncProgressBar.Clear">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.DragAndDropManager">
            <summary>
            <para>This class is due to undergo refactoring. Use the new DragAndDropUtilities instead.</para>
            </summary>
            <seealso cref="T:Sirenix.Utilities.Editor.DragAndDropUtilities"/>
        </member>
        <member name="T:Sirenix.Utilities.Editor.DragAndDropMethods">
            <summary>
            This class is due to undergo refactoring.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DragAndDropMethods.Reference">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DragAndDropMethods.Move">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DragAndDropMethods.Copy">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.DragAndDropUtilities">
            <summary>
            Drag and drop utilities for both Unity and non-unity objects.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.DragAndDropUtilities.OnDragStartMouseScreenPos">
            <summary>
            Gets the position from where the last drag started from in screen space.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.DragAndDropUtilities.MouseDragOffset">
            <summary>
            Gets the delta position between the currrent mouse position and where the last drag originated from.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.DragAndDropUtilities.HoveringAcceptedDropZone">
            <summary>
            Gets the hovering accepted drop zone ID.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.DragAndDropUtilities.IsDragging">
            <summary>
            Gets a value indicating whether an instance is currently being dragged.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.DragAndDropUtilities.CurrentDragId">
            <summary>
            Gets the currently dragging identifier.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.DragAndDropUtilities.CurrentDropId">
            <summary>
            Gets the current hovering drop zone identifier.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.GetDragAndDropId(UnityEngine.Rect)">
            <summary>
            Gets a more percistent id for drag and drop.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.ObjectPickerZone(UnityEngine.Rect,System.Object,System.Type,System.Boolean,System.Int32)">
            <summary>
            Draws a objectpicker button in the given rect. This one is designed to look good on top of DrawDropZone().
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.ObjectPickerZone``1(UnityEngine.Rect,``0,System.Boolean,System.Int32)">
            <summary>
            Draws a objectpicker butter, in the given rect. This one is designed to look good on top of DrawDropZone().
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DrawDropZone(UnityEngine.Rect,System.Object,UnityEngine.GUIContent,System.Int32)">
            <summary>
            Draws the graphics for a DropZone.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DrawDropZone(UnityEngine.Rect,UnityEngine.Texture,UnityEngine.GUIContent,System.Int32)">
            <summary>
            Draws the graphics for a DropZone.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DragAndDropZone(UnityEngine.Rect,System.Object,System.Type,System.Boolean,System.Boolean)">
            <summary>
            A draggable zone for both Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone(UnityEngine.Rect,System.Object,System.Type,System.Boolean,System.Int32)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone(UnityEngine.Rect,System.Object,System.Type,System.Int32)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone(UnityEngine.Rect,System.Object,System.Type)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone(UnityEngine.Rect,System.Object,System.Type,System.Boolean)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone``1(UnityEngine.Rect,``0,System.Boolean,System.Int32)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone``1(UnityEngine.Rect,``0,System.Int32)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone``1(UnityEngine.Rect,``0,System.Boolean)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DropZone``1(UnityEngine.Rect,``0)">
            <summary>
            A drop zone area for bot Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DisallowedDropAreaForNextDragZone(UnityEngine.Rect)">
            <summary>
            Disalloweds the drop area for next drag zone. Follow this function call by a DragZone.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DragZone(UnityEngine.Rect,System.Object,System.Type,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            A draggable zone for both Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DragZone(UnityEngine.Rect,System.Object,System.Type,System.Boolean,System.Boolean)">
            <summary>
            A draggable zone for both Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DragZone``1(UnityEngine.Rect,``0,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            A draggable zone for both Unity and non-unity objects.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.DragAndDropUtilities.DragZone``1(UnityEngine.Rect,``0,System.Boolean,System.Boolean)">
            <summary>
            A draggable zone for both Unity and non-unity objects.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.DragHandle">
            <summary>
            This class is due to undergo refactoring.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.DropEvents">
            <summary>
            This class is due to undergo refactoring.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DropEvents.None">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DropEvents.Referenced">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DropEvents.Moved">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DropEvents.Copied">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.DropEvents.Canceled">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.DropZoneHandle">
            <summary>
            This class is due to undergo refactoring.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.OnDragFinnished">
            <summary>
            This class is due to undergo refactoring.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.EventExtensions">
            <summary>
            Collection of extension methods for <see cref="T:UnityEngine.Event"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnMouseDown(UnityEngine.Event,System.Int32,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user presses the specified mouse button.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="mouseButton">The mouse button the user has to press.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on mouse down events with the specified button. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnMouseDown(UnityEngine.Event,UnityEngine.Rect,System.Int32,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user clicks a rect with the mouse.
            </summary>
            <param name="current">The event.</param>
            <param name="rect">The rect the user can click on.</param>
            <param name="mouseButton">The button the user has to press.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on mouse down events with the specified button. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnMouseUp(UnityEngine.Event,System.Int32,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user releases the specified mouse button.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="mouseButton">The mouse button the user has to release.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on mouse up events, with the specified button. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnMouseUp(UnityEngine.Event,UnityEngine.Rect,System.Int32,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user releases the specified mouse button over the specified rect.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="rect">The rect the user has to release the mouse button over.</param>
            <param name="mouseButton">The mouse button the user has to release.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on mouse up events, with the specified button and over the specified rect. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnLeftClick(UnityEngine.Event,UnityEngine.Rect,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user left clicks a rect.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="rect">The rect the user can click.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on left click events, on the specified rect. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnContextClick(UnityEngine.Event,UnityEngine.Rect,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user right clicks a rect.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="rect">The rect the user can right click.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on context click events, on the specified rect. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnKeyDown(UnityEngine.Event,UnityEngine.KeyCode,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user presses the specified key.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="key">The key the user has to press.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on key down events with the specified key code. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnKeyUp(UnityEngine.Event,UnityEngine.KeyCode,System.Boolean)">
            <summary>
            Returns <c>true</c> when the user releases the specified key.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="key">The key the user has to release.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on key up events with the specified key code. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnMouseMoveDrag(UnityEngine.Event,System.Boolean)">
            <summary>
            Returns <c>true</c> whene the user moves or drags the mouse.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="useEvent">If <c>true</c> then the method will call <see cref="M:UnityEngine.Event.Use"/> on the event.</param>
            <returns><c>true</c> on mouse move or mouse drag events. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.IsHovering(UnityEngine.Event,UnityEngine.Rect)">
            <summary>
            Returns <c>true</c> when the user hovers the mouse over the specified rect.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="rect">The rect the user can hover.</param>
            <returns><c>true</c> on any event where the mouse is hovering the specified rect. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnRepaint(UnityEngine.Event)">
            <summary>
            Returns <c>true</c> on repaint events.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <returns><c>true</c> on repaint events. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnLayout(UnityEngine.Event)">
            <summary>
            Returns <c>true</c> on layout events.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <returns><c>true</c> on layout events. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.EventExtensions.OnEventType(UnityEngine.Event,UnityEngine.EventType)">
            <summary>
            Returns <c>true</c> on the specified event.
            </summary>
            <param name="current">The <see cref="T:UnityEngine.Event"/>.</param>
            <param name="eventType">The required event type.</param>
            <returns><c>true</c> on the specified event. Otherwise <c>false</c>.</returns>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GenericMenuExtensions">
            <summary>
            Collection of extension methods for <see cref="T:UnityEditor.GenericMenu"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GenericMenuExtensions.RemoveMenuItems(UnityEditor.GenericMenu,System.String)">
            <summary>
            Removes all menu items with a given name from the GenericMenu.
            </summary>
            <param name="menu">The GenericMenu to remove items from.</param>
            <param name="name">The name of the items to remove.</param>
            <returns>True if any items were removed, otherwise false.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GenericMenuExtensions.ReplaceOrAdd(UnityEditor.GenericMenu,System.String,System.Boolean,UnityEditor.GenericMenu.MenuFunction)">
            <summary>
            Replaces the first found menu item with a given name with a new menu item, or if no such element is found, adds a new one.
            </summary>
            <param name="menu">The GenericMenu to replace items in.</param>
            <param name="name">The name of the items to remove.</param>
            <param name="func">The func to replace or add.</param>
            <param name="on">The on value to set the new menu item with.</param>
            <returns>True if an item was replaced, otherwise false.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GenericMenuExtensions.ReplaceOrAdd(UnityEditor.GenericMenu,System.String,System.Boolean,UnityEditor.GenericMenu.MenuFunction2,System.Object)">
            <summary>
            Replaces the first found menu item with a given name with a new menu item, or if no such element is found, adds a new one.
            </summary>
            <param name="menu">The GenericMenu to replace items in.</param>
            <param name="name">The name of the items to remove.</param>
            <param name="on">The on value to set the new menu item with.</param>
            <param name="func2">The func to replace or add.</param>
            <param name="userData">The user data.</param>
            <returns>
            True if an item was replaced, otherwise false.
            </returns>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUIClipInfo">
            <summary>
            Emitted wrapper for the internal "UnityEngine.GUIClip" class.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIClipInfo.Enabled">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIClipInfo.TopMostRect">
            <summary>
            Gets the top most clipped rect.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIClipInfo.VisibleRect">
            <summary>
            Gets the visible rect.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIClipInfo.TopRect">
            <summary>
            Gets the top rect.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIClipInfo.Unclip(UnityEngine.Vector2)">
            <summary>
            Unclips the specified position.
            </summary>
            <param name="pos">The position.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIClipInfo.Unclip(UnityEngine.Rect)">
            <summary>
            Unclips the specified rect.
            </summary>
            <param name="rect">The rect.</param>
            <returns></returns>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUIContext`1">
            <summary>
            This class is due to undergo refactoring.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUIContext`1.Value">
            <summary>
            The value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIContext`1.op_Implicit(Sirenix.Utilities.Editor.GUIContext{`0})~`0">
            <summary>
            Performs an implicit conversion from <see cref="T:Sirenix.Utilities.Editor.GUIContext`1"/> to <see cref="!:T"/>.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUIHelper">
            <summary>
            Various helper function for GUI.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetBoldDefaultFont">
            <summary>
            Gets the bold default font.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.RemoveFocusControl">
            <summary>
            An alternative to GUI.FocusControl(null), which does not take focus away from the current GUI.Window.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.IsDrawingDictionaryKey">
            <summary>
            Whether the inspector is currently in the progress of drawing a dictionary key.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.BeginDrawToNothing">
            <summary>
            Hides the following draw calls. Remember to call <see cref="M:Sirenix.Utilities.Editor.GUIHelper.EndDrawToNothing"/> when done.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.EndDrawToNothing">
            <summary>
            Unhides the following draw calls after having called <see cref="M:Sirenix.Utilities.Editor.GUIHelper.BeginDrawToNothing"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.IsDockedWindow(UnityEditor.EditorWindow)">
            <summary>
            Determines whether the specified EditorWindow is docked.
            </summary>
            <param name="window">The editor window.</param>
            <returns><c>true</c> if the editor window is docked. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetEditorWindowRect">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.OpenInspectorWindow(UnityEngine.Object)">
            <summary>
            Opens a new inspector window for the specified object.
            </summary>
            <param name="unityObj">The unity object.</param>
            <exception cref="T:System.ArgumentNullException">unityObj</exception>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.IsBoldLabel">
            <summary>
            Gets or sets a value indicating whether labels are currently bold.
            </summary>
            <value>
            <c>true</c> if this instance is bold label; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.CurrentWindowBorderSize">
            <summary>
            Gets the size of the current window border.
            </summary>
            <value>
            The size of the current window border.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.EditorScreenPointOffset">
            <summary>
            Gets the editor screen point offset.
            </summary>
            <value>
            The editor screen point offset.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.ContextWidth">
            <summary>
            Gets the current editor gui context width. Only set these if you know what it does.
            Setting this has been removed. Use PushContextWidth and PopContextWidth instead.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.BetterLabelWidth">
            <summary>
            Unity EditorGUIUtility.labelWidth only works reliablly in Repaint events.
            BetterLabelWidth does a better job at giving you the correct LabelWidth in non-repaint events.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.BetterContextWidth">
            <summary>
            Odin will set this for you whenever an Odin property tree is drawn.
            But if you're using BetterLabelWidth and BetterContextWidth without Odin, then
            you need to set BetterContextWidth in the beginning of each GUIEvent.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.CurrentIndentAmount">
            <summary>
            Gets the current indent amount.
            </summary>
            <value>
            The current indent amount.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.MouseScreenPosition">
            <summary>
            Gets the mouse screen position.
            </summary>
            <value>
            The mouse screen position.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.CurrentWindow">
            <summary>
            Gets the current editor window.
            </summary>
            <value>
            The current editor window.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.CurrentWindowHasFocus">
            <summary>
            Gets a value indicating whether the current editor window is focused.
            </summary>
            <value>
            <c>true</c> if the current window has focus. Otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.CurrentWindowInstanceID">
            <summary>
            Gets the ID of the current editor window.
            </summary>
            <value>
            The ID of the current editor window.
            </value>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUIHelper.RepaintRequested">
            <summary>
            Gets a value indicating whether a repaint has been requested.
            </summary>
            <value>
              <c>true</c> if repaint has been requested. Otherwise <c>false</c>.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIHelper.ActualLabelWidth">
            <summary>
            Gets or sets the actual EditorGUIUtility.LabelWidth, regardless of the current hierarchy mode or context width.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.RequestRepaint">
            <summary>
            Requests a repaint.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.RequestRepaint(System.Int32)">
            <summary>
            Requests a repaint.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.BeginLayoutMeasuring">
            <summary>
            Begins the layout measuring. Remember to end with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.EndLayoutMeasuring(System.Int32)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.BeginLayoutMeasuring(System.Int32@)">
            <summary>
            Begins the layout measuring. Remember to end with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.EndLayoutMeasuring(System.Int32)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.EndLayoutMeasuring(System.Int32)">
            <summary>
            Ends the layout measuring started by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.EndLayoutMeasuring(System.Int32)"/>
            </summary>
            <returns>The measured rect.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.EndLayoutMeasuring">
            <summary>
            Ends the layout measuring started by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.EndLayoutMeasuring(System.Int32)"/>
            </summary>
            <returns>The measured rect.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetCurrentLayoutRect">
            <summary>
            Gets the current layout rect.
            </summary>
            <returns>The current layout rect.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetCurrentLayoutStyle">
            <summary>
            Gets the current layout rect.
            </summary>
            <returns>The current layout rect.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetPlaymodeTint">
            <summary>
            Gets the playmode color tint.
            </summary>
            <returns>The playmode color tint.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushContextWidth(System.Single)">
            <summary>
            Pushes a context width to the context width stack.
            Remember to pop the value again with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopContextWidth"/>.
            </summary>
            <param name="width">The width to push.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopContextWidth">
            <summary>
            Pops a value pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushContextWidth(System.Single)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushColor(UnityEngine.Color,System.Boolean)">
            <summary>
            Pushes a color to the GUI color stack. Remember to pop the color with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopColor"/>.
            </summary>
            <param name="color">The color to push the GUI color..</param>
            <param name="blendAlpha">if set to <c>true</c> blend with alpha.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.TakeGUIScreenshot(UnityEngine.Rect)">
            <summary>
            Takes a screenshot of the GUI within the specified rect.
            </summary>
            <param name="rect">The rect.</param>
            <returns>The screenshot as a render texture.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopColor">
            <summary>
            Pops the GUI color pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushColor(UnityEngine.Color,System.Boolean)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushGUIEnabled(System.Boolean)">
            <summary>
            Pushes a state to the GUI enabled stack. Remember to pop the state with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopGUIEnabled"/>.
            </summary>
            <param name="enabled">If set to <c>true</c> GUI will be enabled. Otherwise GUI will be disabled.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopGUIEnabled">
            <summary>
            Pops the GUI enabled pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushGUIEnabled(System.Boolean)"/>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushIsDrawingDictionaryKey(System.Boolean)">
            <summary>
            Pushes a state to the IsDrawingDictionaryKey stack. Remember to pop the state with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopIsDrawingDictionaryKey"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopIsDrawingDictionaryKey">
            <summary>
            Pops the state pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushIsDrawingDictionaryKey(System.Boolean)"/>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushHierarchyMode(System.Boolean,System.Boolean)">
            <summary>
            Pushes the hierarchy mode to the stack. Remember to pop the state with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopHierarchyMode"/>.
            </summary>
            <param name="hierarchyMode">The hierachy mode state to push.</param>
            <param name="preserveCurrentLabelWidth">Changing hierachy mode also changes how label-widths are calcualted. By default, we try to keep the current label width.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopHierarchyMode">
            <summary>
            Pops the hierarchy mode pushed by <see cref="!:PushHierarchyMode(bool)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushIsBoldLabel(System.Boolean)">
            <summary>
            Pushes bold label state to the stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopIsBoldLabel"/>.
            </summary>
            <param name="isBold">Value indicating if labels should be bold or not.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopIsBoldLabel">
            <summary>
            Pops the bold label state pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushIsBoldLabel(System.Boolean)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushIndentLevel(System.Int32)">
            <summary>
            Pushes the indent level to the stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopIndentLevel"/>.
            </summary>
            <param name="indentLevel">The indent level to push.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopIndentLevel">
            <summary>
            Pops the indent level pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushIndentLevel(System.Int32)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushContentColor(UnityEngine.Color,System.Boolean)">
            <summary>
            Pushes the content color to the stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopContentColor"/>.
            </summary>
            <param name="color">The content color to push..</param>
            <param name="blendAlpha">If set to <c>true</c> blend with alpha.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopContentColor">
            <summary>
            Pops the content color pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushContentColor(UnityEngine.Color,System.Boolean)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushLabelColor(UnityEngine.Color)">
            <summary>
            Pushes the label color to the stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopLabelColor"/>.
            </summary>
            <param name="color">The label color to push.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopLabelColor">
            <summary>
            Pops the label color pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushLabelColor(UnityEngine.Color)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushGUIPositionOffset(UnityEngine.Vector2)">
            <summary>
            Pushes the GUI position offset to the stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopGUIPositionOffset"/>.
            </summary>
            <param name="offset">The GUI offset.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopGUIPositionOffset">
            <summary>
            Pops the GUI position offset pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushGUIPositionOffset(UnityEngine.Vector2)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushMatrix(UnityEngine.Matrix4x4)">
            <summary>
            Pushes a GUI matrix to the stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopMatrix"/>.
            </summary>
            <param name="matrix">The GUI matrix to push.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopMatrix">
            <summary>
            Pops the GUI matrix pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushMatrix(UnityEngine.Matrix4x4)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.BeginIgnoreInput">
            <summary>
            Ignores input on following GUI calls. Remember to end with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.EndIgnoreInput"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.EndIgnoreInput">
            <summary>
            Ends the ignore input started by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.BeginIgnoreInput"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushEventType(UnityEngine.EventType)">
            <summary>
            Pushes the event type to the stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopEventType"/>.
            </summary>
            <param name="eventType">The type of event to push.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopEventType">
            <summary>
            Pops the event type pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopEventType"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushLabelWidth(System.Single)">
            <summary>
            Pushes the width to the editor GUI label width to the stack. Remmeber to Pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopLabelWidth"/>.
            </summary>
            <param name="labelWidth">The editor GUI label width to push.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopLabelWidth">
            <summary>
            Pops editor gui label widths pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushLabelWidth(System.Single)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushResponsiveVectorComponentFields(System.Boolean)">
            <summary>
            Pushes the value to the responsive vector component fields stack. Remeber to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopResponsiveVectorComponentFields"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopResponsiveVectorComponentFields">
            <summary>
            Pops responsive vector component fields value pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushResponsiveVectorComponentFields(System.Boolean)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushFadeGroupDuration(System.Single)">
            <summary>
            Pushes the value to the fade group duration stack. Remeber to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopFadeGroupDuration"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopFadeGroupDuration">
            <summary>
            Pops fade group duration value pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushFadeGroupDuration(System.Single)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PushTabPageSlideAnimationDuration(System.Single)">
            <summary>
            Pushes the value to the tab page slide animation duration stack. Remember to pop with <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PopTabPageSlideAnimationDuration"/>.
            </summary>
            <param name="duration"></param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.PopTabPageSlideAnimationDuration">
            <summary>
            Pops tab page slide animation duration value pushed by <see cref="M:Sirenix.Utilities.Editor.GUIHelper.PushTabPageSlideAnimationDuration(System.Single)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.ClearRepaintRequest">
            <summary>
            Clears the repaint request.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object,System.String)">
            <summary>
            Gets a temporary value context.
            </summary>
            <typeparam name="TValue">The type of the config value.</typeparam>
            <param name="key">The key for the config.</param>
            <param name="name">The name of the config.</param>
            <returns>GUIConfig for the specified key and name.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object,System.Int32)">
            <summary>
            Gets a temporary value context.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="key">The key for the config.</param>
            <param name="id">The ID for the config.</param>
            <returns>GUIConfig for the specified key and ID.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object,System.Object)">
            <summary>
            Gets a temporary value context.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="primaryKey">The primary key.</param>
            <param name="secondaryKey">The secondary key.</param>
            <returns>GUIConfig for the specified primary and secondary key.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object)">
            <summary>
            Gets a temporary value context.
            </summary>
            <typeparam name="TValue">The type of the value.</typeparam>
            <param name="key">The key for the context.</param>
            <returns>GUIConfig for the specified key.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryNullableContext``1(System.Object,System.String)">
            <summary>
            Gets a temporary nullable value context.
            </summary>
            <param name="key">Key for context.</param>
            <param name="name">Name for the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryNullableContext``1(System.Object,System.Int32)">
            <summary>
            Gets a temporary nullable value context.
            </summary>
            <param name="key">Key for context.</param>
            <param name="id">Id of the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryNullableContext``1(System.Object,System.Object)">
            <summary>
            Gets a temporary nullable value context.
            </summary>
            <param name="primaryKey">Primary key for the context.</param>
            <param name="secondaryKey">Secondary key for the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryNullableContext``1(System.Object)">
            <summary>
            Gets a temporary nullable value context.
            </summary>
            <param name="key">Key for the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object,System.String,``0)">
            <summary>
            Gets a temporary context.
            </summary>
            <param name="key">Key for the context.</param>
            <param name="name">Name for the context.</param>
            <param name="defaultValue">Default value of the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object,System.Int32,``0)">
            <summary>
            Gets a temporary context.
            </summary>
            <param name="key">Key for the context.</param>
            <param name="id">Id for the context.</param>
            <param name="defaultValue">Default value of the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object,System.Object,``0)">
            <summary>
            Gets a temporary context.
            </summary>
            <param name="primaryKey">Primary key for the context.</param>
            <param name="secondaryKey">Secondary key for the context.</param>
            <param name="defaultValue">Default value of the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetTemporaryContext``1(System.Object,``0)">
            <summary>
            Gets a temporary context.
            </summary>
            <param name="key">Key for the context.</param>
            <param name="defaultValue">Default value of the context.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.TempContent(System.String)">
            <summary>
            Gets a temporary GUIContent with the specified text.
            </summary>
            <param name="t">The text for the GUIContent.</param>
            <returns>Temporary GUIContent instance.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.TempContent(System.String,System.String)">
            <summary>
            Gets a temporary GUIContent with the specified text and tooltip.
            </summary>
            <param name="t">The text for the GUIContent.</param>
            <param name="tooltip">The tooltip for the GUIContent.</param>
            <returns>Temporary GUIContent instance.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.TempContent(UnityEngine.Texture,System.String)">
            <summary>
            Gets a temporary GUIContent with the specified image and tooltip.
            </summary>
            <param name="image">The image for the GUIContent.</param>
            <param name="tooltip">The tooltip for the GUIContent.</param>
            <returns>Temporary GUIContent instance.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.TempContent(System.String,UnityEngine.Texture,System.String)">
            <summary>
            Gets a temporary GUIContent with the specified text, image and tooltip.
            </summary>
            <param name="text">The text for the GUIContent.</param>
            <param name="image">The image for the GUIContent.</param>
            <param name="tooltip">The tooltip for the GUIContent.</param>
            <returns>Temporary GUIContent instance.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.IndentRect(UnityEngine.Rect)">
            <summary>
            Indents the rect by the current indent amount.
            </summary>
            <param name="rect">The rect to indent.</param>
            <returns>Indented rect.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.IndentRect(UnityEngine.Rect@)">
            <summary>
            Indents the rect by the current indent amount.
            </summary>
            <param name="rect">The rect to indent.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.RepaintIfRequested(UnityEditor.EditorWindow)">
            <summary>
            Repaints the EditorWindow if a repaint has been requested.
            </summary>
            <param name="window">The window to repaint.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.RepaintIfRequested(UnityEditor.Editor)">
            <summary>
            Repaints the editor if a repaint has been requested. If the currently rendering window is not an InspectorWindow, Repaint() will be called on the current window as well.
            </summary>
            <param name="editor">The editor to repaint.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetAssetThumbnail(UnityEngine.Object,System.Type,System.Boolean)">
            <summary>
            Gets the best thumbnail icon given the provided arguments provided.
            </summary>
            <param name="obj"></param>
            <param name="type"></param>
            <param name="preferObjectPreviewOverFileIcon"></param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIHelper.GetPreviewTexture(UnityEngine.Object)">
            <summary>
            Gets a preview texture for the provided object.
            </summary>
            <param name="objectToPreview"></param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUIPagingHelper">
            <summary>
            A helper class to control paging of n number of elements in various situations.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUIPagingHelper.IsExpanded">
            <summary>
            Disables the paging, and show all elements.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIPagingHelper.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.Editor.GUIPagingHelper"/> class.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIPagingHelper.Update(System.Int32)">
            <summary>
            Updates all values based on <paramref name="elementCount"/> and <see cref="!:NumberOfItemsPrPage"/>.
            </summary>
            <remarks>
            Call update right before using <see cref="P:Sirenix.Utilities.Editor.GUIPagingHelper.StartIndex"/> and <see cref="P:Sirenix.Utilities.Editor.GUIPagingHelper.EndIndex"/> in your for loop.
            </remarks>
            <param name="elementCount">The total number of elements to apply paging for.</param>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.IsEnabled">
            <summary>
            Gets or sets a value indicating whether this instance is enabled.
            </summary>
            <value>
            <c>true</c> if this instance is enabled; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.IsOnFirstPage">
            <summary>
            Gets a value indicating whether this instance is on the frist page.
            </summary>
            <value>
            <c>true</c> if this instance is on frist page; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.IsOnLastPage">
            <summary>
            Gets a value indicating whether this instance is on the last page.
            </summary>
            <value>
            <c>true</c> if this instance is on last page; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.NumberOfItemsPerPage">
            <summary>
            Gets or sets the number of items per page.
            </summary>
            <value>
            The number of items pr page.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.CurrentPage">
            <summary>
            Gets or sets the current page.
            </summary>
            <value>
            The current page.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.StartIndex">
            <summary>
            Gets the start index.
            </summary>
            <value>
            The start index.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.EndIndex">
            <summary>
            Gets the end index.
            </summary>
            <value>
            The end index.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.PageCount">
            <summary>
            Gets or sets the page count.
            </summary>
            <value>
            The page count.
            </value>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIPagingHelper.ElementCount">
            <summary>
            Gets the total number of elements.
            Use <see cref="M:Sirenix.Utilities.Editor.GUIPagingHelper.Update(System.Int32)"/> to change the value.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIPagingHelper.DrawToolbarPagingButtons(UnityEngine.Rect@,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            Draws right-aligned toolbar paging buttons.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUITabGroup">
             <summary>
             The GUITabGroup is a utility class to draw animated tab groups.
             </summary>
             <example>
             <code>
             var tabGroup = SirenixEditorGUI.CreateAnimatedTabGroup(someKey);
             // Register your tabs before starting BeginGroup.
             var tab1 = tabGroup.RegisterTab("tab 1");
             var tab2 = tabGroup.RegisterTab("tab 2");
            
             tabGroup.BeginGroup(drawToolbar: true);
             {
                 if (tab1.BeginPage())
                 {
                     // Draw GUI for the first tab page;
                 }
                 tab1.EndPage();
            
                 if (tab2.BeginPage())
                 {
                     // Draw GUI for the second tab page;
                 }
                 tab2.EndPage();
             }
             tabGroup.EndGroup();
            
             // Control the animation speed.
             tabGroup.AnimationSpeed = 0.2f;
            
             // If true, the tab group will have the height equal to the biggest page. Otherwise the tab group will animate in height as well when changing page.
             tabGroup.FixedHeight = true;
            
             // You can change page by calling:
             tabGroup.GoToNextPage();
             tabGroup.GoToPreviousPage();
             </code>
             </example>
             <seealso cref="T:Sirenix.Utilities.Editor.SirenixEditorGUI"/>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITabGroup.AnimationSpeed">
            <summary>
            The animation speed
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITabGroup.OuterRect">
            <summary>
            Gets the outer rect of the entire tab group.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITabGroup.InnerRect">
            <summary>
            The inner rect of the current tab page.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabGroup.SetCurrentPage(Sirenix.Utilities.Editor.GUITabPage)">
            <summary>
            If true, the tab group will have the height equal to the biggest page. Otherwise the tab group will animate in height as well when changing page.
            </summary>
            <summary>
            Sets the current page.
            </summary>
            <param name="page">The page to switch to.</param>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITabGroup.CurrentPage">
            <summary>
            Gets the current page.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITabGroup.T">
            <summary>
            Gets the t.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITabGroup.ToolbarHeight">
            <summary>
            The height of the tab buttons.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabGroup.RegisterTab(System.String)">
            <summary>
            Registers the tab.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabGroup.BeginGroup(System.Boolean,UnityEngine.GUIStyle)">
            <summary>
            Begins the group.
            </summary>
            <param name="drawToolbar">if set to <c>true</c> a tool-bar for changing pages is drawn.</param>
            <param name="style">The style.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabGroup.EndGroup">
            <summary>
            Ends the group.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabGroup.GoToPage(Sirenix.Utilities.Editor.GUITabPage)">
            <summary>
            Goes to page.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabGroup.GoToNextPage">
            <summary>
            Goes to next page.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabGroup.GoToPreviousPage">
            <summary>
            Goes to previous page.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUITable">
             <summary>
             <para>A Utility class for creating tables in Unity's editor GUI.</para>
             <para>A table can either be created from scratch using new GUITable(xCount,yCount), or created using one of the static GUITable.Create overloads.</para>
             <para>See the online documentation, for examples and more information.</para>
             </summary>
             <example>
             <para>Creating a matrix table for a two-dimentional array.</para>
             <code>
             private GUITable table;
            
             private void Init()
             {
                 bool[,] boolArr = new bool[20,20];
            
                 this.table = GUITable.Create(
                     twoDimArray: boolArr,
                     drawElement: (rect, x, y) => boolArr[x, y] = EditorGUI.Toggle(rect, boolArr[x, y]),
                     horizontalLabel: "Optional Horizontal Label",               // horizontalLabel is optional and can be null.
                     columnLabels: (rect, x) => GUI.Label(rect, x.ToString()),   // columnLabels is optional and can be null.
                     verticalLabel: "Optional Vertical Label",                   // verticalLabel is optional and can be null.
                     rowLabels: (rect, x) => GUI.Label(rect, x.ToString())       // rowLabels is optional and can be null.
                 );
             }
            
             private void OnGUI()
             {
                 this.table.DrawTable();
             }
             </code>
             </example>
             <example>
             <para>Creating a table for a list.</para>
             <code>
             private GUITable table;
            
             private void Init()
             {
                 Listt&lt;SomeClasst&gt; someList = new List&lt;SomeClass&gt;() { new SomeClass(), new SomeClass(), new SomeClass() };
            
                 this.table = GUITable.Create(someList, "Optional Title",
                     new GUITableColumn()
                     {
                         ColumnTitle = "A",
                         OnGUI = (rect, i) => someList[i].A = EditorGUI.TextField(rect, someList[i].A),
                         Width = 200,
                         MinWidth = 100,
                     },
                     new GUITableColumn()
                     {
                         ColumnTitle = "B",
                         OnGUI = (rect, i) => someList[i].B = EditorGUI.IntField(rect, someList[i].B),
                         Resizable = false,
                     },
                     new GUITableColumn()
                     {
                         ColumnTitle = "C",
                         OnGUI = (rect, i) => someList[i].C = EditorGUI.IntField(rect, someList[i].C),
                         SpanColumnTitle = true,
                     }
                 );
             }
            
             private void OnGUI()
             {
                 this.table.DrawTable();
             }
            
             private class SomeClass
             {
                 public string A;
                 public int B;
                 public int C;
                 public int D;
             }
             </code>
             </example>
             <example>
             <para>Styling a cell.</para>
             <para>Each <see cref="T:Sirenix.Utilities.Editor.GUITableCell"/> has two events, OnGUI and OnGUIStyle. OnGUIStyle is called right before OnGUI, but only in repaint events.</para>
             <code>
             guiTable[x,y].GUIStyle += rect => EditorGUI.DrawRect(rect, Color.red);
             </code>
             </example>
             <example>
             <para>Row and column span.</para>
             <para>A cell will span and cover all neighbour cells that are null.</para>
             <code>
             // Span horizontally:
             guiTable[x - 2,y] = null;
             guiTable[x - 1,y] = null;
             guiTable[x,y].SpanX = true;
             guiTable[x + 1,y] = null;
            
             // Span vertically:
             guiTable[x,y - 2] = null;
             guiTable[x,y - 1] = null;
             guiTable[x,y].SpanY = true;
             guiTable[x,y + 1] = null;
             </code>
             </example>
             <seealso cref="T:Sirenix.Utilities.Editor.GUITable"/>
             <seealso cref="T:Sirenix.Utilities.Editor.GUITableCell"/>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITable.RowCount">
            <summary>
            The row count.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITable.ColumnCount">
            <summary>
            The column count.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITable.TableRect">
            <summary>
            The Table Rect.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITable.RespectIndentLevel">
            <summary>
            Whether to respect the current GUI indent level.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITable.Item(System.Int32,System.Int32)">
            <summary>
            Gets or sets a <see cref="T:Sirenix.Utilities.Editor.GUITableCell"/> from the <see cref="T:Sirenix.Utilities.Editor.GUITable"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.Editor.GUITable"/> class.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.DrawTable">
            <summary>
            Draws the table.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.MarkDirty">
            <summary>
            Recaluclates cell and column sizes in the next frame.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.ReCalculateSizes">
            <summary>
            <para>Recalculates the layout for the entire table.</para>
            <para>This method gets called whenever the table is initialized, resized or adjusted. If you are manipulating
            the width or height of individual table cells, remember to call this method when you're done.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.Create(System.Int32,System.Int32,System.Action{UnityEngine.Rect,System.Int32,System.Int32},System.String,System.Action{UnityEngine.Rect,System.Int32},System.String,System.Action{UnityEngine.Rect,System.Int32},System.Boolean)">
            <summary>
            Creates a table.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.Create(System.Int32,System.String,Sirenix.Utilities.Editor.GUITableColumn[])">
            <summary>
            Creates a table.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.Create``1(``0[0:,0:],System.Action{UnityEngine.Rect,System.Int32,System.Int32},System.String,System.Action{UnityEngine.Rect,System.Int32},System.String,System.Action{UnityEngine.Rect,System.Int32})">
            <summary>
            Creates a table.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITable.Create``1(System.Collections.Generic.IList{``0},System.String,Sirenix.Utilities.Editor.GUITableColumn[])">
            <summary>
            Creates a table.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUITableCell">
            <summary>
            A cell of a <see cref="T:Sirenix.Utilities.Editor.GUITable"/>
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableCell.MinWidth">
            <summary>
            The minimum width.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableCell.Width">
            <summary>
            <para>The width of the cell. Default is width is 0.</para>
            <para>The width the column is determained by the widest cell in the column.</para>
            <para>Width = 0 = auto.</para>
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableCell.Height">
            <summary>
            <para>The height of the cell. Default is height is 22.</para>
            <para>The height the column is determained by the tallest cell in the row.</para>
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableCell.SpanY">
            <summary>
            If true, the cell will expand vertically, covering all neighbour null cells.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableCell.SpanX">
            <summary>
            If true, the cell will expand horizontally, covering all neighbour null cells.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITableCell.X">
            <summary>
            The table column index.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITableCell.Y">
            <summary>
            The table row index.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableCell.GUIStyle">
            <summary>
            The GUI style
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITableCell.Rect">
            <summary>
            Gets the rect.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUITableColumn">
            <summary>
            GUITableColumns used creating a table list using GUITable.Create().
            </summary>
            <seealso cref="T:Sirenix.Utilities.Editor.GUITable"/>
            <seealso cref="T:Sirenix.Utilities.Editor.GUITableCell"/>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableColumn.OnGUI">
            <summary>
            Draws a cell at the given row index for this column.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableColumn.ColumnTitle">
            <summary>
            The column title text. If there are is columns with a title, there we not be rendered an additional table row for column titles.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableColumn.MinWidth">
            <summary>
            The minimum with of the column.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableColumn.Width">
            <summary>
            The width of the Column.
            0 = auto, and is also the default.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableColumn.Resizable">
            <summary>
            If true, the column becomes resiziable.
            Default is true.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableColumn.SpanColumnTitle">
            <summary>
            If true, the column title cell, will span horizontally to neighbour columns, which column titles are null.
            Default is false.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.DrawScrollView">
            <summary>
            Whether to draw a draw scroll view.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.MinScrollViewHeight">
            <summary>
            The number of pixels before a scroll view appears.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.MaxScrollViewHeight">
            <summary>
            The maximum scroll view height.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.ScrollPos">
            <summary>
            The scroll position
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.CellStyle">
            <summary>
            The cell style
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.ContentRect">
            <summary>
            Gets the rect containing all rows. 
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.RowIndexFrom">
            <summary>
            Gets the first visible row index.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.RowIndexTo">
            <summary>
            Gets the last visible row index.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.OuterRect">
            <summary>
            Gets the outer rect. The height of this &lt;= <see cref="P:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.ContentRect"/>.height.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.GetRowRect(System.Int32)">
            <summary>
            Gets the row rect.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.BeginTable(System.Int32)">
            <summary>
            Begins the table.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.BeginColumn(System.Int32,System.Int32)">
            <summary>
            Begins the column.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.BeginCell(System.Int32)">
            <summary>
            Begins the cell.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.EndCell(System.Int32)">
            <summary>
            Ends the cell.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.EndColumn">
            <summary>
            Ends the column.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITableRowLayoutGroup.EndTable">
            <summary>
            Ends the table.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUITabPage">
            <summary>
            A tab page created by <see cref="T:Sirenix.Utilities.Editor.GUITabGroup"/>.
            </summary>
            <seealso cref="T:Sirenix.Utilities.Editor.GUITabGroup"/>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabPage.BeginPage">
            <summary>
            Begins the page.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUITabPage.EndPage">
            <summary>
            Ends the page.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.ObjectFieldAlignment">
            <summary>
            How the square object field should be aligned.
            </summary>
            <seealso cref="!:PreviewFieldAttribute"/>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ObjectFieldAlignment.Left">
            <summary>
            Left aligned.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ObjectFieldAlignment.Center">
            <summary>
            Centered.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ObjectFieldAlignment.Right">
            <summary>
            Right aligned.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.ProgressBarConfig">
            <summary>
            Configuration for progress bar fields.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ProgressBarConfig.Height">
            <summary>
            The height of the progress bar field. Default 12 pixel.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ProgressBarConfig.ForegroundColor">
            <summary>
            The foreground color of the progress bar field.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ProgressBarConfig.BackgroundColor">
            <summary>
            The background color of the progress bar field.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ProgressBarConfig.DrawValueLabel">
            <summary>
            If <c>true</c> the progress bar field will draw a label ontop to show the current value.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.ProgressBarConfig.ValueLabelAlignment">
            <summary>
            Alignment of the progress bar field overlay.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.ProgressBarConfig.Default">
            <summary>
            Default configuration.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ProgressBarConfig.#ctor(Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Creates a copy of the configuration.
            </summary>
            <param name="config">The configuration to copy.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ProgressBarConfig.#ctor(System.Int32,UnityEngine.Color,UnityEngine.Color,System.Boolean,UnityEngine.TextAlignment)">
            <summary>
            Creates a progress bar configuration.
            </summary>
            <param name="height">The height of the progress bar.</param>
            <param name="foregroundColor">The foreground color of the progress bar.</param>
            <param name="backgroundColor">The background color of the progress bar.</param>
            <param name="textOverlay">If <c>true</c> there will be drawn a overlay on top of the field.</param>
            <param name="overlayAlignment">The alignment of the text overlay.</param>
        </member>
        <member name="T:Sirenix.Utilities.Editor.QuaternionDrawMode">
            <summary>
            Draw mode of quaternion fields.
            </summary>
            <seealso cref="T:Sirenix.Utilities.Editor.SirenixEditorFields"/>
            <seealso cref="!:Sirenix.OdinInspector.Editor.GeneralDrawerConfig"/>
        </member>
        <member name="F:Sirenix.Utilities.Editor.QuaternionDrawMode.Eulers">
            <summary>
            Draw the quaterion as euler angles.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.QuaternionDrawMode.AngleAxis">
            <summary>
            Draw the quaterion in as an angle and an axis.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.QuaternionDrawMode.Raw">
            <summary>
            Draw the quaternion as raw x, y, z and w values.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.SirenixEditorFields">
            <summary>
            Field drawing functions for various types.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixEditorFields.SingleLetterStructLabelWidth">
            <summary>
            The width of the X, Y and Z labels in structs.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixEditorFields.ResponsiveVectorComponentFields">
            <summary>
            When <c>true</c> the component labels, for vector fields, will be hidden when the field is too narrow.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityObjectField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Object,System.Type,System.Boolean)">
            <summary>
            Draws a regular Unity ObjectField, but supports labels being nulls, and also adds a small button that will open the object in a new inspector window.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityObjectField(UnityEngine.Rect,System.String,UnityEngine.Object,System.Type,System.Boolean)">
            <summary>
            Draws a regular Unity ObjectField, but supports labels being nulls, and also adds a small button that will open the object in a new inspector window.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityObjectField(UnityEngine.Rect,UnityEngine.Object,System.Type,System.Boolean)">
            <summary>
            Draws a regular Unity ObjectField, but supports labels being nulls, and also adds a small button that will open the object in a new inspector window.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityObjectField(UnityEngine.GUIContent,UnityEngine.Object,System.Type,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a regular Unity ObjectField, but supports labels being nulls, and also adds a small button that will open the object in a new inspector window.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityObjectField(System.String,UnityEngine.Object,System.Type,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a regular Unity ObjectField, but supports labels being nulls, and also adds a small button that will open the object in a new inspector window.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityObjectField(UnityEngine.Object,System.Type,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a regular Unity ObjectField, but supports labels being nulls, and also adds a small button that will open the object in a new inspector window.
            </summary>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PreviewObjectField``1(UnityEngine.Rect,``0,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,UnityEngine.Object,UnityEngine.Texture,System.Type,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Object,System.Type,Sirenix.Utilities.Editor.ObjectFieldAlignment,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Object,UnityEngine.Texture,System.Type,Sirenix.Utilities.Editor.ObjectFieldAlignment,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,UnityEngine.Object,System.Type,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Object,System.Type,System.Boolean,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Object,UnityEngine.Texture,System.Type,System.Boolean,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="preview">The Texture to be used as the preview.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,System.String,UnityEngine.Object,System.Type,System.Boolean,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Rect,UnityEngine.Object,System.Type,System.Boolean,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.GUIContent,UnityEngine.Object,System.Type,System.Boolean,System.Single,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="height">The height or size of the square object field.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.GUIContent,UnityEngine.Object,UnityEngine.Texture,System.Type,System.Boolean,System.Single,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="preview">The texture to be used as the preview.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="height">The height or size of the square object field.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(System.String,UnityEngine.Object,System.Type,System.Boolean,System.Single,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="height">The height or size of the square object field.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.UnityPreviewObjectField(UnityEngine.Object,System.Type,System.Boolean,System.Single,Sirenix.Utilities.Editor.ObjectFieldAlignment)">
            <summary>
            Draws a square ObjectField which renders a preview for UnityEngine.Object types.
            This object field also adds support for drag and drop, dragging an object to another square object field, swaps the values.
            If you hold down control while letting go it will replace the value, And you can control + click the object field to quickly delete the value it holds.
            </summary>
            <param name="value">The Unity object.</param>
            <param name="objectType">The Unity object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="height">The height or size of the square object field.</param>
            <param name="alignment">How the square object field should be aligned.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PolymorphicObjectField(UnityEngine.GUIContent,System.Object,System.Type,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a polymorphic ObjectField.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The value.</param>
            <param name="type">The object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PolymorphicObjectField(UnityEngine.GUIContent,System.Object,System.Type,System.String,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a polymorphic ObjectField.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The value.</param>
            <param name="type">The object type. This supports inheritance.</param>
            <param name="title">The title to be shown in the object picker.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PolymorphicObjectField(UnityEngine.Rect,UnityEngine.GUIContent,System.Object,System.Type,System.Boolean)">
            <summary>
            Draws a polymorphic ObjectField.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PolymorphicObjectField(UnityEngine.Rect,System.Object,System.Type,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            Draws a polymorphic ObjectField.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PolymorphicObjectField(System.String,System.Object,System.Type,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a polymorphic ObjectField.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The value.</param>
            <param name="type">The object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PolymorphicObjectField(System.Object,System.Type,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a polymorphic ObjectField.
            </summary>
            <param name="value">The value.</param>
            <param name="type">The object type. This supports inheritance.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.PolymorphicObjectField(System.Object,System.Type,System.String,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a polymorphic ObjectField.
            </summary>
            <param name="value">The value.</param>
            <param name="type">The object type. This supports inheritance.</param>
            <param name="title">The title to be shown in the object picker.</param>
            <param name="allowSceneObjects">Wheather or not to allow scene objects.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LayerMaskField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.LayerMask)">
            <summary>
            Draws a field for a layer mask.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="layerMask">The layer mask to draw.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LayerMaskField(UnityEngine.Rect,System.String,UnityEngine.LayerMask)">
            <summary>
            Draws a field for a layer mask.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="layerMask">The layer mask to draw.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LayerMaskField(UnityEngine.Rect,UnityEngine.LayerMask)">
            <summary>
            Draws a field for a layer mask.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="layerMask">The layer mask to draw.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LayerMaskField(UnityEngine.GUIContent,UnityEngine.LayerMask,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field for a layer mask.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="layerMask">The layer mask to draw.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LayerMaskField(System.String,UnityEngine.LayerMask,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field for a layer mask.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="layerMask">The layer mask to draw.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LayerMaskField(UnityEngine.LayerMask,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field for a layer mask.
            </summary>
            <param name="layerMask">The layer mask to draw.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.GuidField(UnityEngine.Rect,UnityEngine.GUIContent,System.Guid)">
            <summary>
            Draws a Guid field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.GuidField(UnityEngine.Rect,System.Guid)">
            <summary>
            Draws a Guid field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.GuidField(UnityEngine.GUIContent,System.Guid)">
            <summary>
            Draws a Guid field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.GuidField(UnityEngine.GUIContent,System.Guid,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Guid field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.GuidField(UnityEngine.GUIContent,System.Guid,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Guid field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.GuidField(UnityEngine.Rect,UnityEngine.GUIContent,System.Guid,UnityEngine.GUIStyle)">
            <summary>
            Draws a Guid field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32,UnityEngine.GUIStyle)">
            <summary>
            Draws an int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32)">
            <summary>
            Draws an int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(UnityEngine.Rect,System.String,System.Int32)">
            <summary>
            Draws an int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(UnityEngine.Rect,System.Int32)">
            <summary>
            Draws an int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(UnityEngine.GUIContent,System.Int32,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an int field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(UnityEngine.GUIContent,System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an int field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(System.String,System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an int field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.IntField(System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an int field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32,UnityEngine.GUIStyle)">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32)">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(UnityEngine.Rect,System.String,System.Int32)">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(UnityEngine.Rect,System.Int32)">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(UnityEngine.GUIContent,System.Int32,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(UnityEngine.GUIContent,System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(System.String,System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedIntField(System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed int field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32,System.Int32,System.Int32,UnityEngine.GUIStyle)">
            <summary>
            Draws a range field for ints.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws a range field for ints.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(UnityEngine.Rect,System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws a range field for ints.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(UnityEngine.Rect,System.Int32,System.Int32,System.Int32)">
            <summary>
            Draws a range field for ints.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(UnityEngine.GUIContent,System.Int32,System.Int32,System.Int32,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Drwas a range field for ints.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(UnityEngine.GUIContent,System.Int32,System.Int32,System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a range field for ints.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(System.String,System.Int32,System.Int32,System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a range field for ints.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeIntField(System.Int32,System.Int32,System.Int32,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a range field for ints.
            </summary>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.Rect,UnityEngine.GUIContent,System.Double,System.Double,System.Double,Sirenix.Utilities.Editor.ProgressBarConfig,System.String)">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="valueLabel">Optional text for label to be drawn ontop of the progress bar. This value is only used if the DrawValueLabel option is enabled in the ProgressBarConfig.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.Rect,UnityEngine.GUIContent,System.Double,System.Double,System.Double,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.Rect,System.String,System.Double,System.Double,System.Double,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.Rect,System.Double,System.Double,System.Double,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.Rect,UnityEngine.GUIContent,System.Double,System.Double,System.Double)">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.Rect,System.String,System.Double,System.Double,System.Double)">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.Rect,System.Double,System.Double,System.Double)">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.GUIContent,System.Double,System.Double,System.Double,Sirenix.Utilities.Editor.ProgressBarConfig,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(System.String,System.Double,System.Double,System.Double,Sirenix.Utilities.Editor.ProgressBarConfig,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(System.Double,System.Double,System.Double,Sirenix.Utilities.Editor.ProgressBarConfig,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(UnityEngine.GUIContent,System.Double,System.Double,System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(System.String,System.Double,System.Double,System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarField(System.Double,System.Double,System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored progress bar field.
            </summary>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int64,System.Int64,System.Int64,Sirenix.Utilities.Editor.ProgressBarConfig,System.String)">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="valueLabel">Optional text for label to be drawn ontop of the progress bar. This value is only used if the DrawValueLabel option is enabled in the ProgressBarConfig.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int64,System.Int64,System.Int64,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.Rect,System.String,System.Int64,System.Int64,System.Int64,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.Rect,System.Int64,System.Int64,System.Int64,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int64,System.Int64,System.Int64)">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.Rect,System.String,System.Int64,System.Int64,System.Int64)">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.Rect,System.Int64,System.Int64,System.Int64)">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.GUIContent,System.Int64,System.Int64,System.Int64,Sirenix.Utilities.Editor.ProgressBarConfig,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(System.String,System.Int64,System.Int64,System.Int64,Sirenix.Utilities.Editor.ProgressBarConfig,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(System.Int64,System.Int64,System.Int64,Sirenix.Utilities.Editor.ProgressBarConfig,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="config">The configuration for the progress bar field.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(UnityEngine.GUIContent,System.Int64,System.Int64,System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(System.String,System.Int64,System.Int64,System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="label">The label to use, or null if no label should be used.</param>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.SegmentedProgressBarField(System.Int64,System.Int64,System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a colored segmented progress bar field.
            </summary>
            <param name="value">The current value of the progress bar.</param>
            <param name="minValue">The left hand side value of the progress bar.</param>
            <param name="maxValue">The right hand side value of the progress bar.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarOverlayLabel(UnityEngine.Rect,UnityEngine.GUIContent,System.Single,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws an overlay on top of a progress bar field.
            </summary>
            <param name="rect">The rect used to draw the progress bar field with. (Minus the Rect for the prefix label, if any.)</param>
            <param name="label">The label to draw ontop of the progress bar field.</param>
            <param name="progress">The relative value of the progress bar, from 0 to 1.</param>
            <param name="config">The configuration used to draw the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ProgressBarOverlayLabel(UnityEngine.Rect,System.String,System.Single,Sirenix.Utilities.Editor.ProgressBarConfig)">
            <summary>
            Draws an overlay on top of a progress bar field.
            </summary>
            <param name="rect">The rect used to draw the progress bar field with. (Minus the Rect for the prefix label, if any.)</param>
            <param name="label">The label to draw ontop of the progress bar field.</param>
            <param name="progress">The relative value of the progress bar, from 0 to 1.</param>
            <param name="config">The configuration used to draw the progress bar field.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int64,UnityEngine.GUIStyle)">
            <summary>
            Draws an long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int64)">
            <summary>
            Draws an long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(UnityEngine.Rect,System.String,System.Int64)">
            <summary>
            Draws an long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(UnityEngine.Rect,System.Int64)">
            <summary>
            Draws an long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(UnityEngine.GUIContent,System.Int64,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an long field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(UnityEngine.GUIContent,System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an long field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(System.String,System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an long field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.LongField(System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an long field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int64,UnityEngine.GUIStyle)">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(UnityEngine.Rect,UnityEngine.GUIContent,System.Int64)">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(UnityEngine.Rect,System.String,System.Int64)">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(UnityEngine.Rect,System.Int64)">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(UnityEngine.GUIContent,System.Int64,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(UnityEngine.GUIContent,System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(System.String,System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedLongField(System.Int64,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed long field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(UnityEngine.Rect,UnityEngine.GUIContent,System.Single,UnityEngine.GUIStyle)">
            <summary>
            Draws a float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(UnityEngine.Rect,UnityEngine.GUIContent,System.Single)">
            <summary>
            Draws a float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(UnityEngine.Rect,System.String,System.Single)">
            <summary>
            Draws a float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(UnityEngine.Rect,System.Single)">
            <summary>
            Draws a float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(UnityEngine.GUIContent,System.Single,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a float field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(UnityEngine.GUIContent,System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a float field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(System.String,System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a float field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FloatField(System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a float field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(UnityEngine.Rect,UnityEngine.GUIContent,System.Single,UnityEngine.GUIStyle)">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(UnityEngine.Rect,UnityEngine.GUIContent,System.Single)">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(UnityEngine.Rect,System.String,System.Single)">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(UnityEngine.Rect,System.Single)">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(UnityEngine.GUIContent,System.Single,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(UnityEngine.GUIContent,System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(System.String,System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedFloatField(System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed float field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(UnityEngine.Rect,UnityEngine.GUIContent,System.Single,System.Single,System.Single,UnityEngine.GUIStyle)">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(UnityEngine.Rect,UnityEngine.GUIContent,System.Single,System.Single,System.Single)">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(UnityEngine.Rect,System.String,System.Single,System.Single,System.Single)">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(UnityEngine.Rect,System.Single,System.Single,System.Single)">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(UnityEngine.GUIContent,System.Single,System.Single,System.Single,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(UnityEngine.GUIContent,System.Single,System.Single,System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(System.String,System.Single,System.Single,System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RangeFloatField(System.Single,System.Single,System.Single,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a range field for floats.
            </summary>
            <param name="value">Current value.</param>
            <param name="min">Minimum value.</param>
            <param name="max">Maximum value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(UnityEngine.Rect,UnityEngine.GUIContent,System.Double,UnityEngine.GUIStyle)">
            <summary>
            Draws a double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(UnityEngine.Rect,UnityEngine.GUIContent,System.Double)">
            <summary>
            Draws a double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(UnityEngine.Rect,System.String,System.Double)">
            <summary>
            Draws a double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(UnityEngine.Rect,System.Double)">
            <summary>
            Draws a double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(UnityEngine.GUIContent,System.Double,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a double field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(UnityEngine.GUIContent,System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a double field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(System.String,System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a double field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DoubleField(System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a double field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(UnityEngine.Rect,UnityEngine.GUIContent,System.Double,UnityEngine.GUIStyle)">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(UnityEngine.Rect,UnityEngine.GUIContent,System.Double)">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(UnityEngine.Rect,System.String,System.Double)">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(UnityEngine.Rect,System.Double)">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(UnityEngine.GUIContent,System.Double,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(UnityEngine.GUIContent,System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(System.String,System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedDoubleField(System.Double,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed double field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(UnityEngine.Rect,UnityEngine.GUIContent,System.Decimal,UnityEngine.GUIStyle)">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(UnityEngine.Rect,UnityEngine.GUIContent,System.Decimal)">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(UnityEngine.Rect,System.String,System.Decimal)">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(UnityEngine.Rect,System.Decimal)">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(UnityEngine.GUIContent,System.Decimal,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(UnityEngine.GUIContent,System.Decimal,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(System.String,System.Decimal,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DecimalField(System.Decimal,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a decimal field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(UnityEngine.Rect,UnityEngine.GUIContent,System.String,UnityEngine.GUIStyle)">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(UnityEngine.Rect,UnityEngine.GUIContent,System.String)">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(UnityEngine.Rect,System.String,System.String)">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(UnityEngine.Rect,System.String)">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(UnityEngine.GUIContent,System.String,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(UnityEngine.GUIContent,System.String,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(System.String,System.String,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.TextField(System.String,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a text field for strings.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(UnityEngine.Rect,UnityEngine.GUIContent,System.String,UnityEngine.GUIStyle)">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(UnityEngine.Rect,UnityEngine.GUIContent,System.String)">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(UnityEngine.Rect,System.String,System.String)">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(UnityEngine.Rect,System.String)">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(UnityEngine.GUIContent,System.String,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(UnityEngine.GUIContent,System.String,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(System.String,System.String,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.DelayedTextField(System.String,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a delayed text field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FilePathField(UnityEngine.Rect,UnityEngine.GUIContent,System.String,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Draws a field that lets the user select a path to a file.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="extensions">Comma separated list of allowed file extensions. Use <c>null</c> to allow any file extension.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <returns>A path to a file.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FilePathField(UnityEngine.Rect,System.String,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Draws a field that lets the user select a path to a file.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="extensions">Comma separated list of allowed file extensions. Use <c>null</c> to allow any file extension.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <returns>A path to a file.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FilePathField(UnityEngine.GUIContent,System.String,System.String,System.String,System.Boolean,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field that lets the user select a path to a file.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="extensions">Comma separated list of allowed file extensions. Use <c>null</c> to allow any file extension.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <param name="options">Layout options.</param>
            <returns>A path to a file.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FilePathField(System.String,System.String,System.String,System.Boolean,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field that lets the user select a path to a file.
            </summary>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="extensions">Comma separated list of allowed file extensions. Use <c>null</c> to allow any file extension.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <param name="options">Layout options.</param>
            <returns>A path to a file.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FolderPathField(UnityEngine.Rect,UnityEngine.GUIContent,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Draws a field that lets the user select a path to a folder.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <returns>A path to a folder.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FolderPathField(UnityEngine.Rect,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Draws a field that lets the user select a path to a folder.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <returns>A path to a folder.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FolderPathField(UnityEngine.GUIContent,System.String,System.String,System.Boolean,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field that lets the user select a path to a folder.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <param name="options">Layout options.</param>
            <returns>A path to a folder.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.FolderPathField(System.String,System.String,System.Boolean,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field that lets the user select a path to a folder.
            </summary>
            <param name="path">The current value.</param>
            <param name="parentPath">A parent path the path needs to be relative to. Use <c>null</c> for Unity project directory.</param>
            <param name="absolutePath">If <c>true</c> the path will be absolute. Otherwise the path will be relative to parentPath or to the Unity project directory.</param>
            <param name="useBackslashes">If <c>true</c> the path will be enforced to use backslashes. Otherwise the path will be enforced to use forward slashes.</param>
            <param name="options">Layout options.</param>
            <returns>A path to a folder.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.VectorPrefixSlideRect(UnityEngine.Rect,UnityEngine.Vector4)">
            <summary>
            Draws a prefix label for a vector field, that implements label dragging.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.VectorPrefixLabel(UnityEngine.Rect@,UnityEngine.GUIContent,UnityEngine.Vector4)">
            <summary>
            Draws a prefix label for a vector field, that implements label dragging.
            </summary>
            <param name="totalRect">The position and total size of the field.</param>
            <param name="label">The label content. If <c>null</c> this function does nothing.</param>
            <param name="value">The value for the vector field.</param>
            <returns>The vector scaled by label dragging.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.VectorPrefixLabel(UnityEngine.Rect@,System.String,UnityEngine.Vector4)">
            <summary>
            Draws a prefix label for a vector field, that implements label dragging.
            </summary>
            <param name="totalRect">The position and total size of the field.</param>
            <param name="label">The label content. If <c>null</c> this function does nothing.</param>
            <param name="value">The value for the vector field.</param>
            <returns>The vector scaled by label dragging.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.VectorPrefixLabel(UnityEngine.GUIContent,UnityEngine.Vector4)">
            <summary>
            Draws a prefix label for a vector field, that implements label dragging.
            </summary>
            <param name="label">The label content. If <c>null</c> this function does nothing.</param>
            <param name="value">The value for the vector field.</param>
            <returns>The vector scaled by label dragging.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.VectorPrefixLabel(System.String,UnityEngine.Vector4)">
            <summary>
            Draws a prefix label for a vector field, that implements label dragging.
            </summary>
            <param name="label">The label content. If <c>null</c> this function does nothing.</param>
            <param name="value">The value for the vector field.</param>
            <returns>The vector scaled by label dragging.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector2Field(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Vector2)">
            <summary>
            Draws a Vector2 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector2Field(UnityEngine.Rect,System.String,UnityEngine.Vector2)">
            <summary>
            Draws a Vector2 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector2Field(UnityEngine.Rect,UnityEngine.Vector2)">
            <summary>
            Draws a Vector2 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector2Field(UnityEngine.GUIContent,UnityEngine.Vector2,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector2 field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector2Field(System.String,UnityEngine.Vector2,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector2 field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector2Field(UnityEngine.Vector2,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector2 field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector3Field(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Vector3)">
            <summary>
            Draws a Vector3 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector3Field(UnityEngine.Rect,System.String,UnityEngine.Vector3)">
            <summary>
            Draws a Vector3 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector3Field(UnityEngine.Rect,UnityEngine.Vector3)">
            <summary>
            Draws a Vector3 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector3Field(UnityEngine.GUIContent,UnityEngine.Vector3,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector3 field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector3Field(System.String,UnityEngine.Vector3,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector3 field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector3Field(UnityEngine.Vector3,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector3 field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector4Field(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Vector4)">
            <summary>
            Draws a Vector4 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector4Field(UnityEngine.Rect,System.String,UnityEngine.Vector4)">
            <summary>
            Draws a Vector4 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector4Field(UnityEngine.Rect,UnityEngine.Vector4)">
            <summary>
            Draws a Vector4 field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector4Field(UnityEngine.GUIContent,UnityEngine.Vector4,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector4 field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector4Field(System.String,UnityEngine.Vector4,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector4 field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Vector4Field(UnityEngine.Vector4,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Vector4 field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ColorField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Color)">
            <summary>
            Draws a Color field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value"></param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ColorField(UnityEngine.Rect,System.String,UnityEngine.Color)">
            <summary>
            Draws a Color field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value"></param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ColorField(UnityEngine.Rect,UnityEngine.Color)">
            <summary>
            Draws a Color field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value"></param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ColorField(UnityEngine.GUIContent,UnityEngine.Color,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Color field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value"></param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ColorField(System.String,UnityEngine.Color,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Color field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value"></param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.ColorField(UnityEngine.Color,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a Color field.
            </summary>
            <param name="value"></param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Vector2,UnityEngine.Vector2,System.Boolean)">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="limits">The min and max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <returns>A Vector2 with X set as min value, and Y to set as max value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.Rect,System.String,UnityEngine.Vector2,UnityEngine.Vector2,System.Boolean)">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="limits">The min and max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <returns>A Vector2 with X set as min value, and Y to set as max value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.Rect,UnityEngine.Vector2,UnityEngine.Vector2,System.Boolean)">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <param name="limits">The min and max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <returns>A Vector2 with X set as min value, and Y to set as max value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.GUIContent,UnityEngine.Vector2,UnityEngine.Vector2,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="limits">The min and max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <param name="options">Layout options.</param>
            <returns>A Vector2 with X set as min value, and Y to set as max value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(System.String,UnityEngine.Vector2,UnityEngine.Vector2,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="limits">The min and max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <param name="options">Layout options.</param>
            <returns>A Vector2 with X set as min value, and Y to set as max value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.Vector2,UnityEngine.Vector2,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="value">Current value.</param>
            <param name="limits">The min and max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <param name="options">Layout options.</param>
            <returns>A Vector2 with X set as min value, and Y to set as max value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.Rect,UnityEngine.GUIContent,System.Single@,System.Single@,System.Single,System.Single,System.Boolean)">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="minValue">Current min value.</param>
            <param name="maxValue">Current max value.</param>
            <param name="minLimit">The min limit for the value.</param>
            <param name="maxLimit">The max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.Rect,System.String,System.Single@,System.Single@,System.Single,System.Single,System.Boolean)">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="minValue">Current min value.</param>
            <param name="maxValue">Current max value.</param>
            <param name="minLimit">The min limit for the value.</param>
            <param name="maxLimit">The max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.Rect,System.Single@,System.Single@,System.Single,System.Single,System.Boolean)">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="minValue">Current min value.</param>
            <param name="maxValue">Current max value.</param>
            <param name="minLimit">The min limit for the value.</param>
            <param name="maxLimit">The max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(UnityEngine.GUIContent,System.Single@,System.Single@,System.Single,System.Single,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="minValue">Current min value.</param>
            <param name="maxValue">Current max value.</param>
            <param name="minLimit">The min limit for the value.</param>
            <param name="maxLimit">The max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(System.String,System.Single@,System.Single@,System.Single,System.Single,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="minValue">Current min value.</param>
            <param name="maxValue">Current max value.</param>
            <param name="minLimit">The min limit for the value.</param>
            <param name="maxLimit">The max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.MinMaxSlider(System.Single@,System.Single@,System.Single,System.Single,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a slider for setting two values between a min and a max limit.
            </summary>
            <param name="minValue">Current min value.</param>
            <param name="maxValue">Current max value.</param>
            <param name="minLimit">The min limit for the value.</param>
            <param name="maxLimit">The max limit for the value.</param>
            <param name="showFields">Show fields for min and max value.</param>
            <param name="options">Layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RotationField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Quaternion,Sirenix.Utilities.Editor.QuaternionDrawMode)">
            <summary>
            Draws a rotation field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="mode">Draw mode for rotation field.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RotationField(UnityEngine.Rect,System.String,UnityEngine.Quaternion,Sirenix.Utilities.Editor.QuaternionDrawMode)">
            <summary>
            Draws a rotation field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="mode">Draw mode for rotation field.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RotationField(UnityEngine.Rect,UnityEngine.Quaternion,Sirenix.Utilities.Editor.QuaternionDrawMode)">
            <summary>
            Draws a rotation field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <param name="mode">Draw mode for rotation field.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RotationField(UnityEngine.GUIContent,UnityEngine.Quaternion,Sirenix.Utilities.Editor.QuaternionDrawMode,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a rotation field for a quaternion.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="mode">Draw mode for rotation field.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RotationField(System.String,UnityEngine.Quaternion,Sirenix.Utilities.Editor.QuaternionDrawMode,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a rotation field for a quaternion.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="mode">Draw mode for rotation field.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.RotationField(UnityEngine.Quaternion,Sirenix.Utilities.Editor.QuaternionDrawMode,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a rotation field for a quaternion.
            </summary>
            <param name="value">Current value.</param>
            <param name="mode">Draw mode for rotation field.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EulerField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Quaternion)">
            <summary>
            Draws an euler field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EulerField(UnityEngine.Rect,System.String,UnityEngine.Quaternion)">
            <summary>
            Draws an euler field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EulerField(UnityEngine.Rect,UnityEngine.Quaternion)">
            <summary>
            Draws an euler field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EulerField(UnityEngine.GUIContent,UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an euler field for a quaternion.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EulerField(System.String,UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an euler field for a quaternion.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EulerField(UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an euler field for a quaternion.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.AngleAxisField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Quaternion)">
            <summary>
            Draws an angle axis field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.AngleAxisField(UnityEngine.Rect,System.String,UnityEngine.Quaternion)">
            <summary>
            Draws an angle axis field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.AngleAxisField(UnityEngine.Rect,UnityEngine.Quaternion)">
            <summary>
            Draws an angle axis field for a quaternion.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.AngleAxisField(UnityEngine.GUIContent,UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an angle axis field for a quaternion.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.AngleAxisField(System.String,UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an angle axis field for a quaternion.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.AngleAxisField(UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws an angle axis field for a quaternion.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.QuaternionField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Quaternion)">
            <summary>
            Draws a quaternion field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.QuaternionField(UnityEngine.Rect,System.String,UnityEngine.Quaternion)">
            <summary>
            Draws a quaternion field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.QuaternionField(UnityEngine.Rect,UnityEngine.Quaternion)">
            <summary>
            Draws a quaternion field.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="value">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.QuaternionField(UnityEngine.GUIContent,UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a quaternion field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.QuaternionField(System.String,UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a quaternion field.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.QuaternionField(UnityEngine.Quaternion,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a quaternion field.
            </summary>
            <param name="value">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32,System.String[],UnityEngine.GUIStyle)">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32,System.String[])">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(UnityEngine.Rect,System.String,System.Int32,System.String[])">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(UnityEngine.Rect,System.Int32,System.String[])">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(UnityEngine.GUIContent,System.Int32,System.String[],UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(UnityEngine.GUIContent,System.Int32,System.String[],UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(System.String,System.Int32,System.String[],UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown(System.Int32,System.String[],UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown.
            </summary>
            <param name="selected">Current value.</param>
            <param name="itemNames">Names of selectable items.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,UnityEngine.GUIContent,``0,System.Collections.Generic.IList{``0})">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items">Selectable items.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.GUIContent,``0,System.Collections.Generic.IList{``0})">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items">Selectable items.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,UnityEngine.GUIContent,``0,``0[],System.String[],UnityEngine.GUIStyle)">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items">Selectable items.</param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,UnityEngine.GUIContent,``0,``0[],System.String[])">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items"></param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,System.String,``0,``0[],System.String[])">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items"></param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,``0,``0[],System.String[])">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="selected">Current value.</param>
            <param name="items"></param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.GUIContent,``0,``0[],System.String[],UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items"></param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.GUIContent,``0,``0[],System.String[],UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items"></param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(System.String,``0,``0[],System.String[],UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="items"></param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(``0,``0[],System.String[],UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a generic dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="selected">Current value.</param>
            <param name="items"></param>
            <param name="itemNames">Names of selectable items. If <c>null</c> ToString() will be used instead.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(UnityEngine.Rect,UnityEngine.GUIContent,System.Enum,UnityEngine.GUIStyle)">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(UnityEngine.Rect,UnityEngine.GUIContent,System.Enum)">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(UnityEngine.Rect,System.String,System.Enum)">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(UnityEngine.Rect,System.Enum)">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="selected">Current value.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(UnityEngine.GUIContent,System.Enum,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(UnityEngine.GUIContent,System.Enum,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(System.String,System.Enum,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumDropdown(System.Enum,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown for an enum or an enum mask.
            </summary>
            <param name="selected">Current value.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,UnityEngine.GUIContent,System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{``0},System.Boolean)">
            <summary>
            Draws a dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="items">Avaible items in the dropdown.</param>
            <param name="multiSelection">If <c>true</c> then the user can select multiple items. Otherwise the user can only select one item.</param>
            <returns><c>true</c> when the user has changed the selection. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,System.String,System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{``0},System.Boolean)">
            <summary>
            Draws a dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="items">Avaible items in the dropdown.</param>
            <param name="multiSelection">If <c>true</c> then the user can select multiple items. Otherwise the user can only select one item.</param>
            <returns><c>true</c> when the user has changed the selection. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.Rect,System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{``0},System.Boolean)">
            <summary>
            Draws a dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="rect">Position and size of the field.</param>
            <param name="selected">Current selection.</param>
            <param name="items">Avaible items in the dropdown.</param>
            <param name="multiSelection">If <c>true</c> then the user can select multiple items. Otherwise the user can only select one item.</param>
            <returns><c>true</c> when the user has changed the selection. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(UnityEngine.GUIContent,System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{``0},System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="items">Avaible items in the dropdown.</param>
            <param name="multiSelection">If <c>true</c> then the user can select multiple items. Otherwise the user can only select one item.</param>
            <param name="options">Layout options.</param>
            <returns><c>true</c> when the user has changed the selection. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(System.String,System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{``0},System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="items">Avaible items in the dropdown.</param>
            <param name="multiSelection">If <c>true</c> then the user can select multiple items. Otherwise the user can only select one item.</param>
            <param name="options">Layout options.</param>
            <returns><c>true</c> when the user has changed the selection. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.Dropdown``1(System.Collections.Generic.IList{System.Int32},System.Collections.Generic.IList{``0},System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="selected">Current selection.</param>
            <param name="items">Avaible items in the dropdown.</param>
            <param name="multiSelection">If <c>true</c> then the user can select multiple items. Otherwise the user can only select one item.</param>
            <param name="options">Layout options.</param>
            <returns><c>true</c> when the user has changed the selection. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(UnityEngine.Rect,UnityEngine.GUIContent,System.Enum,UnityEngine.GUIStyle)">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(UnityEngine.Rect,UnityEngine.GUIContent,System.Enum)">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(UnityEngine.Rect,System.String,System.Enum)">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(UnityEngine.Rect,System.Enum)">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="rect">Position and size of the field.</param>
            <param name="selected">Current selection.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(UnityEngine.GUIContent,System.Enum,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="style">GUIStyle for drawing the field. Set to <c>null</c> for default.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(UnityEngine.GUIContent,System.Enum,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(System.String,System.Enum,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="label">Label of field. Set to <c>null</c> for no label.</param>
            <param name="selected">Current selection.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorFields.EnumMaskDropdown(System.Enum,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a dropdown field for enum masks.
            </summary>
            <param name="selected">Current selection.</param>
            <param name="options">Layout options.</param>
            <returns>Value assigned to the field.</returns>
        </member>
        <member name="T:Sirenix.Utilities.Editor.SirenixEditorGUI">
            <summary>
            Collection of various editor GUI functions.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixEditorGUI.MixedValueDashChar">
            <summary>
            The mixed value dash character, to show when something has mixed values;
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixEditorGUI.DefaultFadeGroupDuration">
            <summary>
            Default fade group animation duration.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixEditorGUI.TabPageSlideAnimationDuration">
            <summary>
            Tab page slide animation duration.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixEditorGUI.ShakingAnimationDuration">
            <summary>
            Shaking animation duration.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixEditorGUI.ExpandFoldoutByDefault">
            <summary>
            Expand foldouts by default.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixEditorGUI.ShowButtonResultsByDefault">
            <summary>
            Show buttons results by default.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ObjectField(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Object,System.Type,System.Boolean,System.Boolean)">
            <summary>
            Draws a GUI field for objects.
            </summary>
            <param name="rect">The rect to draw the field in.</param>
            <param name="label">The label of the field.</param>
            <param name="value">The value of the field.</param>
            <param name="objectType">The object type for the field.</param>
            <param name="allowSceneObjects">If set to <c>true</c> then allow scene objects to be assigned to the field.</param>
            <param name="isReadOnly">If set to <c>true</c> the field is readonly.</param>
            <returns>The object assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ObjectField(UnityEngine.GUIContent,UnityEngine.Object,System.Type,System.Boolean,System.Boolean)">
            <summary>
            Draws an GUI field for objects.
            </summary>
            <param name="label">The label for the field.</param>
            <param name="value">The value of the field.</param>
            <param name="objectType">The object type for the field.</param>
            <param name="allowSceneObjects">If set to <c>true</c> then allow scene objects to be assigned to the field.</param>
            <param name="isReadOnly">If set to <c>true</c> the field is readonly.</param>
            <returns>The object assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ObjectField(System.Object,System.Type,UnityEngine.GUIContent,System.Object,System.Boolean)">
            <summary>
            Draws a GUI field for objects.
            </summary>
            <param name="key">The key for the field.</param>
            <param name="type">The type.</param>
            <param name="label">The label for the field.</param>
            <param name="value">The current value for the field.</param>
            <param name="allowSceneObjects">If set to <c>true</c> then allow scene objects to be assigned to the field.</param>
            <returns>
            The object assigned to the field.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.Title(System.String,System.String,UnityEngine.TextAlignment,System.Boolean,System.Boolean)">
            <summary>
            Draws a nicely formatted title with an optinal sub-title and horizontal ruler.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawColorField(UnityEngine.Rect,UnityEngine.Color,System.Boolean,System.Boolean)">
            <summary>
            Draws a GUI color field.
            </summary>
            <param name="rect">The rect to draw the field in.</param>
            <param name="color">The color of the field.</param>
            <param name="useAlphaInPreview">If set to <c>true</c> then use alpha in the preview.</param>
            <param name="showAlphaBar">If set to <c>true</c> then show alpha bar in the preview.</param>
            <returns>The color assigned to the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.WarningMessageBox(System.String,System.Boolean)">
            <summary>
            Draws a warning message box.
            </summary>
            <remarks>
            Also triggers a warning during validation checks done by <see cref="!:OdinInspectorValidationChecker"/>
            </remarks>
            <param name="message">The message.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawThickHorizontalSeperator(UnityEngine.Rect)">
            <summary>
            Draws a thick horizontal seperator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawThickHorizontalSeparator">
            <summary>
            Draws a thick horizontal seperator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawThickHorizontalSeparator(System.Single,System.Single)">
            <summary>
            Draws a thick horizontal seperator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawThickHorizontalSeperator(System.Single,System.Single,System.Single)">
            <summary>
            Draws a thick horizontal seperator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawHorizontalLineSeperator(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws a horizontal line seperator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawVerticalLineSeperator(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Draws a vertical line seperator.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ErrorMessageBox(System.String,System.Boolean)">
            <summary>
            Draws an error message box.
            </summary>
            <remarks>
            Also triggers an error during validation checks done by <see cref="!:OdinInspectorValidationChecker"/>
            </remarks>
            <param name="message">The message.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.InfoMessageBox(System.String,System.Boolean)">
            <summary>
            Draws a info message box.
            </summary>
            <param name="message">The message.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.MessageBox(System.String,System.Boolean)">
            <summary>
            Draws a message box.
            </summary>
            <param name="message">The message.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.MessageBox(System.String,UnityEditor.MessageType,System.Boolean)">
            <summary>
            Draws a message box.
            </summary>
            <param name="message">The message.</param>
            <param name="messageType">Type of the message.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.MessageBox(System.String,UnityEditor.MessageType,UnityEngine.GUIStyle,System.Boolean)">
            <summary>
            Draws a message box.
            </summary>
            <param name="message">The message.</param>
            <param name="messageType">Type of the message.</param>
            <param name="style">The style.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.MessageBox(System.String,UnityEditor.MessageType,UnityEngine.GUIStyle,System.Boolean,System.Action{UnityEditor.GenericMenu})">
            <summary>
            Draws a message box.
            </summary>
            <param name="message">The message.</param>
            <param name="messageType">Type of the message.</param>
            <param name="style">The style.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.PrivateMessageBox(System.String,UnityEditor.MessageType,UnityEngine.GUIStyle,System.Boolean,System.Action{UnityEditor.GenericMenu})">
            <summary>
            Draws a message box.
            </summary>
            <param name="message">The message.</param>
            <param name="messageType">Type of the message.</param>
            <param name="messageBoxStyle">The style of the message box.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DetailedMessageBox(System.String,System.String,UnityEditor.MessageType,System.Boolean,System.Boolean)">
            <summary>
            Draws a message box that can be expanded to show more details.
            </summary>
            <param name="message">The message of the message box.</param>
            <param name="detailedMessage">The detailed message of the message box.</param>
            <param name="messageType">Type of the message box.</param>
            <param name="hideDetailedMessage">If set to <c>true</c> the detailed message is hidden.</param>
            <param name="wide">If set to <c>true</c> the message box will be wide.</param>
            <returns>State of isFolded.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconMessageBox(System.String,Sirenix.OdinInspector.SdfIconType,System.Nullable{UnityEngine.Color},UnityEngine.GUIStyle,System.Action{UnityEditor.GenericMenu})">
            <summary>
            Draws a message box with the specified icon.
            </summary>
            <param name="message">The message to be displayed.</param>
            <param name="iconType">The icon to be displayed.</param>
            <param name="color">The color of the icon.</param>
            <param name="messageBoxStyle">The style of the message box.</param>
            <param name="onContextClick">The action to be invoked if the message box is right-clicked.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.HorizontalLineSeparator(System.Int32)">
            <summary>
            Draws a horizontal line separator.
            </summary>
            <param name="lineWidth">Width of the line.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.HorizontalLineSeparator(UnityEngine.Color,System.Int32)">
            <summary>
            Draws a horizontal line separator.
            </summary>
            <param name="color">The color of the line.</param>
            <param name="lineWidth">The size of the line.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.VerticalLineSeparator(System.Int32)">
            <summary>
            Draws a vertical line separator.
            </summary>
            <param name="lineWidth">Width of the line.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.VerticalLineSeparator(UnityEngine.Color,System.Int32)">
            <summary>
            Draws a vertical line separator.
            </summary>
            <param name="color">The color of the line.</param>
            <param name="lineWidth">Width of the line.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(Sirenix.Utilities.Editor.EditorIcon,System.Int32,System.Int32,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="icon">The editor icon for the button.</param>
            <param name="width">The width of the button.</param>
            <param name="height">The height of the button.</param>
            <param name="tooltip">The tooltip of the button.</param>
            <returns><c>true</c> if the button was pressed. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(Sirenix.Utilities.Editor.EditorIcon,UnityEngine.GUIStyle,System.Int32,System.Int32,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="icon">The editor icon for the button.</param>
            <param name="style">The GUI style for the button.</param>
            <param name="width">The width of the button.</param>
            <param name="height">The height of the button.</param>
            <param name="tooltip">The tooltip of the button.</param>
            <returns><c>true</c> if the button was pressed. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(UnityEngine.Rect,Sirenix.Utilities.Editor.EditorIcon)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="rect">The rect to draw the button in.</param>
            <param name="icon">The editor icon for the button.</param>
            <returns><c>true</c> if the button was pressed. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(UnityEngine.Rect,Sirenix.Utilities.Editor.EditorIcon,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="rect">The rect to draw the button in.</param>
            <param name="icon">The editor icon for the button.</param>
            <param name="tooltip">The tooltip of the button.</param>
            <returns><c>true</c> if the button was pressed. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(UnityEngine.Rect,Sirenix.Utilities.Editor.EditorIcon,UnityEngine.GUIStyle,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="rect">The rect to draw the button in.</param>
            <param name="icon">The editor icon for the button.</param>
            <param name="style">The GUI style for the button.</param>
            <param name="tooltip">The tooltip of the button.</param>
            <returns><c>true</c> if the button was pressed. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(UnityEngine.Rect,UnityEngine.Texture,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="rect">The rect to draw the button in.</param>
            <param name="icon">The icon texture.</param>
            <param name="tooltip">The tooltip for the button.</param>
            <returns><c>true</c> when the button is pressed.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(UnityEngine.Rect,UnityEngine.Texture,UnityEngine.GUIStyle,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="rect">The rect to draw the button in.</param>
            <param name="icon">The icon texture.</param>
            <param name="style">Style for the button.</param>
            <param name="tooltip">The tooltip for the button.</param>
            <returns><c>true</c> when the button is pressed.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(UnityEngine.Texture,System.Int32,System.Int32,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="icon">The icon texture.</param>
            <param name="width">Width of the button in pixels.</param>
            <param name="height">Height of the button in pixels.</param>
            <param name="tooltip">The tooltip for the button.</param>
            <returns><c>true</c> when the button is pressed.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconButton(UnityEngine.Texture,UnityEngine.GUIStyle,System.Int32,System.Int32,System.String)">
            <summary>
            Draws a GUI button with an icon.
            </summary>
            <param name="icon">The icon texture.</param>
            <param name="style">Style for the button.</param>
            <param name="width">Width of the button in pixels.</param>
            <param name="height">Height of the button in pixels.</param>
            <param name="tooltip">The tooltip for the button.</param>
            <returns><c>true</c> when the button is pressed.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconRepeatButton(Sirenix.Utilities.Editor.EditorIcon)">
            <summary>
            Draws a repeating icon button.
            </summary>
            <param name="icon">The icon for the button.</param>
            <returns><c>true</c> while the button is active. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconRepeatButton(Sirenix.Utilities.Editor.EditorIcon,System.Int32)">
            <summary>
            Draws a repeating icon button.
            </summary>
            <param name="icon">The icon for the button.</param>
            <param name="size">The size.</param>
            <returns><c>true</c> while the button is active. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IconRepeatButton(Sirenix.Utilities.Editor.EditorIcon,System.Int32,System.Int32)">
            <summary>
            Draws a repeating icon button.
            </summary>
            <param name="icon">The icon for the button.</param>
            <param name="width">The width of the button.</param>
            <param name="height">The height of the button.</param>
            <returns><c>true</c> while the button is active. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(System.String,System.Single,Sirenix.OdinInspector.SdfIconType,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="label">The button's label.</param>
            <param name="height">The button's height.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.GUIContent,System.Single,Sirenix.OdinInspector.SdfIconType,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="label">The button's label.</param>
            <param name="height">The button's height.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(System.String,System.Single,UnityEngine.Texture,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="label">The button's label.</param>
            <param name="height">The button's height.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.GUIContent,System.Single,UnityEngine.Texture,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="label">The button's label.</param>
            <param name="height">The button's height.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.Rect,System.String,Sirenix.OdinInspector.SdfIconType,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="rect">The button's rect.</param>
            <param name="label">The button's label.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.Rect,Sirenix.OdinInspector.SdfIconType,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="rect">The button's rect.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.Rect,Sirenix.OdinInspector.SdfIconType,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="rect">The button's rect.</param>
            <param name="icon">The button's icon.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.Rect,System.String,UnityEngine.Texture,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="rect">The button's rect.</param>
            <param name="label">The button's label.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.Rect,UnityEngine.GUIContent,Sirenix.OdinInspector.SdfIconType,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle,System.Boolean)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="rect">The button's rect.</param>
            <param name="label">The button's label.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SDFIconButton(UnityEngine.Rect,UnityEngine.GUIContent,UnityEngine.Texture,Sirenix.OdinInspector.IconAlignment,UnityEngine.GUIStyle)">
            <summary>
            Draws a SDF icon button.
            </summary>
            <param name="rect">The button's rect.</param>
            <param name="label">The button's label.</param>
            <param name="icon">The button's icon.</param>
            <param name="iconAlignment">The button's icon alignment. ButtonIconAlignment.LeftOfText by default.</param>
            <param name="style">The button's style.</param>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.CalculateMinimumSDFIconButtonWidth(System.String,UnityEngine.GUIStyle,System.Boolean,System.Single,System.Single@,System.Single@,System.Single@,System.Single@)">
            <summary>
            Calculates the minimum needed space for a SDF icon button where the label is still visible.
            </summary>
            <param name="label">The label of the SDF icon button.</param>
            <param name="buttonHeight">The height of the SDF icon button.</param>
            <returns>The minimum width of the SDF icon button.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarButton(Sirenix.Utilities.Editor.EditorIcon,System.Boolean)">
            <summary>
            Draws a toolbar icon button.
            </summary>
            <param name="icon">The icon for the button.</param>
            <param name="ignoreGUIEnabled">If true, the button clickable while GUI.enabled == false.</param>
            <returns>
              <c>true</c> if the button was pressed. Otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarButton(Sirenix.OdinInspector.SdfIconType,System.Boolean)">
            <summary>
            Draws a toolbar icon button.
            </summary>
            <param name="icon">The icon for the button.</param>
            <param name="ignoreGUIEnabled">If true, the button clickable while GUI.enabled == false.</param>
            <returns>
              <c>true</c> if the button was pressed. Otherwise <c>false</c>.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarButton(UnityEngine.GUIContent,System.Boolean)">
            <summary>
            Draws a toolbar icon button.
            </summary>
            <param name="content">The GUI content for the button.</param>
            <param name="selected">Whether the button state is selected or not</param>
            <returns><c>true</c> if the button was pressed. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarButton(System.String,System.Boolean)">
            <summary>
            Draws a toolbar icon button.
            </summary>
            <param name="label">The label for the button.</param>
            <param name="selected">Whether the button state is selected or not</param>
            <returns><c>true</c> if the button was pressed. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarToggle(System.Boolean,Sirenix.Utilities.Editor.EditorIcon)">
            <summary>
            Draws a toolbar toggle.
            </summary>
            <param name="isActive">Current state of the toggle.</param>
            <param name="icon">The icon for the toggle.</param>
            <returns>The state of the toggle.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarToggle(System.Boolean,UnityEngine.Texture)">
            <summary>
            Draws a toolbar toggle.
            </summary>
            <param name="isActive">Current state of the toggle.</param>
            <param name="icon">The icon for the toggle.</param>
            <returns>The state of the toggle.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarToggle(System.Boolean,UnityEngine.GUIContent)">
            <summary>
            Draws a toolbar toggle.
            </summary>
            <param name="isActive">Current state of the toggle.</param>
            <param name="content">The GUI content for the button.</param>
            <returns>The state of the toggle.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarToggle(System.Boolean,System.String)">
            <summary>
            Draws a toolbar toggle.
            </summary>
            <param name="isActive">Current state of the toggle.</param>
            <param name="text">The text for the toggle.</param>
            <returns>The state of the toggle.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarTab(System.Boolean,System.String)">
            <summary>
            Draws a toolbar tab.
            </summary>
            <param name="isActive">If <c>true</c> the tab will be the active tab.</param>
            <param name="label">Name for the tab.</param>
            <returns>State of isActive.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarTab(System.Boolean,UnityEngine.GUIContent)">
            <summary>
            Draws a toolbar tab.
            </summary>
            <param name="isActive">If <c>true</c> the tab will be the active tab.</param>
            <param name="label">Label for the tab.</param>
            <returns>State of isActive.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawSolidRect(UnityEngine.Rect,UnityEngine.Color,System.Boolean)">
            <summary>
            Draws a solid color rectangle.
            </summary>
            <param name="rect">The rect.</param>
            <param name="color">The color.</param>
            <param name="usePlaymodeTint">If <c>true</c> applies the user's playmdoe tint to the rect in playmode.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawSolidRect(System.Single,System.Single,UnityEngine.Color,System.Boolean)">
            <summary>
            Draws a solid color rectangle.
            </summary>
            <param name="width">The width.</param>
            <param name="height">The height.</param>
            <param name="color">The color.</param>
            <param name="usePlaymodeTint">If <c>true</c> applies the user's playmdoe tint to the rect in playmode.</param>
            <returns>The rect created.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawBorders(UnityEngine.Rect,System.Int32,System.Boolean)">
            <summary>
            Draws borders around a rect.
            </summary>
            <param name="rect">The rect.</param>
            <param name="borderWidth">The width of the border on all sides.</param>
            <param name="usePlaymodeTint">If <c>true</c> applies the user's playmdoe tint to the rect in playmode.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawBorders(UnityEngine.Rect,System.Int32,UnityEngine.Color,System.Boolean)">
            <summary>
            Draws borders around a rect.
            </summary>
            <param name="rect">The rect.</param>
            <param name="borderWidth">The width of the border on all sides.</param>
            <param name="color">The color of the border.</param>
            <param name="usePlaymodeTint">If <c>true</c> applies the user's playmdoe tint to the rect in playmode.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawBorders(UnityEngine.Rect,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Draws borders around a rect.
            </summary>
            <param name="rect">The rect.</param>
            <param name="left">The left size.</param>
            <param name="right">The right size.</param>
            <param name="top">The top size.</param>
            <param name="bottom">The bottom size.</param>
            <param name="usePlaymodeTint">If <c>true</c> applies the user's playmdoe tint to the rect in playmode.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DrawBorders(UnityEngine.Rect,System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Color,System.Boolean)">
            <summary>
            Draws borders around a rect.
            </summary>
            <param name="rect">The rect.</param>
            <param name="left">The left size.</param>
            <param name="right">The right size.</param>
            <param name="top">The top size.</param>
            <param name="bottom">The bottom size.</param>
            <param name="color">The color of the borders.</param>
            <param name="usePlaymodeTint">If <c>true</c> applies the user's playmdoe tint to the rect in playmode.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.ToolbarSearchField(System.String,System.Boolean,System.Single)">
            <summary>
            Draws a toolbar search field.
            </summary>
            <param name="searchText">The current search text.</param>
            <param name="forceFocus">If set to <c>true</c> the force focus on the field.</param>
            <param name="marginLeftRight">The left and right margin.</param>
            <returns>The current search text.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SearchField(UnityEngine.Rect,System.String,System.Boolean,System.String)">
            <summary>
            Draws a search field.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginHorizontalToolbar(System.Single,System.Int32)">
            <summary>
            Begins a horizontal toolbar. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndHorizontalToolbar"/>.
            </summary>
            <param name="height">The height of the toolbar.</param>
            <param name="paddingTop">Padding for the top of the toolbar.</param>
            <returns>The rect of the horizontal toolbar.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginHorizontalToolbar(UnityEngine.GUIStyle,System.Single,System.Int32)">
            <summary>
            Begins a horizontal toolbar. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndHorizontalToolbar" />.
            </summary>
            <param name="style">The style for the toolbar.</param>
            <param name="height">The height of the toolbar.</param>
            <param name="topPadding">The top padding.</param>
            <returns>
            The rect of the horizontal toolbar.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndHorizontalToolbar">
            <summary>
            Ends a horizontal toolbar started by <see cref="!:BeginHorizontalToolbar(int, int)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginIndentedHorizontal(UnityEngine.GUILayoutOption[])">
            <summary>
            Begins a horizontal indentation. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndIndentedHorizontal"/>.
            </summary>
            <param name="options">The GUI layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginIndentedHorizontal(UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins a horizontal indentation. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndIndentedHorizontal"/>.
            </summary>
            <param name="style">The style of the indentation.</param>
            <param name="options">The GUI layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndIndentedHorizontal">
            <summary>
            Ends a identation horizontal layout group started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginIndentedHorizontal(UnityEngine.GUILayoutOption[])"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginIndentedVertical(UnityEngine.GUILayoutOption[])">
            <summary>
            Begins a vertical indentation. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndIndentedVertical"/>.
            </summary>
            <param name="options">The GUI layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginIndentedVertical(UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins a vertical indentation. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndIndentedVertical"/>.
            </summary>
            <param name="style">The style of the indentation.</param>
            <param name="options">The GUI layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndIndentedVertical">
            <summary>
            Ends a identation vertical layout group started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginIndentedVertical(UnityEngine.GUILayoutOption[])"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.IndentSpace">
            <summary>
            Indents by the current indent value, <see cref="P:Sirenix.Utilities.Editor.GUIHelper.CurrentIndentAmount"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.MenuButton(System.Int32,System.String,System.Boolean,UnityEngine.Texture)">
            <summary>
            Draws a menu button.
            </summary>
            <param name="indent">The indent of the button.</param>
            <param name="text">The text of the button.</param>
            <param name="isActive">The current state of the button.</param>
            <param name="icon">The texture icon for the button.</param>
            <returns>The current state of the button.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.Boolean)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup"/>.
            </summary>
            <param name="key">The key for the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.Boolean,System.Single@)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup" />.
            </summary>
            <param name="key">The key for the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
            <param name="t">A value between 0 and 1 indicating how expanded the fade group is.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.Object,System.Boolean)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup"/>.
            </summary>
            <param name="primaryKey">The primary key for the fade group.</param>
            <param name="secondaryKey">The secondly key for the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.String,System.Boolean)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup"/>.
            </summary>
            <param name="key">The key for the fade group.</param>
            <param name="name">The name of the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.Boolean,System.Single)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup"/>.
            </summary>
            <param name="key">The key for the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
            <param name="duration">The duration of fade in and out.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.Boolean,System.Single@,System.Single)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup" />.
            </summary>
            <param name="key">The key for the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
            <param name="t">A value between 0 and 1 indicating how expanded the fade group is.</param>
            <param name="duration">The duration of fade in and out.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.Object,System.Boolean,System.Single)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup"/>.
            </summary>
            <param name="primaryKey">The primary key for the fade group.</param>
            <param name="secondaryKey">The secondly key for the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
            <param name="duration">The duration of fade in and out.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Object,System.String,System.Boolean,System.Single)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup"/>.
            </summary>
            <param name="key">The key for the fade group.</param>
            <param name="name">The name of the fade group.</param>
            <param name="isVisible">Current state of the fade group.</param>
            <param name="duration">The duration of fade in and out.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginFadeGroup(System.Single)">
            <summary>
            Begins a fade group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup"/>.
            </summary>
            <param name="t">The current fading value between 0 and 1.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndFadeGroup">
            <summary>
            Ends a fade group started by any BeginFadeGroup.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.Foldout(System.Boolean,System.String,UnityEngine.GUIStyle)">
            <summary>
            Draws a foldout field where clicking on the label toggles to the foldout too.
            </summary>
            <param name="isVisible">The current state of the foldout.</param>
            <param name="label">The label of the foldout.</param>
            <param name="style">The GUI style.</param>
            <returns>
            The current state of the foldout.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.Foldout(System.Boolean,UnityEngine.GUIContent,UnityEngine.GUIStyle)">
            <summary>
            Draws a foldout field where clicking on the label toggles to the foldout too.
            </summary>
            <param name="isVisible">The current state of the foldout.</param>
            <param name="label">The label of the foldout.</param>
            <param name="style">The GUI style.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.Foldout(System.Boolean,UnityEngine.GUIContent,UnityEngine.Rect@,UnityEngine.GUIStyle)">
            <summary>
            Draws a foldout field where clicking on the label toggles to the foldout too.
            </summary>
            <param name="isVisible">The current state of the foldout.</param>
            <param name="label">The label of the foldout.</param>
            <param name="valueRect">The value rect.</param>
            <param name="style">The GUI style.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.Foldout(UnityEngine.Rect,System.Boolean,UnityEngine.GUIContent,UnityEngine.GUIStyle)">
            <summary>
            Draws a foldout field where clicking on the label toggles to the foldout too.
            </summary>
            <param name="rect">The rect to draw the foldout field in.</param>
            <param name="isVisible">The current state of the foldout.</param>
            <param name="label">The label of the foldout.</param>
            <param name="style">The style.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginBox(System.String,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBox"/>.
            </summary>
            <param name="label">The label of the box.</param>
            <param name="centerLabel">If set to <c>true</c> then center label.</param>
            <param name="options">The GUI layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginBox(UnityEngine.GUIContent,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBox"/>.
            </summary>
            <param name="label">The label of the box.</param>
            <param name="centerLabel">If set to <c>true</c> then center label.</param>
            <param name="options">The GUI layout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginBox(UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBox"/>.
            </summary>
            <param name="options">The GUI layout options.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndBox">
            <summary>
            Ends drawing a box started by any BeginBox.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginBoxHeader">
            <summary>
            Begins drawing a box header. Remember to end with <seealso cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBoxHeader"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndBoxHeader">
            <summary>
            Ends drawing a box header started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToolbarBoxHeader(System.Single)"/>,
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToolbarBox(System.String,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a box with toolbar style header. Remember to end with <seealso cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBox"/>.
            </summary>
            <param name="label">Label for box header.</param>
            <param name="centerLabel">If <c>true</c> the label will be drawn in the center of the box header.</param>
            <param name="options">GUILayout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToolbarBox(UnityEngine.GUIContent,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a box with toolbar style header. Remember to end with <seealso cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBox"/>.
            </summary>
            <param name="label">Label for box header.</param>
            <param name="centerLabel">If <c>true</c> the label will be drawn in the center of the box header.</param>
            <param name="options">GUILayout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToolbarBox(UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a box with toolbar style header. Remember to end with <seealso cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBox"/>.
            </summary>
            <param name="options">GUILayout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBox">
            <summary>
            Ends the drawing a box with a toolbar style header started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToolbarBox(UnityEngine.GUILayoutOption[])"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToolbarBoxHeader(System.Single)">
            <summary>
            Begins drawing a toolbar style box header. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBoxHeader"/>.
            </summary>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToolbarBoxHeader">
            <summary>
            Ends the drawing of a toolbar style box header started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToolbarBoxHeader(System.Single)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginLegendBox(System.String,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a legend style box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndLegendBox"/>.
            </summary>
            <param name="label">The label for the legend style box.</param>
            <param name="centerLabel">If <c>true</c> the label will be drawn in the center of the box.</param>
            <param name="options">GUILayout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginLegendBox(UnityEngine.GUIContent,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a legend style box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndLegendBox"/>.
            </summary>
            <param name="label">The label for the legend style box.</param>
            <param name="centerLabel">If <c>true</c> the label will be drawn in the center of the box.</param>
            <param name="options">GUILayout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginLegendBox(UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a legend style box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndLegendBox"/>.
            </summary>
            <param name="options">GUILayout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndLegendBox">
            <summary>
            Ends the drawing of a legend style box started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginLegendBox(UnityEngine.GUILayoutOption[])"/>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginInlineBox(UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing an inline box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndInlineBox"/>.
            </summary>
            <param name="options">The GUI layout options.</param>
            <returns>The rect of the box.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndInlineBox">
            <summary>
            Ends drawing an inline box started by any BeginInlineBox.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.StartShakingGroup(System.Object)">
            <summary>
            Starts the shaking animation of a shaking group.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.StartShakingGroup(System.Object,System.Single)">
            <summary>
            Starts the shaking animation of a shaking group.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginShakeableGroup(System.Object)">
            <summary>
            Begins a shakeable group.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndShakeableGroup(System.Object)">
            <summary>
            Ends the shakeable group.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginVerticalMenuList(System.Object)">
            <summary>
            Begins drawing a vertical menu list.
            </summary>
            <param name="key">The key for the menu list.</param>
            <returns>The rect created.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginMenuListItem(System.Boolean@,System.Boolean@,System.Boolean)">
            <summary>
            Begins drawing a menu list item. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndMenuListItem"/>
            </summary>
            <param name="isSelected">Value indicating whether the item is selected.</param>
            <param name="isMouseDown">Value indicating if the mouse is pressed on the item.</param>
            <param name="setAsSelected">If set to <c>true</c> the item is set as selected..</param>
            <returns>The rect used for the item.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndMenuListItem">
            <summary>
            Ends drawing a menu list item started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginMenuListItem(System.Boolean@,System.Boolean@,System.Boolean)"/>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndVerticalMenuList">
            <summary>
            Ends drawing a vertical menu list started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginVerticalMenuList(System.Object)"/>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginVerticalList(System.Boolean,System.Boolean,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a vertical list.
            </summary>
            <param name="drawBorder">If set to <c>true</c> borders will be drawn around the vertical list.</param>
            <param name="drawDarkBg">If set to <c>true</c> a dark background will be drawn.</param>
            <param name="options">The GUI layout options.</param>
            <returns>The rect used for the list.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndVerticalList">
            <summary>
            Ends drawing a vertical list started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginVerticalList(System.Boolean,System.Boolean,UnityEngine.GUILayoutOption[])"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginListItem(System.Boolean,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a list item.
            </summary>
            <param name="allowHover">If set to <c>true</c> the item can be hovered with the mouse.</param>
            <param name="style">The style for the vertical list item.</param>
            <param name="options">The GUI layout options.</param>
            <returns>The rect used for the item.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginListItem(System.Boolean,UnityEngine.GUIStyle,UnityEngine.Color,UnityEngine.Color,UnityEngine.Color,UnityEngine.Color,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a list item.
            </summary>
            <param name="allowHover">If set to <c>true</c> the item can be hovered with the mouse.</param>
            <param name="style">The style for the vertical list item.</param>
            <param name="options">The GUI layout options.</param>
            <param name="evenColor">The color for even elements.</param>
            <param name="oddColor">The color for odd elements.</param>
            <param name="evenHoverColor">The color for even elements when hovered.</param>
            <param name="oddHoverColor">The color for odd elements when hovered.</param>
            <returns>The rect used for the item.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndListItem">
            <summary>
            Ends drawing a list item started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginListItem(System.Boolean,UnityEngine.GUIStyle,UnityEngine.GUILayoutOption[])"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.CreateAnimatedTabGroup(System.Object)">
            <summary>
            Creates a animated tab group.
            </summary>
            <param name="key">The key for the tab group..</param>
            <returns>An animated tab group.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToggleGroup(System.Object,System.Boolean@,System.Boolean@,System.String)">
            <summary>
            Begins drawing a toggle group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToggleGroup"/>.
            </summary>
            <param name="key">The key of the group.</param>
            <param name="enabled">Value indicating if the group is enabled.</param>
            <param name="visible">Value indicating if the group is visible.</param>
            <param name="title">The title of the group.</param>
            <returns>Value indicating if the group is toggled.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToggleGroup(System.Object,System.Boolean@,System.Boolean@,System.String,System.Single)">
            <summary>
            Begins drawing a toggle group. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToggleGroup"/>.
            </summary>
            <param name="key">The key of the group.</param>
            <param name="enabled">Value indicating if the group is enabled.</param>
            <param name="visible">Value indicating if the group is visible.</param>
            <param name="title">The title of the group.</param>
            <param name="animationDuration">Duration of the animation.</param>
            <returns>Value indicating if the group is toggled.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndToggleGroup">
            <summary>
            Ends drawing a toggle group started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginToggleGroup(System.Object,System.Boolean@,System.Boolean@,System.String,System.Single)"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginHorizontalAutoScrollBox(System.Object,UnityEngine.GUILayoutOption[])">
            <summary>
            Begins drawing a horizontal auto scroll box. Remember to end with <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndHorizontalAutoScrollBox"/>.
            </summary>
            <param name="key">The for the field.</param>
            <param name="options">The GUILayout options.</param>
            <returns>The rect used for the field.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.EndHorizontalAutoScrollBox">
            <summary>
            Ends drawing a horizontal auto scroll box started by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.BeginHorizontalAutoScrollBox(System.Object,UnityEngine.GUILayoutOption[])"/>.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SlideRectInt(UnityEngine.Rect,System.Int32,System.Int32)">
            <summary>
            Creates a rect that can be grabbed and pulled to change a value up or down.
            </summary>
            <param name="rect">The grabbable rect.</param>
            <param name="id">The control ID for the sliding.</param>
            <param name="value">The current value.</param>
            <returns>
            The current value.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SlideRectLong(UnityEngine.Rect,System.Int32,System.Int64)">
            <summary>
            Creates a rect that can be grabbed and pulled to change a value up or down.
            </summary>
            <param name="rect">The grabbable rect.</param>
            <param name="id">The control ID for the sliding.</param>
            <param name="t">The current value.</param>
            <returns>
            The current value.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SlideRect(UnityEngine.Rect,System.Int32,System.Single)">
            <summary>
            Creates a rect that can be grabbed and pulled to change a value up or down.
            </summary>
            <param name="rect">The grabbable rect.</param>
            <param name="id">The control ID for the sliding.</param>
            <param name="t">The current value.</param>
            <returns>
            The current value.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SlideRectDouble(UnityEngine.Rect,System.Int32,System.Double)">
            <summary>
            Creates a rect that can be grabbed and pulled to change a value up or down.
            </summary>
            <param name="rect">The grabbable rect.</param>
            <param name="id">The control ID for the sliding.</param>
            <param name="t">The current value.</param>
            <returns>
            The current value.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SlideRect(UnityEngine.Rect,UnityEditor.MouseCursor)">
            <summary>
            Creates a rect that can be grabbed and pulled
            </summary>
            <param name="rect">The grabbable rect.</param>
            <param name="cursor">The cursor.</param>
            <returns>
            The the mouse delta position.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.SlideRect(UnityEngine.Vector2,UnityEngine.Rect)">
            <summary>
            Creates a rect that can be grabbed and pulled
            </summary>
            <param name="position">The position.</param>
            <param name="rect">The grabbable rect.</param>
            <returns>
            The the mouse delta position.
            </returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DynamicPrimitiveField``1(UnityEngine.GUIContent,``0,UnityEngine.GUILayoutOption[])">
            <summary>
            Draws a field for a value of type T - dynamically choosing an appropriate drawer for the type.
            Currently supported are: char, string, sbyte, byte, short, ushort, int, uint, long, ulong, float, double, decimal, Guid and all enums.
            </summary>
            <typeparam name="T">The type of the value to draw.</typeparam>
            <param name="label">The label of the fields.</param>
            <param name="value">The value to draw.</param>
            <param name="options">The layout options.</param>
            <returns>The possibly changed value.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DynamicPrimitiveFieldCanDraw``1">
            <summary>
            Checks whether a given type can be drawn as a dynamic field by <see cref="M:Sirenix.Utilities.Editor.SirenixEditorGUI.DynamicPrimitiveField``1(UnityEngine.GUIContent,``0,UnityEngine.GUILayoutOption[])"/>
            </summary>
            <typeparam name="T">The type to check.</typeparam>
            <returns>True if the type can be drawn, otherwise false.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.GetFeatureRichControlRect(UnityEngine.GUIContent,System.Int32@,System.Boolean@,UnityEngine.Rect@,UnityEngine.GUILayoutOption[])">
            <summary>
            Gets the feature rich control rect.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.GetFeatureRichControlRect(UnityEngine.GUIContent,System.Int32,System.Int32@,System.Boolean@,UnityEngine.Rect@,UnityEngine.GUILayoutOption[])">
            <summary>
            Gets the feature rich control rect.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.GetFeatureRichControl(UnityEngine.Rect,UnityEngine.GUIContent,System.Int32@,System.Boolean@)">
            <summary>
            Creates a control ID that handles keyboard control, focused editor window, indentation and prefix label correctly.
            </summary>
            <param name="rect">The rect to make a feature rich control for.</param>
            <param name="label">The label for the control. Leave <c>null</c> for no label.</param>
            <param name="controlId">The created control ID.</param>
            <param name="hasKeyboardFocus">A value indicating whether or not the control has keyboard focus.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.SirenixEditorGUI.GetFeatureRichControl(UnityEngine.Rect,System.Int32@,System.Boolean@)">
            <summary>
            Creates a control ID that handles keyboard control, focused editor window, indentation and prefix label correctly.
            </summary>
            <param name="rect">The rect to make a feature rich control for.</param>
            <param name="controlId">The created control ID.</param>
            <param name="hasKeyboardFocus">A value indicating whether or not the control has keyboard focus.</param>
        </member>
        <member name="T:Sirenix.Utilities.Editor.SirenixGUIStyles">
            <summary>
            Collection of GUIStyles used by Sirenix.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.ValidatorGreen">
            <summary>
            Validator Green
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.InspectorOrange">
            <summary>
            Inspector Orange
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.SerializerYellow">
            <summary>
            Serializer Yellow
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.GreenValidColor">
            <summary>
            Green valid color
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.RedErrorColor">
            <summary>
            Red error color
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.YellowWarningColor">
            <summary>
            Yellow warning color
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.BorderColor">
            <summary>
            Border color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.BoxBackgroundColor">
            <summary>
            Box background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.DarkEditorBackground">
            <summary>
            Dark editor background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.EditorWindowBackgroundColor">
            <summary>
            Editor window background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.MenuBackgroundColor">
            <summary>
            Menu background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.HeaderBoxBackgroundColor">
            <summary>
            Header box background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.HighlightedButtonColor">
            <summary>
            Highlighted Button Color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.HighlightedTextColor">
            <summary>
            Highlight text color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.HighlightPropertyColor">
            <summary>
            Highlight property color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemColorHoverEven">
            <summary>
            List item hover color for every other item.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemColorHoverOdd">
            <summary>
            List item hover color for every other item.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemDragBg">
            <summary>
            List item drag background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemDragBgColor">
            <summary>
            List item drag background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.ColumnTitleBg">
            <summary>
            Column title background colors.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.DefaultSelectedMenuTreeColorDarkSkin">
            <summary>
            The default background color for when a menu item is selected.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.DefaultSelectedInactiveMenuTreeColorDarkSkin">
            <summary>
            The default background color for when a menu item is selected.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.DefaultSelectedMenuTreeColorLightSkin">
            <summary>
            The default background color for when a menu item is selected.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.DefaultSelectedInactiveMenuTreeColorLightSkin">
            <summary>
            The default background color for when a menu item is selected.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.MouseOverBgOverlayColor">
            <summary>
            A mouse over background overlay color.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.DefaultSelectedMenuTreeColor">
            <summary>
            The default background color for when a menu item is selected.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.DefaultSelectedMenuTreeInactiveColor">
            <summary>
            The default background color for when a menu item is selected.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemEven">
            <summary>
            List item background color for every other item. OBSOLETE: Use ListItemColorEven instead.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemOdd">
            <summary>
            List item background color for every other item. OBSOLETE: Use ListItemColorOdd instead.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemColorEven">
            <summary>
            List item color for every other item.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItemColorOdd">
            <summary>
            List item color for every other item.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.MenuButtonActiveBgColor">
            <summary>
            Menu button active background color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.MenuButtonBorderColor">
            <summary>
            Menu button border color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.MenuButtonColor">
            <summary>
            Menu button color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.MenuButtonHoverColor">
            <summary>
            Menu button hover color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.LightBorderColor">
            <summary>
            A light border color.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.Temporary">
            <summary>
            Bold label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.TagButton">
            <summary>
            Tag Button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BoldLabel">
            <summary>
            Bold label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BoldLabelCentered">
            <summary>
            Centered bold label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BoxContainer">
            <summary>
            Box container style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.Popup">
            <summary>
            Popup style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BoxHeaderStyle">
            <summary>
            Box header style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.Button">
            <summary>
            Button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ButtonSelected">
            <summary>
            Button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ButtonLeft">
            <summary>
            Left button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ButtonLeftSelected">
            <summary>
            Left button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ButtonMid">
            <summary>
            Mid button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ButtonMidSelected">
            <summary>
            Mid button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ButtonRight">
            <summary>
            Right button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ButtonRightSelected">
            <summary>
            Right button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.DropDownMiniButton">
            <summary>
            Pane Options Button
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButton">
            <summary>
            Left button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButtonSelected">
            <summary>
            Left button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButtonLeft">
            <summary>
            Left button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButtonLeftSelected">
            <summary>
            Left button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButtonMid">
            <summary>
            Mid button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButtonMidSelected">
            <summary>
            Mid button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButtonRight">
            <summary>
            Right button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniButtonRightSelected">
            <summary>
            Right button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ColorFieldBackground">
            <summary>
            Color field background style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.Foldout">
            <summary>
            Foldout style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.IconButton">
            <summary>
            Icon button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.Label">
            <summary>
            Label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.WhiteLabel">
            <summary>
            White label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BlackLabel">
            <summary>
            Black label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.LabelCentered">
            <summary>
            Centered label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.WhiteLabelCentered">
            <summary>
            White centered label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MiniLabelCentered">
            <summary>
            Centered mini label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.LeftAlignedCenteredLabel">
            <summary>
            Left Aligned Centered Label
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.LeftAlignedGreyMiniLabel">
            <summary>
            Left aligned grey mini label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.LeftAlignedGreyLabel">
            <summary>
            Left aligned grey label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.CenteredGreyMiniLabel">
            <summary>
            Centered grey mini label
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.LeftAlignedWhiteMiniLabel">
            <summary>
            Left right aligned white mini label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.CenteredWhiteMiniLabel">
            <summary>
            Centered white mini label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.CenteredBlackMiniLabel">
            <summary>
            Centered black mini label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ListItem">
            <summary>
            List item style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MenuButtonBackground">
            <summary>
            Menu button background style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.None">
            <summary>
            No style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.OdinEditorWrapper">
            <summary>
            Odin Editor Wrapper.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.PaddingLessBox">
            <summary>
            Padding less box style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ContentPadding">
            <summary>
            Content Padding
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.PropertyPadding">
            <summary>
            Property padding.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.PropertyMargin">
            <summary>
            Property margin.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.RichTextLabel">
            <summary>
            Rich text label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.RightAlignedGreyMiniLabel">
            <summary>
            Right aligned grey mini label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.RightAlignedWhiteMiniLabel">
            <summary>
            Right aligned white mini label style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.SectionHeader">
            <summary>
            Section header style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.SectionHeaderCentered">
            <summary>
            Section header style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToggleGroupBackground">
            <summary>
            Toggle group background style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToggleGroupCheckbox">
            <summary>
            Toggle group checkbox style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToggleGroupPadding">
            <summary>
            Toggle group padding style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToggleGroupTitleBg">
            <summary>
            Toggle group title background style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToolbarBackground">
            <summary>
            Toolbar background style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToolbarButton">
            <summary>
            Toolbar button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToolbarButtonSelected">
            <summary>
            Toolbar button selected style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToolbarSearchCancelButton">
            <summary>
            Toolbar search cancel button style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToolbarSearchTextField">
            <summary>
            Toolbar search field style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ToolbarTab">
            <summary>
            Toolbar tab style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.Title">
            <summary>
            Title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BoldTitle">
            <summary>
            Bold title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BoldTitleCentered">
            <summary>
            Centered bold title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BoldTitleRight">
            <summary>
            Right aligned bold title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.TitleCentered">
            <summary>
            Centered title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.TitleRight">
            <summary>
            Right aligned title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.Subtitle">
            <summary>
            Subtitle style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.SubtitleCentered">
            <summary>
            Centered sub-title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.SubtitleRight">
            <summary>
            Right aligned sub-title style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MessageBox">
            <summary>
            Message box style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.DetailedMessageBox">
            <summary>
            Detailed Message box style.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MultiLineLabel">
            <summary>
            Multiline Label
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.MultiLineCenteredLabel">
            <summary>
            Centered Multiline Label
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.CenteredTextField">
            <summary>
            Centered Text Field
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.BottomBoxPadding">
            <summary>
            Gets the bottom box padding.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.PaneOptions">
            <summary>
            Unitys PaneOptions GUIStyle.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ContainerOuterShadow">
            <summary>
            Unitys ProjectBrowserTextureIconDropShadow GUIStyle.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ContainerOuterShadowGlow">
            <summary>
            Unitys TL SelectionButton PreDropGlow GUIStyle.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.ModuleHeader">
            <summary>
            Unitys ShurikenModuleTitle GUIStyle.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.SirenixGUIStyles.CardStyle">
            <summary>
            Draw this one manually with: new Color(1, 1, 1, EditorGUIUtility.isProSkin ? 0.25f : 0.45f)
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.SirenixGUIStyles.SDFIconButtonLabelStyle">
            <summary>
            SDFIconButton Label.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.InstanceCreator">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.InstanceCreator.ControlID">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.InstanceCreator.CurrentSelectedType">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.InstanceCreator.HasCreatedInstance">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.InstanceCreator.Type">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.InstanceCreator.GetCreatedInstance">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.InstanceCreator.Show(System.Type,System.Int32,UnityEngine.Rect)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.ObjectPicker`1">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ObjectPicker`1.GetObjectPicker(System.Object)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.ObjectPicker`1.CurrentSelectedObject">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.ObjectPicker`1.IsReadyToClaim">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.ObjectPicker`1.IsPickerOpen">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ObjectPicker`1.ClaimObject">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ObjectPicker`1.ShowObjectPicker(System.Object,System.Boolean,UnityEngine.Rect,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.ObjectPicker">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ObjectPicker.GetObjectPicker(System.Object,System.Type)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.ObjectPicker.CurrentSelectedObject">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.ObjectPicker.IsReadyToClaim">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.ObjectPicker.IsPickerOpen">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ObjectPicker.ClaimObject">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ObjectPicker.ShowObjectPicker(System.Boolean,UnityEngine.Rect,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.ObjectPicker.ShowObjectPicker(System.Object,System.Boolean,UnityEngine.Rect,System.Boolean)">
            <summary>
            Not yet documented.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.AssetUtilities">
            <summary>
            Utility functions for Unity assets.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetUtilities.GetAllAssetsOfType``1">
            <summary>
            Gets all assets of the specified type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetUtilities.GetAllAssetsOfType(System.Type,System.String)">
            <summary>
            Gets all assets of the specified type.
            </summary>
            <param name="type">The type of assets to find.</param>
            <param name="folderPath">The asset folder path.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetUtilities.GetAllAssetsOfTypeWithProgress(System.Type,System.String)">
            <summary>
            Gets all assets of the specified type.
            </summary>
            <param name="type">The type of assets to find.</param>
            <param name="folderPath">The asset folder path.</param>
        </member>
        <member name="T:Sirenix.Utilities.Editor.AssetUtilities.AssetSearchResult">
            <summary>
            Asset search helper.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.AssetUtilities.AssetSearchResult.Asset">
            <summary>
            The asset object.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.AssetUtilities.AssetSearchResult.CurrentIndex">
            <summary>
            Current index.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.AssetUtilities.AssetSearchResult.NumberOfResults">
            <summary>
            Search result count.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetUtilities.CanCreateNewAsset``1">
            <summary>
            Tests if an asset can be created from a type.
            </summary>
            <typeparam name="T">The type to test.</typeparam>
            <returns><c>true</c> if an asset can be created. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetUtilities.CanCreateNewAsset``1(System.Type@)">
            <summary>
            Tests if an asset can be created from a type.
            </summary>
            <typeparam name="T">The type to test.</typeparam>
            <param name="baseType">The base asset type.</param>
            <returns><c>true</c> if an asset can be created. Otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetUtilities.GetAssetLocation(UnityEngine.Object)">
            <summary>
            Gets project path to the specified asset.
            </summary>
            <param name="obj">The asset object.</param>
            <returns>The path to the asset.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.AssetUtilities.CreateNewAsset``1(System.String,System.String)">
            <summary>
            Creates a new asset of the specified type.
            </summary>
            <typeparam name="T">The type of the asset.</typeparam>
            <param name="path">Project path to the new asset.</param>
            <param name="assetName">The name of the asset.</param>
        </member>
        <member name="T:Sirenix.Utilities.Editor.Clipboard">
            <summary>
            Functions for accessing the clipboard.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.Clipboard.CurrentCopyMode">
            <summary>
            Gets the current copy mode.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.Copy``1(``0,Sirenix.Utilities.Editor.CopyModes)">
            <summary>
            Copies the specified object.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="obj">The object.</param>
            <param name="copyMode">The copy mode.</param>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.Copy``1(``0)">
            <summary>
            Copies the specified object.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.CanPaste(System.Type)">
            <summary>
            Determines whether this instance can paste the specified type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.CanPaste``1">
            <summary>
            Determines whether this instance can paste the specified type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.IsEmpty">
            <summary>
            Determines whether or not the Clipboard contains any instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.TryPaste``1(``0@)">
            <summary>
            Tries the paste.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.Paste``1">
            <summary>
            Copies or gets the current object in the clipboard.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.Clipboard.Paste">
            <summary>
            Copies or gets the current object in the clipboard.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.CopyModes">
            <summary>
            The various modes of copying an object to the clipboard.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.CopyModes.DeepCopy">
            <summary>
            Deep copy.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.CopyModes.ShallowCopy">
            <summary>
            Shallow Copy.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.Editor.CopyModes.CopyReference">
            <summary>
            Reference Copy.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUITimeHelper">
            <summary>
            A utility class for getting delta time for the GUI editor.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.EditorTimeHelper">
            <summary>
            A utility class for getting delta time for the GUI editor.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.FuzzySearch">
            <summary>
            Compare strings and produce a distance score between them.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.FuzzySearch.Contains(System.String@,System.String@,System.Single,System.Boolean,System.Boolean)">
            <summary>
            Determines whether if the source is within the search.
            </summary>
            <param name="source">The source string.</param>
            <param name="target">The target string.</param>
            <param name="ignoreCase">Should the algorithm ignore letter case?.</param>
            <param name="abbreviation">Should the algorithm attempt to search on an abbreviation of the source?.</param>
            <param name="threshold">Threshold for what is considered to be within the search. 0 will return everything and 1 will only return exact matches.</param>
            <returns>True if the source is within the search. Otherwise false.</returns>
        </member>
        <member name="M:Sirenix.Utilities.Editor.FuzzySearch.Compare(System.String@,System.String@,System.Boolean,System.Boolean)">
            <summary>
            Compares the target to the source and returns a distance score.
            </summary>
            <param name="source">The source string.</param>
            <param name="target">The target string.</param>
            <param name="ignoreCase"></param>
            <param name="abbreviation"></param>
            <returns>Distance score. 0 is no match, and 1 is exact match.</returns>
        </member>
        <member name="T:Sirenix.Utilities.Editor.GUIFrameCounter">
            <summary>
            A utility class for properly counting frames and helps determine when a frame has started in an editor window.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIFrameCounter.FrameCount">
            <summary>
            Gets the frame count.
            </summary>
        </member>
        <member name="P:Sirenix.Utilities.Editor.GUIFrameCounter.IsNewFrame">
            <summary>
            Gets a value indicating whether this instance is new frame.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.GUIFrameCounter.Update">
            <summary>
            Updates the frame counter and returns itself.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.IHideObjectMembers">
            <summary>
            Hides the ObjectMembers in Visual Studio IntelliSense
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.IHideObjectMembers.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.IHideObjectMembers.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.IHideObjectMembers.GetType">
            <summary>
            Gets the type.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.IHideObjectMembers.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents this instance.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.Editor.TextureUtilities">
            <summary>
            Collection of texture functions.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.TextureUtilities.LoadImage(System.Int32,System.Int32,System.Byte[])">
            <summary>
            Creates a new texture with no mimapping, linier colors, and calls texture.LoadImage(bytes), DontDestroyOnLoad(tex) and sets hideFlags = DontUnloadUnusedAsset | DontSaveInEditor.
            
            Old description no longer relevant as we've moved past version 2017.
            Loads an image from bytes with the specified width and height. Use this instead of someTexture.LoadImage() if you're compiling to an assembly. Unity has moved the method in 2017, 
            and Unity's assembly updater is not able to fix it for you. This searches for a proper LoadImage method in multiple locations, and also handles type name conflicts.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.TextureUtilities.CropTexture(UnityEngine.Texture,UnityEngine.Rect)">
            <summary>
            Crops a Texture2D into a new Texture2D.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.TextureUtilities.ResizeByBlit(UnityEngine.Texture,System.Int32,System.Int32,UnityEngine.FilterMode)">
            <summary>
            Resizes a texture by blitting, this allows you to resize unreadable textures.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.Editor.TextureUtilities.ConvertSpriteToTexture(UnityEngine.Sprite)">
            <summary>
            Converts a Sprite to a Texture2D.
            </summary>
            <param name="sprite"></param>
            <returns></returns>
        </member>
        <member name="T:Sirenix.Utilities.AssemblyTypeFlags">
            <summary>
            AssemblyTypeFlags is a bitmask used to filter types and assemblies related to Unity.
            </summary>
            <seealso cref="T:Sirenix.Utilities.AssemblyUtilities"/>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.None">
            <summary>
            Excludes all types.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.UserTypes">
            <summary>
            UserTypes includes all custom user scripts that are not located in an editor or plugin folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.PluginTypes">
            <summary>
            PluginTypes includes all types located in the plugins folder and are not located in an editor folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.UnityTypes">
            <summary>
            UnityTypes includes all types depended on UnityEngine and from UnityEngine, except editor, plugin and user types.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.UserEditorTypes">
            <summary>
            UserEditorTypes includes all custom user scripts that are located in an editor folder but not in a plugins folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.PluginEditorTypes">
            <summary>
            PluginEditorTypes includes all editor types located in the plugins folder.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.UnityEditorTypes">
            <summary>
            UnityEditorTypes includes all editor types that are not user editor types nor plugin editor types.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.OtherTypes">
            <summary>
            OtherTypes includes all other types that are not depended on UnityEngine or UnityEditor.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.CustomTypes">
            <summary>
            CustomTypes includes includes all types manually added to the Unity project.
            This includes UserTypes, UserEditorTypes, PluginTypes and PluginEditorTypes.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.GameTypes">
            <summary>
            GameTypes includes all assemblies that are likely to be included in builds.
            This includes UserTypes, PluginTypes, UnityTypes and OtherTypes.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.EditorTypes">
            <summary>
            EditorTypes includes UserEditorTypes, PluginEditorTypes and UnityEditorTypes.
            </summary>
        </member>
        <member name="F:Sirenix.Utilities.AssemblyTypeFlags.All">
            <summary>
            All includes UserTypes, PluginTypes, UnityTypes, UserEditorTypes, PluginEditorTypes, UnityEditorTypes and OtherTypes.
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.AssemblyUtilities">
            <summary>
            A utility class for finding types in various asssembly.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.GetAllAssemblies">
            <summary>
            Gets an <see cref="T:Sirenix.Utilities.ImmutableList"/> of all assemblies in the current <see cref="T:System.AppDomain"/>.
            </summary>
            <returns>An <see cref="T:Sirenix.Utilities.ImmutableList"/> of all assemblies in the current <see cref="T:System.AppDomain"/>.</returns>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.GetAssemblyTypeFlag(System.Reflection.Assembly)">
            <summary>
            Gets the <see cref="T:Sirenix.Utilities.AssemblyTypeFlags"/> for a given assembly.
            </summary>
            <param name="assembly">The assembly.</param>
            <returns>The <see cref="T:Sirenix.Utilities.AssemblyTypeFlags"/> for a given assembly.</returns>
            <exception cref="T:System.NullReferenceException"><paramref name="assembly"/> is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.IsDependentOn(System.Reflection.Assembly,System.Reflection.Assembly)">
            <summary>
            Determines whether an assembly is depended on another assembly.
            </summary>
            <param name="assembly">The assembly.</param>
            <param name="otherAssembly">The other assembly.</param>
            <returns>
              <c>true</c> if <paramref name="assembly"/> has a reference in <paramref name="otherAssembly"/> or <paramref name="assembly"/> is the same as <paramref name="otherAssembly"/>.
            </returns>
            <exception cref="T:System.NullReferenceException"><paramref name="assembly"/> is null.</exception>
            <exception cref="T:System.NullReferenceException"><paramref name="otherAssembly"/> is null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.IsDynamic(System.Reflection.Assembly)">
            <summary>
            Determines whether the assembly module is a of type <see cref="T:System.Reflection.Emit.ModuleBuilder"/>.
            </summary>
            <param name="assembly">The assembly.</param>
            <returns>
              <c>true</c> if the specified assembly of type <see cref="T:System.Reflection.Emit.ModuleBuilder"/>; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.ArgumentNullException">assembly</exception>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.GetAssemblyDirectory(System.Reflection.Assembly)">
            <summary>
            Gets the full file path to a given assembly.
            </summary>
            <param name="assembly">The assembly.</param>
            <returns>The full file path to a given assembly, or <c>Null</c> if no file path was found.</returns>
            <exception cref="T:System.NullReferenceException"><paramref name="assembly"/> is Null.</exception>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.GetAssemblyFilePath(System.Reflection.Assembly)">
            <summary>
            Gets the full directory path to a given assembly.
            </summary>
            <param name="assembly">The assembly.</param>
            <returns>The full directory path in which a given assembly is located, or <c>Null</c> if no file path was found.</returns>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.GetTypeByCachedFullName(System.String)">
            <summary>
            Gets the type.
            </summary>
            <param name="fullName">The full name of the type, with or without any assembly information.</param>
        </member>
        <member name="M:Sirenix.Utilities.AssemblyUtilities.GetTypes(Sirenix.Utilities.AssemblyTypeFlags)">
            <summary>
            Get types from the current AppDomain with a specified <see cref="T:Sirenix.Utilities.AssemblyTypeFlags"/> filter.
            </summary>
            <param name="assemblyTypeFlags">The <see cref="T:Sirenix.Utilities.AssemblyTypeFlags"/> filters.</param>
            <returns>Types from the current AppDomain with the specified <see cref="T:Sirenix.Utilities.AssemblyTypeFlags"/> filters.</returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinderExtensions.FindMember(System.Type)">
            <summary>
            <para>Find members of the given type, while providing good error messages based on the following search filters provided.</para>
            <para>See <see cref="T:Sirenix.Utilities.MemberFinder"/> for more information.</para>
            </summary>
        </member>
        <member name="T:Sirenix.Utilities.MemberFinder">
            <summary>
            MemberFinder is obsolete, and has been replacted by <see cref="!:Sirenix.OdinInspector.Editor.ValueResolvers.ValueResolver" /> and <see cref="!:Sirenix.OdinInspector.Editor.ActionResolvers.ActionResolver" />. 
            Use cases that do not fit those utlities should use manual reflection that is hand-optimized for the best performance in the given case.
            <para />
            MemberFinder was a utility class often used by Odin drawers to find fields, methods, and
            properties while providing good user-friendly error messages based on the search criteria.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberFinder"/> class.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Sirenix.Utilities.MemberFinder"/> class.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.Start``1">
            <summary>
            <para>Find members of the given type, while providing good error messages based on the following search filters provided.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.Start(System.Type)">
            <summary>
            <para>Find members of the given type, while providing good error messages based on the following search filters provided.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasNoParameters">
            <summary>
            Can be true for both fields, properties and methods.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsDeclaredOnly">
            <summary>
            Exclude members found in base-types.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters(System.Type)">
            <summary>
            <para>Only include methods with the following parameter.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters(System.Type,System.Type)">
            <summary>
            <para>Only include methods with the following parameters.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters(System.Type,System.Type,System.Type)">
            <summary>
            <para>Only include methods with the following parameters.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters(System.Type,System.Type,System.Type,System.Type)">
            <summary>
            <para>Only include methods with the following parameters.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters``1">
            <summary>
            <para>Only include methods with the following parameters.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters``2">
            <summary>
            <para>Only include methods with the following parameters.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters``3">
            <summary>
            <para>Only include methods with the following parameters.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasParameters``4">
            <summary>
            <para>Only include methods with the following parameters.</para>
            <para>Calling this will also exclude fields and properties.</para>
            <para>Parameter type inheritance is supported.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasReturnType(System.Type,System.Boolean)">
            <summary>
            Determines whether [has return type] [the specified return type].
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.HasReturnType``1(System.Boolean)">
            <summary>
            Can be true for both fields, properties and methods.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsFieldOrProperty">
            <summary>
            Calls IsField() and IsProperty().
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsStatic">
            <summary>
            Only include static members. By default, both static and non-static members are included.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsInstance">
            <summary>
            Only include non-static members. By default, both static and non-static members are included.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsNamed(System.String)">
            <summary>
            Specify the name of the member.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsProperty">
            <summary>
            <para>Excludes fields and methods if nether IsField() or IsMethod() is called. Otherwise includes properties.</para>
            <para>By default, all member types are included.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsMethod">
            <summary>
            <para>Excludes fields and properties if nether IsField() or IsProperty() is called. Otherwise includes methods.</para>
            <para>By default, all member types are included.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsField">
            <summary>
            <para>Excludes properties and methods if nether IsProperty() or IsMethod() is called. Otherwise includes fields.</para>
            <para>By default, all member types are included.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsPublic">
            <summary>
            <para>Excludes non-public members if IsNonPublic() has not yet been called. Otherwise includes public members.</para>
            <para>By default, both public and non-public members are included.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.IsNonPublic">
            <summary>
            <para>Excludes public members if IsPublic() has not yet been called. Otherwise includes non-public members.</para>
            <para>By default, both public and non-public members are included.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.ReturnsVoid">
            <summary>
            Excludes fields and properties, and only includes methods with a return type of void.
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.GetMember``1">
            <summary>
            <para>Gets the member based on the search filters provided</para>
            <para>Returns null if no member was found.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.GetMember``1(System.String@)">
            <summary>
            <para>Gets the member based on the search filters provided, and provides a proper error message if no members was found.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.GetMember(System.String@)">
            <summary>
            <para>Gets the member based on the search filters provided, and provides a proper error message if no members was found.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.TryGetMember``1(``0@,System.String@)">
            <summary>
            <para>Try gets the member based on the search filters provided, and provides a proper error message if no members was found.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.TryGetMember(System.Reflection.MemberInfo@,System.String@)">
            <summary>
            <para>Try gets the member based on the search filters provided, and provides a proper error message if no members was found.</para>
            </summary>
        </member>
        <member name="M:Sirenix.Utilities.MemberFinder.TryGetMembers(System.Reflection.MemberInfo[]@,System.String@)">
            <summary>
            <para>Try gets all members based on the search filters provided, and provides a proper error message if no members was found.</para>
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.Editor.IResizableColumn.ColWidth">
            <summary>
            Gets or sets the width of the col.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.Editor.IResizableColumn.MinWidth">
            <summary>
            Gets or sets the minimum width.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.Editor.IResizableColumn.PreserveWidth">
            <summary>
            Gets a value indicating whether the width should be preserved when the table itself gets resiszed.
            </summary>
        </member>
        <member name="P:Sirenix.OdinInspector.Editor.IResizableColumn.Resizable">
            <summary>
            Gets a value indicating whether this <see cref="T:Sirenix.OdinInspector.Editor.IResizableColumn"/> is resizable.
            </summary>
        </member>
        <member name="T:Sirenix.OdinInspector.Editor.UnityEditorEventUtility">
            <summary>
            This class contains utility methods for subscribing to various UnityEditor events reliably and safely across all Odin-supported versions of Unity.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.Editor.UnityEditorEventUtility.DelayActionThreadSafe(System.Action)">
            <summary>
            Sometimes, someone accidentally overrides a delay action subscription to <see cref="F:UnityEditor.EditorApplication.delayCall"/> 
            by setting the value instead of using the += operator as should be done,
            which can be done because in many versions of Unity it is a field, and not an event. 
            (In some versions of Unity it is an event, though, and in this case, this method acts as a wrapper 
            to subscribe reliably, no matter the nature of the backing event.)
            This method subscribes to a lot of different callbacks, in the hopes of catching at least one.
            <para />
            As opposed to <see cref="M:Sirenix.OdinInspector.Editor.UnityEditorEventUtility.DelayAction(System.Action)"/>, this method is safe to call from any thread, and will
            delay the actual subscription to a safe time.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.Editor.UnityEditorEventUtility.DelayAction(System.Action)">
            <summary>
            Sometimes, an idiot overrides a delay action subscription to <see cref="F:UnityEditor.EditorApplication.delayCall"/>,
            which can be done because the people at Unity didn't know what events were once upon a time.
            This method subscribes to a lot of different callbacks, in the hopes of catching at least one.
            </summary>
        </member>
        <member name="M:Sirenix.OdinInspector.Editor.UnityEditorEventUtility.DelayAction(System.Action,System.Boolean)">
            <summary>
            Sometimes, an idiot overrides a delay action subscription to <see cref="F:UnityEditor.EditorApplication.delayCall"/>,
            which can be done because the people at Unity didn't know what events were once upon a time.
            This method subscribes to a lot of different callbacks, in the hopes of catching at least one.
            </summary>
        </member>
        <member name="E:Sirenix.OdinInspector.Editor.UnityEditorEventUtility.DuringSceneGUI">
            <summary>
            In 2019.1+, this event subscribes to SceneView.duringSceneGui. In 2018.4 and lower, it subscribes to SceneView.onSceneGUIDelegate.
            </summary>
        </member>
        <member name="E:Sirenix.OdinInspector.Editor.UnityEditorEventUtility.EditorApplication_delayCall">
            <summary>
            In 2020.1, Unity changed EditorApplication.delayCall from a field to an event, meaning 
            we now have to use reflection to access it consistently across all versions of Unity.
            </summary>
        </member>
    </members>
</doc>
