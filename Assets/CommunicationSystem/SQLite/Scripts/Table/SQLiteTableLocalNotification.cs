using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using UnityEngine;

namespace AmaSQLite {
    public class SQLiteTableLocalNotification : SQLiteService {
        private const  string TABLE_NAME                     = "LocalNotification";
        private const  string COLUMN_ID                      = "id";
        private const  string COLUMN_NOTIFICATION_ID         = "notificationId";
        private const  string COLUMN_CLONED_ID               = "clonedId";
        private const  string COLUMN_EXPECTED_SHOW_TIME_UNIX = "expectedShowTimeUnix";
        private const  string COLUMN_SCHEDULE_TIME_UNIX      = "scheduleTimeUnix";
        private const  string COLUMN_RECEIVE_TIME_UNIX       = "receiveTimeUnix";
        private const  string COLUMN_EVENT_LOGGED            = "eventLogged";
        private static bool   isInit                         = false;

        private void CheckCreateTable() {
            if (!isInit) {
                string cmd = $"CREATE TABLE IF NOT EXISTS {TABLE_NAME} " +
                             $"( {COLUMN_ID} INTEGER PRIMARY KEY," +
                             $" {COLUMN_NOTIFICATION_ID} TEXT," +
                             $" {COLUMN_CLONED_ID} INTEGER," +
                             $" {COLUMN_EXPECTED_SHOW_TIME_UNIX} INTEGER," +
                             $" {COLUMN_SCHEDULE_TIME_UNIX} INTEGER," +
                             $" {COLUMN_RECEIVE_TIME_UNIX} INTEGER," +
                             $" {COLUMN_EVENT_LOGGED} INTEGER )";
                ExecuteNonQuery(cmd);
                isInit = true;
            }
        }

        private void InsertOrReplace(LocalNotificationEntity localNotifEntity) {
            CheckCreateTable();
            string idQuery = $"SELECT {COLUMN_ID}" +
                             $" FROM {TABLE_NAME}" +
                             $" WHERE {COLUMN_NOTIFICATION_ID} = '{localNotifEntity.notificationId}'" +
                             $" AND {COLUMN_EXPECTED_SHOW_TIME_UNIX} = {localNotifEntity.expectedShowTimeUnix}";
            //Debug.LogError(idQuery);
            ExecuteQuery(idQuery, delegate(SQLiteResult result) {
                if (result.rows.Count == 0) {
                    // không có notification setup trước đó -> tạo mới
                    string cmd =
                        $"INSERT INTO {TABLE_NAME} " +
                        $"({COLUMN_NOTIFICATION_ID}," +
                        $" {COLUMN_CLONED_ID}," +
                        $" {COLUMN_EXPECTED_SHOW_TIME_UNIX}," +
                        $" {COLUMN_SCHEDULE_TIME_UNIX}," +
                        $" {COLUMN_RECEIVE_TIME_UNIX}," +
                        $" {COLUMN_EVENT_LOGGED})" +
                        $" VALUES" +
                        $"( '{localNotifEntity.notificationId}'," +
                        $" {localNotifEntity.clonedId}," +
                        $" {localNotifEntity.expectedShowTimeUnix}," +
                        $" {localNotifEntity.scheduleTimeUnix}," +
                        $" {localNotifEntity.receiveTimeUnix}," +
                        $" {localNotifEntity.eventLogged} )";
                    //Debug.LogError(cmd);
                    ExecuteNonQuery(cmd);
                } else {
                    //Debug.LogError("Column ID" + result.GetIntValueInRow(0,0));
                    string cmd =
                        $"INSERT OR REPLACE INTO {TABLE_NAME} " +
                        $"( {COLUMN_ID}," +
                        $" {COLUMN_NOTIFICATION_ID}," +
                        $" {COLUMN_CLONED_ID}," +
                        $" {COLUMN_EXPECTED_SHOW_TIME_UNIX}," +
                        $" {COLUMN_SCHEDULE_TIME_UNIX}," +
                        $" {COLUMN_RECEIVE_TIME_UNIX}," +
                        $" {COLUMN_EVENT_LOGGED} )" +
                        $" VALUES" +
                        $"( {result.GetIntValueInRow(0, 0)}," +
                        $" '{localNotifEntity.notificationId}'," +
                        $" {localNotifEntity.clonedId}," +
                        $" {localNotifEntity.expectedShowTimeUnix}," +
                        $" {localNotifEntity.scheduleTimeUnix}," +
                        $" {localNotifEntity.receiveTimeUnix}," +
                        $" {localNotifEntity.eventLogged} )";

                    //Debug.LogError(cmd);
                    ExecuteNonQuery(cmd);
                }
            });
        }

        public static void InsertOrUpdateIfExist(LocalNotificationEntity localNotifEntity) {
            LocalNotificationDBAction localNotifDBAction = new LocalNotificationDBAction();
            localNotifDBAction.Init(localNotifEntity);
            SQLiteDBActionCoroutine.Instance.Enqueue(localNotifDBAction);
        }

        public static List<LocalNotificationEntity> GetAllUnloggedNotification() {
            List<LocalNotificationEntity> result = new List<LocalNotificationEntity>();
            SQLiteTableLocalNotification tableLocalNotification = new SQLiteTableLocalNotification();
            string cmd = $"SELECT * FROM {TABLE_NAME} WHERE {COLUMN_EVENT_LOGGED} = FALSE";
            tableLocalNotification.ExecuteQuery(cmd, (data) => {
                for (int i = 0, n = data.rows.Count; i < n; i++) {
                    LocalNotificationEntity entity = new LocalNotificationEntity(
                        data.GetValueInRow(i, 1),
                        data.GetIntValueInRow(i, 2),
                        data.GetLongValueInRow(i, 3),
                        data.GetLongValueInRow(i, 4),
                        data.GetLongValueInRow(i, 5),
                        data.GetIntValueInRow(i, 6));
                    result.Add(entity);
                }
            });
            return result;
        }

        private class LocalNotificationDBAction : DBAction {
            private LocalNotificationEntity localNotifEntity;

            public void Init(LocalNotificationEntity localNotifEntity) {
                this.localNotifEntity = localNotifEntity;
            }

            public void DoAction() {
                SQLiteTableLocalNotification tableLocalNotification = new SQLiteTableLocalNotification();
                tableLocalNotification.InsertOrReplace(localNotifEntity);
            }
        }
    }
}