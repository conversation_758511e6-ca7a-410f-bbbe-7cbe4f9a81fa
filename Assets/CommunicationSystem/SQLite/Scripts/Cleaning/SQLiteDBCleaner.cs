using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace AmaSQLite
{
    [Serializable]
    public class SQLiteDBCleanConfig
    {
        public int sizeExceedKB;
        public string handleQuery;

        public SQLiteDBCleanConfig()
        {
            sizeExceedKB = 0;
            handleQuery = string.Empty;
        }
    }

    public class SQLiteDBCleaner
    {
        private const string EVENT_CHECK_CLEAN_SQLITE_DONE = "check_clean_sqlite_done";
        private const string PARAM_RESULT = "result";
        private const string PARAM_SIZE_KB_BEFORE = "size_kb_before";
        private const string PARAM_SIZE_KB_AFTER = "size_kb_after";

        public static void CheckCleanDB()
        {
            string cleanResult = "fail";
            SQLiteDBCleanConfig config = InitializeConfig();
            long currentDBSize = GetDBFileSizeInKB();
            if (CheckIfCleanable(currentDBSize, config))
            {
                int rows = 0;
                SQLiteService DB = new SQLiteService();
                DB.ExecuteNonQuery(config.handleQuery, (rowsCallback) =>
                {
                    rows = rowsCallback;
                });
                if (rows > 0)
                {
                    DB.Vacuum();
                    cleanResult = "success";
                }
                long dbSizeAfter = GetDBFileSizeInKB();
                LogCheckCleanDBDone(cleanResult, currentDBSize, dbSizeAfter);
            }
        }

        private static bool CheckIfCleanable(long currentDBSize, SQLiteDBCleanConfig config)
        {
            return config.sizeExceedKB > 0 && currentDBSize >= config.sizeExceedKB && !string.IsNullOrWhiteSpace(config.handleQuery);
        }

        private static SQLiteDBCleanConfig InitializeConfig()
        {
            SQLiteDBCleanConfig result = null;
            //if (ConfigManager.Instance.GameConfigsFrb != null) result = ConfigManager.Instance.GameConfigsFrb.sqliteDBCleanConfig;
            if (result == null) result = new SQLiteDBCleanConfig();
            return result;
        }

        public static long GetDBFileSizeInKB()
        {
            long fileSize = -1;
            string path = Application.persistentDataPath + "/" + SQLiteService.DATABASE_NAME;
            if (File.Exists(path))
            {
                FileInfo fileInfo = new FileInfo(path);
                fileSize = fileInfo.Length / 1024;
            }
            return fileSize;
        }

        private static void LogCheckCleanDBDone(string result, long sizeBefore, long sizeAfter)
        {
            /*
            string eventName = EVENT_CHECK_CLEAN_SQLITE_DONE;
            int count = AnalyticsHelper.GetAccumulatedCount(eventName);
            AnalyticsHelper.Instance.LogNormalAnalyticMultipleKeyValue(eventName,
                PARAM_RESULT, result,
                PARAM_SIZE_KB_BEFORE, sizeBefore,
                PARAM_SIZE_KB_AFTER, sizeAfter,
                AnalyticsHelper.ACCUMULATED_COUNT, count
            );
            AnalyticsHelper.SetAccummulatedCount(eventName, ++count);
            */
        }
    }
}
