using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using AmaSQLite;
using UnityEngine;
using UnityEngine.UI;

public class QueryEvent : MonoBehaviour
{
    [SerializeField] private InputField queryInput;
    [SerializeField] private Text queryResult;

    public void Show()
    {
        queryInput.text = string.Empty;
        queryResult.text = string.Empty;
        gameObject.SetActive(true);
    }

    public void Hide()
    {
        gameObject.SetActive(false);
    }

    public void ClearQueryResult()
    {
        queryResult.text = string.Empty;
    }

    public void CopyQueryResult()
    {
        GUIUtility.systemCopyBuffer = queryResult.text;
    }

    public void RunQuery()
    {
        if (!string.IsNullOrEmpty(queryInput.text))
        {
            string queryStr = queryInput.text;
            SQLiteService DB = new SQLiteService();
            string result = string.Empty;
            if (queryStr.ToLower().Contains("select"))
            {
                DB.ExecuteQuery(queryStr, (data) =>
                {
                    result = data.ToStringFull();
                });
            }
            else
            {
                DB.ExecuteNonQuery(queryStr, (rows) =>
                {
                    result = rows.ToString();
                });
            }
            queryResult.text = result;
        }
    }

    public void ListTablesInfo()
    {
        queryInput.text = "WITH all_tables AS (SELECT name FROM sqlite_master WHERE type = 'table') " +
            "SELECT at.name table_name, pti.* " +
            "FROM all_tables at INNER JOIN pragma_table_info(at.name) pti " +
            "ORDER BY table_name";
        RunQuery();
    }
}
