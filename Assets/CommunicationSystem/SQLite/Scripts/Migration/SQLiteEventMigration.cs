using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace AmaSQLite
{
    public class SQLiteEventMigration : MonoSingleton<SQLiteEventMigration>
    {
        private const string PREF_DONE_MIGRATION = "PREF_DONE_MIGRATION";
        private const string PREF_LAST_APP_VERSION = "PREF_LAST_APP_VERSION";
        private List<MigratedEvent> migratedEvents;

        private void Init()
        {
            migratedEvents = new List<MigratedEvent>()
            {
                new MigratedPassTutorial(),
                new MigratedDoneDemogSurvey(),
                new MigratedSongUnlock(),
                new MigratedUniqueSongStart(),
                new MigratedUniqueSongEnd(),
                new MigratedSongStart(),
                new MigratedSongResult(),
                new MigratedSongClick(),
                new MigratedSongAp(),
                new MigratedMeStart(),
                new MigratedMeResult(),
                new MigratedSongEnd()
            };
        }

        public void MigrateEvents()
        {
            Init();
            if (ShouldMigrate())
            {
                StartCoroutine(Migrate());
            }
            CheckUpdateLastVersion();
            Destroy(this.gameObject, migratedEvents.Count + 10f);
        }

        private bool ShouldMigrate()
        {
            string lastVer = PlayerPrefs.GetString(PREF_LAST_APP_VERSION, "00.000.000");
            return AppIsNewerVersionThan(lastVer) && IsTutorialDone();
        }

        private bool IsTutorialDone()
        {
            bool isTutorialDone = false;
            /*
            if (PlayerDataService.Instance != null)
            {
                isTutorialDone = PlayerDataService.Instance.isTutorialDone;
            }
            */
            return isTutorialDone;
        }

        private void CheckUpdateLastVersion()
        {
            string lastVer = PlayerPrefs.GetString(PREF_LAST_APP_VERSION, "00.000.000");
            if (AppIsNewerVersionThan(lastVer))
            {
                
            }
            else
            {

            }
            PlayerPrefs.SetString(PREF_LAST_APP_VERSION, Application.version);
            PlayerPrefs.Save();
        }

        private bool AppIsNewerVersionThan(string version)
        {
            return CompareVersion(Application.version, version) > 0;
        }

        private int CompareVersion(string version1, string version2)
        {
            return string.Compare(version1, version2);
        }

        private IEnumerator Migrate()
        {
            for (int i = 0, n = migratedEvents.Count; i < n; i++)
            {
                MigratedEvent migrate = migratedEvents[i];
                if (!IsDoneMigration(migrate))
                {
                    migrate.Migrate();
                    SaveDoneMigration(migrate);
                    yield return new WaitForEndOfFrame();
                }
            }
        }

        private void SaveDoneMigration(MigratedEvent migration)
        {
            string className = GetMigrationClassName(migration);
            string doneMigrations = PlayerPrefs.GetString(PREF_DONE_MIGRATION, string.Empty);
            doneMigrations += "," + className;
            PlayerPrefs.SetString(PREF_DONE_MIGRATION, doneMigrations);
            PlayerPrefs.Save();
        }

        private string GetMigrationClassName(MigratedEvent migration)
        {
            return migration.GetType().Name;
        }

        private bool IsDoneMigration(MigratedEvent migration)
        {
            string className = GetMigrationClassName(migration);
            string doneMigrations = PlayerPrefs.GetString(PREF_DONE_MIGRATION, string.Empty);
            return doneMigrations.Contains(className);
        }
    }
}

