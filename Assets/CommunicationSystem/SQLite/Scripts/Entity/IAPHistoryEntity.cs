using System.Threading;
using Newtonsoft.Json;

namespace AmaSQLite
{
    public class IAPHistoryEntity
    {
        public string id;
        public int qty;
        public string purchaseDate;

        public IAPHistoryEntity() { }

        [JsonConstructor]
        public IAPHistoryEntity(string id, int qty, string purchaseDate)
        {
            this.id = id;
            this.qty = qty;
            this.purchaseDate = purchaseDate;
        }
    }
}


