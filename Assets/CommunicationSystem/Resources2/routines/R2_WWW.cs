using System;
using System.Collections;

using UnityEngine;
using WWW = vietlabs.WebRequest.WWW;

public class R2_WWW<T> : R2_Loader<T, R2_WWW<T>>
{
	string url;
	WWW www;

	public float timeout = 0;
	float stTime;
	
	public R2_WWW(string url, bool startRoutine = false)
	{
		this.url = url;
		//if (Resources2.LOG_ENABLE) 
		//Debug.Log("[WWW.Add] : " + url);
		if (startRoutine) StartRoutine();
	}
	
	public R2_WWW(WWW www)
	{
		if (www == null)
		{
			Debug.LogWarning("R2R_WWW : www should not be null!");
		}
		
		this.www = www;
		
		
		//if (Resources2.LOG_ENABLE) Debug.Log("R2_WWW : " + www.url);
		StartRoutine();
	}
	
	internal bool LoadDataT()
	{
		if (!string.IsNullOrEmpty(error)) 
		{
			Debug.LogWarning("Error loading dataT : " + error);
			return false;
		}

		if (!string.IsNullOrEmpty(www.error)) 
		{
			error = www.error + "\n" + www.url;
			Debug.LogWarning("Error loading dataT : " + error);
			return false;
		}
		
		//if (Resources2.LOG_ENABLE) 
		//Debug.Log("[WWW.Done "+www.size+" b] R2_WWW : " + www.url);
		
		var typeT = typeof(T);
		
		if (typeT == typeof(string))
		{
			dataT = (T) (object)www.text;
			return true;
		}
		
		if (typeT == typeof(AudioClip))
		{
			#if UNITY_5_5_OR_NEWER
			dataT = (T) (object)www.GetAudioClip();
			#else
			dataT = (T) (object)www.audioClip;
			#endif
			return true;
		}
		
		if (typeT == typeof(byte[]))
		{
			dataT = (T) (object)www.bytes;
			return true;
		}
		
		if (typeT == typeof(Texture2D))
		{
			dataT = (T) (object)www.texture;
			return true;
		}
		
		// load assetbundle the old way
		if (typeT == typeof(AssetBundle))
		{
			dataT = (T) (object)www.assetBundle;
			return true;
		}
	
		error = "Unsupport loading files of type <" + dataT + ">";
		#if UNITY_EDITOR
		Debug.LogError(error);
	#endif
		return false;
	}
	
	protected override void Start()
	{
		if (www == null) www = new WWW(url, WWW.GetRequestType(typeof(T)));
		stTime = Time.realtimeSinceStartup;
		
		base.Start();
	}

	
	protected override void End()
	{
		LoadDataT();
		
		base.End(); //trigger events
		www = null; //clean up
	}
	
	internal override void Update()
	{	
		if (www.isDone) 
		{
			// if (timeout > 0)
			// {
			// 	Debug.Log("Loaded " + url + ": " + (Time.realtimeSinceStartup - stTime));
			// }
			
			ReturnCounter(); // don't hold counter because the actual load process is completed
			EndRoutine();
			return;
		}
		
		if (onProgress != null) onProgress(www.progress);
		if (timeout <= 0) return;
		if (Time.realtimeSinceStartup - stTime < timeout) return;
		
		error = "Timeout loading \n" + url;
		www.Dispose();
		
		ReturnCounter();
		EndRoutine();
		return;
	}
}