#if UNITY_5_3_OR_NEWER
    #define UNITY_4_3_OR_NEWER
    #define UNITY_4_4_OR_NEWER
    #define UNITY_4_5_OR_NEWER
    #define UNITY_4_6_OR_NEWER
    #define UNITY_4_7_OR_NEWER
    #define UNITY_5_0_OR_NEWER
	#define UNITY_5_1_OR_NEWER
	#define UNITY_5_2_OR_NEWER
#else
    #if UNITY_5
	    #define UNITY_4_3_OR_NEWER
        #define UNITY_4_4_OR_NEWER
        #define UNITY_4_5_OR_NEWER
        #define UNITY_4_6_OR_NEWER
        #define UNITY_4_7_OR_NEWER
	
        #if UNITY_5_0 
            #define UNITY_5_0_OR_NEWER
	    #elif UNITY_5_1
		    #define UNITY_5_0_OR_NEWER
		    #define UNITY_5_1_OR_NEWER
	    #elif UNITY_5_2
		    #define UNITY_5_0_OR_NEWER
		    #define UNITY_5_1_OR_NEWER
		    #define UNITY_5_2_OR_NEWER
	    #endif
    #else
        #if UNITY_4_3
            #define UNITY_4_3_OR_NEWER
        #elif UNITY_4_4
            #define UNITY_4_3_OR_NEWER
            #define UNITY_4_4_OR_NEWER
        #elif UNITY_4_5    
		    #define UNITY_4_3_OR_NEWER
            #define UNITY_4_4_OR_NEWER
            #define UNITY_4_5_OR_NEWER
        #elif UNITY_4_6
		    #define UNITY_4_3_OR_NEWER
            #define UNITY_4_4_OR_NEWER
            #define UNITY_4_5_OR_NEWER
            #define UNITY_4_6_OR_NEWER
        #elif UNITY_4_7
		    #define UNITY_4_3_OR_NEWER
            #define UNITY_4_4_OR_NEWER
            #define UNITY_4_5_OR_NEWER
            #define UNITY_4_6_OR_NEWER
            #define UNITY_4_7_OR_NEWER
        #endif
    #endif
#endif

using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif
using WWW = vietlabs.WebRequest.WWW;
using System;
using System.Collections;
using System.Reflection;

using Object = UnityEngine.Object;

public class R2_Unity {
	#if UNITY_EDITOR
	public static T LoadAssetAtPath<T>(string path) where T : Object {
		#if UNITY_5_1_OR_NEWER
			return AssetDatabase.LoadAssetAtPath<T>(path);
		#else
		return (T)AssetDatabase.LoadAssetAtPath(path, typeof(T));
		#endif
	}
	public static string[] Selection_AssetGUIDs {
		get {
			var mInfo = typeof(Selection).GetProperty("assetGUIDs", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Static);
			if (mInfo != null){
				return (string[]) mInfo.GetValue(null, null);
			}
			
			Debug.LogWarning("Unity changed ! Selection.assetGUIDs not found !");
			return new string[0];
		}
	}
	
	
	public static void UnloadUnusedAssets()
	{
		#if UNITY_5_0_OR_NEWER
        	EditorUtility.UnloadUnusedAssetsImmediate();
		#else
			EditorUtility.UnloadUnusedAssets();
		#endif
		Resources.UnloadUnusedAssets();
	}
	#endif
	
	static string _platformName;
	static public string PlatformName
	{
		get { 
			if (!string.IsNullOrEmpty(_platformName)) return _platformName;
			_platformName = 
#if UNITY_EDITOR
				GetPlatformForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
#else
		GetPlatformForAssetBundles(Application.platform);
#endif
			return _platformName;
		}
	}
	
	// --------------------------------- STATIC UTILS ---------------------------------
	
#if UNITY_EDITOR
	static string GetPlatformForAssetBundles(BuildTarget target)
	{
		switch (target)
		{
		case BuildTarget.Android:
			return "Android";

	#if UNITY_5_0_OR_NEWER
		case BuildTarget.iOS:
	#else
		case BuildTarget.iPhone:
	#endif
			return "iOS";

	#if UNITY_5
		case BuildTarget.WebGL:
			return "WebGL";
	#endif
			
	#if UNITY_5
		case BuildTarget.WebPlayer:
			return "WebPlayer";
	#endif

		case BuildTarget.StandaloneWindows:
		case BuildTarget.StandaloneWindows64:
			return "Windows";
		case BuildTarget.StandaloneOSXIntel:
		case BuildTarget.StandaloneOSXIntel64:

	#if UNITY_5
		case BuildTarget.StandaloneOSXUniversal:
	#endif
			return "OSX";
            // Add more build targets for your own.
            // If you add more targets, don't forget to add the same platforms to GetPlatformForAssetBundles(RuntimePlatform) function.
		default:
			return null;
		}
	}
#endif
	static string GetPlatformForAssetBundles(RuntimePlatform platform)
	{
		switch (platform)
		{
		case RuntimePlatform.Android:
			return "Android";

		case RuntimePlatform.IPhonePlayer:
			return "iOS";

		case RuntimePlatform.WebGLPlayer:
			return "WebGL";

	#if UNITY_5

		case RuntimePlatform.OSXWebPlayer:
		case RuntimePlatform.WindowsWebPlayer:
			return "WebPlayer";
	#endif

		case RuntimePlatform.WindowsPlayer:
			return "Windows";
			
		case RuntimePlatform.OSXPlayer:
			return "OSX";
            // Add more build targets for your own.
            // If you add more targets, don't forget to add the same platforms to GetPlatformForAssetBundles(RuntimePlatform) function.
		default:
			return null;
		}
	}
}
