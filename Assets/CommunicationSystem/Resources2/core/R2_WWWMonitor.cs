using UnityEngine;
using System.Collections.Generic;
using System;
using System.Linq;
using WWW = vietlabs.WebRequest.WWW;
public class R2_WWWMonitor : MonoBehaviour
{
	/*
    private static bool dirty;
    private static List<WWWInfo> list =new List<WWWInfo>();
    static Dictionary<WWW, WWWInfo> map;
    private static R2_WWWMonitor _api;

    public static void Add(WWW www)
    {
        if (map == null)
        {
            map = new Dictionary<WWW, WWWInfo>();
        } else if (map.ContainsKey(www)) return;

        map.Add(www, new WWWInfo(www));
        dirty = true;
    }

    [Range(0.1f, 1f)]public float interval = 0.5f;

    public bool showDownloaded;

    private float stTime;
    private int bytes;
    private float speed;

    private int downloading;
    private int downloaded;
    private int downloadError;

    private Vector2 scroll;

    void Awake()
    {
        if (_api != null && _api != this)
        {
            Destroy(this);
            return;
        }

        _api = this;
        DontDestroyOnLoad(this);
    }

    void OnGUI()
    {
        if (dirty)
        {
            dirty = false;
            list = map.Values.ToList();
            list.Sort((i1, i2) => i1.stTime.CompareTo(i2.stTime));
        }

        var cTime = Time.realtimeSinceStartup;
        var dTime = cTime - stTime;
        if (stTime <= 0 || (dTime > interval))
        {
            stTime = cTime;
            speed = bytes/dTime;
            bytes = 0;
        }

        GUILayout.BeginHorizontal();
        {
            GUILayout.Box("Speed : " + speed);

            if (GUILayout.Button("Downloaded : " + downloaded))
            {
                showDownloaded = !showDownloaded;
            }

            GUILayout.Box("Downloading : " + downloading + ":" + Resources2.Api.loadingList.Count);
            GUILayout.Box("Error : " + downloadError);
            GUILayout.Box("MapCount : " + Resources2.map.Count + ":" + (Resources2.map.Count-downloading));

            if (GUILayout.Button("Clean Cache"))
            {
                Caching.CleanCache();
            }
        }
        GUILayout.EndHorizontal();
        
        downloading = 0;
        downloaded = 0;
        downloadError = 0;

        scroll = GUILayout.BeginScrollView(scroll);
        for (var i = 0; i < list.Count; i++)
        {
            var l = list[i];
            var willDraw = (showDownloaded || l.status != WWWStatus.Completed);
            if (willDraw) bytes += l.Draw();

            switch (l.status)
            {
                case WWWStatus.Completed    : downloaded++; break;
                case WWWStatus.Downloading  : downloading++; break;
                case WWWStatus.Error        : downloadError++; break;
            }
        }
        GUILayout.EndScrollView();
    }
	*/
}

enum WWWStatus
{
    Downloading,
    Completed,
    Error
}

class WWWInfo
{
    public WWW www;
    public string url;
    public WWWStatus status;
    public float progress;
    public float stTime;
    public float duration;

    public WWWInfo(WWW www)
    {
        this.www = www;

        progress = 0;
        url = www.url;
        stTime = Time.realtimeSinceStartup;
        status = WWWStatus.Downloading;
    }

    public int Draw()
    {
        if (status == WWWStatus.Downloading)
        {
            try
            {
                progress = www.progress;
                if (www.isDone)
                {
                    duration = Time.realtimeSinceStartup - stTime;
                    status = string.IsNullOrEmpty(www.error) ? WWWStatus.Completed : WWWStatus.Error;
                }
            } catch(Exception e)
            {
                status = WWWStatus.Error;
                Debug.LogWarning(e);
            }
        }

        GUILayout.BeginHorizontal();
        {
            if (status == WWWStatus.Downloading)
            {
                GUILayout.Box(Mathf.Round(progress*100) + " %", GUILayout.Width(70f));
            }
            else
            {
                GUILayout.Label(status.ToString(), GUILayout.Width(70f));
            }
            GUILayout.Box(Mathf.Round(duration*100)/100f + " s", GUILayout.Width(50f));
            GUILayout.Box(url);
        }
        GUILayout.EndHorizontal();

        return 0;
    }
}