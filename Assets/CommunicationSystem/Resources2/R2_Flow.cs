//using System.Collections;
//using System.Collections.Generic;
//using System.Linq;
//using UnityEngine;

//using StateMachine = R2_StateMachine<R2_Flow.R2_LdAction, R2_Flow.R2_LdEvent>;

//public class R2_Flow
//{
//	public enum R2_LdAction
//	{
//		Load_Web,
//		Load_Cache,
//		Load_Resources,
//		Load_StreamingAssets,
		
//		RetryOrAlternate,
		
//		Check_Cache,
//		Write_Cache,
		
//		Hot_Reload,
		
//		Trigger_Error,
//		Trigger_Complete,
//		End
//	}
	
//	public enum R2_LdEvent
//	{
//		Any,
		
//		// Loader events
//		TimeoutError,
//		NetworkError,
//		LoadSuccess,
		
//		// Write events
//		CacheWriteError,
//		CacheWriteSuccess,
		
//		//
//		CacheValid,
//		CacheExpire,
//		CacheNotFound,
//		CacheDiskError,		// disk errors
		
//		CacheLoadSuccess,	
//		CacheLoadFail,		// disk errors?
		
//		// Write events
//		RetryStart,
//		RetryCountMaxed,
//	}
	
//	public StateMachine sm;
	
//	void HandleStateChange(R2_LdAction action)
//	{
		
//	}
	
//	public StateMachine ForceWeb()
//	{
//		// Always load from web, do not write / read cache
		
//		var result = new StateMachine(HandleStateChange);
		
//		result.AddState(R2_LdAction.Load_Web)
//			.SupportEvent(R2_LdEvent.LoadSuccess, 	R2_LdAction.Trigger_Complete)
//			.OtherEvents(R2_LdAction.RetryOrAlternate); //R2_LdEvent.TimeoutError, R2_LdEvent.NetworkError
		
//		result.AddState 
//		(	R2_LdAction.RetryOrAlternate, 
			
//			R2_LdEvent.RetryStart,				R2_LdAction.Load_Web,
//			R2_LdEvent.RetryCountMaxed, 		R2_LdAction.Trigger_Error //end
//		);
		
//		return result.Start(R2_LdAction.Load_Web);
//	}
	
//	public StateMachine PreferWeb()
//	{
//		// Try to load from web, if timeout (1-3 secs) or error fallback to LocalCache --> Embed
		
//		var result = new StateMachine(HandleStateChange);
		
//		result.AddState(R2_LdAction.Check_Cache)
//			.SetCustomEventHandler((R2_LdEvent e) => R2_LdAction.Load_Web);
			
//		result.AddState ( R2_LdAction.Load_Web, 
//			R2_LdEvent.LoadSuccess, 				R2_LdAction.Write_Cache, //end
//			new []{ 
//				R2_LdEvent.TimeoutError,
//				R2_LdEvent.NetworkError	},			R2_LdAction.RetryOrAlternate
//		);
		
//		result.AddState ( R2_LdAction.RetryOrAlternate, 
			
//			R2_LdEvent.RetryStart,					R2_LdAction.Load_Web,
//			R2_LdEvent.RetryCountMaxed, 			R2_LdAction.Trigger_Error //end
//		);
		
//		return result;
//	}
	
//	//public enum R2_UpdateMode
//	//{
//	//	NeverUpdate,	// Do not update - use embed version only
//	//	ForceWeb,		
//	//	PreferWeb,		
//	//	HotReload,		// Use local cache if any, load from web, write cache and trigger hot reload if sucess
//	//	DelayWeb,		// cc, load from web, write cache to be used next time
//	//	PreferLocal,	// Use local cache until expires
//	//	Custom,			// Custom update mode
//	//}
	
//	//public class R2_FlowHistory
//	//{
//	//	public R2_LdAction RAction;
//	//	public R2_LdEvent REvent;
//	//}
	
//	//public class R2_FState
//	//{
//	//	public string id;
//	//	public R2_LdAction[] actions; //actions to take in this state
//	//	public Dictionary<R2_LdEvent, R2_FState> map = new Dictionary<R2_LdEvent, R2_FState>();
		
//	//	public R2_FState Next(R2_LdEvent evt)
//	//	{
//	//		if (map.Count == 0) return null;
//	//		if (map.Count == 1) return map.Values.ToList()[0];
			
//	//		R2_FState result;
//	//		if (map.TryGetValue(evt, out result)) return result;
			
//	//		Debug.LogWarning("Transition not found! State=" + id + " event=" + evt);
//	//		return null;
//	//	}
//	//}
	
//	//Dictionary<string, R2_FState> stateMap;
//	//internal R2_FState GetState(string stateId, R2_LdAction[] actions = null)
//	//{
//	//	if (stateMap == null) stateMap = new Dictionary<string, R2_FState>();
		
//	//	R2_FState state;
		
//	//	if (!stateMap.TryGetValue(stateId, out state))
//	//	{
//	//		state = new R2_FState() { id = stateId };
//	//		stateMap.Add(stateId, state);
//	//	}
		
//	//	if (actions != null)
//	//	{
//	//		if (state.actions != null)
//	//		{
//	//			Debug.LogWarning("State actions redefinition found <" + stateId + ">");
//	//		}
//	//		state.actions = actions;
//	//	}
		
//	//	return state;
//	//}
	
//	//internal void AddState(string stateId, R2_LdAction[] actions, params object[] eventStatePair)
//	//{
//	//	var from = GetState(stateId, actions);
		
//	//	if (eventStatePair.Length == 1)
//	//	{
//	//		//single default transition
//	//		var next	= eventStatePair[0];
//	//		R2_FState nextState = null;
			
//	//		if (next is string)
//	//		{
//	//			nextState = (R2_FState)GetState((string)next);
//	//		}
			
//	//		if (next is R2_LdAction)
//	//		{
//	//			var nextAction = (R2_LdAction)next;
//	//			nextState = (R2_FState)GetState(nextAction.ToString());
//	//			if (nextState.actions == null)
//	//			{
//	//				nextState.actions = new R2_LdAction[]{ nextAction };	
//	//			}
//	//		}
			
//	//		if (nextState == null)
//	//		{
//	//			Debug.LogWarning("Unsupported type of nextState (must be stateId (string) or R2_LdAction ): " + next);
//	//			return;
//	//		}
			
//	//		from.map.Add(R2_LdEvent.Any, nextState);
//	//	}
		
//	//	for (var i = 0;i < eventStatePair.Length; i+=2)
//	//	{
//	//		var evt		= (R2_LdEvent)eventStatePair[i];
//	//		var next	= eventStatePair[i+1];
			
//	//		if (next is string)
//	//		{
//	//			var nextState = (R2_FState)GetState((string)next);
//	//			from.map.Add(evt, nextState);	
//	//			continue;
//	//		} 
			
//	//		if (next is R2_LdAction) //simple leaf state
//	//		{
//	//			var nextAction = (R2_LdAction)next;
//	//			var nextState = (R2_FState)GetState(nextAction.ToString());
//	//			if (nextState.actions == null)
//	//			{
//	//				nextState.actions = new R2_LdAction[]{ nextAction };	
//	//			}
//	//			from.map.Add(evt, nextState);
//	//		}
			
//	//		Debug.LogWarning("Unsupported type of nextState (must be stateId (string) or R2_LdAction ): " + next);
//	//	}
//	//}
	
//	//public static R2_Flow ForceWeb
//	//{
//	//	get
//	//	{
//	//		var result = new R2_Flow();
			
//	//		result.AddState(
//	//			"LoadWeb", new[] { R2_LdAction.Load_Web },
				
//	//			R2_LdEvent.LoadSuccess, 	R2_LdAction.Trigger_Complete,
//	//			R2_LdEvent.NetworkError,	"Retry",
//	//			R2_LdEvent.TimeoutError,	"LoadLocal"
//	//		);
			
//	//		result.AddState(
//	//			"Retry", new []{ R2_LdAction.RetryOrAlternate },
				
//	//			R2_LdEvent.RetryStart,		"LoadWeb",
//	//			R2_LdEvent.RetryCountMaxed,  R2_LdAction.Trigger_Error
//	//		);
			
//	//		return result;
//	//	}
//	//}
	
//	//public static R2_Flow PreferWeb
//	//{
//	//	get
//	//	{
//	//		var result = new R2_Flow();
			
//	//		//result.AddState
//	//		//(
//	//		//	"CheckCache", new[] { R2_LdAction.Check_Cache },
//	//		//	"LoadWeb"
//	//		//);
			
//	//		//result.AddState(
//	//		//	"LoadWeb", new[] { R2_LdAction.Load_Web },
				
//	//		//	R2_LdEvent.LoadSuccess, 	R2_LdAction.Trigger_Complete,
//	//		//	R2_LdEvent.NetworkError,	"Retry",
//	//		//	R2_LdEvent.TimeoutError,	//depends on hasCache or not
//	//		//);
			
//	//		//result.AddState(
//	//		//	"Retry", new []{ R2_LdAction.RetryOrAlternate },
				
//	//		//	R2_LdEvent.RetryStart,		"LoadWeb",
//	//		//	R2_LdEvent.RetryCountMaxed,  R2_LdAction.Trigger_Error
//	//		//);
//	//		return result;
//	//	}
//	//}
	
	
	
//}
