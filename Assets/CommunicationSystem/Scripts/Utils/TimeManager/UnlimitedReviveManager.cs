using System;
using System.Collections;
using PianoIdol;
using UnityEngine;

/// <summary>
/// Revive không giới hạn số lần và ad-free
/// </summary>
public class UnlimitedReviveManager : SingletonMono<UnlimitedReviveManager> {
    #region Unlimited Revive

    private const  string   UNLIMITED_REVIVE_REMAIN = "Unlimited_Revive_RemainTime";
    private static string   _unlimitedReviveRemainningTimeString;
    private static TimeSpan _lastRemainingUnlimitedReviveTime  = TimeSpan.Zero;
    private static float    _lastSetUnlimitedRevive            = 0f;
    private static bool     _isLoadedUnlimitedReviveRemainTime = false;

    /// <summary>
    /// còn thời gian free unlimited revive hay không?
    /// </summary>
    public static bool IsInUnlimitedRevive =>
        PlayerPrefs.HasKey(UNLIMITED_REVIVE_REMAIN) && CurrentUnlimitedReviveRemainingTime.CompareTo(TimeSpan.Zero) > 0;

    public static event Action ChangeState;

    #region Unity Method

    private IEnumerator Start() {
        yield return null; //delay 1 frame

        TimeManager.saveDataOnPauseQuit += SaveData;
    }

    private void OnDestroy() {
        TimeManager.saveDataOnPauseQuit -= SaveData;
    }

    #endregion

    private static void GetLastSavedRemainUnlimitReviveTime() {
        _isLoadedUnlimitedReviveRemainTime = true;
        _unlimitedReviveRemainningTimeString = PlayerPrefs.GetString(UNLIMITED_REVIVE_REMAIN);
        if (!TimeSpan.TryParse(_unlimitedReviveRemainningTimeString, out _lastRemainingUnlimitedReviveTime)) {
            _lastRemainingUnlimitedReviveTime = TimeSpan.Zero;
        }
    }

    public static void IncreaseUnlimitedReviveTime(float minutes) {
        _lastRemainingUnlimitedReviveTime = CurrentUnlimitedReviveRemainingTime + TimeSpan.FromMinutes(minutes);
        _lastSetUnlimitedRevive = TimeManager.TimeIngameIgnorePause;
        SaveUnlimitedReviveRemainTime();

        var topBarHome = HomeManager.instance?.topBarHome;
        if (topBarHome) {
            topBarHome.ShowUnlimitedReviveCountdown();
        }

        ChangeState?.Invoke();
        Logger.EditorLog($"Add Unlimited Revive Time: {minutes} minutes");
    }

    public static TimeSpan CurrentUnlimitedReviveRemainingTime {
        get {
            if (!_isLoadedUnlimitedReviveRemainTime) {
                GetLastSavedRemainUnlimitReviveTime();
            }

            TimeSpan remain = _lastRemainingUnlimitedReviveTime -
                              TimeSpan.FromSeconds(TimeManager.TimeIngameIgnorePause - _lastSetUnlimitedRevive);
            return remain.CompareTo(TimeSpan.Zero) < 0 ? TimeSpan.Zero : remain;
        }
    }

    public void SaveData() {
        if (!PlayerPrefs.HasKey(UNLIMITED_REVIVE_REMAIN)) {
            return;
        }

        SaveUnlimitedReviveRemainTime();
    }

    private static void SaveUnlimitedReviveRemainTime() {
        var remainingUnlimitedReviveTime = CurrentUnlimitedReviveRemainingTime;
        if (remainingUnlimitedReviveTime.CompareTo(TimeSpan.Zero) <= 0) {
            PlayerPrefs.DeleteKey(UNLIMITED_REVIVE_REMAIN);
            return;
        }

        PlayerPrefs.SetString(UNLIMITED_REVIVE_REMAIN, remainingUnlimitedReviveTime.ToString());
    }

    #endregion
}