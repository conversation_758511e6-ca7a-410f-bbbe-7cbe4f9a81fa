
using UnityEngine;
using Amanotes.ActionSystem;


public class NotificationDeepLink : MonoBehaviour
{
    public static bool IsInGame { private set; get; }

    public static bool OpenFromDeepLink = false;
    public static IAction deepLinkAction;

    private static string notificationID = string.Empty;

    public static void Activate()
    {
        IsInGame = true;
    }

    public static bool CheckStartupScene()
    {
        if (deepLinkAction != null) {
            if (deepLinkAction is ANavigate aNavigate) {
                if (aNavigate.CheckParam())
                {
                    RunAndClearDeeplinkAction();
                    OpenFromDeepLink = true;
                    return true;
                }
            }else if (deepLinkAction is AOpenPopup aOpenPopup) {
                if (aOpenPopup.CheckParam())
                {
                    RunAndClearDeeplinkAction();
                    OpenFromDeepLink = true;
                    return true;
                }
            } else {
                RunAndClearDeeplinkAction();
                OpenFromDeepLink = true;
                return true;
            }   
        }

        return false;
    }

    public static void ProcessNotificationJsonData(string json, bool isAlreadyOpenedApp = false, string actionFrom = "")
    {
        if (HasDeepLinkAction()) return;

        try
        {
            AmaNotificationData data = JsonUtility.FromJson<AmaNotificationData>(json);
            if (data != null)
            {
                ProcessNotificationData(data, isAlreadyOpenedApp, actionFrom);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError("[DeepLink] ProcessNotificationData error: " + json + " ---> " + ex.Message);
        }
    }

    public static void ProcessNotificationData(AmaNotificationData data, bool isAlreadyOpenedApp = false, string actionFrom = "")
    {
        if (data == null) return;
        if (HasDeepLinkAction()) return;

        if (!string.IsNullOrEmpty(data.action_data))
        {
            HandleActionSystemData(data, actionFrom);
            return;
        }
        else
        {
            CommunicationEventTracking.LogNotificationOpen(data.notification_id, "home");
        }
    }

    private static void HandleActionSystemData(AmaNotificationData data, string actionFrom)
    {
        IAction action = ActionSystem.GenerateAction(data.action_data, actionFrom);
        if (action != null)
        {
            if (IsInGame)
            {
                action.Run(
                    onSuccess: () =>
                    {
                        CommunicationEventTracking.LogNotificationOpen(data.notification_id, action.GetDescription());
                    },
                    onFail: () =>
                    {
                        CommunicationEventTracking.LogNotificationOpen(data.notification_id, "action_fail");
                    });
            }
            else
            {
                deepLinkAction = action;
                notificationID = data.notification_id;
            }
            HandleConflictWithIgPopup();
        }
    }

    private static void HandleConflictWithIgPopup()
    {
        if (!IngamePopupSystem.HasShownThisSession)
        {
            IngamePopupSystem.IsReserved = true;
        }
    }

    public static void HandleDeeplink()
    {
        if (HasDeepLinkAction())
        {
            RunAndClearDeeplinkAction();
        }
    }

    public static bool CheckPlaySong()
    {
        if (deepLinkAction != null)
        {
            bool isDeeplinkSong = false;
            if (deepLinkAction is APlaySong aPlaySong)
            {
                if (aPlaySong.IsParamValid())
                {
                    isDeeplinkSong = true;
                }
                else
                {
                    deepLinkAction = null;
                    isDeeplinkSong = false;
                }
            }
            else if (deepLinkAction is ARewardNextSong)
            {
                isDeeplinkSong = true;
            }
            
            if (isDeeplinkSong)
            {
                RunAndClearDeeplinkAction();
                return true;
            }

            return false;
        }

        return false;
    }


    public static void RunAndClearDeeplinkAction()
    {
        if (deepLinkAction != null)
        {
            IAction temp = deepLinkAction;
            deepLinkAction = null;
            temp.Run(
                onSuccess: () =>
                {
                    CommunicationEventTracking.LogNotificationOpen(notificationID, temp.GetDescription());
                },
                onFail: () =>
                {
                    CommunicationEventTracking.LogNotificationOpen(notificationID, "action_fail");
                });
            if ((temp is APlaySong aPlaySong && aPlaySong.IsParamValid()) || temp is ARewardNextSong)
            {
                //PopupSessionController.haveSeenMainSonglist = true;
            }
        }
    }

    public static bool HasDeepLinkAction()
    {
        return deepLinkAction != null;
    }
}
