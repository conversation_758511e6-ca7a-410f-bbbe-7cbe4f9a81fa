using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

namespace Amanotes.ActionSystem
{

    [Serializable]
    public class ActionSystemData<T> where T : ActionData
    {
        public string type;
        public T param;
    }

    public static class ActionSystem
    {
        public static int actionCount = 0;

        [Serializable]
        class TypeDataOnly
        {
            public string type;
        }

        public static void ExecuteAction(string json, Action onSuccess = null, Action onFail = null)
        {
            Debug.Log("[AS] execute json:\n" + json);
            IAction action = GenerateAction(json);
            if (action != null) action.Run(onSuccess, onFail);
            else onFail?.Invoke();
        }

        public static IAction GenerateAction(string json, string placement = "")
        {
            TypeDataOnly typeData = null;
            try
            {
                typeData = JsonUtility.FromJson<TypeDataOnly>(json);
            }
            catch (Exception e)
            {
                Debug.LogError("[AS] cant parse action json e:\n" + e.Message); 
            }

            if (typeData == null || string.IsNullOrEmpty(typeData.type)) return null;

            if (Enum.TryParse(typeData.type, true, out ActionType actionType))
            {
                IAction action = GetAction(actionType);
                if (action != null)
                {
                    try
                    {
                        action.InitData(json);
                        action.SetPlacement(placement);
                        
                        ActionSystemLogs.LogGenerateAction(json, placement, action);
                    }
                    catch (Exception e)
                    {
                        Debug.LogError("[AS] cant init action data e:\n" + e.Message);
                        return null;
                    }

                }
                return action;
            }

            Debug.LogError("[AS] json not valid:\n" + json);
            return null;
        }

        private static IAction GetAction(ActionType actionType)
        {
            switch (actionType)
            {
                case ActionType.PlaySong:
                    return new APlaySong();
                case ActionType.Navigate:
                    return new ANavigate();
                case ActionType.OpenStore:
                    return new AOpenStore();
                case ActionType.OpenUrl:
                    return new AOpenURL();
                case ActionType.OpenVipSub:
                    return new AOpenVipSub();
                case ActionType.RewardNextSong:
                    return new ARewardNextSong();
                case ActionType.PlaySongAndUnlock:
                    return new APlaySongAndUnlock();
                case ActionType.Popup:
                    return new AOpenPopup();
            }

            Debug.LogError("[AS] Cant get action for type: " + actionType);
            return null;
        }
    }

    public enum ActionType
    {
        Navigate = 0,
        PlaySong,
        Reward,
        Discovery,
        OpenStore,
        OpenUrl,
        OpenVipSub,
        RewardNextSong,
        PlaySongAndUnlock,
        Popup,
    }
}