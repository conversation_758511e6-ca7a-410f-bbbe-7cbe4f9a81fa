using System;
using AmaSQLite;
using UnityEngine;

namespace Amanotes.Push {
    public class NotificationManager : Singleton<NotificationManager> {
        private NotificationBase notificationBase = null;

        public NotificationBase GetNotification() {
            if (notificationBase == null) {
#if UNITY_ANDROID
                notificationBase = new NotificationForAndroid();
#else
				notificationBase = new NotificationForIOS();
#endif
            }

            return notificationBase;
        }

        public void InitialLocalNotification() {
            GetNotification().InitialLocalNotification();
        }

        public void CheckFirstTimeComeFrom() {
            AmanotesPushAction.instance.CheckFirstTimeAppLauchFrom();
        }

        public void RegisterNotification(AmaNotificationData data) {
            if (DateTime.Compare(data.fireTime.DateTime, DateTime.Now) <= 0) {
                if (NotificationLog.isEnabled) {
                    Debug.LogWarning(
                        $"[RegisterNotification] Notification failed {data.title} fireTime {data.fireTime.DateTime} vs now {DateTime.Now}");
                }

                return;
            }

            GetNotification().RegisterNotification(data);
            StoreToDB(data);
        }

        private void StoreToDB(AmaNotificationData data) {
            if (string.IsNullOrWhiteSpace(data.notification_id)) {
                return;
            }

            long expectedShowTimeUnix = data.fireTime.ToUnixTimeMilliseconds();
            long scheduleTimeUnix = DateTimeOffset.Now.ToUnixTimeMilliseconds();
            if (scheduleTimeUnix > expectedShowTimeUnix) {
                return;
            }

            LocalNotificationEntity localNotificationEntity = new LocalNotificationEntity(data.notification_id, data.id,
                expectedShowTimeUnix, scheduleTimeUnix, 0, 0);
            SQLiteTableLocalNotification.InsertOrUpdateIfExist(localNotificationEntity);
        }

        public void CancelNotification(int notificationId) {
            GetNotification().CancelNotification(notificationId);
        }

        public void SetActiveLog(bool isLog) {
            GetNotification().SetActiveLog(isLog);
        }

        public void ClearAllShowingNotification() {
            GetNotification().ClearAllShowingNotification();
        }

        public string CheckFirstTimeAppLaunch() {
            return GetNotification().CheckFirstTimeAppLaunch();
        }
    }
}