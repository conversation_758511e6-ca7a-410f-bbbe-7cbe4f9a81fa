// Made with Amplify Shader Editor v1.9.1.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "InwaveEffect/StandardRimp"
{
	Properties
	{
		_Color("Color", Color) = (1,1,1,0)
		[SingleLineTexture]_MainTexture("Main Texture", 2D) = "white" {}
		[HDR]_EmissionColor("Emission Color", Color) = (0.3773585,0.3773585,0.3773585,0)
		[SingleLineTexture]_EmissionTexture("Emission Texture", 2D) = "white" {}
		_RimplightColor("Rimplight Color", Color) = (0.3773585,0.3773585,0.3773585,1)
		_RimpPower("RimpPower", Float) = 1
		_RimpThreshole("RimpThreshole", Float) = 1
		[HideInInspector] _texcoord( "", 2D ) = "white" {}
		[HideInInspector] __dirty( "", Int ) = 1
	}

	SubShader
	{
		Tags{ "RenderType" = "Custom"  "Queue" = "Transparent+0" "IgnoreProjector" = "True" "IsEmissive" = "true"  }
		Cull Off
		ZWrite On
		Blend SrcAlpha OneMinusSrcAlpha
		
		CGPROGRAM
		#pragma target 3.0
		#pragma surface surf Unlit keepalpha noshadow 
		struct Input
		{
			float2 uv_texcoord;
			float3 worldPos;
			float3 worldNormal;
		};

		uniform float4 _EmissionColor;
		uniform sampler2D _EmissionTexture;
		uniform float4 _EmissionTexture_ST;
		uniform float4 _Color;
		uniform sampler2D _MainTexture;
		uniform float4 _MainTexture_ST;
		uniform float4 _RimplightColor;
		uniform float _RimpPower;
		uniform float _RimpThreshole;

		inline half4 LightingUnlit( SurfaceOutput s, half3 lightDir, half atten )
		{
			return half4 ( 0, 0, 0, s.Alpha );
		}

		void surf( Input i , inout SurfaceOutput o )
		{
			float2 uv_EmissionTexture = i.uv_texcoord * _EmissionTexture_ST.xy + _EmissionTexture_ST.zw;
			float4 tex2DNode2 = tex2D( _EmissionTexture, uv_EmissionTexture );
			float2 uv_MainTexture = i.uv_texcoord * _MainTexture_ST.xy + _MainTexture_ST.zw;
			float3 ase_worldPos = i.worldPos;
			float3 ase_worldViewDir = normalize( UnityWorldSpaceViewDir( ase_worldPos ) );
			float3 ase_worldNormal = i.worldNormal;
			float fresnelNdotV8 = dot( ase_worldNormal, ase_worldViewDir );
			float fresnelNode8 = ( 0.0 + _RimpPower * pow( 1.0 - fresnelNdotV8, _RimpThreshole ) );
			o.Emission = ( ( _EmissionColor * tex2DNode2 ) + ( _Color * tex2D( _MainTexture, uv_MainTexture ) ) + ( _RimplightColor * fresnelNode8 ) ).rgb;
			o.Alpha = tex2DNode2.a;
		}

		ENDCG
	}
	CustomEditor "ASEMaterialInspector"
}
/*ASEBEGIN
Version=19102
Node;AmplifyShaderEditor.FresnelNode;8;-694.045,646.498;Inherit;False;Standard;WorldNormal;ViewDir;False;False;5;0;FLOAT3;0,0,1;False;4;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;5;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;9;-678.6358,458.1644;Inherit;False;Property;_RimplightColor;Rimplight Color;5;0;Create;True;0;0;0;False;0;False;0.3773585,0.3773585,0.3773585,1;1,0.2877358,0.8853827,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;12;-118.6091,57.33118;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;14;-1043.507,716.5923;Inherit;False;Property;_RimpThreshole;RimpThreshole;7;0;Create;True;0;0;0;False;0;False;1;1.05;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;13;-1060.507,590.5923;Inherit;False;Property;_RimpPower;RimpPower;6;0;Create;True;0;0;0;False;0;False;1;1.26;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;11;-367.6739,463.8218;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;6;-540.8976,154.217;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;4;-1017.803,-16.40704;Inherit;False;Property;_EmissionColor;Emission Color;3;1;[HDR];Create;True;0;0;0;False;0;False;0.3773585,0.3773585,0.3773585,0;0.5,0.5,0.5,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SamplerNode;2;-1054.895,163.3996;Inherit;True;Property;_EmissionTexture;Emission Texture;4;1;[SingleLineTexture];Create;True;0;0;0;False;0;False;-1;9d1ccfeec3910f34e8064fc59d5ad175;64ae63b1f423a7143801a8d70727fe54;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;5;-591.9202,-231.9121;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SamplerNode;1;-1016.097,-231.5986;Inherit;True;Property;_MainTexture;Main Texture;1;1;[SingleLineTexture];Create;True;0;0;0;False;0;False;-1;9d1ccfeec3910f34e8064fc59d5ad175;64ae63b1f423a7143801a8d70727fe54;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;3;-1007.086,-428.8485;Inherit;False;Property;_Color;Color;0;0;Create;True;0;0;0;False;0;False;1,1,1,0;0.6320754,0.6320754,0.6320754,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.StandardSurfaceOutputNode;0;121.2999,-17.7767;Float;False;True;-1;2;ASEMaterialInspector;0;0;Unlit;InwaveEffect/StandardRimp;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;False;False;False;False;False;False;Off;1;False;;0;False;;False;0;False;;0;False;;False;0;Custom;0.5;True;False;0;True;Custom;;Transparent;All;12;all;True;True;True;True;0;False;;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;2;15;10;25;False;0.5;False;2;5;False;;10;False;;0;0;False;;0;False;;0;False;;0;False;;0;False;0;0,0,0,0;VertexOffset;True;False;Cylindrical;False;True;Relative;0;;2;-1;-1;-1;0;False;0;0;False;;-1;0;False;;0;0;0;False;0.1;False;;0;False;;False;15;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT;0;False;9;FLOAT;0;False;10;FLOAT;0;False;13;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;12;FLOAT3;0,0,0;False;14;FLOAT4;0,0,0,0;False;15;FLOAT3;0,0,0;False;0
WireConnection;8;2;13;0
WireConnection;8;3;14;0
WireConnection;12;0;6;0
WireConnection;12;1;5;0
WireConnection;12;2;11;0
WireConnection;11;0;9;0
WireConnection;11;1;8;0
WireConnection;6;0;4;0
WireConnection;6;1;2;0
WireConnection;5;0;3;0
WireConnection;5;1;1;0
WireConnection;0;2;12;0
WireConnection;0;9;2;4
ASEEND*/
//CHKSM=4C67DCFE54842FE2D8DF074D0910739748B33E3B
