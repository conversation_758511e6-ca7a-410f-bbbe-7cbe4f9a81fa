// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

Shader "Custom/Mobile/Particles/AdditiveScroll"
{
    Properties
    {
        _TintColor("Tint Color", Color) = (0.5, 0.5, 0.5, 0.5)
        _MainTex("Particle Texture", 2D) = "white" {}
        _SpeedX ("SpeedX", float) = 0
        _SpeedY ("SpeedY", float) = 0
    }

    CGINCLUDE
    #include "UnityCG.cginc"

    sampler2D _MainTex;
    float4 _MainTex_ST;
    fixed4 _TintColor;
    float _SpeedX;
    float _SpeedY;

    struct appdata_t
    {
        float4 position : POSITION;
        float4 texcoord : TEXCOORD0;
        fixed4 color : COLOR;
    };

    struct v2f
    {
        float4 position : SV_POSITION;
        float2 texcoord : TEXCOORD0;
        fixed4 color : COLOR;
        UNITY_FOG_COORDS(1)
    };

    v2f vert(appdata_t v)
    {
        v2f o;
        o.position = UnityObjectToClipPos(v.position);
        o.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);
        o.color = v.color;
        UNITY_TRANSFER_FOG(o, o.vertex);
        return o;
    }

    fixed4 frag(v2f i) : SV_Target
    {
         float2 uv =  float2(i.texcoord.x + _SpeedX* _Time.y,i.texcoord.y+ _SpeedY * _Time.y);
       // fixed4 col = 2.0f * i.color * _TintColor * tex2D(_MainTex, i.texcoord);
        fixed4 col = 2.0f * i.color * _TintColor * tex2D(_MainTex, uv);
        UNITY_APPLY_FOG_COLOR(i.fogCoord, col, (fixed4)0);
        return col;
    }
    ENDCG

    SubShader
    {
        Tags
        {
            "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent"
        }

        Blend SrcAlpha One
        Cull Off Lighting Off ZWrite Off Fog
        {
            Mode Off
        }

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_particles
            #pragma multi_compile_fog
            ENDCG
        }
    }
}