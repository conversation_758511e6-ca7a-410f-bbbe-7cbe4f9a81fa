// Made with Amplify Shader Editor v1.9.1.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "InwaveEffect/TH-Unlit_ZWriteOff_Cull"
{
	Properties
	{
		[HDR]_ColorMark("Color Mark", Color) = (1,1,1,1)
		[HDR]_ColorTile1("Color Tile 1", Color) = (1,1,1,1)
		_ColorTile2("Color Tile 2", Color) = (1,1,1,1)
		_MainTexture("Main Texture", 2D) = "white" {}
		_OpacityTexture("Opacity Texture", 2D) = "white" {}
		_Opacity("Opacity", Range( 0 , 1)) = 1
		[Toggle(_RIMPLIGHT_ON)] _RimpLight("Rimp Light", Float) = 1
		_RimpColor("Rimp Color", Color) = (0.004716992,0.7491221,1,0)
		_RimpPower("Rimp Power", Float) = 1
		_RimpScale("Rimp Scale", Float) = 2
		[HideInInspector] _texcoord( "", 2D ) = "white" {}
		[HideInInspector] __dirty( "", Int ) = 1
	}

	SubShader
	{
		Tags{ "RenderType" = "Custom"  "Queue" = "Transparent+0" "IgnoreProjector" = "True" "IsEmissive" = "true"  }
		Cull Back
		ZWrite Off
		Blend SrcAlpha OneMinusSrcAlpha
		
		CGPROGRAM
		#pragma target 3.0
		#pragma shader_feature_local _RIMPLIGHT_ON
		#pragma surface surf Unlit keepalpha noshadow 
		struct Input
		{
			float2 uv_texcoord;
			float3 worldPos;
			float3 worldNormal;
		};

		uniform float4 _ColorMark;
		uniform sampler2D _MainTexture;
		uniform float4 _MainTexture_ST;
		uniform float4 _ColorTile1;
		uniform float4 _ColorTile2;
		uniform float4 _RimpColor;
		uniform float _RimpPower;
		uniform float _RimpScale;
		uniform sampler2D _OpacityTexture;
		uniform float4 _OpacityTexture_ST;
		uniform float _Opacity;

		inline half4 LightingUnlit( SurfaceOutput s, half3 lightDir, half atten )
		{
			return half4 ( 0, 0, 0, s.Alpha );
		}

		void surf( Input i , inout SurfaceOutput o )
		{
			float2 uv_MainTexture = i.uv_texcoord * _MainTexture_ST.xy + _MainTexture_ST.zw;
			float4 break30 = tex2D( _MainTexture, uv_MainTexture );
			float4 temp_output_31_0 = ( _ColorMark * break30.r );
			float4 temp_cast_0 = (0.0).xxxx;
			float3 ase_worldPos = i.worldPos;
			float3 ase_worldViewDir = normalize( UnityWorldSpaceViewDir( ase_worldPos ) );
			float3 ase_worldNormal = i.worldNormal;
			float fresnelNdotV84 = dot( ase_worldNormal, ase_worldViewDir );
			float fresnelNode84 = ( 0.0 + _RimpPower * pow( max( 1.0 - fresnelNdotV84 , 0.0001 ), _RimpScale ) );
			#ifdef _RIMPLIGHT_ON
				float4 staticSwitch90 = ( _RimpColor * fresnelNode84 );
			#else
				float4 staticSwitch90 = temp_cast_0;
			#endif
			o.Emission = ( ( temp_output_31_0 + ( _ColorTile1 * break30.g ) + ( _ColorTile2 * break30.b ) ) + staticSwitch90 ).rgb;
			float2 uv_OpacityTexture = i.uv_texcoord * _OpacityTexture_ST.xy + _OpacityTexture_ST.zw;
			o.Alpha = ( tex2D( _OpacityTexture, uv_OpacityTexture ).a * _Opacity );
		}

		ENDCG
	}
	CustomEditor "ASEMaterialInspector"
}
/*ASEBEGIN
Version=19102
Node;AmplifyShaderEditor.SamplerNode;22;-1672.736,-177.0271;Inherit;True;Property;_MainTexture;Main Texture;3;0;Create;True;0;0;0;False;0;False;-1;6fde1a5c8ee9abc48813d0ef2a9db912;e0f6e59d0a4fd224bad8149d1ceb03bf;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.BreakToComponentsNode;30;-1376.715,-165.2841;Inherit;False;COLOR;1;0;COLOR;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15
Node;AmplifyShaderEditor.SimpleAddOpNode;24;-331.1728,-552.635;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;37;-931.8996,-169.3557;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;31;-952.8977,-407.9592;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;39;-935.2366,53.41945;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;38;-1378.868,2.742305;Inherit;False;Property;_ColorTile1;Color Tile 1;1;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;0.2688679,0.7102035,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;32;-1408.705,-404.2958;Inherit;False;Property;_ColorMark;Color Mark;0;1;[HDR];Create;True;0;0;0;False;0;False;1,1,1,1;0.5188679,0.5188679,0.5188679,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;86;-748.4324,434.4756;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;42;-602.1771,-62.24525;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;85;-268.7332,87.37487;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.StaticSwitch;90;-485.8336,377.2754;Inherit;False;Property;_RimpLight;Rimp Light;7;0;Create;True;0;0;0;False;0;False;0;1;1;True;;Toggle;2;Key0;Key1;Create;True;True;All;9;1;COLOR;0,0,0,0;False;0;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;4;COLOR;0,0,0,0;False;5;COLOR;0,0,0,0;False;6;COLOR;0,0,0,0;False;7;COLOR;0,0,0,0;False;8;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;87;-1055.233,323.9749;Inherit;False;Property;_RimpColor;Rimp Color;8;0;Create;True;0;0;0;False;0;False;0.004716992,0.7491221,1,0;0.3529412,0.957868,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;89;-1325.633,662.2751;Inherit;False;Property;_RimpPower;Rimp Power;9;0;Create;True;0;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;92;-1312.633,741.5751;Inherit;False;Property;_RimpScale;Rimp Scale;10;0;Create;True;0;0;0;False;0;False;2;2;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.GrabScreenPosition;107;-1527.294,434.4725;Inherit;False;0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.FresnelNode;84;-1072.566,562.5108;Inherit;False;Standard;TangentNormal;ViewDir;False;True;5;0;FLOAT3;0,0,1;False;4;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;5;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;91;-665.2328,308.3749;Inherit;False;Constant;_Float0;Float 0;9;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;29;-1687.555,114.2066;Inherit;True;Property;_OpacityTexture;Opacity Texture;4;0;Create;True;0;0;0;False;0;False;-1;068fb4717db120049a3b2b229ce74140;3c2316bd473a184469022600eab9dd49;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;108;-1679.245,325.924;Inherit;False;Property;_Opacity;Opacity;5;0;Create;True;0;0;0;False;0;False;0;1;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;83;-1255.572,198.0791;Inherit;False;Property;_ColorTile2;Color Tile 2;2;0;Create;True;0;0;0;False;0;False;1,1,1,1;0.6,0.3345942,0.9716981,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;109;-1369.931,213.7376;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.StandardSurfaceOutputNode;70;0,0;Float;False;True;-1;2;ASEMaterialInspector;0;0;Unlit;InwaveEffect/TH-Unlit;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;False;False;False;False;False;False;Off;1;False;;0;False;;False;0;False;;0;False;;False;0;Custom;0.5;True;False;0;True;Custom;;Transparent;All;12;all;True;True;True;True;0;False;;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;2;15;10;25;False;0.5;False;2;5;False;;10;False;;0;4;False;;1;False;;0;False;;4;False;;0;False;0;0,0,0,0;VertexOffset;True;False;Cylindrical;False;True;Relative;0;;6;-1;-1;-1;0;False;0;0;False;;-1;0;False;;0;0;0;False;0.1;False;;0;False;;False;15;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT;0;False;9;FLOAT;0;False;10;FLOAT;0;False;13;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;12;FLOAT3;0,0,0;False;14;FLOAT4;0,0,0,0;False;15;FLOAT3;0,0,0;False;0
WireConnection;30;0;22;0
WireConnection;24;0;31;0
WireConnection;37;0;38;0
WireConnection;37;1;30;1
WireConnection;31;0;32;0
WireConnection;31;1;30;0
WireConnection;39;0;83;0
WireConnection;39;1;30;2
WireConnection;86;0;87;0
WireConnection;86;1;84;0
WireConnection;42;0;31;0
WireConnection;42;1;37;0
WireConnection;42;2;39;0
WireConnection;85;0;42;0
WireConnection;85;1;90;0
WireConnection;90;1;91;0
WireConnection;90;0;86;0
WireConnection;84;2;89;0
WireConnection;84;3;92;0
WireConnection;109;0;29;4
WireConnection;109;1;108;0
WireConnection;70;2;85;0
WireConnection;70;9;109;0
ASEEND*/
//CHKSM=8D08607400052D802997F56C128DEFCA0EAFA01C