using System;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using UnityEngine.Networking;


// public class UnityWebRequestAwaiter : INotifyCompletion
// {
//     private UnityWebRequestAsyncOperation asyncOp;
//     private Action continuation;
//
//     public UnityWebRequestAwaiter(UnityWebRequestAsyncOperation asyncOp)
//     {
//         this.asyncOp = asyncOp;
//         asyncOp.completed += OnRequestCompleted;
//     }
//
//     public bool IsCompleted { get { return asyncOp.isDone; } }
//
//     public void GetResult() { }
//
//     public void OnCompleted(Action continuation)
//     {
//         // this.continuation = continuation;
//     }
//
//     private void OnRequestCompleted(UnityEngine.AsyncOperation obj)
//     {
//         continuation();
//     }
// }

public static class ExtensionMethods {
    public static TaskAwaiter<UnityWebRequest> GetAwaiter(this UnityWebRequestAsyncOperation asyncOp) {
        // return new UnityWebRequestAwaiter(asyncOp);
        var taskCompletionSource = new TaskCompletionSource<UnityWebRequest>();
        
        asyncOp.completed += _ => {
            if (asyncOp.webRequest != null) {
                taskCompletionSource.SetResult(asyncOp.webRequest);
            } else {
                taskCompletionSource.SetException(new NullReferenceException("[GetAwaiter] webRequest is null"));
            }
        };
        
        return taskCompletionSource.Task.GetAwaiter();
    }
}