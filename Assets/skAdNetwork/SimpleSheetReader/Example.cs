using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Security.Cryptography;
using System.IO;
using System;
using tule.JWT;
using UnityEngine.Networking;
using System.Linq;
using System.Text;

public class Example : MonoBehaviour
{
    byte[] cert;
    const string serviceAccMail = "<EMAIL>";
    const string sheetUrl = "https://sheets.googleapis.com/v4/spreadsheets/18YFoBb82RiTPx7KUFx15tEKyxOux6c7YpPy_AcOcKGQ/values/PI!A1:AB2200";

    // Start is called before the first frame update
    void Start()
    {
        var jwt = SignData();
        Debug.Log("JWT: " + jwt);
        StartCoroutine(RequestForAccessToken(jwt));
    }

    IEnumerator RequestForAccessToken(string jwt)
    {
        var form = new WWWForm();
        form.AddField("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer");
        form.AddField("assertion", jwt);
        var post = UnityWebRequest.Post("https://oauth2.googleapis.com/token", form);
        post.SetRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        yield return post.SendWebRequest();
        if(post.isHttpError || post.isNetworkError)
        {
            Debug.LogError(post.error);
        }
        else
        {
            var text = post.downloadHandler.text;
            var res = JsonUtility.FromJson<Response>(text);
            Debug.Log("Response: " + res);
            Debug.Log("Access token: " + res.access_token);
            yield return StartCoroutine(RequestSheetValues(res.access_token));
        }
    }

    IEnumerator RequestSheetValues(string access_token)
    {
        var get = UnityWebRequest.Get(sheetUrl);
        get.SetRequestHeader("Authorization", "Bearer " + access_token);
        yield return get.SendWebRequest();
        if (get.isHttpError || get.isNetworkError)
        {
            Debug.LogError(get.error);
        }
        else
        {
            var text = get.downloadHandler.text;
            Debug.Log("Values: " + text);
        }
    }

    string SignData()
    {
        var textAsset = Resources.Load<TextAsset>("testlinkcontenttool-5e2a8b84c0f7");
        cert = textAsset.bytes;

        var time = GetTimeStamp();
        var payload = new Payload
        {
            iss = serviceAccMail,
            scope = "https://www.googleapis.com/auth/spreadsheets.readonly",
            aud = "https://oauth2.googleapis.com/token",
            iat = time,
            exp = time + 3600
        };
        var jwt = JsonWebToken.Create(payload, cert, tule.JWT.HashAlgorithm.RS256);
        return jwt;
    }

    int GetTimeStamp()
    {
        var time = DateTime.UtcNow;
        var timestamp = (int)(time - new DateTime(1970, 1, 1)).TotalSeconds;
        return timestamp;
    }
}

public class Payload
{
    public string iss;
    public string scope;
    public string aud;
    public int iat;
    public int exp;
}

public class Response
{
    public string id_token;
    public string access_token;
}