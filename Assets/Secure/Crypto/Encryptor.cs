using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Security.Cryptography;
using System.IO;
using System.Text;
using System;
using tule.Secure;
using System.Threading.Tasks;

namespace tule.Crypto
{
    public class Encryptor : CustomYieldInstruction
    {
        const int buffer_size = 1024;
        static byte[] key;
        static byte[] iv;

        public enum Mode
        {
            Encrypt,
            Decrypt
        }

        private bool isDone;

        public override bool keepWaiting => !isDone;

        public byte[] Data { get; private set; }

        public string Error { get; private set; }

        public bool HasError => !string.IsNullOrWhiteSpace(Error);

        public Encryptor(byte[] data, Mode mode)
        {
            if (key == null)
                ReadConfig();

            switch (mode)
            {
                case Mode.Encrypt:
                    Encrypt(data);
                    break;
                case Mode.Decrypt:
                    Decrypt(data);
                    break;
            }
        }

        async void Encrypt(byte[] data) {
            try {
                Aes aes = Aes.Create();
                ICryptoTransform transform = aes.CreateEncryptor(key, iv);
                var inputStream = new MemoryStream(data);
                MemoryStream ms = new MemoryStream();
                using (CryptoStream cs = new CryptoStream(ms, transform, CryptoStreamMode.Write))
                {
                    await Task.Run(() => CopyTo(inputStream, cs));
                }
                Data = ms.ToArray();
                isDone = true;
            } catch (Exception e) {
                CustomException.Fire("[Encryptor][Encrypt]", e.Message);
            }
        }


        async void Decrypt(byte[] data) {
            try {
                Aes aes = Aes.Create();
                ICryptoTransform transform = aes.CreateDecryptor(key, iv);
                var stream = new MemoryStream(data);
                MemoryStream ms = new MemoryStream();
                using (CryptoStream cs = new CryptoStream(stream, transform, CryptoStreamMode.Read))
                {
                    await Task.Run(() => CopyTo(cs, ms));
                }
                Data = ms.ToArray();
                isDone = true;
            } catch (Exception e) {
                CustomException.Fire("[Encryptor][Decrypt]", e.Message);
            }
        }

        static void ReadConfig()
        {
            var config = Resources.Load<SecureConfig>("SecureConfig");
            if (config == null)
            {
                Debug.LogError("Please run \"tule/Generate config\" before using Encryptor");
                return;
            }
            key = Convert.FromBase64String(config.base64Key);
            iv = Convert.FromBase64String(config.base64IV);
        }

        async void CopyTo(Stream src, Stream dst) {
            try {
                byte[] buffer = new byte[buffer_size];
                int count;
                while ((count = await src.ReadAsync(buffer, 0, buffer_size)) > 0)
                {
                    await dst.WriteAsync(buffer, 0, count);
                }
            } catch (Exception e) {
                CustomException.Fire("[Encryptor][CopyTo]", e.Message);
            }
        }
    }
}