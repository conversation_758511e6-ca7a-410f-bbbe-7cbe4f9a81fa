/*
 * Copyright (c) 2013 <PERSON>
 *
 * Based on the JSON parser by <PERSON>
 * http://techblog.procurios.nl/k/618/news/view/14605/14863/How-do-I-write-my-own-parser-for-<PERSON><PERSON><PERSON>.html
 *
 * Simplified it so that it doesn't throw exceptions
 * and can be used in Unity iPhone with maximum code stripping.
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;

[assembly: InternalsVisibleTo("AirbridgeTests")]

/******************
/// <summary>
/// This class encodes and decodes JSON strings.
/// Spec. details, see http://www.json.org/
///
/// JSON uses Arrays and Objects. These correspond here to the datatypes IList and IDictionary.
/// All numbers are parsed to doubles.
///
/// @note
/// Extensions of Unity Technologies [MiniJSON](https://github.com/Unity-Technologies/UnityCsReference/blob/77b37cd9f002e27b45be07d6e3667ee53985ec82/External/JsonParsers/MiniJson/MiniJSON.cs).
/// </summary>
******************/

// Exclude a class from the document
/// @cond HIDDEN_SYMBOLS
static class AirbridgeJson
{
    /// <summary>
    /// Parses the string json into a value
    /// </summary>
    /// <param name="json">A JSON string.</param>
    /// <returns>An List&lt;object&gt;, a Dictionary&lt;string, object&gt;, a double, an integer,a string, null, true, or false</returns>
    public static object Deserialize(string json)
    {
        // save the string for debug information
        if (json == null)
        {
            return null;
        }

        return Parser.Parse(json);
    }

    sealed class Parser : IDisposable
    {
        const string WORD_BREAK = "{}[],:\"";

        public static bool IsWordBreak(char c)
        {
            return Char.IsWhiteSpace(c) || WORD_BREAK.IndexOf(c) != -1;
        }

        enum TOKEN
        {
            NONE,
            CURLY_OPEN,
            CURLY_CLOSE,
            SQUARED_OPEN,
            SQUARED_CLOSE,
            COLON,
            COMMA,
            STRING,
            NUMBER,
            TRUE,
            FALSE,
            NULL
        }

        StringReader json;

        Parser(string jsonString)
        {
            json = new StringReader(jsonString);
        }

        public static object Parse(string jsonString)
        {
            using (var instance = new Parser(jsonString))
            {
                return instance.ParseValue();
            }
        }

        public void Dispose()
        {
            json.Dispose();
            json = null;
        }

        Dictionary<string, object> ParseObject()
        {
            Dictionary<string, object> table = new Dictionary<string, object>();

            // ditch opening brace
            json.Read();

            // {
            while (true)
            {
                switch (NextToken)
                {
                    case TOKEN.NONE:
                        return null;
                    case TOKEN.COMMA:
                        continue;
                    case TOKEN.CURLY_CLOSE:
                        return table;
                    default:
                        // name
                        string name = ParseString();
                        if (name == null)
                        {
                            return null;
                        }

                        // :
                        if (NextToken != TOKEN.COLON)
                        {
                            return null;
                        }

                        // ditch the colon
                        json.Read();

                        // value
                        table[name] = ParseValue();
                        break;
                }
            }
        }

        List<object> ParseArray()
        {
            List<object> array = new List<object>();

            // ditch opening bracket
            json.Read();

            // [
            var parsing = true;
            while (parsing)
            {
                TOKEN nextToken = NextToken;

                switch (nextToken)
                {
                    case TOKEN.NONE:
                        return null;
                    case TOKEN.COMMA:
                        continue;
                    case TOKEN.SQUARED_CLOSE:
                        parsing = false;
                        break;
                    default:
                        object value = ParseByToken(nextToken);

                        array.Add(value);
                        break;
                }
            }

            return array;
        }

        object ParseValue()
        {
            TOKEN nextToken = NextToken;
            return ParseByToken(nextToken);
        }

        object ParseByToken(TOKEN token)
        {
            switch (token)
            {
                case TOKEN.STRING:
                    return ParseString();
                case TOKEN.NUMBER:
                    return ParseNumber();
                case TOKEN.CURLY_OPEN:
                    return ParseObject();
                case TOKEN.SQUARED_OPEN:
                    return ParseArray();
                case TOKEN.TRUE:
                    return true;
                case TOKEN.FALSE:
                    return false;
                case TOKEN.NULL:
                    return null;
                default:
                    return null;
            }
        }

        string ParseString()
        {
            StringBuilder s = new StringBuilder();
            char c;

            // ditch opening quote
            json.Read();

            bool parsing = true;
            while (parsing)
            {
                if (json.Peek() == -1)
                {
                    parsing = false;
                    break;
                }

                c = NextChar;
                switch (c)
                {
                    case '"':
                        parsing = false;
                        break;
                    case '\\':
                        if (json.Peek() == -1)
                        {
                            parsing = false;
                            break;
                        }

                        c = NextChar;
                        switch (c)
                        {
                            case '"':
                            case '\\':
                            case '/':
                                s.Append(c);
                                break;
                            case 'b':
                                s.Append('\b');
                                break;
                            case 'f':
                                s.Append('\f');
                                break;
                            case 'n':
                                s.Append('\n');
                                break;
                            case 'r':
                                s.Append('\r');
                                break;
                            case 't':
                                s.Append('\t');
                                break;
                            case 'u':
                                var hex = new char[4];

                                for (int i = 0; i < 4; i++)
                                {
                                    hex[i] = NextChar;
                                }

                                s.Append((char)Convert.ToInt32(new string(hex), 16));
                                break;
                        }

                        break;
                    default:
                        s.Append(c);
                        break;
                }
            }

            return s.ToString();
        }

        /// <summary>
        /// Parses a string representation of a number and returns it as either a long (Int64) or double.
        /// </summary>
        object ParseNumber()
        {
            string number = NextWord;

            if (!number.Contains('.') &&
                Int64.TryParse(number, System.Globalization.NumberStyles.Any,
                    System.Globalization.CultureInfo.InvariantCulture, out var parsedInt))
            {
                return parsedInt;
            }

            Double.TryParse(number, System.Globalization.NumberStyles.Any,
                System.Globalization.CultureInfo.InvariantCulture, out var parsedDouble);
            return parsedDouble;
        }

        void EatWhitespace()
        {
            while (json.Peek() != -1 && Char.IsWhiteSpace(PeekChar))
            {
                json.Read();
            }
        }

        char PeekChar
        {
            get { return Convert.ToChar(json.Peek()); }
        }

        char NextChar
        {
            get { return Convert.ToChar(json.Read()); }
        }

        string NextWord
        {
            get
            {
                StringBuilder word = new StringBuilder();

                while (json.Peek() != -1 && !IsWordBreak(PeekChar))
                {
                    word.Append(NextChar);
                }

                return word.ToString();
            }
        }

        TOKEN NextToken
        {
            get
            {
                EatWhitespace();

                if (json.Peek() == -1)
                {
                    return TOKEN.NONE;
                }

                switch (PeekChar)
                {
                    case '{':
                        return TOKEN.CURLY_OPEN;
                    case '}':
                        json.Read();
                        return TOKEN.CURLY_CLOSE;
                    case '[':
                        return TOKEN.SQUARED_OPEN;
                    case ']':
                        json.Read();
                        return TOKEN.SQUARED_CLOSE;
                    case ',':
                        json.Read();
                        return TOKEN.COMMA;
                    case '"':
                        return TOKEN.STRING;
                    case ':':
                        return TOKEN.COLON;
                    case '0':
                    case '1':
                    case '2':
                    case '3':
                    case '4':
                    case '5':
                    case '6':
                    case '7':
                    case '8':
                    case '9':
                    case '-':
                        return TOKEN.NUMBER;
                }

                switch (NextWord)
                {
                    case "false":
                        return TOKEN.FALSE;
                    case "true":
                        return TOKEN.TRUE;
                    case "null":
                        return TOKEN.NULL;
                }

                return TOKEN.NONE;
            }
        }
    }

    /// <summary>
    /// Converts a IDictionary / IList object or a simple type (string, int, etc.) into a JSON string
    /// </summary>
    /// <param name="json">A Dictionary&lt;string, object&gt; / List&lt;object&gt;</param>
    /// <param name="pretty">A boolean to indicate whether or not JSON should be prettified, default is false.</param>
    /// <param name="indentText">A string to ibe used as indentText, default is 2 spaces.</param>
    /// <returns>A JSON encoded string, or null if object 'json' is not serializable</returns>
    public static string Serialize(object obj, bool pretty = false, string indentText = "  ")
    {
        return Serializer.Serialize(obj, pretty, indentText);
    }

    sealed class Serializer
    {
        readonly string indentText;
        bool pretty;
        StringBuilder builder;

        Serializer(bool pretty, string indentText)
        {
            builder = new StringBuilder();
            this.pretty = pretty;
            this.indentText = indentText;
        }

        public static string Serialize(object obj, bool pretty, string indentText)
        {
            var instance = new Serializer(pretty, indentText);

            if (IsSerializableValue(obj))
            {
                instance.SerializeValue(obj, 0);
            }

            return instance.builder.ToString();
        }

        void SerializeValue(object value, int indent)
        {
            IDictionary asDict;
            string asStr;

            if (value == null || value is Delegate)
            {
                builder.Append("null");
            }
            else if ((asStr = value as string) != null)
            {
                SerializeString(asStr);
            }
            else if (value is bool)
            {
                builder.Append((bool)value ? "true" : "false");
            }
            else if (IsList(value, out var asList))
            {
                SerializeArray(asList, indent);
            }
            else if ((asDict = value as IDictionary) != null)
            {
                SerializeObject(asDict, indent);
            }
            else if (value is char)
            {
                SerializeString(new string((char)value, 1));
            }
            else
            {
                SerializeOther(value, indent);
            }
        }

        void SerializeObject(IDictionary obj, int indent)
        {
            if (pretty && obj.Keys.Count == 0)
            {
                builder.Append("{}");
                return;
            }

            bool first = true;
            string indentLine = null;

            builder.Append('{');
            if (pretty)
            {
                builder.Append('\n');
                indentLine = string.Concat(Enumerable.Repeat(indentText, indent).ToArray());
            }

            foreach (object e in obj.Keys)
            {
                if (!IsSerializableValue(obj[e])) continue;

                if (!first)
                {
                    builder.Append(',');
                    if (pretty)
                        builder.Append('\n');
                }

                if (pretty)
                {
                    builder.Append(indentLine);
                    builder.Append(indentText);
                }

                SerializeString(e.ToString());
                builder.Append(':');
                if (pretty)
                    builder.Append(' ');

                SerializeValue(obj[e], indent + 1);

                first = false;
            }

            if (pretty)
            {
                builder.Append('\n');
                builder.Append(indentLine);
            }

            builder.Append('}');
        }

        void SerializeArray(IList anArray, int indent)
        {
            if (pretty && anArray.Count == 0)
            {
                builder.Append("[]");
                return;
            }

            bool first = true;
            string indentLine = null;

            builder.Append('[');
            if (pretty)
            {
                builder.Append('\n');
                indentLine = string.Concat(Enumerable.Repeat(indentText, indent).ToArray());
            }

            foreach (object obj in anArray)
            {
                if (!IsSerializableValue(obj)) continue;

                if (!first)
                {
                    builder.Append(',');
                    if (pretty)
                        builder.Append('\n');
                }

                if (pretty)
                {
                    builder.Append(indentLine);
                    builder.Append(indentText);
                }

                SerializeValue(obj, indent + 1);

                first = false;
            }

            if (pretty)
            {
                builder.Append('\n');
                builder.Append(indentLine);
            }

            builder.Append(']');
        }

        void SerializeString(string str)
        {
            builder.Append('\"');

            char[] charArray = str.ToCharArray();
            foreach (var c in charArray)
            {
                switch (c)
                {
                    case '"':
                        builder.Append("\\\"");
                        break;
                    case '\\':
                        builder.Append("\\\\");
                        break;
                    case '\b':
                        builder.Append("\\b");
                        break;
                    case '\f':
                        builder.Append("\\f");
                        break;
                    case '\n':
                        builder.Append("\\n");
                        break;
                    case '\r':
                        builder.Append("\\r");
                        break;
                    case '\t':
                        builder.Append("\\t");
                        break;
                    default:
                        int codepoint = Convert.ToInt32(c);
                        if ((codepoint >= 32) && (codepoint <= 126))
                        {
                            builder.Append(c);
                        }
                        else
                        {
                            builder.Append("\\u");
                            builder.Append(codepoint.ToString("x4"));
                        }

                        break;
                }
            }

            builder.Append('\"');
        }

        void SerializeOther(object value, int indent)
        {
            // NOTE: decimals lose precision during serialization.
            // They always have, I'm just letting you know.
            // Previously floats and doubles lost precision too.
            if (value is float)
            {
                var modifiedValue = ((float)value).ToString("R", System.Globalization.CultureInfo.InvariantCulture);
                if (
                    (modifiedValue.IndexOf("e", StringComparison.OrdinalIgnoreCase) == -1) &&
                    !modifiedValue.Contains('.')
                )
                {
                    modifiedValue += ".0";
                }

                builder.Append(modifiedValue);
            }
            else if (value is int
                     || value is uint
                     || value is long
                     || value is sbyte
                     || value is byte
                     || value is short
                     || value is ushort
                     || value is ulong)
            {
                builder.Append(value);
            }
            else if (value is double || value is decimal)
            {
                var modifiedValue = Convert.ToDouble(value)
                    .ToString("R", System.Globalization.CultureInfo.InvariantCulture);
                if (
                    (modifiedValue.IndexOf("e", StringComparison.OrdinalIgnoreCase) == -1) &&
                    !modifiedValue.Contains('.')
                )
                {
                    modifiedValue += ".0";
                }

                builder.Append(modifiedValue);
            }
            else
            {
                Dictionary<string, object> map = new Dictionary<string, object>();
                List<FieldInfo> fields = value.GetType().GetFields(BindingFlags.Instance | BindingFlags.Public)
                    .ToList();
                foreach (FieldInfo field in fields)
                {
                    map.Add(field.Name, field.GetValue(value));
                }

                List<PropertyInfo> properties =
                    value.GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public).ToList();
                foreach (PropertyInfo property in properties)
                {
                    map.Add(property.Name, property.GetValue(value, null));
                }

                SerializeObject(map, indent);
            }
        }

        private static bool IsSerializableValue(object value)
        {
            if (value is float)
            {
                // Ignores NaN and Infinity values
                if ((float.IsInfinity((float)value) || float.IsNaN((float)value)))
                    return false;
            }
            else if (value is double || value is decimal)
            {
                // Ignores NaN and Infinity values
                if ((double.IsInfinity((double)value) || double.IsNaN((double)value)))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Attempts to determine if the provided object is an enumerable collection
        /// with a single generic type argument and converts it to a List&lt;object&gt;.
        /// </summary>
        /// <param name="value"> The object to be checked and converted.</param>
        /// <param name="result"> An output parameter that will contain the List&lt;object&gt; if the conversion is successful, otherwise it will be null.</param>
        /// <returns> Returns true if the object is a valid enumerable with a single generic type argument and is successfully converted to a List&lt;object&gt;,
        /// otherwise returns false.</returns>
        private bool IsList(object value, out List<object> result)
        {
            IEnumerable asEnumerable;

            if ((asEnumerable = value as IEnumerable) != null)
            {
                Type type = asEnumerable.GetType();
                if (!type.IsGenericType ||
                    (type.IsGenericType && type.GetGenericArguments().Length == 1))
                {
                    var list = new List<object>();
                    IEnumerator enumerator = asEnumerable.GetEnumerator();
                    try
                    {
                        while (enumerator.MoveNext())
                        {
                            list.Add(enumerator.Current);
                        }
                    }
                    finally
                    {
                        if (enumerator is IDisposable disposable)
                        {
                            disposable.Dispose();
                        }
                    }

                    result = list;
                    return true;
                }
            }

            result = null;
            return false;
        }
    }
}
// ReSharper disable once InvalidXmlDocComment
/// @endcond