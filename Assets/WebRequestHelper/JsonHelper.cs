using System.Collections.Generic;
using System;
using UnityEngine;

namespace Amanotes.TripleSDK.Core
{
    public class JsonHelper
    {
        public static T[] FromJsonToArray<T>(string json)
        {
            Wrapper<T> wrapper = JsonUtility.FromJson<Wrapper<T>>(json);
            return wrapper.notes;
        }

        public static T FromJsonToObject<T>(string json)
        {
            return JsonUtility.FromJson<T>(json);
        }

        public static string ToJson<T>(T[] array)
        {
            Wrapper<T> wrapper = new Wrapper<T>();
            wrapper.notes = array;
            return JsonUtility.ToJson(wrapper);
        }

        public static string ToJson<T>(T obj)
        {            
            return JsonUtility.ToJson(obj);
        }

        [Serializable]
        private class Wrapper<T>
        {
            public T[] notes;
        }
    }
}