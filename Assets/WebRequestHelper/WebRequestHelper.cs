using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;
using System.Runtime.CompilerServices;

namespace Amanotes.TripleSDK.Core
{
    public class WebRequestHelper
    {
        public static async Task<ResponseData> Get(string uri, string token)
        {
            using (UnityWebRequest request = new UnityWebRequest(uri, "GET"))
            {
                request.SetRequestHeader("Authorization", $"bearer {token}");
                request.downloadHandler = new DownloadHandlerBuffer();
                await request.SendWebRequest();

                ResponseData response = new ResponseData();

                if (request.isNetworkError || request.isHttpError)
                {
                    response.error = ParseError(request.downloadHandler.text);
                }
                else
                {
                    Debug.Log("GET complete!");
                    response.data = request.downloadHandler.text;
                }

                return response;
            }
        }

        public static async Task<ResponseData> Post(string uri, string data, string header = "")
        {
            using (UnityWebRequest request = new UnityWebRequest(uri, "POST"))
            {
                byte[] bodyRaw = System.Text.Encoding.UTF8.GetBytes(data);
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("api-token" ,header);
                await request.SendWebRequest();

                ResponseData response = new ResponseData();

                if (request.isNetworkError || request.isHttpError)
                {
                    response.error = ParseError(request.downloadHandler.text);
                }
                else
                {
                    Debug.Log("POST complete!");
                    response.data = request.downloadHandler.text;
                }

                return response;
            }
        }

        public static async Task<ResponseData> Put(string uri, string token, string data)
        {
            using (UnityWebRequest request = new UnityWebRequest(uri, "PUT"))
            {
                byte[] bodyRaw = System.Text.Encoding.UTF8.GetBytes(data);
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("Authorization", $"bearer {token}");
                await request.SendWebRequest();

                ResponseData response = new ResponseData();

                if (request.isNetworkError || request.isHttpError)
                {
                    response.error = ParseError(request.downloadHandler.text);
                }
                else
                {
                    Debug.Log("PUT complete!");
                    response.data = request.downloadHandler.text;
                }

                return response;
            }
        }

        public static async Task<ResponseData> DownloadImage(string imageUrl)
        {
            UnityWebRequest request = UnityWebRequestTexture.GetTexture(imageUrl);
            await request.SendWebRequest();

            ResponseData response = new ResponseData();

            if (request.isNetworkError || request.isHttpError)
            {
                response.error = ParseError(request.downloadHandler.text);
            }
            else
            {
                Debug.Log("Download img complete!");
                response.data = ((DownloadHandlerTexture)request.downloadHandler).texture;
            }

            return response;
        }

        static ErrorResponseData ParseError(string rawError)
        {
            ErrorResponseData error = JsonHelper.FromJsonToObject<ErrorResponseData>(rawError);
            Debug.LogError(error.message);
            return error;
        }
    }

    public static class ExtensionMethods
    {
        public static TaskAwaiter GetAwaiter(this AsyncOperation asyncOp)
        {
            var tcs = new TaskCompletionSource<object>();
            asyncOp.completed += obj => { tcs.SetResult(null); };
            return ((Task)tcs.Task).GetAwaiter();
        }
    }

    public enum HTTPStatus
    {
        OK = 200,
        BadRequest = 400,
        Unauthorized = 401,
        Forbidden = 403,
        NotFound = 404,
        InternalServerError = 500
    }
}