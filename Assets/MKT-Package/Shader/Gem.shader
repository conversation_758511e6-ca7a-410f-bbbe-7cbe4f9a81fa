Shader "pGem/Rim_Reflect"
{
    Properties
    {
        _DiffuseColour ("Diffuse Colour", Color) = (1,1,1,1)
        _Glossiness ("Glossiness", Range(0, 1)) = 0.65
        _RimThickness ("Rim Thickness", Range(0, 4)) = 0.75
        _RimPower ("Rim Power", Range(0, 2)) = 1
        _Cubemap ("Cubemap", Cube) = "_Skybox" {}
        _Fade ("Fade Amount", Range(0,1)) = 1
    }
    SubShader
    {
        Tags
        {
            "RenderType"="Transparent"
        }
        Pass
        {
            Name "FORWARD"
            Tags
            {
                "LightMode"="ForwardBase"
            }
            ZWrite On
            Blend SrcAlpha OneMinusSrcAlpha

            ZTest Always

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            //#define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #include "AutoLight.cginc"
            #pragma multi_compile_fwdbase_fullshadows
            #pragma multi_compile_fog
            #pragma exclude_renderers xbox360 ps3
            #pragma target 2.0
            uniform float _Fade;
            uniform float4 _LightColor0;
            uniform float4 _DiffuseColour;
            uniform float _Glossiness;
            uniform samplerCUBE _Cubemap;
            uniform float _RimThickness;
            uniform float _RimPower;

            struct VertexInput
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
            };

            struct VertexOutput
            {
                float4 pos : SV_POSITION;
                float4 posWorld : TEXCOORD0;
                float3 normalDir : TEXCOORD1;
                LIGHTING_COORDS(2, 3)
                UNITY_FOG_COORDS(4)
            };

            VertexOutput vert(VertexInput v)
            {
                VertexOutput o = (VertexOutput)0;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                float3 lightColor = _LightColor0.rgb;
                o.pos = UnityObjectToClipPos(v.vertex);
                UNITY_TRANSFER_FOG(o, o.pos);
                TRANSFER_VERTEX_TO_FRAGMENT(o)
                return o;
            }

            float4 frag(VertexOutput i) : COLOR
            {
                i.normalDir = normalize(i.normalDir);
                float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
                float3 normalDirection = i.normalDir;
                float3 viewReflectDirection = reflect(-viewDirection, normalDirection);
                float3 lightDirection = normalize(_WorldSpaceLightPos0.xyz);
                float3 lightColor = _LightColor0.rgb;
                float3 halfDirection = normalize(viewDirection + lightDirection);
                ////// Lighting:
                float attenuation = LIGHT_ATTENUATION(i);
                float3 attenColor = attenuation * _LightColor0.xyz;
                ///////// Gloss:
                float gloss = _Glossiness;
                float specPow = exp2(gloss * 10.0 + 1.0);
                ////// Specular:
                float NdotL = max(0, dot(normalDirection, lightDirection));
                float node_7714 = 1.0;
                float3 specularColor = float3(node_7714, node_7714, node_7714);
                float3 directSpecular = (floor(attenuation) * _LightColor0.xyz) * pow(
                    max(0, dot(halfDirection, normalDirection)), specPow) * specularColor;
                float3 specular = directSpecular;
                ////// Emissive:
                float3 emissive = (_DiffuseColour.rgb + (pow(1.0 - max(0, dot(normalDirection, viewDirection)),
         _RimThickness) * _RimPower * texCUBE(_Cubemap, viewReflectDirection).rgb));
                /// Final Color:
                float3 finalColor = specular + emissive;
                fixed4 finalRGBA = fixed4(finalColor, _Fade);
                UNITY_APPLY_FOG(i.fogCoord, finalRGBA);
                return finalRGBA;
            }
            ENDCG
        }
        Pass
        {
            Name "FORWARD_DELTA"
            Tags
            {
                "LightMode"="ForwardAdd"
            }
            Blend One One
            ZWrite On
            ZTest Always

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            // #define UNITY_PASS_FORWARDADD
            #include "UnityCG.cginc"
            #include "AutoLight.cginc"
            #pragma multi_compile_fwdadd_fullshadows
            #pragma multi_compile_fog
            #pragma exclude_renderers xbox360 ps3
            #pragma target 2.0
            uniform float4 _LightColor0;
            uniform float4 _DiffuseColour;
            uniform float _Glossiness;
            uniform samplerCUBE _Cubemap;
            uniform float _RimThickness;
            uniform float _RimPower;

            struct VertexInput
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
            };

            struct VertexOutput
            {
                float4 pos : SV_POSITION;
                float4 posWorld : TEXCOORD0;
                float3 normalDir : TEXCOORD1;
                LIGHTING_COORDS(2, 3)
                UNITY_FOG_COORDS(4)
            };

            VertexOutput vert(VertexInput v)
            {
                VertexOutput o = (VertexOutput)0;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                float3 lightColor = _LightColor0.rgb;
                o.pos = UnityObjectToClipPos(v.vertex);
                UNITY_TRANSFER_FOG(o, o.pos);
                TRANSFER_VERTEX_TO_FRAGMENT(o)
                return o;
            }

            float4 frag(VertexOutput i) : COLOR
            {
                i.normalDir = normalize(i.normalDir);
                float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
                float3 normalDirection = i.normalDir;
                float3 viewReflectDirection = reflect(-viewDirection, normalDirection);
                float3 lightDirection = normalize(lerp(_WorldSpaceLightPos0.xyz,
                    _WorldSpaceLightPos0.xyz - i.posWorld.xyz, _WorldSpaceLightPos0.w));
                float3 lightColor = _LightColor0.rgb;
                float3 halfDirection = normalize(viewDirection + lightDirection);
                ////// Lighting:
                //float attenuation = LIGHT_ATTENUATION(i);
                float3 attenColor = _LightColor0.xyz;
                ///////// Gloss:
                float gloss = _Glossiness;
                float specPow = exp2(gloss * 10.0 + 1.0);
                ////// Specular:
                float NdotL = max(0, dot(normalDirection, lightDirection));
                float node_7714 = 1.0;
                float3 specularColor = float3(node_7714, node_7714, node_7714);
                float3 directSpecular = attenColor * pow(max(0, dot(halfDirection, normalDirection)), specPow) *
                    specularColor;
                float3 specular = directSpecular;
                /// Final Color:
                float3 finalColor = specular;
                fixed4 finalRGBA = fixed4(finalColor * 1, 0);
                UNITY_APPLY_FOG(i.fogCoord, finalRGBA);
                return finalRGBA;
            }
            ENDCG
        }
    }
    FallBack "Diffuse"
    CustomEditor "ShaderForgeMaterialInspector"
}