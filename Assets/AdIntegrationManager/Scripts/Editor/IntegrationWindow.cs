using System;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace AdIntegrationManager.Editor
{
    class IntegrationWindow : EditorWindow
    {
        internal const string windowTitle = "Playgap Integration Manager";

        private int tabsInt = 0;
        private readonly string[] tabs = { "Applovin MAX", "Unity Levelplay", "Google Admob", "Other" };
        private const string SettingsFolder = "Assets/AdIntegrationManager/Resources";
        private static readonly string SettingsPath = Path.Combine(SettingsFolder, "AdIntegrationManagerSettings.asset");
        private const string ManualIntegration = "For now, please use the <color=orange>Integration Documentation found in the Playgap Dashboard</color> to Integrate with us Manually";

        private AdIntegrationManagerSettings settings;

        private void OnEnable()
        {
            settings = AssetDatabase.LoadAssetAtPath<AdIntegrationManagerSettings>(SettingsPath);
            if (settings == null)
            {
                settings = CreateInstance<AdIntegrationManagerSettings>();
                AssetDatabase.CreateAsset(settings, SettingsPath);
            }

            titleContent = new GUIContent(windowTitle);
            minSize = new Vector2(530, 548);
            maxSize = new Vector2(530, 548);
        }

        private void OnGUI()
        {
            GUILayout.Space(10);

            GUILayout.Label("Playgap Plugin Details", new GUIStyle(GUIStyleExtensions.Title)
            {
                fontSize = 14,
            });
            DrawPluginDetails();

            GUILayout.Space(16);

            tabsInt = GUILayout.Toolbar(tabsInt, tabs);

            switch (tabsInt)
            {
                case 0:
                    CommonForm("Applovin MAX SDK");
                    DetailsBody(() =>
                    {
                        IntegratorForm(new MaxSdkPlaygapIntegrator());
                    });
                    break;

                case 1:
                    DetailsBody(() =>
                    {
                        UnsupportedIntegratorForm();
                    });
                    break;

                case 2:
                    DetailsBody(() =>
                    {
                        UnsupportedIntegratorForm();
                    });
                    break;

                case 3:
                    DetailsBody(() =>
                    {
                        ManualIntegratorForm();
                    });
                    break;
            };
        }

        private void CommonForm(string sdk)
        {
            GUILayout.Space(16);
            GUILayout.Label("Step 1. Install and configure " + sdk, GUIStyleExtensions.Title);
            GUILayout.Space(16);
            GUILayout.Label("Step 2. Provide Playgap API keys found in the Playgap Dashboard", GUIStyleExtensions.Title);
            DetailsBody(() =>
            {
                PlaygapForm();
            });
            GUILayout.Space(16);
            GUILayout.Label("Step 3. Integrate " + sdk + " with Playgap", GUIStyleExtensions.Title);
            GUILayout.Space(6);
            DetailsBody(() =>
            {
                GUILayout.Label("Automatic Integration Outcomes:", GUIStyleExtensions.SubTitle);

                var outcomes = new string[] {
                    "• Online Ad delivery where Mediation does not have an Ad",
                    "• Offline Ad delivery",
                    "• Measuring Playgap's Percentage of Revenue Uplift"
                };

                foreach (var step in outcomes)
                {
                    GUILayout.Label(step, GUIStyleExtensions.ListItem);
                }
            });
        }

        private void DrawPluginDetails()
        {
            var info = PluginInfoLoader.info;

            if (info == null) return;

            DetailsBody(() =>
            {
                using (new EditorGUILayout.VerticalScope("box"))
                {
                    DrawPluginDetailRow("Current Version", Utilities.CurrentSdkVersion());
                    DrawPluginDetailRow("Latest Version", info.version);

                    GUILayout.BeginHorizontal();
                    GUILayout.FlexibleSpace();

                    GUI.enabled = Utilities.IsCurrentSdkVersionLower(info.version);
                    if (GUILayout.Button(new GUIContent("Upgrade")))
                    {
                        PluginEditorCoroutine.StartCoroutine(PlaygapInitialize.Updater.DownloadPlugin(info));
                    }

                    GUI.enabled = true;
                    GUILayout.Space(5);
                    GUILayout.EndHorizontal();

                    GUILayout.Space(5);
                }
            });
        }

        private void DrawPluginDetailRow(string title, string details)
        {
            using (new EditorGUILayout.HorizontalScope())
            {
                GUILayout.Label(title, new GUIStyle(GUIStyleExtensions.SubTitle)
                {
                    fixedWidth = 128
                });
                GUILayout.Label(details);
            }
        }
         
        private void PlaygapForm()
        {
            GUILayout.Space(6);
            GUILayout.BeginHorizontal();
            GUILayout.BeginVertical();
            TextField("iOS Api Key", ref settings.iOSApiKey);
            TextField("Android Api Key", ref settings.AndroidApiKey);
            GUILayout.EndVertical();
            GUILayout.Space(8);
            if (GUILayout.Button("Get API keys", GUILayout.Height(43)))
            {
                Application.OpenURL(Constants.apps);
            }
            GUILayout.EndHorizontal();
            GUILayout.Space(8);
            if (GUILayout.Button("Save Settings"))
            {
                SavePlaygapSettings(settings);
            }
        }

        private void IntegratorForm(IPlaygapIntegrator integrator)
        {
            GUILayout.Space(12);
            if (GUILayout.Button("Integrate"))
            {
                integrator.Integrate();
            }
            GUILayout.Space(36);
            GUILayout.Label("Automatically remove all the changes from Pressing 'Integrate' above", GUIStyleExtensions.SubTitle);
            if (GUILayout.Button("Remove Integration"))
            {
                integrator.Reset();
            }
        }

        private void UnsupportedIntegratorForm()
        {
            GUILayout.Space(16);
            GUILayout.Label("This integration will be supported soon.", GUIStyleExtensions.Title);
            if (GUILayout.Button(ManualIntegration, GUIStyleExtensions.HyperLink()))
            {
                Application.OpenURL(Constants.manualIntegration);
            }
        }

        private void ManualIntegratorForm()
        {
            GUILayout.Space(16);
            if (GUILayout.Button(ManualIntegration, GUIStyleExtensions.HyperLink()))
            {
                Application.OpenURL(Constants.manualIntegration);
            }
        }

        private void TextField(string title, ref string value)
        {
            GUILayout.Space(2);
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label(title, new GUIStyle(GUI.skin.label)
            {
                fixedWidth = 100,
                fontStyle = FontStyle.Bold
            });
            value = EditorGUILayout.TextField("", value);
            EditorGUILayout.EndHorizontal();
            GUILayout.Space(2);
        }

        private void DetailsBody(Action body)
        {
            GUILayout.BeginHorizontal();
            GUILayout.Space(10);
            GUILayout.BeginVertical();
            body();
            GUILayout.EndVertical();
            GUILayout.Space(10);
            GUILayout.EndHorizontal();
        }

        private void SavePlaygapSettings(AdIntegrationManagerSettings settings)
        {
            Directory.CreateDirectory(SettingsFolder);

            EditorUtility.SetDirty(settings);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
    }
}

public static class GUIStyleExtensions
{
    public static GUIStyle Title => new(GUI.skin.label)
    {
        fontSize = 16,
        fontStyle = FontStyle.Bold,
        wordWrap = true
    };

    public static GUIStyle SubTitle => new(GUI.skin.label)
    {
        fontStyle = FontStyle.Bold,
        wordWrap = true
    };

    public static GUIStyle ListItem => new(GUI.skin.label)
    {
        padding = {
            left = 12
        }
    };

    public static GUIStyle HyperLink()
    {
        var style = SubTitle;
        style.richText = true;
        return style;
    }

    public static GUIStyle MultilineCheckbox => new(GUI.skin.toggle)
    {
        wordWrap = true
    };
}