namespace AdIntegrationManager
{
    public class Constants
    {
        public const string manualIntegration = "https://playgap.io/dash/docs/integration/unity/manual";
        public const string apps = "https://playgap.io/dash/monetisation/applications";
        public const string contactUs = "mailto:<EMAIL>";
        public const string release = "https://playgap.b-cdn.net/sdk/release-info.json";
        public const string pluginName = "Playgap";
    }

    public class AdType
    {
        public const string Interstitial = "interstitial";
        public const string Rewarded = "rewarded";
        public const string Banner = "banner";
    }

    public static class StringExtension
    {
        public static string AdFormat(this string type)
        {
            return type == AdType.Interstitial ? "INTER" : type.ToUpper();
        }
    }

    public class Events
    {
        public class Playgap
        {
            public const string MediationLoadFailed = "mediation_load_failed";
            public const string MediationLoadAttempt = "mediation_load_attempt";
            public const string MediationShowAttempt = "mediation_show_attempt";
            public const string MediationHideAttempt = "mediation_hide_attempt";
            public const string MediationDestroyAttempt = "mediation_destroy_attempt";
            public const string MediationAdRevenue = "mediation_ad_revenue";
        }

        public class Max
        {
            public const string OnInterstitialLoadedEvent = "OnInterstitialLoadedEvent";
            public const string OnRewardedAdLoadedEvent = "OnRewardedAdLoadedEvent";
            public const string OnBannerAdLoadedEvent = "OnBannerAdLoadedEvent";
            public const string OnSdkInitializedEvent = "OnSdkInitializedEvent";
            public const string OnInterstitialDisplayedEvent = "OnInterstitialDisplayedEvent";
            public const string OnRewardedAdDisplayedEvent = "OnRewardedAdDisplayedEvent";
            public const string OnRewardedAdReceivedRewardEvent = "OnRewardedAdReceivedRewardEvent";
            public const string OnRewardedAdFailedToDisplayEvent = "OnRewardedAdFailedToDisplayEvent";
            public const string OnInterstitialAdFailedToDisplayEvent = "OnInterstitialAdFailedToDisplayEvent";
            public const string OnRewardedAdHiddenEvent = "OnRewardedAdHiddenEvent";
            public const string OnInterstitialHiddenEvent = "OnInterstitialHiddenEvent";
        }
    }
}