using System;
using Playgap;

namespace AdIntegrationManager
{
    internal class MaxSdkClaimRewardsAdapter
    {
        private Action OnDismiss;

        internal void Setup(Action dismiss)
        {
            OnDismiss = dismiss;
        }

        internal bool ShowClaimRewardIfNeeded(bool hasConnection)
        {
            if (!hasConnection)
            {
                return false;
            }

            Utilities.Log("Attempt to show claim rewards screeen");

            var rewards = PlaygapAds.CheckRewards();

            if (rewards.unclaimed.Count > 0)
            {
                PlaygapAds.ClaimRewards();
                return true;
            }

            return false;
        }

        internal void OnRewardScreenClosed()
        {
            Utilities.Log("Reward screen closed");

            OnDismiss();
        }

        internal void OnRewardScreenFailed(string error)
        {
            Utilities.Error("Reward screen failed with error: " + error);

            OnDismiss();
        }

        internal void OnUserClaimedRewards(string[] _)
        {
            Utilities.Log("Rewarding user, please make sure to subscribe to PlaygapAds.OnUserClaimedRewards to handle it.");
        }
    }
}