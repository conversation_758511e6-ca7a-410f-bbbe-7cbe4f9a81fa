using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(CanvasGroup))]
public class CanvasGroupController : MonoBehaviour
{
    protected CanvasGroup mCanvasGroup;
    protected RectTransform mRectTransform;
    bool isInit = false;
    // Start is called before the first frame update

    private void OnEnable()
    {
        if (mCanvasGroup == null)
        {
            mCanvasGroup = GetComponent<CanvasGroup>();
            mRectTransform = GetComponent<RectTransform>();
            isInit = true;
        }
    }

    public void ToggleOnOff(bool isON, bool affectGameObj = false)
    {
        if (!isInit)
            return;

        mCanvasGroup.alpha = isON ? 1 : 0;
        mCanvasGroup.interactable = isON;
        mCanvasGroup.blocksRaycasts = isON;

        if (affectGameObj)
        {
            mCanvasGroup.gameObject.SetActive(isON);
        }
    }

    public void ToggleFadeOnOff(bool isON, float timer = 0.5f, bool affectGameObj = false)
    {
        if (!isInit)
            return;

        mCanvasGroup.interactable = isON;
        mCanvasGroup.blocksRaycasts = isON;

        mCanvasGroup.DOKill();

        if (affectGameObj)
        {
            mCanvasGroup.DOFade(isON ? 1f : 0f, timer).OnComplete(() =>
            {
                mCanvasGroup.gameObject.SetActive(isON);
            }).Play();
        }
        else
        {
            mCanvasGroup.DOFade(isON ? 1f : 0f, timer).Play();
        }
    }

    public bool isON ()
    {
        if (!isInit) return false;
        return mCanvasGroup.interactable;
    }

    public float CanvasWidth()
    {
        if (!isInit)
        {
            return 0f;
        }

        return mRectTransform.rect.width;
    }

    public bool isVisible()
    {
        if (!isInit) return false;
        return mCanvasGroup.alpha > 0f;
    }
}
