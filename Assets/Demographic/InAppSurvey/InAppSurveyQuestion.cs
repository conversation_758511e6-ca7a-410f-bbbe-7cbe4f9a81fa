using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DG.Tweening;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;
using Image = UnityEngine.UI.Image;
using Random = UnityEngine.Random;

public class InAppSurveyQuestion : MonoBehaviour {
    [SerializeField] private Button btnNext;
    [SerializeField] private Button btnNotSay;

    public Text       txtQuestion;
    public GameObject goAnswer;
    public Transform  pivotAnswers_2Column;
    public Transform  pivotAnswers_1Column;
    public Text       txtOptional;

    InAppSurveyData surveyData;

    public                   Image      bgNextButton;
    [SerializeField] private GameObject objNextButton;
    public                   Text       textNextButton;

    public Action<InAppSurveyAnswerResult> collectAnswerCallback;
    public float                           timeToNext         = 0.1f;
    List<string>                           listSelectedAnswer = new List<string>();
    string                                 finalAnswer        = "";

    List<InAppSurveyAnswer> listAnswerObject = new List<InAppSurveyAnswer>();

    InAppSurveyGroupQuestion groupQuestionsData;
    int                      surveyId;
    int                      questionId;

    [Header("Group 2 Questions")] public GameObject prefabMiniQuestion;
    public                               Transform  pivotMiniQuestion;
    public                               GameObject goGroup2Question;

    [Header("Control Height")] public ScrollRect    scrollRect;
    public                            RectTransform gridComplex;
    public                            RectTransform gridSimple;

    [Header("Not Say")] public Text notSayTxt;

    [Header("For Anim")] public float                 delayTime = 0.05f;
    public                      List<RectTransform>   moveItems;
    public                      CanvasGroupController canvasCtrl;

    bool isMiltipleQuestionMode = false;

    public GameObject guidelineText;

    Dictionary<int, string> answerForMultipleQuestion = new Dictionary<int, string>();

    //private Coroutine IEResize;
    private bool _isNext = false;
    private bool _initDone;

    private void Awake() {
        btnNext.onClick.AddListener(PressNext);
        btnNotSay.onClick.AddListener(PressNext);
        LocalizationManager.instance.UpdateFont(txtQuestion);
        LocalizationManager.instance.UpdateFont(notSayTxt);
        LocalizationManager.instance.UpdateFont(txtOptional);
        LocalizationManager.instance.UpdateFont(textNextButton);
    }

    private IEnumerator Start() {
        var scale = InAppSurvey.GetScaleOptimizedForPortrait();
        Vector3 newScale = new Vector3(scale, scale);
        foreach (Transform child in gameObject.transform) {
            child.localScale = newScale;
            var rect = child.GetComponent<RectTransform>();
            rect.anchoredPosition = new Vector2(rect.anchoredPosition.x, rect.anchoredPosition.y * scale);
            var ctrl = rect.GetComponent<EffectButtonCtrl>();
            if (ctrl)
                ctrl.Reset();
        }

        if (!string.IsNullOrEmpty(groupQuestionsData.additionalText) ||
            !string.IsNullOrEmpty(groupQuestionsData.localizedIdAddition)) {
            guidelineText.SetActive(true);
            if (!string.IsNullOrEmpty(groupQuestionsData.localizedIdAddition)) {
                string locStr = LocalizationManager.instance.GetLocalizedValue(groupQuestionsData.localizedIdAddition);

                groupQuestionsData.additionalText = !locStr.Equals(groupQuestionsData.localizedIdAddition)
                    ? locStr
                    : groupQuestionsData.additionalText;
            }

            txtOptional.text = groupQuestionsData.additionalText;
        } else {
            guidelineText.SetActive(false);
        }

        if (groupQuestionsData.listQuestions.Count == 1) {
            isMiltipleQuestionMode = false;

            goGroup2Question.SetActive(false);
            surveyData = groupQuestionsData.listQuestions[0];

            if (surveyData != null) {
                var answers = surveyData.answers;
                for (int i = answers.Count - 1; i >= 0; i--) {
                    // prefer not to say button kind
                    if (answers[i].answerId == 0) {
                        groupQuestionsData.havePreferNotToSay = true;
                        groupQuestionsData.needContinueButton = false;
                        groupQuestionsData.hideBeforeAnswer = false;
                        notSayTxt.text = LocalizationManager.instance.GetLocalizedValue(answers[i].localizedId);
                        notSayTxt.rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,
                            notSayTxt.preferredWidth);
                        //notSayTxt.text = answers[i].answerContent;
                        answers.RemoveAt(i);
                        break;
                    }
                }

                Transform pivot = surveyData.isOneColumn ? pivotAnswers_1Column : pivotAnswers_2Column;
                //Debug.LogWarning("[InAppSurvey] ==== Answer count = " + surveyData.answers.Count);

                if (surveyData.answers.Count > 0) {
                    pivotAnswers_1Column.gameObject.SetActive(surveyData.isOneColumn);
                    pivotAnswers_2Column.gameObject.SetActive(surveyData.isOneColumn == false);
                }

                textNextButton.text = groupQuestionsData.nextButtonText;
                bgNextButton.SetAlpha(groupQuestionsData.enableNextButtonBG ? 255 : 1);
                objNextButton.SetActive(groupQuestionsData.needContinueButton);

                notSayTxt.transform.parent.gameObject.SetActive(groupQuestionsData.havePreferNotToSay);

                if (groupQuestionsData.hideBeforeAnswer)
                    objNextButton.SetActive(false);

                shuffleAnswers();

                int insertIdx = 1; // position after the title
                foreach (var answer in surveyData.answers) {
                    GameObject go = Instantiate(goAnswer, pivot);
                    var tmpAnswer = go.GetComponent<InAppSurveyAnswer>();
                    if (tmpAnswer) {
                        moveItems.Insert(insertIdx++, tmpAnswer.GetComponent<RectTransform>());
                        tmpAnswer.SetUp(answer, (selected) => {
                            if (groupQuestionsData.needContinueButton == false || answer.haveSkipFunction) {
                                if (selected) {
                                    listSelectedAnswer.Clear();
                                    listSelectedAnswer.Add(answer.answerId.ToString());
                                } else
                                    listSelectedAnswer.Add(groupQuestionsData.nextButtonText);

                                StartCoroutine(IEStartNext());
                            } else {
                                if (answer.styleIndex == 0) {
                                    // select new -> deselect others
                                    foreach (var item in listAnswerObject) {
                                        if (item.GetAnswerID() != answer.answerId)
                                            item.SetSelectedState(false);
                                        else
                                            item.SetSelectedState(true);
                                    }

                                    listSelectedAnswer.Clear();
                                    listSelectedAnswer.Add(answer.answerId.ToString());

                                    objNextButton.SetActive(groupQuestionsData.needContinueButton);
                                    guidelineText.SetActive(listSelectedAnswer.Count == 0);
                                } else {
                                    // genres style
                                    if (selected)
                                        listSelectedAnswer.Add(answer.answerId.ToString());
                                    else
                                        listSelectedAnswer.Remove(answer.answerId.ToString());

                                    if (groupQuestionsData.hideBeforeAnswer) {
                                        objNextButton.SetActive(listSelectedAnswer.Count > 0);
                                        guidelineText.SetActive(listSelectedAnswer.Count == 0);
                                    }
                                }
                            }
                        }, groupQuestionsData.needContinueButton);
                        tmpAnswer.gameObject.SetActive(true);
                        listAnswerObject.Add(tmpAnswer);
                    }

                    yield return null;
                }
            }
        } else {
            isMiltipleQuestionMode = true;

            goGroup2Question.SetActive(true);
            pivotAnswers_1Column.gameObject.SetActive(false);
            pivotAnswers_2Column.gameObject.SetActive(false);

            foreach (var item in groupQuestionsData.listQuestions) {
                GameObject go = Instantiate(prefabMiniQuestion, pivotMiniQuestion);
                var miniQuestion = go.GetComponent<InAppMiniQuestion>();
                if (miniQuestion != null) {
                    miniQuestion.Setup(item, (quesID, str_answer) => {
                        if (answerForMultipleQuestion.ContainsKey(quesID) == false)
                            answerForMultipleQuestion.Add(quesID, str_answer);
                        else
                            answerForMultipleQuestion[quesID] = str_answer;

                        if (answerForMultipleQuestion.Count == groupQuestionsData.listQuestions.Count)
                            StartCoroutine(IEStartNext());
                    });
                }

                yield return null;
            }

            txtQuestion.transform.parent.gameObject.SetActive(false);
            textNextButton.text = groupQuestionsData.nextButtonText;
            bgNextButton.SetAlpha(groupQuestionsData.enableNextButtonBG ? 1f : 1 / 255f);
        }

        yield return YieldPool.GetWaitForEndOfFrame();

        _initDone = true;
    }

    void shuffleAnswers() {
        Dictionary<int, InAppSurveyAnswerData> newOrderAnswer = new Dictionary<int, InAppSurveyAnswerData>();
        List<InAppSurveyAnswerData> listShuffleData = new List<InAppSurveyAnswerData>();

        for (int i = 0; i < surveyData.answers.Count; i++) {
            if (surveyData.answers[i].allowShuffleOrder == false) {
                newOrderAnswer.Add(i, surveyData.answers[i]);
            } else {
                listShuffleData.Add(surveyData.answers[i]);
            }
        }

        List<InAppSurveyAnswerData> tmpRandomizeList = new List<InAppSurveyAnswerData>();
        if (listShuffleData.Count > 0) {
            while (listShuffleData.Count > 0) {
                int random = Random.Range(0, listShuffleData.Count - 1);
                tmpRandomizeList.Add(listShuffleData[random]);
                listShuffleData.RemoveAt(random);
            }
        }

        int newCounter = 0;
        for (int i = 0; i < surveyData.answers.Count; i++) {
            if (newOrderAnswer.ContainsKey(i) == false) {
                if (newCounter < tmpRandomizeList.Count) {
                    newOrderAnswer.Add(i, tmpRandomizeList[newCounter]);
                    newCounter++;
                }
            }
        }

        surveyData.answers.Clear();
        int count = 0;
        while (surveyData.answers.Count != newOrderAnswer.Count) {
            foreach (var pair in newOrderAnswer) {
                if (pair.Key == count) {
                    surveyData.answers.Add(pair.Value);
                    count++;
                    break;
                }
            }
        }
    }

    public void SetUp(int surveyId, InAppSurveyGroupQuestion group, Action<InAppSurveyAnswerResult> action) {
        this.surveyId = surveyId;
        _isNext = false;
        groupQuestionsData = group;
        // override question localize
        for (int i = 0; i < groupQuestionsData.listQuestions.Count; i++) {
            InAppSurveyData data = groupQuestionsData.listQuestions[i];
            this.questionId = data.questionId;
            if (!string.IsNullOrEmpty(data.localizedId)) {
                string locStr = LocalizationManager.instance.GetLocalizedValue(data.localizedId);
                txtQuestion.text = !locStr.Equals(data.localizedId) ? locStr : data.question;
            }
        }

        groupQuestionsData.listQuestions = groupQuestionsData.listQuestions.OrderByDescending(x => x.priority).ToList();
        collectAnswerCallback = action;
    }

    private void PressNext() {
        if (_isNext)
            return;

        _isNext = true;
        finalAnswer = string.Empty;
        for (int i = 0, n = listSelectedAnswer.Count; i < n; i++) {
            finalAnswer += listSelectedAnswer[i];
            if (i != n - 1) {
                finalAnswer += ";";
            }
        }

        InAppSurvey.instance.PlaySoundClick();

        // 0 - prefer not to say
        if (string.IsNullOrEmpty(finalAnswer) && groupQuestionsData.havePreferNotToSay) {
            finalAnswer = "0";
        }

        List<string> localizedIdsAnswer = GetLocalizedIds(finalAnswer);

        string questionType = string.Empty;
        if (surveyData != null) {
            questionType = surveyData.questionType;
            if (isMiltipleQuestionMode) {
                foreach (var question in groupQuestionsData.listQuestions) {
                    finalAnswer = string.Empty;
                    if (answerForMultipleQuestion.ContainsKey(question.questionId))
                        finalAnswer = answerForMultipleQuestion[question.questionId];
                    else
                        finalAnswer = groupQuestionsData.nextButtonText;

                    collectAnswerCallback?.Invoke(new InAppSurveyAnswerResult(groupQuestionsData.groupId,
                        surveyData.questionId, question.questionType, finalAnswer, localizedIdsAnswer));
                }
            } else {
                collectAnswerCallback?.Invoke(new InAppSurveyAnswerResult(groupQuestionsData.groupId,
                    surveyData.questionId, questionType, finalAnswer, localizedIdsAnswer));
            }

            InAppSurvey.instance.PlaySoundSlide();
        }
    }

    // private void OnEnable() {
    //     // might not user resize function, because of strong size correct from PO request
    //     if (IEResize != null) {
    //         StopCoroutine(IEResize);
    //         IEResize = null;
    //     }
    //
    //     IEResize = StartCoroutine(IEResizeQuestion());
    // }

    //private IEnumerator IEResizeQuestion() {
    //yield return new WaitForEndOfFrame();
    // var questionH = GetComponent<RectTransform>().GetHeight();
    // float tempH = 0f;
    // if (gridSimple.gameObject.activeInHierarchy)
    // {
    //     tempH = gridSimple.GetHeight();
    // }
    // else
    // {
    //     tempH = gridComplex.GetHeight();
    // }
    //
    // scrollRect.enabled = tempH >= questionH * 0.7125f;
    // scrollRect.GetComponent<RectTransform>().SetHeight(scrollRect.enabled ? questionH * 0.7125f : tempH);
    // scrollRect.content.SetHeight(tempH);
    //}

    private IEnumerator IEStartNext() {
        yield return new WaitForSeconds(timeToNext);

        PressNext();
    }

    private async Task WaitForInitAsync() {
        int count = 0;
        while (!_initDone && count < 100) {
            count++;
            await Task.Delay(10); // Avoid busy waiting
        }
    }

    public async void MoveIn(float time, Ease ease) {
        try {
            btnNotSay.interactable = false;
            canvasCtrl.ToggleOnOff(false);
            await Task.Delay(185);
            await WaitForInitAsync();
            canvasCtrl?.ToggleOnOff(true);

            if (Configuration.instance.isTutorial) {
                InAppSurveyEventTracking.LogFNDemographicSurveyQuestion(surveyId, questionId,
                    transform ? transform.GetSiblingIndex() : 0);

                string questionType = surveyData.questionType.ToUpper();
                switch (questionType) {
                    case InAppSurvey.QuestionTypeGenre:
                        InAppSurveyEventTracking.FN_Demographic_Genre_Question_Show();
                        break;

                    case InAppSurvey.QuestionTypeGender:
                        InAppSurveyEventTracking.FN_Demographic_Gender_Question_Show();
                        break;

                    case InAppSurvey.QuestionTypeAge:
                        InAppSurveyEventTracking.FN_Demographic_Age_Question_Show();
                        break;
                }

            } else {
                InAppSurveyEventTracking.LogDemographicSurveyQuestion(surveyId, questionId,
                    transform.GetSiblingIndex());
            }

            if (moveItems.IsNullOrEmpty())
                return;

            for (int i = 0; i < moveItems.Count; i++) {
                var item = moveItems[i];
                if (item && item.gameObject.activeInHierarchy) {
                    var anchoredPos = item.anchoredPosition;
                    var oldx = anchoredPos.x;
                    anchoredPos.x += Screen.width;
                    item.anchoredPosition = anchoredPos;
                    item.DOAnchorPosX(oldx, time).SetEase(ease).SetDelay(i * delayTime);
                }
            }

            DOVirtual.DelayedCall(moveItems.Count * delayTime, () => { btnNotSay.interactable = true; });

            // check first question
            if (moveItems is {Count: > 1} && moveItems[1].gameObject.name.Contains("Answer")) {
                await Task.Delay((int) ((moveItems.Count * delayTime + time + 0.05f) * 1000));
                if (surveyData != null && moveItems != null && moveItems[1].gameObject) {
                    if (surveyData.isOneColumn) {
                        var verGroup = moveItems[1].GetComponentInParent<VerticalLayoutGroup>();
                        if (verGroup)
                            verGroup.enabled = true;
                    } else {
                        var grid = moveItems[1].GetComponentInParent<GridLayout>();
                        if (grid)
                            grid.enabled = true;
                    }
                }
            }
        } catch (Exception e) {
            btnNotSay.interactable = true;
            CustomException.Fire("[MoveIn]", e.Message);
        }
    }

    public async void MoveOut(float time, Ease ease) {
        try {
            await Task.Delay(185);
            for (int i = 0; i < moveItems.Count; i++) {
                var item = moveItems[i];
                if (item && item.gameObject && item.gameObject.activeInHierarchy) {
                    item.DOAnchorPosX(item.anchoredPosition.x - Screen.width, time).SetEase(ease)
                        .SetDelay(i * delayTime);
                }
            }

            await Task.Delay((int) ((moveItems.Count * delayTime + time + 0.05f) * 1000));
            if (canvasCtrl) {
                canvasCtrl.gameObject.SetActive(false);
            }
        } catch (Exception e) {
            CustomException.Fire("[MoveOut]", e.Message);
        }
    }

    private List<string> GetLocalizedIds(string answers) {
        List<string> ans = new List<string>();
        string[] strings = answers.Split(';');
        List<InAppSurveyAnswerData> inAppSurveyAnswerDatas = surveyData.answers;

        bool isKeepOrder = RemoteConfigBase.instance.TutorialSong_Rule != 0 ||
                           RemoteConfigBase.instance.onboarding_demographic_improve == 1;
        if (isKeepOrder) { // giữ đúng thứ tự click của user
            foreach (var str in strings) {
                foreach (InAppSurveyAnswerData answerData in inAppSurveyAnswerDatas) {
                    if (str.Equals(answerData.answerId.ToString())) {
                        ans.Add(answerData.localizedId);
                        break;
                    }
                }
            }
        } else { // reorder theo thứ tự xuất hiện của answers
            foreach (InAppSurveyAnswerData answerData in inAppSurveyAnswerDatas) {
                if (strings.Contains(answerData.answerId.ToString())) {
                    ans.Add(answerData.localizedId);
                }
            }
        }

        return ans;
    }
}