using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class InAppSurveyEventTracking {
    //events
    private const string EVENT_DEMOPRAGPHIC_SURVEY             = "demographic_survey";
    private const string FN_Demographic_Intro                  = "FN_Demographic_Intro";
    private const string EVENT_FN_DEMOPRAGPHIC_SURVEY_QUESTION = "FN_demographic_survey_question";
    private const string EVENT_FN_DEMOPRAGPHIC_SURVEY_END      = "FN_demographic_survey_end";
    private const string EVENT_DEMOPRAGPHIC_INTRO              = "demographic_intro";
    private const string EVENT_DEMOPRAGPHIC_SURVEY_QUESTION    = "demographic_survey_question";
    private const string EVENT_DEMOPRAGPHIC_SURVEY_END         = "demographic_survey_end";
    private const string EVENT_DEMOPRAGPHIC_SURVEY_THANKS      = "demographic_survey_thanks";

    //params
    private const string PARAM_SURVEY_ID      = "survey_id";
    private const string PARAM_QUESTION       = "q";
    private const string PARAM_QUESTION_ID    = "question_id";
    private const string PARAM_QUESTION_ORDER = "question_order";

    public static void LogDemographicSurvey(int surveyId, List<InAppSurveyAnswerResult> answers) {
        string eventName = EVENT_DEMOPRAGPHIC_SURVEY;

        Dictionary<string, object> param = new Dictionary<string, object> {
            { PARAM_SURVEY_ID, surveyId }
            //
        };

        for (int i = 0, n = answers.Count; i < n; i++) {
            param.TryAdd(PARAM_QUESTION + answers[i].questionId, answers[i].answer);
        }

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void LogFNDemographicIntro() {
        string eventName = FN_Demographic_Intro;

        Dictionary<string, object> param = new Dictionary<string, object>();
        AnalyticHelper.UpdateParams(eventName,  param, TRACK_PARAM.accumulated_count, TRACK_NAME.loading_time);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void LogFNDemographicSurveyQuestion(int surveyId, int questionId, int questionOrder) {
        string eventName = EVENT_FN_DEMOPRAGPHIC_SURVEY_QUESTION;

        Dictionary<string, object> param = new Dictionary<string, object> {
            { PARAM_SURVEY_ID, surveyId },
            { PARAM_QUESTION_ID, questionId },
            { PARAM_QUESTION_ORDER, questionOrder }
        };
        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void LogFNDemographicSurveyEnd(int surveyId) {
        string eventName = EVENT_FN_DEMOPRAGPHIC_SURVEY_END;

        Dictionary<string, object> param = new Dictionary<string, object> {
            { PARAM_SURVEY_ID, surveyId },
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void LogDemographicIntro() {
        string eventName = EVENT_DEMOPRAGPHIC_INTRO;

        Dictionary<string, object> param = new Dictionary<string, object> { };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void LogDemographicSurveyQuestion(int surveyId, int questionId, int questionOrder) {
        string eventName = EVENT_DEMOPRAGPHIC_SURVEY_QUESTION;

        Dictionary<string, object> param = new Dictionary<string, object> {
            { PARAM_SURVEY_ID, surveyId },
            { PARAM_QUESTION_ID, questionId },
            { PARAM_QUESTION_ORDER, questionOrder },
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void LogDemographicSurveyEnd(int surveyId) {
        string eventName = EVENT_DEMOPRAGPHIC_SURVEY_END;

        Dictionary<string, object> param = new Dictionary<string, object> {
            { PARAM_SURVEY_ID, surveyId },
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void LogDemographicSurveyThanks() {
        string eventName = EVENT_DEMOPRAGPHIC_SURVEY_THANKS;

        Dictionary<string, object> param = new Dictionary<string, object> { };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void FN_Demographic_Genre_Question_Show() {
        string eventName = TRACK_NAME.FN_Demographic_Genre_Question_Show;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_NAME.loading_time, Time.realtimeSinceStartup - AnalyticHelper.timeGameOpen }
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void FN_Demographic_Genre_Question_Finish(string selectGenre) {
        string eventName = TRACK_NAME.FN_Demographic_Genre_Question_Finish;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_NAME.loading_time, Time.realtimeSinceStartup - AnalyticHelper.timeGameOpen },
            { TRACK_NAME.genre_answer, selectGenre }
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void FN_Demographic_Gender_Question_Show() {
        string eventName = TRACK_NAME.FN_Demographic_Gender_Question_Show;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_NAME.loading_time, Time.realtimeSinceStartup - AnalyticHelper.timeGameOpen }
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void FN_Demographic_Gender_Question_Finish(string selectGenre) {
        string eventName = TRACK_NAME.FN_Demographic_Gender_Question_Finish;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_NAME.loading_time, Time.realtimeSinceStartup - AnalyticHelper.timeGameOpen },
            { TRACK_NAME.gender_answer, selectGenre }
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void FN_Demographic_Age_Question_Show() {
        string eventName = TRACK_NAME.FN_Demographic_Age_Question_Show;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_NAME.loading_time, Time.realtimeSinceStartup - AnalyticHelper.timeGameOpen }
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    public static void FN_Demographic_Age_Question_Finish(string age) {
        string eventName = TRACK_NAME.FN_Demographic_Age_Question_Finish;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_NAME.loading_time, Time.realtimeSinceStartup - AnalyticHelper.timeGameOpen },
            { TRACK_NAME.age_answer, age }
        };

        AnalyticHelper.UpdateParamsAccumulatedCount( param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }
}