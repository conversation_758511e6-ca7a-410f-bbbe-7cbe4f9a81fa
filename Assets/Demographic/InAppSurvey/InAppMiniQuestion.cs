using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System;

public class InAppMiniQuestion : MonoBehaviour
{

    public Text txtQuestionContent;
    public GameObject goPrefabAnswer;

    public Transform pivotGroup1Column;
    public Transform pivotGroup2Column;

    public LayoutElement layoutElementGroup1Column;
    public LayoutElement layoutElementGroup2Column;
    float fixedHeight1Item = 150;

    InAppSurveyData surveyData;

    List<InAppSurveyAnswer> listAnswerObject = new List<InAppSurveyAnswer>();

    Action<int, string> chooseAnswerCallback;

    // Start is called before the first frame update
    void Start()
    {
        if(surveyData != null)
        {
            Transform pivot = surveyData.isOneColumn ? pivotGroup1Column : pivotGroup2Column;
            pivotGroup1Column.gameObject.SetActive(surveyData.isOneColumn);
            pivotGroup2Column.gameObject.SetActive(surveyData.isOneColumn == false);

            float totalHeight = fixedHeight1Item * surveyData.answers.Count;
            layoutElementGroup1Column.preferredHeight = totalHeight;
            layoutElementGroup2Column.preferredHeight = totalHeight;

            txtQuestionContent.text = surveyData.question;
            
            foreach(var answer in surveyData.answers)
            {
                GameObject go = Instantiate(goPrefabAnswer, pivot);
                var answerObject = go.GetComponent<InAppSurveyAnswer>();
                if(answerObject != null)
                {
                    answerObject.SetUp(answer, (selected) =>
                    {
                        processOutput(answer.answerId.ToString());
                        if (answer.styleIndex == 0)
                        {
                            // select new -> deselect others
                            foreach (var item in listAnswerObject)
                            {
                                if (item.GetAnswerID() != answer.answerId)
                                    item.SetSelectedState(false);
                            }
                        }
                    });
                }
                

                listAnswerObject.Add(answerObject);
            }
        }
    }

    public void Setup(InAppSurveyData data, Action<int, string> callback)
    {
        surveyData = data;
        chooseAnswerCallback = callback;
    }

    void processOutput(string str_answer)
    {
        if (chooseAnswerCallback != null) chooseAnswerCallback.Invoke(surveyData.questionId, str_answer);
    }
}

