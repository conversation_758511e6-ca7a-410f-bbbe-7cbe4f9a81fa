using System.Collections;
using Adverty5.AdPlacements;
using UnityEngine;

namespace Adverty5.Examples
{
    [RequireComponent(typeof(AdPlacement))]
    public class AdReceivingValidation : MonoBehaviour
    {
        public uint AdReceivingValidationDelay = 4;
        private AdPlacement targetPlacement;

        protected void Start()
        {
            targetPlacement = GetComponent<AdPlacement>();

            targetPlacement.AdPlacementRegisteredEvent += OnAdPlacementRegistered;
            targetPlacement.AdPlacementFailedToRegisterEvent += OnAdPlacementRegistrationFailed;
            targetPlacement.AdPlacementActivatedEvent += OnAdPlacementActivated;
        }

        private void OnAdPlacementActivated(AdPlacement placement)
        {
            Debug.LogFormat("Handling Adverty's placement {0} after receiving an ad.", placement.name);
        }

        private void OnAdPlacementRegistrationFailed(AdPlacement placement)
        {
            Debug.LogWarningFormat("Handling Adverty's placement {0} after failed registration.", placement.name);
        }

        private void OnAdPlacementRegistered(AdPlacement placement)
        {
            Debug.LogWarningFormat("Handling Adverty's placement {0} after successful registration.", placement.name);
            StartCoroutine(ValidateAdReceiving());
        }

        private IEnumerator ValidateAdReceiving()
        {
            yield return new WaitForSeconds(AdReceivingValidationDelay);

            if (!targetPlacement.IsActive)
            {
                Debug.LogWarningFormat("Adverty currently have no ad for AdPlacement: {0}", targetPlacement.name);
            }

            StartCoroutine(ValidateAdReceiving());
        }
    }
}
