using System.Runtime.InteropServices;
using System;
using Adverty5.Checks;
using UnityEngine;

namespace Adverty5.AdPlacements
{
    [RequireComponent(typeof(SpriteRenderer))]
    public class SpriteAdPlacement : AdPlacement
    {
        private Vector3[] placementVertices;
        private GCHandle verticesHandle;
        private GCHandle modelMatrixHandle;
        protected float[] modelMatrixArray;

        private IntPtr vertices;
        private int vertexCount;
        private IntPtr modelMatrix;

        internal override Material material
        {
            get {
                return GetComponent<SpriteRenderer>().material;
            }
        }

        internal override Texture2D texture
        {
            get {
                var spriteRenderer = GetComponent<SpriteRenderer>();
                if (spriteRenderer != null && spriteRenderer.sprite != null)
                {
                    return spriteRenderer.sprite.texture;
                }

                return null;
            }
        }

        internal override Vector2Int placementSize
        {
            get {
                return new Vector2Int((int)(texture.width * transform.lossyScale.x),
                                      (int)(texture.height* transform.lossyScale.y));
            }
        }

        protected override void Awake()
        {
            base.Awake();
            placementVertices = GetPlacementVerticies();
            vertexCount = placementVertices.Length;
            verticesHandle = GCHandle.Alloc(placementVertices, GCHandleType.Pinned);
            vertices = verticesHandle.AddrOfPinnedObject();
            modelMatrixArray = new float[16];
            modelMatrixHandle = GCHandle.Alloc(modelMatrixArray, GCHandleType.Pinned);
            modelMatrix = modelMatrixHandle.AddrOfPinnedObject();
        }

        protected override void ApplyPlacementTexture()
        {
            SpriteRenderer renderer = GetComponent<SpriteRenderer>();

            Sprite oldSprite = renderer.sprite;
            Sprite sprite =
                Sprite.Create(adPlacementTexture, new Rect(0, 0, adPlacementTexture.width, adPlacementTexture.height),
                              new Vector2(0.5f, 0.5f));

            // Apply new sprite and fix sprite scale to avoid sprite "jumping". USDK returns texture with size equals to
            // power of 2 and it affects rendered size of the sprite

            Vector2 oldSpriteSize = renderer.sprite.bounds.size;
            Vector3 oldLossyScale = transform.lossyScale;

            renderer.sprite = sprite;

            Vector2 newSpriteSize = renderer.sprite.bounds.size;
            Vector3 newLossyScale = transform.lossyScale;

            Vector3 newLocalScale = transform.localScale;

            newLocalScale.x *= (oldSpriteSize.x / newSpriteSize.x) * (oldLossyScale.x / newLossyScale.x);
            newLocalScale.y *= (oldSpriteSize.y / newSpriteSize.y) * (oldLossyScale.y / newLossyScale.y);

            transform.localScale = newLocalScale;
        }

        private void UpdateMatrix()
        {
            AdvertyViewabilityCheck.MatrixToFloatArray(transform.localToWorldMatrix, modelMatrixArray);
        }

        protected override void ViewabilityCheck()
        {
            UpdateMatrix();
            AdvertyViewabilityCheck.ViewabilityCheckMatrices(Id, modelMatrix, vertices, vertexCount);
        }

        private Vector3[] GetPlacementVerticies()
        {
            Sprite sprite = GetComponent<SpriteRenderer>().sprite;
            Vector3[] vertices = new Vector3[sprite.vertices.Length];
            for (int i = 0; i < vertices.Length; i++)
            {
                vertices[i] = sprite.vertices[i];
            }

            return vertices;
        }
    }
}
