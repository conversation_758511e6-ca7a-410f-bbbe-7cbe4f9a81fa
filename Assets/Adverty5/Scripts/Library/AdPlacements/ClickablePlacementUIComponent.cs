using UnityEngine;
using UnityEngine.EventSystems;

namespace Adverty5.AdPlacements
{
    [RequireComponent(typeof(UIAdPlacement))]
    public class ClickablePlacementUIComponent : MonoBeh<PERSON>our, IPointerClickHandler
    {
        private AdPlacement adPlacement;

        protected void Start()
        {
            adPlacement = GetComponent<AdPlacement>();
        }

        public void OnPointerClick(PointerEventData eventData)
        {
            Adverty.ClickOnAdPlacement(adPlacement.Id);
        }
    }
}
