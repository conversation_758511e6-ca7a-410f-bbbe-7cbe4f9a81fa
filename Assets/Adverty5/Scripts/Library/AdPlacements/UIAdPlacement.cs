using System;
using System.Runtime.InteropServices;
using Adverty5.Checks;
using UnityEngine;
using UnityEngine.UI;

namespace Adverty5.AdPlacements
{
    [RequireComponent(typeof(RawImage))]
    public class UIAdPlacement : AdPlacement
    {
        private int[] cornerPointsOnScreen;
        private GCHandle cornerPointsHandle;

        private IntPtr cornerPoints;
        private int cornerPoinsCount;

        private RawImage rawImage;
        private RectTransform rectTransform;

        protected override void Awake()
        {
            base.Awake();
            rawImage = GetComponent<RawImage>();
            cornerPointsOnScreen = new int[8];
            cornerPoinsCount = cornerPointsOnScreen.Length / 2;
            cornerPointsHandle = GCHandle.Alloc(cornerPointsOnScreen, GCHandleType.Pinned);
            cornerPoints = cornerPointsHandle.AddrOfPinnedObject();
            rectTransform = GetComponent<RectTransform>();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            cornerPointsHandle.Free();
        }

        internal override Material material
        {
            get {
                return rawImage.material;
            }
        }

        internal override Texture2D texture
        {
            get {
                var t = rawImage.texture as Texture2D;
                if (t == null)
                {
                    t = material.mainTexture as Texture2D;
                }
                return t;
            }
        }

        internal override Vector2Int placementSize
        {
            get {
                return new Vector2Int(Mathf.RoundToInt(rectTransform.rect.width * transform.lossyScale.x),
                                      Mathf.RoundToInt(rectTransform.rect.height* transform.lossyScale.y));
            }
        }

        protected override void ApplyPlacementTexture()
        {
            rawImage.texture = adPlacementTexture;
        }

        private void UpdateScreenPositions(Vector3[] worldCorners)
        {
            Canvas canvas = rawImage.canvas;

            if (canvas == null)
            {
                return;
            }

            Canvas rootCanvas = canvas.rootCanvas;

            if (rootCanvas == null)
            {
                return;
            }

            for (int i = 0; i < 4; i++)
            {
                if (canvas.renderMode != RenderMode.ScreenSpaceOverlay)
                {
                    Vector2 screenPosition =
                        RectTransformUtility.WorldToScreenPoint(canvas.worldCamera, worldCorners[i]);
                    cornerPointsOnScreen[i * 2] = Mathf.FloorToInt(screenPosition.x);
                    cornerPointsOnScreen[i * 2 + 1] = Mathf.FloorToInt(screenPosition.y);
                    continue;
                }

                cornerPointsOnScreen[i * 2] = Mathf.FloorToInt(worldCorners[i].x);
                cornerPointsOnScreen[i * 2 + 1] = Mathf.FloorToInt(worldCorners[i].y);
            }
        }

        protected override void ViewabilityCheck()
        {
            Vector3[] corners = new Vector3[4];
            GetComponent<RectTransform>().GetWorldCorners(corners);
            UpdateScreenPositions(corners);
            AdvertyViewabilityCheck.ViewabilityCheckScreenSpace(Id, cornerPoints, cornerPoinsCount);
        }
    }
}
