
using System.Runtime.InteropServices;
using UnityEditor;
using UnityEngine;

namespace Adverty5.Editor
{
    public class AboutWindow : AdvertyWindow
    {
        private const string PLATFORM_DESC = "Adverty Seamless Advertising Platform";
        private const string COPYRIGHT = "Copyright © 2021 Adverty AB. All Rights Reserved.";
        private const string LOGOTYPE_TEXTURE = "/Textures/advertyLogo.png";
        private const string ABOUT_TITLE = "About";

        private Texture2D logo;

        [MenuItem(ABOUT_MENU_ITEM_PATH, false, MENU_QUEUE_VALUE + 1)]
        public static void ShowWindow()
        {
            EditorWindow window = GetWindow(typeof(AboutWindow));
            window.minSize = new Vector2(600, 200);
            window.maxSize = new Vector2(600, 200);
        }

        private void OnGUI()
        {
            GUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            GUILayout.Label(logo, GUILayout.Width(256), GUILayout.Height(128));
            GUILayout.FlexibleSpace();
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            GUILayout.Label(PLATFORM_DESC);
            GUILayout.FlexibleSpace();
            GUILayout.EndHorizontal();

            GUILayout.FlexibleSpace();

            GUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            GUILayout.Label(Marshal.PtrToStringAnsi(Adverty.GetSDKVersion()));
            GUILayout.FlexibleSpace();
            GUILayout.EndHorizontal();

            GUILayout.Space(10);

            GUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            GUILayout.Label(COPYRIGHT);
            GUILayout.FlexibleSpace();
            GUILayout.EndHorizontal();
        }

        private void OnEnable()
        {
            logo = AssetDatabase.LoadAssetAtPath<Texture2D>(SDKLocator.AdvertyDirectoryRelative + LOGOTYPE_TEXTURE);
            titleContent.text = ABOUT_TITLE;
        }
    }
}
