using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEditor;

namespace Adverty5.Editor
{
    internal class PluginPreprocessor : AssetPostprocessor
    {
        private const string FRAMEWORK_COLLECTION_KEY = "FrameworkDependencies";
        private const string IOS_WEBKIT_FRAMEWORK_NAME = "WebKit";
        private const string IOS_ADSUPPORT_FRAMEWORK_NAME = "AdSupport";
        private const string IOS_VIDEOTOOLBOX_FRAMEWORK_NAME = "VideoToolbox";
        private const string IOS_SAFARI_SERVICES_FRAMEWORK_NAME = "SafariServices";
        private const string IOS_IMAGEIO_FRAMEWORK_NAME = "ImageIO";
        private const string IOS_SKOVERLAY_FRAMEWORK_NAME = "StoreKit";
        private const string VALUE_SEPARATOR = ";";

#if UNITY_2021_2_OR_NEWER
        public static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets,
                                                  string[] movedFromAssetPaths, bool domainReloaded)
#else
        public static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets,
                                                  string[] movedFromAssetPaths)
#endif
        {
            foreach (string path in importedAssets)
            {
                if (!path.Contains("adverty") && !path.Contains("Adverty") && !path.Contains("Plugins"))
                {
                    continue;
                }

                PluginImporter pluginImporter = AssetImporter.GetAtPath(path) as PluginImporter;
                if (pluginImporter != null)
                {
                    BuildTarget buildTarget = GetBuildTarget(path);
                    if (buildTarget != BuildTarget.NoTarget)
                    {
                        bool isEditorSupported =
                            buildTarget == BuildTarget.StandaloneWindows || buildTarget == BuildTarget.StandaloneOSX;
                        pluginImporter.SetCompatibleWithAnyPlatform(false);

                        foreach (BuildTarget target in (BuildTarget[])Enum.GetValues(typeof(BuildTarget)))
                        {
                            if (!IsBuildTargetObsolete(target))
                            {
                                pluginImporter.SetCompatibleWithPlatform(target, target == buildTarget);
                            }
                        }

                        pluginImporter.SetCompatibleWithEditor(isEditorSupported);
                        pluginImporter.SetPlatformData(buildTarget, "CPU", "AnyCPU");

                        if (buildTarget == BuildTarget.StandaloneWindows && path.Contains("AdvertyUnityPlugin"))
                        {
                            pluginImporter.SetCompatibleWithPlatform(BuildTarget.StandaloneWindows64, true);
                            pluginImporter.SetPlatformData(BuildTarget.StandaloneWindows64, "CPU", "x86_64");
                        }

                        if (isEditorSupported)
                        {
                            string osValue = buildTarget == BuildTarget.StandaloneWindows ? "Windows" : "OSX";
                            pluginImporter.SetPlatformData(buildTarget, "OS", osValue);
                        }

                        if (buildTarget == BuildTarget.iOS && path.Contains("AdvertyUnityPlugin"))
                        {
                            ResolveiOSPluginMetadata(pluginImporter);
                        }
                    }
                }
            }
        }

        private static BuildTarget GetBuildTarget(string path)
        {
            if (path.Contains("iOS"))
            {
                return BuildTarget.iOS;
            }
            else if (path.Contains("Android"))
            {
                return BuildTarget.Android;
            }
            else if (path.Contains("Darwin"))
            {
                return BuildTarget.StandaloneOSX;
            }
            else if (path.Contains("Windows"))
            {
                return BuildTarget.StandaloneWindows;
            }

            return BuildTarget.NoTarget;
        }

        internal static void ResolveiOSPluginMetadata(PluginImporter iosPluginImporter)
        {
            string data = iosPluginImporter.GetPlatformData(BuildTarget.iOS, FRAMEWORK_COLLECTION_KEY);
            List<string> currentFrameworks = new List<string>();

            if (!string.IsNullOrEmpty(data))
            {
                currentFrameworks = data.Split(VALUE_SEPARATOR.ToCharArray()).ToList();
            }

            currentFrameworks.Add(IOS_WEBKIT_FRAMEWORK_NAME);
            currentFrameworks.Add(IOS_ADSUPPORT_FRAMEWORK_NAME);
            currentFrameworks.Add(IOS_VIDEOTOOLBOX_FRAMEWORK_NAME);
            currentFrameworks.Add(IOS_SAFARI_SERVICES_FRAMEWORK_NAME);
            currentFrameworks.Add(IOS_IMAGEIO_FRAMEWORK_NAME);
            currentFrameworks.Add(IOS_SKOVERLAY_FRAMEWORK_NAME);
            string updatedFrameworks = string.Join(VALUE_SEPARATOR, currentFrameworks.ToArray());
            iosPluginImporter.SetPlatformData(BuildTarget.iOS, FRAMEWORK_COLLECTION_KEY, updatedFrameworks);
        }

        private static bool IsBuildTargetObsolete(BuildTarget target)
        {
            FieldInfo fieldInfo = target.GetType().GetField(target.ToString());
            ObsoleteAttribute[] attributes =
                (ObsoleteAttribute[])fieldInfo.GetCustomAttributes(typeof(ObsoleteAttribute), false);
            return attributes != null && attributes.Length > 0;
        }
    }
}
