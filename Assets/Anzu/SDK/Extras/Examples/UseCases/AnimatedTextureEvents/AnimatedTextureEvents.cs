using UnityEngine;
using anzu;
using System.Collections.Generic;
using UnityEngine.UI;

namespace Anzu.Examples
{
    public class AnimatedTextureEvents : MonoBehaviour
    {
        public Slider playbackSlider;
        public Slider impressionSlider;
        public AnimatedTexture animatedTexture;

        [Header("Animated Texture Data")]
        public float playbackFullness;
        public double currentAngleScore;
        public double currentVisibilityScore;
        public float impressionFullness;
        public float totalImpressions;
        public float totalEmpties;
        public double currentViewabilityScore;
        public List<GameObject> interferingColliders;


        private void Awake()
        {
            animatedTexture.OnPlaybackEmpty += OnPlaybackEmpty;
            animatedTexture.OnPlaybackStarted += OnPlaybackStarted;
            animatedTexture.OnPlaybackInitiated += OnPlaybackInitiated;
            animatedTexture.OnPlaybackCompleted += OnPlaybackCompleted;
            animatedTexture.OnPlaybackMediaInfo += OnPlaybackMediaInfo;

        }

        private void OnDestroy()
        {
            animatedTexture.OnPlaybackEmpty -= OnPlaybackEmpty;
            animatedTexture.OnPlaybackStarted -= OnPlaybackStarted;
            animatedTexture.OnPlaybackInitiated -= OnPlaybackInitiated;
            animatedTexture.OnPlaybackCompleted -= OnPlaybackCompleted;
            animatedTexture.OnPlaybackMediaInfo -= OnPlaybackMediaInfo;
        }

        private void Update()
        {
            playbackFullness = animatedTexture.GetPlaybackFullness();
            currentAngleScore = animatedTexture.GetCurrentAngleScore();
            currentVisibilityScore = animatedTexture.GetCurrentVisibilityScore();
            impressionFullness = animatedTexture.GetImpressionFullness();
            totalImpressions = animatedTexture.GetTotalImpressions();
            totalEmpties = animatedTexture.GetTotalEmpties();
            currentViewabilityScore = animatedTexture.GetCurrentViewabilityScore();
            interferingColliders = animatedTexture.GetInterferingColliders();

            playbackSlider.value = playbackFullness;
            impressionSlider.value = impressionFullness;
        }


        #region Events

        private void OnPlaybackEmpty()
        {
            Debug.Log("Playback Empty");
        }

        private void OnPlaybackStarted()
        {
            Debug.Log("Playback Started");
        }

        private void OnPlaybackCompleted()
        {
            Debug.Log("Playback Completed");
        }

        private void OnPlaybackInitiated()
        {
            Debug.Log("Playback Initiated");
        }

        private void OnPlaybackMediaInfo(string filePath, int mediaClass, int width, int height)
        {
            Debug.Log("Playback MediaInfo");
        }

        #endregion
    }
}
