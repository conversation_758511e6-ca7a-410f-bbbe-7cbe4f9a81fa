using System.Collections.Generic;
using System.Text;
using Music.ACM;
using Music.ACM.Interfaces;using UnityEngine;
using UnityEngine.Serialization;

public abstract class ApprovalProcessContentDisplay : MonoBehaviour
{
    public int audioSlotOrder;
    public int levelDesignSlotOrder;
    public bool hasInitialized;
    public ApprovalProcessSongState currentSongState;

    [Header("Options")]
    [SerializeField] protected bool _truncatedText;

    protected bool IsNewSong;
    protected List<Content> AudioContents;
    protected List<Content> LevelDesignContents;

    private const int TRUNCATE_PREFIX_CHARACTER_NUMBER = 10;
    private const int TRUNCATE_SUFFIX_CHARACTER_NUMBER = 30;
    private const string ACTIVE_SUFFIX = "- ACTIVE";


    public virtual void Init(ApprovalProcessSongState songState, List<Content> audioContents, List<Content> levelDesignContents, bool isNewSong = false)
    {
        hasInitialized = true;
        IsNewSong = isNewSong;
        AudioContents = audioContents;
        LevelDesignContents = levelDesignContents;
        currentSongState = songState;
    }

    public virtual void Show()
    {
        gameObject.SetActive(true);
    }

    public virtual void Close()
    {
        gameObject.SetActive(false);
    }

    protected bool IsActiveContent(Content content)
    {
        return content.name.Contains(ACTIVE_SUFFIX);
    }

    protected virtual string GetContentName(Content content)
    {
        return _truncatedText ? Truncate(content.name) : content.name;
    }

    protected string Truncate(string text)
    {
        if (text.Length > TRUNCATE_PREFIX_CHARACTER_NUMBER + TRUNCATE_SUFFIX_CHARACTER_NUMBER)
        {
            StringBuilder truncatedText = new StringBuilder();
            truncatedText.Append(text.Substring(0, TRUNCATE_PREFIX_CHARACTER_NUMBER));
            truncatedText.Append("...");
            truncatedText.Append(text.Substring(text.Length-TRUNCATE_SUFFIX_CHARACTER_NUMBER, TRUNCATE_SUFFIX_CHARACTER_NUMBER));
            return truncatedText.ToString();
        }
        else
        {
            return text;
        }
    }
}