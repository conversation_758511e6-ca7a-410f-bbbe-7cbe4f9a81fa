using System;
using UnityEngine;
using UnityEngine.UI;

public abstract class OnscreenObject : MonoBehaviour
{
    [SerializeField] private OnScreenPosition onScreenPosition = OnScreenPosition.BottomRight;
    [SerializeField] private Button _btnClose = null;

    private RectTransform _rectTransform;
    
    public virtual void Init()
    {
        _rectTransform = GetComponent<RectTransform>();
        _btnClose.onClick.RemoveAllListeners();
        _btnClose.onClick.AddListener(Hide);
        
        SetPosition(onScreenPosition);
        gameObject.SetActive(false);
    }

    public virtual void Show()
    {
        gameObject.SetActive(true);
    }

    public virtual void Hide()
    {
        gameObject.SetActive(false);
    }

    private void SetPosition(OnScreenPosition position)
    {
        switch (onScreenPosition)
        {
            case OnScreenPosition.TopLeft:
            {
                _rectTransform.anchorMin = new Vector2(0, 1);
                _rectTransform.anchorMax = new Vector2(0, 1);
                _rectTransform.pivot = new Vector2(0, 1);
                break;
            }
            case OnScreenPosition.TopRight:
            {
                _rectTransform.anchorMin = new Vector2(1, 1);
                _rectTransform.anchorMax = new Vector2(1, 1);
                _rectTransform.pivot = new Vector2(1, 1);
                break;
            }
            case OnScreenPosition.BottomLeft:
            {
                _rectTransform.anchorMin = new Vector2(0, 0);
                _rectTransform.anchorMax = new Vector2(0, 0);
                _rectTransform.pivot = new Vector2(0, 0);
                break;
            }
            case OnScreenPosition.BottomRight:
            {
                _rectTransform.anchorMin = new Vector2(1, 0);
                _rectTransform.anchorMax = new Vector2(1, 0);
                _rectTransform.pivot = new Vector2(1, 0);
                break;
            }
            default:
                break;
        }

        _rectTransform.anchoredPosition = Vector2.zero;
    }
}

public enum OnScreenPosition
{
    TopLeft,
    TopRight,
    BottomLeft,
    BottomRight
}
