// using System.Diagnostics;
// using System;
// using System.Collections.Generic;
// using Newtonsoft.Json;
// using System.ComponentModel;
// using Music.ACM.Attributes;
// using UnityEngine.Serialization;
//
// namespace Music.ACM
// {
//     [System.Serializable]
//     public class TraceData : BaseDataHoneyComb
//     {
//         [ConsistentValue]
//         [JsonProperty("honeycomb.lib_name"      ), DefaultValue("")] public string lib_honeycomb_name       = "";
//         [JsonProperty("honeycomb.lib_version"   ), DefaultValue("")] public string lib_honeycomb_version    = "";
//
//         [JsonProperty("event_name"      ), DefaultValue("")]    public string   event_name      = "";
//         [JsonProperty("endpoint"        ), DefaultValue("")]    public string   endpoint        = "";
//
//         [JsonProperty("trace.span_id"   ), DefaultValue("")]    public string   span_id         = "";
//         [JsonProperty("trace.parent_id" ), DefaultValue("")]    public string   parent_id       = "";
//         [JsonProperty("trace.trace_id"  ), DefaultValue("")]    public string   trace_id        = "";
//         [JsonProperty("name"            ), DefaultValue("")]    public string   name            = "";
//         [JsonProperty("service.name"    ), DefaultValue("")]    public string   service_name    = "acm-sdk";
//         [JsonProperty("duration_ms"     ), DefaultValue(0)]     public float    duration_ms     = 0;
//         [JsonProperty("deltaTimestamp"  ), DefaultValue(0)]     public long     deltaTimestamp  = 0;
//         [JsonProperty("content_length"  ), DefaultValue(0)]     public long     content_length  = 0;
//         [JsonProperty("content_encoding"), DefaultValue("")]    public string   content_encoding= "";
//         [JsonProperty("region_code"     ), DefaultValue("")]    public string   region_code     = "";
//         [JsonProperty("firebase_config_fetched"), DefaultValue(false)] public bool     firebase_config_fetched = false;
//
// #region Search Song
//         [JsonProperty("search.limit"    ), DefaultValue("")]    public string   limit       = "";
//         [JsonProperty("search.keyword"  ), DefaultValue("")]    public string   keyword     = "";
//         [JsonProperty("search.playlist" ), DefaultValue("")]    public string   playlist    = "";
//         [JsonProperty("search.sortBy"   ), DefaultValue("")]    public string   sortBy      = "";
//         [JsonProperty("search.sortType" ), DefaultValue("")]    public string   sortType    = "";
//         [JsonProperty("search.type"     ), DefaultValue("")]    public string   type        = "";
//         [JsonProperty("search.where"    ), DefaultValue("")]    public string   where       = "";
//         [JsonProperty("search.orderBy"  ), DefaultValue("")]    public string   orderBy     = "";
//         [JsonProperty("is_cache"        ), DefaultValue(false)] public bool     is_cache    = false;
//         [JsonProperty("search.endpoint_path"            ), DefaultValue("")]    public string   endPointPath            = "";
//         [JsonProperty("search.endpoint_hostname"        ), DefaultValue("")]    public string   endPointHostName        = "";
//         [JsonProperty("search.endpoint_ip"              ), DefaultValue("")]    public string   endPointIp              = "";
//         [JsonProperty("search.search_page_index"        ), DefaultValue(0)]     public int      searchPageIndex         = 0;
//         [JsonProperty("search.search_token"             ), DefaultValue("")]    public string   searchToken             = "";
//         [JsonProperty("search.result.count"             ), DefaultValue("")]    public string   resultCount             = "";
//         [JsonProperty("search.result.song_per_page"     ), DefaultValue(0)]     public int      resultPerPage           = 0;
//         [JsonProperty("search.result.cache_succeeded"   ), DefaultValue("")]    public string   resultCacheSucceeded    = "";
//         [JsonProperty("search.result.from_cache"        ), DefaultValue(false)] public bool     searchResultFromCache   = false;
//         [JsonProperty("search.result.overrided_count"   ), DefaultValue(0)]     public int      searchResultOverridedCount = 0;
// #endregion
//
// #region Error
//         [JsonProperty("error"           ), DefaultValue("")] public bool    error           = false;
//
//         // There is a space character at the end or error.message property because this value name on HoneyComb server has space characte too.
//         [JsonProperty("error.message "  ), DefaultValue("")] public string  errorMessage    = "";
//         [JsonProperty("error.stacktrace"), DefaultValue("")] public string  errorStackTrace = "";
// #endregion
//
// #region Download
//         [JsonProperty("download.file_type"          ), DefaultValue("")]    public string   download_file_type      = "";
//         [JsonProperty("download.song_id"            ), DefaultValue("")]    public string   download_song_id        = "";
//         [JsonProperty("download.song_name"          ), DefaultValue("")]    public string   download_song_name      = "";
//         [JsonProperty("download.cdn"                ), DefaultValue("")]    public string   download_cdn            = "";
//         [ConsistentValue()]
//         [JsonProperty("download.cdn_name"           ), DefaultValue("")]    public string   download_cdn_name       = "";
//         [JsonProperty("download.retry_time"         ), DefaultValue(-1)]     public int      download_retry_time     = 0;
//         [JsonProperty("download.ttfb_ms"            ), DefaultValue(0)]     public int      download_ttfb           = 0;
//         [JsonProperty("download.download_link"      ), DefaultValue("")]    public string   download_link           = "";
//         [JsonProperty("download.complete_time_ms"   ), DefaultValue(0)]     public int      download_complete_time  = 0;
//         [JsonProperty("download.file_size_bytes"    ), DefaultValue(0)]     public int      download_file_size      = 0;
//         [JsonProperty("download.download_speed_bps" ), DefaultValue(0)]     public int      download_speed          = 0;
//         [JsonProperty("download.reason"             ), DefaultValue("")]    public string   download_reason         = "";
//         [JsonProperty("download.condition"          ), DefaultValue("")]    public string   download_condition      = "";
//         [JsonProperty("download.result"             ), DefaultValue("")]    public string   download_result         = "";
//         [JsonProperty("download.method"             ), DefaultValue("")]    public string   download_method         = "";
// #endregion
//
// #region Override Data
//         [JsonProperty("override.data_hash"          ), DefaultValue("")]    public string   override_data_hash      = "";
//         [JsonProperty("override.data_size_byte"     ), DefaultValue(0)]     public int      override_data_size      = 0;
//         [JsonProperty("override.song_count"         ), DefaultValue(0)]     public int      override_song_count     = 0;
//         [JsonProperty("override.data_header"        ), DefaultValue("")]    public string   override_data_header    = "";
// #endregion
//
//         [ConsistentValue(ConsistentType.Children)]
//         [JsonProperty("sampling_value"              ), DefaultValue("")]    public int      samplingValue               = 0;
//
//         [JsonProperty("app.pause_system_time_ms"    ), DefaultValue(0)]     public long     app_pause_system_time_ms        = 0;
//         [JsonProperty("app.resume_system_time_ms"   ), DefaultValue(0)]     public long     app_resume_system_time_ms       = 0;
//         [JsonProperty("app.pause_unity_time_ms"     ), DefaultValue(0)]     public long     app_pause_unity_time_ms         = 0;
//         [JsonProperty("app.resume_unity_time_ms"    ), DefaultValue(0)]     public long     app_resume_unity_time_ms        = 0;
//         [JsonProperty("app.pause_duration_ms"       ), DefaultValue(0)]     public long     app_pause_duration_ms           = 0;
//         [JsonProperty("app.user_minimized"),       DefaultValue(false)]     public bool     app_user_minimized          = false;
//         [Deprecated]
//         [JsonProperty("app.is_cache"        ),     DefaultValue(false)]     public bool     app_is_cache                    = false;
//
//         DateTime    startTime       ;
//         DateTime    endTime         ;
//         DateTime    timestamp       ;
//         Activity    activity        ;
//
//         [ConsistentValue(ConsistentType.Children)]
//         [JsonIgnore] public bool    samplingStatus  = false;
//
//         [JsonIgnore] public Activity Activity       { get; set; }
//
//         [JsonIgnore] public bool     SamplingStatus  => samplingStatus   ;
//
//         [JsonIgnore] public int      SamplingValue   => samplingValue    ;
//
//         [JsonIgnore] public DateTime    Timestamp       => timestamp        ;
//
//         [JsonIgnore] public DateTime CurrentTime { get { return DateTime.UtcNow.AddMilliseconds(deltaTimestamp); } }
//
//         [JsonIgnore] public Dictionary<string, string> DynamicAttributes => dynamicAttributes;
//
//         [JsonIgnore] public Dictionary<string, string> dynamicAttributes = new Dictionary<string, string>();
//
//         #region Constructors
//
//         public TraceData(string name)
//         {
//             this.name               = name;
//             this.trace_id           = ActivityTraceId.CreateRandom().ToString() ;
//             this.span_id            = ActivitySpanId.CreateRandom().ToString()  ;
//             this.parent_id          = ""    ;
//             this.samplingStatus     = false ;
//             this.samplingValue      = 0     ;
//
//             acm_first_initialized_time  = ACMSDK.Instance.FirstInitializedTime      ;
//             acm_initialized_time        = ACMSDK.Instance.InitializedTime           ;
//             acm_session_time            = ACMSDK.Instance.SessionTime               ;
//             firebase_config_fetched     = ACMSDK.Instance.IsFirebaseConfigFetched   ;
//
//             app_acm_id                  = ACMSDK.Instance.GameId            ;
//             app_product_code            = ACMSDK.Instance.ProductCode       ;
//             app_product_code_full       = ACMSDK.Instance.ProductCodeFull   ;
//         }
//
// #endregion
//
//         public void SetParent(TraceData parent)
//         {
//             this.trace_id       = parent.trace_id       ;
//             this.parent_id      = parent.span_id        ;
//             this.deltaTimestamp = parent.deltaTimestamp ;
//             this.samplingStatus = parent.samplingStatus ;
//             this.samplingValue  = parent.samplingValue  ;
//             this.download_method = parent.download_method;
//         }
//
//         public void SetStartTime(DateTime time)
//         {
//             this.timestamp      = time;
//             this.startTime      = time;
//         }
//
//         public TraceData FinishTrace(long manualEndTime = 0)
//         {
//             endTime = DateTime.UtcNow;
//             if (manualEndTime != 0)
//             {
//                 endTime = DateTimeOffset.FromUnixTimeMilliseconds(manualEndTime).UtcDateTime;
//             }
//             else
//             {
//                 endTime = endTime.AddMilliseconds(deltaTimestamp);
//             }
//
//             duration_ms = new DateTimeOffset(endTime).ToUnixTimeMilliseconds() - new DateTimeOffset(startTime).ToUnixTimeMilliseconds();
//
//             if (samplingValue == 0)
//             {
//                 ACMDebug.Warning(this.name + " not set sampling value");
//             }
//
//             return this;
//         }
//     }
// }
