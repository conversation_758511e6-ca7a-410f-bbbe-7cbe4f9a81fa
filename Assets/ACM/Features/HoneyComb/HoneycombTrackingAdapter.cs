using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Net;
using System.Reflection;
using Music.ACM.Honeycomb;
using Music.ACM.Interfaces;
using UnityEngine;

namespace Music.ACM
{
    public partial class ACMSetting
    {
        [SerializeField] internal HoneycombTrackingAdapter HoneycombTracking = null;
    }

    [Serializable]
    internal partial class HoneycombTrackingAdapter : TrackingAdapter
    {
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        private static void AutoStart()
        {
            Register(nameof(ACMSetting.HoneycombTracking));
        }

        protected override void Init(Action<bool> onComplete)
        {
            HoneycombManager.Instance.Initialize(ACMSDK.Instance.Endpoint, ACMSDK.Setting.useHoneycombProxy);
            onComplete?.Invoke(true);
        }

        public override void SwitchEndPoint(string endPoint)
        {
            HoneycombManager.Instance.Initialize(endPoint, ACMSDK.Setting.useHoneycombProxy);
        }

        public override TrackingActivity CreateTrackingActivity(string name)
        {
            TrackingActivity activity = new TrackingActivity(name);
            HoneycombManager.Instance.AddActivity(activity);
            return activity;
        }

        public override TrackingActivity CreateTrackingActivity(string name, long deltaTime)
        {
            TrackingActivity activity = new TrackingActivity(name,deltaTime);
            SetActivityId(activity);
            HoneycombManager.Instance.AddActivity(activity);
            return activity;
        }

        public override TrackingActivity CreateChild(TrackingActivity parent, string name)
        {
            DateTime startTime = DateTime.UtcNow.AddMilliseconds(parent.deltaTime);
            TrackingActivity activity = new TrackingActivity(name, startTime, parent);
            SetActivityId(activity, parent);
            HoneycombManager.Instance.AddActivity(activity);
            return activity;
        }
        public override TrackingActivity CreateChild(TrackingActivity parent, string name, string startTime)
        {
            DateTimeOffset time = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(startTime));
            TrackingActivity activity = new TrackingActivity(name, time.UtcDateTime, parent);
            SetActivityId(activity, parent);
            HoneycombManager.Instance.AddActivity(activity);
            return activity;
        }

        public override void SetStartTime(TrackingActivity activity, string startTime)
        {
            activity.parameters[TIMESTAMP] = startTime;
            activity.parameters[START_TIME] = startTime;
        }

        public override void SetResultCode(TrackingActivity activity, ResultCode code)
        {
            activity.parameters[RESULT_CODE] = ((int)code).ToString();
            activity.parameters[RESULT_MESSAGE] = code.ToString();
        }

        public override void FinishActivity(TrackingActivity activity, string message = "", long manualEndTime = 0)
        {
            if (activity.isFinised)
                return;
            bool isError =!String.IsNullOrEmpty(message);

            if(isError)
            {
                if(activity.parent == null || activity.parent.hasStackTrack == false)
                {
                    string stackTrack = GetStackTrack(2);

                    activity.parameters[ERROR_STACK_TRACE] = stackTrack;
                    activity.hasStackTrack = true;
                }
            }

            activity.isFinised = true;
            activity.parameters[ERROR] = isError.ToString();
            activity.parameters[ERROR_MESSAGE] = message;

            foreach (TrackingActivity child in activity.childrens)
            {
                FinishActivity(child, message);
            }

            var endTime = DateTime.UtcNow;
            if (manualEndTime != 0)
            {
                endTime = DateTimeOffset.FromUnixTimeMilliseconds(manualEndTime).UtcDateTime;
            }
            else
            {
                endTime = endTime.AddMilliseconds(activity.deltaTime);
            }

            activity.parameters[NAME] = activity.name;
            activity.parameters[DELTA_TIMESTAMP] = activity.deltaTime.ToString();
            activity.parameters[DURATION_MS] = (new DateTimeOffset(endTime).ToUnixTimeMilliseconds() - new DateTimeOffset(activity.startTime).ToUnixTimeMilliseconds()).ToString();

            SetDefaultData(activity);
            HoneycombManager.Instance.Send(activity);
            HoneycombManager.Instance.RemoveActivity(activity);
        }

        private void SetActivityId(TrackingActivity activity, TrackingActivity parent = null)
        {
            if (parent != null)
            {
                activity.parameters[TRACE_ID] = GetActivityTraceId(parent);
                activity.parameters[PARENT_ID] = GetActivitySpanId(parent);
            }
            else
            {
                activity.parameters[TRACE_ID] = ActivityTraceId.CreateRandom().ToString() ;
            }
            activity.parameters[SPAN_ID] = ActivitySpanId.CreateRandom().ToString()  ;
        }

        private void SetDefaultData(TrackingActivity activity)
        {
            activity.parameters[LIB_HONEYCOMB_NAME] = HoneycombManager.Instance.LibraryName;
            activity.parameters[LIB_HONEYCOMB_VERSION] = HoneycombManager.Instance.LibraryVersion;

            activity.parameters[ACM_FIRST_INITIALIZED_TIME] = ACMSDK.Instance.FirstInitializedTime.ToString();
            activity.parameters[ACM_INITIALIZED_TIME] = ACMSDK.Instance.InitializedTime.ToString();
            activity.parameters[ACM_SESSION_TIME] = ACMSDK.Instance.SessionTime.ToString();

            activity.parameters[APP_ACM_ID] = ACMSDK.Instance.GameId;
            activity.parameters[APP_PRODUCT_CODE] = ACMSDK.Instance.ProductCode;
            activity.parameters[APP_PRODUCT_CODE_FULL] = ACMSDK.Instance.ProductCodeFull;

            activity.parameters[SAMPLING_VALUE] = "1";
            activity.parameters[SERVICE_NAME] = "acm-sdk";

            DeviceInfoExtend deviceInfo = DeviceInfoExtend.GetDeviceInfoExtend();
            activity.parameters[DEVICE_MODEL] = deviceInfo.device_model;
            activity.parameters[DEVICE_NAME] = deviceInfo.device_name;
            activity.parameters[DEVICE_TYPE] = deviceInfo.device_type;
            activity.parameters[DEVICE_UNIQUE_IDENTIFIER] = deviceInfo.device_unique_identifier;
            activity.parameters[OPERATING_SYSTEM] = deviceInfo.operating_system;
            activity.parameters[OPERATING_SYSTEM_FAMILY] = deviceInfo.operating_system_family;
            activity.parameters[PROCESSOR_TYPE] = deviceInfo.processor_type;
            activity.parameters[BATTERY_LEVEL] = deviceInfo.battery_level.ToString(CultureInfo.InvariantCulture);
            activity.parameters[BATTERY_STATUS] = deviceInfo.battery_status;
            activity.parameters[PROCESSOR_COUNT] = deviceInfo.processor_count.ToString();
            activity.parameters[PROCESSOR_FREQUENCY] = deviceInfo.processor_frequency.ToString();
            activity.parameters[SUPPORTS_ASYNC_COMPUTE] = deviceInfo.supports_async_compute.ToString();
            activity.parameters[SUPPORTS_AUDIO] = deviceInfo.supports_audio.ToString();
            activity.parameters[SYSTEM_MEMORY_SIZE] = deviceInfo.system_memory_size.ToString();
            activity.parameters[DEVICE_ADVERTISING_ID] = deviceInfo.device_advertising_id;
            activity.parameters[DEVICE_IP] = deviceInfo.device_ip;

            activity.parameters[REQUEST_BASE_URL] = ACMConstant.BCS_VERSION;
            activity.parameters[ACM_VERSION] = ACMSDK.VERSION;
            activity.parameters[ACM_VERSION_META] = ACMSDK.VERSION_META;

            ApplicationInfoExtend applicationInfo = ApplicationInfoExtend.GetApplicationInfoExtend();
            activity.parameters[BUILD_GUID] = applicationInfo.build_guid;
            activity.parameters[GENUINE] = applicationInfo.genuine.ToString();
            activity.parameters[GENUINE_CHECK_AVAILABLE] = applicationInfo.genuine_check_available.ToString();
            activity.parameters[IDENTIFIER] = applicationInfo.identifier;
            activity.parameters[INSTALLER_NAME] = applicationInfo.installer_name;
            activity.parameters[IS_EDITOR] = applicationInfo.is_editor.ToString();
            activity.parameters[PLATFORM] = applicationInfo.platform;
            activity.parameters[PRODUCT_NAME] = applicationInfo.product_name;
            activity.parameters[SYSTEM_LANGUAGE] = applicationInfo.system_language;
            activity.parameters[LOCALE] = applicationInfo.locale;
            activity.parameters[UNITY_VERSION] = applicationInfo.unity_version;
            activity.parameters[APP_VERSION] = applicationInfo.app_version;
            activity.parameters[ABSOLUTE_URL] = applicationInfo.absolute_url;
            activity.parameters[CLOUD_PROJECT_ID] = applicationInfo.cloud_project_id;
            activity.parameters[COMPANY_NAME] = applicationInfo.company_name;
            activity.parameters[INSTALL_MODE] = applicationInfo.install_mode;
            activity.parameters[INTERNET_REACHABILITY] = applicationInfo.internet_reachability;
            activity.parameters[IS_FOCUSED] = applicationInfo.is_focused.ToString();
            activity.parameters[IS_PLAYING] = applicationInfo.is_playing.ToString();
            activity.parameters[IS_CONSOLE_PLATFORM] = applicationInfo.is_console_platform.ToString();
            activity.parameters[IS_MOBILE_PLATFORM] = applicationInfo.is_mobile_platform.ToString();
            activity.parameters[RUN_IN_BACKGROUND] = applicationInfo.run_in_background.ToString();
            activity.parameters[CONSOLE_LOG_PATH] = applicationInfo.console_log_path;
            activity.parameters[PERSISTENT_DATA_PATH] = applicationInfo.persistent_data_path;
            activity.parameters[STREAMING_ASSETS_PATH] = applicationInfo.streaming_assets_path;
            activity.parameters[TEMPORARY_CACHE_PATH] = applicationInfo.temporary_cache_path;
            activity.parameters[TARGET_FRAME_RATE] = applicationInfo.target_frame_rate.ToString();

            activity.parameters[DEVICE_TS] = (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0)).TotalMilliseconds.ToString(CultureInfo.InvariantCulture);
        }

        public override void SetIsFromCache(TrackingActivity activity, bool isFromCache)
        {
            activity.parameters[SEARCH_RESULT_FROM_CACHE] = isFromCache.ToString();
            activity.parameters[APP_IS_CACHE] = isFromCache.ToString();
            activity.parameters[IS_CACHE] = isFromCache.ToString();
            if (activity.parent != null)
            {
                SetIsFromCache(activity.parent, isFromCache);
            }
        }

        public override void SetAttribute(TrackingActivity activity, string attributeKey, string value)
        {
            activity.parameters[attributeKey] = value;
        }

        public override void SetSearchSongProperties(TrackingActivity activity, string endpoint, string keyword,
            string playlist, string type,
            string sortBy, Sort_Type sortType, int limit, string where, string orderBy)
        {
            activity.parameters[ENDPOINT] = endpoint;
            activity.parameters[KEYWORD] = keyword;
            activity.parameters[PLAYLIST] = playlist;
            activity.parameters[TYPE] = type;
            activity.parameters[SORT_BY] = sortBy;
            activity.parameters[SORT_TYPE] = sortType.ToString();
            activity.parameters[LIMIT] = limit.ToString();
            activity.parameters[WHERE] = where;
            activity.parameters[ORDER_BY] = orderBy;
        }

        public override void SetSearchSongParams(TrackingActivity activity, string endPointPath,
            string endPointHostName, string searchToken,
            int resultCount, int resultPerPage)
        {
            activity.parameters[ENDPOINT_PATH] = endPointPath;
            activity.parameters[ENDPOINT_HOST_NAME] = endPointHostName;
            activity.parameters[SEARCH_TOKEN] = searchToken;
            activity.parameters[RESULT_COUNT] = resultCount.ToString();
            activity.parameters[RESULT_PER_PAGE] = resultPerPage.ToString();
        }

        public override void SetSearchResultCount(TrackingActivity activity, int resultCount)
        {
            activity.parameters[RESULT_COUNT] = resultCount.ToString();
        }

        public override void SetSearchResultOverrideCount(TrackingActivity activity, int overrideCount)
        {
            activity.parameters[SEARCH_RESULT_OVERRIDE_COUNT] = overrideCount.ToString();
        }

        public override void SetEndpoint(TrackingActivity activity, string endpoint)
        {
            activity.parameters[ENDPOINT] = endpoint;
        }

        public override void SetStatusCode(TrackingActivity activity, HttpStatusCode statusCode)
        {
            activity.parameters[STATUS_CODE] = ((int)statusCode).ToString();
            if (activity.parent != null)
            {
                SetStatusCode(activity.parent, statusCode);
            }
        }

        public override void SetSongId(TrackingActivity activity, string songId)
        {
            activity.parameters[SONG_ID] = songId;
        }

        public override void SetSlotId(TrackingActivity activity, string slotId)
        {
            activity.parameters[SLOT_ID] = slotId;
        }

        public override void SetFileType(TrackingActivity activity, string fileType)
        {
            activity.parameters[DOWNLOAD_FILE_TYPE] = fileType;
        }

        public override void SetSongName(TrackingActivity activity, string songName)
        {
            activity.parameters[DOWNLOAD_SONG_NAME] = songName;
        }

        public override void SetDownloadMethod(TrackingActivity activity, string downloadMethod)
        {
            activity.parameters[DOWNLOAD_METHOD] = downloadMethod;
        }

        public override void SetDownloadLink(TrackingActivity activity, string downloadLink)
        {
            activity.parameters[DOWNLOAD_LINK] = downloadLink;
        }

        public override void SetCompleteTime(TrackingActivity activity, int completeTime)
        {
            activity.parameters[DOWNLOAD_COMPLETE_TIME] = completeTime.ToString();
        }

        public override void SetFileSize(TrackingActivity activity, int fileSize)
        {
            activity.parameters[DOWNLOAD_FILE_SIZE] = fileSize.ToString();
        }

        public override void SetDownloadSpeed(TrackingActivity activity, int downloadSpeed)
        {
            activity.parameters[DOWNLOAD_SPEED] = downloadSpeed.ToString();
        }

        public override void SetReason(TrackingActivity activity, string reason)
        {
            activity.parameters[DOWNLOAD_REASON] = reason;
        }

        public override void SetDownloadCondition(TrackingActivity activity, string condition)
        {
            activity.parameters[DOWNLOAD_CONDITION] = condition;
        }

        public override void SetDownloadResult(TrackingActivity activity, string result)
        {
            activity.parameters[DOWNLOAD_RESULT] = result;
        }

        public override void SetRegionCode(TrackingActivity activity, string regionCode)
        {
            activity.parameters[REGION_CODE] = regionCode;
        }

        public override void SetCDNName(TrackingActivity activity, string cdnName)
        {
            activity.parameters[DOWNLOAD_CDN_NAME] = cdnName;
        }

        public override void SetDownloadCDN(TrackingActivity activity, string downloadCDN)
        {
            activity.parameters[DOWNLOAD_CDN] = downloadCDN;
        }

        public override void SetDownloadRetryTime(TrackingActivity activity, int retryTime)
        {
            activity.parameters[DOWNLOAD_RETRY_TIME] = retryTime.ToString();
        }

        public override void SetTimeToFirstByte(TrackingActivity activity, int ttfb)
        {
            activity.parameters[DOWNLOAD_TTFB] = ttfb.ToString();
        }

        public override void SetResponseCode(TrackingActivity activity, int code, string message = "",
            string detail = "")
        {
            activity.parameters[RESPONSE_CODE] = code.ToString();
            activity.parameters[RESPONSE_MESSAGE] = message;
            activity.parameters[RESPONSE_DETAIL] = detail;
        }

        public override void SetOverrideDataHash(TrackingActivity activity, string hash)
        {
            activity.parameters[OVERRIDE_DATA_HASH] = hash;
        }

        public override void SetOverrideDataSize(TrackingActivity activity, int size)
        {
            activity.parameters[OVERRIDE_DATA_SIZE] = size.ToString();
        }

        public override void SetOverrideDataSongCount(TrackingActivity activity, int count)
        {
            activity.parameters[OVERRIDE_SONG_COUNT] = count.ToString();
        }

        public override void SetOverrideDataHeader(TrackingActivity activity, string header)
        {
            activity.parameters[OVERRIDE_DATA_HEADER] = header;
        }

        public override void SetAppPauseTime(TrackingActivity activity, long systemTimeMs, long unityTimeMs)
        {
            activity.parameters[APP_PAUSE_SYSTEM_TIME_MS] = systemTimeMs.ToString();
            activity.parameters[APP_PAUSE_UNITY_TIME_MS] = unityTimeMs.ToString();
            activity.parameters[APP_USER_MINIMIZED] = "True";
        }

        public override void SetAppResumeTime(TrackingActivity activity, long systemTimeMs, long unityTimeMs,
            long pauseDurationMs)
        {
            activity.parameters[APP_RESUME_SYSTEM_TIME_MS] = systemTimeMs.ToString();
            activity.parameters[APP_RESUME_UNITY_TIME_MS] = unityTimeMs.ToString();
            activity.parameters[APP_PAUSE_DURATION_MS] = pauseDurationMs.ToString();
        }

        public override void SetRequestData(TrackingActivity activity, RestType method, string path)
        {
            activity.parameters[REQUEST_METHOD] = method.ToString();
            activity.parameters[REQUEST_PATH] = path;
        }

        public override void SetContentData(TrackingActivity activity, long contentLength, string contentEncoding)
        {
            activity.parameters[CONTENT_LENGTH] = contentLength.ToString();
            activity.parameters[CONTENT_ENCODING] = contentEncoding;
        }

        public override void SetDeltaTime(TrackingActivity activity, long deltaTime)
        {
            activity.deltaTime = deltaTime;
            DateTime starTimeWithDelta = activity.startTime.AddMilliseconds(deltaTime);
            activity.startTime = starTimeWithDelta;

            foreach (var child in activity.childrens)
            {
                SetDeltaTime(child, deltaTime);
            }
        }

        public override void KillActivity(TrackingActivity activity)
        {
            activity.isFinised = true;
            foreach (TrackingActivity child in activity.childrens)
            {
                KillActivity(child);
            }
        }

        public override string GetActivityTraceId(TrackingActivity activity)
        {
            return activity.parameters.TryGetValue(TRACE_ID, out var id) ? id.ToString(): string.Empty;
        }
        public override string GetActivitySpanId(TrackingActivity activity)
        {
            return activity.parameters.TryGetValue(SPAN_ID, out var id) ? id.ToString() : string.Empty;
        }
        public override string GetActivityParentId(TrackingActivity activity)
        {
            return activity.parameters.TryGetValue(PARENT_ID, out var id) ? id.ToString() : string.Empty;
        }

        string GetStackTrack(int skipFrame)
        {
            StackFrame  sf          = new StackFrame(skipFrame, true);
            string      stackLog    = "";

            if(sf != null)
            {
                string      fileName    = sf.GetFileName();
                int         lineNumber  = sf.GetFileLineNumber();
                MethodBase  method      = sf.GetMethod();
                string      methodName  = "";
                string      typeName    = "";

                if(method != null)
                {
                    methodName  = method.Name;
                    typeName    = method.ReflectedType != null ? method.ReflectedType.Name : "";
                }

                if(string.IsNullOrWhiteSpace(fileName) == false)
                {
                    fileName = fileName.Replace("\\", "/");
                    fileName = System.IO.Path.GetFileName(fileName);
                }

                stackLog = fileName + ":" + typeName + ":" + methodName + ":" + lineNumber;
            }

            return stackLog;
        }
    }

    internal partial class HoneycombTrackingAdapter
    {
        #region Base Data

        internal const string RESULT_CODE = "result.code";
        internal const string RESULT_MESSAGE = "result.message";
        internal const string REQUEST_BASE_URL = "request.base_url";
        internal const string REQUEST_METHOD = "request.method";
        internal const string REQUEST_PATH = "request.path";
        internal const string RESPONSE_CODE = "response.code";
        internal const string RESPONSE_MESSAGE = "response.message";
        internal const string RESPONSE_DETAIL = "response.detail";
        internal const string STATUS_CODE = "response.status_code";
        internal const string SONG_ID = "song_id";
        internal const string SLOT_ID = "slot_id";
        internal const string DEVICE_MODEL = "device.device_model";
        internal const string DEVICE_NAME = "device.device_name";
        internal const string DEVICE_TYPE = "device.device_type";
        internal const string DEVICE_UNIQUE_IDENTIFIER = "device.device_unique_identifier";
        internal const string OPERATING_SYSTEM = "device.operating_system";
        internal const string OPERATING_SYSTEM_FAMILY = "device.operating_system_family";
        internal const string PROCESSOR_TYPE = "device.processor_type";
        internal const string BATTERY_LEVEL = "device.battery_level";
        internal const string BATTERY_STATUS = "device.battery_status";
        internal const string PROCESSOR_COUNT = "device.processor_count";
        internal const string PROCESSOR_FREQUENCY = "device.processor_frequency";
        internal const string SUPPORTS_ASYNC_COMPUTE = "device.supports_async_compute";
        internal const string SUPPORTS_AUDIO = "device.supports_audio";
        internal const string SYSTEM_MEMORY_SIZE = "device.system_memory_size";
        internal const string DEVICE_ADVERTISING_ID = "device.device_advertising_id";
        internal const string DEVICE_IP = "device.device_ip";
        internal const string ACM_INITIALIZED_TIME = "app.acm_initialized_time";
        internal const string ACM_FIRST_INITIALIZED_TIME = "app.acm_first_initialized_time";
        internal const string ACM_SESSION_TIME = "app.acm_session_time";
        internal const string APP_ACM_ID = "app.acm_id";
        internal const string APP_PRODUCT_CODE = "app.product_code";
        internal const string APP_PRODUCT_CODE_FULL = "app.product_full_code";
        internal const string ACM_VERSION = "app.acm_version";
        internal const string ACM_VERSION_META = "app.acm_version_meta";
        internal const string BUILD_GUID = "app.build_guid";
        internal const string GENUINE = "app.genuine";
        internal const string GENUINE_CHECK_AVAILABLE = "app.genuine_check_available";
        internal const string IDENTIFIER = "app.identifier";
        internal const string INSTALLER_NAME = "app.installer_name";
        internal const string IS_EDITOR = "app.is_editor";
        internal const string PLATFORM = "app.platform";
        internal const string PRODUCT_NAME = "app.product_name";
        internal const string SYSTEM_LANGUAGE = "app.system_language";
        internal const string LOCALE = "app.locale";
        internal const string UNITY_VERSION = "app.unity_version";
        internal const string APP_VERSION = "app.app_version";
        internal const string ABSOLUTE_URL = "app.absolute_url";
        internal const string CLOUD_PROJECT_ID = "app.cloud_project_id";
        internal const string COMPANY_NAME = "app.company_name";
        internal const string INSTALL_MODE = "app.install_mode";
        internal const string INTERNET_REACHABILITY = "app.internet_reachability";
        internal const string IS_FOCUSED = "app.is_focused";
        internal const string IS_PLAYING = "app.is_playing";
        internal const string IS_CONSOLE_PLATFORM = "app.is_console_platform";
        internal const string IS_MOBILE_PLATFORM = "app.is_mobile_platform";
        internal const string RUN_IN_BACKGROUND = "app.run_in_background";
        internal const string CONSOLE_LOG_PATH = "app.console_log_path";
        internal const string PERSISTENT_DATA_PATH = "app.persistent_data_path";
        internal const string STREAMING_ASSETS_PATH = "app.streaming_assets_path";
        internal const string TEMPORARY_CACHE_PATH = "app.temporary_cache_path";
        internal const string TARGET_FRAME_RATE = "app.target_frame_rate";

        internal const string DEVICE_TS = "device_ts";

        #endregion

        internal const string LIB_HONEYCOMB_NAME = "honeycomb.lib_name";
        internal const string LIB_HONEYCOMB_VERSION = "honeycomb.lib_version";
        internal const string ENDPOINT = "endpoint";
        internal const string SPAN_ID = "trace.span_id";
        internal const string PARENT_ID = "trace.parent_id";
        internal const string TRACE_ID = "trace.trace_id";
        internal const string NAME = "name";
        internal const string SERVICE_NAME = "service.name";
        internal const string DURATION_MS = "duration_ms";
        internal const string DELTA_TIMESTAMP = "deltaTimestamp";
        internal const string CONTENT_LENGTH = "content_length";
        internal const string CONTENT_ENCODING = "content_encoding";
        internal const string REGION_CODE = "region_code";
        internal const string FIREBASE_CONFIG_FETCHED = "firebase_config_fetched";

        #region Search Song

        internal const string LIMIT = "search.limit";
        internal const string KEYWORD = "search.keyword";
        internal const string PLAYLIST = "search.playlist";
        internal const string SORT_BY = "search.sortBy";
        internal const string SORT_TYPE = "search.sortType";
        internal const string TYPE = "search.type";
        internal const string WHERE = "search.where";
        internal const string ORDER_BY = "search.orderBy";
        internal const string IS_CACHE = "is_cache";
        internal const string ENDPOINT_PATH = "search.endpoint_path";
        internal const string ENDPOINT_HOST_NAME = "search.endpoint_hostname";
        internal const string SEARCH_TOKEN = "search.search_token";
        internal const string RESULT_COUNT = "search.result.count";
        internal const string RESULT_PER_PAGE = "search.result.song_per_page";
        internal const string RESULT_CACHE_SUCCEEDED = "search.result.cache_succeeded";
        internal const string SEARCH_RESULT_FROM_CACHE = "search.result.from_cache";
        internal const string SEARCH_RESULT_OVERRIDE_COUNT = "search.result.overrided_count";

        #endregion

        #region Error

        internal const string ERROR = "error";

        // There is a space character at the end or error.message property because this value name on HoneyComb server has space characte too.
        internal const string ERROR_MESSAGE = "error.message ";
        internal const string ERROR_STACK_TRACE = "error.stacktrace";

        #endregion

        #region Download

        internal const string DOWNLOAD_FILE_TYPE = "download.file_type";
        internal const string DOWNLOAD_SONG_NAME = "download.song_name";
        internal const string DOWNLOAD_CDN = "download.cdn";
        internal const string DOWNLOAD_CDN_NAME = "download.cdn_name";
        internal const string DOWNLOAD_RETRY_TIME = "download.retry_time";
        internal const string DOWNLOAD_TTFB = "download.ttfb_ms";
        internal const string DOWNLOAD_LINK = "download.download_link";
        internal const string DOWNLOAD_COMPLETE_TIME = "download.complete_time_ms";
        internal const string DOWNLOAD_FILE_SIZE = "download.file_size_bytes";
        internal const string DOWNLOAD_SPEED = "download.download_speed_bps";
        internal const string DOWNLOAD_REASON = "download.reason";
        internal const string DOWNLOAD_CONDITION = "download.condition";
        internal const string DOWNLOAD_RESULT = "download.result";
        internal const string DOWNLOAD_METHOD = "download.method";

        #endregion

        #region Override Data

        internal const string OVERRIDE_DATA_HASH = "override.data_hash";
        internal const string OVERRIDE_DATA_SIZE = "override.data_size_byte";
        internal const string OVERRIDE_SONG_COUNT = "override.song_count";
        internal const string OVERRIDE_DATA_HEADER = "override.data_header";

        #endregion

        internal const string SAMPLING_VALUE = "sampling_value";
        internal const string APP_PAUSE_SYSTEM_TIME_MS = "app.pause_system_time_ms";
        internal const string APP_RESUME_SYSTEM_TIME_MS = "app.resume_system_time_ms";
        internal const string APP_PAUSE_UNITY_TIME_MS = "app.pause_unity_time_ms";
        internal const string APP_RESUME_UNITY_TIME_MS = "app.resume_unity_time_ms";
        internal const string APP_PAUSE_DURATION_MS = "app.pause_duration_ms";
        internal const string APP_USER_MINIMIZED = "app.user_minimized";
        internal const string APP_IS_CACHE = "app.is_cache";
        internal const string START_TIME = "startTime";
        internal const string TIMESTAMP = "timestamp";
    }
}