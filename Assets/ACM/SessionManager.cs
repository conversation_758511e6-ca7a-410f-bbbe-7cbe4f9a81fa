using Music.ACM.Playback;
using UnityEngine;

public class GameSession : Session
{
    public void Thredshold_Hit(int threshold)
    {
        AnalyticsUtils.Log_ThresholdHit(this, threshold);
    }

    public void Start()
    {
        if (GameController.enableEndless)
        {
            AnalyticsUtils.Log_Session_Start(this, AnalyticsUtils.VALUE_SONG_START_ENDLESS_LOOP);
        }
        else
        {
            AnalyticsUtils.Log_Session_Start(this, AnalyticsUtils.VALUE_SONG_START_FIRST);
        }
    }

    public void Stop(string reason)
    {
        AnalyticsUtils.Log_Session_End(this, reason);
    }

    public void Pause(string reason)
    {
        AnalyticsUtils.Log_SessionPause(this, reason);
    }

    public void Resume(string reason)
    {
        AnalyticsUtils.Log_SessionResume(this, reason);
    }

    public GameSession(string acmId, string contentUrl, string songName, AudioSource source, float startTime) : base(acmId, contentUrl, songName, source, startTime)
    {

    }

    public GameSession(string acmId, string contentUrl, string songName, ISuperPowered superPowered, float startTime) : base(acmId, contentUrl, songName, superPowered, startTime)
    {

    }
}

public class GameSessionManager : SessionManager
{
    private static GameSessionManager _instance;
    private static bool _isCreated = false;
    private bool _isStarted = false;
    private GameSession _gameSession;

    public static GameSessionManager Instance
    {
        get
        {
            if (!Application.isPlaying)
            {
                return null;
            }
            if (_isCreated)
                return _instance;

            if (_instance == null)
            {
                GameObject gameObject = new GameObject("GameSessionManager");
                GameObject.DontDestroyOnLoad(gameObject);

                _instance = gameObject.AddComponent(typeof(GameSessionManager)) as GameSessionManager;
                _isCreated = true;
            }

            return _instance;
        }
    }
    protected void OnDestroy()
    {
        if (!Application.isPlaying)
        {
            return;
        }
        if (_instance != null && _instance.gameObject == gameObject)
        {
            Destroy(_instance);

            _instance = null;
            _isCreated = false;
        }
    }

    void Update()
    {
        if(_gameSession != null && _isStarted)
        {
            float curTime = _gameSession.UpdatePlaybackPosition();

            if(curTime - _lastTime >= 1.0f) //Check for each 1 sec
            {
                _lastTime = curTime;

                float duration = curTime - _startTime;

                this.CountingTime((int)duration);
            }
        }
    }

    private void CountingTime(int time)
    {
        int index = _gameSession.ThresholdIndex;

        if(index < _thresholds.Length)
        {
            int threshold = _thresholds[index];

            if (time >= threshold)
            {
                _gameSession.ThresholdIndex ++;

                _gameSession.Thredshold_Hit(threshold * 1000);  //to milisec
            }
        }
    }

    public void StartGame(string acmId, string contentUrl, string songName, AudioSource audioSource,
        float startTime)
    {
        _gameSession = new GameSession(acmId, contentUrl, songName, audioSource, startTime);
        _length = audioSource.clip.length;
        _startTime = startTime;
        _lastTime = 0;

        _gameSession.Start();
        _isStarted = true;
    }

    public void PauseGame(string reason)
    {
        _gameSession?.Pause(reason);
    }

    public void ResumeGame(string reason)
    {
        _gameSession?.Resume(reason);
    }

    public void StopGame(string reason)
    {
        _gameSession?.Stop(reason);
    }
}