using System;
using UnityEngine;
using UnityEngine.UI;

public class ContinueByGemUI : ContinueUI {
    [SerializeField] protected CanvasGroup mainCanvasGroup;
    [SerializeField] protected Button      btnVipActivate;
    [SerializeField] protected Button      btnGemsContinue;
    [SerializeField] protected Button      btnClose;
    [SerializeField] protected Text        gemsPriceText;
    [SerializeField] protected GameObject  currentGemsContainer;
    [SerializeField] protected Button      btnDiamondShop;
    [SerializeField] protected Text        currentGemsText;
    public                     Vector2     offset;

    [Space]
    [SerializeField] private GridLayoutGroup buttonBuyGridGroup;
    
    protected override void Awake() {
        ProcessGemPricesConfig();
        btnVipActivate.onClick.AddListener(BtnVipContinueClick);
        btnGemsContinue.onClick.AddListener(BtnGemsContinueClick);
        btnDiamondShop.onClick.AddListener(ShowIapShop);
        btnClose.onClick.AddListener(Cancel);
        base.Awake();
    }

    protected override void OnEnable() {
        mainCanvasGroup.interactable = true;
        UpdateCurrentGemsText();
        base.OnEnable();
        
        UpdateReviveConfig();
        UpdateReviveOptionsButtons();
        SetGemPriceText();
    }

    private void Start() {
        // tắt object current gems nếu top bar đã show rồi
        if (TopBar.instance.IsShowDiamonds) {
            currentGemsContainer.SetActive(false);
        }
    }
    
    protected void UpdateReviveOptionsButtons() {
        switch (_reviveType) {
            case ReviveType.AD:
                btnGemsContinue.gameObject.SetActive(false);
                btnVideoContinue.gameObject.SetActive(true);
                break;

            case ReviveType.GEMS:
                btnGemsContinue.gameObject.SetActive(true);
                btnVideoContinue.gameObject.SetActive(false);
                break;

            case ReviveType.AD_GEMS:
                btnGemsContinue.gameObject.SetActive(true);
                btnVideoContinue.gameObject.SetActive(true);
                break;
        }
    }

    protected override void ActiveBtnVipRevive(bool isActive, bool isFree) {
        base.ActiveBtnVipRevive(isActive, isFree);
        
        if (isActive) {
            buttonBuyGridGroup.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
        } else {
            buttonBuyGridGroup.constraint = GridLayoutGroup.Constraint.FixedRowCount;
        }
    }

    protected void SetGemPriceText() {
        gemsPriceText.text = _continueGemsPrice.ToString();
    }
    
    private void StartGemsChangeEffect(int change, Action onDone) {
        UIOverlay.instance.ShowVFXSubDiamond(
            change, 
            btnGemsContinue.transform.position, 
            offset,
            onDone);
    }
    
    protected void BtnGemsContinueClick() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Continue_Gems);

        isCounting = false;
        if (Configuration.instance.GetDiamonds() >= _continueGemsPrice) {
            mainCanvasGroup.interactable = false;
            Configuration.UpdateDiamond(-_continueGemsPrice, CONFIG_STRING.Revive);
            UpdateCurrentGemsText();
            StartGemsChangeEffect(_continueGemsPrice, () => DoGameContinue("currency"));
            
            if (_continueGemsPrice > 0) {
                AirfluxTracker.TrackSpendCredits(_continueGemsPrice);
            }        
        } else {
            Util.ShowIAPShop(LOCATION_NAME.revive);
        }
    }

    private void ShowIapShop() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Shop);
        isCounting = false;
        Util.ShowIAPShop(LOCATION_NAME.revive);
    }

    public override void EndWaitPay() {
        isCounting = true;
        UpdateCurrentGemsText();
    }

    private void UpdateCurrentGemsText() {
        currentGemsText.text = Configuration.instance.GetDiamonds().ToString();
    }
}