using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.LiveEvent.BallSpinner {
    public class ItemSpinner : MonoBehaviour {
        public                   Transform transformCached;
        [SerializeField] private Image     img;

        [SerializeField] private GameObject objBGWin;
        [SerializeField] private GameObject objVFX;

        private Tween _tweenScale;
        private int   _idBall;
        private bool  _isActiveBall;

        public void SetId(int id, MainSpinner.UISpinItem data) {
            if (data != null) {
                _idBall = data.idBall;
                _isActiveBall = data.cangetReward;
                img.sprite = data.icon;
                img.color = data.cangetReward ? Color.white : Color.gray;
            } else {
                img.sprite = null;
                img.color = id switch {
                    1 => new Color32(129, 211, 248, 100),
                    2 => new Color32(236, 128, 141, 100),
                    3 => new Color32(245, 154, 35, 100),
                    4 => new Color32(194, 128, 255, 100),
                    5 => new Color32(202, 149, 130, 100),
                    6 => new Color32(217, 0, 27, 100),
                    7 => new Color32(109, 0, 14, 100),
                    8 => new Color32(0, 0, 191, 100),
                    BallSpinnerData.ID_SPECIAL_ITEM => new Color32(134,0,210,100),
                    _ => img.color
                };
            }

            SetActiveVFX(false);
        }

        public void SetActiveVFX(bool isActive) {
            if (objVFX) {
                objVFX.SetActive(isActive);
            }

            if (objBGWin) {
                objBGWin.SetActive(isActive);
            }

            if (isActive) {
                _tweenScale ??= img.transform.DOScale(1.1f, 0.5f).SetLoops(-1, LoopType.Yoyo);
            } else {

                _tweenScale?.Kill();
            }
        }

        public void Refresh() {
            if (_isActiveBall)
                return;
            _isActiveBall = Configuration.IsOpenBall(_idBall);
            img.color = _isActiveBall ? Color.white : Color.gray;
        }
    }
}