using System;
using System.Collections.Generic;
using System.Globalization;
using System.Reflection;
using Newtonsoft.Json;
using UnityEngine;

namespace TilesHop.LiveEvent.BallSpinner {
    /// <summary>
    /// Load & Save data config of Ball Spinner
    /// </summary>
    public class BallSpinnerStore {
        public          List<StoreItemData> itemsData;
        protected       bool                isLoadedConfig        = false;
        protected const string              KEY_DATA              = "liveevent_ballspinner_store";
        protected const string              KEY_ORDER_BOUGHT_ITEM = "liveevent_ballspinner_store_order_bought_item";
        protected const string              KEY_LAST_RESET_TIME   = "BallSpinnerStore_LastResetTimeDaily";

        private const string DEFAULT_CONFIG = "id,content,rewardType,value,rewardID,exchangeCount,costType,cost,rarity||" +
                                              "1,Ticket 1,TicketDaily,1,0,2,Ads,1,special||" +
                                              "2,Ticket 2,Ticket,2,0,1,Coin,50,rare||" +
                                              "3,Diamond Pack (100),Gem,100,0,2,Coin,450,rare||" +
                                              "4,Diamond Pack (150),Gem,150,0,2,Coin,350,||" +
                                              "5,Diamond Pack (250),Gem,250,0,3,Ads,250,||" +
                                              "6,Diamond Pack (350),Gem,350,0,4,Ticket,10,||" +
                                              "7,Diamond Pack (1),Gem,1,0,99,Coin,100,||" +
                                              "8,,Song,0,cce96830-5bb6-4002-8f8e-0a12be7464cd,1,Coin,500,||" +
                                              "9,Skin 1,Skin,0,126,1,Coin,150||" + "10,Skin 2,Skin,0,99,1,Ticket,20,";

        public BallSpinnerStore() {
            LoadData();
        }

        protected void LoadData() {
            if (isLoadedConfig) {
                return;
            }

            string cachedData = PlayerPrefs.GetString(KEY_DATA);
            if (string.IsNullOrEmpty(cachedData)) {
                string config = RemoteConfigBase.instance.LiveEvent_BallSpinner_Store;

                if (string.IsNullOrEmpty(config)) {
                    config = DEFAULT_CONFIG;
                }

                string[,] grid = CSVReader.SplitCsvGrid(config.Replace("||", "\n"), true);
                itemsData = GetItemsData(grid);
                if (itemsData.Count > 0) {
                    SaveData();
                } else {
                    CustomException.Fire("[Ball Spinner Store] LoadData", "Cannot get config from " + config);
                }
            } else {
                itemsData = JsonConvert.DeserializeObject<List<StoreItemData>>(cachedData);
            }

            isLoadedConfig = true;
        }

        public void SaveData() {
            if (itemsData == null || itemsData.Count == 0) {
                LoadData();
            }

            string strMissionDays = JsonConvert.SerializeObject(itemsData);
            PlayerPrefs.SetString(KEY_DATA, strMissionDays);
        }

        private List<StoreItemData> GetItemsData(string[,] dataCsv) {
            List<StoreItemData> items = new();

            Dictionary<int, string> header = CSVReader.GetHeader(dataCsv);
            for (int row = 1; row < dataCsv.GetUpperBound(1)+1; row++) {
                StoreItemData item = new();

                for (int column = 0; column < header.Count; column++) {
                    string columnName = header[column];
                    string cell = dataCsv[column, row];

                    if (cell != null && header.ContainsKey(column)) {
                        string value = cell.Trim();
                        FieldInfo field = typeof(StoreItemData).GetField(columnName);
                        if (field != null) {
                            CSVReader.SetFieldValue(item, field, value);
                        }
                    }
                }

                items.Add(item);
            }

            return items;
        }

        public static void ClearUserData() {
            PlayerPrefs.DeleteKey(KEY_DATA);
            PlayerPrefs.DeleteKey(KEY_ORDER_BOUGHT_ITEM);
        }

        public static int GetOrderBoughtItem() {
            return PlayerPrefs.GetInt(KEY_ORDER_BOUGHT_ITEM, 1);
        }

        public static void SetOrderBoughtItem(int value) {
            PlayerPrefs.GetInt(KEY_ORDER_BOUGHT_ITEM, value);
        }

        private static readonly TimeSpan _resetTime = new (0, 0, 0); //12 am
        private static          DateTime _lastDailyResetTime;
        private static          bool     _isInitedLastResetTime;
        public static bool IsNewDay() {
            if (!_isInitedLastResetTime) {
                InitLastResetTime();
            }
            
            var currentTime = DateTime.Now;
            bool isNewDay = currentTime.TimeOfDay > _resetTime && _lastDailyResetTime.Date != currentTime.Date;
            if (isNewDay) {
                _lastDailyResetTime = currentTime;
                PlayerPrefs.SetString(KEY_LAST_RESET_TIME, currentTime.ToString(CultureInfo.InvariantCulture));
            }
            
            return isNewDay;
        }

        protected static void InitLastResetTime() {
            if (!PlayerPrefs.HasKey(KEY_LAST_RESET_TIME)) {
                _lastDailyResetTime = DateTime.Now;
                PlayerPrefs.SetString(KEY_LAST_RESET_TIME, _lastDailyResetTime.ToString(CultureInfo.InvariantCulture));
            } else {
                _lastDailyResetTime = DateTime.Parse(PlayerPrefs.GetString(KEY_LAST_RESET_TIME));
            }
        }
    }
}