using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.LiveEvent.BallSpinner {
    public class UIItemMission : MonoBehaviour {
        [SerializeField] private GameObject objRewardCoin;
        [SerializeField] private GameObject objRewardTicket;
        [SerializeField] private Text       txtReward;
        [SerializeField] private Text       txtMission;

        [SerializeField] private Image imgProgress;
        [SerializeField] private Text  txtProgress;

        [SerializeField] private Button btnGo;
        [SerializeField] private Button btnClaim;

        [SerializeField] private GameObject tagClaimed;
        [SerializeField] private GameObject btnClaimed;
        [SerializeField] private Button     btnProgress;

        private bool           _isDailyMission;
        private MissionData    _mission;
        private VFXSubCurrency _subCurrency;

        private void Start() {
            btnGo.onClick.AddListener(OnBtnGoClicked);
            btnClaim.onClick.AddListener(OnBtnClaimClicked);
            btnProgress.onClick.AddListener(OnBtnInprogressClick);
        }

        public void SetData(bool isDailyMisison, MissionData missionData, VFXSubCurrency subCurrency) {
            this._isDailyMission = isDailyMisison;
            this._mission = missionData;
            this._subCurrency = subCurrency;
            this.txtReward.text = $"+{_mission.reward}";
            this.txtMission.text = Mission.GetTitle(_mission.type, _mission.count);
            objRewardCoin.SetActive(_mission.rewardType == RewardType.Coin);
            objRewardTicket.SetActive(_mission.rewardType == RewardType.Ticket ||
                                      _mission.rewardType == RewardType.TicketDaily);
            UpdateUI();
        }

        private void UpdateUI() {
            if (_mission.isComplete) {
                txtProgress.text = $"{_mission.count}/{_mission.count}";
                imgProgress.fillAmount = 1f;
            } else {
                txtProgress.text = $"{_mission.progress}/{_mission.count}";
                imgProgress.fillAmount = _mission.progress / (float) _mission.count;
            }

            btnClaim.gameObject.SetActive(_mission.isComplete && !_mission.isRewarded);
            btnClaimed.SetActive(_mission.isComplete && _mission.isRewarded);
            tagClaimed.SetActive(_mission.isComplete && _mission.isRewarded);

            if (!_mission.isComplete && _mission.type != MissionType.login_x_days) {
                btnGo.gameObject.SetActive(true);
            } else {
                btnGo.gameObject.SetActive(false);
            }
        }

        private void OnBtnClaimClicked() {
            SoundManager.PlayGameButton();
            if (_isDailyMission) {
                BallSpinnerManager.instanceSafe.GetRewardMission(_mission);
            } else {
                BallSpinnerManager.instanceSafe.GetRewardAchievement(_mission);
            }

            _subCurrency.ShowVfx(_mission.rewardType, _mission.reward, objRewardCoin.transform.position);
        }

        private void OnBtnInprogressClick() {
            if (!Configuration.instance.isDebug) {
                return;
            }

            if (_mission.isComplete) {
                return;
            }

            //only for admin to test
            switch (_mission.type) {
                case MissionType.get_x_stars:
                    BallSpinnerManager.instanceSafe.DoMission(_mission.type, "1");
                    break;

                case MissionType.get_at_least_x_point:
                case MissionType.get_x_perfect:
                    BallSpinnerManager.instanceSafe.DoMission(_mission.type, "9999");
                    break;

                default:
                    BallSpinnerManager.instanceSafe.DoMission(_mission.type, "dance");
                    break;
            }

            GetComponentInParent<UIBallSpinnerMission>().ReloadData();
        }

        private void OnBtnGoClicked() {
            SoundManager.PlayGameButton();
            Mission.GoToAction(_mission.type, "ballspinner_mission", string.Empty);
        }
    }
}