using System;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using TilesHop.Cores.Pooling;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TilesHop.LiveEvent.BallSpinner {
    public class SpinWheelColumn : MonoBehaviour {
        [SerializeField]            private ItemSpinner[] items;
        [ShowInInspector, ReadOnly] private byte          _indexRow;
        [ShowInInspector, ReadOnly] private List<byte>    _data;

        private MainSpinner _spinner;

        private readonly int[] localPos    = new int[] {-120, -40, 40, 120, 200};
        private          float sizeItem    = 73;
        private          float paddingItem = 7;
        private          int   totalItem   = 5;

        private float _deltaRecycle;
        private float _recyclePos;
        private float _lengthOfRound;
        private int   _round;
        private float _distanceMoved;
        private float _targetDistance;
        private float _remainDistance;
        private float _timeToStop;
        private float _remainMoved;

        [ShowInInspector, ReadOnly] private int _curIndex;
        [ShowInInspector, ReadOnly] private int _targetIndex;

        private float _currentSpeed;
        private float _maxSpeed;
        private float _timeSpin;
        private float _timeToMax = 0.2f;

        private void OnEnable() {
            _deltaRecycle = totalItem * (sizeItem + paddingItem);
            _recyclePos = _deltaRecycle / 2f;
        }

        public void SetData(MainSpinner spinner, byte index, List<byte> data) {
            this._spinner = spinner;
            this._indexRow = index;
            this._data = data;
            ResetItems();
        }

        public void SetData(List<byte> data) {
            this._data = data;
        }

        private void ResetItems() {
            for (byte i = 0; i < items.Length; i++) {
                items[i].transformCached.localPosition = new Vector3(localPos[i], 0, 0);
                items[i].SetId(_data[i], _spinner.GetData(_data[i]));
            }

            _curIndex = 0;
        }
        private void ClearVFXItems() {
            for (byte i = 0; i < items.Length; i++) {
                items[i].SetActiveVFX(false);
            }
        }

        public void Spin(int targetIndex, float maxSpeed, float delay, float spinTime, Action onDone) {
            if (targetIndex >= _data.Count) {
                targetIndex -= _data.Count;
            }

            if (targetIndex < 0) {
                targetIndex += _data.Count;
            }

            _targetIndex = targetIndex;
            _currentSpeed = 0f;
            _maxSpeed = maxSpeed;
            _timeSpin = spinTime;
            
            
            ClearVFXItems();
            StartCoroutine(IESpin(delay, onDone));
        }

        private IEnumerator IESpin(float delay, Action onDone) {
            yield return YieldPool.GetWaitForSeconds(delay);

            ResetItems();

            float time = 0f;
            _distanceMoved = 0f;

            while (time < _timeToMax) {
                time += Time.deltaTime;
                _currentSpeed = Mathf.Lerp(0, _maxSpeed, time / _timeToMax);
                _distanceMoved += MoveWithSpeed(_currentSpeed, _maxSpeed / _timeToMax);
                yield return null;
            }

            while (time < _timeSpin) {
                time += Time.deltaTime;
                _currentSpeed = _maxSpeed;
                _distanceMoved += MoveWithSpeed(_currentSpeed, 0f);
                yield return null;
            }

            _lengthOfRound = _data.Count * (sizeItem + paddingItem);
            _round = (int) (_distanceMoved / _lengthOfRound);

            _targetDistance = (_round + 2) * _lengthOfRound - _targetIndex * (sizeItem + paddingItem);
            _remainDistance = _targetDistance - _distanceMoved;

            _timeToStop = _remainDistance * 2f / _maxSpeed;
            time = 0;

            float a = _maxSpeed * _maxSpeed / (2 * _remainDistance);
            float deltaSpeed = 0f;
            while (time < _timeToStop) {
                time += Time.deltaTime;
                deltaSpeed = a * Time.deltaTime;
                _currentSpeed -= deltaSpeed;
                _distanceMoved += MoveWithSpeed(_currentSpeed, a);
                yield return null;
            }

            _remainMoved = _targetDistance - _distanceMoved;
            MoveWithS(_remainMoved);
            if (!_curIndex.Equals(_targetIndex)) {
                _curIndex = _targetIndex;
            }

            onDone?.Invoke();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="speed">vận tốc tức thời</param>
        /// <param name="a">gia tốc tức thới</param>
        /// <returns></returns>
        private float MoveWithSpeed(float speed, float a) {
            var deltaTime = Time.deltaTime;

            // công thức chuyển động biến đổi đều
            float distanceMove = speed * deltaTime + 0.5f * a * deltaTime * deltaTime;

            Vector3 vectorMove = distanceMove * Vector3.right;
            for (int i = 0; i < items.Length; i++) {
                items[i].transformCached.localPosition += vectorMove;
                if (items[i].transformCached.localPosition.x >= _recyclePos) {
                    items[i].transformCached.localPosition -= _deltaRecycle * Vector3.right;
                    if (_curIndex == 0) {
                        _curIndex = _data.Count - 1;
                    } else {
                        _curIndex -= 1;
                    }

                    items[i].SetId(_data[_curIndex], _spinner.GetData(_data[_curIndex]));
                }
            }

            return distanceMove;
        }

        private void MoveWithS(float s) {
            Vector3 vectorMove = s * Vector3.right;
            for (int i = 0; i < items.Length; i++) {
                items[i].transformCached.localPosition += vectorMove;
            }
        }

        public byte GetIndexOf(byte target, byte offset) {
            for (byte i = 0; i < _data.Count; i++) {
                if (_data[(i + offset) % _data.Count] == target)
                    return i;
            }

            return 0;
        }

        public byte GetRandomIndex() {
            return (byte) (Random.Range(0, 999) % _data.Count);
        }

        public byte[] GetCurrentIndexs(byte lenght) {
            var indexs = new byte[lenght];
            for (byte i = 0; i < lenght; i++) {
                indexs[i] = _data[(i + _curIndex) % _data.Count];
            }

            return indexs;
        }

        public Transform ShowVFXItemWin(byte indexInRow) {
            float offset = localPos[indexInRow];
            foreach (ItemSpinner item in items) {
                if (Mathf.Abs(item.transformCached.localPosition.x - offset) < 5) {
                    item.SetActiveVFX(true);
                    return item.transform;
                }
            }
            return null;
        }

        public void RefreshItems() {
            for (byte i = 0; i < items.Length; i++) {
                items[i].Refresh();
            }
        }
    }
}