using System.Collections;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.LiveEvent.BallSpinner {
    public class VFXSubCurrency : MonoBehaviour {
        [SerializeField] private Animator   mainAnimator;
        [SerializeField] private GameObject objCoin;
        [SerializeField] private GameObject objTicket;
        [SerializeField] private Text       txtMessage;

        private Coroutine _coroutine;

        public void ShowVfx(RewardType type, int amount, Vector3 position) {
            this.transform.position = position;
            objCoin.SetActive(type == RewardType.Coin);
            objTicket.SetActive(type is RewardType.Ticket or RewardType.TicketDaily);
            txtMessage.text = amount > 0 ? $"+{amount}" : $"{amount}";
            if (_coroutine != null) {
                StopCoroutine(_coroutine);
            }

            this.gameObject.SetActive(true);
            mainAnimator.SetTrigger(amount > 0 ? "Add" : "Sub");
            _coroutine = StartCoroutine(IEClose());
        }

        private IEnumerator IEClose() {
            yield return YieldPool.GetWaitForSeconds(1f);

            this.gameObject.SetActive(false);
        }
    }
}