using System.Collections.Generic;
using System.Text;
using TilesHop.GameCore.ShopBallRevamp;
using UnityEngine;

public class ShopBallTracking : MonoBehaviour {
    #region Tracking Name

    private const string Prefix = "shop_ball";
    private const string Middle = "_";

    public enum EventName {
        impression,
        select,
        unlock,
        vip_click,
        vip_purchase
    }

    public enum Location {
        home   = 0,
        ingame = 1
    }

    #endregion

    public static void FireEvent(Location screen, string location, EventName eventName, int ballId = -1,
                                 string previousLocation = null) {
        Dictionary<string, object> param = AnalyticHelper.GetDefaultParam(location, true);

        switch (eventName) {
            case EventName.impression:
                bool isHuman = BallManager.itemsHuman.Contains(ballId);
                param[TRACK_NAME.category] = (isHuman ? ShopScript.Tabs.Character : ShopScript.Tabs.Ball).ToString();
                param[TRACK_NAME.user_VIP_status] = SubscriptionController.IsSubscriptionVip() ? "Y" : "N";
                break;

            case EventName.select:
            case EventName.unlock:
                param.Add(TRACK_NAME.item_id, ballId.ToString());
                var ballConfig = BallManager.instance.GetBallConfig(ballId);
                if (ballConfig != null) {
                    param.Add(TRACK_NAME.item_name, ballConfig.name);
                }

                if (RemoteConfigBase.instance.ShopBall_UseCategoryLayout) {
                    param.Add("category", BallCategoryScroller.CurrentCategory);
                }

                break;
        }

        if (!string.IsNullOrEmpty(previousLocation)) {
            param.Add("previous_location", previousLocation);
        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.Append(Prefix);
        stringBuilder.Append(Middle);
        stringBuilder.Append(screen);
        stringBuilder.Append(Middle);
        stringBuilder.Append(eventName);
        AnalyticHelper.LogEvent(stringBuilder.ToString(), param);
    }

    public static void VipPurchase(string source) {
        if (source == SUBSCRIPTION_SCREEN.shop_ball_inhome.ToString()) {
            FireEvent(Location.home, "home_skinshop", EventName.vip_purchase);
        } else if (source == SUBSCRIPTION_SCREEN.shop_ball_ingame.ToString()) {
            FireEvent(Location.ingame, "gameplay_skinshop", EventName.vip_purchase);
        }
    }
}