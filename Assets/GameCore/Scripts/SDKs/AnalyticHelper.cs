using System.Collections.Generic;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;
using System;
using System.Drawing.Text;
using System.Globalization;
using System.Linq;
using System.Security.Principal;
using Firebase.Analytics;
using Firebase.Extensions;
using Music.ACM;
using Music.ACM.Playback;
using UnityEngine.Analytics;
using System.Text;
using System.Text.RegularExpressions;
using GameCore.LiveEvent.MysteryDoor;
using GamePlay.Levels;
using Inwave;
using Inwave.CommunicationSystem.LocalSegmentation;
using TilesHop.ChallengeOldUser;
using TilesHop.Cores.Boosters;
using TilesHop.GameCore.ShopBallRevamp;
using TilesHop.LiveEvent;
using TilesHop.Cores.UserProgression;
using TilesHop.LiveEvent.GalaxyQuest;
using TilesHop.LiveEvent.MilestoneEvent;
using Utils = Inwave.Utils;
using UnityEngine.Pool;

public class AnalyticHelper : MonoBehaviour {
    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ static ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public static AnalyticHelper instance => SDKs.isInstanced ? SDKs.instance.analyticHelper : null;

    [ReadOnly] public LOCATION_NAME current_location;
    [ReadOnly] public LOCATION_NAME last_location;
    LOCATION_NAME                   current_popup;

    public TimePlayManager timePlayManager;
    static float           lastClickTime = 0;
    static float           bannerTime    = 0;

    static List<KeyValuePair<string, Parameter[]>> queueData = new List<KeyValuePair<string, Parameter[]>>();

    static bool          waitTutorialReplay = false;
    string               _isoCountryCode;
    const         string IsoCountryCodeChina = "CN";
    private const string strFirebaseEvent    = "[FirebaseEvent]: ";

    #region Unity Method

    protected void Awake() {
        StartCoroutine(SetAnalyticsOnLog(LogEvent));
    }

    private void Start() {
        CustomException.SetKey(FirebaseKey.CountryCode, GetIsoCountryCode() ?? "UNKNOWN");
        if (IsChina()) {
            RemoteConfigBase.instance.Enable_IAP = false;
            RemoteConfigBase.instance.Subscription_Enable = false;
        }
    }

    protected void OnDestroy() {
        string lastCloseTime = DateTime.Now.ToShortDateString();
        PlayerPrefs.SetString(UserProperties.prefix + CONFIG_STRING.last_close_time, lastCloseTime);
    }

    #endregion

    public static int GetSessionDay() {
        string lastCloseTime = PlayerPrefs.GetString(UserProperties.prefix + CONFIG_STRING.last_close_time);
        bool tryParse = DateTime.TryParse(lastCloseTime, out DateTime lastOpen);
        int sessionDay = tryParse ? DateTime.Now.DayOfYear - lastOpen.DayOfYear : 0;
        return sessionDay;
    }

    private IEnumerator SetAnalyticsOnLog(Action<string, Dictionary<string, object>> logEvent) {
        // Add timeout to prevent ANR
        float timeout = 15f;
        float elapsed = 0f;

        while (!ACMSDK.IsInitialized && elapsed < timeout) {
            yield return null;

            elapsed += Time.deltaTime;
        }

        if (ACMSDK.IsInitialized && ACMSDK.Instance != null) {
            ACMSDK.Instance.SetAnalyticsOnLog(logEvent);
        }
    }

    #region Core Functions

    public bool IsChina() {
        return GetIsoCountryCode() == IsoCountryCodeChina ||
               (RemoteConfigBase.isInstanced && RemoteConfigBase.instance.forceChinaUser);
    }

    public void GameLoaded(bool isFirstGame = false) {
        try {
            timePlayManager.GameLoaded(isFirstGame);

            UserProperties.InitProperties();
        } catch (Exception ex) {
            Debug.Log(ex.Message);
        }
    }

    private string GetIsoCountryCode() {
        if (string.IsNullOrEmpty(_isoCountryCode)) {
            _isoCountryCode = DeviceCarrierInfo.instanceSafe.GetIsoCountryCode();
        }

        return _isoCountryCode;
    }

    // public string GetCurrentLocation() {
    //     if (current_popup == LOCATION_NAME.POPUP_CLOSE) {
    //         return current_location.ToString();
    //     } else {
    //         return current_popup.ToString();
    //     }
    // }
    // public LOCATION_NAME GetCurrentLocationEnum() {
    //     if (current_popup == LOCATION_NAME.POPUP_CLOSE) {
    //         return current_location;
    //     } else {
    //         return current_popup;
    //     }
    // }
    public void SetCurrentLocation(LOCATION_NAME location) {
        if (Configuration.IsPrepareQuit()) {
            return;
        }

        Logger.EditorLog("[AnalyticHelper][SetCurrentLocation] " + location.ToString());
        if (location == LOCATION_NAME.shop || location == LOCATION_NAME.GAME_SETTINGS) {
            last_location = current_location;
        }

        current_location = location;
        current_popup = LOCATION_NAME.POPUP_CLOSE;
        Screen(current_location.ToString(), current_location.ToString());
        BannerUpdate();
    }

    public LOCATION_NAME GetCurrentLocation() {
        return current_location;
    }

    public void SetCurrentPopup(LOCATION_NAME location) {
        current_popup = location;
        Screen(location.ToString(), current_location.ToString());
        BannerUpdate();
    }

    public void SetPopupClose() {
        current_popup = LOCATION_NAME.POPUP_CLOSE;
        Screen(current_location.ToString(), current_location.ToString());
        BannerUpdate();
    }

    private void BannerUpdate() {
        if (LoadingScript.instance != null || !AdsManager.isInstanced || AdsManager.instance == null) {
            return;
        }

        if (RemoteConfigBase.instance == null) {
            return;
        }

        if (ShouldHideBanner()) {
            AdsManager.instance.HideBanner();
        } else {
            AdsManager.instance.ShowBanner();
        }
    }

    private bool ShouldHideBanner() {
        switch (current_location) {
            case LOCATION_NAME.BALL_LIST or LOCATION_NAME.BALL_LIST_INGAME
                when !RemoteConfigBase.instance.BannerAd_shopball:
            case LOCATION_NAME.result when !RemoteConfigBase.instance.BannerAd_result:
            case LOCATION_NAME.DOWNLOAD_SONG when !RemoteConfigBase.instance.BannerAd_loading:
            case LOCATION_NAME.TIPS when !RemoteConfigBase.instance.BannerAd_guideline:
            case LOCATION_NAME.revive when !RemoteConfigBase.instance.BannerAd_Continue:
            case LOCATION_NAME.shop when !RemoteConfigBase.instance.BannerAd_shop:
            case LOCATION_NAME.discover when !RemoteConfigBase.instance.BannerAd_discover:
            case LOCATION_NAME.hardcore_challenge when !RemoteConfigBase.instance.BannerAd_hardcorechallenge:
            case LOCATION_NAME.sevenday_mission when !RemoteConfigBase.instance.BannerAd_7daymission:
            case LOCATION_NAME.setting when !RemoteConfigBase.instance.BannerAd_setting:
            case LOCATION_NAME.leaderboard when !RemoteConfigBase.instance.BannerAd_leaderboard:
            case LOCATION_NAME.ACHIEVEMENT_POPUP when !RemoteConfigBase.instance.BannerAd_achievement:
            case LOCATION_NAME.gameplay or LOCATION_NAME.PREPARE_GAME when !RemoteConfigBase.instance.BannerAd_GamePlay:
            case LOCATION_NAME.sub_offer or LOCATION_NAME.Subscription_Intro
                when !RemoteConfigBase.instance.BannerAd_sub_offer:
            case LOCATION_NAME.Subscription_SeeAll when !RemoteConfigBase.instance.BannerAd_sub_catalog:
                return true;
        }

        switch (current_popup) {
            case LOCATION_NAME.AdFs_Reward when !RemoteConfigBase.instance.BannerAd_AfterAd:
            case LOCATION_NAME.EXIT_POPUP when !RemoteConfigBase.instance.BannerAd_ExitGame:
                return true;
        }

        if (IsHomeLocation() && !RemoteConfigBase.instance.BannerAd_Home) {
            return true;
        }

        return false;
    }

    private bool IsHomeLocation() {
        // mặc định không show banner ads ở shop iap và shop ball dạng tab ở home của bản UserProgression
        if (UserProgressionController.EnableFeature) {
            if (current_location is LOCATION_NAME.shop && Util.IsHomeScene()) {
                return true;
            }

            if (current_location is LOCATION_NAME.BALL_LIST) {
                return true;
            }
        }

        return current_popup == LOCATION_NAME.POPUP_CLOSE && (current_location == LOCATION_NAME.home ||
                                                              current_location == LOCATION_NAME.liveEvent);
    }

    void OnApplicationPause(bool pauseStatus) {
        UserProperties.OnApplicationPause(pauseStatus);
    }

    #endregion

    #region Analytic Functions

    public static float timeGameOpen = 0;

    public static void Game_Step(GAME_STEP step, bool isOneTimeWithTutorial = false, string errorMsg = null) {
        string eventName = step.ToString();

        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);

        if (!string.IsNullOrEmpty(errorMsg)) {
            param.TryAdd(TRACK_NAME.error, errorMsg);
            UpdateNetWorkState(param); //connection
        }

        if (step == GAME_STEP.User_RemoteConfigLoaded || step == GAME_STEP.Step_RemoteConfigLoadedFail) {
            int totalTryFetch = RemoteConfig.instance.GetTotalTryFetchRemoteConfig();
            param.TryAdd(TRACK_PARAM.total_try_fetch, totalTryFetch.ToString());
        }

        if (step == GAME_STEP.User_Game_Start) {
            timeGameOpen = Time.realtimeSinceStartup;
            param.TryAdd(TRACK_NAME.loading_time, 0);
        } else {
            param.TryAdd(TRACK_NAME.loading_time, Time.realtimeSinceStartup - timeGameOpen);
        }

        if (Configuration.instance.isTutorial) {
            LogEventTutorial(step, param);
            if (!isOneTimeWithTutorial) {
                //UpdateParamsAccumulatedCount(param, eventName);
                LogEvent(eventName, param);
            }
        } else {
            UpdateParamsAccumulatedCount(param, eventName);
            LogEvent(eventName, param);
        }

        if (step == GAME_STEP.User_Game_Start) {
            int updateCountEvent = UpdateCountEvent(FIRE_EVENT.app_open.ToString());
            if (updateCountEvent <= 10) {
                LogEvent(FIRE_EVENT.app_open + "_" + updateCountEvent);
            }

            if (updateCountEvent == 1) {
                //first time
                LogEvent(FIRE_EVENT.af_app_open.ToString());
            }
        }
    }

    private static void LogEventTutorial(GAME_STEP step, Dictionary<string, object> param) {
        switch (step) {
            case GAME_STEP.User_Game_Start:
                UpdateParamsAccumulatedCount(param, TRACK_NAME.FN_Game_Start);
                LogEvent(TRACK_NAME.FN_Game_Start, param);
                break;

            case GAME_STEP.User_Loading_Start:
                UpdateParamsAccumulatedCount(param, TRACK_NAME.FN_Loading_Start);
                LogEvent(TRACK_NAME.FN_Loading_Start, param);
                break;

            case GAME_STEP.User_RemoteConfigLoaded:
                UpdateParamsAccumulatedCount(param, TRACK_NAME.FN_RemoteConfigLoaded);
                LogEvent(TRACK_NAME.FN_RemoteConfigLoaded, param);
                break;

            case GAME_STEP.User_Loading_End:
                UpdateParamsAccumulatedCount(param, TRACK_NAME.FN_Loading_End);
                LogEvent(TRACK_NAME.FN_Loading_End, param);
                break;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

            case GAME_STEP.Step_RemoteConfigLoadedFail:
                UpdateParamsAccumulatedCount(param, TRACK_NAME.fn_remoteconfig_loaded_fail);
                LogEvent(TRACK_NAME.fn_remoteconfig_loaded_fail, param);
                break;
        }
    }

    public static void UserReturn(int day) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.day, day);

        if (day == 1 || day == 7) {
            string eventName = TRACK_NAME.user_return + "_" + day;
            LogEvent(eventName);
            //AppsFlyerInit.TrackEvent(eventName);
        }

        LogEvent(TRACK_NAME.user_return, param);
    }

    public static void Button_Click(BUTTON_NAME btn) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.name, btn.ToString());
        LogEvent(TRACK_NAME.button_click, param);
    }

    public static void Button_ClickDiscover(string source) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.name, BUTTON_NAME.Discover.ToString());
        param.TryAdd(TRACK_PARAM.source, source);
        param.TryAdd(TRACK_PARAM.current_level, Configuration.GetCurrentLevel());
        LogEvent(TRACK_NAME.button_click, param);
    }

    public static void Button_Click(string btn) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.name, btn);
        param.TryAdd(TRACK_NAME.button_name, btn); //TH-124
        LogEvent(TRACK_NAME.button_click, param);
    }

    public static void Button_Click(string eventName, string btn) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.name, btn);
        LogEvent(eventName, param);
    }

    public static void DownloadFailed(string downloadUrl, string error, int count) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);

        param.TryAdd(TRACK_NAME.url, downloadUrl);
        param.TryAdd(TRACK_NAME.error, error);
        param.TryAdd(TRACK_NAME.retry_times, count);

        LogEvent(TRACK_NAME.download_failed, param);
    }

    public static void NeedMore(string type) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);

        param.TryAdd(TRACK_NAME.name, type);
        LogEvent(TRACK_NAME.needmore, param);
    }

    public static void ScreenResult(string type) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);

        param.TryAdd(TRACK_NAME.name, type);
        LogEvent(TRACK_NAME.screen_result, param);
    }

    public static void FireString(string name, Dictionary<string, object> data = null) {
        LogEvent(name, data);
    }

    public static void FireEvent(FIRE_EVENT type, Dictionary<string, object> data = null) {
        LogEvent(type.ToString(), data);
    }

    public static void Endless(int mode, Song song, bool isFailed) {
        if (mode <= 10) {
            Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
            param.TryAdd(TRACK_NAME.item_id, song.path);
            string name;
            if (isFailed) {
                name = TRACK_NAME.song_endless_x_fail;
            } else {
                name = TRACK_NAME.song_endless_x_start;
            }

            LogEvent(string.Format(name, mode.ToString()), param);
        }
    }

    #region Log ads event

    //Video reward track
    public static void LogVideoAds(AD_STATE adState, VIDEOREWARD type, object rewardItem, string location) {
        string eventName = TRACK_NAME.videoads_ + adState;
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);

        if (rewardItem is Song song) {
            UpdateParamsAdsBySong(param, song, true);
            if (song.isDualUnlock) { // override for dual unlock by ads
                if (param.ContainsKey(TRACK_NAME.song_unlock_type)) {
                    param[TRACK_NAME.song_unlock_type] = SongUnlockType.ads.ToString(); //song_unlock_type
                } else {
                    param.TryAdd(TRACK_NAME.song_unlock_type, SongUnlockType.ads.ToString()); //song_unlock_type
                }
            }
        }

        param.TryAdd(TRACK_NAME.reward, type.ToString().ToLower()); //reward

        UpdateNetWorkState(param);
        if (!string.IsNullOrEmpty(location)) {
            UpdateParamsLocation(param, location); //location
        }

        ChallengeOldUserTracking.AddAdsParam(ref param);
        UpdateParamsAccumulatedCount(param, eventName);

        //Debug.LogWarning("[TODO][LogRewardAds] param " + "reward".ToColor(Color.red));

        //other events
        switch (adState) {
            case AD_STATE.request_failed:
                param.TryAdd(TRACK_NAME.error, AdsManager.instance.GetAdError(AdErrorType.RwCaching));
                break;

            case AD_STATE.show_ready:
                break;

            case AD_STATE.show_notready:
                param.TryAdd(TRACK_NAME.error, AdsManager.instance.GetAdError(AdErrorType.RwCaching));
                break;

            case AD_STATE.show:
                UserProperties.UpdateUserType(USER_PROPERTY_TYPE.ADS);
                if (type == VIDEOREWARD.song_unlock) {
                    if (UserProperties.GetPropertyInt(UserProperties.rw_song) == 0) {
                        LogEvent(TRACK_NAME.video_ads_1_song);
                    }

                    UserProperties.IncreasePropertyInt(UserProperties.rw_song);
                } else if (type == VIDEOREWARD.revive) {
                    UserProperties.IncreasePropertyInt(UserProperties.rw_revive);
                } else if (type == VIDEOREWARD.currency) {
                    //UserProperties.UpdatePropertyInt(UserProperties.rw_diamond);
                }

                UserProperties.IncreasePropertyInt(UserProperties.videoads_show, 1);
                GameCore.BHBalancy.CustomUserProperties.RWShow =
                    UserProperties.GetPropertyInt(UserProperties.videoads_show);
                break;

            case AD_STATE.show_failed:
                param.TryAdd(TRACK_NAME.error, AdsManager.instance.GetAdError(AdErrorType.RwShow));

                break;

            case AD_STATE.finish:
                //UserProperties.UpdatePropertyInt(UserProperties.videoads_finish, 1);
                break;

            case AD_STATE.click:
                UserProperties.IncreasePropertyInt(UserProperties.videoads_click, 1);

                break;
        }

        LogEvent(eventName, param);
    }

    //Interstitial track
    /// <summary>
    /// LogFullAds
    /// </summary>
    /// <param name="adState"></param>
    public static void LogFullAds(AD_STATE adState, string location) {
        string eventName = TRACK_NAME.fullAds + adState;
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);

        // song_id + song_name + level
        Song currentSong = Configuration.instance.GetCurrentSong();
        if (adState == AD_STATE.request || adState == AD_STATE.request_failed || adState == AD_STATE.request_success) {
            UpdateParamsAdsBySong(param, currentSong, false);
        } else {
            UpdateParamsAdsBySong(param, currentSong, true);
        }

        //TH-124
        UpdateNetWorkState(param);
        if (!string.IsNullOrEmpty(location)) {
            UpdateParamsLocation(param, location);
        }

        ChallengeOldUserTracking.AddAdsParam(ref param);
        UpdateParamsAccumulatedCount(param, eventName);

        //other events
        switch (adState) {
            case AD_STATE.request:
                break;

            case AD_STATE.request_failed:
                param.TryAdd(TRACK_NAME.error, AdsManager.instance.GetAdError(AdErrorType.FsCaching));
                break;

            case AD_STATE.request_success:
                break;

            case AD_STATE.show_ready:
                break;

            case AD_STATE.show_notready:
                param.TryAdd(TRACK_NAME.error, AdsManager.instance.GetAdError(AdErrorType.FsCaching));

                break;

            case AD_STATE.show:
                UserProperties.UpdateUserType(USER_PROPERTY_TYPE.ADS);
                UserProperties.IncreasePropertyInt(UserProperties.fullads_show, 1);
                GameCore.BHBalancy.CustomUserProperties.FSShow =
                    UserProperties.GetPropertyInt(UserProperties.fullads_show);
                break;

            case AD_STATE.show_failed:
                param.TryAdd(TRACK_NAME.error, AdsManager.instance.GetAdError(AdErrorType.FsShow));

                break;

            case AD_STATE.finish:
                UserProperties.IncreasePropertyInt(UserProperties.fullads_finish, 1);

                break;

            case AD_STATE.click:
                UserProperties.IncreasePropertyInt(UserProperties.fullads_click, 1);

                break;
        }

        LogEvent(eventName, param);
    }

    //Banner track
    public static void LogBannerAds(AD_STATE adState) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        string location = GetLocation();

        param.TryAdd(TRACK_PARAM.location, location);

        switch (adState) {
            case AD_STATE.request:
                break;

            case AD_STATE.request_failed:
                param.TryAdd(TRACK_NAME.error, AdsManager.instance.GetAdError(AdErrorType.BnCaching));

                break;

            case AD_STATE.show:
                UserProperties.IncreasePropertyInt(UserProperties.bannerads_show, 1);
                bannerTime = Time.time;

                break;

            case AD_STATE.click:
                UserProperties.IncreasePropertyInt(UserProperties.bannerads_click, 1);
                param.TryAdd(TRACK_NAME.level, Configuration.GetGameLevel());
                param.TryAdd(TRACK_NAME.viewTime, (int) (Time.time - bannerTime));

                break;
        }

        LogEvent(TRACK_NAME.bannerAds + adState, param);
    }

    #endregion

    public static void UpdateParams(string eventName, Dictionary<string, object> lsParam, params string[] paramsName) {
        foreach (string paramName in paramsName) {
#if UNITY_EDITOR
            if (lsParam.ContainsKey(paramName)) {
                Logger.LogError("[UpdateParams] " + paramName + " is already exist!");
            }
#endif
            switch (paramName) {
                case TRACK_PARAM.accumulated_count:
                    UpdateParamsAccumulatedCount(lsParam, eventName);
                    break;

                case TRACK_NAME.loading_time:
                    lsParam[TRACK_NAME.loading_time] = Time.realtimeSinceStartup - timeGameOpen;
                    break;
            }
        }
    }

    /// <summary>
    /// Đếm số lần bắn event
    /// </summary>
    /// <param name="param">số lần fire event sau khi update được gắn vào param</param>
    /// <param name="eventName">key playerpref để đếm số lần fire event</param>
    /// <returns></returns>
    public static int UpdateParamsAccumulatedCount(Dictionary<string, object> param, string eventName) {
        if (param.ContainsKey(TRACK_PARAM.accumulated_count)) {
            Logger.LogError("[UpdateParamsAccumulatedCount] " + TRACK_PARAM.accumulated_count + " is already exist!");
        }

        int countEvent = UpdateCountEvent(eventName, 1);
        param[TRACK_PARAM.accumulated_count] = countEvent;

        if (eventName == UserProperties.videoads_show && countEvent % 3 == 0) {
            LogEvent(TRACK_NAME.videoads_show_combo3, param);
        }

        switch (eventName) {
            case UserProperties.fullads_show:
            case UserProperties.videoads_show:
                if (countEvent <= 10) { //fullads_show_[from 1 to 10] //videoads_show_[from 1 to 10]
                    LogEvent(eventName + "_" + countEvent, param);
                } else {
                    if (countEvent % 3 == 0) {
                        LogEvent(eventName + "_" + countEvent, param); //videoads_show_12 videoads_show_15 ... 
                    }
                }

                int total = CountEvent(UserProperties.fullads_show) + CountEvent(UserProperties.videoads_show);
                if (total <= 15) {
                    LogEvent(AppsFlyerInit.KEY.tad_impression + "_" + total, param);
                }

                object cached = param[TRACK_PARAM.accumulated_count];
                param[TRACK_PARAM.accumulated_count] = total;

                LogEvent(AppsFlyerInit.KEY.tad_impression.ToString(), param);
                param[TRACK_PARAM.accumulated_count] = cached;

                break;

            case UserProperties.song_revive_impression:
                if (countEvent <= 10) { //song_revive_impression_[from 1 to 10]
                    LogEvent(eventName + "_" + countEvent, param);
                }

                break;
        }

        return countEvent;
    }

    public static int UpdateCountEvent(string eventName, int increase = 1) {
        eventName = CONFIG_STRING.count_event + eventName;

        int countEvent = PlayerPrefs.GetInt(eventName, 0);
        if (increase != 0) {
            countEvent += increase;
            PlayerPrefs.SetInt(eventName, countEvent);
        }

        return countEvent;
    }

    public static int CountEvent(FIRE_EVENT eventName) {
        return CountEvent(eventName.ToString());
    }

    public static int CountEvent(string eventName) {
        var prefKey = Util.BuildString(string.Empty, CONFIG_STRING.count_event, eventName);
        return PlayerPrefs.GetInt(prefKey, 0);
    }

    //IAP track
    public static void IAP_Impression() {
        var param = GetDefaultParam(location: string.Empty, includeLocation: false);

        LogEvent(TRACK_NAME.iap + AD_STATE.show.ToString(), param);
    }

    public static void IAP_Click(string pack_id, string pack_name, string price) {
        var param = GetDefaultParam(location: string.Empty, includeLocation: false);
        //UserProperties.UpdatePropertyInt(UserProperties.iapltv, 1);
        param.TryAdd(TRACK_NAME.item_id, pack_id);
        param.TryAdd(TRACK_NAME.item_name, pack_name);
        param.TryAdd(TRACK_NAME.item_price, price);
        LogEvent(TRACK_NAME.iap + AD_STATE.click.ToString(), param);
    }

    // Song track
    public static float _songclickTime = 0;
    public static float _songapTime    = 0;

    public static void LogSong(SONG_STATUS songStatus, Song song, string location,
                               Dictionary<string, object> param = null, string songPlayTypeDetail = null) {
        if (!RemoteConfigBase.isInstanced) {
            CustomException.Fire("[LogSong] " + songStatus, "RemoteConfigBase is not instanced");
            return;
        }

        int countIndex = 0;
        try {
            SONG_PLAY_TYPE itemSource = GetSongPlayType(song);
            countIndex = 1;
            param ??= GetDefaultParam(location, true);
            countIndex = 2;

            countIndex = 3;
            //Process switch
            switch (songStatus) {
                case SONG_STATUS.song_click:
                    ProcessSongClick(song, param, location, itemSource);
                    break;

                case SONG_STATUS.song_start:
                    ProcessSongStart(song, param);
                    break;

                case SONG_STATUS.song_end:
                    ProcessSongEnd(song, param);
                    break;

                case SONG_STATUS.song_unlock:
                    ProcessSongUnlock(song, param);
                    break;

                case SONG_STATUS.song_fail:
                    ProcessSongFail(song, param);
                    break;

                case SONG_STATUS.song_revive:
                    ProcessSongRevive(song, param);
                    break;

                case SONG_STATUS.song_revive_impression:
                    AddGamePlayCount(param);
                    AddNewElement(param);
                    AddSongObstacleType(param); //song_revive_impression
                    GameCore.BHBalancy.CustomUserProperties.IncreaseSongReviveImpression();
                    break;

                case SONG_STATUS.song_result:
                    ProcessSongResult(song, param);
                    AddUniqueStarEarn(param);
                    break;

                case SONG_STATUS.song_replay:
                    ProcessSongReplay(param, song, location);
                    break;

                case SONG_STATUS.song_ap:
                    ProcessSongAp(song, param);
                    break;

                case SONG_STATUS.song_preview:
                case SONG_STATUS.song_preview_click:
                    param.TryAdd(TRACK_NAME.item_source, itemSource);
                    break;

                case SONG_STATUS.song_suggest_show:
                case SONG_STATUS.song_suggest_click:
                    param.TryAdd(TRACK_NAME.item_source, itemSource);
                    break;

                case SONG_STATUS.me_start:
                case SONG_STATUS.me_result:
                    if (songStatus == SONG_STATUS.me_result) {
                        GameCore.BHBalancy.CustomUserProperties.IncreaseMeResult();
                        AddUniqueStarEarn(param);
                    } else {
                        GameCore.BHBalancy.CustomUserProperties.IncreaseMeStart();
                    }

                    param.TryAdd(TRACK_PARAM.previous_star,
                        (string.IsNullOrEmpty(song.previousStar) ? "0" : song.previousStar));
                    AddGamePlayCount(param);
                    AddNewElement(param);
                    AddSongObstacleTotal(param);
                    break;
            }

            if (UserProgressionController.EnableFeature) {
                AddSongPackParam(song, param, songStatus);
            }

            if (StarsJourneyManager.isEnable) {
                AddStarsJourneyParam(song, param, songStatus);
            }

            // if (BoosterManager.isInstanced) {
            //     AddBoosterParam(song, param, songStatus);
            // }if (PowerCubeManager.isEnable) {
            //     AddPowerCubeParam(song, param, songStatus);
            // }

            countIndex = 4;
            AddAdvanceParams(songStatus, song, location, param, songPlayTypeDetail);

            RemoveParams(songStatus, param);

            //~~~~~~~~~~~~~~~ ACM Log Event ~~~~~~~~~~~~~~~
            countIndex = 5;
            AcmSongEvent(songStatus, song, param.TryGetValue(TRACK_NAME.playtime, out object value) ? (int) value : 0);

            countIndex = 6;
            LogSongForTutorial(songStatus, song, param);

            //~~~~~~~~~~~~~~~ Fire finally event ~~~~~~~~~~~~~~~
            countIndex = 7;
            CheckOverrideSongPlayType(songStatus, param);
            LogEvent(songStatus.ToString(), param);

            countIndex = 8;

            MissionCenter.LogSongMission(songStatus, song);
        } catch (Exception e) {
            CustomException.Fire("[LogSong] " + songStatus, $"Index: {countIndex} => {e.Message} => {e.StackTrace}");
        }
    }

    private static void AddUniqueStarEarn(Dictionary<string, object> param) {
        if (param == null) {
            return;
        }

        if (param.ContainsKey(TRACK_PARAM.accumulated_star_earn))
            return;
        param.Add(TRACK_PARAM.accumulated_star_earn, Configuration.GetTotalStar());

    }

    public static void CheckOverrideSongPlayType(SONG_STATUS songStatus, Dictionary<string, object> param = null) {
        List<string> overlaps = new List<string>();
        if (GalaxyQuest.CheckCanOverrideSongPlayType(songStatus)) {
            overlaps.Add(GalaxyQuest.KEY_FEATURE);
        }

        if (MysteryDoorManager.CheckCanOverrideSongPlayType(songStatus)) {
            overlaps.Add(MysteryDoorManager.KEY_FEATURE);
        }

        if (ChallengeMode.IsActive) {
            overlaps.Add(GameController.enableEndless ? "challenge_mode" : "normal_mode");
        }
        
        if (MilestoneEvent.CheckCanOverrideSongPlayType(songStatus)) {
            overlaps.Add(MilestoneEvent.KEY_FEATURE);
        }
        
        if (overlaps.IsNullOrEmpty()) {
            return;
        }

        param ??= new Dictionary<string, object>();
        param[TRACK_NAME.song_play_type] = string.Join(";", overlaps);
    }

    private static void AddSongPackParam(Song song, Dictionary<string, object> param, SONG_STATUS songStatus) {
        if (param == null) {
            return;
        }

        if (songStatus is SONG_STATUS.song_impression) {
            param.TryAdd(TRACK_PARAM.songpack_id, song.songPackId);
        }

        if (songStatus is SONG_STATUS.song_click or SONG_STATUS.song_ap or SONG_STATUS.song_start
            or SONG_STATUS.me_start or SONG_STATUS.song_impression) {
            param.TryAdd(TRACK_PARAM.current_level, Configuration.GetCurrentLevel());
        }

        if (songStatus is SONG_STATUS.song_start or SONG_STATUS.song_end or SONG_STATUS.song_result
            or SONG_STATUS.song_fail or SONG_STATUS.song_revive or SONG_STATUS.song_revive_impression
            or SONG_STATUS.song_click or SONG_STATUS.song_ap or SONG_STATUS.song_unlock or SONG_STATUS.me_start
            or SONG_STATUS.me_result or SONG_STATUS.song_replay or SONG_STATUS.song_preview) {
            param.TryAdd(TRACK_PARAM.songpack_id, song.songPackId);
            param.TryAdd(TRACK_PARAM.gems_unlock_song, song.diamonds);
            if (param.ContainsKey(TRACK_PARAM.previous_star)) {
                param.Remove(TRACK_PARAM.previous_star);
            }
        }
    }

    private static void AddStarsJourneyParam(Song song, Dictionary<string, object> param, SONG_STATUS songStatus) {
        if (param == null) {
            return;
        }

        int star = Configuration.instance.GetCurrentStars();
        int tile = Configuration.instance.GetCurrentTiles();
        if (GameController.instance != null && !GameController.instance.IsSavedScore) {
            star += song.LastestStar > song.bestStar
                ? (song.LastestStar - song.bestStar)
                : 0; //= Mathf.Max(star, song.LastestStar);
            tile += song.LastestTile;
        }

        string totalAchieve = $"{star};{tile}";

        if (songStatus is SONG_STATUS.song_click or SONG_STATUS.song_ap or SONG_STATUS.song_start
            or SONG_STATUS.me_start or SONG_STATUS.song_impression or SONG_STATUS.song_fail or SONG_STATUS.song_revive
            or SONG_STATUS.song_result or SONG_STATUS.song_end or SONG_STATUS.me_start or SONG_STATUS.me_result) {
            param.TryAdd(TRACK_PARAM.current_level, Configuration.GetCurrentLevel());
            param.TryAdd(TRACK_PARAM.current_balance, Configuration.instance.GetDiamonds());
            param.TryAdd(TRACK_PARAM.total_achieve, totalAchieve);
        }
    }

    /// <summary>
    /// TH-2390: Add tracking for difficulty tag
    /// </summary>
    /// <param name="songStatus">type of event</param>
    /// <param name="tag">difficulty of song</param>
    /// <param name="param">current dictionary params</param>
    private static void UpdateSongDifficultyTag(SONG_STATUS songStatus, string tag, Dictionary<string, object> param) {
        if (!RemoteConfigBase.instance.DifficultyTag_IsEnable) {
            return;
        }

        switch (songStatus) {
            case SONG_STATUS.song_start:
            case SONG_STATUS.song_end:
            case SONG_STATUS.song_result:
            case SONG_STATUS.song_fail:
            case SONG_STATUS.song_revive:
            case SONG_STATUS.song_revive_impression:
            case SONG_STATUS.song_impression:
            case SONG_STATUS.song_click:
            case SONG_STATUS.song_ap:
            case SONG_STATUS.song_unlock:
            case SONG_STATUS.me_start:
            case SONG_STATUS.me_result:
            case SONG_STATUS.song_start_first:
            case SONG_STATUS.song_result_first:
            case SONG_STATUS.song_replay:
            case SONG_STATUS.song_preview:
            case SONG_STATUS.song_preview_click:
            case SONG_STATUS.song_loaded:
            case SONG_STATUS.song_suggest_click:
                param.TryAdd(TRACK_NAME.difficulty_tag, tag.ToLower());
                break;
        }
    }

    private static int GetValueIntOfDictionary(Dictionary<string, object> param, string key) {
        if (!param.TryGetValue(key, out object sValue) || !int.TryParse(sValue.ToString(), out int value)) {
            value = 0;
        }

        return value;
    }

    public static string GetValueStringOfDictionary(Dictionary<string, object> param, string key) {
        string value = param.ContainsKey(key) ? param[key]?.ToString() ?? string.Empty : string.Empty;
        return value;
    }

    /// <summary>
    /// ACM Fire Song Events
    /// </summary>
    /// <param name="st">Song Status</param>
    /// <param name="song">Song Data</param>
    /// <param name="playTime">Playtime for song result</param>
    static void AcmSongEvent(SONG_STATUS st, Song song, int playTime) {
        if (RemoteConfig.instance.Enable_ACM && !string.IsNullOrEmpty(song.acm_id_v3)) {
            string acmUnlock = song.IsPlayed() ? string.Empty : song.GetUnlockTypeString();
            int level = Configuration.GetGameLevel();
            switch (st) {
                case SONG_STATUS.song_start:
                    ACMSDK.Instance.AnalyticsService.SendEventSongStart(song.acm_id_v3, level, acmUnlock);
                    break;

                case SONG_STATUS.song_end:
                    ACMSDK.Instance.AnalyticsService.SendEventSongEnd(song.acm_id_v3, level, acmUnlock);
                    break;

                case SONG_STATUS.song_fail:
                    ACMSDK.Instance.AnalyticsService.SendEventSongFail(song.acm_id_v3, level, acmUnlock);
                    break;

                case SONG_STATUS.song_result:
                    ACMSDK.Instance.AnalyticsService.SendEventSongResult(song.acm_id_v3, level, acmUnlock, playTime);
                    break;
            }
        }
    }

    private static void UpdateParamsAdsBySong(Dictionary<string, object> param, Song song, bool isFullParams) {
        if (song != null && !param.ContainsKey(TRACK_NAME.song_name)) {
            bool isLocalSong = SongManager.instance.IsLocalSong(song.path);

            param.TryAdd(TRACK_NAME.song_name, song.name); //song_name
            param.TryAdd(TRACK_NAME.song_type, isLocalSong ? TRACK_NAME.local_song : TRACK_NAME.song_list);
            param.TryAdd(TRACK_NAME.level,
                isLocalSong
                    ? (object) (SongManager.instance.userLocalSongs.Count + "a")
                    : Configuration.GetGameLevel().ToString());

            param.TryAdd(TRACK_NAME.song_order, song.indexInConfig);
            if (!param.ContainsKey(TRACK_NAME.song_play_type)) {
                string songPlayType = GetStrSongPlayType(song);
                param.TryAdd(TRACK_NAME.song_play_type, songPlayType); //song_play_type
            }

            if (isFullParams) {
                param.TryAdd(TRACK_NAME.song_acm_id, song.acm_id_v3); //song_acm_id
                param.TryAdd(TRACK_NAME.song_unlock_type, song.GetUnlockTypeString()); //song_unlock_type
            }
        }
    }

    public static SONG_PLAY_TYPE GetSongPlayType(Song song) {
        if (song.isSongOfDay) {
            if (song.isSongOfDayFromDiscover) {
                return SONG_PLAY_TYPE.discover_song_of_the_day;
            }

            return SONG_PLAY_TYPE.song_of_the_day;
        }

        return SongLocationTracker.UpdateCurrentLocation(song);
    }

    public static string GetStrSongPlayType(Song song) {
        return GetSongPlayType(song).ToString();
    }

    private static void UpdateSongParams(Song song, Dictionary<string, object> param, string songPlayTypeDetail) {
        bool isLocalSong = false;
        if (song != null && !param.ContainsKey(TRACK_NAME.song_name)) {
            if (SongManager.instance.IsLocalSong(song.path)) {
                isLocalSong = true;
                param.TryAdd(TRACK_NAME.song_acm_id, TRACK_NAME.local_song); //song_acm_id
                param.TryAdd(TRACK_NAME.song_name, song.name); //song_name
                param.TryAdd(TRACK_NAME.song_mode, SONG_MODE.local_song.ToString());
            } else {
                param.TryAdd(TRACK_NAME.song_acm_id, song.isTutorialSong ? string.Empty : song.acm_id_v3); //song_acm_id
                param.TryAdd(TRACK_NAME.song_name, song.name); //song_name
                param.TryAdd(TRACK_NAME.song_mode, song.SongMode.ToString());
            }

            param.TryAdd(TRACK_NAME.song_unlock_type, song.GetUnlockTypeString()); //song_unlock_type

            //Update new params
            SONG_PLAY_TYPE songPlayType = GetSongPlayType(song);
            if (songPlayType != SONG_PLAY_TYPE.none) {
                string playType = songPlayType.ToString();

                if (!string.IsNullOrEmpty(songPlayTypeDetail)) {
                    playType += $"_{songPlayTypeDetail}";

                } else if (!string.IsNullOrEmpty(song.song_play_type_detail)) {
                    playType += $"_{song.song_play_type_detail}";
                }

                param[TRACK_NAME.song_play_type] = playType;
            }

            int accumulatedCount = Configuration.GetGameLevel() + 1;
            UserActivated(accumulatedCount);

            if (Spawner.s != null) {
                param.TryAdd(TRACK_NAME.theme, Spawner.s.themeId.ToString());
            }

            switch (songPlayType) {
                case SONG_PLAY_TYPE.home:
                    if (isLocalSong) {
                        param.TryAdd(TRACK_NAME.song_order, "local_song");
                    } else {
                        if (song.indexInConfig > 0) {
                            param.TryAdd(TRACK_NAME.song_order, song.ordering);
                        } else {
                            param.TryAdd(TRACK_NAME.song_order, "ACM");
                        }
                    }

                    break;

                case SONG_PLAY_TYPE.tutorial:
                    param.TryAdd(TRACK_NAME.song_order, song.ordering);
                    break;

                case SONG_PLAY_TYPE.recommended_result:
                    param.TryAdd(TRACK_NAME.song_order, song.ordering);
                    break;

                default:
                    if (song.indexInConfig > 0) {
                        param.TryAdd(TRACK_NAME.song_order, song.ordering);
                    } else {
                        param.TryAdd(TRACK_NAME.song_order, "ACM");
                    }

                    break;
            }
        }
    }

    internal static void UpdateBasicSongParams(Song song, Dictionary<string, object> param) {
        bool isLocalSong = false;
        if (song != null && !param.ContainsKey(TRACK_NAME.song_name)) {
            if (SongManager.instance.IsLocalSong(song.path)) {
                param.TryAdd(TRACK_NAME.song_acm_id, TRACK_NAME.local_song); //song_acm_id
                param.TryAdd(TRACK_NAME.song_name, song.name); //song_name
                isLocalSong = true;
            } else {
                param.TryAdd(TRACK_NAME.song_acm_id, song.isTutorialSong ? string.Empty : song.acm_id_v3); //song_acm_id
                param.TryAdd(TRACK_NAME.song_name, song.name); //song_name
            }

            param.TryAdd(TRACK_NAME.song_unlock_type, song.GetUnlockTypeString()); //song_unlock_type

            //Update new params

            SONG_PLAY_TYPE songPlayType = GetSongPlayType(song);
            param.TryAdd(TRACK_NAME.song_play_type, songPlayType.ToString());
            switch (songPlayType) {
                case SONG_PLAY_TYPE.home:
                    if (isLocalSong) {
                        param.TryAdd(TRACK_NAME.song_order, "local_song");
                    } else {
                        if (song.indexInConfig > 0) {
                            param.TryAdd(TRACK_NAME.song_order, song.ordering);
                        } else {
                            param.TryAdd(TRACK_NAME.song_order, "ACM");
                        }
                    }

                    break;

                case SONG_PLAY_TYPE.tutorial:
                    param.TryAdd(TRACK_NAME.song_order, song.ordering);
                    break;

                case SONG_PLAY_TYPE.recommended_result:
                    param.TryAdd(TRACK_NAME.song_order, song.ordering);
                    break;

                default:
                    if (song.indexInConfig > 0) {
                        param.TryAdd(TRACK_NAME.song_order, song.ordering);
                    } else {
                        param.TryAdd(TRACK_NAME.song_order, "ACM");
                    }

                    break;
            }
        }
    }

    static void UserActivated(int accumulatedCount) {
        if (accumulatedCount == 2 && !PlayerPrefs.HasKey(TRACK_NAME.user_activated)) {
            LogEvent(TRACK_NAME.user_activated);
            PlayerPrefs.SetInt(TRACK_NAME.user_activated, 1);
        }
    }

    #region Update parameter event

    private const string NetWorkOff = "offline";
    private const string NetWorkOn  = "online";

    public static string UpdateNetWorkState(Dictionary<string, object> param) {
        if (!param.ContainsKey(TRACK_NAME.connection)) {
            param.TryAdd(TRACK_NAME.connection,
                Application.internetReachability == NetworkReachability.NotReachable ? NetWorkOff : NetWorkOn);
        }

        return param[TRACK_NAME.connection].ToString();
    }

    private static void UpdateParamsLocation(Dictionary<string, object> param, string location) {
        param[TRACK_PARAM.location] = location;
    }

    #endregion

    public static void Level(int level, string song_id) {
        UserProperties.UpdateLvl(level);
        FB_Standard_Level_Up(level, song_id);
        //AppsFlyerInit.TrackLvl(level);
        if (level <= 10) {
            LogEvent("new_lvup_" + level.ToString());
        }
    }

    // Currency_Income track
    public static void Currency_Income(CurrencyEarnSource currencyEarnSource, int income_value) {
        var param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.item_source, currencyEarnSource.ToString());
        param.TryAdd(TRACK_NAME.item_value, (long) income_value);
        LogEvent(TRACK_NAME.currency_income, param);
    }

    // Item Buy track
    public static void Item_Buy(CurrencyEarnSource currencyEarnSource, string item_name, float item_value,
                                string item_id = null) {
        var param = GetDefaultParam(location: string.Empty, includeLocation: false);

        param.TryAdd(TRACK_NAME.item_type, currencyEarnSource.ToString());
        param.TryAdd(TRACK_NAME.item_name, item_name);
        param.TryAdd(TRACK_NAME.item_value, (item_value * -1).ToString());
        if (!string.IsNullOrEmpty(item_id)) {
            param.TryAdd(TRACK_NAME.item_id, item_id);
        }

        LogEvent(TRACK_NAME.item_buy, param);
    }

    // Item Usage track
    public static void Item_Usage(CurrencySpendSource item_type, string item_name) {
        var param = GetDefaultParam(location: string.Empty, includeLocation: false);

        param.TryAdd(TRACK_NAME.item_type, item_type);
        param.TryAdd(TRACK_NAME.item_name, item_name);

        LogEvent(TRACK_NAME.item_usage, param);
    }

    // Tutorial track
    static void LogSongTutorial(SONG_STATUS st, Dictionary<string, object> param) {
        switch (st) {
            case SONG_STATUS.song_click:
                LogEvent(TRACK_NAME.tutorial_song_click, param);
                break;

            case SONG_STATUS.song_loaded:
                LogEvent(TRACK_NAME.tutorial_song_loaded, param);
                break;

            case SONG_STATUS.song_ap:
                LogEvent(TRACK_NAME.fn_tutorial_start, param);
                break;

            case SONG_STATUS.song_start:
                LogEvent(TRACK_NAME.fn_song_start_tutorial, param);
                break;

            case SONG_STATUS.song_fail:
                //if ((int) param[TRACK_NAME.stars] >= 2) {
                //    LogEvent(TRACK_NAME.fn_song_end_tutorial, param);
                //    Configuration.instance.isTutorial = false;
                //} else {
                LogEvent(TRACK_NAME.fn_tutorial_show_continue, param);
                //}

                break;

            case SONG_STATUS.song_replay:
                LogEvent(TRACK_NAME.fn_result_replay, param);
                break;

            case SONG_STATUS.song_revive:
                LogEvent(TRACK_NAME.fn_continue_show, param);
                break;

            case SONG_STATUS.song_end:
                LogEvent(TRACK_NAME.fn_song_end_tutorial, param);
                break;

            case SONG_STATUS.song_result:
                LogEvent(TRACK_NAME.fn_song_result_tutorial, param);
                break;

            case SONG_STATUS.song_suggest_click:
                LogEvent(TRACK_NAME.fn_result_nextsong, param);
                break;
        }
    }

    public static void SongTrial(Song song) {
        var param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.song_name, song.name);
        param.TryAdd(TRACK_NAME.song_id, song.path);
        int trialCount = PlayerPrefs.GetInt(TRACK_NAME.song_trial);
        int currentCount = trialCount + 1;
        PlayerPrefs.SetInt(TRACK_NAME.song_trial, currentCount);
        param.TryAdd(TRACK_NAME.trial_count, currentCount);
        LogEvent(TRACK_NAME.song_trial, param);
    }

    public static void Screen(string screen_name, string screen_class) {
        if (FirebaseSDK.firebaseStatus == FirebaseSDK.FirebaseStatus.READY) {
            LogEvent(FirebaseAnalytics.EventScreenView, new Dictionary<string, object>() {
                { FirebaseAnalytics.ParameterScreenName, screen_name },
                { FirebaseAnalytics.ParameterScreenClass, screen_class.ToLower() }
            });

            CustomException.SetKey(FirebaseKey.CurrentScreen, screen_name);

            Logger.Log($"[SetCurrentScreen]: {screen_name}-{screen_class.ToLower()}");
        }
    }

    public static void ScreenBack(string location) {
        Screen(location, instance.current_location.ToString().ToLower());
    }

    public static void Share(Song song, string location) {
        var param = GetDefaultParam(location);
        UpdateSongParams(song, param, "share");
        LogEvent(TRACK_NAME.share, param);
    }

    public static void Artist_Click(string name) {
        Dictionary<string, object> param = new Dictionary<string, object>();

        param.TryAdd(TRACK_NAME.name, name);

        LogEvent("artist_click", param);
    }

    public static void Album_Click(string name) {
        Dictionary<string, object> param = new Dictionary<string, object>();

        param.TryAdd(TRACK_NAME.name, name);

        LogEvent("album_click", param);
    }

    public static void Genre_Click(string name) {
        Dictionary<string, object> param = new Dictionary<string, object>();

        param.TryAdd(TRACK_NAME.name, name);

        LogEvent("genre_click", param);
    }

    public static void SongCard_Click(string clickType, string location) {
        var param = GetDefaultParam(location);
        param.TryAdd(TRACK_NAME.type, clickType);
        LogEvent(TRACK_NAME.songcard_click, param);
    }

    public static void Subscription(SUBSCRIPTION_EVENT eventName, string subId = null, int viewTime = -1,
                                    int count = -1) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false); //screen

        if (eventName == SUBSCRIPTION_EVENT.sub_offer_show && count == 1) {
            Subscription(SUBSCRIPTION_EVENT.sub_offer_show_first, subId, viewTime, count);
        }

        string screen = SubscriptionController.GetScreen();
        if (screen != null) {
            param.TryAdd(TRACK_NAME.screen, screen);
        }

        if (eventName == SUBSCRIPTION_EVENT.Subscription_Charge || eventName == SUBSCRIPTION_EVENT.Subscription_Trial) {
            ChallengeOldUserTracking.AddSubParam(ref param);
        }

        if (count >= 0) {
            if (eventName == SUBSCRIPTION_EVENT.Subscription_Charge) {
                param.TryAdd(TRACK_NAME.chargeCount, count);
            } else {
                param.TryAdd(TRACK_NAME.showCount, count);
            }
        }

        if (viewTime >= 0) {
            param.TryAdd(TRACK_NAME.time, viewTime); //time
        }

        if (!string.IsNullOrEmpty(subId)) {
            param.TryAdd(TRACK_NAME.product_id, subId);
        }

        param.TryAdd(TRACK_NAME.trial_status, RevenueCatPurchases.instance.IsUnSubUser() ? "yes" : "no");
        param.TryAdd(TRACK_NAME.loading_duration, Time.time);

        UpdateParamsAccumulatedCount(param, eventName.ToString()); //accumulated_count
        LogEvent(eventName.ToString(), param);
        LogNewEvent(eventName);
    }

    private static void LogNewEvent(SUBSCRIPTION_EVENT eventName) {
        bool isTutorial = LoadingScript.instance != null && Configuration.instance.isTutorial;

        switch (eventName) {
            case SUBSCRIPTION_EVENT.sub_offer_show: {
                if (isTutorial) {
                    LogSubsEventWithDefaultParams(TRACK_NAME.FN_Subscription_Intro_Offer_Show);
                } else {
                    if (!SubscriptionController.isUserOpen) {
                        LogSubsEventWithDefaultParams(SUBSCRIPTION_EVENT.User_Subscription_Intro_Offer_Show.ToString());
                    }
                }

                break;
            }

            case SUBSCRIPTION_EVENT.sub_offer_click: {
                if (isTutorial) {
                    LogSubsEventWithDefaultParams(TRACK_NAME.FN_Subscription_Intro_Offer_Trial);
                } else {
                    if (!SubscriptionController.isUserOpen) {
                        LogSubsEventWithDefaultParams(SUBSCRIPTION_EVENT.User_Subscription_Intro_Offer_Trial
                            .ToString());
                    }
                }

                break;
            }

            case SUBSCRIPTION_EVENT.sub_offer_close: {
                if (isTutorial) {
                    LogSubsEventWithDefaultParams(TRACK_NAME.FN_Subscription_Intro_Offer_Close);
                } else {
                    if (!SubscriptionController.isUserOpen) {
                        LogSubsEventWithDefaultParams(SUBSCRIPTION_EVENT.User_Subscription_Intro_Offer_Close
                            .ToString());
                    }
                }

                break;
            }

            case SUBSCRIPTION_EVENT.sub_view_all_plans_show: {
                if (isTutorial) {
                    LogSubsEventWithDefaultParams(TRACK_NAME.FN_Subscription_Intro_Offer_Explore);
                } else {
                    if (!SubscriptionController.isUserOpen) {
                        LogSubsEventWithDefaultParams(
                            SUBSCRIPTION_EVENT.User_Subscription_Intro_Offer_Explore.ToString());
                    }
                }

                break;
            }
        }
    }

    private static void LogSubsEventWithDefaultParams(string eventName) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false); //screen
        UpdateParams(eventName, param, TRACK_PARAM.accumulated_count, TRACK_NAME.loading_time);
        LogEvent(eventName, param);
    }

    public static void Subscription_Catalogue_Show(string cata, int time) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);

        param.TryAdd(TRACK_NAME.value, cata);

        //TH-124
        //screen => cata1 cata2 genre artist
        param.TryAdd(TRACK_NAME.screen, cata);
        //timne
        param.TryAdd(TRACK_NAME.time, time);
        param.TryAdd(TRACK_NAME.loading_duration, Time.time);

        //accumulated_count
        string eventName = SUBSCRIPTION_EVENT.sub_cata_show.ToString();
        UpdateParamsAccumulatedCount(param, eventName);
        LogEvent(eventName, param);

        //funnel
        if (Configuration.instance.isTutorial) {
            switch (cata) {
                case "cata1":
                    LogEvent(TRACK_NAME.fn_subscription_intro_01, param);
                    break;

                case "cata2":
                    LogEvent(TRACK_NAME.fn_subscription_intro_02, param);
                    break;

                default:
                    LogEvent(TRACK_NAME.fn_subscription_intro_03, param);
                    break;
            }
        }
    }

    public static void Subscription_Catalogue_ButtonClick(string buttonName) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.button_name, buttonName);

        LogEvent(TRACK_NAME.button_click, param);
    }

    public static void Subscription_Catalogue_ArtistClick(string value, string type, string location) {
        var param = GetDefaultParam(location: location, includeLocation: true);
        param.TryAdd(TRACK_NAME.value, value);
        param.TryAdd(TRACK_NAME.type, type);
        LogEvent("sub_cata_artist_click", param);
    }

    public static void Subscription_Catalogue_GenreChoice(string value) {
        Dictionary<string, object> param = GetDefaultParam(location: string.Empty, includeLocation: false);
        param.TryAdd(TRACK_NAME.name, value);
        LogEvent(TRACK_NAME.genre_choice, param);
    }

    public static void Subscription_Catalogue_ArtistChoice(string value, string location) {
        Dictionary<string, object> param = GetDefaultParam(location, false);
        param.TryAdd(TRACK_NAME.name, value);
        LogEvent(TRACK_NAME.artist_choice, param);
    }

    public static void Subscription_Catalogue_Close(int cata, int time, string location) {
        var param = GetDefaultParam(location, true);
        param.TryAdd(TRACK_NAME.value, "cata" + cata);
        param.TryAdd(TRACK_NAME.time, time);
        LogEvent("sub_cata_close", param);
    }

    public static Dictionary<string, object> GetDefaultParam(string location, bool includeLocation = false) {
        Dictionary<string, object> param = new();
        if (includeLocation && instance != null) {
            param.TryAdd(TRACK_PARAM.location, location); //bỏ param screen thay bằng location
        }

        return param;
    }

    public static void LogEvent(string eventName, Dictionary<string, object> param = null) {
        if (IsIgnoreEvent(eventName)) {
            //Logger.Log("LogEvent " + eventName, "Ignore event");
            return;
        }

        lock (queueData) {
            int countTemp = 0;
            try {
                if (Configuration.isAdmin ||
                    (RemoteConfigBase.isInstanced && RemoteConfigBase.instance.Admin_isShowLogEvent) ||
                    Application.isEditor) {
                    LogForAdmin(eventName, param);
                }

                countTemp = 1;

                if (Application.isMobilePlatform && (!Configuration.isAdmin ||
                                                     (RemoteConfigBase.isInstanced && RemoteConfigBase.instance
                                                         .Admin_isForceFireEvent))) {
                    countTemp = 2;

                    try {
                        if (instance != null && instance.IsChina()) {
                            Analytics.CustomEvent(eventName, param);
                        }
                    } catch (Exception e) {
                        CustomException.Fire("[LogChinaEvent]", $"Can't log event {eventName} to unity! => {e}");
                    }

                    countTemp = 3;
                    if (queueData.Count < 50) {
                        Parameter[] parameters = GetParameters(param);
                        queueData.Add(new KeyValuePair<string, Parameter[]>(eventName, parameters));
                    } else {
                        if (FirebaseSDK.firebaseStatus == FirebaseSDK.FirebaseStatus.READY) {
                            CustomException.Fire("[LogEvent]",
                                $"queueData.Count >= 50. Don't log event {eventName} to firebase!");
                        }
                    }

                    countTemp = 4;
                    if (FirebaseSDK.firebaseStatus == FirebaseSDK.FirebaseStatus.READY) {
                        foreach (KeyValuePair<string, Parameter[]> data in queueData) {
                            FirebaseAnalytics.LogEvent(data.Key, data.Value);
                            ValidateEvents(data);
                        }

                        queueData.Clear();
                    } else {
                        Logger.LogWarning("[LogEvent] FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY");
                    }
                }

                countTemp = 5;
                if (AppsFlyerInit.isInstanced) {
                    AppsFlyerInit.instanceAFI.LogAppsFlyerEvent(eventName, param);
                }

                countTemp = 6;
                LocalDB.StoreEventToDB(eventName, param);
            } catch (Exception ex) {
                CustomException.Fire("[LogEvent] " + eventName + " => " + countTemp, ex.ToString());
            }
        }
    }

    private static void ValidateEvents(KeyValuePair<string, Parameter[]> data) {
        string eventName = data.Key;
        Parameter[] parameters = data.Value;

        if (eventName.Length > 40) {
            CustomException.Fire("ValidateEvents", $"Event name [{eventName}] is longer than 40 character");
        }

        if (Regex.IsMatch(eventName, @"^\d")) {
            CustomException.Fire("ValidateEvents", $"Event name [{eventName}] can't start with number");
        }

        if (parameters.Length > 25) {
            CustomException.Fire("ValidateEvents", $"Event [{eventName}] can't contain more than 25 parameters");
        }
    }

    #endregion

    #region Firebase Standard

    public static void FB_Standard_Level_Up(int level, string song_id) {
        try {
            if (FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY) {
                return;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                { FirebaseAnalytics.ParameterLevel, (long) level },
                { TRACK_NAME.song_id, song_id }
            };
            LogEvent(FirebaseAnalytics.EventLevelUp, param);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
    }

    public static void FB_Standard_Select_Content(string type, string id) {
        try {
            if (FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY) {
                return;
            }

            Dictionary<string, object> param = new() {
                { FirebaseAnalytics.ParameterContentType, type },
                { FirebaseAnalytics.ParameterItemID, id }
            };
            LogEvent(FirebaseAnalytics.EventSelectContent, param);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
    }

    public static void FB_Standard_Earn(string item_name, int value) {
        try {
            if (FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY) {
                return;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                { FirebaseAnalytics.ParameterVirtualCurrencyName, CONFIG_STRING.Diamonds },
                { FirebaseAnalytics.ParameterItemName, item_name },
                { FirebaseAnalytics.ParameterValue, (long) value }
            };
            LogEvent(FirebaseAnalytics.EventEarnVirtualCurrency, param);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
    }

    public static void FB_Standard_Spend(string item_name, int value) {
        try {
            if (FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY) {
                return;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                { FirebaseAnalytics.ParameterVirtualCurrencyName, CONFIG_STRING.Diamonds },
                { FirebaseAnalytics.ParameterItemName, item_name },
                { FirebaseAnalytics.ParameterValue, (long) value }
            };
            LogEvent(FirebaseAnalytics.EventSpendVirtualCurrency, param);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
    }

    public static void FB_Standard_Unlock_Achievement(string id) {
        /*
         //Update new firebase 8.10.1 removed EventUnlockAchievement
         try {
            Dictionary<string, object> param = new Dictionary<string, object> {
                { FirebaseAnalytics.ParameterAchievementId, id }
            };
            LogEvent(FirebaseAnalytics.EventUnlockAchievement, param);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
        */
    }

    public static void FB_Standard_Post_Score(string character, int score) {
        try {
            if (FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY) {
                return;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                { FirebaseAnalytics.ParameterLevel, (long) Configuration.GetGameLevel() },
                { FirebaseAnalytics.ParameterCharacter, character },
                { FirebaseAnalytics.ParameterScore, (long) score }
            };
            LogEvent(FirebaseAnalytics.EventPostScore, param);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
    }

    public static void FB_Standard_Login(bool isNew = false) {
        try {
            if (FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY) {
                return;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                { FirebaseAnalytics.ParameterMethod, FirebaseSDK.instance.authTypeLogin.ToString() },
                { TRACK_NAME.type, isNew ? "new" : "old" }
            };
            LogEvent(FirebaseAnalytics.EventLogin, null);
            //FirebaseAnalytics.SetUserId(CoreUser.instance.user.auId);
            UserProperties._FB_Property("userId", CoreUser.instance.user.auId);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
    }

    public static void FB_Standard_Signup() {
        try {
            if (FirebaseSDK.firebaseStatus != FirebaseSDK.FirebaseStatus.READY) {
                return;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                { FirebaseAnalytics.ParameterMethod, FirebaseSDK.instance.authTypeLogin.ToString() }
            };
            LogEvent(FirebaseAnalytics.EventSignUp, null);
        } catch (Exception ex) {
            Debug.LogError(ex.ToString());
        }
    }

    #endregion

    #region ACM Requirements

    public static void NO_ACM_Event(string name, Song song, string reason = null, string unlock_type = null,
                                    float response_time = -1) {
        if (SongManager.instance.IsOfficial(song.path)) {
            Dictionary<string, object> param = new Dictionary<string, object>();

            param.TryAdd(TRACK_NAME.song_name, song.name);
            if (reason != null) {
                param.TryAdd(TRACK_NAME.reason, reason);
            }

            if (unlock_type != null) {
                param.TryAdd(TRACK_NAME.unlock_type, unlock_type);
            }

            if (response_time >= 0) {
                param.TryAdd(TRACK_NAME.response_time, response_time.ToString("N0"));
            }

            LogEvent(name, param);
        }
    }

    #endregion

    #region Analytic Shop

    public static void LogEventUserAvatarImpression(string location) {
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_PARAM.location, location }
        };
        LogEvent(TRACK_NAME.user_avatar, param);
    }

    public static void LogEventUserDiamondImpression(string location) {
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_PARAM.location, location }
        };
        LogEvent(TRACK_NAME.user_diamond, param);
    }

    public static void LogEventShopBallBackButtonImpression(int userDiamond, string category) {
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_NAME.Session_ID, UserProperties.GetSessionCount() },
            { TRACK_NAME.user_diamond, userDiamond },
            { TRACK_NAME.category, category }
        };
        LogEvent(TRACK_NAME.shop_ball_back_button, param);
    }

    public static void ShopBallItemClick(Dictionary<string, object> param) {
        if (RemoteConfigBase.instance.ShopBall_UseCategoryLayout) {
            param.TryAdd("category", BallCategoryScroller.CurrentCategory);
        }

        LogEvent(TRACK_NAME.shop_ball_item_click, param);
    }

    public static void ShopBallCategoryClick(string localtion, string categoryName) {
        Dictionary<string, object> param = new Dictionary<string, object>() {
            { "category_name", categoryName },
            { "category_location", localtion }
        };
        LogEvent(TRACK_NAME.shop_ball_category_click, param);
    }

    public static void ShopBallEntryPointClick(string localtion) {
        Dictionary<string, object> param = new Dictionary<string, object>() {
            { "category_location", localtion }
        };
        LogEvent(TRACK_NAME.shop_ball_entry_click, param);
    }

    public static void ShopBallItemBuy(Dictionary<string, object> param) {
        if (RemoteConfigBase.instance.ShopBall_UseCategoryLayout) {
            param.TryAdd("category", BallCategoryScroller.CurrentCategory);
        }

        LogEvent(TRACK_NAME.shop_ball_item_buy, param);
    }

    #endregion

    private static Parameter[] GetParameters(Dictionary<string, object> param) {
        List<Parameter> parameters = ListPool<Parameter>.Get();
        if (param != null && param.Count > 0) {
            foreach (KeyValuePair<string, object> p in param) {
                if (!string.IsNullOrEmpty(p.Key) && p.Value != null) {
                    string key = Util.ValidAttributeName(p.Key);
                    if (p.Value is int intValue) {
                        parameters.Add(new Parameter(key, intValue));
                    } else if (p.Value is long longValue) {
                        parameters.Add(new Parameter(key, longValue));
                        // } else if (p.Value is float floatValue) { // firebase không hỗ trợ float
                        //     parameters.Add(new Parameter(key, floatValue));
                    } else if (p.Value is double doubleValue) {
                        parameters.Add(new Parameter(key, doubleValue));
                    } else if (!string.IsNullOrEmpty(p.Value.ToString())) {
                        parameters.Add(new Parameter(key, p.Value.ToString()));
                    }
                }
            }
        }

        Parameter[] result = parameters.ToArray();
        ListPool<Parameter>.Release(parameters);
        return result;
    }

    private const string StartNgoacNhon = "{ ";
    private const string EndNgoacNhon   = " }";
    private const string Space          = " ";
    private const string EndParam       = "\";";
    private const string MiddleParam    = " : \"";
    private const string Patern         = @"^\d";

    private static void LogForAdmin(string eventName, Dictionary<string, object> param) {
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.Append(strFirebaseEvent);
        logBuilder.Append(eventName);
        List<string> orderParams = new List<string>();
#if UNITY_EDITOR
        orderParams = new List<string>() {
            ChallengeModeTracking.PARAM_crowns_achieved, ChallengeModeTracking.PARAM_milestone_achieved
        };
#endif
        if (param != null && param.Count != 0) {
            logBuilder.Append(StartNgoacNhon);

            if (!orderParams.IsNullOrEmpty()) {
                for (int i = 0; i < orderParams.Count; i++) {
                    var key = orderParams[i];
                    if (param.ContainsKey(key)) {
                        AddParamStr(key, param[key], i);
                    }
                }
            }

            if (Regex.IsMatch(eventName, Patern)) {
                Debug.LogError($"[FirebaseEvent Name][{eventName}] : Key event Name can't start with numeric!!!!");
            }

            foreach (var p in param) {
                if (orderParams.IsNullOrEmpty() || !orderParams.Contains(p.Key)) {
                    AddParamStr(p.Key, p.Value, -1);
                }
            }

            logBuilder.Remove(logBuilder.Length - 1, 1); // xóa bỏ dấu ; cuối cùng
            logBuilder.Append(EndNgoacNhon);
        }

        if (param != null && param.Count > 25) {
            Debug.LogError($"[FirebaseEvent Reaching Max Limit] : {logBuilder}");

#if UNITY_EDITOR
            if (MaxLimitEventCounter.instance != null) {
                var eventError = new FailedEventFirebase {
                    eventName = eventName,
                    paramNames = param.Keys.ToList()
                };

                if (MaxLimitEventCounter.instance.events.Find(x =>
                        x.eventName == eventName && x.paramNames.Count == param.Count) == null) {
                    MaxLimitEventCounter.instance.events.Add(eventError);
                }
            }
#endif
        }

        Debug.Log(logBuilder.ToString());

        void AddParamStr(string key, object value, int priority) {
            if (Regex.IsMatch(key, Patern)) {
                Debug.LogError($"[FirebaseEvent Param][{key}] : Key parameter can't start with numeric!!!!");
            }

            logBuilder.Append(Space);
            logBuilder.Append(priority >= 0 ? key.ToColor(priorityColors[priority % priorityColors.Length]) : key);
            logBuilder.Append(MiddleParam);
            logBuilder.Append(priority >= 0
                ? (value != null
                    ? value.ToString().ToColor(priorityColors[priority % priorityColors.Length])
                    : string.Empty)
                : value);
            logBuilder.Append(EndParam);
        }
    }

    private static Color[] priorityColors = new[] { Color.green, Color.cyan, Color.magenta, Color.red, Color.yellow };

    public static void LogSubmitReview(int star, string comment, string location) {
        var param = GetDefaultParam(location, true);
        param.TryAdd(TRACK_NAME.rate, star);
        param.TryAdd(TRACK_NAME.comment, comment);
        LogEvent(TRACK_NAME.user_review, param);
    }

    public void CheckSubscriptionClose() {
        if (HomeManager.instance != null) {
            if (ShopScript.instance != null && ShopScript.instance.gameObject.activeInHierarchy) {
                SetCurrentLocation(LOCATION_NAME.BALL_LIST);
            } else {
                SetCurrentLocation(LOCATION_NAME.home);
            }
        } else {
            if (ShopScript.instance != null && ShopScript.instance.gameObject.activeInHierarchy) {
                SetCurrentLocation(LOCATION_NAME.BALL_LIST_INGAME);
            } else if (UIController.ui != null && UIController.ui.gameover != null &&
                       UIController.ui.gameObject.activeSelf) {
                SetCurrentLocation(LOCATION_NAME.result);
            } else {
                SetCurrentLocation(LOCATION_NAME.gameplay);
            }
        }
    }

    private static void UpdateParamsDeeplink(Dictionary<string, object> dictionary) {
        if (!AppsFlyerInit.instanceAFI || !AppsFlyerInit.instanceAFI.isOpenGameByOnelink) {
            return;
        }

        string type = AppsFlyerInit.instanceAFI.GetParam(TRACK_PARAM.type) as string;
        string path = AppsFlyerInit.instanceAFI.GetParam(TRACK_PARAM.path) as string;
        if (!string.IsNullOrEmpty(type) && !string.IsNullOrEmpty(path)) {
            dictionary.TryAdd(TRACK_PARAM.deeplink, type + ":" + path);
        }
    }

    public static void AddNewElement(Dictionary<string, object> dictionary) {
        dictionary ??= new Dictionary<string, object>();

        if (dictionary.ContainsKey(TRACK_PARAM.is_new_element)) {
            return;
        }

        dictionary.TryAdd(TRACK_PARAM.is_new_element,
            NotesManager.instance != null ? (NotesManager.instance.HasElementType() ? 1 : 0) : "NULL");
    }

    public static void AddGamePlayCount(Dictionary<string, object> dictionary) {
        dictionary ??= new Dictionary<string, object>();

        if (dictionary.ContainsKey(TRACK_PARAM.gameplay_count)) {
            return;
        }

        dictionary.TryAdd(TRACK_PARAM.gameplay_count, GameController.gameplayCount);
    }

    public static void AddSongObstacleType(Dictionary<string, object> dictionary) {
        dictionary ??= new Dictionary<string, object>();

        if (dictionary.ContainsKey(TRACK_PARAM.obstacle_type)) {
            return;
        }

        int lastNote = Spawner.s != null ? Spawner.s.currentJumpNoteID + 1 : -1;
        if (lastNote < 0) {
            Logger.EditorLogError($"No data for lastNote {lastNote}");
            return;
        }

        if (NotesManager.instance == null || NotesManager.instance.noteDatas.IsNullOrEmpty())
            return;

        int noteId = Mathf.Clamp(lastNote, 0, NotesManager.instance.noteDatas.Count - 1);
        NoteData noteData = NotesManager.instance.noteDatas[noteId];

        dictionary.TryAdd(TRACK_PARAM.obstacle_type, noteData.elementType.ToString());
    }

    public static void AddSongObstacleTotal(Dictionary<string, object> dictionary) {
        dictionary ??= new Dictionary<string, object>();

        if (dictionary.ContainsKey(TRACK_PARAM.played_element)) {
            return;
        }

        if (NotesManager.instance == null || NotesManager.instance.noteDatas.IsNullOrEmpty())
            return;

        string playedElement = NotesManager.instance.TrackElementType();
        dictionary.TryAdd(TRACK_PARAM.played_element, playedElement);
    }

    private static void ProcessSongClick(Song song, Dictionary<string, object> param, string location,
                                         SONG_PLAY_TYPE itemSource) {
        _songclickTime = Time.time;
        lastClickTime = Time.time;
        if (itemSource == SONG_PLAY_TYPE.recommended_result) {
            LogSong(SONG_STATUS.song_suggest_click, song, location: location);
        }

        if (SongManager.instance.IsLocalSong(song.path)) {
            FireEvent(FIRE_EVENT.LocalSong_SongClick);
        }

        if (!song.IsSongPlayCount()) {
            if (!Configuration.instance.isTutorial) {
                Configuration.UpdateUniquePlayCount(song);
            }

            LogSong(SONG_STATUS.song_unlock, song, location: location);
        }

        if (song.isSongOfDay) {
            FireString(TRACK_NAME.today_song_play);
        }

        song.IncreasePlayCount();
        GameCore.BHBalancy.CustomUserProperties.IncreaseSongClick();
    }

    private static void ProcessSongEnd(Song song, Dictionary<string, object> param) {
        AddGamePlayCount(param);
        AddNewElement(param);
        AddSongObstacleTotal(param);
        int countIndex = 0;
        try {
            int songEndCount = UserProperties.IncreasePropertyInt(UserProperties.song_end, 1);
            countIndex = 1;

            if (instance && instance.timePlayManager) {
                instance.timePlayManager.PausePlayTimeCount();
            }

            countIndex = 2;
            GameCore.BHBalancy.CustomUserProperties.SongEnd = songEndCount;

            if (CanLogEvent(songEndCount, RemoteConfigBase.instance.EventTriggers_SongEnd)) {
                LogEvent($"{UserProperties.song_end}_{songEndCount}", param);
            }

        } catch (Exception e) {
            bool isNull = instance == null;
            bool isNullTimePlayManager = isNull || instance.timePlayManager == null;
            CustomException.Fire("[ProcessSongEnd]",
                $"Index: {countIndex} => {e.Message} => isNull {isNull} => isNullTimePlayManager {isNullTimePlayManager} => {e.StackTrace}");
        }
    }

    private static void ProcessSongAp(Song song, Dictionary<string, object> param) {
        _songapTime = Time.time;
        param.TryAdd(TRACK_NAME.loading_duration_songap, Time.time - _songclickTime);
        param.TryAdd(TRACK_NAME.response_time, Time.time - lastClickTime);
        SongList.lastSong = song;
        AddGamePlayCount(param);
        AddNewElement(param);
        GameCore.BHBalancy.CustomUserProperties.IncreaseSongAP();
    }

    private static void ProcessSongReplay(Dictionary<string, object> param, Song song, string location) {
        lastClickTime = Time.time;
        LogSong(SONG_STATUS.song_click, song, location: location);
        LogSong(SONG_STATUS.song_loaded, song, location: location);
        int songReplayCount = UserProperties.IncreasePropertyInt(UserProperties.song_replay, 1);
        GameCore.BHBalancy.CustomUserProperties.SongReplay = songReplayCount;
        AddGamePlayCount(param);
        AddNewElement(param);
    }

    private static void ProcessSongResult(Song song, Dictionary<string, object> param) {
        AddGamePlayCount(param);
        AddNewElement(param);
        AddSongObstacleType(param); //song_result
        AddSongObstacleTotal(param);
        param.TryAdd(TRACK_NAME.revive_count, song.GetReviveCount()); // Revive count in total this song
        if (!song.IsLocalSong() && GameSessionManager.Instance != null) {
            GameSessionManager.Instance.StopGame(GameController.instance.stars >= 3 ? Reason.GameWin : Reason.GameFail);
        }

        int songResultCount = UserProperties.IncreasePropertyInt(UserProperties.song_result, 1);
        instance?.timePlayManager?.PausePlayTimeCount();

        param.TryAdd(TRACK_PARAM.previous_star, (string.IsNullOrEmpty(song.previousStar) ? "0" : song.previousStar));

        GameCore.BHBalancy.CustomUserProperties.SongResult = songResultCount;
      
        if (CanLogEvent(songResultCount, RemoteConfigBase.instance.EventTriggers_SongResult)) {
            LogEvent($"{UserProperties.song_result}_{songResultCount}", param);
        }
    }

    private static void ProcessSongRevive(Song song, Dictionary<string, object> param) {
        AddGamePlayCount(param);
        AddNewElement(param);
        AddSongObstacleType(param); //song_revive
        AddSongObstacleTotal(param);
        if (!string.IsNullOrEmpty(GameController.songReviveType)) {
            param.TryAdd(TRACK_PARAM.song_revive_type, GameController.songReviveType);
        }

        song.IncreaseReviveCount();
        //int reviveCount = song.GetReviveCount();

        int reviveCount = UserProperties.IncreasePropertyInt(UserProperties.song_revive, 1);
        GameCore.BHBalancy.CustomUserProperties.SongRevive = reviveCount;
        
        if (CanLogEvent(reviveCount, RemoteConfigBase.instance.EventTriggers_SongRevive)) {
            LogEvent($"{UserProperties.song_revive}_{reviveCount}", param);
        }

        param.TryAdd(TRACK_PARAM.transaction_id, song.transactionIDSpend);
    }

    private static void ProcessSongFail(Song song, Dictionary<string, object> param) {
        AddGamePlayCount(param);
        AddNewElement(param);
        AddSongObstacleType(param); //song_fail
        AddSongObstacleTotal(param);
        int songFailCount = UserProperties.IncreasePropertyInt(UserProperties.song_fail, 1);
        GameCore.BHBalancy.CustomUserProperties.SongFail = songFailCount;
        instance.timePlayManager.PausePlayTimeCount();
    }

    private static void ProcessSongUnlock(Song song, Dictionary<string, object> param) {
        int songUnlockCount = UserProperties.IncreasePropertyInt(UserProperties.song_unlock, 1);
        param.TryAdd(TRACK_PARAM.current_level, Configuration.GetCurrentLevel());
        GameCore.BHBalancy.CustomUserProperties.SongUnlock = songUnlockCount;

        if (CanLogEvent(songUnlockCount, RemoteConfigBase.instance.EventTriggers_SongUnlock)) {
            LogEvent($"{UserProperties.song_unlock}_{songUnlockCount}", param);
        }

        param.TryAdd(TRACK_PARAM.transaction_id, song.transactionIDSpend);
    }

    private static void ProcessSongStart(Song song, Dictionary<string, object> param) {
        AddGamePlayCount(param);
        AddNewElement(param);
        AddSongObstacleTotal(param);
        param.TryAdd(TRACK_NAME.loading_duration_song_start, Time.time - _songapTime);
        if (!song.IsLocalSong() && GameSessionManager.Instance != null) {
            GameSessionManager.Instance.StartGame(song.acm_id_v3, song.path, song.name,
                SuperpoweredSDK.instance.GetAudioSource(), 0);
        }

        int songStartCount = UserProperties.IncreasePropertyInt(UserProperties.song_start, 1);
        FB_Standard_Select_Content(SONG_STATUS.song_start.ToString(), song.path);
        song.SetPlayed();

        // set AirFlux level property
        if (AirfluxTracker.IsEnableAirFlux()) {
            Airflux.SetLevel(songStartCount);
        }

        if (SongList.previewSongID == song.path) {
            param.TryAdd(TRACK_NAME.preview, 1);
        }

        SongList.previewSongID = null;

        if (SongManager.instance && SongManager.instance.IsLocalSong(song.path)) {
            FireEvent(FIRE_EVENT.LocalSong_SongPlay);
        }

        //Set PlayTime
        if (GameController.enableEndless && ChallengeMode.IsAcceptFromNormal) {
            //don't reset play_time
        } else {
            instance?.timePlayManager?.StartPlayTimeCount();
        }

        GameCore.BHBalancy.CustomUserProperties.SongStart = songStartCount;

        if (CanLogEvent(songStartCount, RemoteConfigBase.instance.EventTriggers_SongStart)) {
            LogEvent($"{UserProperties.song_start}_{songStartCount}", param);
        }

        param.TryAdd(TRACK_PARAM.previous_star, (string.IsNullOrEmpty(song.previousStar) ? "0" : song.previousStar));
    }

    private static void LogSongForTutorial(SONG_STATUS songStatus, Song song, Dictionary<string, object> param) {
        if (songStatus == SONG_STATUS.song_result) {
            //Check possible tutorial replaying
            waitTutorialReplay = Configuration.instance.isTutorial;

            //me_result event
            instance.timePlayManager.MeResult(param);
        }

        //Tutorial Log Event
        if (Configuration.instance.isTutorial || (songStatus == SONG_STATUS.song_replay && waitTutorialReplay) ||
            (Configuration.instance.isGamePlayTutorial && songStatus == SONG_STATUS.song_suggest_click) ||
            song.isTutorialSong) {
            if (songStatus == SONG_STATUS.song_end || songStatus == SONG_STATUS.song_result) {
                Configuration.instance.isTutorial = false;
                if (RemoteConfig.instance.TutorialSong_IsMustComplete) {
                    Configuration.SetIntroOff();
                }

                // đây là bài hát tutorial, khi đã chơi xong rồi, thì unload
                if (SuperpoweredSDK.instance != null) {
                    SuperpoweredSDK.instance.SetReady(false);
                }
            }

            LogSongTutorial(songStatus, param);
        }
    }

    private static void AddAdvanceParams(SONG_STATUS songStatus, Song song, string location,
                                         Dictionary<string, object> param, string songPlayTypeDetail) {
        int countIndex = 0;
        try {
            //TH-2390 add tracking difficulty_tag tracking
            countIndex = 1;
            UpdateSongDifficultyTag(songStatus, song.GetDifficultyTag().ToString(), param);

            //song attributes:
            countIndex = 2;
            UpdateSongParams(song, param, songPlayTypeDetail);

            if (songStatus == SONG_STATUS.song_preview && song.isDualUnlock && location.Equals("home") &&
                param.ContainsKey(TRACK_NAME.song_unlock_type)) {
                param[TRACK_NAME.song_unlock_type] = SongUnlockType.ads.ToString();
            }

            countIndex = 3;
            UpdateNetWorkState(param); //connection
            countIndex = 4;
            UpdateParamsAccumulatedCount(param, songStatus.ToString()); //accumulated_count
            countIndex = 5;
            UpdateParamsDeeplink(param);

            countIndex = 6;
            if (songStatus != SONG_STATUS.song_end) {
                UpdateParamsLocation(param, location); //location
            }

            countIndex = 7;
            if (param.ContainsKey(TRACK_NAME.song_progress) && !param.ContainsKey(TRACK_NAME.song_play_time)) {
                //song_play_time
                param.TryAdd(TRACK_NAME.song_play_time, instance.timePlayManager.GetPlayDuration());
            }

        } catch (Exception e) {
            CustomException.Fire("[AddAdvanceParams] " + songStatus,
                $"Index: {countIndex} => {e.Message} => {e.StackTrace}");
        }
    }

    private static void RemoveParams(SONG_STATUS songStatus, Dictionary<string, object> param) {
        if (songStatus == SONG_STATUS.song_impression) {
            //no need this param
            param.Remove(TRACK_NAME.song_play_type);
            param.Remove(TRACK_NAME.theme);
        }
    }

    private static List<string> _listString;

    private static bool IsIgnoreEvent(string eventName) {
        _listString ??= (RemoteConfigBase.instance.Ignore_LogEvents).StringToList();
        return _listString.Contains(eventName);
    }

    public static string GetLocation() {
        string location;
        switch (instance.current_location) {
            case LOCATION_NAME.TIPS:
                location = "guideline";
                break;

            case LOCATION_NAME.discover:
            case LOCATION_NAME.setting:
            case LOCATION_NAME.leaderboard:
            case LOCATION_NAME.shop:
                location = instance.current_location.ToString();
                break;

            case LOCATION_NAME.loading:
                location = "loading";
                break;

            case LOCATION_NAME.hardcore_challenge:
                location = "hardcorechallenge";
                break;

            case LOCATION_NAME.sevenday_mission:
                location = "7daymission";
                break;

            case LOCATION_NAME.ACHIEVEMENT_POPUP:
                location = "achievement";
                break;

            case LOCATION_NAME.sub_offer:
            case LOCATION_NAME.Subscription_Intro:
                location = "sub_offer";
                break;

            case LOCATION_NAME.Subscription_SeeAll:
                location = "sub_catalog";
                break;

            case LOCATION_NAME.gameplay:
            case LOCATION_NAME.PREPARE_GAME:
                location = "action_phase";
                break;

            default:
                location = instance.current_location.ToString();
                break;
        }

        return location;
    }

    private static bool CanLogEvent(int count, string strTriggers) {
        if (count <= 10) { //[from 1 to 10]
            return true;
        }

        List<string> triggers = strTriggers.StringToList();
        if (triggers.Contains(count.ToString())) {
            return true;
        }

        return false;
    }
}