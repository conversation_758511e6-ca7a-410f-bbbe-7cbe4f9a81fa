using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using GameCore.LiveEvent.MysteryDoor;
using TilesHop.Cores.Pooling;
using TilesHop.LiveEvent.GalaxyQuest;

public class TimePlayManager : MonoBehaviour {
    private int _sessionId;
    private int _sessionDuration;
    private DateTime _startActiveTime;
    private DateTime _lastActiveTime;
    private DateTime _firstVisited;
    float _playDateTime;
    private int _playDuration;
    private float _sessionTimeMinutes = 30;
    const int SongGuaranteedTime = 15;
    private Coroutine _meStartCourCoroutine;
    void Start() {
        _lastActiveTime = DateTime.Now;
    }
    public void GameLoaded(bool isFirstGame = false) {
        _sessionId = UserProperties.GetSessionCount();
        IncreaseSession();
        _firstVisited = DateTime.Now;
        if (isFirstGame) {
            AnalyticHelper.LogEvent(TRACK_NAME.new_firstopen);
            PlayerPrefs.SetString(CONFIG_STRING.FirstVisitedGame,
                _firstVisited.ToString(CONFIG_STRING.DateTimeFormat));
        }
        else {
            try {
                _firstVisited = DateTime.Parse(PlayerPrefs.GetString(CONFIG_STRING.FirstVisitedGame));
            }
            catch (Exception ex) {
                Debug.Log(ex.Message);
                PlayerPrefs.SetString(CONFIG_STRING.FirstVisitedGame,
                    _firstVisited.ToString(CONFIG_STRING.DateTimeFormat));
            }
        }
    }

    public float GetPlayDuration() {
        return _playDuration;
    }

    public DateTime GetFirstVisited() {
        return _firstVisited;
    }

    public void StartPlayTimeCount() {
        _playDateTime = Time.time;
        _playDuration = 0;

        _meStartCourCoroutine = StartCoroutine(MeStart());
    }

    public void ResumePlayTimeCount() {
        _playDateTime = Time.time;
    }

    public void PausePlayTimeCount() {
        int plus = (int) (Time.time - _playDateTime);
        _playDuration += plus;
        _playDateTime = Time.time;
        UserProperties.IncreasePropertyInt(UserProperties.play_time, plus);
    }

    private void IncreaseSession() {
        _sessionDuration = 0;
        _sessionId++;
        PlayerPrefs.SetInt(TRACK_NAME.Session_ID, _sessionId);
        BalancyManager.UpdateSession(_sessionId);
    }

    /// <summary>
    /// Fire me_start: Triggered when user play a song and reachs to the 15 second. Including: Action phase in tutorial,
    /// tap on Play from Main Screen, Retry (Replay). Not including: Revive to continue.
    /// </summary>
    private IEnumerator MeStart() {
        yield return YieldPool.GetWaitForSeconds(SongGuaranteedTime);
        if (_playDuration < SongGuaranteedTime) {
            yield return null;
        }
        if (GameController.CheckInstanced() && !GameController.instance.IsContinued()) {
            AnalyticHelper.LogSong(SONG_STATUS.me_start, NotesManager.instance.song, LOCATION_NAME.gameplay.ToString());
        }
    }
    
    /// <summary>
    /// Fire me_result: Triggered by the same logic with song_result but song_playtime is higher or equal than 15 sec.
    /// </summary>
    /// <param name="param"></param>
    public void MeResult(Dictionary<string, object> param = null) {
        if (_playDuration >= SongGuaranteedTime) {
            AnalyticHelper.CheckOverrideSongPlayType(SONG_STATUS.me_result, param);
            AnalyticHelper.LogEvent(SONG_STATUS.me_result.ToString(), param);
        }
        
        if (_meStartCourCoroutine != null) {
            StopCoroutine(_meStartCourCoroutine);
        }
    }
    void OnApplicationPause(bool pauseStatus) {
        if (pauseStatus) {
            //Pause
            _lastActiveTime = DateTime.Now;
            //Save session duration
            _sessionDuration += (int) (DateTime.Now - _startActiveTime).TotalSeconds;
            PlayerPrefs.SetFloat(TRACK_NAME.Session_Duration, _sessionDuration);
        }
        else {
            float minutes = (float) (DateTime.Now - _lastActiveTime).TotalMinutes;
            if (minutes > _sessionTimeMinutes) {
                IncreaseSession();
                _lastActiveTime = DateTime.Now;
            }

            _startActiveTime = DateTime.Now;
        }
    }
}