#if !MAX_ADS
using System;
using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using AppsFlyerSDK;
using Inwave;
using System.Threading;

public class AdIronSource : AdNetwork {
    //IRON SOURCE
    public string androidAppKey;
    public string iosAppKey;

    private bool   _listenerAttached;
    const   string AD_REWARDED     = "rewared";
    const   string AD_INTERSTITIAL = "interstitial";

    private IronsourceMapping _ironsourceMapping;

    public override void Init() {
        if (_listenerAttached) {
            return;
        }

        _listenerAttached = true;
        
        // Khởi tạo bất đồng bộ để tránh block main thread
        StartCoroutine(InitAsync());
    }

    private IEnumerator InitAsync() {
        // Đợi một frame để tránh block main thread
        yield return null;
        
        try {
            // Khởi tạo mapping
            _ironsourceMapping ??= IronsourceMapping.Init(this);
            _ironsourceMapping.MapWithPseudoID();

            // Khởi tạo events trước
            InitializeEvents();
            
            // Khởi tạo segmentation
            SegmentationForIRS.InitSegmenteCPM3();
        } catch (System.Exception e) {
            Debug.LogError($"[AdIronSource] Error in early initialization: {e.Message}");
        }
        
        // Khởi tạo IronSource với timeout
        yield return StartCoroutine(InitIronSourceSDK());
        
        // Khởi tạo Ad Quality
        yield return StartCoroutine(IEInitAdQuality());
    }

    private void InitializeEvents() {
        //ARM SDK Postbacks Events
        IronSourceEvents.onImpressionDataReadyEvent += OnImpressionSuccessEvent;

        if (DevInfo.isShowTestSuite) {
            // Sử dụng coroutine để tránh block main thread
            StartCoroutine(SetupTestSuiteAsync());
        }

        // Add Interstitial Events
        IronSourceInterstitialEvents.onAdReadyEvent += InterstitialAdReadyEvent;
        IronSourceInterstitialEvents.onAdLoadFailedEvent += InterstitialAdLoadFailedEvent;
        IronSourceInterstitialEvents.onAdClosedEvent += InterstitialAdClosedEvent;
        IronSourceInterstitialEvents.onAdClickedEvent += InterstitialAdClickedEvent;
        IronSourceInterstitialEvents.onAdShowFailedEvent += InterstitialAdShowFailedEvent;
        IronSourceInterstitialEvents.onAdShowSucceededEvent += InterstitialAdShowSucceededEvent;
        IronSourceInterstitialEvents.onAdOpenedEvent += InterstitialAdOpenedEvent;

        //Rewarded Video Ad
        IronSourceRewardedVideoEvents.onAdClosedEvent += RewardedVideoAdClosedEvent;
        IronSourceRewardedVideoEvents.onAdRewardedEvent += RewardedVideoAdRewardedEvent;
        IronSourceRewardedVideoEvents.onAdShowFailedEvent += RewardedVideoAdShowFailedEvent;
        IronSourceRewardedVideoEvents.onAdClickedEvent += RewardedVideoAdClickedEvent;
        IronSourceRewardedVideoEvents.onAdReadyEvent += RewardedVideoAdReadyEvent;
        IronSourceRewardedVideoEvents.onAdLoadFailedEvent += RewardedVideoAdLoadFailedEvent;
        IronSourceRewardedVideoEvents.onAdOpenedEvent += RewardedVideoAdOpenedEvent;

        // Banner Events
        IronSourceBannerEvents.onAdLoadedEvent += BannerAdLoadedEvent;
        IronSourceBannerEvents.onAdLoadFailedEvent += BannerAdLoadFailedEvent;
        IronSourceBannerEvents.onAdClickedEvent += BannerAdClickedEvent;
        //IronSourceBannerEvents.onAdScreenPresentedEvent += BannerAdScreenPresentedEvent;
        //IronSourceBannerEvents.onAdScreenDismissedEvent += BannerAdScreenDismissedEvent;
        //IronSourceBannerEvents.onAdLeftApplicationEvent += BannerAdLeftApplicationEvent;
    }

    private IEnumerator InitIronSourceSDK() {
        string appKey = Utils.IsAndroid() ? androidAppKey : iosAppKey;
        
        if (string.IsNullOrEmpty(appKey)) {
            Debug.LogError("[AdIronSource] App key is null or empty");
            yield break;
        }

        // Đăng ký callback trước khi khởi tạo
        IronSourceEvents.onSdkInitializationCompletedEvent += SdkInitializationCompletedEvent;
        
        // Khởi tạo IronSource trực tiếp trên main thread để đảm bảo init hoàn thành
        try {
            IronSource.Agent.init(appKey, IronSourceAdUnits.REWARDED_VIDEO, IronSourceAdUnits.INTERSTITIAL,
                IronSourceAdUnits.BANNER);
            IronSource.Agent.SetPauseGame(true);
            Debug.Log("[AdIronSource] IronSource initialization started");
        } catch (System.Exception e) {
            Debug.LogError($"[AdIronSource] IronSource init error: {e.Message}");
        }
        
        // Đợi ít nhất 1 frame để SDK có thời gian khởi tạo
        yield return null;
    }

    private IEnumerator IEInitAdQuality() {
        yield return null;

        // Init Ad Quality
        string appKey = Utils.IsAndroid() ? androidAppKey : iosAppKey;
        if (string.IsNullOrWhiteSpace(appKey)) {
            Debug.LogError("Ad Quality Init Error: AppKey is invalid.");
        } else {
            IronSourceAdQuality.Initialize(appKey);
            IronSourceAdQuality.SetUserConsent(true);
        }
    }
    
    private IEnumerator SetupTestSuiteAsync() {
        // Đợi một frame để tránh block
        yield return null;
        
        // Các method này thường không throw exception
        IronSource.Agent.setMetaData("is_test_suite", "enable");
        IronSource.Agent.setAdaptersDebug(true);
        IronSource.Agent.validateIntegration();
    }

    public override void SetConsent(bool value) {
        // Sử dụng coroutine để tránh block main thread
        StartCoroutine(SetConsentAsync(value));
    }
    
    private IEnumerator SetConsentAsync(bool value) {
        // Đợi một frame để tránh block
        yield return null;
        
        // IronSource.Agent.setConsent() thường không throw exception
        IronSource.Agent.setConsent(value);
    }

    public override void AppPause(bool pauseStatus) {
        // Sử dụng coroutine để tránh block main thread
        StartCoroutine(AppPauseAsync(pauseStatus));
    }
    
    private IEnumerator AppPauseAsync(bool pauseStatus) {
        // Đợi một frame để tránh block
        yield return null;
        
        // IronSource.Agent.onApplicationPause() thường không throw exception
        IronSource.Agent.onApplicationPause(pauseStatus);
    }

    /// <summary>
    /// OnImpressionSuccessEvent
    /// </summary>
    /// <param name="impressionData"></param>
    private void OnImpressionSuccessEvent(IronSourceImpressionData impressionData) {
        //Todo: Very important function, be carefully when edit
        if (impressionData == null) {
            return;
        }

        //Log to firebase
        string adUnit = impressionData.adUnit;
        Dictionary<string, object> paramFirebase = new Dictionary<string, object> {
            { "ad_platform", "IronSource" },
            { "ad_source", impressionData.adNetwork },
            { "ad_unit_name", impressionData.instanceName },
            { "ad_format", adUnit },
            { "value", impressionData.revenue },
            { "currency", "USD" }, // All IronSource revenue is sent in USD
            { "appsflyerid", AppsFlyerSDK.AppsFlyer.getAppsFlyerId() },
            //{ "ama_device_id", AmaPassportService.AMA_DEVICE_ID },
        };
        if (Configuration.isInstanced) {
            string location = string.Empty;
            switch (adUnit) {
                case "banner":
                    location = "banner";
                    string screen = AnalyticHelper.GetLocation();
                    paramFirebase.Add(TRACK_PARAM.screen, screen);
                    break;

                case "interstitial":
                    location = AdsManager.instance.LocationInterstitialAds;
                    break;

                default:
                    location = AdsManager.instance.LocationRewardAds;
                    break;
            }

            if (string.IsNullOrWhiteSpace(location)) {
                location = "others";
            }

            paramFirebase.Add(TRACK_PARAM.location, location);
        }

        AdsManager.instance?.TryAddSongParam(paramFirebase);

        AnalyticHelper.FireEvent(FIRE_EVENT.ad_impression, paramFirebase);
        AnalyticHelper.FireEvent(FIRE_EVENT.ad_impression_ama, paramFirebase);

        //Log to appsflyer
        LogRevenueToAppsflyer(impressionData);
        LogAdUnitToAppsflyer(adUnit);

        // Airflux tracking
        AirfluxTracker.TrackAdsImpression(impressionData);

        eCPMSaver.UpdateeCPMImpSegment(impressionData);
    }

    // private void LogRevenueToAppsflyer(IronSourceImpressionData impressionData) {
    //     Dictionary<string, string> dic = new Dictionary<string, string>();
    //     dic.Add("ad_unit_name", impressionData.instanceName);
    //     dic.Add("ad_format", impressionData.adUnit);
    //
    //     AppsFlyerAdRevenue.logAdRevenue(impressionData.adNetwork,
    //         AppsFlyerAdRevenueMediationNetworkType.AppsFlyerAdRevenueMediationNetworkTypeIronSource, // 
    //         impressionData.revenue ?? 0, // 
    //         "USD", dic);
    // }

    private void LogRevenueToAppsflyer(IronSourceImpressionData impressionData) {
        Dictionary<string, string> dic = new();
        dic.Add("ad_unit_name", impressionData.instanceName);
        dic.Add("ad_format", impressionData.adUnit);

        AFAdRevenueData logRevenue = new(impressionData.adNetwork, MediationNetwork.IronSource, "USD",
            impressionData.revenue ?? 0);
        AppsFlyer.logAdRevenue(logRevenue, dic);
    }

    private void LogAdUnitToAppsflyer(string adUnit) {
        if (string.IsNullOrEmpty(adUnit)) {
            return;
        }

        switch (adUnit) {
            case "banner":
                break;

            case "interstitial":
                AppsFlyerInit.LogEvent("af_inters_displayed");
                break;

            case "rewarded_video":
                AppsFlyerInit.LogEvent("af_rewarded_displayed");
                break;

            default:
                CustomException.Fire("[OnImpressionSuccessEvent]", "Need to handle new ad unit type: " + adUnit);
                break;
        }
    }

    private void SdkInitializationCompletedEvent() {
        Debug.Log("[AdIronSource] SDK initialization completed, loading ads...");
        
        // Load ads sau khi SDK đã init hoàn thành
        IronSource.Agent.loadRewardedVideo();
        IronSource.Agent.loadInterstitial();
        
        if (DevInfo.isShowTestSuite) {
            // Sử dụng coroutine để tránh block main thread
            StartCoroutine(LaunchTestSuiteAsync());
        }
    }
    
    private IEnumerator LaunchTestSuiteAsync() {
        // Đợi một frame để tránh block
        yield return null;
        
        // IronSource.Agent.launchTestSuite() thường không throw exception
        IronSource.Agent.launchTestSuite();
    }

    #region InterstitialAd

    public override bool IsInterstitialReady() {
        return IronSource.Agent.isInterstitialReady();
    }

    public override void LoadInterstitial() {
        CustomException.SetKey(FirebaseKey.LastAdAction, "[Interstitial][LoadAd]");
        IronSource.Agent.loadInterstitial();
    }

    public override void ShowInterstitial() {
        CustomException.SetKey(FirebaseKey.LastAdAction, "[Interstitial][ShowAd]");
        IronSource.Agent.showInterstitial();
    }

    private void InterstitialAdClickedEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(AdsManager.instance.InterstitialAdClickedEvent);
    }

    private void InterstitialAdLoadFailedEvent(IronSourceError error) {
        UnityMainThreadDispatcher.Instance()
            .Enqueue(() => AdsManager.instance.InterstitialAdLoadFailedEvent(error.getDescription()));
    }

    private void InterstitialAdShowFailedEvent(IronSourceError error, IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance()
            .Enqueue(() => AdsManager.instance.InterstitialAdShowFailedEvent(error.getDescription()));
    }

    private void InterstitialAdClosedEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(AdsManager.instance.InterstitialAdClosedEvent);
    }

    //Invoked when the Interstitial Ad Unit has opened
    private void InterstitialAdOpenedEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(AdsManager.instance.InterstitialAdOpenedEvent);
        AdInfoLogging(AD_INTERSTITIAL, adInfo);
    }

    //Invoked right before the Interstitial screen is about to open.
    private void InterstitialAdShowSucceededEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(AdsManager.instance.InterstitialAdShowSucceededEvent);
    }

    private void InterstitialAdReadyEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(AdsManager.instance.InterstitialAdReadyEvent);
    }

    #endregion

    #region RewardedVideoAd

    public override void LoadRewardedVideo() {
        //IronSource.Agent.loadRewardedVideo();
    }

    public override bool IsRewardedVideoAvailable() {
        return IronSource.Agent.isRewardedVideoAvailable();
    }

    public override void ShowRewardedVideo(string placement = null) {
        CustomException.SetKey(FirebaseKey.LastAdAction, "[Rewarded][ShowAd]");
        IronSource.Agent.showRewardedVideo(placement);
    }

    //Invoked when the RewardedVideo ad view has opened.
    //Your Activity will lose focus. Please avoid performing heavy 
    //tasks till the video ad will be closed.
    private void RewardedVideoAdOpenedEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(() => AdsManager.instance.RewardedVideoAdOpenedEvent());
        AdInfoLogging(AD_REWARDED, adInfo);
    }

    private void RewardedVideoAdLoadFailedEvent(IronSourceError error) {
        UnityMainThreadDispatcher.Instance()
            .Enqueue(() => AdsManager.instance.RewardedVideoLoadFailed(error.getDescription()));
    }

    private void RewardedVideoAdReadyEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(() => AdsManager.instance.RewardedVideoLoaded());
    }

    private void RewardedVideoAdRewardedEvent(IronSourcePlacement ssp, IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(() => AdsManager.instance.RewardedVideoAdRewardedEvent());
    }

    private void RewardedVideoAdClosedEvent(IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(() => AdsManager.instance.RewardedVideoAdClosedEvent());
    }

    private void RewardedVideoAdShowFailedEvent(IronSourceError error, IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance()
            .Enqueue(() => AdsManager.instance.RewardedVideoAdShowFailedEvent(error.getDescription()));
    }

    private void RewardedVideoAdClickedEvent(IronSourcePlacement ssp, IronSourceAdInfo adInfo) {
        UnityMainThreadDispatcher.Instance().Enqueue(() => AdsManager.instance.RewardedVideoClickedEvent());
    }

    #endregion

    #region BannerAd

    public override void LoadBanner() {
        if (!Utils.isInternetReachable) {
            return;
        }

        if (RemoteConfigBase.instance.BannerAd_Tutorial && Configuration.instance.isGamePlayTutorial) {
            return;
        }

        if (bannerRequestStatus == BannerRequestStatus.Loaded || bannerRequestStatus == BannerRequestStatus.Loading) {
            return;
        } else {
            bannerRequestStatus = BannerRequestStatus.Loading;
            AdsManager.instance.BannerAdRequestEvent();
            IronSourceBannerPosition pos = RemoteConfigBase.instance.BannerAd_IsBottom
                ? IronSourceBannerPosition.BOTTOM
                : IronSourceBannerPosition.TOP;
            IronSource.Agent.loadBanner(IronSourceBannerSize.BANNER, pos);
        }

// #if UNITY_EDITOR
//         //for testing
//         BannerAdLoadedEvent(null);
// #endif
    }

    public override float GetBannerHeight() {
        //Logger.Log($"[Banner] IronSourceBannerSize Height:50");
        return 50; // height of IronSourceBannerSize.BANNER
    }

    public override void HideBanner() {
        IronSource.Agent.hideBanner();
        if (bannerStatus != BANNER_STATUS.HIDE) {
            bannerStatus = BANNER_STATUS.HIDE;
            AdsManager.instance.BannerAdHideEvent();
        }
    }

    public override void ShowBanner() {
        if (bannerStatus != BANNER_STATUS.SHOWED) {
            if (bannerRequestStatus == BannerRequestStatus.Loaded) {
                IronSource.Agent.displayBanner();
                bannerStatus = BANNER_STATUS.SHOWED;
                AdsManager.instance.BannerAdShowedEvent();
            } else if (bannerRequestStatus != BannerRequestStatus.Loading) {
                LoadBanner();
            }
        }
    }

    public override void DestroyBanner() {
        bannerStatus = BANNER_STATUS.DESTROYED;
        bannerRequestStatus = BannerRequestStatus.None;
        IronSource.Agent.destroyBanner();
        AdsManager.instance.HideBanner();
    }

    //Invoked once the banner has loaded
    private void BannerAdLoadedEvent(IronSourceAdInfo adInfo) {
        bannerRequestStatus = BannerRequestStatus.Loaded;
        if (bannerStatus == BANNER_STATUS.HIDE) {
            HideBanner();
        } else if (bannerStatus == BANNER_STATUS.DESTROYED) {
            DestroyBanner();
        } else if (bannerStatus == BANNER_STATUS.SHOWED) {
            ShowBanner();
        } else {
            HideBanner();
        }

        UnityMainThreadDispatcher.Instance().Enqueue(() => AdsManager.instance.BannerAdLoadedEvent());
    }

    //Invoked when the banner loading process has failed.
    //@param description - string - contains information about the failure.
    private void BannerAdLoadFailedEvent(IronSourceError error) {
        bannerRequestStatus = BannerRequestStatus.Failed;
        UnityMainThreadDispatcher.Instance().Enqueue(() => {
            AdsManager.instance.BannerAdLoadFailedEvent(error.getDescription());
        });
    }

    // Invoked when end user clicks on the banner ad
    private void BannerAdClickedEvent(IronSourceAdInfo adInfo) {
        AdsManager.IsLeavingByAds = true;
        UnityMainThreadDispatcher.Instance().Enqueue(() => AnalyticHelper.LogBannerAds(AD_STATE.click));
    }

    //Notifies the presentation of a full screen content following user click
    private void BannerAdScreenPresentedEvent(IronSourceAdInfo adInfo) {
        //Debug.Log("bad: BannerAdScreenPresentedEvent");
    }

    //Notifies the presented screen has been dismissed
    private void BannerAdScreenDismissedEvent(IronSourceAdInfo adInfo) {
        //Debug.Log("bad: BannerAdScreenDismissedEvent");
    }

    //Invoked when the user leaves the app
    private void BannerAdLeftApplicationEvent(IronSourceAdInfo adInfo) {
        //Debug.Log("bad: BannerAdLeftApplicationEvent");
    }

    #endregion

    #region adInfo Logging

    private void AdInfoLogging(string type, IronSourceAdInfo adInfo) {
        Dictionary<string, string> dicAdInfo = new();
        dicAdInfo.Add(FirebaseKey.AD_TYPE, type);
        dicAdInfo.Add(FirebaseKey.AD_AUCTIONID, adInfo?.auctionId ?? "");
        dicAdInfo.Add(FirebaseKey.AD_UNIT, adInfo?.adUnit ?? "");
        dicAdInfo.Add(FirebaseKey.AD_NETWORK, adInfo?.adNetwork ?? "");
        dicAdInfo.Add(FirebaseKey.AD_InstanceName, adInfo?.instanceName ?? "");
        dicAdInfo.Add(FirebaseKey.AD_InstanceId, adInfo?.instanceId ?? "");

        foreach (KeyValuePair<string, string> pair in dicAdInfo) {
            CustomException.SetKey(pair.Key, pair.Value);
        }

        AdsManager.instance.AddLogAd(dicAdInfo);
    }

    #endregion
}
#endif