#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using System.Reflection;
using TilesHop.ChallengeOldUser;
using TilesHop.Cores.Boosters;
using TilesHop.EconomySystem;
using TilesHop.MysteryBox;
using TilesHop.Cores.Popups.Home;
using TilesHop.LiveEvent.MilestoneEvent;
using UnityEditor;
using UnityEngine;

/// <summary>
/// trungvt
/// </summary>
public partial class RemoteConfig {
    #region Methods

    protected override void EditorTestConfig() {
        RecommendedConfig();
        Hybrid_BottomTabOrder = "shop_ball;shop_iap;main_home;live_event;search;star_journey";
        OldUserBLE_Enable = true;
        OldUserBLE_DefaultBallReward = 121;
        if (enableLocalSync || DevInfo.isAppliedManualConditions) {
            return;
        }

        //~~~~~~~~~~~~~~~~~~~~ start test ~~~~~~~~~~~~~~~~~~~~
        //EditorTestConfig_MilestoneEvent();
        //EditorTestConfig_1TutorialSong();
        //EditorTestConfig_TutorialSongList();

        // Economy_MileStone_Enable = true;
        // ProgressBar_StyleIndex = (int) ProgressBarIngameType.Horizontal;
        // TopBarHome_ShowTotalStar = true;

        // Booster_ReviveOffer = true;
        // Booster_ReviveOffer_Rewards = "Shield/1;TileTidy/1;Magnet/1";
        // Booster_ReviveOffer_Capping = 1;
        // Hybrid_UniqueStarIngame = true;
        // EditorTest_HybridUI();
        // EditorTest_IAPSummerPack();
        //TutorialSong_ResultPreview = true;

        //TutorialSong_ResultPreview = true;
        //TopBarHome_ShowTotalStar = true;
        EditorTestTheme();

        //EditorTestConfig_NativeAds();
        //EditorTestConfig_Banner();
        //EditorTestConfig_MultiAds();

        //PlayNewSFX_UP_Enable = true;
        //TutorialTheme_ImproveExperience = true;
        //onboarding_tutorial_gameplay = null;
        //LongTile_IndexLine = (int) LongNoteType.LineDiamond;
        //IsMIDI_FullControl = true;
        //isGetLeaderBoardFromStorage = false;
        //LowEndDevice_Model = "Xiaomi M2102J20SG;vivo 1904;ROG Strix G513QC_G513QC (ASUSTeK COMPUTER INC.)";
        //CommunicationSystem_EnableSquidAnim = true;
        //Home_LeftMenu_ButtonPriorities_IsEnable = true;

        //CommunicationSystem_EnableSquidAnim = true;

        //Home_LeftMenu_ButtonPriorities_IsEnable = true;

        //StartCoroutine(EditorTest_BallConfig());
        //StartCoroutine(EditorTest_BallConfig());
        //EditorTest_UIHomeDecor();

        //EditorTest_LiveEvent_BallSpinner();
        //EditorTest_LiveEvent_BallSpinner();
        //EditorTestConfig_UserProgression();
        //EditorTest_StarJourney(isLock: false);
        //EditorTest_ShopRevampUP();

        //EditorTest_Economy_ImprovedStarterPack();
        //EditorTest_SpecialTileBySingleRule();
        // EditorTest_IAPEasterPack(on: true);
        // EditorTestConfig_MysteryBox();
        //EditorTestConfig_CommunicationPopup();
        //EditorTest_HomePopupFlow();
        //EditorTest_IAPValentinePack();

        //EditorTest_Economy_ImprovedSpecialOffer();
        //EditorTest_IAPChristmasPack();
        //EditorTest_ReviveBooster();

        //EditorTest_InstantMission();
        //EditorTest_UX_Home_Icons();
        //EditorTestConfig_AchievementMissionHybrid();
        //EditorTestTheme();

        EditorTest_Booster();
        //EditorTest_ShopBallVertical();
        //EditorTest_FeverMode();
        //EditorTest_ShopBallVerticle();
        //EditorTestConfig_MysteryBox();
        //EditorTest_UX_Home_Icons();
        //EditorTest_IncreaseLongTile();
        //EditorTest_TileAppearance();
        //EditorTestConfig_EndlessModeIteration();
        EditorTest_MysteryDoor();
        //EditorTestConfig_ContinueBooster();
        EditorTest_ChallengeMode();
    }

    private void RecommendedConfig() {
        MultiSubPackages_Enable = true;
        SubscriptionCatalogue_Enable = true;
        OnboardingFlow_IsShowSubOffer = true;
        SubscriptionFollowLocation_Enable = true;
        Enable_LocalSong = true;

        // FixedScrollviewPositionInHome_Center = true;
        Secure_Data_Enable = true;
        //ACMv4_FireLicenseException = true;
        ACM_IsEnableSquareArtistCover = true;
        Subscription_CTA_ShowPrice = true;
    }

    #endregion

    #region Editor Test Config

    private void EditorTest_Balancy() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Balancy_enable = true;
        Balancy_branch = "endless-offer";
        Balancy_enviroment = "dev";
    }

    private void EditorTest_ChallengeMode() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        ChallengeMode_Enable = true;
        ChallengeMode_SpeedUp = new float[] {0.333f, 0.666f, 1f};
        ChallengeMode_Recurring = false;
        ChallengeMode_Countdown = 3;
        ChallengeMode_AutoClose = true;
        ChallengeMode_AdBreak_Ask = true;
        ChallengeMode_AdBreak_Result = false;
        ChallengeMode_ForceEmptyCrown = true;
        ChallengeMode_ReviveNormal_Config = "Ad_Gems/101";
        ChallengeMode_ReviveEndless_Config = "Ad_Gems/121";
        //Hybrid_UI_Rearrange = true;
        TopBarHome_ShowTotalStar = true;
        ReviveScreen_StyleIndex = 2;
        Onboarding_Ingame_Progressbar_Enable = true;
        OnboardingFlow_EnableProgressBar = true;
    }

    private void EditorTest_MysteryDoor() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        MysteryDoor_IsEnable = true;
        MysteryDoor_Config = JsonUtility.ToJson(GameCore.LiveEvent.MysteryDoor.MysteryDoorConfig.GetDefaultConfig());
    }
    
    private void EditorTestConfig_MilestoneEvent() {
        Remote_Localization += "SUB_INFO,Collect token on tiles to win the reward!\nComplete all steps to win the Grand Prize!,Thu thập token trên viên gạch để giành phần thưởng!,,,,,,,,,,\n";
        Remote_Localization += "INS_TITLE,EVENT UNLOCKED,MỞ KHÓA SỰ KIỆN,,,,,,,,,,\n";
        Remote_Localization += "INS_SUB_INFO,This is an instruction for the Event,Hướng dẫn cho sự kiện,,,,,,,,,,\n";
        
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        StarsJourney_IsEnable = false;
        MileStoneEvent_IsEnable = true;
        MileStoneEvent_ShowWhenLock = true;
        ListHiddenBalls = "127;128;129;130;131;132;133;134;135";
        var config = new MilestoneEventConfig();
        config.unlockCondition = new UnlockCondition {
            stars = 0,
            songStart = 1,
            furtherStars = 0,
            furtherSongStart = 0,
        };
        config.rewardPoolBalls = "111;112;113;114;115;117";
        config.rewardPoolThemes = "11;12;13;14;16;18;19;21;22;23;24";
        config.gemsCompensation = 50;
        config.extraConfigUrl = "https://drive.google.com/uc?export=download&id=1vKmIriKMneMN928DqcsE4j7ekkmPz9Nb";
        config.grandPrizeRewards = new[] {
            new RewardConfig(RewardType.Gem.ToString(), 100),
            new RewardConfig("Booster;Shield", 2),
            new RewardConfig(RewardType.Skin.ToString(), 2),
        };
        MileStoneEvent_Config = JsonUtility.ToJson(config);
    }
    
    private void EditorTestConfig_Banner() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        BannerAd_x_session = 0;
    }

    private void EditorTest_HomePopupFlow() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        HomePopupFlow_IsEnable = true;
        HomePopupFlow_Ordering =
            "type,orderOldUser||GEMS_OLD_USER,1||CHALLENGE_OLD_USER,5||CONSENT,0||FORCE_UPDATE,0||SUBSCRIPTION,1||VIP_DAILY_REWARD,3||ONBOARDING,4||COMMUNICATION_SYSTEM,7||STARTER_PACK,6||MYSTERY_BOX,6||SPECIAL_OFFER,6||SEASONAL_PACK,6||ECONOMY_BOOST,7||RATE_US,8";

        ShopBall_TooltipNewBalls_IsEnable = true;
    }

    private void EditorTest_TileAppearance() {
        TileAppearance_Type = string.Empty;
        TileAppearance_IsRandomEachSong = false;
        TileAppearance_Pool = Util.BuildString(";", TileAppearanceType.RANDOM_2DHOR, TileAppearanceType.RANDOM_8D,
            TileAppearanceType.RANDOM_2DVER);
    }

    private void EditorTest_Booster() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Booster_IsEnable = true;
        PowerCube_IsEnable = true;
        Booster_UnlockVersion = "3";
        PowerCube_UnlockVersion = "3";

        Booster_Shield_StarUnlock = "0;0";
        Booster_Shield_SongStartUnlock = "1;1";

        Booster_TidyTile_StarUnlock = "14;10";
        Booster_TidyTile_SongStartUnlock = "0;0";

        Booster_HyperBoost_StarUnlock = "10;7";
        Booster_HyperBoost_SongStartUnlock = "0;0";

        PowerCube_Unlock_Star = "17;13";
        PowerCube_Unlock_SongStart = "0;0";
    }

    private void EditorTestConfig_TutorialSongList() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        string path = @"Assets\GameCore\ConfigData\Editor\TutorialSongList_Config_test.json";
        TextAsset loadAssetAtPath = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
        TutorialSongList_Config = loadAssetAtPath.text;
    }

    private void TestGroupSettings(string path) {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name} => {path}");

        TextAsset editorConfig = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
        string key = editorConfig.name + "_editor";
        string json = editorConfig.text;

        MergeGroupSettings(new() {{key, json}});
    }

    private void EditorTestConfig_MultiAds() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        Android_LevelPlay_BannerIDs = "0dr8wyf1n6b86vlu;iyq50z3mhksuo0xo";
        Android_LevelPlay_InterstitialIDs = "up0rgv7m9guscthh;fnw2hyp4paqjrkx7";
        Android_LevelPlay_RewardIDs = "6cqhrmk12kgoq8fk;ekx7z1hig5dnye11";
    }

    private void EditorTest_InstantMission() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        InstantMission_Enable = true;
        InstantMission_Config = null;
    }

    private void EditorTest_Album() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        SongDiscovery_UrlAlbumData = null;
    }

    private void EditorTest_IncreaseLongTile() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        LongTile_Increase_ByCode = true; // bật /tắt
        LongTile_Increase_MinTile = 2;
        LongTile_Increase_MaxTile = 3;
        LongTile_Increase_MaxTime = 2f;
        LongTile_Increase_Separate = true;
    }

    private void EditorTest_475_UP_Eco_TileUnlock_Star() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        string path = "Assets/GameCore/UserProgression/Editor/VarTest/475_UP_Eco_TileUnlock_Star.csv";
        var textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
        if (textAsset == null) {
            Logger.LogError($"Not found local config at path: {path}");
            return;
        }

        var text = textAsset.text;
        Dictionary<string, string> configs = LoadResourcesConfig(text);
        ApplyDefaultLocalConfigs(configs);
        UserProgression_DualUnlockSong = true;
    }

    private void EditorTest_UX_Home_Icons() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Home_RightMenu_Toggle_IsEnable = true;
        //Home_RightMenu_Toggle_Features = "SevenDayMission;Achievement;MysteryBox";
        Home_RightMenu_Toggle_Features =
            "FreeGift;ShopIAP;DiscoverChallenge;SevenDayMission;DailyMission;Achievement;MysteryBox;VipMission";
        WeeklyMission_IsEnable = true;
    }

    private void EditorTest_FeverMode() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        FeverMode_IsEnable = true;
        FeverMode_Perfect = 20;
        FeverMode_Multiply = 1.15f;
        FeverMode_Time = 15;
        FeverMode_Cooldown = 5f;
    }

    private void EditorTest_ShopBallVertical() {
        Logger.LogError($"[{GetType().Name}] => {System.Reflection.MethodBase.GetCurrentMethod()?.Name}");
        ShopBall_VerticleScroll = true;
    }

    private void EditorTest_UIHomeDecor() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        HomeDecor_UserProfile = true;
        HomeDecor_SongCard = true;
        HomeDecor_SpecialParticles = true;
        HomeDecor_BottomMenuDecal = true;
        HomeDecor_SongDisk = true;
    }

    private void EditorTest_LiveEvent_BallSpinner() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        var textAsset =
            AssetDatabase.LoadAssetAtPath<TextAsset>(
                "Assets/GameCore/LiveEvent-BallSpinner/Editor/local_ballspinner_settings.csv");
        var text = textAsset.text;
        Dictionary<string, string> configs = LoadResourcesConfig(text);
        ApplyDefaultLocalConfigs(configs);
    }

    private void EditorTestTheme() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Theme_ConfigUrl = null;
        Theme_IsEnableSelectTheme = true;
        Theme_IsEnableSelectThemeAfterReward = false;
        //urlBallConfig = null;
    }

    private void EditorTest_NewSubpackages() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        string weekPackage = "com.amanotes.pamadr_sub_1w_6.99usd_freetrial_3d0.202";
        string monthPackage = "com.amanotes.beathopper.vipsubmonth.01";
        string yearPackage = "com.amanotes.beathopper.vipsubyear.01";

        monthPackage = "com.amanotes.pamadr_sub_1m_14.99usd_freetrial_3d0.2024";
        // string yearPackage = "com.amanotes.pamadr_sub_1y_49.99usd_freetrial_3d0.2024";

        MultiSubPackages_Enable = true;
        SubPackages =
            $"{{\r\n  \"week\": {{\r\n    \"id\": \"{weekPackage}\",\r\n    \"price\": \"$5.99\",\r\n    \"saleoff\": \"\"\r\n  }},\r\n  \"month\": {{\r\n    \"id\": \"{monthPackage}\",\r\n    \"price\": \"$19.99\",\r\n    \"week_price\": \"$4.99\",\r\n    \"saleoff\": \"30%\"\r\n  }},\r\n  \"year\": {{\r\n    \"id\": \"{yearPackage}\",\r\n    \"price\": \"$99.99\",\r\n    \"week_price\": \"$2.99\",\r\n    \"saleoff\": \"60%\"\r\n  }}\r\n}}";
        paywall_weekly_plan_optimization = string.Empty;
    }

    private IEnumerator EditorTest_BallConfig() {
        Logger.LogError($"[{GetType().Name}] => EditorTest_BallConfig");

        yield return Inwave.Utils.WaitUntil(() => BallManager.isInstanced);
#if UNITY_EDITOR
        //ListHiddenBalls = Util.BuildString(";", BallManager.SnowHop, BallManager.MisterClaus);
        ListHiddenBalls = "";
        urlBallConfig = "";
        string path = @"Assets\GameCore\ConfigData\Editor\ballconfig_Test.csv";
        TextAsset loadAssetAtPath = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
        BallManager.instance.SetDefaultBallConfig(loadAssetAtPath);
#endif
    }

    private void EditorTest_AOA() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        AdOpenApp_Config = JsonUtility.ToJson(new ABAppOpenAds().SetTestConfig());
    }

    private void EditorTestConfig_NativeAds() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        NativeAds_Style = 2;
    }

    private void EditorTest_ShopRevampUP() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        ShopUI_Revamp_Enable = true;
        ShopAdvancedPack_IsEnable = true;
        ShopAdvancedPack_Gems = 1500;
        ShopAdvancedPack_UnlimitedRevive = 10;
        ShopAdvancedPack_Booster = "[{\"name\":\"Shield\",\"amount\":3},{\"name\":\"GemRain\",\"amount\":1}]";
        ShopMasterPack_IsEnable = true;
        ShopMasterPack_Gems = 3000;
        ShopMasterPack_UnlimitedRevive = 15;
        ShopMasterPack_Booster = "[{\"name\":\"Shield\",\"amount\":5},{\"name\":\"TileTidy\",\"amount\":3}]";

        DiamondShop_Config =
            "{\"packages\":[{\"idPack\":\"diamond1\",\"value\":500,\"androidId\":\"com.amanotes.beathopper_iap.gem_0.99usd\",\"iosId\":\"com.amanotes.beathopper_iap.gem_0.99usd\"},{\"idPack\":\"diamond2\",\"value\":2000,\"bargainPercent\":30,\"androidId\":\"com.amanotes.beathopper_iap.gem_2.99usd\",\"iosId\":\"com.amanotes.beathopper_iap.gem_2.99usd\"},{\"idPack\":\"diamond3\",\"value\":4500,\"bargainPercent\":50,\"androidId\":\"com.amanotes.beathopper_iap.gem_4.99usd\",\"iosId\":\"com.amanotes.beathopper_iap.gem_4.99usd\"},{\"idPack\":\"diamond4\",\"value\":10000,\"androidId\":\"com.amanotes.beathopper_iap.gem_9.99usd\",\"iosId\":\"com.amanotes.beathopper_iap.gem_9.99usd\"},{\"idPack\":\"diamond5\",\"value\":22000,\"bargainPercent\":70,\"androidId\":\"com.amanotes.beathopper_iap.gem_19.99usd\",\"iosId\":\"com.amanotes.beathopper_iap.gem_19.99usd\"},{\"idPack\":\"diamond6\",\"value\":50000,\"androidId\":\"com.amanotes.beathopper_iap.gem_0.99usd\",\"iosId\":\"com.amanotes.beathopper_iap.gem_0.99usd\"}]}";

        ShopDailyDeal_IsEnable = true;
        ShopDailyDeal_Require = new int[] {0, 1, 2};
        ShopDailyDeal_Reward = new[] {20, 50, 75};
        ShopDailyDeal_IntervalTime = 0.02f;
    }

    private void EditorTest_ShopBallRevamp() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        ShopBall_EntryPointV2_IsEnable = false;
        ShopBall_Category_Tags = "All;My Balls;New;Sale;Legendary;Favorites;Fun;Sporty;Seasonal;Empty";
        ShopBall_UseCategoryLayout = true;
        ShopBall_Transition_IsEnable = true;
        ShopBall_SaleOff_IsEnable = true;
        ShopBall_LayoutStyleIndex = 3;
        ShopBall_TooltipNewBalls_IsEnable = true;
    }

    private void EditorTest_ShopBallRotate() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        // ShopBall_Rotate_IsEnable = true;
        // ShopBall_Rotate_Onboarding = true;
        // ShopBall_Rotate_Sensitive = 120;
    }

    private void EditorTest_ReviveByGems() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        ReviveScreen_UsingGems = true;
        ReviveScreen_GemsPriceList = "Ad_Gems/1;Gems/2;Ad/0;Ad_Gems/3;Gems/5";
        Hybrid_LoseGemsOnFailed = true;
        Hybrid_UI_Rearrange = true;
    }

    private void EditorTest_HybridUI() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        ReviveScreen_UsingGems = true;
        ReviveScreen_GemsPriceList = "Ad_Gems/100;Ad_Gems/100;Gems/150;Gems/200;Gems/250;Gems/300";
        Hybrid_LoseGemsOnFailed = true;
        Hybrid_UI_Rearrange = true;
        EditorTest_UX_Home_Icons();
    }

    private void EditorTest_ReviveBooster() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        ReviveScreen_StyleIndex = 2;
        ReviveScreen_Banner_List = "StarterPack;TripleOffers;EndlessOffer";
    }

    private void EditorTest_BouncingBall() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        BouncingBall_UseBouncingBall = true;
        BouncingBall_UseBallRotation = true;
        BouncingBall_MaxStretch_X = 1.8f;
        BouncingBall_MaxStretch_Y = 3f;
        BouncingBall_MinPercentage = 0f;
    }

    private void EditorTest_FSRewardPool() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        AdBreak_Rewards_Pool_IsEnable = true;
        AdBreak_Rewards = "Ball";
        AdBreak_Rewards_Ball_Pool = new[] {20, 8, 19, 59, 58, 17, 16};
    }

    private void EditorTest_DiscoveryRevamp() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        SongDiscovery_DataOrder =
            "Banners;FreeSongToday;ChallengeEntry;HardCoreCollection;Populars;Artists;Albums;Genres;FavouriteSong;RecentSong";
        SongDiscovery_DataBanners = "{\"banners\":[" +
                                    "{\"Id\":0,\"Order\":0,\"Url\":\"https://d1xncywiopk9ma.cloudfront.net/Songs/BannerDiscovery/08ff8118-553c-45e2-924a-d0d04f5b6b23.png\",\"Navigation\":\"{\\\"type\\\":\\\"PlaySong\\\",\\\"param\\\":{\\\"acmID\\\":\\\"0457a9c8-4bdd-4699-9da5-22d9fe318fc6\\\"}}\",\"Alt\":\"Banner 1\"}," +
                                    "{\"Id\":1,\"Order\":1,\"Url\":\"https://d1xncywiopk9ma.cloudfront.net/Songs/BannerDiscovery/559d507f-7138-4cf1-bfa9-58bb31b9862e.png\",\"Navigation\":\"{\\\"type\\\":\\\"Navigate\\\",\\\"param\\\":{\\\"path\\\":\\\"DISCOVERY/ALBUM/Undertale x Delterune\\\"}}\",\"Alt\":\"Banner 2\"}]}";
    }

    private void EditorTest_DiscoveryChallenge() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        DiscoveryChallenge_Is_Enable = true;
        DiscoveryChallenge_Config = JsonUtility.ToJson(new DiscoveryChallengeConfig().SetTestConfig());
        DiscoveryChallenge_ChallengeList_Path =
            "https://d1xncywiopk9ma.cloudfront.net/Songs/SongConfig/Discovery_Challenge_Medium.csv";
        DiscoveryChallenge_HardcoreList_Path =
            "https://d1xncywiopk9ma.cloudfront.net/Songs/SongConfig/Discovery_GoldenBox_songlist.csv";
        DiscoveryChallenge_LocalizedTexts = "DISCOVER_CHALLENGE_TITLE,Hardcore hits!,Tiêu đề,,,,,,,,,,\n" +
                                            "DISCOVER_CHALLENGE_DESCRIPTION,Description,Mô tả,,,,,,,,,,";
        NewElements_Enable_HC_CS = true;
        NewElements_Enable_HC_HS = true;
        DiscoveryChallenge_NewUI = true;
    }

    private void EditorTest_PauseQuitAP() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        PauseQuitAP_DeviceBackButton_IsIgnore = false;
        PauseQuitAP_PauseButton_IsEnable = true;
        PauseQuitAP_ExitButton_IsEnable = true;
        PauseQuitAP_EndButtonBottom = true;
        PauseQuitAP_UsingResumeButton = false;
        PauseQuitAP_ButtonSettingWithBackground = true;
    }

    private void EditorTest_TrySkin() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        TrySkin_Enable = true;
        TrySkin_TypeBall = "LOCK,VIDEO,DIAMOND";
        TrySkin_Style = 1; //1: chạy tiếp sức 2: đổi character
        TrySkin_PercentAppear = 10;

        TrySkin_LimitInDay = 9999;

        //giá trị 0 => lúc nào cũng hiện; giá trị x => cứ cách x bài thì hiện 1 lần
        TrySkin_FirstSession_FirstShow = 2;
        TrySkin_FirstSession_NextShow = 2;
        TrySkin_NextSession_FirstShow = 0;
        TrySkin_NextSession_NextShow = 2;
        TrySkin_UniqueShow = true;

        TrySkin_IsRandomSkin = true;
        TrySkin_SkinIdList = new[] {83, 84, 85};
        TrySkin_MaxTimeOffer = 999; //Sau 1 số lần liên tiếp mà user ko unlock thì tắt tính năng này

        TrySkin_ShowBeforeFSAds = true;
        TrySkin_ShowFSAdsAfterUnlockBall = false;
        TrySkinFeature.InitData();
    }

    private void EditorTest_FreeChallenge() {
        if (!ChallengeOldUser_IsEnable) {
            Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
            ChallengeOldUser_IsEnable = true;
            var testConfig = ChallengeOldUserConfig.GetTestConfig();
            ChallengeOldUser_Config = JsonUtility.ToJson(testConfig);
            Midi_FullControl_Force += $";{testConfig.SongChallenge}";
            ChallengeOldUserController.instanceSafe.Init(ChallengeOldUser_Config);
            urlBallConfig = null;
        } else {
            //AppsFlyerInit.instanceAFI.SetCampaign("BHaUSRemerge");
            AppsFlyerInit.instanceAFI.SetScheme("tilehop://challenge");
        }
    }

    private void EditorTest_IAPSummerPack() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        SeasonalPack_PackName = "SUMMER";
        SeasonalPack_Config = JsonUtility.ToJson(new IAP_SummerPackConfig().GetTest());
    }

    private void EditorTest_IAPHalloweenPack() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        SeasonalPack_PackName = "HALLOWEEN";
        SeasonalPack_Config = JsonUtility.ToJson(new IAP_HalloweenPackConfig().GetTest());
    }

    private void EditorTest_IAPChristmasPack() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        SeasonalPack_PackName = "CHRISTMAS";
        SeasonalPack_Config = JsonUtility.ToJson(new IAP_ChristmasConfig().GetTest());
        ListHiddenSongs = "27b8182d-21b4-43fe-b614-6c84ca122229";
    }

    private void EditorTest_IAPValentinePack(bool on = true) {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        SeasonalPack_PackName = on ? "VALENTINE" : "NONE";
        SeasonalPack_Config = JsonUtility.ToJson(new IAP_ValentineConfig().GetTest());
    }

    private void EditorTest_IAPEasterPack(bool on = true) {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        SeasonalPack_PackName = on ? "EASTER" : "NONE";
        SeasonalPack_Config = JsonUtility.ToJson(new IAP_EasterConfig().GetTest());
    }

    private void EditorTest_MIDIForceControl() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Midi_FullControl_Force =
            "8ccfad4d-d5ee-4477-b05e-68eee8ee748b;ccd10c58-27b8-4917-bbf1-839c50ff6758;cab8ca34-76d6-4ce5-9a39-bd54a59ffc24"; //crazy fog, dance monkey
    }

    private void EditorTest_MidiMultipleVersion() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        MidiMultipleVersion_IsEnable = true;
        MidiMultipleVersion_Config =
            "https://docs.google.com/spreadsheets/d/1Dhe-UX2lboqAGXRP1vrs_hRLcLduHs8I63-uAMd0oLc/export?format=csv";
    }

    private void EditorTest_FakeTileFollowPitch() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        FakeTile_Follow_Pitch = true;
        FakeTile_Follow_Pitch_Include =
            "8ccfad4d-d5ee-4477-b05e-68eee8ee748b;ccd10c58-27b8-4917-bbf1-839c50ff6758"; //crazy fog, dance monkey
        FakeTile_Follow_Pitch_Exclude = "";
    }

    private void EditorTest_PreviewMusicImprove() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        PreviewMusic_Improve_IsEnable = true;
        PreviewMusic_AutoFixScrollviewPosition = true;
        PreviewMusic_Index = 0;
        PreviewMusic_AutoReplayPreviousSong = true;
        PreviewMusic_TimeDelay = 0.5f;
        PreviewMusic_AutoResetWhenComeBackHome = true;
    }

    private void EditorTestConfig_AchievementMissionHybrid() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Hybrid_Achievement_IsEnable = true;
        Hybrid_Mission_IsEnable = true;
        Hybrid_Achievement_ConfigUrl = "https://test.inwave.vn/upload-files/data/Achievements.csv";
        Hybrid_Mission_Daily = "";
        //Hybrid_Mission_Weekly = "";
        Hybrid_Mission_Instant = "";
    }

    private void EditorTestConfig_UserProgression() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        UserProgression_IsEnable = true;
        //UserProgression_Config = JsonUtility.ToJson(UserProgressionConfig.GetTest());
        UserProgression_SongPack_ConfigUrl = null;
        UserProgression_ExpConfig = new[] {
            0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500
        };
        UserProgression_UnlockAll_Type = SONGTYPE.LOCK.ToString();
        UserProgression_UnlockAll_Value = 99;

        UserProgression_ShowLockedSong = false;
        UserProgression_ShowDiscoverBeforeUnlockAll = true;
        UserProgression_DualUnlockSong = true;
    }

    private void EditorTest_StarJourney(bool isLock = false) {
        BadgeNoti_EnableSyncAnimation = true;
        StarsJourney_IsEnable = true;
        StarsJourney_IsLockTopMenu = isLock;
        StarsJourney_StarsFlyFromSongDisk = true;
        StarsJourney_ConfigUrl = null; //"https://test.inwave.vn/upload-files/data/StarsJourney.csv";
    }

    private void EditorTest_UserProfile() {
        UserProfile_UseNewProfile = true;
        if (UserProfile_UseNewProfile) {
            Logger.EditorLogError("EditorTestConfig", "Using new profile page instead of leader board");
        }
    }

    private void EditorTest_SpecialLearningCurve() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        // Tile_Special_RuleShow_Order =
        //     "FadeOut,SpecialHyperBoost,FadeInOut,FakeTile,Teleport,MovingTile,MovingCircle,SpecialUpsideDown,FakeThunder,FakeConveyor,SpecialMirror,FakeTransform,MoodChangeBroken,MoodChangeInstant,SpecialZicZac,MoodChangeOrder,MoodChangeTrain,LongTileBreak";
        NewElements_Enable = true;
        NewElements_RuleShow_Order =
            "FadeOut,FadeInOut,MovingCircle,Teleport,FakeTransform,SpecialMirror,SpecialUpsideDown";
        // Tile_Special_RuleShow_SongCount = new int[]
        //     {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20};
        NewElements_RuleShow_SongCount = new int[] {1, 3, 5, 7, 9, 11, 13};
        NewElements_RuleShow_NewElementPercent = 35;
        NewElements_RuleShow_OldElementPercent = 20;
    }

    private void EditorTest_SpecialTileByDifficulty() {
        NewElements_Enable = true;
        NewElements_AfterSongStart = 3;
        NewElements_OverLapOtherRule = true;
        NewElements_MidiAutoGenElement_Enable = true;
        NewElements_Onboarding_Enable = true;
        NewElements_OverLapMidiManualElement = true;
        NewElements_NormalAutoGenElement_Enable = true;
        NewElements_AutoGenElement_Max = 3;

        DifficultyTag_IsEnable = true;
    }

    private void EditorTest_SpecialTileBySingleRule() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        NewElements_Enable = true;
        NewElements_AutoGen_List = string.Join(";", NoteElementType.LongTileBreak.ToString(),
            NoteElementType.FakeConveyor.ToString(), NoteElementType.Teleport.ToString(),
            NoteElementType.FadeOut.ToString(), NoteElementType.MovingCircle.ToString(),
            NoteElementType.SpecialHyperBoost.ToString(), NoteElementType.SpecialMirror.ToString(),
            NoteElementType.SpecialUpsideDown.ToString());

        NewElements_MovingCircle_UseMidiPos = false;
        NewElements_AutoGen_Group_Start = 1;

        // brick
        NewElements_Brick_Appear = 0;
        NewElements_Brick_Star = 3;

        // conveyor
        NewElements_Conveyor_Appear = 0;
        NewElements_Conveyor_Star = 6;
        NewElements_Conveyor_MoodChange = 2;

        // teleport
        NewElements_Teleport_Appear = 8;
        NewElements_Teleport_Star = 9;
        NewElements_Teleport_MoodChange = 3;

        // fade out
        NewElements_FadeOut_Appear = 0;
        NewElements_FadeOut_Star = 12;
        NewElements_FadeOut_MoodChange = 2;

        // moving circle
        NewElements_MovingCircle_Appear = 0;
        NewElements_MovingCircle_Star = 15;
        NewElements_MovingCircle_MoodChange = 3;

        // hyper star
        NewElements_HyperBoost_Appear = 0;
        NewElements_HyperBoost_Star = 18;

        // mirror
        NewElements_Mirror_Appear = 0;
        NewElements_Mirror_Star = 21;

        // upsidedown
        NewElements_UpSideDown_Appear = 0;
        NewElements_UpSideDown_Star = 24;
    }

    private void EditorTestConfig_SpecialSection() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        NewElements_Enable = true;
        NewElements_Section_Index = new int[] {(int) IngameSectionType.ZicZac,};
        NewElements_HyperBoost_FlyHeight = 4;
    }

    private void EditorTestConfig_TileSpecial() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        NewElements_Enable = true;
        NewElements_Tile_index = new int[] {(int) SpecialTileIndex.FadeOut,};
        NewElements_MoodChange_Index = new int[] {(int) SpecialMoodChangeIndex.MoodBreak};
        NewElements_MovingCircle_Speed = 8f;
        NewElements_MovingCircle_UseMidiPos = false;
        NewElements_TransformTrap_Index = 2;
    }

    private void EditorTestConfig_TrySkin() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        TrySkin_Enable = true;
        TrySkin_PercentAppear = 8;
        TrySkin_LimitInDay = 1000;
        TrySkin_SkinIdList = null;
    }

    private void EditorTestConfig_SongStructure() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        SongStructure_Enable = true;
    }

    private void EditorTest_Economy_SpecialOffer() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Economy_SpecialOffer = JsonUtility.ToJson(new SpecialOfferConfig());
        EditorTest_Economy_IAP();
    }

    private void EditorTest_Economy_ImprovedStarterPack() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        StarterPack_IsEnable = true;
        StarterPack_Config = JsonUtility.ToJson(new StarterPackConfig(5));
        StarterPack_Version = 4;
    }

    private void EditorTest_Economy_ImprovedSpecialOffer() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Economy_SpecialOffer = JsonUtility.ToJson(new SpecialOfferConfig());
        EditorTest_Economy_Config(1);
        Economy_Topbar_IsFixed = false;
        Subscription_Enable = true;
        Economy_buttonShopInHome_Enable = false;
        Economy_VIP_BenefitAds_IsEnable = true;
        Economy_SpecialOffer_Version = 2;

        var defaultIapData = new EconomyIAPRemoteData(4);
        Economy_IAP_data = JsonUtility.ToJson(defaultIapData);
        ReviveScreen_ShowSubscription_RemainRevive = true;
    }

    private void EditorTestConfig_RatePopup() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        RatePopup_StyleIndex = 2;
        RatePopup_Config = JsonUtility.ToJson(new RatePopupConfig());
    }

    private void EditorTestConfig_PerfectSize() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Tile_PerfectIcon_Enable = true;
        Tile_PerfectIcon_Size = new Vector3(0.33f, 0.29f, 1f);
        Tile_PerfectIcon_AnimEnable = true;
    }

    private void EditorTest_Economy_Config(int variantConfig) {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        //Economy
        Economy_Variant_Config = variantConfig;
        Logger.LogError($"Fixed config for variant config: {Economy_Variant_Config}");
        if (Economy_Variant_Config > 0) {
            Economy_IsEnable = true;
            Onboarding_Ingame_Progressbar_Enable = true;
            OnboardingFlow_EnableProgressBar = true;
            Onboarding_Result_IntroGem_Enable = false;
            Onboarding_Ingame_ShopballNotice_Enable = false;
            AdBreak_InterstitialReward = false;
            SongOfDay_IsShow = false;
            Economy_MileStone_1st_Value = 10;
            Economy_MileStone_Enable = true;
            Economy_MileStone_Endless_Detailed = false; // ( chỉ nhận 1 cục tại 1 loop)
            Economy_TutorialReward_Enable = true;
            Economy_TutorialReward_Value = 200;
            Economy_Diamond_Ingame_XStartTiles = 10;
            Economy_SongPrice = 100;
            LongTile_IndexLine = 1;
            Diamond_SpawnRate = 0;
            Diamond_Init = -20;
            Economy_MileStone_RepeatedEat_Normal = false; //Không repeat ăn gem ở normal
            Economy_MileStone_RepeatedEat_Endless = true; //Repeat ăn gem ở endless
            OnboardingFlow_IsShowPlayNext = false;
            NextBtn_IsEnable = false;
            ResultScreen_UseEconomyResult = true;
        }

        switch (Economy_Variant_Config) {
            case 0: // base line - no economy
                AdBreak_InterstitialReward = false;
                SongOfDay_IsShow = false;
                Onboarding_Ingame_Progressbar_Enable = false;
                Onboarding_Result_IntroGem_Enable = false;
                Onboarding_Ingame_ShopballNotice_Enable = false;
                Economy_MileStone_Enable = false;
                EndlessMode_Enable = true; // bật endless mode
                LongTile_IndexLine = 0;
                break;

            case 1: // Variant 1: The old Variant 6
                Economy_MileStone_2nd_Value = 10;
                Economy_MileStone_3rd_Value = 20;
                Economy_MileStone_Endless_Value = 40;
                break;

            case 2: //Variant 2 (off endless mode)
                Economy_TutorialReward_Value = 300;
                Economy_MileStone_2nd_Value = 10;
                Economy_MileStone_3rd_Value = 20;
                Economy_MileStone_Endless_Value = 40;
                break;

            case 3: //"Variant 3 (Endless mode receives the same value as normal mode.)"
                Economy_MileStone_2nd_Value = 15;
                Economy_MileStone_3rd_Value = 25;
                Economy_MileStone_Endless_Value = 50;
                break;

            case 4: //Variant 4 (Change ratio)
                Economy_MileStone_2nd_Value = 15;
                Economy_MileStone_3rd_Value = 25;
                Economy_MileStone_Endless_Value = 100;
                break;

            case 5: //Variant 5
                Economy_MileStone_2nd_Value = 10;
                Economy_MileStone_3rd_Value = 20;
                Economy_MileStone_Endless_Value = 40;
                Economy_SongPrice = 80;
                break;

            case 6: //Variant 6
                Economy_MileStone_2nd_Value = 10;
                Economy_MileStone_3rd_Value = 20;
                Economy_MileStone_Endless_Value = 40;
                break;
        }
    }

    private void EditorTest_Economy_IAP() {
        EditorTest_Economy_Config(1);
        int variant = 3;
        switch (variant) {
            case 1:
                Economy_Topbar_IsFixed = true;
                Subscription_Enable = false;
                Economy_buttonShopInHome_Enable = false;
                break;

            case 2:
                Economy_Topbar_IsFixed = true;
                Subscription_Enable = true;
                Economy_buttonShopInHome_Enable = false;
                break;

            case 3:
                Economy_Topbar_IsFixed = true;
                Subscription_Enable = true;
                Economy_buttonShopInHome_Enable = false;
                Economy_VIP_BenefitAds_IsEnable = true;
                ReviveScreen_ShowSubscription_RemainRevive = true;
                break;
        }

        var defaultIAPdata = new EconomyIAPRemoteData(variant);
        Economy_IAP_data = JsonUtility.ToJson(defaultIAPdata);
    }

    private void EditorTestConfig_LiveEvent() {
#if UNITY_EDITOR
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        LiveEvent_Enable = true;
        if (LiveEvent_Data_test != null) {
            LiveEvent_Data = LiveEvent_Data_test.text;
        }

        LiveEvent_ReplaceLockedSongAndBall = true; // thay thế ball và song

        CommunicationSystem_IsOn = true;
        CommunicationSystem_IsTesting = true;
        INGAME_POPUP_Test = "[\"https://d3vvl57xnvh6za.cloudfront.net/INGAME%2Fpopup_16_L0L_1692242871507.json\"]";

        //urlBallConfig = "https://d1xncywiopk9ma.cloudfront.net/Songs/BallConfig/ballconfig-hlw.csv";
        //SongList_Path = "https://d1xncywiopk9ma.cloudfront.net/Songs/SongConfig/acmv4_hlw2.csv";
        //SongList_Version = "2023.07.25";
#endif
    }

    private void EditorTestConfig_TutorialSong(int tutorialSongRule) {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        TutorialSong_Rule = tutorialSongRule;
        switch (TutorialSong_Rule) {
            case 0:
                // mặc định -> không can thiệp gì
                break;

            case 1:
            case 2:
                // Rule 1 + 2
                TutorialSongList_IsEnableRemote = true;
#if UNITY_EDITOR
                //TutorialSongList_Config = TutorialSongList_Config_test.text;
#endif
                break;
        }
    }

    private void EditorTestConfig_DoubleReward(int variant) {
        EditorTest_Economy_Config(1);
        switch (variant) {
            case 0: // base line
                Economy_dia_watched_ads_required_location = string.Empty;
                break;

            case 1:
                Economy_dia_watched_ads_value = 100;
                Economy_dia_watched_ads_required_diamond = 100;
                Economy_dia_watched_ads_required_songstart = 1;
                Economy_dia_watched_ads_required_location = "Home";
                Economy_dia_watched_ads_session_per_day = 10;

                ResultScreen_AdsMultiply = 2;
                ResultScreen_AdsMinDiamondInStorage = int.MaxValue; //no limited
                ResultScreen_AdsMinDiamond = 0; //no limited
                ResultScreen_AdsCapping = 6;

                Economy_dia_sale_IAP_required_location = string.Empty;
                break;

            case 2:
                Economy_dia_watched_ads_value = 100;
                Economy_dia_watched_ads_required_diamond = 100;
                Economy_dia_watched_ads_required_songstart = 1;
                Economy_dia_watched_ads_required_location = "Home";
                Economy_dia_watched_ads_session_per_day = 10;

                ResultScreen_AdsMultiply = 2;
                ResultScreen_AdsMinDiamondInStorage = 150;
                ResultScreen_AdsMinDiamond = 10;
                ResultScreen_AdsMaxDiamond = 150;
                ResultScreen_AdsCapping = int.MaxValue; //no limited

                //Economy_dia_sale_IAP_required_location = string.Empty;
                break;

            case 3:
                break;
        }
    }

    private void EditorTestConfig_KnobPlaytimeReward() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        knob_enable_condition = "{\"after_song_start\": 2 , \"after_day_diff\": 1}";
        knob_data =
            "{\"gem_per_minute\": 3 , \"gem_max\": 30 ,\"multiply_value\": \"2,3,5\" ,\"multiply_speed\": \"2,3,5\" ,\"multiply_display\": true, \"enable_tooltip\": true }";
    }

    private void EditorTestConfig_FTUE_Part2() {
#if UNITY_EDITOR
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        onboarding_demographic_improve = 1;
        onboarding_tutorial_improve = 1;

        // if (TutorialSongList_Config_test != null) {
        //     TutorialSongList_Config = TutorialSongList_Config_test.text;
        // }
#endif
    }

    private void EditorTestConfig_FTUE_Part3() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        onboarding_tutorial_gameplay = JsonUtility.ToJson(new TutorialGamePlayConfig());
        ReviveTutorial_Enable = true;
        ReviveTutorial_NumberTile = 1;
    }

    private void EditorTestConfig_ResultScreen() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        ResultScreen_StyleIndex = 2;
        ResultScreen_IsShowCrownEndless = true;

        SongItem_StyleIndex = 6;

        ProgressByScore_IsEnable = false;
        ProgressByScore_Star = new[] {10, 50, 70};
        EndlessMode_UnlockByStar = 3;

        OnboardingFlow_EnableProgressBar = true;
        ProgressBar_StyleIndex = 3;

        FixedScrollviewPositionInHome_IsEnable = true;

        ACM_IsEnableSquareArtistCover = true;

        //force show inter
        dailyMaxReward = 99;
        Ad_MaxGamesBetweenFS = 0;
        Ad_SongTimeShowFS = 0;
        OnboardingFlow_IsEnable = true;
        OnboardingFlow_AdAfterTutorial = 0;
    }

    private void EditorTestConfig_MysteryBox() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        mysterybox_enable = true;
        mysterybox_daily = true;
        mysterybox_replace_freegift = false;
        mysterybox_config = JsonUtility.ToJson(new MysteryV2Config());
    }

    private void EditorTestConfig_CommunicationPopup() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        CommunicationSystem_IsOn = true;
        CommunicationSystem_IsTesting = true;
        INGAME_POPUP_Test = "[\"https://d3vvl57xnvh6za.cloudfront.net/INGAME%2Fpopup_4_L0L_1724379099251.json\"]";
    }

    private void EditorTestConfig_EventAppflyer() {
#if UNITY_EDITOR
        if (AfEventsConfig_test != null) {
            AfEventsConfig = AfEventsConfig_test.text;
        }
#endif
    }

    private void EditorTestConfig_DownloadSong() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        DownloadSong_v2_IsEnable = true;
        DownloadSong_v2_Tips = new[] {1, 2, 3, 4, 5, 6};
    }

    private void EditorTestConfig_OpenEndingVFX() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        VFX_Opening_IsEnable = true;
        VFX_Dead_IsEnable = true;
        VFX_Dead_KeepTileVisible = true;
    }

    private void EditorTestConfig_LongNote() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        LongTile_IndexLine = (int) LongNoteType.LongTile;
    }

    private void EditorTestConfig_LongNoteV2() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        LongTile_IndexLine = (int) LongNoteType.None;
        LongTile_RangeRandom = null;

        LongNote_v2_IsEnable = true;
        var defaultConfig = new TilesHop.LongNote.LongNoteConfig();
        LongNote_Config = JsonUtility.ToJson(defaultConfig);
    }

    private void EditorTestConfig_SelectThemes() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        Theme_IsEnableSelectTheme = true;
        Theme_DisableSelectionFixTheme = false;
        Theme_CanPreviewThemeVariants = true;
    }

    private void EditorTestConfig_NotificationBox() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        NotificationBox_IsEnable = true;
    }

    private void EditorTestConfig_DifficultyTag() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        DifficultyTag_IsEnable = true;
        DifficultyTag_HideNone = true;
        DifficultyTag_HideEasy = true;
        DifficultyTag_HideMedium = false;
        DifficultyTag_HideHard = false;
        DifficultyTag_HideExtreme = false;
        DifficultyTag_HideInsane = false;
    }

    private void EditorTestConfig_NewMidi() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");
        IsMIDI_FullControl = true;
        Musicalization_LongNoteDuration = 0.5f; // độ dài tính là 0.5s;
        LongTile_IndexLine = 10; // tile dài
    }

    private void EditorTestConfig_SubPaywallWeekly() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        var defaultConfig = new PayWallWeeklyConfig();
        paywall_weekly_plan_optimization = JsonUtility.ToJson(defaultConfig);
    }

    private void EditorTestConfig_EndlessModeIteration() {
        EndlessMode_Iteration = true;
        EndlessMode_LowValue_FSRound = 1;
        EndlessMode_HighValue_FSRound = 3;
        EndlessMode_Iteration_HighValue_Enable = true;
        ResultScreen_StyleIndex = 6;
    }

    private void EditorTestConfig_ContinueBooster() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        ReviveScreen_StyleIndex = 2;
    }

    private void EditorTestConfig_1TutorialSong() {
        Logger.LogError($"[{GetType().Name}] => {MethodBase.GetCurrentMethod()?.Name}");

        TutorialSong_AcmId = "d1e9f6c5-42e0-477c-b74a-73bf61ecb4e9";
    }

    #endregion
}
#endif