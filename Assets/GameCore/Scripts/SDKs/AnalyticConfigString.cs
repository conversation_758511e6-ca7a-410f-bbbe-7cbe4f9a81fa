// ReSharper disable InconsistentNaming

public struct TRACK_NAME {
    public const string difficulty_tag = "difficulty_tag";

    //Core
    public const string Session_ID       = "Session_ID";
    public const string Session_Duration = "Session_Duration";
    public const string country_code     = "country_code";
    public const string error_code       = "error_code";
    public const string user_VIP_status  = "user_VIP_status";

    public const string vip_status = "vip_status";

    //Appsflyer convert
    public const string new_firstopen    = "new_firstopen";
    public const string video_ads_1_song = "video_ads_1_song";

    //Song
    public const string song_name        = "song_name";
    public const string song_id          = "song_id";
    public const string song_type        = "type_song";
    public const string song_mode        = "song_mode";
    public const string local_song       = "local_song";
    public const string song_list        = "song_list";
    public const string song_last_note   = "song_last_note";
    public const string song_unlock_type = "song_unlock_type"; //old name => unlock
    public const string playtime         = "playtime";
    public const string able_to_revive   = "able_to_revive";
    public const string audiodevice_info = "audiodevice_info"; // song_force_close

    public const string song_play_type  = "song_play_type";
    public const string revive_count    = "revive_count";
    public const string user_activated  = "user_activated";
    public const string death_positionX = "death_positionX";

    public const string videoads_       = "videoads_";
    public const string fullAds         = "fullads_";
    public const string bannerAds       = "bannerads_";
    public const string iap             = "iap_";
    public const string share           = "share";
    public const string item_name       = "item_name";
    public const string item_buy        = "item_buy";
    public const string item_type       = "item_type";
    public const string item_price      = "item_price";
    public const string item_value      = "item_value";
    public const string item_id         = "item_id";
    public const string item_source     = "item_source";
    public const string item_usage      = "item_usage";
    public const string currency_income = "currency_income";
    public const string type            = "type";
    public const string pack_id         = "pack_id";
    public const string pack_name       = "pack_name";

    public const string name        = "name";
    public const string end_time    = "end_time";
    public const string retry_times = "retry_times";
    public const string stars       = "stars";
    public const string screen      = "screen";

    public const string download_failed     = "download_failed_new";
    public const string url                 = "url";
    public const string error               = "error";
    public const string needmore            = "needmore";
    public const string popup_shop          = "popup_shop";
    public const string popup_shop_watch    = "popup_shop_watch";
    public const string popup_shop_purchase = "popup_shop_purchase";

    public const string screen_result        = "screen_result";
    public const string lucky_spin           = "lucky_spin";
    public const string count                = "count";
    public const string button_click         = "button_click";
    public const string button_name          = "button_name";
    public const string category             = "category";
    public const string level                = "level";
    public const string song_endless_x_start = "song_endless_{0}_start";
    public const string song_endless_x_fail  = "song_endless_{0}_fail";
    public const string theme                = "theme";

    //ACM Requirement
    public const string get_mp3_start                = "get_mp3_start";
    public const string get_mp3_fail                 = "get_mp3_fail";
    public const string get_mp3_success              = "get_mp3_success";
    public const string get_midi_start               = "get_midi_start";
    public const string get_midi_fail                = "get_midi_fail";
    public const string get_midi_success             = "get_midi_success";
    public const string download_content_mp3_start   = "download_content_mp3_start";
    public const string download_content_mp3_finish  = "download_content_mp3_finish";
    public const string download_content_midi_start  = "download_content_midi_start";
    public const string download_content_midi_finish = "download_content_midi_finish";
    public const string reason                       = "reason";
    public const string response_time                = "response_time";
    public const string unlock_type                  = "unlock_type";
    public const string preview                      = "preview";

    //Subscription
    public const string showCount    = "showCount";
    public const string viewTime     = "viewTime";
    public const string chargeCount  = "chargeCount";
    public const string product_id   = "product_id";
    public const string time         = "time";
    public const string value        = "value_name";
    public const string connection   = "connection"; //old name => status
    public const string trial_status = "trial_status";

    //Song mode
    public const string song_mode_show = "song_mode_show";

    // Tutorial update
    public const string FN_Game_Start         = "FN_Game_Start";
    public const string FN_Loading_Start      = "FN_Loading_Start";
    public const string FN_RemoteConfigLoaded = "FN_RemoteConfigLoaded";
    public const string FN_Loading_End        = "FN_Loading_End";

    public const string fn_remoteconfig_loaded_fail = "FN_RemoteConfigLoadedFail";
    public const string fn_song_end_tutorial        = "FN_Song_End_Tutorial";
    public const string fn_song_result_tutorial     = "FN_Song_Result_Tutorial";
    public const string fn_result_home              = "FN_Result_Home";
    public const string fn_result_replay            = "FN_Result_Replay";
    public const string fn_result_nextsong          = "FN_Result_NextSong";
    public const string fn_tutorial_show_continue   = "FN_Tutorial_Show_Continue";
    public const string fn_continue_click           = "FN_Continue_Click";
    public const string fn_continue_show            = "FN_Continue_Show";
    public const string fn_tutorial_start           = "FN_Tutorial_Start";
    public const string fn_song_start_tutorial      = "FN_Song_Start_Tutorial";
    public const string fn_tooltip_display          = "FN_tooltip_display";

    public const string fn_subscription_intro_01            = "FN_Subscription_Intro_01";
    public const string fn_subscription_intro_02            = "FN_Subscription_Intro_02";
    public const string fn_subscription_intro_03            = "FN_Subscription_Intro_03";
    public const string FN_Subscription_Intro_Offer_Show    = "FN_Subscription_Intro_Offer_Show";
    public const string FN_Subscription_Intro_Offer_Trial   = "FN_Subscription_Intro_Offer_Trial";
    public const string FN_Subscription_Intro_Offer_Close   = "FN_Subscription_Intro_Offer_Close";
    public const string FN_Subscription_Intro_Offer_Explore = "FN_Subscription_Intro_Offer_Explore";

    public const string FN_Demographic_Genre_Question_Show    = "FN_Demographic_Genre_Question_Show";
    public const string FN_Demographic_Genre_Question_Finish  = "FN_Demographic_Genre_Question_Finish";
    public const string FN_Demographic_Gender_Question_Show   = "FN_Demographic_Gender_Question_Show";
    public const string FN_Demographic_Gender_Question_Finish = "FN_Demographic_Gender_Question_Finish";
    public const string FN_Demographic_Age_Question_Show      = "FN_Demographic_Age_Question_Show";
    public const string FN_Demographic_Age_Question_Finish    = "FN_Demographic_Age_Question_Finish";

    public const string fn_song_s = "FN_Song_{0}s";

    public const string FN_Start_Onboarding    = "FN_Start_Onboarding";
    public const string fn_tutorial_play_click = "fn_tutorial_play_click";

    public const string tutorial_song_loaded = "tutorial_song_loaded";
    public const string tutorial_song_click  = "tutorial_song_click";
    public const string user_return          = "user_return";
    public const string day                  = "day";

    public const string FN_Tutorial_Song_Selection_Show = "FN_Tutorial_Song_Selection_Show";
    public const string fn_Sensitive_Config_Screen      = "FN_Sensitive_Config_Screen";
    public const string loading_duration                = "loading_duration";
    public const string loading_time                    = "loading_time";

    public const string genre_answer  = "genre_answer";
    public const string gender_answer = "gender_answer";
    public const string age_answer    = "age_answer";

    public const string loading_duration_songap     = "loading_duration_songap";
    public const string loading_duration_song_start = "loading_duration_song_start";

    //NewUI
    public const string artist      = "artist";
    public const string album       = "album";
    public const string genre       = "genre";
    public const string song_trial  = "song_trial";
    public const string trial_count = "trial_count";

    //Event for subs
    public const string artist_choice       = "artist_choice";
    public const string genre_choice        = "genre_choice";
    public const string sub_offer_close     = "sub_offer_close";
    public const string sub_offer_subscribe = "sub_offer_subscribe";

    public const string song_acm_id    = "song_acm_id";
    public const string song_order     = "song_order";
    public const string song_play_time = "song_play_time";
    public const string song_progress  = "song_progress";
    public const string song_score     = "song_score";
    public const string reward         = "reward";

    //Survey	
    public const string popup_survey_name  = "popup_survey_name";
    public const string popup_survey_start = "popup_survey_start";
    public const string question_          = "question_";
    public const string popup_survey_close = "popup_survey_close";
    public const string popup_survey_send  = "popup_survey_send";

    public const string survey_reward      = "survey_reward";
    public const string survey_id          = "survey_id";
    public const string survey_show_number = "survey_show_number";
    public const string session_day        = "session_day";
    public const string total_question     = "total_question";
    public const string answer             = "answer";

    //Song of the day
    public const string today_song_play        = "today_song_play";
    public const string today_song_popup_show  = "today_song_popup_show";
    public const string today_song_popup_claim = "today_song_popup_claim";
    public const string today_song_popup_close = "today_song_popup_close";

    //Song cards
    public const string songcard_click  = "songcard_click";
    public const string rank            = "rank";
    public const string favorite_add    = "favorite_add";
    public const string favorite_remove = "favorite_remove";
    public const string default_action  = "default";

    //Subscription Onboarding
    public const string package          = "package";
    public const string first_time_enter = "first_time_enter";
    public const string page_benefit     = "page_benefit";
    public const string open_method      = "open_method";
    public const string benefit          = "benefit";
    public const string tip_step         = "tip_step";
    public const string tip_number       = "tip_number";

    //Onboarding flow improvement
    public const string onboard_genre                      = "onboard_genre";
    public const string onboard_genre_click                = "onboard_genre_click";
    public const string onboard_song                       = "onboard_song";
    public const string FN_Tutorial_Song_Selection_Preview = "FN_Tutorial_Song_Selection_Preview";
    public const string FN_Tutorial_Song_Selection_Finish  = "FN_Tutorial_Song_Selection_Finish";
    public const string onboard_button_back                = "onboard_button_back";

    // ShopHome
    public const string shop_ball_home_impression = "shop_ball_home_impression";
    public const string user_avatar               = "user_avatar";
    public const string user_diamond              = "user_diamond";
    public const string shop_ball_back_button     = "back_button";
    public const string shop_ball_item_click      = "shop_ball_item_click";
    public const string shop_ball_entry_click     = "shop_ball_click";
    public const string shop_ball_category_click  = "shop_category_click";
    public const string shop_ball_item_buy        = "shop_ball_item_buy";
    public const string shop_ball_item_equipped   = "equipped_item";

    //TH-1176 : Economy tracking events
    public const string iapshop_open        = "iapshop_open";
    public const string iapshop_close       = "iapshop_close";
    public const string iapshop_click       = "iapshop_click";
    public const string iapshop_cancel      = "iapshop_cancel";
    public const string iapshop_success     = "iapshop_success";
    public const string currency_spend      = "currency_spend";
    public const string currency_earn       = "currency_earn";
    public const string achievement_collect = "achievement_collect";
    public const string mission_collect     = "mission_collect";
    public const string onboard_progression = "onboard_progression";
    public const string onboard_gem         = "onboard_gem";
    public const string onboard_unlock      = "onboard_unlock";

    //TH-1421: PopupRate V2
    public const string user_review        = "user_review";
    public const string rate               = "rate";
    public const string comment            = "comment";
    public const string button_click_close = "button_click_close";

    public static string rating_click_rate         = "rating_click_rate";
    public static string rating_click_rate_dontask = "rating_click_rate_dontask";
    public static string rating_click_rate_later   = "rating_click_rate_later";
    public static string rating_click_yes          = "rating_click_yes";
    public static string rating_click_no           = "rating_click_no";
    public static string rating_click_star         = "rating_click_star";

    public static string feedback_rating_impression = "feedback_rating_impression";
    public static string popup_rating_impression    = "popup_rating_impression";

    //TH-1586 [DEV] Kiểm tra lại event rewarded popup economy
    public const string popup_rw_gem_show  = "popup_rw_gem_show";
    public const string popup_rw_gem_claim = "popup_rw_gem_claim";
    public const string popup_rw_gem_close = "popup_rw_gem_close";

    public const string popup_iap_gem_show  = "popup_iap_gem_show";
    public const string popup_iap_gem_close = "popup_iap_gem_close";

    public const string double_reward = "double_reward";

    public const string free_gem_claim   = "free_gem_claim";
    public const string free_gem_success = "free_gem_success";

    //CMP
    public const string cmp_popup_show   = "cmp_popup_show";
    public const string cmp_popup_finish = "cmp_popup_finish";

    //select theme
    public const string themeui_ap_button_click    = "themeui_ap_button_click";
    public const string themeui_thumbnail_click    = "themeui_thumbnail_click";
    public const string themeui_preview_impression = "themeui_preview_impression";
    public const string themeui_item_load          = "themeui_item_load";
    public const string feature_session_time       = "feature_session_time";
    public const string see_all_click              = "see_all_click";
    public const string see_all_impression         = "see_all_impression";
    public const string see_all_out                = "see_all_out";
    public const string themeui_equipped           = "themeui_equipped";

    //TH-3002: load economy config
    public const string load_economy_config = "load_economy_config";

    //old user gem popup
    public const string olduser_gem_popup       = "olduser_gem_popup";
    public const string olduser_gem_popup_click = "olduser_gem_popup_click";

    public const string native_ads_impression = "native_ads_impression";

    public const string videoads_show_combo3 = "videoads_show_combo3";

    //Mission
    public const string mission_click      = "mission_click";
    public const string mission_impression = "mission_impression";
    public const string mission_tooltip    = "mission_tooltip";
    public const string mission_progress   = "mission_progress";
    public const string mission_complete   = "mission_complete";
    public const string mission_claim      = "mission_claim";
}

public class TRACK_PARAM {
    public const string status = "status"; //Global param

    //TH-1176 : Economy tracking events
    public const string accumulated_count = "accumulated_count";
    public const string location          = "location";
    public const string placement         = "placement";
    public const string amount            = "amount";

    public const string amount_earn       = "amount_earn";
    public const string accumulated_earn  = "accumulated_earn";
    public const string amount_spend      = "amount_spend";
    public const string accumulated_spend = "accumulated_spend";

    public const string price      = "price";
    public const string currency   = "currency";
    public const string package_id = "package_id";

    public const string currency_amount_before = "currency_amount_before";
    public const string currency_amount_after  = "currency_amount_after";

    public const string current_level = "current_level";

    public const string achievement_name = "achievement_name";

    public const string mission_id   = "mission_id";
    public const string mission_type = "mission_type";
    public const string mission_name = "mission_name";

    public const string gameplay_count        = "gameplay_count";
    public const string is_new_element        = "is_new_element";
    public const string obstacle_type         = "obstacle_type";
    public const string played_element        = "played_element";
    public const string previous_star         = "previous_star";
    public const string total_try_fetch       = "total_try_fetch";
    public const string stars_required        = "stars_required";
    public const string accumulated_star_earn = "accumulated_star_earn";

    public static string screen              = "screen";
    public static string popup_no            = "popup_no";
    public static string action              = "action";
    public static string claim_type          = "claim_type";
    public static string free_gem_no         = "free_gem_no";
    public static string tutorial_song_name  = "tutorial_song_name";
    public static string tutorial_song_genre = "tutorial_song_genre";
    public static string tutorial_song_order = "tutorial_song_order";
    public static string currency_inventory  = "currency_inventory";
    public static string song_revive_type    = "song_revive_type";
    public static string songpack_id         = "songpack_id";
    public static string gems_unlock_song    = "gems_unlock_song";
    public static string entry_point         = "entry_point";
    public static string song_note           = "song_note";

    public const string source        = "source";
    public const string currency_earn = "currency_earn";

    //TH-1828: popup playtimeReward
    public const string user_gem_inventory              = "user_gem_inventory";
    public const string gauge_percentile_enter          = "gauge_percentile_enter";
    public const string gem_stack_enter                 = "gem_stack_enter";
    public const string gauge_percentile_exit           = "gauge_percentile_exit";
    public const string gem_stack_exit                  = "gem_stack_exit";
    public const string onboarding_step                 = "onboarding_step";
    public const string reward_multiply                 = "reward_multiply";
    public const string gauge_percentile_multiply_click = "gauge_percentile_multiply_click";
    public const string gem_stack_multiply_click        = "gem_stack_multiply_click";
    public const string gauge_percentile_collect        = "gauge_percentile_collect";
    public const string gem_stack_collect               = "gem_stack_collect";

    public const string gem_amount             = "gem_amount";
    public const string user_gem_before        = "user_gem_before";
    public const string user_gem_after         = "user_gem_after";
    public const string iap_pack_id            = "pack_id";
    public const string accumulated_view_count = "accumulated_view_count";
    public const string button_name            = "button_name";
    public const string time_elapsed           = "time_elapsed";

    public const string type     = "type";
    public const string path     = "path";
    public const string deeplink = "deeplink";

    //TH-3002: load economy config
    public const string eco_load_status      = "status";
    public const string eco_load_fail_reason = "fail_reason";
    public const string eco_version          = "economy_version";

    public const string gem_rewarded = "gem_rewarded";

    public const string transaction_id = "transaction_id";

    public const string ad_source       = "ad_source";
    public const string value           = "value";
    public const string total_achieve   = "total_achieve";
    public const string gameplay_earn   = "gameplay_earn";
    public const string current_balance = "current_balance";
    public const string complete        = "complete";

    public const  string booster_name        = "booster_name";
    public static string streak_name         = "streak_name";
    public static string streak_level        = "streak_level";
    public static string rationale           = "rationale";
    public static string streak_level_before = "streak_level_before";
    public static string streak_level_after  = "streak_level_after";
    public static string total_unique_star   = "total_unique_star";

    public const string product_id = "product_id";
}

public class TRACK_LOCATION {
    //TH-1176 : Economy tracking events
    public const string song_result_0star   = "song_result_0star ";
    public const string song_result_1star   = "song_result_1star";
    public const string song_result_2star   = "song_result_2star";
    public const string song_result_3star   = "song_result_3star";
    public const string song_result_endless = "song_result_endless";
    public const string ad_shop             = "ad_shop";

    public const string tutorial_result = "tutorial_result";
}

public enum SONG_PLAY_TYPE {
    none,
    home,
    free_video_character,
    free_video_song,
    free_video_ball,
    free_video_theme,
    replay,
    next,
    recommended_result, //game_result_song
    game_result_ball,
    home_button_play,
    tutorial,
    ball_shop,
    endless,
    AdFsReward,

    //UI Revamp
    sevendaymission_reward,

    Survey, //Survey popup
    song_of_the_day, // song_of_the_day
    discover_song_of_the_day,
    live_event, // TH-1545
    mysterybox_popup_auto, //TH-1893
    mysterybox_popup_manual, //TH-1893
    ball_equip, // play when "equip ball & play"
    search,
    free_challenge, //TH-3057: challenge for old user
    replay_challenge, //TH-3057: challenge for old user
    discover,
    discover_artist,
    discover_album,
    discover_artist_search,
    discover_genre,
    discover_recent_songs,
    discover_popular,
    discover_your_challenge,
    discover_your_challenge_golden_box,
    discover_song_search,
    discover_hardcore,
    discover_favorite,
    discover_newupdate,
    discover_recommend,
    song_pack_reward,
    beat_sahur,
}

public enum LOCATION_NAME {
    POPUP_CLOSE,
    home, //TH-124 HOME
    setting, //SETTINGS
    ACHIEVEMENT,
    shop, //
    leaderboard, //
    FREE_GIFT,
    BALL_LIST,
    BALL_LIST_INGAME,
    PREPARE_GAME,
    result, //RESULT_GAME
    revive, //CONTINUE_GAME
    TIPS,
    DAILY_GIFT,
    LANGUAGE_SELECTION,
    SELECT_LOCALSONG,
    RATE_US,
    ACHIEVEMENT_POPUP,
    MISSION_NOTICE,
    CREDIT,
    loading, //LOADING_GAME
    DOWNLOAD_SONG,
    gameplay, //PLAY_GAME
    SONG_DETAILS,
    NEED_DIAMONDS,
    LUCKY_SPIN,
    EXIT_POPUP,
    ADS_PURCHASE,
    GAME_SETTINGS,
    INTERSTITIAL_AD,
    REWARD_AD,
    ADS,
    PROFILE,
    discover, //UI2 SEARCH
    PREMIUM,
    sub_offer, //SUBSCRIPTION
    sub_catalog, //CATALOGUE
    PAUSE_GAME,
    PROMOTION_VIDEO,
    VIP_TAB,
    MORE_FILTER,
    others,
    sevenday_mission,
    sevenday_mission_reward,

    main_menu, //TH-1176
    unlock_song, //TH-1176
    unlock_ball, //TH-1176
    unlock_char, //TH-1176
    Feedback, //TH-1421
    liveEvent, // TH-1545
    popup_iap_gem,
    popup_rw_diamond,
    knob_of_gem,
    popup_rw_gem,
    free_gem_iap_shop,
    double_reward,
    AdFs_Reward,
    Subscription_Intro,
    Subscription_SeeAll,
    mysterybox_popup_auto,
    mysterybox_popup_manual,
    select_theme,
    unlock_theme,
    expanded_theme,
    tutorial,
    survey,
    approval_process,
    unlock_song_of_day,
    multi_difficult,
    devInfo,
    CommunicationSystem,
    vip_mission,
    upload_song,
    user_profile,
    NotificationBox,
    MidiMultipleVersion,
    tryskin_reward,
    free_challenge, //TH-3057: challenge for old user
    discover_your_challenge_mystery_box,
    songpack_reward,
    starsjourney_reward,
    mission,
    pending_purchase,
    no_internet,
    async_server_data,
    permission_needed,
    tutorial_reward,
    hardcore_challenge,
    select_booster,
    exchange_booster,
    after_3_stars,
    star_journey,
}

public enum AD_STATE {
    request, //When an FS ad is requested from ads network
    request_failed, //When an ad request has been sent but received no response from ads network
    request_success, //When an ad request has been sent and received response from ads network
    show_ready, //When an ad is called to show in app
    show_notready, //When an ad is called to show in app but no ad available
    show, //When an ad is called to show in app and it show successfully
    show_failed, //When an ad is called to show in app but cannot show due to error
    show_success,
    finish, //When user finish watch an ad
    click, //When user touching on CTA button of an ad
    special,
    opened
}

public enum SONG_STATUS {
    song_start,
    song_fail,
    song_start_first,
    song_result_first,
    song_replay,
    song_end,
    song_revive,
    song_result,
    song_preview,
    song_preview_click,
    song_impression,
    song_click,
    song_ap,
    song_loaded,
    song_suggest_click,
    song_suggest_show,
    song_revive_impression, //continue_impression
    song_unlock, //Trigger when user successfully go to the gameplay scene of a new unique song that they haven't played before
    song_vip_click,
    me_start,
    me_result,
    song_force_close
}

public enum VipStatus {
    none,
    trial,
    purchase,
}

public enum LoadStatus {
    fail,
    success
}