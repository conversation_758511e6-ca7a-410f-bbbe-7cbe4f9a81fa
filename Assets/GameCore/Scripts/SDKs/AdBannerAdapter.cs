// using com.unity3d.mediation;
//
// public class AdBannerAdapter {
//     public bool              loaded;
//     public double?           revenue;
//     public LevelPlayBannerAd bannerAd;
//
//     public void ShowAd() {
//         if (bannerAd != null) {
//             bannerAd.ShowAd();
//         }
//     }
//
//     public void HideAd() {
//         if (bannerAd != null) {
//             bannerAd.HideAd();
//         }
//     }
//
//     public void DestroyAd() {
//         if (bannerAd != null) {
//             bannerAd.DestroyAd();
//             bannerAd = null; 
//         }
//     }
// }