using System;
using System.Collections;
using Amanotes.Push;
using AmaSQLite;
using UnityEngine;

public class NotificationInit : FastSingleton<NotificationInit> {
    const        string SetupNotificationKey         = "SetupNotification";
    public const int    id_SevenDayMission_noti_day1 = 22053015;
    public const int    id_SevenDayMission_noti_day2 = 22053016;
    public const int    id_SevenDayMission_noti_day4 = 22053017;
    public const int    id_SevenDayMission_noti_day7 = 22053018;

    private bool _initialized;
    private bool _notificationInitializationFailed;

    public void InitNotification() {
        if (!_initialized) {
            _initialized = true;
            FirebaseSDK.instance.InitializeFirebaseMessaging();
            Util.WaitRemoteConfigDone(ScheduleNotification, true);
        }
    }

    private void ScheduleNotification() {
        StartCoroutine(IEScheduleNotification());
    }

    private IEnumerator IEScheduleNotification() {
        if (RemoteConfigBase.instance.CommunicationSystem_IsOn) {
            SQLiteService.SetDatabasePathNative(); //fix bug => Failed to open database 'PI_DB'
        }

        yield return Inwave.Utils.WaitUntil(CheckLocalizationLoaded);

        // Add delay and retry mechanism for notification initialization
        yield return StartCoroutine(InitializeNotificationSystemSafely());

        // Only proceed if notification system was successfully initialized
        if (_notificationInitializationFailed) {
            Debug.LogWarning(
                "[NotificationInit] Notification system failed to initialize, skipping notification setup");
            yield break;
        }

        FirebaseMessageService.Instance.Init();
        yield return null;

        string title = Application.productName;
        string push1 = LocalizationManager.instance.GetLocalizedValue("PUSH_1");
        string push2 = LocalizationManager.instance.GetLocalizedValue("PUSH_2");
        string push3 = LocalizationManager.instance.GetLocalizedValue("PUSH_3");

        if (PlayerPrefs.GetInt(SetupNotificationKey, 0) == 0) { //first open
            // Only clear and schedule if notification system is ready
            if (NotificationForAndroid.IsNotificationSystemReady()) {
                LocalPushManager.Instance.UnscheduleAllNotifications();
                yield return null;

                ScheduleNotification(DateTime.Now.AddDays(1), title, push1, 1);
                yield return null;

                TimeSpan repeatTime = new TimeSpan(9, 0, 0, 0);
                ScheduleRepeatNotification(DateTime.Now.AddDays(3), title, push2, 2, repeatTime);
                yield return null;

                ScheduleRepeatNotification(DateTime.Now.AddDays(6), title, push3, 3, repeatTime);
                yield return null;
                
                ScheduleRepeatNotification(DateTime.Now.AddDays(9), title, push1, 4, repeatTime);

                PlayerPrefs.SetInt(SetupNotificationKey, 1);
            } else {
                Debug.LogWarning("[NotificationInit] Notification system not ready, deferring notification setup");
            }
        }

        yield return null;
    }

    private bool CheckLocalizationLoaded() {
        // TH-4963
        return LocalizationManager.instance != null && LocalizationManager.instance.IsLoaded();
    }
    
    /// <summary>
    /// Safely initialize notification system with retries and delay
    /// </summary>
    private IEnumerator InitializeNotificationSystemSafely() {
        const int maxRetries = 3;
        const float delayBetweenRetries = 2.0f;

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            Debug.Log(
                $"[NotificationInit] Attempting notification initialization (attempt {attempt + 1}/{maxRetries})");

            // Add frame delay before each attempt
            yield return new WaitForSeconds(delayBetweenRetries * attempt);

            // Initialize LocalPushManager which will trigger notification system init
            LocalPushManager.Instance.Init();

            // Wait a few frames to let initialization complete
            yield return new WaitForSeconds(1.0f);

            // Check if initialization was successful
            if (NotificationForAndroid.IsNotificationSystemReady()) {
                Debug.Log("[NotificationInit] Notification system successfully initialized");
                _notificationInitializationFailed = false;
                yield break;
            } else {
                Debug.LogWarning($"[NotificationInit] Notification initialization attempt {attempt + 1} failed");
            }
        }

        // All attempts failed
        Debug.LogError("[NotificationInit] Failed to initialize notification system after all attempts");
        _notificationInitializationFailed = true;
    }

    public static void ScheduleNotification(DateTime fireTime, string title, string body, int id) {
        // Add safety check before scheduling
        if (!NotificationForAndroid.IsNotificationSystemReady()) {
            Debug.LogWarning($"[NotificationInit] Cannot schedule notification {id} - system not ready");
            return;
        }

        try {
            AmaNotificationData notificationData = new AmaNotificationData {
                notification_id = id.ToString(),
                id = id,
                title = title,
                content = body,
                fireTime = fireTime
            };
            LocalPushManager.Instance.Schedule(notificationData);
        } catch (Exception ex) {
            Debug.LogError($"[NotificationInit] Failed to schedule notification {id}: {ex.Message}");
        }
    }

    private void ScheduleRepeatNotification(DateTime fireTime, string title, string body, int id, TimeSpan repeatTime) {
        // Add safety check before scheduling
        if (!NotificationForAndroid.IsNotificationSystemReady()) {
            Debug.LogWarning($"[NotificationInit] Cannot schedule repeat notification {id} - system not ready");
            return;
        }

        try {
            AmaNotificationData notificationData = new AmaNotificationData {
                notification_id = id.ToString(),
                id = id,
                title = title,
                content = body,
                fireTime = fireTime,
                repeatInterval = repeatTime,
                type = AmaNotificationData.Type.recurring.ToString(),
            };
            LocalPushManager.Instance.Schedule(notificationData);
        } catch (Exception ex) {
            Debug.LogError($"[NotificationInit] Failed to schedule repeat notification {id}: {ex.Message}");
        }
    }
}