using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Amanotes.AmaPassport;
using DG.Tweening;
using Inwave;
using TilesHop.Cores.IAASegmentation;
using UnityEngine;

public class RevenueCatPurchases : Purchases.UpdatedCustomerInfoListener {
    enum RcProductType {
        subs,
        inapp
    }

    private const string PriceStringPrefix = "PRICE_STRING";
    private const string PriceValuePrefix  = "PRICE_VALUE";
    private const string TitleStringPrefix = "TITLE_STRING";

    private Dictionary<IAPDefinitionId, KeyValuePair<RcProductType, string>> _productIDs =
        new Dictionary<IAPDefinitionId, KeyValuePair<RcProductType, string>>();

    private          bool      _isWaitingPurchase = false;
    [SerializeField] Purchases _purchases;

    private Dictionary<IAPDefinitionId, Purchases.StoreProduct> _avaliablePackages =
        new Dictionary<IAPDefinitionId, Purchases.StoreProduct>();

    private List<IAPDefinitionId> _loadingDiscountPaymentIds = new List<IAPDefinitionId>();

    public static RevenueCatPurchases instance;

    [HideInInspector] public bool isLoadPurchaserInfo = false;

    private IAPDefinitionId      _idSubsPurchased = IAPDefinitionId.none;
    private bool                 _isLoadingProducts;
    private Action<bool, string> _callbackPurchase;
    public int availableProductCount => _avaliablePackages?.Count ?? 0;

    public static event Action<IAPDefinitionId> OnPurchased;

    private void Awake() {
        instance = this;
        if (Application.isEditor) {
            isLoadPurchaserInfo = true;
        }
    }

    public void InitializePurchasing() {
        StartCoroutine(IEInitializePurchasing());
    }

    private IEnumerator IEInitializePurchasing() {
        // Đợi thêm 1 frame để đảm bảo Activity ready
        yield return null;

        //_purchases.SetDebugLogsEnabled(true);
        StartCoroutine(SetAttributes());
        SyncPurchases();

        yield return Inwave.Utils.WaitUntil(() => !_isLoadingProducts);

        GetAvailableProducts(RcProductType.subs);

        yield return Inwave.Utils.WaitUntil(() => !_isLoadingProducts);

        GetAvailableProducts(RcProductType.inapp);

        yield return Inwave.Utils.WaitUntil(() => !_isLoadingProducts);

        CheckExpiredSubscription();

        // Wait one frame to allow other UI components to run their Awake/Start methods
        yield return null;

        IapBase.InitCompleted();
    }

    private IEnumerator WaitLoadingProducts() {
        while (!_isLoadingProducts) {
            yield return null;
        }
    }

    private void SyncPurchases() {
        if (PlayerPrefs.HasKey(PlayerPrefsKey.rc_SyncPurchases)) {
            return;
        }

        PlayerPrefs.SetInt(PlayerPrefsKey.rc_SyncPurchases, 1);
        //Debug.Log("[IAP] SYNC PURCHASES");

        if (_purchases != null) {
            _purchases.SyncPurchases(); // _rc is Purchases, and call this function ONE TIME ONLY
        }
    }

    private void CheckExpiredSubscription() {
        //Debug.Log("[IAP] Check status at first");
        if (_purchases != null) {
            _purchases.GetCustomerInfo((customerInfo, error) => {
                if (error != null) {
                    Debug.Log($"[IAP] Check status at first Error! {error}");
                } else {
                    Debug.Log($"[IAP] Check status at first Done! " + customerInfo.Entitlements.All.Count);
                    bool localVip = SubscriptionController.IsSubscription();
                    if (localVip) {
                        if (customerInfo != null && customerInfo.Entitlements != null &&
                            !customerInfo.Entitlements.Active.ContainsKey("VIP")) {
                            //user has not access to VIP entitlement
                            SubscriptionController.SetExpired(); // hết hạn
                        }
                    } else {
                        if (customerInfo != null && customerInfo.Entitlements != null) {
                            /*if (customerInfo.Entitlements.Active.Count != 0)*/
                            {
                                //Debug.Log("[IAP] : " + customerInfo.Entitlements.ToString());
                                if (customerInfo.Entitlements.All.ContainsKey("VIP")) {
                                    var pack = customerInfo.Entitlements.All["VIP"];
                                    bool isFreeTrial = !pack.WillRenew;
                                    string definitionId = pack.ProductIdentifier;
                                    //khôi phục lại IAP nếu ng chơi lỡ xóa game
                                    if (pack.IsActive) {
                                        Debug.Log("[IAP] Restore subscrip " + definitionId);
                                        SubscriptionController.StartSubscription_RevenueCat(isFreeTrial, definitionId);
                                    } else if (RemoteConfigBase.instance.IaaSegmentation_IsEnablePopup) {
                                        UpdateIaaSegmentation(isFreeTrial);
                                    }
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    private static void UpdateIaaSegmentation(bool isFreeTrial) {
        // detect when the user cancel the trial, only if they haven't purchased IAP yet
        if (!isFreeTrial && IaaSegmentation.data.isHighValueFromTrial && !IaaSegmentation.data.isHighValueFromIAP) {
            if (UserProperties.GetPropertyInt(UserProperties.daydiff) == 0) {
                IaaSegmentation.data.isHighValueFromTrial = false;
                IaaSegmentation.SetUserValue(UserValue.none);
            } else {
                IaaSegmentation.TrySegmentation();
            }
        }
    }

    private void GetAvailableProducts(RcProductType type) {
        _isLoadingProducts = true;

        //Get product array ids
        List<string> productIds = new List<string>();
        foreach (var item in _productIDs.Values) {
            if (item.Key == type) {
                productIds.Add(item.Value);
            }
        }
        //Debug.Log("IAPDebug Package " + string.Join(",",productIds));

        //Get RevenueCat Product list
        _purchases.GetProducts(productIds.ToArray(), (products, error) => {
            _isLoadingProducts = false;
            if (error != null) {
                Debug.LogError("IAPDebug" + error.Message);
            } else {
                //Debug.LogError("IAPDebug products" + products.Count);
                bool isSubscription = false;
                foreach (Purchases.StoreProduct product in products) {
                    //Debug.Log("IAPDebug Package " + type + ": " + product);
                    if (product != null) {
                        IAPDefinitionId definitionId = GetDefinitionId(product.Identifier);
                        _avaliablePackages.TryAdd(definitionId, product);
                        if (_productIDs.TryGetValue(definitionId, out var productType) &&
                            productType.Key == RcProductType.subs) {
                            isSubscription = true;
                        }
                    }
                }

                if (isSubscription) {
                    SubPackageManager.UpdatePrices();
                    if (Application.platform == RuntimePlatform.IPhonePlayer &&
                        RemoteConfig.instance.Subscription_DiscountOffer) {
                        UpdatePromotionalOffer();
                    }
                }
            }
        }, type.ToString());
    }

    public void GetAdditionalProducts() {
        _isLoadingProducts = true;

        //Get product array ids
        List<string> productIds = new List<string>();
        foreach (var item in _productIDs.Values) {
            if (item.Key == RcProductType.inapp) {
                productIds.Add(item.Value);
            }
        }

        //Get RevenueCat Product list
        _purchases.GetProducts(productIds.ToArray(), (products, error) => {
            _isLoadingProducts = false;
            if (error != null) {
                Debug.LogError("IAPDebug" + error.Message);
            } else {
                foreach (Purchases.StoreProduct product in products) {
                    //Debug.Log("IAPDebug Package " + type + ": " + product);
                    if (product != null) {
                        IAPDefinitionId definitionId = GetDefinitionId(product.Identifier);
                        _avaliablePackages.TryAdd(definitionId, product);
                    }
                }
            }
        }, RcProductType.inapp.ToString());
    }

    public void GetCustomerInfo(Purchases.CustomerInfoFunc onDone) {
        _purchases.GetCustomerInfo(onDone);
    }

    public void AddProduct(IAPDefinitionId iapId, ProductType type, string playStoreName, string appStoreName) {
        RcProductType rcProductType = type == ProductType.Subscription ? RcProductType.subs : RcProductType.inapp;
        if (_productIDs.ContainsKey(iapId)) {
            return;
        }

        if (Utils.IsAndroid()) {
            _productIDs.Add(iapId, new KeyValuePair<RcProductType, string>(rcProductType, playStoreName));
        } else {
            _productIDs.Add(iapId, new KeyValuePair<RcProductType, string>(rcProductType, appStoreName));
        }
    }
    public void AddOrReplaceProduct(IAPDefinitionId iapId, ProductType type, string playStoreName, string appStoreName) {
        RcProductType rcProductType = type == ProductType.Subscription ? RcProductType.subs : RcProductType.inapp;
        if (_productIDs.ContainsKey(iapId)) {
            _productIDs[iapId] = new KeyValuePair<RcProductType, string>(rcProductType,
                Utils.IsAndroid() ? playStoreName : appStoreName);
        } else {
            _productIDs.Add(iapId,
                new KeyValuePair<RcProductType, string>(rcProductType,
                    Utils.IsAndroid() ? playStoreName : appStoreName));
        }
    }

    [HideInInspector] public bool isprocessingPurchase;

    public void BuyProduct(IAPDefinitionId definitionId, bool isPromotionalOffer,
                           Action<bool, string> onPurchaseCallBack = null) {
        if (!_productIDs.ContainsKey(definitionId)) {
            CustomException.Fire("[BuyProduct] ", "cannot find product: " + definitionId.ToString());
            onPurchaseCallBack?.Invoke(false, null);
            return;
        }

        _callbackPurchase = onPurchaseCallBack;
        if (Configuration.instance.isDebug || Application.isEditor) {
            isprocessingPurchase = true;
            ShowLoading();
            Logger.Log("[BuyProduct] Debug mode, CompletedPurchase after 3s!");
            DOVirtual.DelayedCall(3, () => { ProcessPurchase(definitionId, _productIDs[definitionId].Value, null); });
        } else if (_productIDs.ContainsKey(definitionId)) {
            ShowLoading();
            isprocessingPurchase = true;
            string identifier = _productIDs[definitionId].Value;
            if (_avaliablePackages.TryGetValue(definitionId, out Purchases.StoreProduct product)) {
                identifier = product.Identifier;
            } else {
                Logger.LogError($"[BuyProduct] cannot find product: {definitionId}");
            }

            if (isPromotionalOffer) {
                if (_paymentDiscounts.ContainsKey(identifier)) {
                    Purchases.PromotionalOffer paymentDiscount = _paymentDiscounts[identifier];
                    _purchases.PurchaseDiscountedProduct(identifier, paymentDiscount,
                        (productIdentifier, purchaserInfo, userCancelled, error) => {
                            if (!userCancelled) {
                                if (IsPurchaseError(identifier, error, purchaserInfo)) {
                                    ShowMessageErrorPurchase(error);
                                    _callbackPurchase?.Invoke(false, error.Message);
                                } else {
                                    ProcessPurchase(definitionId, identifier, purchaserInfo);
                                    AnalyticHelper.LogEvent(SUBSCRIPTION_EVENT.user_subscription_resubs.ToString());
                                }
                            } else {
                                _callbackPurchase?.Invoke(false, error.Message);
                            }

                            HideLoading();
                            isprocessingPurchase = false;
                        });
                } else {
                    Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("PACKAGE_IS_LOADING"));
                    HideLoading();
                    isprocessingPurchase = false;
                    _callbackPurchase?.Invoke(false, null);
                }
            } else {
                _purchases.PurchaseProduct(identifier, (productIdentifier, purchaserInfo, userCancelled, error) => {
                    if (!userCancelled) {
                        if (IsPurchaseError(identifier, error, purchaserInfo)) {
                            ShowMessageErrorPurchase(error);
                            _callbackPurchase?.Invoke(false, error.Message);
                        } else {
                            ProcessPurchase(definitionId, identifier, purchaserInfo);
                        }
                    } else {
                        _callbackPurchase?.Invoke(false, error.Message);
                    }

                    HideLoading();
                    isprocessingPurchase = false;
                }, _productIDs[definitionId].Key.ToString());
            }
        }
    }

    private void ShowMessageErrorPurchase(Purchases.Error error) {
        if (error == null) {
            return;
        }

        if (Utils.IsAndroid() && error.Code == (int) RCPurchasesErrorCode.NetworkError) {
            NoInternetPopup.Show();
        } else {
            Util.ShowMessage(error.Message);
        }
    }

    public bool Contains(string definitionId) {
        foreach (var item in _productIDs.Values) {
            if (item.Value != null && item.Value.Equals(definitionId))
                return true;
        }

        return false;
    }

    public bool Contains(IAPDefinitionId definitionId) {
        foreach (var item in _productIDs) {
            if (item.Key.Equals(definitionId))
                return true;
        }

        return false;
    }

    public void BuyProduct(IAPDefinitionId definitionId, string identifier, bool isPromotionalOffer,
                           Action<bool, string> onPurchaseCallBack = null) {
        if (!_productIDs.ContainsKey(definitionId) || !Contains(identifier)) {
            CustomException.Fire("[BuyProduct] ",
                $"cannot find product: definitionId => {definitionId} identifier => {identifier}");
            onPurchaseCallBack?.Invoke(false, null);
            return;
        }

        _callbackPurchase = onPurchaseCallBack;

        if (Configuration.instance.isDebug || Application.isEditor) {
            isprocessingPurchase = true;
            ShowLoading();
            Logger.Log("[BuyProduct] Debug mode Completed Purchase after 3s");
            DOVirtual.DelayedCall(3, () => { ProcessPurchase(definitionId, _productIDs[definitionId].Value, null); });
        } else if (Contains(identifier)) {
            ShowLoading();
            isprocessingPurchase = true;
            if (isPromotionalOffer) {
                if (_paymentDiscounts.ContainsKey(identifier)) {
                    Purchases.PromotionalOffer paymentDiscount = _paymentDiscounts[identifier];
                    _purchases.PurchaseDiscountedProduct(identifier, paymentDiscount,
                        (productIdentifier, purchaserInfo, userCancelled, error) => {
                            if (!userCancelled) {
                                if (IsPurchaseError(identifier, error, purchaserInfo)) {
                                    ShowMessageErrorPurchase(error);
                                    _callbackPurchase?.Invoke(false, error.Message);
                                } else {
                                    ProcessPurchase(definitionId, identifier, purchaserInfo);
                                    AnalyticHelper.LogEvent(SUBSCRIPTION_EVENT.user_subscription_resubs.ToString());
                                }
                            } else {
                                _callbackPurchase?.Invoke(false, error.Message);
                            }

                            HideLoading();
                            isprocessingPurchase = false;
                        });
                } else {
                    Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("PACKAGE_IS_LOADING"));
                    HideLoading();
                    isprocessingPurchase = false;
                    _callbackPurchase?.Invoke(false, null);
                }
            } else {
                _purchases.PurchaseProduct(identifier, (productIdentifier, purchaserInfo, userCancelled, error) => {
                    if (!userCancelled) {
                        if (IsPurchaseError(identifier, error, purchaserInfo)) {
                            ShowMessageErrorPurchase(error);
                            _callbackPurchase?.Invoke(false, error.Message);
                        } else {
                            ProcessPurchase(definitionId, identifier, purchaserInfo);
                        }
                    } else {
                        _callbackPurchase?.Invoke(false, error.Message);
                    }

                    HideLoading();
                    isprocessingPurchase = false;
                }, _productIDs[definitionId].Key.ToString());
            }
        }
    }

    public string GetProductID(IAPDefinitionId package) {
        Purchases.StoreProduct p = GetProduct(package);
        if (p != null) {
            return p.Identifier;
        }

        return package.ToString();
    }

    public string GetPriceString(IAPDefinitionId package) {
        Purchases.StoreProduct p = GetProduct(package);
        string prefKey = Util.BuildString(string.Empty, PriceStringPrefix, package.ToString());

        if (p != null) {
            PlayerPrefs.SetString(prefKey, p.PriceString);
            return p.PriceString;
        }

        return PlayerPrefs.GetString(prefKey, null);
    }

    public string GetPriceString(string packageName) {
        Purchases.StoreProduct p = GetProduct(packageName);
        string prefKey = Util.BuildString(string.Empty, PriceStringPrefix, packageName);

        if (p != null) {
            PlayerPrefs.SetString(prefKey, p.PriceString);
            return p.PriceString;
        }

        return PlayerPrefs.GetString(prefKey, null);
    }

    public float GetPriceValue(IAPDefinitionId package) {
        Purchases.StoreProduct p = GetProduct(package);
        string prefKey = Util.BuildString(string.Empty, PriceValuePrefix, package.ToString());
        if (p != null) {
            PlayerPrefs.SetFloat(prefKey, p.Price);
            return p.Price;
        }

        return PlayerPrefs.GetFloat(prefKey, 0);
    }

    public float GetPriceValue(string package) {
        Purchases.StoreProduct p = GetProduct(package);
        string prefKey = Util.BuildString(string.Empty, PriceValuePrefix, package);
        if (p != null) {
            PlayerPrefs.SetFloat(prefKey, p.Price);
            return p.Price;
        }

        return PlayerPrefs.GetFloat(prefKey, 0);
    }

    public string GetPriceCode(IAPDefinitionId package) {
        Purchases.StoreProduct p = GetProduct(package);
        if (p != null && p.CurrencyCode != null) {
            return p.CurrencyCode;
        }

        return string.Empty;
    }

    public string GetPriceCode(string package) {
        Purchases.StoreProduct p = GetProduct(package);
        if (p != null && p.CurrencyCode != null) {
            return p.CurrencyCode;
        }

        return string.Empty;
    }

    private string GetAllAvaliablePackages() {
        StringBuilder sb = new StringBuilder();

        foreach (KeyValuePair<IAPDefinitionId, Purchases.StoreProduct> pair in _avaliablePackages) {
            sb.Append($"{pair.Key} - {pair.Value.Identifier};");
        }

        return sb.ToString();
    }

    public (string price, float priceValue, string priceCode) GetInforProduct(IAPDefinitionId package) {
        Purchases.StoreProduct p = GetProduct(package);
        string prefKeyString = Util.BuildString(string.Empty, PriceStringPrefix, package.ToString());
        string prefKeyValue = Util.BuildString(string.Empty, PriceValuePrefix, package.ToString());

        if (p != null) {
            PlayerPrefs.SetString(prefKeyString, p.PriceString);
            PlayerPrefs.SetFloat(prefKeyValue, p.Price);
            return (p.PriceString, p.Price, p.CurrencyCode);
        }

        return (PlayerPrefs.GetString(prefKeyString, string.Empty), PlayerPrefs.GetFloat(prefKeyValue, 0),
            string.Empty);
    }

    public (string price, float priceValue, string priceCode) GetInforProduct(string productId) {
        Purchases.StoreProduct p = GetProduct(productId);
        string prefKeyString = Util.BuildString(string.Empty, PriceStringPrefix, productId);
        string prefKeyValue = Util.BuildString(string.Empty, PriceValuePrefix, productId);

        if (p != null) {
            PlayerPrefs.SetString(prefKeyString, p.PriceString);
            PlayerPrefs.SetFloat(prefKeyValue, p.Price);
            return (p.PriceString, p.Price, p.CurrencyCode);
        }

        return (PlayerPrefs.GetString(prefKeyString, string.Empty), PlayerPrefs.GetFloat(prefKeyValue, 0),
            string.Empty);
    }

    public string GetProductName(IAPDefinitionId package) {
        Purchases.StoreProduct p = GetProduct(package);
        string prefKey = Util.BuildString(string.Empty, TitleStringPrefix, package.ToString());
        if (p != null) {
            PlayerPrefs.SetString(prefKey, p.Title);
            return p.Title;
        }

        return PlayerPrefs.GetString(prefKey, null);
    }

    public Purchases.StoreProduct GetProduct(IAPDefinitionId package) {
        if (_avaliablePackages.ContainsKey(package)) {
            return _avaliablePackages[package];
        } else {
            return null;
        }
    }

    public Purchases.StoreProduct GetProduct(string namePack) {
        foreach (var product in _avaliablePackages.Values) {
            if (product.Identifier == namePack)
                return product;
        }

        return null;
    }

    public IAPDefinitionId GetDefinitionId(string package) {
        if (package == null) {
            return IAPDefinitionId.none;
        }

        if (package.Contains(':')) {
            package = package.Split(':')[0];
        }

        foreach (KeyValuePair<IAPDefinitionId, KeyValuePair<RcProductType, string>> product in _productIDs) {
            if (package == product.Value.Value) {
                return product.Key;
            }
        }

        Logger.LogError("[GetDefinitionId] Cannot find product: " + package);
        return IAPDefinitionId.none;
    }

    // Restore purchases previously made by this customer. Some platforms automatically restore purchases, like Google.
    // Apple currently requires explicit purchase restoration for IAP, conditionally displaying a password prompt.
    public void RestorePurchases(Action<bool> callback) {
        // If Purchasing has not yet been set up ...
        var mPopup = Util.ShowMessage(string.Empty, 0, false);
        if (mPopup != null) {
            mPopup.animate = PopUpAnimate.AUTO_CLOSE;
            mPopup.autoCloseTime = 2f;
            mPopup.UpdateMsg(LocalizationManager.instance.GetLocalizedValue("RP_START"));
        }

        _purchases.RestorePurchases((purchaserInfo, error) => {
            if (error != null) {
                if (mPopup != null) {
                    mPopup.UpdateMsg(LocalizationManager.instance.GetLocalizedValue("RP_ERROR") + ": " + error);
                }

                callback?.Invoke(false);
            } else {
                StartCoroutine(IEProcessPurchaserInfoReceived(purchaserInfo));
                callback?.Invoke(true);
                if (mPopup != null) {
                    mPopup.UpdateMsg(LocalizationManager.instance.GetLocalizedValue("RP_COMPLETED"));
                }
            }
        });
    }

    private Purchases.CustomerInfo _purchaserInfo;

    private void ProcessPurchase(IAPDefinitionId iapId, string productIdentifier,
                                 Purchases.CustomerInfo purchaserInfo) {
        _purchaserInfo = purchaserInfo;
        HideLoading();
        bool isSubscriptionId = IsSubscriptionId(iapId);
        Configuration.instance.CompletedPurchase(iapId, productIdentifier, isSubscriptionId);
        _callbackPurchase?.Invoke(true, null);
        OnPurchased?.Invoke(iapId);
        isprocessingPurchase = false;

        if (RemoteConfigBase.instance.IaaSegmentation_IsEnablePopup) {
            IaaSetUserValue(iapId, isSubscriptionId);
        }

        TrackPurchaseAirFlux(iapId);
    }

    private static void IaaSetUserValue(IAPDefinitionId iapId, bool isSubscriptionId) {
        if (isSubscriptionId && iapId != IAPDefinitionId.subs_no_ads) { // VIP trial
            IaaSegmentation.data.isHighValueFromTrial = true;
        } else { // mua gói IAP hoặc mua thành công VIP (ko trial)
            IaaSegmentation.data.isHighValueFromIAP = true;
        }

        IaaSegmentation.SetUserValue(UserValue.high_value);

    }

    public void TrackPurchaseAirFlux(IAPDefinitionId iapId) {
        if (AirfluxTracker.IsEnableAirFlux()) {
            Purchases.StoreProduct product = GetProduct(iapId);

            if (product == null) {
                //CustomException.Fire("TrackPurchaseAirFlux", $"cannot get product: {iapId}");
                return;
            }

            Logger.Log($"Airflux Order Completed: {iapId}");
            AirfluxTracker.TrackOrderCompleted(product);
        }
    }

    public string GetLastestTransactionId(string productIdentifier) {
        if (_purchaserInfo?.NonSubscriptionTransactions == null) {
            return string.Empty;
        }

        foreach (var transaction in _purchaserInfo.NonSubscriptionTransactions.OrderByDescending(t => t.PurchaseDate)) {
            if (transaction.ProductIdentifier == productIdentifier) {
                return transaction.TransactionIdentifier;
            }
        }

        return string.Empty;
    }

    private static bool IsSubscriptionId(IAPDefinitionId iapId) {
        return iapId == IAPDefinitionId.subscription_week || iapId == IAPDefinitionId.subscription_month ||
               iapId == IAPDefinitionId.subscription_year || iapId == IAPDefinitionId.subscription_old ||
               iapId == IAPDefinitionId.subs_no_ads;
    }

    private void OnApplicationPause(bool pauseStatus) {
        if (_isWaitingPurchase && !pauseStatus) {
            HideLoading();
        }
    }

    void ShowLoading() {
        if (Application.platform == RuntimePlatform.IPhonePlayer || Application.isEditor) {
            SceneFader.instance.ShowOverlay();
            _isWaitingPurchase = true;
        }
    }

    void HideLoading() {
        if (Application.platform == RuntimePlatform.IPhonePlayer || Application.isEditor) {
            SceneFader.instance.HideOverlay();
            _isWaitingPurchase = false;
        }
    }

    /// <summary>
    ///  This will fire whenever the Purchases SDK receives an updated purchaser info object from calls to getPurchaserInfo(), purchasePackage(), purchaserProduct(), or restoreTransactions()
    ///  So we only apply with subscription products 
    /// </summary>
    /// <param name="purchaserInfo"></param>
    public override void CustomerInfoReceived(Purchases.CustomerInfo purchaserInfo) {
        List<string> purchasedProduct = purchaserInfo.AllPurchasedProductIdentifiers;
        if (purchasedProduct == null || purchasedProduct.Count == 0) {
            Debug.Log($"[IAP] CustomerInfoReceived 0 purchasedProduct!");
            isLoadPurchaserInfo = true;
            return;
        }

        StartCoroutine(IEProcessPurchaserInfoReceived(purchaserInfo));
        if (NonSubPendingPurchaseManager.IsSyncedPurchases()) {
            StartCoroutine(IEProcessPendingPurchaseReceived(purchaserInfo));
        }
    }

    private IEnumerator IEProcessPurchaserInfoReceived(Purchases.CustomerInfo purchaserInfo) {
        while (_productIDs.Count == 0 || _avaliablePackages.Count == 0) {
            yield return null;
        }

        var purchasedProduct = purchaserInfo.Entitlements.Active;
        //Sort by purchased date to get latest _idSubsPurchased
        purchasedProduct = purchasedProduct.OrderBy(x => x.Value.LatestPurchaseDate)
            .ToDictionary(x => x.Key, x => x.Value);

        foreach (var purchased in purchasedProduct) {
            IAPDefinitionId id = GetDefinitionId(purchased.Key);
            Debug.Log($"[IAP] Convert IAPDefinitionId:{purchased.Key} => {id}.");

            if (_productIDs.ContainsKey(id) && _avaliablePackages.ContainsKey(id)) {
                Debug.Log($"[IAP] {id} exists in config");
                if (_productIDs[id].Key == RcProductType.subs || id == IAPDefinitionId.removeads) {
                    // Nếu đây là gói subs hoặc gói remove ads
                    ProcessPurchase(id, purchased.Value.ProductIdentifier, purchaserInfo);
                    if (_productIDs[id].Key == RcProductType.subs) {
                        _idSubsPurchased = id;
                    }
                } else { // xử lý gói bình thường, ví dụ như là hoàn tất consumable diamonds,...
                    Debug.Log($"[IAP] {id} restore consumable product {id} =>{purchased.Value.ProductIdentifier}");
                    ProcessPurchase(id, purchased.Value.ProductIdentifier, purchaserInfo);
                }
            } else {
                Debug.Log($"[IAP] {id} doesn't exist in config");
                DateTime? expiredDate = purchased.Value.ExpirationDate;
                if (expiredDate != null) { // chỉ có các gói subscription mới có expiredDate
                    // Nếu người dùng vẫn đang mua gói VIP, mà trong config hiện tại lại k có gói đó
                    // Khôi phục gói mặc định "subscription_old" cho khách hàng
                    if (purchaserInfo.Entitlements.All.ContainsKey("VIP") &&
                        purchaserInfo.Entitlements.All["VIP"].IsActive) {
                        ProcessPurchase(IAPDefinitionId.subscription_old, purchased.Value.ProductIdentifier,
                            purchaserInfo);
                    }
                } else {
                    // không khôi phục lại các gói consumable khi k có trong config
                }
            }
        }

        isLoadPurchaserInfo = true;
        yield return null;
    }

    /// <summary>
    /// Handle for Non-Subscription Transactions
    /// </summary>
    /// <param name="purchaserInfo"></param>
    /// <returns></returns>
    private IEnumerator IEProcessPendingPurchaseReceived(Purchases.CustomerInfo purchaserInfo) {
        if (!NonSubPendingPurchaseManager.HavePurchasedTransactions()) {
            NonSubPendingPurchaseManager.ForceUpdatePurchasedList(purchaserInfo.NonSubscriptionTransactions);
        }

        // chờ các product id được init và chờ khi user đang chơi
        // Trường hợp mua IAP thành công, RevenueCat có thể bắn customerInfo về trước khi callback
        // nên cần chờ update lịch sử giao dịch trong callback trước khi xử lý trả thưởng ở CustomerInfoReceived
        yield return Inwave.Utils.WaitUntil(() =>
            IapBase.IsInited && Util.IsHomeScene() && !IapBase.IsProcessingPurchase);

        var nonSubTransaction = purchaserInfo.NonSubscriptionTransactions;
        foreach (var transaction in nonSubTransaction) {
            string productId = transaction.ProductIdentifier;
            string transactionId = transaction.TransactionIdentifier;

            NonSubPendingPurchaseManager.TryAddToUnprocessedList(productId, transactionId);
        }

        NonSubPendingPurchaseManager.OnProcessTransactions();
        isLoadPurchaserInfo = true;
    }

    /// <summary>
    /// SetAttributes for revenuecat tracking
    /// </summary>
    /// <returns></returns>
    private IEnumerator SetAttributes() {
        Dictionary<string, string> attrFb = new() {
            {"$appsflyerId", AppsFlyerSDK.AppsFlyer.getAppsFlyerId()},
            {"$firebaseAppInstanceId", IronsourceMapping.GetPseudoIDFromLocal()}
        };
        _purchases.SetAttributes(attrFb);

        AdvertisingIdFetcher.RequestAdvertisingId((advertisingId) => {
            if (!string.IsNullOrEmpty(advertisingId)) {
                if (Application.platform == RuntimePlatform.Android) {
                    Dictionary<string, string> attrAdr = new() {
                        {"$gpsAdId", advertisingId},
                        {"$androidId", SystemInfo.deviceUniqueIdentifier}
                    };
                    _purchases.SetAttributes(attrAdr);
                } else if (Application.platform == RuntimePlatform.IPhonePlayer) {
                    if (PlayerPrefs.GetInt(UserPermissions.IS_ALLOW_ATT_KEY, 0) == 1) {
                        Dictionary<string, string> attrIOS = new() {
                            {"$idfa", advertisingId},
                            {"$idfv", SystemInfo.deviceUniqueIdentifier}
                        };
                        _purchases.SetAttributes(attrIOS);
                    }
                }
            }
        });
        yield return null;
    }

    public void ReloadAttributes() {
        if (Application.platform == RuntimePlatform.IPhonePlayer &&
            PlayerPrefs.GetInt(UserPermissions.IS_ALLOW_ATT_KEY, 0) == 0) {
            AdvertisingIdFetcher.RequestAdvertisingId((advertisingId) => {
                if (!string.IsNullOrEmpty(advertisingId)) {
                    Debug.LogError("advertisingId_2: " + advertisingId);
                    Dictionary<string, string> attr = new() {
                        {"$idfa", advertisingId},
                        {"$idfv", SystemInfo.deviceUniqueIdentifier}
                    };
                    _purchases.SetAttributes(attr);
                }
            });
        }
    }

    private void UpdatePromotionalOffer() {
        IAPDefinitionId currentSub = GetIdSubsPurchased();
        GetPaymentDiscount(currentSub, null);
    }

    private Dictionary<string, Purchases.PromotionalOffer> _paymentDiscounts =
        new Dictionary<string, Purchases.PromotionalOffer>();

    public void GetPaymentDiscount(IAPDefinitionId iApDefinitionId, Action onDone) {
        if (!_avaliablePackages.ContainsKey(iApDefinitionId) || _loadingDiscountPaymentIds.Contains(iApDefinitionId)) {
            return;
        }

        var product = _avaliablePackages[iApDefinitionId];
        if (product.Discounts != null && product.Discounts.Length > 0 &&
            !_paymentDiscounts.ContainsKey(product.Identifier)) {
            _loadingDiscountPaymentIds.Add(iApDefinitionId);
            _purchases.GetPromotionalOffer(product, product.Discounts[0], (discount, error) => {
                _loadingDiscountPaymentIds.Remove(iApDefinitionId);
                if (error != null) {
                    Debug.Log("[GetPaymentDiscount] product " + product.Identifier + " => " + error.Message);
                } else {
                    if (!_paymentDiscounts.ContainsKey(product.Identifier)) {
                        _paymentDiscounts.Add(product.Identifier, discount);
                    }
                }

                onDone?.Invoke();
            });
        } else {
            onDone?.Invoke();
        }
    }

    public bool IsUnSubUser() {
        if (PlayerPrefs.HasKey(SubscriptionController.Key_UserType)) {
            return PlayerPrefsCache.GetInt(SubscriptionController.Key_UserType, 0) ==
                   (int) SUBSCRIPTION_USERTYPE.CANCEL;
        }

        return isLoadPurchaserInfo && _idSubsPurchased != IAPDefinitionId.none;
    }

    public IAPDefinitionId GetIdSubsPurchased() {
        //get from local
        if (PlayerPrefs.HasKey(SubPackageManager.CurrentPurchasedIdKey)) {
            string currentPurchasedId = SubPackageManager.GetCurrentPurchasedId();

            IAPDefinitionId iapDefinitionId = GetDefinitionId(currentPurchasedId);
            if (iapDefinitionId != IAPDefinitionId.none) {
                return iapDefinitionId;
            } else {
                var oldDefinition = SubPackageManager.GetCachedDefinitionId(currentPurchasedId);
                if (oldDefinition != IAPDefinitionId.none) {
                    iapDefinitionId = oldDefinition;
                    return iapDefinitionId;
                }
            }
        }

        //get from app store
        if (isLoadPurchaserInfo) {
            return _idSubsPurchased;
        }

        return IAPDefinitionId.none;
    }

    public Purchases.StoreProduct GetPromotionalOffer(IAPDefinitionId id) {
        return _avaliablePackages.TryGetValue(id, out Purchases.StoreProduct product) ? product : null;
    }

    private bool IsPurchaseError(string productId, Purchases.Error error, Purchases.CustomerInfo customerInfo) {
        if (error != null) {
            var errorCode = (RCPurchasesErrorCode) error.Code;
            FireEvent("PurchaseFailed", productId, errorCode.ToString());
            switch (errorCode) {
                case RCPurchasesErrorCode.PaymentPendingError:
                    FireEvent("PurchasePending", productId);
                    if (NonSubPendingPurchaseManager.IsSupportedPendingHandler(IapBase.GetDefinitionId(productId))) {
                        return true;
                    }

                    return false;

                case RCPurchasesErrorCode.ProductAlreadyPurchasedError:
                    if (NonSubPendingPurchaseManager.IsSupportedPendingHandler(IapBase.GetDefinitionId(productId))) {
                        return true;
                    }

                    return false;
            }

            return true;
        }

        FireEvent("PurchaseSuccess", productId);
        if (customerInfo != null) {
            NonSubPendingPurchaseManager.ForceUpdatePurchasedList(customerInfo.NonSubscriptionTransactions);
        } else {
            Logger.LogError($"[IsPurchaseError] no customer infro after purchasing {productId}");
        }

        return false;
    }

    private void FireEvent(string eventName, string packageId, string errorCode = null) {
        var param = new Dictionary<string, object>();
        string countryCode = DeviceCarrierInfo.instanceSafe.GetIsoCountryCode() ?? "UNKNOWN";
        param.Add(TRACK_NAME.country_code, countryCode);
        param.Add(TRACK_NAME.pack_id, packageId);
        if (errorCode != null) {
            param.Add(TRACK_NAME.error_code, errorCode);
        }

        AnalyticHelper.FireString(eventName, param);
    }

    public string GetPackagePriceCode(IAPDefinitionId idPack) {
        var product = GetProduct(idPack);
        if (product == null) {
            return string.Empty;
        }

        return product.CurrencyCode;
    }

    public string GetPackagePriceCode(string namePack) {
        var product = GetProduct(namePack);
        if (product == null) {
            return string.Empty;
        }

        return product.CurrencyCode;
    }
}