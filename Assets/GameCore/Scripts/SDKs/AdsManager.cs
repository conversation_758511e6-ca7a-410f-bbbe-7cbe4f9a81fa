using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System;
using System.Text;
using DG.Tweening;
using Inwave;
using Newtonsoft.Json;
using TilesHop.Cores.IAASegmentation;
using TilesHop.Cores.Pooling;
using System.Threading;

public enum AdDelayOption {
    BeforeFirstFS,
    BetweenFS,
    BetweenRVAndFS
}

public enum AdErrorType {
    FsShow,
    FsCaching,
    RwShow,
    RwCaching,
    BnCaching
}

public class AdsManager : Singleton<AdsManager>, IEscapeHandler {
    #region Fields

    [HideInInspector] public VIDEOREWARD  rewardType = VIDEOREWARD.SKIPABLE;
    [HideInInspector] public object       rewardItem = null;
    private                  bool         isFirst    = true;
    public                   AdNetwork    adn;
    public                   AdNetwork    secondaryAdn;
    public                   List<string> secondaryCountries;
    [HideInInspector] public bool         allowBanner    = true;
    float                                 nextAdsTime    = 0;
    int                                   nextGameCount  = 0;
    private float                         _lastRwAdsTime = 0;

    public float lastRwAdsTime => _lastRwAdsTime;
    [HideInInspector] public bool isCachingFs = false;
    [HideInInspector] public bool isCachingRw = false;

    private int          rewardCompletedStep = 0;
    private Action<bool> OnRewardCompleted;
    private Song _relatedSong = null;

    private string _locationRewardAds = String.Empty;
    public string LocationRewardAds => _locationRewardAds;
    public IronSourceImpressionData lastRewardAdImpressionData;

    private event Action onCloseInterstitial;

    public Action OnCloseInterstitial;

    private string _locationInterstitial = string.Empty;
    public string LocationInterstitialAds => _locationInterstitial;

    private Coroutine                       _corWaitingRv;
    private Dictionary<AdErrorType, string> _adErrors = new Dictionary<AdErrorType, string>();

    public delegate void OnBannerLoadedHandler();

    public delegate void OnBannerChangeHandler();

    public event OnBannerLoadedHandler OnBannerLoaded;
    public event OnBannerChangeHandler OnBannerChange;

    public static bool IsLeavingByAds = false;
    public static bool isCmpConsented = false;

    public bool IsEnableBanner {
        get {
            if (RemoteConfig.instance == null) {
                return false;
            }

            if (!RemoteConfig.instance.BannerAd_Enable) {
                return false;
            }

            if (Configuration.IsNoAllAds()) {
                return false;
            }

            if (Configuration.IsNoSomeAds()) {
                return false;
            }

            if (Configuration.IsNoFSAds()) {
                return false;
            }

            int amountSession = UserProperties.GetSessionCount();
            if (amountSession < RemoteConfig.instance.BannerAd_x_session) {
                return false;
            }

            int songResult = AnalyticHelper.CountEvent(SONG_STATUS.song_result.ToString());
            if (songResult < RemoteConfig.instance.BannerAd_x_song_results) {
                return false;
            }

            return true;
        }
    }

    public bool IsEnableInterstitial {
        get {
            if (Configuration.IsNoAllAds()) {
                return false;
            }

            if (Configuration.IsNoSomeAds()) {
                return false;
            }

            if (Configuration.IsNoFSAds()) {
                return false;
            }

            return true;
        }
    }

    public float timeShowInterstitial { get; private set; } = -1f;
    public float timeShowRewardedAds { get; private set; } = -1f;

    #endregion

    #region Unity Method

    private void Start() {
        _adErrors = new Dictionary<AdErrorType, string> {
            { AdErrorType.FsShow, null },
            { AdErrorType.FsCaching, null },
            { AdErrorType.RwShow, null },
            { AdErrorType.RwCaching, null }
        };
        
        // Khởi tạo consent và privacy metadata trước
        SetConsent();
        SetPrivacyMetaData();

        // Sử dụng coroutine để khởi tạo quảng cáo bất đồng bộ
        StartCoroutine(InitializeAdsAsync());
        
        CMPWrapper.OnConsentCheckPassed += SetConsent;
    }

    private IEnumerator InitializeAdsAsync() {
        // Đợi một frame để đảm bảo Start() hoàn thành
        yield return null;
        
        // Đợi song list hoàn thành nếu cần
        if (Configuration.isFirstOpen) {
            yield return StartCoroutine(WaitForSongListAsync());
        }
        
        // Khởi tạo quảng cáo bất đồng bộ
        yield return StartCoroutine(InitAdNetAsync());
    }

    private IEnumerator WaitForSongListAsync() {
        while (!SongManager.isInitedSongList) {
            yield return null; // Tối ưu: chỉ đợi 1 frame thay vì 0.1s
        }
    }

    private IEnumerator InitAdNetAsync() {
        // Khởi tạo quảng cáo trong coroutine để tránh block main thread
        yield return StartCoroutine(InitAsync());
        
        Util.WaitRemoteConfigDone(RemoteConfigLoaded, true);
    }

    private IEnumerator InitAsync() {
        // Xử lý secondary ad network
        if (secondaryCountries.Count > 0 && secondaryAdn != null) {
            string country = DeviceCarrierInfo.instanceSafe.GetIsoCountryCode();
            if (!string.IsNullOrEmpty(country) && secondaryCountries.Contains(country.ToUpper())) {
                adn = secondaryAdn;
            }
        }

        if (adn != null) {
            // Khởi tạo ad network bất đồng bộ
            yield return StartCoroutine(InitAdNetworkAsync());
            
            // Load ads sau khi khởi tạo
            TriggerLoadFullAds(true);
            LoadRewardAds(VIDEOREWARD.None, null);
        }
    }

    private IEnumerator InitAdNetworkAsync() {
        bool initSuccess = false;
        
        // Xử lý khởi tạo ad network
        if (adn is AdIronSource ironSourceAd) {
            // Khởi tạo IronSource với timeout
            yield return StartCoroutine(InitIronSourceWithTimeout(ironSourceAd));
            initSuccess = true;
        } else {
            // Khởi tạo ad network khác
            try {
                adn.Init();
                initSuccess = true;
            } catch (System.Exception e) {
                Debug.LogError($"[AdsManager] Error initializing ad network: {e.Message}");
                initSuccess = false;
            }
        }
        
        // Fallback nếu khởi tạo thất bại
        if (!initSuccess) {
            yield return new WaitForSeconds(2f);
            try {
                adn.Init();
            } catch (System.Exception retryEx) {
                Debug.LogError($"[AdsManager] Retry initialization failed: {retryEx.Message}");
            }
        }
    }

    private IEnumerator InitIronSourceWithTimeout(AdIronSource ironSourceAd) {
        // Sử dụng timeout cố định để tránh ANR
        float timeout = 10f;
        float elapsed = 0f;
        
        // Bắt đầu khởi tạo
        bool initCompleted = false;
        int retryCount = 0;
        int maxRetries = 2;
        
        while (retryCount <= maxRetries && !initCompleted) {
            if (retryCount > 0) {
                Debug.Log($"[AdsManager] Retrying IronSource initialization (attempt {retryCount}/{maxRetries})");
                yield return new WaitForSeconds(2f);
            }
            
            // Khởi tạo IronSource trên main thread để tránh coroutine issues
            try {
                ironSourceAd.Init();
                initCompleted = true;
            } catch (System.Exception e) {
                Debug.LogError($"[AdsManager] IronSource init error (attempt {retryCount + 1}): {e.Message}");
                initCompleted = false;
            }
            
            // Đợi khởi tạo hoàn thành hoặc timeout
            elapsed = 0f;
            while (!initCompleted && elapsed < timeout) {
                yield return null; // Tối ưu: chỉ đợi 1 frame thay vì 0.1s
                elapsed += Time.deltaTime; // Sử dụng deltaTime thay vì 0.1f cố định
            }
            
            if (!initCompleted) {
                Debug.LogWarning($"[AdsManager] IronSource initialization timeout (attempt {retryCount + 1})");
                retryCount++;
            }
        }
        
        if (!initCompleted) {
            Debug.LogError("[AdsManager] IronSource initialization failed after all retries");
        } else {
            Debug.Log("[AdsManager] IronSource initialization completed successfully");
        }
    }

    #endregion

    #region Escape Handlers

    public bool CanHandleEventBack() {
        return IsWaitingRv();
    }

    public bool HandleEventBack() {
        DiscardWaitingRv();
        return true;
    }

    #endregion

    public string GetAdError(AdErrorType type) {
        if (_adErrors == null || !_adErrors.ContainsKey(type)) {
            return null;
        }

        return _adErrors[type];

    }

    // Các method cũ đã được thay thế bằng các coroutine bất đồng bộ

    public void SetConsent() {
        UnityMainThreadDispatcher.Instance().Enqueue(() => {
            isCmpConsented = true; // UserPermissions.IsATTAllowed();
            string additionalConsent = IabTcfStringHelper.GetAdditionalConsent();
            if (CMPWrapper.IsEeaOrUK && !string.IsNullOrEmpty(additionalConsent)) {
                //Debug.Log($"[AdsManager] Passing consent to ironSource: {additionalConsent.Contains("2878")}. In EEA or UK: {CMPWrapper.IsEeaOrUK}.");
                isCmpConsented &= additionalConsent.Contains("2878");
            } else {
                isCmpConsented = true;
                //Debug.Log($"[AdsManager] Force passing consent to ironSource. In EEA or UK: {CMPWrapper.IsEeaOrUK}.");
            }

            adn.SetConsent(isCmpConsented);
        });
    }

    private void RemoteConfigLoaded() {
        //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

        //Load banner
        allowBanner = IsEnableBanner;
        LoadBanner();

        //Set ads time
        SetNextAdsTime(AdDelayOption.BeforeFirstFS);
    }

    public bool IsWaitingRv() {
        return _corWaitingRv != null;
    }

    public bool DiscardWaitingRv() {
        if (_corWaitingRv != null) {
            SceneFader.instance.HideOverlay();
            StopCoroutine(_corWaitingRv);
            _corWaitingRv = null;
            EventEscapeManager.Pop(this);
            return true;
        } else {
            return false;
        }
    }

    public void RequestShowInterstitial(string location, Song song, Action complete = null) {
        // Show Interstitial Video Ads
        if (IsEnableInterstitial) {
            nextGameCount++;
            if (Time.time > nextAdsTime || (RemoteConfig.instance != null &&
                                            nextGameCount >= (RemoteConfig.instance?.Ad_MaxGamesBetweenFS ?? 5))) {
                ShowInterstitial(location, song, complete);
            } else {
                complete?.Invoke();
            }
        } else {
            complete?.Invoke();
        }
    }

    // opt==0: Ad_DelayBeforeFirstFS
    // opt==1: Ad_DelayBetweenFS
    // opt==2: Ad_DelayBetweenRVAndFS
    public void SetNextAdsTime(AdDelayOption opt) {
        nextGameCount = 0;
        switch (opt) {
            case AdDelayOption.BeforeFirstFS:
                nextAdsTime = Time.time + (RemoteConfig.instance?.Ad_DelayBeforeFirstFS ?? 30);
                break;

            case AdDelayOption.BetweenFS:
                nextAdsTime = Time.time + (RemoteConfig.instance?.Ad_DelayBetweenFS ?? 30);
                break;

            case AdDelayOption.BetweenRVAndFS:
                _lastRwAdsTime = Time.time;
                nextAdsTime = _lastRwAdsTime + (RemoteConfig.instance?.Ad_DelayBetweenRVAndFS ?? 15);
                break;
        }
    }

    private Coroutine _cLoadFullAds;

    private void TriggerLoadFullAds(bool shouldLoad, float delay=0) {
        if (shouldLoad) {
            _cLoadFullAds ??= StartCoroutine(IELoadFullAds(delay));
        } else {
            if (_cLoadFullAds == null) {
                return;
            }

            StopCoroutine(_cLoadFullAds);
            _cLoadFullAds = null;
        }
    }

    private IEnumerator IELoadFullAds(float delay) {
        yield return delay > 0 ? new WaitForSeconds(delay) : null;

        int timeStep = 2;
        var wait = new WaitForSeconds(timeStep);
        var timeOut = RemoteConfig.instance?.Ad_CacheRetryTimeout ?? 10;
        var counter = 0;
        while (true) {
            if (Utils.isInternetReachable && !adn.IsInterstitialReady()) {
                if (!isCachingFs || counter >= timeOut) {
                    counter = 0;
                    isCachingFs = true;
                    AnalyticHelper.LogFullAds(AD_STATE.request, string.Empty);
                    adn.LoadInterstitial();
                }
            }

            counter += timeStep;
            yield return wait; // Giữ nguyên: cần delay thực sự để tránh spam
        }
    }

    #region Banner Ad

    public void LoadBanner() {
        if (!Utils.isInternetReachable) {
            return;
        }

        if (allowBanner && IsEnableBanner) {
            adn.LoadBanner();
        }
    }

    public float GetBannerHeight() {
        if (allowBanner && IsEnableBanner) {
            return adn.GetBannerHeight();
        }

        Logger.Log($"[Banner] bannerSize DPI: {0} because banner not active!!!????");
        return 0;
    }

    public void HideBanner() {
        //Debug.Log("[HideBanner] invoke");
        if (adn != null) {
            //Debug.Log("[AdsManager] HideBanner");
            adn.HideBanner();
        }
    }

    public void DestroyBanner() {
        if (adn != null) {
            adn.DestroyBanner();
        }
    }

    public void ShowBanner() {
        //Debug.Log("[ShowBanner] invoke");
        if (IsBannerAvailable() && adn != null) {
            //Debug.Log("[AdsManager] ShowBanner");
            adn.ShowBanner();
        }
    }

    public void BannerAdRequestEvent() {
        AnalyticHelper.LogBannerAds(AD_STATE.request);
    }

    public void BannerAdLoadedEvent() {
        OnBannerLoaded?.Invoke();
    }

    public void BannerAdShowedEvent() {
        AnalyticHelper.LogBannerAds(AD_STATE.show);
        OnBannerChange?.Invoke();
#if UNITY_EDITOR
        EditorShowBanner();
#endif
    }

    public void BannerAdHideEvent() {
        OnBannerChange?.Invoke();
#if UNITY_EDITOR
        EditorHideBanner();
#endif
    }

    public void BannerAdLoadFailedEvent(string error) {
        _adErrors[AdErrorType.BnCaching] = error;
        AnalyticHelper.LogBannerAds(AD_STATE.request_failed);
    }

    public bool IsBannerAvailable() {
        return allowBanner && IsEnableBanner;
    }

    public bool IsBannerLoaded() {
        return adn.bannerRequestStatus == AdNetwork.BannerRequestStatus.Loaded;
    }

    public bool IsBottomBannerShown() {
        return RemoteConfig.instance != null && RemoteConfig.instance.BannerAd_IsBottom && IsBannerAvailable() &&
               IsBannerShowed();
    }

    public bool IsTopBannerShown() {
        return RemoteConfig.instance != null && !RemoteConfig.instance.BannerAd_IsBottom && IsBannerAvailable() &&
               IsBannerShowed();
    }

    public bool IsBannerShowed() {
        return adn.bannerStatus == AdNetwork.BANNER_STATUS.SHOWED;
    }

    #endregion

    #region InterstitialAds

    public void InterstitialAdOpenedEvent() {
        timeShowInterstitial = Time.realtimeSinceStartup;
        AdsManager.IsLeavingByAds = true;
        AnalyticHelper.LogFullAds(AD_STATE.opened, _locationInterstitial);
    }

    public void InterstitialAdShowSucceededEvent() {
        AnalyticHelper.LogFullAds(AD_STATE.show_success, _locationInterstitial);
        timeShowInterstitial = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }
    
    public void ShowInterstitial(string location, Song song, Action onClose) {
        this._locationInterstitial = location;
        _relatedSong = song;
        this.onCloseInterstitial = onClose;
        AnalyticHelper.LogFullAds(AD_STATE.show_ready, _locationInterstitial);
        if (isFirst) {
            isFirst = false;
        }

        bool isReady = adn.IsInterstitialReady() || Application.isEditor;
        TriggerLoadFullAds(!isReady);
        if (isReady) {
            bool canRequestAirFluxInference = RemoteConfig.instance?.AirFlux_IsEnable == true &&
                                              RemoteConfig.instance?.AirFlux_InferenceShowAd == true &&
                                              Airflux.IsSDKEnabled();

            if (canRequestAirFluxInference) {
                UIOverlay.instance.RequestShowAdOverlay();

                Airflux.InferenceShowAdInterstitial(onShowAd: HandleOnAirFluxShowAd, onSkipAd: HandleOnAirFluxSkipAd,
                    onFailure: HandleOnAirFluxFailed);
            } else {
                ShowInterAd();
            }

            if (Application.isEditor) {
                InterstitialAdShowSucceededEvent();
                // Sử dụng coroutine thay vì DOVirtual.DelayedCall để tránh DelayedCallManager crash
                StartCoroutine(DelayedInterstitialAdClosedEvent());
            }
        } else if (PlaygapManager.CanShowAd()) {
            _locationInterstitial = "PlaygapAd";
            PlaygapManager.instanceSafe.ShowInterstitialAdPressed();
        } else {
            AnalyticHelper.LogFullAds(AD_STATE.show_notready, _locationInterstitial);
            this.onCloseInterstitial?.Invoke();
        }
    }

    private void HandleOnAirFluxShowAd() {
        UIOverlay.instance.RequestHideAdOverlay();
        ShowInterAd();
    }

    private void HandleOnAirFluxSkipAd() {
        UIOverlay.instance.RequestHideAdOverlay();
    }

    private void HandleOnAirFluxFailed(AirfluxError error) {
        UIOverlay.instance.RequestHideAdOverlay();
        ShowInterAd();
        Logger.LogError($"[AirFlux][InferenceShowAdInterstitial Failed] {error.ToString()}");
    }

    private void ShowInterAd() {
        UIOverlay.instance.InterstitialAdShowEvent();
        ToggleGameActivities(_locationInterstitial, true);
        AnalyticHelper.LogFullAds(AD_STATE.show, _locationInterstitial);
        AnalyticHelper.Screen(LOCATION_NAME.INTERSTITIAL_AD.ToString(), LOCATION_NAME.ADS.ToString());
        if (RemoteConfig.instance?.SilentMode_IsEnable == true) {
            AmanoteNativeBinding.PlaySoundInSilentMode();
        }

        timeShowInterstitial = Time.realtimeSinceStartup;
        IsLeavingByAds = true;
        adn.ShowInterstitial();
    }

    public void InterstitialAdClickedEvent() {
        AnalyticHelper.LogFullAds(AD_STATE.click, _locationInterstitial);
    }

    public void InterstitialAdReadyEvent() {
        isCachingFs = false;
        AnalyticHelper.LogFullAds(AD_STATE.request_success, String.Empty);
    }

    public void InterstitialAdLoadFailedEvent(string error) {
        isCachingFs = false;
        _adErrors[AdErrorType.FsCaching] = error;
        AnalyticHelper.LogFullAds(AD_STATE.request_failed, String.Empty);
    }

    public void InterstitialAdShowFailedEvent(string error) {
        this.onCloseInterstitial?.Invoke();
        UIOverlay.instance.InterstitialAdShowFailedEvent();
        _adErrors[AdErrorType.FsShow] = error;
        AnalyticHelper.LogFullAds(AD_STATE.show_failed, _locationInterstitial);
        timeShowInterstitial = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    public void InterstitialAdClosedEvent() {
        CustomException.SetKey(FirebaseKey.LastAdAction, "[Interstitial][ClosedAd]");
        this.onCloseInterstitial?.Invoke();
        OnCloseInterstitial?.Invoke();
        AnalyticHelper.LogFullAds(AD_STATE.finish, _locationInterstitial);
        timeShowInterstitial = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
        bool inShowFromGameSetting =
            _locationInterstitial.Equals(LOCATION_NAME.GAME_SETTINGS.ToString(), StringComparison.OrdinalIgnoreCase);

        UIOverlay.instance.InterstitialAdClosedEvent(!inShowFromGameSetting);
        SetNextAdsTime(AdDelayOption.BetweenFS);
        ToggleGameActivities(_locationInterstitial, false);
        AnalyticHelper.ScreenBack(_locationInterstitial);
        if (RemoteConfig.instance?.SilentMode_IsEnable == true) {
            SuperpoweredSDK.instance.ReloadAudioSource();
        }

        IaaSegmentation.CountFirstDayFsAd();
        TriggerLoadFullAds(true, 2);
    }

    #endregion

    #region Coroutines

    private IEnumerator DelayedInterstitialAdClosedEvent() {
        yield return new WaitForSeconds(0.1f);
        InterstitialAdClosedEvent();
    }

    #endregion

    #region Rewarded Video

    public void ShowRewardAds(VIDEOREWARD reward_type, object reward_item, string location, bool isFirstRequest = true,
                              Action<bool> OnCompleted = null, bool isAutoRewardWhenAdsNotReady = true) {
        OnRewardCompleted = OnCompleted;
        rewardCompletedStep = 0;
        rewardItem = reward_item;
        if (rewardItem is Song relatedSong) {
            _relatedSong = relatedSong;
        } else {
            _relatedSong = null;
        }
        rewardType = reward_type;
        this._locationRewardAds = location;
        if (isFirstRequest) {
            AnalyticHelper.LogVideoAds(AD_STATE.show_ready, rewardType, rewardItem, location: _locationRewardAds);
        }

        bool isAvailable = adn.IsRewardedVideoAvailable() || Configuration.instance.isDebug;
        if (isAvailable) {
            ToggleGameActivities(_locationRewardAds, true);
            AnalyticHelper.Screen(LOCATION_NAME.REWARD_AD.ToString(), LOCATION_NAME.ADS.ToString());
            AnalyticHelper.LogVideoAds(AD_STATE.show, rewardType, rewardItem, this._locationRewardAds);
            UIOverlay.instance.RewardAdShowEvent();

            if (RemoteConfig.instance?.SilentMode_IsEnable == true) {
                AmanoteNativeBinding.PlaySoundInSilentMode();
            }

            timeShowRewardedAds = Time.realtimeSinceStartup;
            IsLeavingByAds = true;
            adn.ShowRewardedVideo(GetPlacement());

            if (RemoteConfig.instance?.downloadWhenWatchingRW == true && reward_item is Song song) {
                DownloadManager.instanceSafe.CheckDataAndDownload(song);
            }

            if (Configuration.instance.isDebug) {
                //AnalyticHelper.LogRewardAds(AD_STATE.show, rewardType, rewardItem);
                rewardCompletedStep = 1;
                CompleteAdRewarded();
            }
        } else if (isFirstRequest) {
            _corWaitingRv = StartCoroutine(WaitingRewardedVideo(isAutoRewardWhenAdsNotReady));
            EventEscapeManager.Push(this);

        } else if (PlaygapManager.CanShowAd()) {
            _locationRewardAds = "PlaygapAd";
            PlaygapManager.instanceSafe.ShowRewardedAdPressed();

        } else {
            // because of no-internet
            if (Utils.IsAndroid() && !Utils.isInternetReachable) {
                NoInternetPopup.Show();
            } else {
                Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("REWARD_VIDEO_UNAVAILABLE"));
            }

            AnalyticHelper.LogVideoAds(AD_STATE.show_notready, rewardType, rewardItem, this._locationRewardAds);
            OnCompleted?.Invoke(false);
        }
    }

    private string GetPlacement() {
        switch (rewardType) {
            case VIDEOREWARD.revive:
                return "reborn";

            case VIDEOREWARD.currency_x2:
                return "double";

            case VIDEOREWARD.currency:

                if (FreeVideo.instance != null) {
                    return "diamond_freegift";
                } else if (Shop.instance != null) {
                    return "diamond_shop";
                }

                break;

            case VIDEOREWARD.item:
                // if (BallList.instance != null && BallList.instance.gameObject.activeSelf) {
                //     return "character_collection";
                // }
                if (FreeVideo.instance != null) {
                    return "character_freegift";
                }

                if (UIController.ui != null && UIController.ui.gameover.gameObject.activeSelf) {
                    return "character_result";
                }

                break;

            case VIDEOREWARD.song_unlock:
                if (FreeVideo.instance != null) {
                    return "stage_freegift";
                } else if (Util.IsHomeScene()) {
                    return "stage_home";
                } else if (UIController.ui != null && UIController.ui.gameover.gameObject.activeSelf) {
                    return "stage_result";
                }

                break;

            case VIDEOREWARD.CHALLENGE:
                return "challenge_home";
        }

        return null;
    }

    private IEnumerator WaitingRewardedVideo(bool isAutoRewardWhenAdsNotReady = true) {
        SceneFader.instance.ShowOverlay();
        float time = Time.realtimeSinceStartup;
        // Special case - tối ưu: chỉ đợi 1 frame thay vì 0.2s
        while (!adn.IsRewardedVideoAvailable() &&
               Time.realtimeSinceStartup - time < (RemoteConfig.instance?.Ad_WaitMaxTime ?? 10) &&
               Utils.isInternetReachable) {
            yield return null;
        }

        SceneFader.instance.HideOverlay();
        if (!adn.IsRewardedVideoAvailable() && Utils.isInternetReachable &&
            RemoteConfig.instance?.Ad_WaitMaxAllowUnlock == true && isAutoRewardWhenAdsNotReady) {
            if (PlaygapManager.CanShowAd()) {
                _locationRewardAds = "PlaygapAd";
                PlaygapManager.instanceSafe.ShowRewardedAdPressed();
            } else {
                AnalyticHelper.LogVideoAds(AD_STATE.special, rewardType, rewardItem, this._locationRewardAds);
                rewardCompletedStep = 1;
                CompleteAdRewarded(true);
            }
        } else {
            // try again
            ShowRewardAds(rewardType, rewardItem, this._locationRewardAds, false, OnRewardCompleted);
        }

        _corWaitingRv = null;
        EventEscapeManager.Pop(this);
    }

    public void RewardedVideoAdOpenedEvent() {
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = true;
        AnalyticHelper.LogVideoAds(AD_STATE.opened, rewardType, rewardItem, this._locationRewardAds);
    }

    public void RewardedVideoLoaded() {
        AnalyticHelper.LogVideoAds(AD_STATE.request_success, rewardType, rewardItem, string.Empty);
    }

    public void RewardedVideoLoadFailed(string error) {
        _adErrors[AdErrorType.RwCaching] = error;
        AnalyticHelper.LogVideoAds(AD_STATE.request_failed, rewardType, rewardItem, string.Empty);
    }

    public void RewardedVideoClickedEvent() {
        AnalyticHelper.LogVideoAds(AD_STATE.click, rewardType, rewardItem, this._locationRewardAds);
    }

    public void ToggleGameActivities(string location, bool isPause = true) {
        if (Application.isMobilePlatform && Application.platform == RuntimePlatform.IPhonePlayer) {
            if (isPause) {
                GroundMusic.instance.StopMusic(0);
            } else {
                if (Util.IsHomeScene()) {
                    GroundMusic.instance.PlayMusic(location);
                }
            }
        }
    }

    public void RewardedVideoAdRewardedEvent() {
        CompleteAdRewarded();
    }

    public void RewardedVideoAdClosedEvent() {
        CustomException.SetKey(FirebaseKey.LastAdAction, "[Rewarded][ClosedAd]");
        UIOverlay.instance.RewardAdClosedEvent();
        ToggleGameActivities(_locationRewardAds, false);
        AnalyticHelper.ScreenBack(_locationRewardAds);
        SetNextAdsTime(AdDelayOption.BetweenRVAndFS);
        if (RemoteConfig.instance?.SilentMode_IsEnable == true) {
            SuperpoweredSDK.instance.ReloadAudioSource();
        }

        CompleteAdRewarded();
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    private void CompleteAdRewarded(bool isSpecial = false) {
#if UNITY_EDITOR
        if (RemoteConfig.instance?.SilentMode_IsEnable == true) {
            SuperpoweredSDK.instance.ReloadAudioSource();
        }
#endif

        if (Configuration.instance.isDebug) {
            UIOverlay.instance.RewardAdClosedEvent();
        }

        rewardCompletedStep++;
        if (rewardCompletedStep == 2) { //Require two steps: RewardedVideoAdClosedEvent and RewardedVideoAdRewardedEvent
            if (!isSpecial) {
                AnalyticHelper.LogVideoAds(AD_STATE.finish, rewardType, rewardItem, this._locationRewardAds);
            }

            OnRewardCompleted?.Invoke(true);
            IaaSegmentation.CountFirstDayRewardedAd();
        }
    }

    public void RewardedVideoAdSkipped() {
        ToggleGameActivities(_locationRewardAds, false);
        OnRewardCompleted?.Invoke(false);
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    public void RewardedVideoAdShowFailedEvent(string error) {
        UIOverlay.instance.RewardAdShowFailedEvent();
        _adErrors[AdErrorType.RwShow] = error;
        ToggleGameActivities(_locationRewardAds, false);
        AnalyticHelper.ScreenBack(_locationRewardAds);
        AnalyticHelper.LogVideoAds(AD_STATE.show_failed, rewardType, rewardItem, this._locationRewardAds);
        OnRewardCompleted?.Invoke(false);
        timeShowRewardedAds = Time.realtimeSinceStartup;
        IsLeavingByAds = false;
    }

    private void LoadRewardAds(VIDEOREWARD type, object reward) {
        //Auto caching
    }

    #endregion

    public void OnApplicationPause(bool pauseStatus) {
        if (adn != null) {
            adn.AppPause(pauseStatus);
        }

        // if (!pauseStatus) {
        //     if (IsLeavingByAds) {
        //         StartCoroutine(IEResumeByAds());
        //     }
        // }
    }

    // private static IEnumerator IEResumeByAds() {
    //     yield return new WaitForSeconds(0.5f);
    //     IsLeavingByAds = false;
    // }

    #region UnityAds

    private void SetPrivacyMetaData() {
#if UNITY_ANDROID //&& !UNITY_EDITOR
        if (Application.isEditor) {
            return;
        }

        AndroidJavaClass unityPlayerClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        AndroidJavaObject unityActivity = unityPlayerClass.GetStatic<AndroidJavaObject>("currentActivity");

        AndroidJavaObject metaDataObj = new AndroidJavaObject("com.unity3d.ads.metadata.MetaData", unityActivity);
        metaDataObj.Call<bool>("set", "privacy.mode", "mixed");
        metaDataObj.Call<bool>("set", "user.nonbehavioral", false);
        metaDataObj.Call("commit");
#endif
    }

    #endregion

    #region Log AD ID

    private StringBuilder _sbLogAd;

    public void AddLogAd(Dictionary<string, string> dicAdInfo) {
        string serializeObject = DateTime.Now.ToLongTimeString() + " => " + JsonConvert.SerializeObject(dicAdInfo);

        if (Configuration.isAdmin) {
            _sbLogAd ??= new StringBuilder();
            _sbLogAd.AppendLine(serializeObject);
        }

        PlayerPrefs.SetString(PlayerPrefsKey.lastIdAds, serializeObject);
    }

    public string GetLogAd() {
        if (_sbLogAd == null) {
            string s = PlayerPrefs.GetString(PlayerPrefsKey.lastIdAds, "Empty!");
            return s;
        } else {
            return _sbLogAd.ToString();
        }
    }

    #endregion

    #region editor

#if UNITY_EDITOR

    private GameObject _testBanner;

    private void EditorShowBanner() {
        if (_testBanner == null) {
            string path = @"Assets\GameCore\Editor\Prefabs\Canvas_Banner.prefab";
            GameObject textAsset = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(path);
            _testBanner = Instantiate(textAsset);
        }

        _testBanner.SetActive(true);
    }

    private void EditorHideBanner() {
        if (_testBanner) {
            _testBanner.SetActive(false);
        }
    }

#endif

    #endregion

    public void RecheckBannerStatus() {
        if (allowBanner)
            return;

        //Load banner
        allowBanner = IsEnableBanner;
        if (allowBanner) {
            LoadBanner();
        }
    }

    public bool IsInterstitialReady() {
        return adn.IsInterstitialReady();
    }

    public void TryAddSongParam(Dictionary<string, object> paramFirebase) {
        paramFirebase??= new Dictionary<string, object>();
        if (_relatedSong == null) {
            paramFirebase.TryAdd(TRACK_NAME.song_acm_id, "none");
            paramFirebase.TryAdd(TRACK_NAME.song_name, "none");
            paramFirebase.TryAdd(TRACK_NAME.song_order, "none");
        } else {
            string song_acm_id = _relatedSong.acm_id_v3;
            if (string.IsNullOrEmpty(song_acm_id)) {
                if (_relatedSong.isTutorialSong) {
                    song_acm_id = "tutorial";
                } else if (_relatedSong.IsLocalSong()) {
                    song_acm_id = "local_song";
                }
            }
            paramFirebase.TryAdd(TRACK_NAME.song_acm_id, song_acm_id);
            paramFirebase.TryAdd(TRACK_NAME.song_name, _relatedSong.name);
            paramFirebase.TryAdd(TRACK_NAME.song_order, _relatedSong.ordering);
        }
    }
}