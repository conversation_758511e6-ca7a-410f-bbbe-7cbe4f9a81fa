using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;
using Inwave;
using JetBrains.Annotations;
using TilesHop.Cores.IAASegmentation;
using TilesHop.EconomySystem;
using UnityEngine;

public static class NonSubPendingPurchaseManager {
    public const    string                   UNPROCESSED_LIST_KEY = "nonsub_unprocess_after_pending";
    public const    string                   PURCHASED_LIST_KEY   = "purchased_transactions";
    
    private static readonly HashSet<IAPDefinitionId> _supportedDefinitionIds = new () { 
        IAPDefinitionId.seasonal_pack_p1,
        IAPDefinitionId.seasonal_pack_p2,
        IAPDefinitionId.diamond1,
        IAPDefinitionId.diamond2,
        IAPDefinitionId.diamond3,
        IAPDefinitionId.diamond4,
        IAPDefinitionId.diamond5,
        IAPDefinitionId.starter_pack,
        IAPDefinitionId.special_offer,
        IAPDefinitionId.advanced_pack,
        IAPDefinitionId.master_pack,
    };

    private static List<string> _unprocessedProductIds;
    private static List<string> _purchasedTransactionIds;
    private static bool         _isLoaded = false;
    // public static event Action  onProcessTransactions;
    
    public static void OnProcessTransactions() {
        if (!_isLoaded) {
            LoadPurchasesData();
        }

        if (_unprocessedProductIds.IsNullOrEmpty()) {
            return;
        }
        
        ProcessCompletedPendingPurchases();
    }

    private static void ProcessCompletedPendingPurchases() {
        // onProcessTransactions?.Invoke();
        bool isRewarded = false;

        // seasonal pack
        if (SeasonalPackManager.isEnable) {
            isRewarded = SeasonalPackManager.HandleTransactionAfterPending();
        }
        
        // stater pack
        string starterPackProductId = IapBase.GetProductID(IAPDefinitionId.starter_pack);
        if (!string.IsNullOrEmpty(starterPackProductId) && _unprocessedProductIds.Contains(starterPackProductId)) {
            EconomyOfferStarterPack.MakePurchase(EconomyIAPTracker.TRACK_LOCATION.pending_purchase);
            _unprocessedProductIds.Remove(starterPackProductId);
            isRewarded = true;
        } else {
            var config = EconomyOfferStarterPack.GetConfig();
            if(config != null) {
                var secondProductId = Utils.IsAndroid()
                    ? config.ProductID_Android_Secondary
                    : config.ProductID_IOS_Secondary;
            
                if (!string.IsNullOrEmpty(secondProductId) && _unprocessedProductIds.Contains(secondProductId)) {
                    EconomyOfferStarterPack.MakePurchase(EconomyIAPTracker.TRACK_LOCATION.pending_purchase);
                    _unprocessedProductIds.Remove(secondProductId);
                    isRewarded = true;
                }   
            }
        }
        
        // special offer
        if (SpecialOfferManager.isInstanced && SpecialOfferManager.instanceSafe.HandleUnprocessedPurchase()) {
            isRewarded = true;
        }

        // shop diamonds
        var packIds = IapBase.ShopPackIDs;
        
        foreach (IAPDefinitionId packId in packIds) {
            string productId = IapBase.GetProductID(packId);
            if (_unprocessedProductIds.Contains(productId)) {
                var shopData = RemoteConfigBase.instance.GetShopRemoteData(packId.ToString());
                if (shopData != null) {
                    _unprocessedProductIds.Remove(productId);
                    Configuration.UpdateDiamond(shopData.value, CurrencyEarnSource.IAP.ToString(), CurrencyEarnSource.iap_shop.ToString());
                    isRewarded = true;

                    // tracking
                    EconomyTrackingEvents.TrackIAPShopPurchase(
                        EconomyTrackingEvents.IAPShopPurchaseState.Success,
                        LOCATION_NAME.pending_purchase,
                        productId,
                        IapBase.GetPriceCode(productId),
                        shopData.value,
                        IapBase.GetPriceString(productId));
                    
                    RevenueCatPurchases.instance.TrackPurchaseAirFlux(packId);
                }
            }
        }
        
        //advanced pack (in shop)
        string advancedPackId = IapBase.GetProductID(IAPDefinitionId.advanced_pack);
        if (_unprocessedProductIds.Contains(advancedPackId)) {
            AdvancedPackShopItem.ProcessCompletedPendingPurchase();
            _unprocessedProductIds.Remove(advancedPackId);
            isRewarded = true;
        }

        //master pack (in shop)
        string masterPackId = IapBase.GetProductID(IAPDefinitionId.master_pack);
        if (_unprocessedProductIds.Contains(masterPackId)) {
            MasterPackShopItem.ProcessCompletedPendingPurchase();
            _unprocessedProductIds.Remove(advancedPackId);
            isRewarded = true;
        }

        if (isRewarded) {
            SaveUnprocessedProducts();
            if (RemoteConfigBase.instance.IaaSegmentation_IsEnablePopup) {
                IaaSegmentation.data.isHighValueFromIAP = true;
                IaaSegmentation.SetUserValue(UserValue.high_value);
            }
            
            if (!Util.IsGamePlaying()) {
                Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("PENDING_PAYMENT_COMPLETED"));
            }
        }
    }

    #region Check condition

    public static bool IsSupportedPendingHandler(IAPDefinitionId id) {
        return _supportedDefinitionIds.Contains(id);
    }

    
    /// <summary>
    /// Dùng để kiểm tra xem đã init lịch sử giao dịch của user hay chưa
    /// </summary>
    /// <returns></returns>
    public static bool IsSyncedPurchases() {
        return PlayerPrefs.HasKey(PlayerPrefsKey.rc_SyncPurchases);
    }
    
    public static bool HavePurchasedTransactions() {
        return PlayerPrefs.HasKey(PURCHASED_LIST_KEY);
    }
    
    #endregion


    #region Save & Load

    public static void LoadPurchasesData() {
        if (_isLoaded) {
            return;
        }

        if (PlayerPrefs.HasKey(PURCHASED_LIST_KEY)) {
            _purchasedTransactionIds = EncryptHelper.DecryptList(PlayerPrefs.GetString(PURCHASED_LIST_KEY));
        }

        if (PlayerPrefs.HasKey(UNPROCESSED_LIST_KEY)) {
            _unprocessedProductIds = EncryptHelper.DecryptList(PlayerPrefs.GetString(UNPROCESSED_LIST_KEY));
        }
        
        _purchasedTransactionIds ??= new List<string>();
        _unprocessedProductIds ??= new List<string>();
        _isLoaded = true;
    }
    
    private static void SavePurchasedTransactions() {
        if (_purchasedTransactionIds == null) {
            return;
        }
        PlayerPrefs.SetString(PURCHASED_LIST_KEY, EncryptHelper.EncryptList(_purchasedTransactionIds));
    }
    
    private static void SaveUnprocessedProducts() {
        if (_unprocessedProductIds == null) {
            return;
        }
        PlayerPrefs.SetString(UNPROCESSED_LIST_KEY, EncryptHelper.EncryptList(_unprocessedProductIds));
    }
    

    /// <summary>
    /// Cập nhật list những giao dịch non-sub đã thành công
    /// Gọi ở những thời điểm:
    ///  + khi lần đầu lấy được lịch sử giao dịch
    ///  + khi có một giao dịch thành công
    /// </summary>
    /// <param name="transactions"></param>
    public static void ForceUpdatePurchasedList(List<Purchases.StoreTransaction> transactions) {
        if (transactions == null) {
            return;
        }

        if (!_isLoaded) {
            LoadPurchasesData();
        }
        
        _purchasedTransactionIds = new List<string>();
        foreach (Purchases.StoreTransaction transaction in transactions) {
            if (string.IsNullOrEmpty(transaction.TransactionIdentifier)) {
                continue;
            }
            _purchasedTransactionIds.Add(transaction.TransactionIdentifier);
        }
        
        SavePurchasedTransactions();
    }

    #endregion


    #region Process Transactions
    
    /// <summary>
    /// Chuyển transaction đã thanh toán sau khi pending vào list chờ xử lý trả thưởng
    /// </summary>
    /// <param name="productId"></param>
    /// <param name="transactionId"></param>
    public static void TryAddToUnprocessedList(string productId, string transactionId) {
        if (!_isLoaded) {
            LoadPurchasesData();
        }

        // product_id có thể null do lỗi RevenueCat, return để xử lý ở game session sau
        if (string.IsNullOrEmpty(productId)) {
            return;
        }

        if (CheckTransactionSuccessAfterPending(transactionId)) {
            // thêm giao dịch vào list đã thanh toán và chuyển product sang list đợi trả thưởng
            _purchasedTransactionIds.Add(transactionId);
            SavePurchasedTransactions();

            if (!_unprocessedProductIds.Contains(productId)) {
                _unprocessedProductIds.Add(productId);
                SaveUnprocessedProducts();
            }
        }
    }

    
    /// <summary>
    /// Xóa product id khỏi list sau khi đã xử lý trả thưởng xong
    /// </summary>
    /// <param name="productIds"></param>
    public static void RemoveUnprocessedPurchase(params string[] productIds) {
        if (!_isLoaded) {
            LoadPurchasesData();
        }

        bool isNeedUpdateSavedData = false;
        foreach (string productId in productIds) {
            if (_unprocessedProductIds.Contains(productId)) {
                _unprocessedProductIds.Remove(productId);
                isNeedUpdateSavedData = true;
            }
        }

        if (isNeedUpdateSavedData) {
            SaveUnprocessedProducts();
        }
    }
    
    #endregion

    
    #region Checking
    
    /// <summary>
    /// Kiểm tra xem transaction đã thanh toán của user có phải giao dịch pending không dựa vào các giao dịch thành công
    /// đã ghi nhận trước đó. List giao dịch thành công được lưu lại mỗi khi:
    /// + thanh toán thành công
    /// + có giao dịch pending
    /// </summary>
    /// <param name="transactionId"></param>
    /// <returns></returns>
    private static bool CheckTransactionSuccessAfterPending([NotNull] string transactionId) {
        if (_purchasedTransactionIds == null) {
            return false;
        }
        
        return !string.IsNullOrEmpty(transactionId) && !_purchasedTransactionIds.Contains(transactionId);
    }

    
    /// <summary>
    /// Kiểm tra product id có đang trong list chờ trả thưởng hay không
    /// </summary>
    /// <param name="productId"></param>
    /// <returns></returns>
    public static bool ContainsUnprocessed(string productId) {
        if (!_isLoaded) {
            LoadPurchasesData();
        }
        
        return _unprocessedProductIds != null && _unprocessedProductIds.Contains(productId);
    }

    
    public static bool ContainsUnprocessed(params string[] productIds) {
        if (!_isLoaded) {
            LoadPurchasesData();
        }
        
        foreach (string productId in productIds) {
            if (_unprocessedProductIds.Contains(productId)) {
                return true;
            }
        }

        return false;
    }

    #endregion

    #region Log

    private static string LogList(List<string> list, string name) {
        StringBuilder sb = new();
        sb.AppendLine($"[IAP] List: {name}");
        sb.Append("{ ");
        bool isFist = true;
        if (list != null) {
            foreach (string s in list) {
                if (isFist) {
                    isFist = false;
                } else {
                    sb.Append(", ");
                }

                sb.Append(s);
            }
        }

        sb.Append(" }");
        Debug.LogWarning(sb.ToString());
        return sb.ToString();
    }
    
    
    public static string Log(string title = null) {
        if (!Configuration.isAdmin) {
            return string.Empty;
        }

        StringBuilder sb = new();
        Debug.LogError($"[IAP] Pending: {title}" +
                        $"\n - {PURCHASED_LIST_KEY}: \n\t{PlayerPrefs.GetString(PURCHASED_LIST_KEY)}" +
                        $"\n - {UNPROCESSED_LIST_KEY}: \n\t{PlayerPrefs.GetString(UNPROCESSED_LIST_KEY)}");
        sb.AppendLine(LogList(_purchasedTransactionIds, "Purchased transactions"));
        sb.AppendLine(LogList(_unprocessedProductIds, "Unprocessed product IDs"));
        sb.AppendLine($"Available Products Count: {RevenueCatPurchases.instance.availableProductCount}");
        return sb.ToString();
    }

    #endregion
}

public static class EncryptHelper {
    private static readonly string key = "SW53YXZlLlRpbGVzSG9wLkVuY3J5cHQ="; // 32 characters for AES 256-bit key
    
    // Helper class to wrap List<string> for JsonUtility
    [Serializable]
    private class StringListWrapper {
        public List<string> Items;
    }


    // Convert List<string> to JSON and encrypt it
    public static string EncryptList(List<string> list) {
        string json = JsonUtility.ToJson(new StringListWrapper { Items = list });
        return Encrypt(json);
    }

    
    // Decrypt string and convert back to List<string>
    public static List<string> DecryptList(string encryptedString) {
        string decryptedJson = Decrypt(encryptedString);
        var listWrapper = new StringListWrapper();
        JsonUtility.FromJsonOverwrite(decryptedJson, listWrapper);
        return listWrapper.Items;
    }
    
    
    // Encrypt a string using AES
    public static string Encrypt(string plainText) {
        using (Aes aesAlg = Aes.Create())
        {
            aesAlg.Key = Encoding.UTF8.GetBytes(key);
            aesAlg.IV = new byte[16]; // Using a zero IV for simplicity; consider using a random IV in production.

            ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

            byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
            byte[] encryptedBytes = encryptor.TransformFinalBlock(plainBytes, 0, plainBytes.Length);

            return Convert.ToBase64String(encryptedBytes);
        }
    }

    
    // Decrypt a string using AES
    public static string Decrypt(string encryptedText) {
        using (Aes aesAlg = Aes.Create())
        {
            aesAlg.Key = Encoding.UTF8.GetBytes(key);
            aesAlg.IV = new byte[16]; // Using a zero IV for simplicity; consider using a random IV in production.

            ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

            byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
            byte[] decryptedBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

            return Encoding.UTF8.GetString(decryptedBytes);
        }
    }
}