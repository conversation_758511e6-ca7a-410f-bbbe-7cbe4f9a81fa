#if UNITY_PURCHASING
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Purchasing;
using UnityEngine.Purchasing.Security;

// Placing the Purchaser class in the CompleteProject namespace allows it to interact with ScoreManager,
// one of the existing Survival Shooter scripts.
//namespace CompleteProject
//{
// Deriving the Purchaser class from IStoreListener enables it to receive messages from Unity Purchasing.
//using UnityEditor;

public class InAppPurchaser : FastSingleton<InAppPurchaser>, IStoreListener {
    private static IStoreController _mStoreController;

    // The Unity Purchasing system.
    private static IExtensionProvider _mStoreExtensionProvider;
    // The store-specific Purchasing subsystems.

    // Product identifiers for all products capable of being purchased:
    // "convenience" general identifiers for use with Purchasing, and their store-specific identifier
    // counterparts for use with and outside of Unity Purchasing. Define store-specific identifiers
    // also on each platform's publisher dashboard (iTunes Connect, Google Play Developer Console, etc.)

    // General product identifiers for the consumable, non-consumable, and subscription products.
    // Use these handles in the code to reference which product to purchase. Also use these values
    // when defining the Product Identifiers on the store. Except, for illustration purposes, the
    // kProductIDSubscription - it has custom Apple and Google identifiers. We declare their store-
    // specific mapping to Unity Purchasing's AddProduct, below.

    const string PriceStringPrefix = "PRICE_STRING";
    const string TitleStringPrefix = "TITLE_STRING";

    Dictionary<IAPDefinitionId, KeyValuePair<ProductType, IDs>> _productIDs =
        new Dictionary<IAPDefinitionId, KeyValuePair<ProductType, IDs>>();

    ConfigurationBuilder _builder;
    private bool         _isWaitingPurchase = false;

    public void InitializePurchasing() {

        // If we have already connected to Purchasing ...
        if (IsInitialized()) {
            // ... we are done here.
            return;
        }

        // Create a builder, first passing in a suite of Unity provided stores.
        _builder = ConfigurationBuilder.Instance(StandardPurchasingModule.Instance());

        // Add a product to sell / restore by way of its identifier, associating the general identifier
        // with its store-specific identifiers.
        foreach (var item in _productIDs) {
            _builder.AddProduct(item.Key.ToString(), item.Value.Key, item.Value.Value);
        }

        // Kick off the remainder of the set-up with an asynchrounous call, passing the configuration 
        // and this class' instance. Expect a response either in OnInitialized or OnInitializeFailed.
        UnityPurchasing.Initialize(this, _builder);
    }

    public void AddProduct(IAPDefinitionId iapId, ProductType type, string playStoreName, string appStoreName) {
        var ids = new IDs() {
            { playStoreName, GooglePlay.Name },
            { appStoreName, AppleAppStore.Name }
        };
        _productIDs.Add(iapId, new KeyValuePair<ProductType, IDs>(type, ids));
    }

    private bool IsInitialized() {
        // Only say we are initialized if both the Purchasing references are set.
        return _mStoreController != null && _mStoreExtensionProvider != null;
    }

    public bool Contains(IAPDefinitionId definitionId) {
        foreach (var item in _productIDs) {
            if (item.Key.Equals(definitionId))
                return true;
        }

        return false;
    }
    public void BuyProduct(IAPDefinitionId package) {
        if (Configuration.instance.isDebug) {
            if (package == IAPDefinitionId.subscription_week || package == IAPDefinitionId.subscription_month ||
                package == IAPDefinitionId.subscription_year) {
                SubscriptionController.StartSubscription(true, true);
            } else {
                if (RemoteConfig.instance.isEnableUI3) {
                    ShopUI3Script.CompletedPurchase(package);
                } else {
                    Shop.CompletedPurchase(package);
                }
            }
        } else {
            Product product = GetProduct(package);
            if (product != null) {
                ShowLoading();
                _mStoreController.InitiatePurchase(product);
            }
        }
    }

    public string GetProductID(IAPDefinitionId package) {

        string pid = package.ToString();
        if (RemoteConfig.instance.Enable_SaleOffIAP) {
            string saleid = pid + "_sale";
            if (Enum.IsDefined(typeof(IAPDefinitionId), saleid)) {
                return saleid;
            }
        }

        return pid;
    }

    public string GetPriceString(IAPDefinitionId package) {
        Product p = GetProduct(package);
        if (p != null) {
            PlayerPrefs.SetString(PriceStringPrefix + package.ToString(), p.metadata.localizedPriceString);
            return p.metadata.localizedPriceString;
        } else {
            return PlayerPrefs.GetString(PriceStringPrefix + package.ToString(), null);
        }
    }

    public string GetProductName(IAPDefinitionId package) {
        Product p = GetProduct(package);
        if (p != null) {
            PlayerPrefs.SetString(TitleStringPrefix + package.ToString(), p.metadata.localizedTitle);
            return p.metadata.localizedTitle;
        } else {
            return PlayerPrefs.GetString(TitleStringPrefix + package.ToString(), null);
        }
    }

    public Product GetProduct(IAPDefinitionId package) {
        string productId = GetProductID(package);
        // If Purchasing has been initialized ...
        if (IsInitialized()) {
            // ... look up the Product reference with the general product identifier and the Purchasing 
            // system's products collection.
            Product product = _mStoreController.products.WithID(productId);
            // If the look up found a product for this device's store and that product is ready to be sold ... 
            if (product != null && product.availableToPurchase) {
                return product;
            }
        }
        // Otherwise ...
        else {
            // ... report the fact Purchasing has not succeeded initializing yet. Consider waiting longer or 
            // retrying initiailization.
            Debug.Log("BuyProductID FAIL. Not initialized.");
        }

        return null;
    }

    public IAPDefinitionId GetDefinitionId(string package) {
        package = package.Replace("_sale", "");
        if (Enum.IsDefined(typeof(IAPDefinitionId), package)) {
            return (IAPDefinitionId) Enum.Parse(typeof(IAPDefinitionId), package);
        } else {
            CustomException.Fire("GetDefinitionId", "Invalid package:" + package);
            return IAPDefinitionId.none;
        }
    }

    // Restore purchases previously made by this customer. Some platforms automatically restore purchases, like Google.
    // Apple currently requires explicit purchase restoration for IAP, conditionally displaying a password prompt.
    public void RestorePurchases(Action<bool> callback) {
        if (RemoteConfig.instance.enableRevenueCat) {
            callback?.Invoke(false);
            return;
        }

        // If Purchasing has not yet been set up ...
        var mPopup = Util.ShowMessage("", 0, false);
        mPopup.animate = PopUpAnimate.AUTO_CLOSE;
        mPopup.autoCloseTime = 2f;

        if (!IsInitialized()) {
            // ... report the situation and stop restoring. Consider either waiting longer, or retrying initialization.
            if (mPopup != null)
                mPopup.UpdateMsg(LocalizationManager.instance.GetLocalizedValue("RP_ERROR"));
            callback?.Invoke(false);
            return;
        }

        // If we are running on an Apple device ... 
        if (Application.platform == RuntimePlatform.IPhonePlayer || Application.platform == RuntimePlatform.OSXPlayer) {
            // ... begin restoring purchases
            if (mPopup != null)
                mPopup.UpdateMsg(LocalizationManager.instance.GetLocalizedValue("RP_START"));

            // Fetch the Apple store-specific subsystem.
            var apple = _mStoreExtensionProvider.GetExtension<IAppleExtensions>();
            // Begin the asynchronous process of restoring purchases. Expect a confirmation response in 
            // the Action<bool> below, and ProcessPurchase if there are previously purchased products to restore.
            apple.RestoreTransactions((result) => {
                // The first phase of restoration. If no more responses are received on ProcessPurchase then 
                // no purchases are available to be restored.
                if (mPopup != null) {
                    mPopup.UpdateMsg(LocalizationManager.instance.GetLocalizedValue("RP_COMPLETED"));
                }
                callback?.Invoke(true);
            });
        }
        // Otherwise ...
        else {
            // We are not running on an Apple device. No work is necessary to restore purchases.
            if (mPopup != null)
                mPopup.UpdateMsg(LocalizationManager.instance.GetLocalizedValue("RP_ERROR"));
            callback?.Invoke(false);
        }
    }

    //
    // --- IStoreListener
    //

    public void OnInitialized(IStoreController controller, IExtensionProvider extensions) {
        // Purchasing has succeeded initializing. Collect our Purchasing references.
        Debug.Log("IAP OnInitialized: PASS");
        // Overall Purchasing system, configured with products for this application.
        _mStoreController = controller;
        // Store specific subsystem, for accessing device-specific store features.
        _mStoreExtensionProvider = extensions;

        if (RemoteConfig.instance.enableRevenueCat) {
            return;
        } else {
            SubPackageManager.UpdatePrices();
        }

        foreach (var item in controller.products.all) {
            if (item.availableToPurchase) {
                if (item.definition.type == ProductType.Subscription) {
                    SubscriptionController.UpdateSubscription(item);
                }
            }
        }
        IapBase.InitCompleted();
    }

    public void OnInitializeFailed(InitializationFailureReason error) {
        // Purchasing set-up has not succeeded. Check error for reason. Consider sharing this reason with the user.
        Debug.Log("IAP OnInitializeFailed InitializationFailureReason:" + error);
        //Helper.OpenMessagePopup (MessageText.Error, error.ToString ());
    }

    public PurchaseProcessingResult ProcessPurchase(PurchaseEventArgs args) {
        if (RemoteConfig.instance.enableRevenueCat) {
            return PurchaseProcessingResult.Complete;
        }

        HideLoading();
        var productID = args.purchasedProduct.definition.id;
        IAPDefinitionId iapID = GetDefinitionId(productID);

        bool validPurchase = true; // Presume valid for platforms with no R.V.

        // Unity IAP's validation logic is only included on these platforms.
#if UNITY_ANDROID || UNITY_IOS || UNITY_STANDALONE_OSX
        // Prepare the validator with the secrets we prepared in the Editor
        // obfuscation window.
        var validator = new CrossPlatformValidator(GooglePlayTangle.Data(), AppleTangle.Data(), Application.identifier);

        try {
            // On Google Play, result has a single product ID.
            // On Apple stores, receipts contain multiple products.
            var result = validator.Validate(args.purchasedProduct.receipt);
            // For informational purposes, we list the receipt(s)
            Debug.Log("Receipt is valid. Contents:");
            foreach (IPurchaseReceipt productReceipt in result) {
                Debug.Log(productReceipt.productID);
                Debug.Log(productReceipt.purchaseDate);
                Debug.Log(productReceipt.transactionID);
            }
        } catch (IAPSecurityException) {
            Debug.Log("Invalid receipt, not unlocking content");
            validPurchase = false;
        }
#endif

        if (validPurchase) {
            if (iapID == IAPDefinitionId.subscription_week || iapID == IAPDefinitionId.subscription_month ||
                iapID == IAPDefinitionId.subscription_year ) {
                SubscriptionController.UpdateSubscription(args.purchasedProduct, iapID);
            } else {
                Shop.CompletedPurchase(iapID);
            }
        }

        // Return a flag indicating whether this product has completely been received, or if the application needs 
        // to be reminded of this purchase at next app launch. Use PurchaseProcessingResult.Pending when still 
        // saving purchased products to the cloud, and when that save is delayed. 
        return PurchaseProcessingResult.Complete;
    }

    public static string GetPurchaseToken(string receipt) {

#if UNITY_ANDROID || UNITY_IOS || UNITY_STANDALONE_OSX
        var validator = new CrossPlatformValidator(GooglePlayTangle.Data(), AppleTangle.Data(), Application.identifier);
        try {
            var result = validator.Validate(receipt);
            foreach (IPurchaseReceipt productReceipt in result) {
                Debug.Log(productReceipt.productID);
                Debug.Log(productReceipt.purchaseDate);
                Debug.Log(productReceipt.transactionID);

                GooglePlayReceipt google = productReceipt as GooglePlayReceipt;
                if (null != google) {
                    // This is Google's Order ID.
                    // Note that it is null when testing in the sandbox
                    // because Google's sandbox does not provide Order IDs.
                    Debug.Log(google.transactionID);
                    Debug.Log(google.purchaseState);
                    Debug.Log(google.purchaseToken);
                    return google.purchaseToken;
                } else {
                    Debug.LogError("GooglePlayReceipt is null");
                }
                /*
                AppleInAppPurchaseReceipt apple = productReceipt as AppleInAppPurchaseReceipt;
                if (null != apple) {
                    Debug.Log(apple.originalTransactionIdentifier);
                    Debug.Log(apple.subscriptionExpirationDate);
                    Debug.Log(apple.cancellationDate);
                    Debug.Log(apple.quantity);
                }
                */
            }
        } catch (IAPSecurityException) {
            Debug.Log("Invalid receipt, not unlocking content");
        }
#endif
        return null;
    }
    public static bool checkIfProductIsAvailableForSubscriptionManager(string receipt) {
        var receipt_wrapper = (Dictionary<string, object>) MiniJson.JsonDecode(receipt);
        if (!receipt_wrapper.ContainsKey("Store") || !receipt_wrapper.ContainsKey("Payload")) {
            Debug.Log("The product receipt does not contain enough information");
            return false;
        }

        var store = (string) receipt_wrapper["Store"];
        var payload = (string) receipt_wrapper["Payload"];

        if (payload != null) {
            switch (store) {
                case GooglePlay.Name: {
                    var payload_wrapper = (Dictionary<string, object>) MiniJson.JsonDecode(payload);
                    if (!payload_wrapper.ContainsKey("json")) {
                        Debug.Log(
                            "The product receipt does not contain enough information, the 'json' field is missing");
                        return false;
                    }

                    var original_json_payload_wrapper =
                        (Dictionary<string, object>) MiniJson.JsonDecode((string) payload_wrapper["json"]);
                    if (original_json_payload_wrapper == null ||
                        !original_json_payload_wrapper.ContainsKey("developerPayload")) {
                        Debug.Log(
                            "The product receipt does not contain enough information, the 'developerPayload' field is missing");
                        return false;
                    }

                    var developerPayloadJSON = (string) original_json_payload_wrapper["developerPayload"];
                    var developerPayload_wrapper =
                        (Dictionary<string, object>) MiniJson.JsonDecode(developerPayloadJSON);
                    if (developerPayload_wrapper == null || !developerPayload_wrapper.ContainsKey("is_free_trial") ||
                        !developerPayload_wrapper.ContainsKey("has_introductory_price_trial")) {
                        Debug.Log(
                            "The product receipt does not contain enough information, the product is not purchased using 1.19 or later");
                        return false;
                    }

                    return true;
                }

                case AppleAppStore.Name:
                case AmazonApps.Name:
                case MacAppStore.Name: {
                    return true;
                }

                default: {
                    return false;
                }
            }
        }

        return false;
    }

    public void OnPurchaseFailed(Product product, PurchaseFailureReason failureReason) {
        // A product purchase attempt did not succeed. Check failureReason for more detail. Consider sharing 
        // this reason with the user to guide their troubleshooting actions.
        //Util.ShowMessage($"OnPurchaseFailed: FAIL. Product: '{product.definition.storeSpecificId}', PurchaseFailureReason: {failureReason}");
        //Helper.OpenMessagePopup (MessageText.Error, failureReason.ToString ());
        HideLoading();
    }

    private void OnApplicationPause(bool pauseStatus) {
        if (_isWaitingPurchase && !pauseStatus) {
            HideLoading();
        }
    }

    void ShowLoading() {
        if (Application.platform == RuntimePlatform.IPhonePlayer) {
            SceneFader.instance.ShowOverlay();
            _isWaitingPurchase = true;
        }
    }

    void HideLoading() {
        if (Application.platform == RuntimePlatform.IPhonePlayer) {
            SceneFader.instance.HideOverlay();
            _isWaitingPurchase = false;
        }
    }
    
}
//}
#endif