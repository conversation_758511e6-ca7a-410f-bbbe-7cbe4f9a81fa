using System.Text.RegularExpressions;

/// <summary>
/// Appslyer Event Config: Firebase Regex Definitions + Appflyers Event Definitions
/// </summary>
struct AfEventConfig {
    public Regex fbEventName; // Regex of firebase event name definition
    public Regex fbParamName; // Regex of firebase param name definition
    public Regex fbParamValue;// Regex of firebase param value definition
    public string afEventName;// Firebase event name will be converted to afEventName
    public string afParamName;// Firebase param name will be converted to afParamName
    public string afParamValue;// Firebase param value will be converted to afParamValue
    /// <summary>
    /// Set up firebase event config 
    /// </summary>
    /// <param name="cfg"></param>
    public void SetFbConfig(string cfg) {
        var list = SplitConfig(cfg);
        if (list.Length >= 1) {
            fbEventName = ConvertToRegex(list[0]);
        }

        if (list.Length >= 2) {
            fbParamName = ConvertToRegex(list[1]);
        }

        if (list.Length == 3) {
            fbParamValue = ConvertToRegex(list[2]);
        }
    }
    /// <summary>
    /// Set up appsflyer event config 
    /// </summary>
    /// <param name="cfg"></param>
    public void SetAfConfig(string cfg) {
        var list = SplitConfig(cfg);
        if (list.Length >= 1) {
            afEventName = ConvertAfName(list[0]);
        }

        if (list.Length >= 2) {
            afParamName = ConvertAfName(list[1]);
        }

        if (list.Length == 3) {
            afParamValue = ConvertAfName(list[2]);
        }
    }
    /// <summary>
    /// Split config string to array by SplitChar
    /// </summary>
    /// <param name="cfg"></param>
    /// <returns></returns>
    string[] SplitConfig(string cfg) {
        return cfg.Split(AfEventProcess.SplitChar);
    }

    /// <summary>
    /// Convert string config to regex for comparing
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    Regex ConvertToRegex(string str) {
        return new Regex(@"^" + str.Replace(AfEventProcess.NumberSign, "([0-9]*)")
                             .Replace(AfEventProcess.TextSign, "(.*)") + "$");
    }

    /// <summary>
    /// Convert raw string config to appsflyer event name standard, prepare for replacement
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    string ConvertAfName(string str) {
        return str.Replace(AfEventProcess.NumberSign, AfEventProcess.ReplaceSign)
            .Replace(AfEventProcess.TextSign, AfEventProcess.ReplaceSign);
    }
}