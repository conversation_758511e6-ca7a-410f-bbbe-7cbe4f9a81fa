using System;
using UnityEngine;

public class LocalizedToggle : MonoBehaviour {
    public string MatchLanguageID;
    public bool IsShowOnMatch = true;
    
    void Start()
    {
        if (LocalizationManager.GetLanguageID().Equals(MatchLanguageID, StringComparison.Ordinal)) {
            gameObject.SetActive(IsShowOnMatch);
        }
        else {
            gameObject.SetActive(!IsShowOnMatch);
        }
    }
}
