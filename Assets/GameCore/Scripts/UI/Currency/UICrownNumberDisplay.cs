using TMPro;
using UnityEngine;

public class UICrownNumberDisplay : MonoBehaviour {
    [SerializeField] private TextMeshProUGUI txtCrown;

    private void OnEnable() {
        RefreshText();
        Configuration.instance.OnSaveEarnedCrowns += RefreshText;
    }

    private void OnDisable() {
        if (Configuration.instance == null) {
            return;
        }
        Configuration.instance.OnSaveEarnedCrowns -= RefreshText;
    }

    private void RefreshText() {
        txtCrown.text = $"<sprite=0> {Configuration.instance.GetCurrentCrowns()}";
    }
}
