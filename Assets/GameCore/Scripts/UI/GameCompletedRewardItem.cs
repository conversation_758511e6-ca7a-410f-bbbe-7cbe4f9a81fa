using UnityEngine;
using UnityEngine.UI;

public class GameCompletedRewardItem : MonoBehaviour
{
	[SerializeField] private Image icon;
	[SerializeField] private Text txtAmount;
	private int _amount;

	public int Amount => _amount;
	public Sprite Sprite=>icon.sprite;
	public Text Text => txtAmount;

	public void Config(Sprite sprite, int count) {
		_amount = count;
		icon.sprite = sprite;
		txtAmount.text = $"x{count}";
	}

	public void Config(int count) {
		_amount = count;
		txtAmount.text = $"x{count}";
	}
}
