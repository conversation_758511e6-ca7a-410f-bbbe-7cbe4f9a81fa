using System;
using System.Collections;
using GameCore.Scripts;
using TilesHop.Cores.UserProgression;
using TilesHop.LiveEvent;
using UnityEngine;
using UnityEngine.UI;

public class UIController : MonoBehaviour, GameSubscriber {
    //main ui controller
    //all fields should be linked in editor
    public static UIController ui;

    public   GameUI          gameui;
    internal GameCompletedUI gameover;
    internal ContinueUI      continueUI;
    internal EndlessIterationUI endlessIterationUI;
    public   PauseUI         pauseui;

    [SerializeField] private Text       songName;
    public                   GameObject songDetailButton;

    [SerializeField] private GameObject    pauseButton;
    [SerializeField] private CanvasGroup[] bgCanvasGroup;
    [SerializeField] private Text          txtTapAnHold;
    private RemoteConfig remoteConfig => RemoteConfig.instance;

    public BlackBGTooltip bgToolTip;

    [Header("Diamond Fly Effect")] 
    [SerializeField] private DiamondFlyEffect diamondFlyEffect;


    private FeverModeController _feverMode;

    #region Unity Method

    private void Awake() {
        ui = this;
    }

    private IEnumerator Start() {
        GameController.instance.addSubcriber(this);
        if (RemoteConfig.instance.Enable_SongInfo &&
            !SongManager.instance.IsLocalSong(NotesManager.instance.song.path)) {
            songDetailButton.SetActive(true);

            ((RectTransform) songDetailButton.transform).anchoredPosition = new Vector2(-30f, -10f);
        } else {
            songDetailButton.SetActive(false);
        }

        songName.text = NotesManager.instance.SongName;

        InitPopupEndlessIteration();
        InitPopupGameCompleted();
        UpdateTextTapHold();

        yield return null;

        InitPopupShopInGame();
        yield return null;

        InitPopupSelectThemes();
        yield return null;

        InitPopupTrySkin();
        yield return null;

        InitPopupTrySkinReward();
        yield return null;

        InitPopupCharacterPreview();
        yield return null;

        if (RemoteConfigBase.instance.UserProgression_TutoRewardLocatedAtResult) {
            InitPopupTutorialReward();

        InitFeverMode();
        }
    }

    #endregion

    #region GameSubscriber

    public void gamePrepare() {
        if (!GameController.instance.isUpdateSensitive) {
            if (GameController.instance.Score > 0) {
                ShowSongName(false);
                TopBar.instance.ToggleScore(true);
            } else {
                ShowSongName(true);
                TopBar.instance.ToggleScore(false);
            }

            TopBar.instance.ToggleDiamondContainer(true);
        }

        TopBar.instance.ToggleButtons(true);
        pauseButton.SetActive(false);
        GameItems.instance.ShowFxWin(false);
        if (_feverMode) {
            _feverMode.gameObject.SetActive(!GameController.instance.IsTutorial);
            _feverMode.StartCounting();
        }
    }

    public void gameOver() {
        pauseButton.SetActive(false);
        if (_feverMode) {
            _feverMode.Interrupt();
        }
    }

    public void gameStart() {
        if (GameController.instance.IsNewTutorialGamePlay) {
            gameui.GameStart(GameController.instance.timePlay,
                GameController.instance._tutorialGamePlayConfig.Phase1Duration);
        } else {
            gameui.GameStart();
        }

        if (GameController.enableEndless) {
            songName.text = NotesManager.instance.SongName;
        }

        ShowSongName(false);

        TopBar.instance.GameStart();

        if (Configuration.instance.Admin_IsShowedPause) {
            pauseButton.SetActive(true);
        }
    }

    public void gameContinue() {
        //gameui.GameContinue();
        TopBar.instance.GameContinue();
    }

    public void Resume() {
        if (pauseui.gameObject.activeSelf) {
            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.gameplay);
            pauseui.gameObject.SetActive(false);
            GameController.SetGameState(true);
            SuperpoweredSDK.instance.SetPosition(GameController.instance.pauseTime);
            SuperpoweredSDK.instance.SetVolume(1f);
            SuperpoweredSDK.instance.SmoothPlay(0.5f);

            if (Configuration.instance.Admin_IsShowedPause) {
                pauseButton.SetActive(true);
            }
        }
    }

    #endregion

    #region popup continue

    public void InitPopupContinue() {
        if (continueUI != null) {
            Destroy(continueUI.gameObject);
        }

        string resourcePath = "";

        if (remoteConfig.Economy_IsEnable) {
            resourcePath = GetPopupContinueForEconomy();
        } else {
            resourcePath = GetPopupContinueForNormal();
        }

        ContinueUI prefab = Resources.Load<ContinueUI>(resourcePath);
        if (prefab != null) {
            continueUI = Instantiate(prefab, transform, false);
            continueUI.SetActive(false);
        } else {
            Logger.LogError("[InitPopupContinue] cannot load resource");
        }
    }

    public void ShowContinueUI(Action<bool> callback = null) {
        continueUI.ShowPopup(callback);
    }

    public void ContinueUIEndWaitPay() {
        continueUI.EndWaitPay();
    }

    public void ContinueUICancel() {
        continueUI.Cancel();
    }

    public bool IsShowingContinueUI() {
        return continueUI != null && continueUI.gameObject.activeSelf;
    }

	public void ShowContinueBooster() {
		var prefab = Resources.Load<GameObject>(ResourcesPath.Popups_ContinueUI_ByBooster);
		if (prefab != null) {
			ContinueUI popup = Instantiate(prefab, transform, false).GetComponent<ContinueUI>();
            popup.SetActive(false);
            popup.ShowPopup(continueUI.Callback);
		} else {
			Logger.LogError("[PopupContinueBooster] cannot load resource");
		}
	}

    public void ShowReviveOfferWithBoosters() {
        Util.ShowPopUp("HereToHelp");
    }

	#endregion

	public float WidthOfSongName() {
        return Util.GetWithOfText(songName);
    }

    public void UpdateTextTapHold(string text = null) {
        text = text ?? "REPLAY_TEXT";
        string localizedValue = LocalizationManager.instance.GetLocalizedValue(text);
        txtTapAnHold.text = localizedValue;
        LocalizationManager.instance.UpdateFont(txtTapAnHold);
        LocalizationManager.instance.UpdateFont(songName);
    }

    public void ShowSongDetails() {
        SoundManager.PlayGameButton();
        GameObject popUp = Util.ShowPopUp(PopupName.SongDetails);
        popUp.GetComponent<SongDetails>().SetSong(NotesManager.instance.song);
    }

    public void ShowGameSetting() {
        SoundManager.PlayGameButton();
        Util.ShowPopUp(PopupName.GameSettingHome);
    }

    /// <summary>
    /// TH-3449: show setting panel which has an "exit AP" button
    /// </summary>
    public void ShowGameSetttingV2() {
        SoundManager.PlayGameButton();
        Util.ShowPopUp(PopupName.GameSettingAP);
    }

    public void PauseClick() {
        pauseButton.SetActive(false);
        if (!NotesManager.instance.song.IsLocalSong() && GameSessionManager.Instance != null) {
            GameSessionManager.Instance.PauseGame(Reason.PlayerPause);
        }

        GameController.instance.Pause();
    }

    private void InitPopupGameCompleted() {
        GameObject prefab;
        if (UserProgressionController.EnableFeature) {
            prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUserProgression);
        } else if (remoteConfig.Economy_IsEnable && remoteConfig.ResultScreen_UseEconomyResult) {
            prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedEconomy);
        } else {
            prefab = GetPopupGameCompletedNormal();
        }

        if (prefab) {
            gameover = Instantiate(prefab, transform, false).GetComponent<GameCompletedUI>();
            gameover.gameObject.SetActive(false);
        } else {
            Logger.LogError("[InitPopupGameCompleted] cannot load resource");
        }
    }

   

    public void ShowSongName(bool isShow) {
        songName.gameObject.SetActive(isShow);
    }

    #region Shop In Game

    private ShopScript _shopScript;
    public  ShopScript shopScript => _shopScript;

    private void InitPopupShopInGame() {
        if (ThemeManager.IsEnableShopInGame()) {
            string prefabName;
            if (RemoteConfigBase.instance.ShopBall_UseCategoryLayout) {
                prefabName = $"{ResourcesPath.Popups_ShopInGame}V{remoteConfig.ShopBall_LayoutStyleIndex}";
            } else if (RemoteConfigBase.instance.ShopBall_VerticleScroll) {
                prefabName = ResourcesPath.Popups_ShopInGame_Vert;
            } else {
                prefabName = ResourcesPath.Popups_ShopInGame;
            }
            
            GameObject prefab = Resources.Load<GameObject>(prefabName);
            if (prefab) {
                GameObject shopPopup = Instantiate(prefab, transform, false);
                shopPopup.SetActive(false);
                _shopScript = shopPopup.GetComponent<ShopScript>();
            }
        }
    }

    public void ShowShopInGame(ShopScript.PreviousLocation previousLocation, bool isWatchedAds, Action<bool> onClose) {
        if (_shopScript != null) {
            _shopScript.ShowPopup(Direction.Bottom, previousLocation, -1, isWatchedAds, onClose);

            if (previousLocation != ShopScript.PreviousLocation.ingame) {
                ShowCharacterPreview(true);
                ShopScript.instance.previewCharacter = _characterPreview;
            } else {
                ShopScript.instance.previewCharacter = null;
            }

            gameui.ShowHandUI(false);

            // tracking
            string location = previousLocation switch {
                ShopScript.PreviousLocation.offer_skin or ShopScript.PreviousLocation.unlocked_skin => "try_skin_offer",
                ShopScript.PreviousLocation.ingame => "AP",
                _ => previousLocation.ToString()
            };

            if (CheckLocationParamV2(previousLocation)) {
                location = Util.BuildString('_', location, "v2");
            }

            AnalyticHelper.ShopBallEntryPointClick(location);
        } else {
            onClose?.Invoke(false);
        }
    }

    /// <summary cref="https://amanotesjsc.atlassian.net/wiki/spaces/THE/pages/3673752425/Ball+Shop+Improvement+for+BH+-+UIUX+Improvement">
    /// Check để thêm hậu tố "v2" sau location nhằm phân biệt phiên bản của shop ball entry point 
    /// </summary>
    /// <param name="previousLocation"></param>
    /// <returns></returns>
    private bool CheckLocationParamV2(ShopScript.PreviousLocation previousLocation) {
        return remoteConfig.ShopBall_EntryPointV2_IsEnable && previousLocation is ShopScript.PreviousLocation.offer_skin
            or ShopScript.PreviousLocation.unlocked_skin or ShopScript.PreviousLocation.home
            or ShopScript.PreviousLocation.ingame;
    }

    public bool IsShopShowed() {
        return _shopScript != null && _shopScript.IsShowed();

    }

    /// <summary>
    /// Shop opened tính từ lúc enable đến khi click button close
    /// không tính thời gian diễn anim close
    /// </summary>
    /// <returns></returns>
    public bool CheckShopOpened() {
        return _shopScript != null && _shopScript.IsShopOpened;
    }

    #endregion

    #region Trying New Skin

    private TrySkinPopUp  _trySkinPopUp;
    private TrySkinReward _trySkinReward;

    private void InitPopupTrySkin() {
        if (!TrySkinFeature.IsActive) {
            return;
        }

        TrySkinPopUp prefab = Resources.Load<TrySkinPopUp>(ResourcesPath.Popups_TrySkin);
        if (prefab) {
            _trySkinPopUp = Instantiate(prefab, transform, false);
            _trySkinPopUp.gameObject.SetActive(false);
        }
    }

    private void InitPopupTrySkinReward() {
        if (!TrySkinFeature.IsActive) {
            return;
        }

        TrySkinReward prefab = Resources.Load<TrySkinReward>(ResourcesPath.Popups_TrySkinReward);
        if (prefab) {
            _trySkinReward = Instantiate(prefab, transform, false);
            _trySkinReward.gameObject.SetActive(false);
        }
    }

    public DisableCallbackUI ShowTrySkinPopUp(Action<bool> onClose) {
        if (_trySkinPopUp != null) {
            _trySkinPopUp.ShowPopup(onClose);
            return _trySkinPopUp;
        } else {
            onClose?.Invoke(false);
            return null;
        }
    }

    public void ShowTrySkinRewardPopUp(Action<bool> onClose) {
        if (_trySkinReward != null) {
            _trySkinReward.Show(onClose);
        }
    }

    public void UpdateTimeStartTrySkin() {
        if (_trySkinPopUp != null) {
            _trySkinPopUp.UpdateTimeStartTrySkin();
        }
    }

    private CharacterPreviewPopup _characterPreview;

    private void InitPopupCharacterPreview() {
        if (TrySkinFeature.IsActive) {
            GameObject prefab = Resources.Load<GameObject>(ResourcesPath.Popups_CharacterPreview);
            if (prefab) {
                GameObject surveyPopup = Instantiate(prefab, transform, false);
                surveyPopup.SetActive(false);
                _characterPreview = surveyPopup.GetComponent<CharacterPreviewPopup>();

                if (_shopScript != null) {
                    int siblingIndex = _shopScript.transform.GetSiblingIndex();
                    _characterPreview.transform.SetSiblingIndex(siblingIndex);
                }
            }
        }
    }

    public void ShowCharacterPreview(bool isShow) {
        if (_characterPreview != null) {
            _characterPreview.gameObject.SetActive(isShow);
        }
    }

    public void UpdateCharacterPreview(int clickBallID) {
        if (_characterPreview != null && _characterPreview.gameObject.activeSelf) {
            _characterPreview.UpdateCharacterPreview(clickBallID);
        }
    }

    #endregion

    #region Tutorial Reward

    private TutorialReward _tutorialReward;

    private void InitPopupTutorialReward() {
        if (TutorialReward.CanGetReward()) {
            GameObject prefab = Resources.Load<GameObject>(ResourcesPath.Popups_TutorialReward);
            if (prefab) {
                GameObject surveyPopup = Instantiate(prefab, transform, false);
                surveyPopup.SetActive(false);
                _tutorialReward = surveyPopup.GetComponent<TutorialReward>();
            }
        }
    }

    public void ShowPopupTutorialReward() {
        if (_tutorialReward != null) {
            PlayerPrefs.SetInt(CONFIG_STRING.CanGetTutorialReward, 1);
            PlayerPrefs.SetInt(CONFIG_STRING.TutorialRewardPopUpAppear, 1);
            _tutorialReward.gameObject.SetActive(true);
        }
    }

    public bool IsPopupTutorialRewardShowed() {
        if (_tutorialReward != null && _tutorialReward.gameObject.activeSelf) {
            return true;
        }

        return false;
    }

    #endregion

    #region Diamond Fly Effect

    public void AddDiamondsV2(Vector3 from, Vector3 to, int amount, Action complete) {
        if (!diamondFlyEffect.gameObject.activeSelf) {
            diamondFlyEffect.gameObject.SetActive(true);
        }

        diamondFlyEffect.AddDiamondsV2(from, to, amount, complete);
    }

    #endregion

    public bool InFever() {
        return _feverMode && _feverMode.inFeverState;
    }
    private void InitFeverMode() {
        if (!remoteConfig.FeverMode_IsEnable)
            return;

        GameObject prefab = ResourcesManager.Load<GameObject>(ResourcesPath.FeverMode);
        if (prefab) {
            GameObject popup = Instantiate(prefab,transform,false);
            popup.transform.SetSiblingIndex(pauseui.transform.GetSiblingIndex());
            _feverMode = popup.GetComponent<FeverModeController>();
        }
    }
    public void AddTokens(Vector3 from, int amount, Action complete) {
        if (!diamondFlyEffect.gameObject.activeSelf) {
            diamondFlyEffect.gameObject.SetActive(true);
        }

        var eventItem = LiveEventManager.instance.GetCurrentLiveEvent();
        if (eventItem != null) {
            diamondFlyEffect.AddTokens(from, amount, eventItem.eventConfig.IconToken, null, complete, 2f, true, .8f);
        }
    }

    #region Select Themes

    private SelectThemes _selectThemes;

    private void InitPopupSelectThemes() {
        if (ThemeManager.IsEnableSelectThemes() && _selectThemes == null) {
            GameObject prefab = Resources.Load<GameObject>(ResourcesPath.Popups_SelectThemes);
            if (prefab) {
                GameObject selectTheme = Instantiate(prefab, transform, false);
                selectTheme.SetActive(false);
                _selectThemes = selectTheme.GetComponent<SelectThemes>();
            }
        }
    }

    public void ShowSelectThemes(Direction direction) {
        if (_selectThemes == null) {
            InitPopupSelectThemes();
        }

        if (_selectThemes != null) {
            _selectThemes.ShowPopup(direction);
            gameui.ShowHandUI(false);
        }
    }

    public bool IsSelectThemeShowed() {
        return _selectThemes != null && _selectThemes.IsShowed();
    }

    #endregion
    
    private GameObject GetPopupGameCompletedNormal() {
        GameObject prefab;
        int styleIndex = remoteConfig.ResultScreen_StyleIndex;
        int countShowComplete = PlayerPrefs.GetInt(PlayerPrefsKey.CountResultScreen, 0);
        bool useNewFlow = remoteConfig.ResultScreen_NewFlowVariant > 0;

        if (useNewFlow && styleIndex == 2) {
            if (countShowComplete < 2) {
                styleIndex = 2;
                countShowComplete += 1;
                PlayerPrefs.SetInt(PlayerPrefsKey.CountResultScreen, countShowComplete);
            } else {
                switch (remoteConfig.ResultScreen_NewFlowVariant) {
                    case 1:
                        styleIndex = 3;
                        break;
                    case 2:
                        styleIndex = 4;
                        break;
                    case 3:
                        styleIndex = 5;
                        break;
                }
            }
        }

        switch (styleIndex) {
			case 7:
				prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUI7);
				break;
			case 6:
				prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUI6);
				break;
			case 5:
                prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUI5);
                break;
            case 4:
                prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUI4);
                break;

            case 3:
                prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUI3);
                break;

            case 2:
                prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUI2);
                break;

            default:
                prefab = ResourcesManager.Load<GameObject>(ResourcesPath.Popups_GameCompletedUI1);
                break;
        }

        return prefab;
    }
    
    private string GetPopupContinueForNormal() {
        string resourcePath;
        if (remoteConfig.ReviveScreen_UsingGems) {
            resourcePath = ResourcesPath.Popups_ContinueUI_ByGems;
        } else {
            resourcePath = ResourcesPath.Popups_ContinueUI;
        }

        return resourcePath;
    }

    private string GetPopupContinueForEconomy() {
        if (remoteConfig.ReviveScreen_UsingGems) {
            return ResourcesPath.Popups_ContinueUI_ByGems;
        }
        return ResourcesPath.Popups_ContinueUI;
    }

    public void ShowCanvasGroup(bool isShow) {
        foreach (CanvasGroup canvasGroup in bgCanvasGroup) {
            canvasGroup.gameObject.SetActive(isShow);
        }
    }

    private void InitPopupEndlessIteration() {
        if (endlessIterationUI) {
            return;
        }

        var prefab = Resources.Load<EndlessIterationUI>("Popups/EndlessIteration");

        if (prefab) {
            endlessIterationUI = Instantiate(prefab, transform, false);
            endlessIterationUI.gameObject.SetActive(false);
        } else {
			Logger.LogError("[InitPopupEndlessIteration] cannot load resource");
		}
	}

    public void ShowEndlessIteration() {
        endlessIterationUI.Active();
    }

    public bool IsEndlessIterationActive() {
        return endlessIterationUI != null && endlessIterationUI.gameObject.activeInHierarchy;
    }
}