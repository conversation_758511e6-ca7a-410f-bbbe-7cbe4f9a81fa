using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class HandEffect : MonoBehaviour
{
	public ParticleSystem left;
	public ParticleSystem right;
	private ParticleSystem.EmissionModule leftEmi;
	private ParticleSystem.EmissionModule rightEmi;

	private Transform cachedTransform;
	// Use this for initialization
	float x;

	void Start ()
	{
		leftEmi = left.emission;
		rightEmi = right.emission;
		cachedTransform = this.transform;
	}
	
	// Update is called once per frame
	void Update ()
	{
		if (left.isPlaying) {
			if (cachedTransform.position.x - x > 0) {
				leftEmi.rateOverTime = 0;	
				rightEmi.rateOverTime = 2.5f;	
			} else {
				leftEmi.rateOverTime = 2.5f;	
				rightEmi.rateOverTime = 0;	
			}
			x = cachedTransform.position.x;
		}
	}
}
