using System;
using System.Collections;
using System.Collections.Generic;
using GameCore;
using TilesHop.ChallengeOldUser;
using UnityEngine;
using UnityEngine.UI;

public class TrySkinReward : MonoBehaviour, IEscapeHandler {
    [SerializeField] private Text                   txtCongratulation;
    [SerializeField] private CharacterManagerScript characterManagerScript;
    [SerializeField] private BallManagerScript      ballManagerScript;
    [SerializeField] private Camera                 playerCamera;
    [SerializeField] private Button                 shopButton;
    [SerializeField] private Button                 playButton;
    [SerializeField] private Text                   txtPlayNow;
    [SerializeField] private Button                 closeButton;
    [SerializeField] private GameObject             ballBeltWay;
    [SerializeField] private RarityImageListenter   rarityPodium;

    private int          _ballIdReward;
    private Action<bool> _onClose;

    private string _location = LOCATION_NAME.tryskin_reward.ToString();

    private bool _canPlayNow;
    private void Start() {
        playButton.onClick.AddListener(OnPlayButtonClicked);
        shopButton.onClick.AddListener(OnShopButtonClicked);
        closeButton.onClick.AddListener(Close);
    }

    private void OnEnable() {
        EventEscapeManager.Push(this);
        _canPlayNow = !ChallengeOldUserController.IsPlayingChallenge;
        txtPlayNow.text = LocalizationManager.instance.GetLocalizedValue(_canPlayNow?"PLAY_ON" : "CLAIM");
    }

    private void OnDisable() {
        if (playerCamera != null) {
            playerCamera.gameObject.SetActive(false);
        }

        EventEscapeManager.Pop(this);
    }


    #region Escape Handlers

    public bool CanHandleEventBack() {
        return gameObject.activeInHierarchy;
    }

    public bool HandleEventBack() {
        Close();
        return true;
    }

    #endregion

    public void Show(Action<bool> onClose) {
        this._onClose = onClose;
        this.gameObject.SetActive(true);
        _ballIdReward = Ball.b.tryBallId;

        var ballConfig = BallManager.instance.GetBallConfig(_ballIdReward);
        txtCongratulation.text =
            string.Format(LocalizationManager.instance.GetLocalizedValue("YOU_JUST_GET"), ballConfig.name);
        Init(_ballIdReward);
        UpdateVfx(_ballIdReward);

        playerCamera.gameObject.SetActive(true);
        Camera main = Camera.main;
        if (main != null) {
            playerCamera.transform.SetParent(main.transform, false);
            main.cullingMask = main.cullingMask & ~playerCamera.cullingMask;
        }
    }

    private void UpdateVfx(int ballId) {
        int ballRarity = (int)BallManager.instance.GetBallConfig(ballId).rarity;
        //Show màu tile theo độ hiếm
        rarityPodium.SetRarity((BallRarity)ballRarity);
    }

    private void Init(int ballId) {
        bool isCharacter = BallManager.instance.IsHumanBehaviour(ballId);
        //characters
        characterManagerScript.SetActive(isCharacter);
        if (isCharacter) {
            characterManagerScript.Init(ballId, characterManagerScript.gameObject.layer);
            characterManagerScript.Rotate(180, 0.1f);
        }

        ballManagerScript.gameObject.SetActive(!isCharacter);
        if (!isCharacter) {
            ballManagerScript.Init(ballId, ballManagerScript.gameObject.layer);
        }

        ballBeltWay.SetActive(ballId == BallManager.ballSaturn);
    }


    private void OnPlayButtonClicked() {
        //Need save data
        GameController.instance.SaveScoreAndDiamond();
        if (_canPlayNow) {
            Song songUnlockedNotPlay = SongManager.instance.GetSongUnlockedNotPlay();
            if (songUnlockedNotPlay != null) {
                SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.ball_equip);
                Util.GoToGamePlay(songUnlockedNotPlay, location: _location, isSongClick: false);
            } else {
                Logger.EditorLogError("TrySkin Reward", $"NULL song by GetSongUnlockedNotPlay");
                this.Close();
            }
        } else {
            this.Close();
        }
    }

    private void OnShopButtonClicked() {
        UIController.ui.ShowShopInGame(ShopScript.PreviousLocation.unlocked_skin, true, _onClose);
        gameObject.SetActive(false);

        int tryBallId = GameItems.instance.tryBallId;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { "item_id", tryBallId },
            { "skin_type", BallManager.itemsHuman.Contains(tryBallId) ? "character" : "ball" },
            { "action", "browse" },
        };
        AnalyticHelper.FireEvent(FIRE_EVENT.skin_unlocked_popup, param);
    }

    public void Close() {
        this._onClose?.Invoke(true);
        gameObject.SetActive(false);

        int tryBallId = GameItems.instance.tryBallId;
        Dictionary<string, object> param = new Dictionary<string, object> {
            { "item_id", tryBallId },
            { "skin_type", BallManager.itemsHuman.Contains(tryBallId) ? "character" : "ball" },
            { "action", "close" },
        };
        AnalyticHelper.FireEvent(FIRE_EVENT.skin_unlocked_popup, param);
    }
}