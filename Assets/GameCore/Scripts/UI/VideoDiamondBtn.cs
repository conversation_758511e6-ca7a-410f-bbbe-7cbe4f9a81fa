using System.Collections;
using TilesHop.Cores.Hybrid;
using UnityEngine;
using UnityEngine.UI;

public class VideoDiamondBtn : MonoBehaviour {
    public Text diamondBonusText;

    private string _text;

    public enum Type {
        DEFAULT,
        FreeVideo,
        ShopUI2
    }

    public Type type = Type.DEFAULT;

    // Use this for initialization
    private void Awake() {
        _text = LocalizationManager.instance.GetLocalizedValue("X_DIAMONDS");
        switch (type) {
            case Type.DEFAULT:
                diamondBonusText.text = Util.BuildString(string.Empty, "+", Configuration.instance.diamondsVideoBonus);
                break;

            case Type.ShopUI2:
                diamondBonusText.text = Util.BuildString(string.Empty,
                    RemoteConfigBase.instance.ShopUI_Revamp_Enable ? string.Empty : "+", "<b>",
                    Configuration.instance.diamondsVideoBonus, "</b> ",
                    LocalizationManager.instance.GetLocalizedValue("DIAMONDS").ToLower());
                break;

            default:
                diamondBonusText.text = LocalizationManager.instance.GetLocalizedValue("X_DIAMONDS")
                    .Replace("10", Configuration.instance.diamondsVideoBonus.ToString());
                break;
        }

        LocalizationManager.instance.UpdateFont(diamondBonusText);
    }

    private IEnumerator UpdateText(int from, int to) {
        float t = 0;
        float time = 1f;

        while (t < time) {
            t += Time.deltaTime;

            switch (type) {
                case Type.DEFAULT:
                    diamondBonusText.text = $"+{(int) Mathf.Lerp(from, to, t / time)}";
                    break;

                case Type.ShopUI2:
                    diamondBonusText.text =
                        $"+<b>{(int) Mathf.Lerp(from, to, t / time)}</b> {LocalizationManager.instance.GetLocalizedValue("DIAMONDS").ToLower()}";
                    break;

                default:
                    diamondBonusText.text = _text.Replace("10", ((int) Mathf.Lerp(from, to, t / time)).ToString());
                    break;
            }

            yield return null;
        }
    }

    public void IncreaseDiamondsVideoBonus() {
		if (Configuration.instance == null || RemoteConfig.instance == null || this == null) {
			return;
		}

        if (Configuration.instance.diamondsVideoBonus == 0) {
            Configuration.instance.diamondsVideoBonus = RemoteConfig.instance.Diamond_Video_1st;
        }

        int nextValue = Configuration.instance.diamondsVideoBonus + RemoteConfig.instance.Diamond_Video_Step;
        if (nextValue > RemoteConfig.instance.Diamond_Video_Max) {
            nextValue = RemoteConfig.instance.Diamond_Video_1st;
        }

		try {
            StartCoroutine(UpdateText(Configuration.instance.diamondsVideoBonus, nextValue));
		} catch (System.Exception e) {
			CustomException.Fire("[VideoDiamondBtn]", e.Message);
		}
        Configuration.instance.diamondsVideoBonus = nextValue;
    }

    public void ShowAds_Click() {
        if (Shop.instance != null && Shop.instance.IsNeedMore()) {
            AnalyticHelper.NeedMore(TRACK_NAME.popup_shop_watch);
        }

        SoundManager.PlayGameButton();
        AdsManager.instance.ShowRewardAds(VIDEOREWARD.currency, null, location: TRACK_LOCATION.ad_shop, true,
            OnRewardVideoCompleted);
    }

    private void OnRewardVideoCompleted(bool isComplete) {
        if (isComplete) {
            AirfluxTracker.TrackRewardAdsImpression(Configuration.instance.diamondsVideoBonus);
            Configuration.UpdateDiamond(Configuration.instance.diamondsVideoBonus, CurrencyEarnSource.VIDEO.ToString(),
                TRACK_LOCATION.ad_shop);
            IncreaseDiamondsVideoBonus();
            MissionCenter.DoMission(MissionType.get_free_gem_in_shop_x_times);
            
        }
    }
}