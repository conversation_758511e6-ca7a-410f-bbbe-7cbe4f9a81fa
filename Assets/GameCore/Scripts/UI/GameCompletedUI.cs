using System.Collections;
using System.Collections.Generic;
using System;
using System.Globalization;
using UnityEngine;
using UnityEngine.UI;
using System.IO;
using DG.Tweening;
using GameCore;
using GameCore.EndlessOffer;
using GameCore.LiveEvent.MysteryDoor;
using SongManagers;
using TilesHop.LiveEvent;
using Music.ACM;
using TilesHop.ChallengeOldUser;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Hybrid.TripleOffer;
using TilesHop.Cores.Pooling;
using TilesHop.GameCore;
using TilesHop.GameCore.StarsJourney;
using TilesHop.LiveEvent.GalaxyQuest;
using TilesHop.Cores.IAASegmentation;
using TilesHop.EconomySystem;
using TilesHop.LiveEvent.MilestoneEvent;
using UnityEngine.EventSystems;

[Serializable]
public struct GameResultImage {
    public Image  img;
    public Sprite win;
    public Sprite over;
}

public class GameCompletedUI : MonoBehaviour, IEscapeHandler {
    #region Fields

    [SerializeField] protected int styleIndex;
    //public

    [Space] [SerializeField] protected ScrollRect scrollRect;

    public                     Text       songName;
    public                     GameObject winningEffect;
    public                     GameObject diamondIcon;
    public                     Text       diamondText;
    public                     Image      tokenIcon;
    [SerializeField] private   Text       tokenText;
    [SerializeField] private   Text       rankText;
    public                     Image      favoriteImage;
    public                     Animation  favoriteBtnAnim;
    public                     GameObject vipButton;
    [SerializeField] protected Button     btnMultiplyDiamond;
    [SerializeField] private   Text       txtMultiplyDiamond;

    protected virtual GameObject objMultiplyDiamond {
        get { return btnMultiplyDiamond.gameObject; }
    }

    [SerializeField] protected RectTransform  bottomMenuInfinity;
    [SerializeField] protected UIButtonReplay btnReplay;
    public                     RectTransform  scrollContainer;

    [Space] [SerializeField] protected GameObject scoresGroup;
    [SerializeField]         protected Text       scoreText;
    [SerializeField]         protected Text       bestScoreText;

    [Space] [SerializeField] protected GameObject starsGroup;
    [SerializeField]         protected GameObject star1;
    [SerializeField]         protected GameObject star2;
    [SerializeField]         protected GameObject star3;

    [SerializeField] protected CrownGroupController miniCrownGroup;

    [Space] [SerializeField] protected GameResultImage[] gameResultImages;

    //Bottom Menu Infinity
    [SerializeField] protected Button     btnNext;
    [SerializeField] private   Button     btnShare;
    [SerializeField] private   GameObject iconBack;

    [SerializeField] protected GameObject objButtonHome;
    [SerializeField] protected GameObject objButtonHomeBig;

    //New tutorial
    [Space] [SerializeField] protected GameObject    objBottomMenuTutorial;
    [SerializeField]         private   Button        btnPlayNextTutorial;
    [SerializeField]         private   Button        btnHomeTutorial;
    [SerializeField]         private   RectTransform rtfSongRecommend;
    [SerializeField]         private   Text          txtNextSong;
    [SerializeField]         protected Button        btnSeeMore;

    [SerializeField] private Transform  tfScrollView;
    [SerializeField] private RectMask2D rm2dScrollView;

    [SerializeField] protected DiamondFlyEffect diamondFlyEffect;
    [SerializeField] protected ResultOnboarding resultOnboarding;

    [Space] [Header("Completed Label")] [SerializeField]
    protected GameObject completedLabel;

    [Space] [Header("Star/Crown")] [SerializeField]
    protected Sprite spCrownEndless;

    [SerializeField] protected Sprite  spCrownDarkEndless;
    [SerializeField] protected Image[] imgLightStar;
    [SerializeField] protected Image[] imgDarkStar;

    [Space]

    //~~~~~~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~~~~~~
    public Action showInterstitial;

    protected int score           = 0;
    protected int highestScore    = 0;
    protected int oldHighestScore = 0;

    protected int   oldStars;
    private   float scoreUpdate       = 0;
    protected float diamondUpdate     = 0;
    private   int   _diamondCollected = 0;
    private   int   retry             = 0;
    protected Song  currentSong;
    private   Song  nextSong;
    private   bool  _savedScoreAndDiamond;
    private   bool  _showInfinity;
    protected bool  isNewBest;
    protected bool  _needCheckChallengeOldUser;

    protected List<SongItem> cachedItemList = new List<SongItem>();
    protected bool           first3Stars;

    //private
    protected int _freeVipTrialBtnOrder   = 0;
    protected int _removeAdsBtnOrder      = 1;
    protected int _unlockAllSongsBtnOrder = 2;
    protected int stars => GameController.instance.stars;

    public bool isCompletedSong {
        get {
            if (GameController.enableEndless) {
                if (ChallengeMode.IsActive) {
                    return stars == 3 || (!GameController.instance.isAlreadyCompletedSong &&
                                          ChallengeMode.IsAcceptFromNormal);
                }

                return false;
            }

            return stars == 3;
        }
    }

    protected bool isGameWin => stars >= 3;

    protected bool   isShowToken = false;
    protected int    earnAmount;
    protected Sprite iconToken;

    protected bool       isShowDiamond;
    protected int        maxRecommendSong;
    protected NextButton nextButtonHandler;

    private int _lastStreak;

    public virtual Transform SongGroupTransform => scrollContainer;
    protected int starBonus = 0;

    protected RemoteConfig remoteConfig => RemoteConfig.instance;
    protected LiveEventManager liveEventManager => LiveEventManager.instance;

    protected bool canShowMultiplyDiamond {
        get {
            if (btnMultiplyDiamond == null) {
                return false;
            }

            // exist ads multiplier
            if (remoteConfig.ResultScreen_AdsMultiply < 2) {
                return false;
            }

            //TH-1556: Thêm condition cho việc show btnMultipleReward khi Inventory: <1,5 song gems
            if (Configuration.instance.GetDiamonds() >= remoteConfig.ResultScreen_AdsMinDiamondInStorage) {
                // khi lượng gem trong storage vẫn còn nhiều -> không show popup
                return false;
            }

            // got minimum required diamond
            if (_diamondCollected <= remoteConfig.ResultScreen_AdsMinDiamond) {
                return false;
            }

            // got maximum required diamond
            if (_diamondCollected >= remoteConfig.ResultScreen_AdsMaxDiamond) {
                return false;
            }

            if (remoteConfig.ResultScreen_AdsCapping > 0 &&
                Configuration.CountedMultiplierAds >= remoteConfig.ResultScreen_AdsCapping) {
                // exist capping /day. Nếu capping per day config <=0 => unlimited
                return false;
            }

            return true;
        }
    }

    protected int _starGroupSize = 100;

    // TH-1194: Hide the progress bar and diamond in tutorial game play -> CanShowStar = false;
    protected virtual bool CanShowStar => !(remoteConfig.Economy_IsEnable && NotesManager.instance.song.isTutorialSong);

    protected bool isEndless => GameController.enableEndless;

    protected bool NeedShowSongOnboarding {
        get {
            if (!remoteConfig.Onboarding_Tutorial_PreviewSong_Enable)
                return false;

            if (PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_PreviewSong_Appear, 0) != 0)
                return false;

            if (remoteConfig.Onboarding_Tutorial_PreviewSong_LevelAppear > Configuration.GetGameLevel())
                return false;

            return PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ResultDiamond, 0) == 1;
        }
    }

    [HideInInspector] public bool NeedShowDiamondOnboarding {
        get {
            if (!remoteConfig.Onboarding_Result_IntroGem_Enable)
                return false;

            if (PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ResultDiamond, 0) != 0)
                return false;

            return remoteConfig.Onboarding_Result_IntroGem_LevelAppear <= Configuration.GetGameLevel();
        }
    }

    [HideInInspector] public bool NeedShowUnlockSongByGemOnboarding {
        get {
            if (!remoteConfig.Onboarding_Result_IntroGem_Enable)
                return false;

            if (PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_UnlockSongByGem, 0) != 0)
                return false;

            return remoteConfig.Onboarding_Result_IntroGem_LevelAppear <= Configuration.GetGameLevel();
        }
    }

    [ReadOnly] public bool isShowDone;

    protected Coroutine coroutineAppear;
    private   Coroutine _coroutineBgMusic;

    [ReadOnly] public LOCATION_NAME location = LOCATION_NAME.result;

    #endregion

    #region Unity Methods

    protected virtual void Awake() {
        PreviewSongController.instanceSafe.Init();
        if (scrollRect) {
            scrollRect.decelerationRate = Mathf.Clamp01(1f - remoteConfig.Scroller_DeclearationRate);
        }

        if (btnNext != null) {
            btnNext.onClick.AddListener(PreBtnNextClick);
        }

        if (songName != null) {
            songName.text = NotesManager.instance.SongName;
        }

        if (scrollRect && (styleIndex == 1 || styleIndex == 3 || styleIndex == 4 || styleIndex == 7)) {
            maxRecommendSong = GetMaxRecommendSong();

            SongItem prefSongItem = SongItem.GetSongItemPrefab();
            prefSongItem.CreatePool(maxRecommendSong);

            for (int i = maxRecommendSong; i > 0; i--) {
                SongItem songItem = prefSongItem.Spawn();
                songItem.transform.SetParent(scrollContainer, false);
                songItem.OnPreviewSongClick += OnPreviewSongClick;
                cachedItemList.Add(songItem);
            }
        }

        //AB Test Update alpha for game result background
        if (remoteConfig.resultBgOpacity < 1) {
            if (gameResultImages.Length > 0) {
                gameResultImages[0].img.SetAlpha(remoteConfig.resultBgOpacity);
            }

            transform.Find("ParticleSystemBubble").gameObject.SetActive(false);
        }

        //if (remoteConfig.OnboardingFlow_IsEnable) 
        {
            if (btnPlayNextTutorial) {
                btnPlayNextTutorial.onClick.AddListener(BtnPlayNextTutorialOnClick);
            }

            if (btnHomeTutorial) {
                btnHomeTutorial.onClick.AddListener(Back);
            }

            if (btnSeeMore) {
                btnSeeMore.onClick.AddListener(BtnSeeMoreOnClick);
            }
        }

        DisableLiveEventToken();
        UpdateFontText();

        if (iconBack != null) {
            iconBack.SetActive(!remoteConfig.GameCompleteUI_ButtonHome_Enable);
        }
    }

    protected virtual IEnumerator Start() {
        yield return null;
    }

    protected virtual void OnEnable() {
        if (!_isInited) {
            return;
        }

        AnalyticHelper.instance.SetCurrentLocation(location);

        ChangeTopBar();
        EventEscapeManager.Push(this);
        if (!styleIndex.IsInListNumber(2, 3, 4)) {
            Util.CheckShowEconomyBoost(location.ToString());
        }

        TopBar.OnTokenClicked += TopBarOnOnTokenClicked;
        SubscriptionController.OnChange += SubscriptionControllerOnOnChange;
        SceneFader.OnLoadingScene += SceneFaderOnOnLoadingScene;
    }

    protected virtual void OnDisable() {
        if (!_isInited) {
            return;
        }

        SceneFader.OnLoadingScene -= SceneFaderOnOnLoadingScene;
        SubscriptionController.OnChange -= SubscriptionControllerOnOnChange;
        TopBar.OnTokenClicked -= TopBarOnOnTokenClicked;
        star1.SetActive(false);
        star2.SetActive(false);
        star3.SetActive(false);
        scrollContainer.SetAnchoredPosition3DPosY(0);

        if (completedLabel) {
            completedLabel.SetActive(false);
        }

        if (TopBar.instance != null) {
            TopBar.instance.SetParentDiamondContainer(TopBar.instance.transform.Find("Container").transform);
            TopBar.instance.SetActiveTokens(false);
        }

        SaveScoreAndDiamond(); // đảm bảo lưu score/diamond khi popup disable vì bất cứ lý do gì
        EventEscapeManager.Pop(this);

        SoundManager.StopResultBG();
        StopCoroutineAutoPreviewSong();
    }

    protected virtual void Update() {
        if (isShowDone) {
            if (_needCheckChallengeOldUser) {
                _needCheckChallengeOldUser = false;
                ChallengeOldUserController.instanceSafe.ShowPopupResultScreen(isGameWin);
            }
        }
    }

    #endregion

    protected virtual void UpdateFontText() {
        if (songName != null) {
            LocalizationManager.instance.UpdateFont(songName);
        }

        LocalizationManager.instance.UpdateFont(bestScoreText);
        LocalizationManager.instance.UpdateFont(diamondText);
        if (scoreText != null) {
            LocalizationManager.instance.UpdateFont(scoreText);
        }

        LocalizationManager.instance.UpdateFont(txtMultiplyDiamond);
        LocalizationManager.instance.UpdateFont(rankText);
        LocalizationManager.instance.UpdateFont(txtNextSong);
    }

    private void OnPreviewSongClick(Song obj) {
        StopCoroutineAutoPreviewSong();
    }

    protected virtual int GetMaxRecommendSong() {
        return remoteConfig.Recommend_RelatedGenreSongs + remoteConfig.Recommend_UnrelatedGenreSongs;
    }

    protected virtual bool IsFixedTopBar() {
        return remoteConfig.Economy_Topbar_IsFixed;
    }

    private void PreBtnNextClick() {
        if (TransitionInOut.isInstanced) {
            TransitionInOut.instance.TransitionOut(btnNext.transform.position, BtnNextClick);
        } else {
            BtnNextClick();
        }
    }

    private void BtnNextClick() {
        var song = nextButtonHandler?.nextSong ?? SongRecommendation.GetNextSong();
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Next);
        if (song.savedType == SONGTYPE.LOCK) {
            if (song.isDualUnlock) {
                NextSongByAd(song);
            } else {
                song.TryToUnLockSongByGem(result => {
                    if (result) {
                        AnalyticHelper.ScreenResult("SongUnlock_Diamond");
                        UIOverlay.instance.ShowVFXSubDiamond(song.diamonds, btnNext.transform.RootLocalPos(),
                            new Vector2(-55, 0), () => { PlaySong(SONG_PLAY_TYPE.next, song, true); });
                    } else {
                        NextSongByAd(song);
                    }
                });
            }
        } else if (Configuration.IsNoAllAds() || song.savedType == SONGTYPE.OPEN) {
            PlaySong(SONG_PLAY_TYPE.next, song, true);
        } else {
            NextSongByAd(song);
        }
    }

    private void NextSongByAd(Song song) {
        SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.next);
        AdsManager.instance.ShowRewardAds(VIDEOREWARD.song_unlock, song, location: this.location.ToString(), true,
            BtnNextClick_VideoCompleted);
    }

    private void AnimatePlaySong(Song song) {
        if (TransitionInOut.isInstanced && EventSystem.current.currentSelectedGameObject != null) {
            TransitionInOut.instance.TransitionOut(EventSystem.current.currentSelectedGameObject.transform.position,
                () => { PlaySong(SONG_PLAY_TYPE.recommended_result, song, true); });
        } else {
            PlaySong(SONG_PLAY_TYPE.recommended_result, song, true);
        }
    }

    private void PlaySong(SONG_PLAY_TYPE songPlayType, Song song, bool isFromClick) {
        SongLocationTracker.SetSongPlayType(songPlayType);
        Util.GoToGamePlay(song, location: this.location.ToString(), isFromClick);
    }

    private void BtnNextClick_VideoCompleted(bool isCompleted) {
        if (!isCompleted) {
            return;
        }

        var song = nextButtonHandler?.nextSong ?? SongRecommendation.GetNextSong();
        if (nextSong != null) {
            Configuration.instance.SetOpenSong(song, 0, true, SongUnlockType.ads);
            PlaySong(SONG_PLAY_TYPE.next, song, true);
        }

        AirfluxTracker.TrackRewardAdsImpression();
    }

    protected virtual void UpdateGameResultImage() {
        if (!remoteConfig.Enable_TwoResultScene) {
            return;
        }

        foreach (GameResultImage item in gameResultImages) {
            if (item.img != null) {
                item.img.sprite = isGameWin ? item.win : item.over;
            }
        }
    }

    protected bool _isInited = false;

    public void Active() {
        int countTemp = 0;
        try {
            if (this == null || gameObject == null || !GameController.CheckInstanced(true) || gameObject.activeSelf ||
                !RemoteConfigBase.isInstanced) {
                return;
            }

            AnalyticHelper.instance.SetCurrentLocation(location);
            ToggleScrollRect(false); //Disable scroll rect

            countTemp = 1;
            _isInited = true;
            isShowDone = false;
            gameObject.SetActive(true);

            countTemp = 2;
            GameController.instance.FireSongResult();
            if (BoosterManager.isInstanced) {
                BoosterManager.instanceSafe.ClearUIIngameBooster();
            }

            countTemp = 3;
            gameObject.transform.localPosition = Vector3.zero;
            countTemp = 5;
            _savedScoreAndDiamond = false;
            countTemp = 6;
            winningEffect.SetActive(false);

            countTemp = 7;
            UpdateData();

            if (remoteConfig.Hybrid_LoseGemsOnFailed && !GameController.enableEndless && stars < 3) {
                isShowDiamond = false;
            } else {
                isShowDiamond = GameController.instance.GetDiamond() > 0;
            }

            countTemp = 8;
            UpdateUI();

            PlayActiveSfx();

            countTemp = 10;
            _needCheckChallengeOldUser = ChallengeOldUserController.IsNeedCheckResult();

            if (PowerCubeManager.isActive) {
                _lastStreak = PowerCubeManager.GetCurrentStreak();
                PowerCubeManager.GameComplete(isGameWin);
            }

            MysteryDoorManager.TryToUnlock(GameController.instance.GetTotalStar());

            if (currentSong != null && !currentSong.isTutorialSong) {
                int perfectCount = GameController.instance != null ? GameController.instance.perfectCount : 0;
                if (perfectCount != 0) {
                    MissionCenter.DoMission(MissionType.get_x_perfect, perfectCount.ToString());
                }

                int numberTile = GameController.instance != null ? GameController.instance.GetCurrentExp() : 0;
                if (numberTile != 0) {
                    MissionCenter.DoMission(MissionType.get_x_tile, numberTile.ToString());
                }
            }

            GameController.TrackingCMResult();
        } catch (Exception e) {
            CustomException.Fire("[GameCompletedUI.Active]",
                $"{e.Message} => countTemp: {countTemp} => StackTrace: " + e.StackTrace);
        }
    }

    private void SceneFaderOnOnLoadingScene(bool isLoading, bool isSuccess) {
        if (isLoading) {
            //stop coroutine autoplay preview
            StopCoroutineAutoPreviewSong();
        }
    }

    private void StopCoroutineAutoPreviewSong() {
        //Logger.EditorLog("stop auto play preview last song");
        if (_coroutineBgMusic != null) {
            StopCoroutine(_coroutineBgMusic);
        }
    }

    private CanvasGroup _canvasGroupForLockUI;
    public bool isLocking => _canvasGroupForLockUI && !_canvasGroupForLockUI.interactable;

    protected virtual void UpdateUI() {
        UpdateGameResultImage();
        CheckToShowQuestPopups();
        UpdateStarCrown();

        if (miniCrownGroup != null) {
            miniCrownGroup.SetActive(false);
        }

        if (ChallengeMode.IsActive) {
            bool isFirstTimeComplete = !GameController.instance.isAlreadyCompletedSong;

            if (miniCrownGroup != null && ChallengeMode.IsAcceptFromNormal && GameController.instance.stars > 0 &&
                isFirstTimeComplete) {
                miniCrownGroup.SetActive(true);
                miniCrownGroup.LightUp(GameController.instance.stars);
            }

            starsGroup.SetActive(true);
            return;
        }

        if (!remoteConfig.Hybrid_UniqueStarIngame) {
            return;
        }

// the 3-star group is always displayed when the Challenge Mode is activated for the current playing song 
        if (GameController.instance.isAlreadyCompletedSong) {
            starsGroup.SetActive(false);
        } else {
            starsGroup.SetActive(true);
        }
    }

    private void UpdateStarCrown() {
        if (!isEndless || !remoteConfig.ResultScreen_IsShowCrownEndless) {
            return;
        }

        if (ChallengeMode.IsAcceptFromNormal && !GameController.instance.isAlreadyCompletedSong) {
            return;
        }

        foreach (Image image in imgLightStar) {
            image.sprite = spCrownEndless;
        }

        foreach (Image image in imgDarkStar) {
            image.sprite = spCrownDarkEndless;
        }
    }

    protected void CheckToShowQuestPopups() {
        var cansShowQuestPopup = GalaxyQuest.CheckCanShowPopup();
        if (cansShowQuestPopup) {
            DisableInteractable();
        }
    }

    protected IEnumerator IEShowGalaxyQuest() {
        bool isWinQuest = isGameWin || (ChallengeMode.IsAcceptFromNormal && NotesManager.instance.song.bestStar >= 3);
        GalaxyQuest.TriggerWinStreak(isWinQuest);

        while (GalaxyQuest.isInstanced && GalaxyQuest.instanceSafe.isShowingQuestPopups) {
            yield return null;
        }
    }

    protected IEnumerator IEShowMysteryDoor() {
        yield return MysteryDoorManager.IECheckAutoStartPopupAtResult();
    }

    protected void DisableInteractable() {
        if (!_canvasGroupForLockUI) {
            _canvasGroupForLockUI = UIController.ui.gameover.gameObject.AddComponent<CanvasGroup>();
        }

        _canvasGroupForLockUI.interactable = false;
    }

    protected void EnableInteractable() {
        if (!_canvasGroupForLockUI) {
            return;
        }

        _canvasGroupForLockUI.interactable = true;
    }

    protected virtual void PlayActiveSfx() {
    }

    protected virtual IEnumerator IEShowAnimAppear() {
        if (CanShowStar) {
            starsGroup.SetActive(true);
            if (styleIndex == 1 || styleIndex == 7) { //old
                MakeStarEffect();
            } else {
                if (isGameWin) {
                    winningEffect.SetActive(true);
                }
            }
        } else {
            // TH-1194: Hide the progress bar and diamond in tutorial game play
            starsGroup.SetActive(false);
        }

        StartCoroutine(ScoreEffect());

        if (isShowDiamond || isShowToken) { //wait to diamond fly to target
            if (isShowToken) {
                if (styleIndex == 7) {
                    yield return StartCoroutine(IEShowTokenEffect());
                } else {
                    yield return StartCoroutine(TokenEffect(earnAmount));
                }
            }

            if (isShowDiamond) {
                diamondUpdate = 0;
                yield return StartCoroutine(DiamondEffect(GameController.instance.GetDiamond(), true));
            }

            DOVirtual.DelayedCall(.6f, () => {
                SaveScoreAndDiamond(); // add diamond behind animation diamond
                OnAddDiamondDone();

                if (styleIndex == 1 || styleIndex == 7) {
                    ToggleScrollRect(true);
                }
            });
        } else {
            SaveScoreAndDiamond(); // add diamond behind animation diamond
            OnAddDiamondDone();

            if (styleIndex == 1 || styleIndex == 7) {
                ToggleScrollRect(true);
            }
        }

        if (TutorialReward.CanGetReward()) {
            UIController.ui.ShowPopupTutorialReward();
        }

        while (UIController.ui.IsPopupTutorialRewardShowed())
            yield return null;

        if (NeedShowSongOnboarding && firstSongItem != null) {
            resultOnboarding.EnablePreviewSong(firstSongItem.gameObject);
        } else if (NeedShowDiamondOnboarding) {
            StartCoroutine(TopBar.instance.ShowDiamondNotice());
        } else {
            ShowBuyNotice();
        }

        yield return null;
    }

    protected IEnumerator IEShowNoticePowerCube() {
        while (UIOverlay.instance.IsWaitingAdFsReward())
            yield return null;

        yield return YieldPool.GetWaitForSeconds(.5f);

        if (PowerCubeManager.isActive && !PowerCubeManager.instanceSafe.isLocked) {
            int currentStreak = PowerCubeManager.GetCurrentStreak();
            if (currentStreak != 0 || _lastStreak != 0) {
                yield return Util.ShowPopupAsync<UIPowerCubeStreakNotice>(PopupName.PowerCubeStreakNotice, null,
                    notice => {
                        notice.Show(_lastStreak, currentStreak);
                    });
            }
        }
    }

    protected virtual void MakeStarEffect() {
    }

    protected void CheckLiveEvent() {
        if (!gameObject || !gameObject.activeSelf) {
            return;
        }

        if (liveEventManager && liveEventManager.IsActiveEvent && liveEventManager.HaveNotice) {
            TopBar.instance.SetActiveTokenNotice(true);
        }
    }

    private void SubscriptionControllerOnOnChange(bool isBuySuccess) {
        if (isBuySuccess && this.gameObject.activeSelf) {
            RefreshRecommendedSongList();
        }
    }

    private void RefreshRecommendedSongList() {
        foreach (var songItem in cachedItemList) {
            songItem.song.UpdateData();
            songItem.SetSong(songItem.song.savedType, songItem.song, SONG_PLAY_TYPE.recommended_result,
                location: this.location.ToString(), songItem.song.ordering);
        }
    }

    private void TopBarOnOnTokenClicked() {
        SoundManager.PlayGameButton();
        if (liveEventManager.IsActiveEvent) {
            liveEventManager.IsGoEventTab = true;
        }

        Util.GoHome();
    }

    protected void SaveScoreAndDiamond() {
        if (_savedScoreAndDiamond) {
            return;
        }

        if (GameController.CheckInstanced(true)) {
            GameController.instance.SaveScoreAndDiamond();
        }

        _savedScoreAndDiamond = true;
    }

    protected virtual void OnAddDiamondDone() {
        // chờ khi add diamond,token xong rồi mới check boost
        Util.CheckShowEconomyBoost(location.ToString());
        CheckLiveEvent();
    }

    public void ShowBuyNotice() {
        if (NeedShowUnlockSongByGemOnboarding) {
            if (firstSongItem.type == SONGTYPE.LOCK)
                firstSongItem.ShowBuyNotice();
            else if (UIController.ui.bgToolTip.gameObject.activeSelf) {
                UIController.ui.bgToolTip.HideRequired();
            }
        }
    }

    public bool IsBuyNoticeEnable() {
        return firstSongItem != null && firstSongItem.buyNotice.activeSelf;
    }

    protected virtual void UpdateData() {
        isShowDone = true;
        //try {
        Ball.ResetGravity();
        GameController.instance.ToggleOverlay(false);
        TopBar.instance.ToggleScore(false);
        TopBar.instance.ToggleDiamondContainer(true);
        if (!remoteConfig.PlayNewSFX_Enable)
            SoundManager.instance.PlayGameCompleted();
        else {
            SoundManager.PlayResultBG();
            _coroutineBgMusic = StartCoroutine(IEShowPreviewSong());
        }

        resultOnboarding.RedDotCheck();

        // Set Best Score
        _diamondCollected = GameController.instance.GetDiamond();
        currentSong = NotesManager.instance.song;
        score = GameController.instance.Score;
        // Set Best Stars
        highestScore = Configuration.GetBestScore(currentSong.path);
        oldStars = Configuration.GetBestStars(currentSong.path);
        scoreUpdate = 0;
        diamondUpdate = 0;
        starBonus = 0;

        if (highestScore < score) {
            oldHighestScore = highestScore;
            highestScore = score;
            isNewBest = true;
        } else {
            isNewBest = false;
        }

        // Set Diamonds
        if (remoteConfig.NotesDifficult_IsEnable && remoteConfig.NotesDifficult_Diamond_InEnable) {
            GameController.instance.CalcDiamond();
        }

        if (rankText != null && rankText.gameObject.activeSelf) {
            long rank = 0;
            if (CoreUser.instance.user != null && CoreUser.instance.loadedFriend) {
                rank = CoreUser.instance.user.GetMyRank(Util.SongToBoardId(currentSong.path));
            }

            rankText.text = rank > 0 ? rank.ToString() : "--";
        }

        //update achievements
        if (GameController.instance.continueCount > 0) {
            PlayerPrefs.SetInt(CONFIG_STRING.Revive,
                PlayerPrefs.GetInt(CONFIG_STRING.Revive, 0) + GameController.instance.continueCount);
        } else {
            if (retry > 0) {
                int countRetry = PlayerPrefs.GetInt(CONFIG_STRING.Retry, 0) + 1;
                PlayerPrefs.SetInt(CONFIG_STRING.Retry, countRetry);
                Configuration.instance.userDataCached.Replay(countRetry);
            }
        }

        if (NotesManager.instance.song.isTutorialSong) {
            PlayerPrefs.SetInt(CONFIG_STRING.TutorialPerfect, GameController.instance.perfectCountMax);
        } else {
            int pNormal = PlayerPrefs.GetInt(CONFIG_STRING.NormalPerfect, 0);
            if (pNormal < GameController.instance.perfectCountMax) {
                PlayerPrefs.SetInt(CONFIG_STRING.NormalPerfect, GameController.instance.perfectCountMax);
            }
        }

        //update perfect
        int perfectCount = PlayerPrefs.GetInt(CONFIG_STRING.PerfectCount, 0);
        if (perfectCount < GameController.instance.perfectCountMax) {
            PlayerPrefs.SetInt(CONFIG_STRING.PerfectCount, GameController.instance.perfectCountMax);

            //Reached a new streak, should clear tutorial perfect count
            if (!NotesManager.instance.song.isTutorialSong) {
                PlayerPrefs.SetInt(CONFIG_STRING.TutorialPerfect, 0);
            }
        }

        //isShowToken = false;

        bool mysteryDoor = MysteryDoorManager.CanEarnDrill(GameController.enableEndless);
        if (!NotesManager.instance.song.isTutorialSong) {
            if (mysteryDoor || liveEventManager.CanShowTokenAtGameComplete) {
                // có live event
                int totalToken = mysteryDoor
                    ? MysteryDoorManager.currentDrill
                    : ((liveEventManager?.GetCurrentLiveEvent()?.progress.TokenAmount) ?? 0);
                earnAmount = GameController.instance.GetTokenEarnResult();
                //if (mysteryDoor && stars != 0 && !GameController.enableEndless) {
                //    earnAmount += MysteryDoorManager.instanceSafe.BonusDrill(stars);
                //}

                iconToken = mysteryDoor
                    ? MysteryDoorManager.iconItem
                    : liveEventManager?.GetCurrentLiveEvent()?.eventConfig?.IconToken;
                //if (earnAmount != 0) {
                //    ShowLiveEventToken(iconToken);
                //}

                TopBar.instance.SetActiveTokenNotice(false); // tạm tắt icon notice

                if (styleIndex != 7) {
                    TopBar.instance.ShowTokenInResult(iconToken, totalToken,
                        mysteryDoor || liveEventManager?.GetCurrentLiveEvent().progress != null);
                }
            }

            if (MilestoneEvent.isAvailable) {
                TopBar.instance.ShowOtherTokensInResult();
            }
        }

        if (styleIndex == 1 || styleIndex == 7) {
            if (remoteConfig.Hybrid_LoseGemsOnFailed && !GameController.enableEndless && stars < 3) {
                isShowDiamond = false;
            } else {
                isShowDiamond = GameController.instance.GetDiamond() > 0;
            }

            if (styleIndex == 1) {
                diamondIcon.gameObject.SetActive(isShowDiamond);
                diamondIcon.transform.parent.gameObject.SetActive(isShowDiamond || isShowToken);
            }
        }

        if (isShowToken && songName != null) {
            songName.transform.SetLocalY(-85); // khi có token -> cũng rời vị trí song name tụt xuống
        }

        if (styleIndex == 1) {
            float positionYScoreGroup = -270;
            if (!CanShowStar) {
                positionYScoreGroup += _starGroupSize;
            }

            scoresGroup.GetComponent<RectTransform>().SetAnchoredPosition3DPosY(positionYScoreGroup);
        }

        UpdateFavoriteButton();
        if (remoteConfig.OnboardingFlow_IsEnable && currentSong.isTutorialSong) {
            //don't show sub button
            if (vipButton) {
                vipButton.SetActive(false);
            }

            if (btnMultiplyDiamond) {
                objMultiplyDiamond.SetActive(false);
            }

            ProcessButtonTutorial();

            if (styleIndex == 1 || styleIndex == 7) {
                txtNextSong.gameObject.SetActive(true);
                float txtNextSongPositionY = -343;
                if (!CanShowStar) {
                    txtNextSongPositionY += _starGroupSize;
                }

                if (isShowDiamond || isShowToken) {
                    txtNextSongPositionY -= 57;
                }

                txtNextSong.rectTransform.SetAnchoredPosition3DPosY(txtNextSongPositionY);
            }
        } else {
            ShowSubscriptionButton();
            if (canShowMultiplyDiamond) {
                ShowMultiplyDiamondButton();
            } else if (btnMultiplyDiamond) {
                objMultiplyDiamond.SetActive(false);
            }

            if (objBottomMenuTutorial) {
                objBottomMenuTutorial.SetActive(false);
                if (txtNextSong) {
                    txtNextSong.gameObject.SetActive(false);
                }
            }
        }

        ShowRecommendNew();

        if (currentSong.isTutorialSong && !remoteConfig.OnboardingFlow_IsEnable && recommendedList.IsNullOrEmpty()) {
            ProcessButtonTutorial();
        }

        if (styleIndex == 1 || styleIndex == 7) {
            if (coroutineAppear != null) {
                StopCoroutine(coroutineAppear);
            }

            coroutineAppear = StartCoroutine(IEShowAnimAppear());
        }

        int modeUnlockByStar = remoteConfig.EndlessMode_UnlockByStar;
        first3Stars = stars >= modeUnlockByStar && oldStars < modeUnlockByStar &&
                      NotesManager.instance.song.GetSongTypeByMode(SONG_MODE.endless_mode) != SONGTYPE.OPEN;
        _showInfinity = (oldStars >= modeUnlockByStar || stars >= modeUnlockByStar) &&
                        Configuration.EnableEndlessModeByUser();

        if (HasNotificationEndlessMode() && (styleIndex == 1 || styleIndex == 7)) {
            NotificationUnlockEndlessMode();
        }

        ShowBottomButtons();
    }

    protected virtual void ProcessButtonTutorial() {
        objBottomMenuTutorial.SetActive(true);
        bottomMenuInfinity.gameObject.SetActive(false);
        if (remoteConfig.OnboardingFlow_IsShowPlayNext) {
            btnPlayNextTutorial.gameObject.SetActive(true);
            btnHomeTutorial.gameObject.SetActive(false);
        } else {
            btnPlayNextTutorial.gameObject.SetActive(false);
            if (styleIndex == 1 || styleIndex == 7) {
                btnHomeTutorial.gameObject.SetActive(false); //đã có btn seemore    
                if (rtfSongRecommend) {
                    rtfSongRecommend.SetRectStretchStretchBottom(0);
                }
            } else {
                btnHomeTutorial.gameObject.SetActive(true);
            }
        }
    }

    protected virtual void ShowBottomButtons() {
        ShowButtonReplay();
        if (btnNext != null) {
            if (remoteConfig.NextBtn_IsEnable) {
                Song song = SongRecommendation.GetNextSong();

                if (song != null) {
                    btnNext.gameObject.SetActive(true);
                    if (btnNext.TryGetComponent(out nextButtonHandler)) {
                        nextButtonHandler.Init(song);
                    }
                } else {
                    btnNext.gameObject.SetActive(false);
                }
            } else {
                btnNext.gameObject.SetActive(false);
            }

            if (btnShare != null) {
                btnShare.gameObject.SetActive(!btnNext.gameObject.activeInHierarchy);
            }
        }
    }

    protected void ShowButtonReplay() {
        if (btnReplay) {
            btnReplay.Init(this, _showInfinity, this.currentSong);
        }
    }

    private IEnumerator IEShowPreviewSong() {
        yield return YieldPool.GetWaitForSeconds(7f);

        while (PreviewSongController.instanceSafe.IsPausing) {
            yield return null;
        }

        OnCompletePlayResultSFX();
    }

    private void OnCompletePlayResultSFX() {
        if (!gameObject.activeSelf)
            return;
        if (GroundMusic.instance.IsPlaying())
            return;

        Song previewSong = currentSong;
        if (currentSong.isTutorialSong && !RemoteConfigBase.instance.TutorialSong_ResultPreview) { // if tutorial song
            var song = SongManager.instance.GetSongUnlockedNotPlay();
            // nếu trong songlist không có bài nào đã OPEN sẵn
            // thì không set current song và phát preview bài tutorial hiện tại
            if (song != null) {
                Configuration.SetCurrentSong(song);
                previewSong = song;
            }
        }

        PreviewSongController.instanceSafe.StartPreviewMusic(previewSong, location.ToString(), false);
    }

    protected void NotificationUnlockEndlessMode() {
        Util.ShowSmallMessage(LocalizationManager.instance.GetLocalizedValue("UNLOCKED") + "\n" +
                              LocalizationManager.instance.GetLocalizedValue("ENDLESS_MODE"));
    }

    public ResultOnboarding GetResultOnboarding() {
        return resultOnboarding;
    }

    [HideInInspector] public SongItem   firstSongItem;
    [HideInInspector] public List<Song> recommendedList;

    public Song GetSongofListRecommendSongs(int index) {
        if (index >= 0 && index < recommendedList.Count)
            return recommendedList[index];

        return null;
    }

    protected virtual void ShowRecommendNew() {
        isShowDone = true;
    }

    protected float GetItemHeight() {
        return SongList.itemHeight;
    }

    protected void CheckAchievements() {
        if (currentSong.IsLocalSong())
            return;
        if (currentSong.isTutorialSong)
            return;

        AchievementCenter.CheckAchievements(currentSong);
    }

    protected virtual void ShowSubscriptionButton() {
        bool showSubscription =
            SubscriptionController.IsEnableSubscription() && !SubscriptionController.IsSubscriptionVip();
        bottomMenuInfinity.gameObject.SetActive(!currentSong.isTutorialSong);

        if (showSubscription) {
            vipButton.transform.GetChild(_freeVipTrialBtnOrder).gameObject.SetActive(true);
            vipButton.transform.GetChild(_removeAdsBtnOrder).gameObject.SetActive(false);
            vipButton.transform.GetChild(_unlockAllSongsBtnOrder).gameObject.SetActive(false);
            vipButton.SetActive(true);
        } else {
            vipButton.SetActive(false);
        }
    }

    protected virtual void ShowMultiplyDiamondButton() {
        LogEvent(TRACK_NAME.double_reward, "show");
        objMultiplyDiamond.SetActive(true);

        if (vipButton != null && vipButton.activeSelf && styleIndex == 1) {
            vipButton.SetActive(false);
        }
    }

    protected void LogEvent(string eventName, string action) {
        Dictionary<string, object> param = new Dictionary<string, object> {
            { TRACK_PARAM.screen, "result" },
            { TRACK_PARAM.action, action },
            { TRACK_PARAM.currency_inventory, Configuration.instance.GetDiamonds() },
        };

        AnalyticHelper.UpdateParamsAccumulatedCount(param, eventName);
        AnalyticHelper.LogEvent(eventName, param);
    }

    protected void SetSongData(SongItem songItem, Song data, int ordering, SONGTYPE songtype) {
        data.ordering = ordering;
        nextSong = data;
        songItem.SetSong(songtype, data, GetSongPlayType(data), location: this.location.ToString(), ordering, default);
        songItem.OnClickItemPlay = (songClick, localPos) => {
            if (songtype == SONGTYPE.OPEN && !data.isSongOfDay) {
                Configuration.instance.SetOpenSong(songClick, 0, false, SongUnlockType.reward);
            }

            SongItem_ResultClick(songClick, localPos, songItem);
            songItem.ForceUpdateButtonAfterUnlock();
        };
    }

    public virtual void ChangeTopBar() {
        if (!gameObject.activeInHierarchy) {
            return;
        }

        if (IsFixedTopBar() && tfScrollView != null) {
            StartCoroutine(TopBar.instance.IESetParentDiamondContainer(tfScrollView));
            Vector4 padding = rm2dScrollView.padding;

            if (styleIndex.IsInListNumber(3, 4)) {
                padding.w = 50;
                rtfSongRecommend.SetRectStretchStretchTop(10);
            } else {
                padding.w = 60;
            }

            rm2dScrollView.padding = padding;
            if (songName != null) {
                songName.transform.SetLocalY(-85);
            }
        } else {
            StartCoroutine(TopBar.instance.IESetParentDiamondContainer(scrollContainer.transform));
        }
    }

    public void VipButton_Click() {
        SoundManager.PlayGameButton();
        if (vipButton.transform.GetChild(_removeAdsBtnOrder).gameObject.activeSelf) {
            SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.song_result.ToString(), false, OfferType.Ads,
                userOpen: true);
        } else {
            SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.song_result.ToString(), userOpen: true);
        }
    }

    public void MultiplyDiamond_Click() {
        if (!objMultiplyDiamond.activeSelf) {
            return;
        }

        LogEvent(TRACK_NAME.double_reward, "click");
        SoundManager.PlayGameButton();
        AdsManager.instance.ShowRewardAds(VIDEOREWARD.currency_multiply, null,
            location: LOCATION_NAME.double_reward.ToString(), true, MultiDiamondFinish);
    }

    private void MultiDiamondFinish(bool isCompleted) {
        if (!isCompleted) {
            return;
        }

        LogEvent(TRACK_NAME.double_reward, "claim");
        StartCoroutine(BonusDiamondByMultiply());
    }

    private IEnumerator BonusDiamondByMultiply() {
        Configuration.CountedMultiplierAds += 1;
        objMultiplyDiamond.SetActive(false);
        int diamond = GameController.instance.GetDiamond();
        int increaseAmount = 0;
        if (diamond > 0) {
            increaseAmount = Mathf.RoundToInt(diamond * (remoteConfig.ResultScreen_AdsMultiply - 1));
            diamond = GameController.instance.AddDiamond(increaseAmount, CurrencyEarnSource.VIDEO_MULTIPLY);
        } else {
            diamond = remoteConfig.Diamond_Video_1st;
            increaseAmount = remoteConfig.Diamond_Video_1st;
        }

        AirfluxTracker.TrackRewardAdsImpression(increaseAmount);

        ShowSubscriptionButton();
        yield return StartCoroutine(DiamondEffectMultiply(diamond, false));

        DOVirtual.DelayedCall(0.5f, () => { //wait to diamond fly to target
            Configuration.UpdateDiamond(increaseAmount, CurrencyEarnSource.VIDEO_MULTIPLY.ToString(),
                CurrencyEarnSource.double_reward.ToString());
        });
    }

    public void Rank_Click() {
        var song = Configuration.instance.GetCurrentSong();
        if (!SongManager.instance.IsLocalSong(song.path)) {
            SoundManager.PlayGameButton();
            Util.ShowLeaderBoard(song);
        }
    }

    protected IEnumerator ScoreEffect() {
        scoreUpdate = 0;
        while (scoreUpdate < score) {
            scoreUpdate += Time.deltaTime * score;
            if (scoreText != null) {
                scoreText.text = ((int) scoreUpdate).ToString(CultureInfo.CurrentCulture);
            }

            yield return null;
        }

        if (scoreText != null) {
            scoreText.text = score.ToString();
        }
    }

    protected virtual IEnumerator DiamondEffect(int diamond, bool isDiamondInGame) {
        int amount = Mathf.RoundToInt(diamond - diamondUpdate);

        if (diamondText != null) {
            string text;

            while (diamondUpdate < diamond) {
                diamondUpdate += Time.deltaTime * diamond;
                text = $"{Mathf.Round(diamondUpdate).ToString(CultureInfo.CurrentCulture)}";
                diamondText.text = (styleIndex == 1 || styleIndex == 7) ? $"x{text}" : text;

                yield return null;
            }

            text = isDiamondInGame ? $"{GameController.instance.GetDiamond() + starBonus}" : $"{diamond.ToString()}";
            diamondText.text = styleIndex == 1 || styleIndex == 7 ? $"x{text}" : text;   
        }

        SoundManager.instance.PlayResultGEM();

        Transform diamondFlyTarget = null;
        if (TopBar.instance.isAlterHUD && TopBar.instance.gemsHUDHolder != null) {
            diamondFlyTarget = TopBar.instance.gemsHUDHolder.gemsFlyingTarget;
        }

        var diamondFlyController =
            diamondFlyEffect != null ? diamondFlyEffect : UIOverlay.instance.GetDiamondFlyController();

        // Result screen style 7 transfers diamondIcon from referenced into dynamic spawned
        if (diamondFlyController == null || diamondIcon == null) {
            yield break;
        }
        
        if (diamondFlyTarget == null) {
            diamondFlyController.AddDiamonds(diamondIcon.transform.RootLocalPos(), amount);
        } else {
            diamondFlyController.AddDiamondsRootLocal(diamondIcon.transform, amount, diamondFlyTarget);
        }
    }

    private IEnumerator DiamondEffectMultiply(int diamond, bool isDiamondInGame) {
        yield return DiamondEffect(diamond, isDiamondInGame);
    }

    private void NextSong_VideoCompleted(SongItem songItem, bool isCompleted) {
        if (!isCompleted) {
            return;
        }

        if (nextSong != null) {
            if (SongList.TryToUnlockSongByAds(nextSong)) {
                Configuration.instance.SetOpenSong(nextSong, 0, true, SongUnlockType.ads);
                AnimatePlaySong(nextSong);
            } else {
                songItem.UpdateVideoUnlockStatus();
            }
        }

        AirfluxTracker.TrackRewardAdsImpression();
    }

    private SONG_PLAY_TYPE GetSongPlayType(Song song) {
        return SONG_PLAY_TYPE.recommended_result;
    }

    public void SongItem_ResultClick(Song item, Vector3 localPos, SongItem songItem) {
        if (Configuration.instance.isGamePlayTutorial) {
            AnalyticHelper.LogEvent(TRACK_NAME.fn_result_nextsong);
        }

        SONGTYPE type = item.savedType;
        nextSong = item;
        switch (type) {
            case SONGTYPE.USER_PROGRESSION:
            case SONGTYPE.COUNTDOWN:
                break;

            case SONGTYPE.OPEN:
                SoundManager.PlayGameButton();
                AnalyticHelper.FireEvent(FIRE_EVENT.NextSong_Play);
                AnimatePlaySong(item);
                break;

            case SONGTYPE.VIDEO:
                SoundManager.PlayGameButton();
                SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.next);
                AdsManager.instance.ShowRewardAds(VIDEOREWARD.song_unlock, item, location: this.location.ToString(),
                    true, (result) => { NextSong_VideoCompleted(songItem, result); });
                AnalyticHelper.ScreenResult("SongUnlock_Video");
                break;

            case SONGTYPE.EVENT:
                if (liveEventManager.UnlockSong(LiveEventManager.IdCurrentEvent, nextSong.acm_id_v3)) {
                    SongList.OpenSong(nextSong, 0, SongUnlockType.token);
                    UIOverlay.instance.ShowVFXSubCustom(songItem.imgToken.sprite, songItem.price, localPos,
                        new Vector2(-45, 0), () => {
                            if (HomeManager.CachedTabActived != BottomMenuScript.Type.LiveEvent) {
                                PlaySong(SONG_PLAY_TYPE.recommended_result, nextSong, true);
                            }
                        });
                    liveEventManager.CompleteUnlockSong();
                } else {
                    var popup = Util.ShowPopUp(PopupName.LiveEventNotEnoughToken);
                    if (popup != null) {
                        if (HomeManager.instance && popup.TryGetComponent(out UINotEnoughToken notEnoughToken)) {
                            notEnoughToken.Show(HomeManager.instance.GetLocation());
                        }
                    } else {
                        Logger.EditorLogError("Not found popup!");
                    }
                }

                break;

            default:
                if (item.isDualUnlock) {
                    SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.next);
                    AdsManager.instance.ShowRewardAds(VIDEOREWARD.song_unlock, item, this.location.ToString(), true,
                        (result) => { NextSong_VideoCompleted(songItem, result); });
                } else {
                    item.TryToUnLockSongByGem((result) => {
                        if (result) {
                            AnalyticHelper.ScreenResult("SongUnlock_Diamond");
                            item.savedType = SONGTYPE.OPEN;
                            UIOverlay.instance.ShowVFXSubDiamond(item.diamonds, localPos, new Vector2(-55, 0),
                                () => {
                                    Util.GoToGamePlay(item, location: this.location.ToString(), isSongClick: true);
                                });
                        }
                    });
                }

                break;
        }
    }

    public void Replay() {
        retry++;
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Replay);

        SongLocationTracker.SetSongPlayType(_showInfinity ? SONG_PLAY_TYPE.endless : SONG_PLAY_TYPE.replay);
        if (TransitionInOut.isInstanced && EventSystem.current.currentSelectedGameObject != null) {
            TransitionInOut.instance.TransitionOut(EventSystem.current.currentSelectedGameObject.transform.position,
                Reload);
        } else {
            Reload();
        }
    }

    private void Reload() {
        gameObject.SetActive(false);

        bool isNeedReloadContent = NotesManager.instance.HasNewElements() ||
                                   (remoteConfig.NotesDifficult_IsEnable && GameController.instance.isChangedLevel) ||
                                   (ChallengeMode.IsActive && GameController.enableEndless &&
                                    remoteConfig.ChallengeMode_Recurring);
        if (isNeedReloadContent) {
            GameController.instance.isChangedLevel = false;
            Spawner.s.ReloadDifficult(true);
        } else {
            GameController.instance.GamePrepare(true);
        }
    }

    public virtual void Back() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Menu);
        if (Configuration.instance.isGamePlayTutorial) {
            Dictionary<string, object> param =
                AnalyticHelper.GetDefaultParam(location: this.location.ToString(), includeLocation: false);
            param.Add(TRACK_NAME.loading_duration, Time.time);
            AnalyticHelper.LogEvent(TRACK_NAME.fn_result_home);
        }

        resultOnboarding.HideRedDot();

        // add transition VFX
        if (TransitionInOut.isInstanced && EventSystem.current.currentSelectedGameObject) {
            TransitionInOut.instance.TransitionOut(EventSystem.current.currentSelectedGameObject.transform.position,
                GameController.instance.BackToHome);
        } else {
            GameController.instance.BackToHome();
        }
    }

    private void UpdateFavoriteButton() {
        if (favoriteImage) {
            RectTransform btn = (RectTransform) favoriteImage.transform.parent;
            btn.gameObject.SetActive(true);
            float paddingX = 15 + UIController.ui.WidthOfSongName() / 2f;
            paddingX = Mathf.Clamp(paddingX, 0, 150);

            if (styleIndex.IsInListNumber(2, 3, 4)) {
                btn.SetAnchoredPosition3DPosX(paddingX);
            } else {
                btn.localPosition = Util.SetPositionX(btn.localPosition, paddingX);
            }

            bool isFavorite = UserEntry.GetFavoriteList().Contains(currentSong.acm_id_v3);
            if (isFavorite) {
                favoriteBtnAnim.Stop();
            } else {
                favoriteBtnAnim.Play();
            }

            UpdateColorFavoriteButton(isFavorite);
        }
    }

    public void FavoriteButton_Click() {
        if (favoriteImage) {
            SoundManager.PlayGameButton();
            bool isFavorite = UserEntry.GetFavoriteList().Contains(currentSong.acm_id_v3);
            if (!isFavorite) {
                UserEntry.SaveFavorite(currentSong);
                favoriteBtnAnim.Stop();
                AnalyticHelper.Button_Click(BUTTON_NAME.Favorite_Add);
            } else {
                UserEntry.RemoveFavorite(currentSong);
                AnalyticHelper.Button_Click(BUTTON_NAME.Favorite_Remove);
                favoriteBtnAnim.Play();
            }

            UpdateColorFavoriteButton(!isFavorite);
        }
    }

    protected virtual void UpdateColorFavoriteButton(bool isFavorite) {
    }

    //Share the result
    public void Share() {
        if (scoreUpdate >= score) {
            AnalyticHelper.Button_Click(BUTTON_NAME.Share);
            SoundManager.PlayGameButton();
            StartCoroutine(TakeSSAndShare());

            AnalyticHelper.Share(Configuration.instance.GetCurrentSong(), location: this.location.ToString());
        }
    }

    IEnumerator TakeSSAndShare() {
        yield return YieldPool.GetWaitForEndOfFrame();

        string screenshotName = "screenshot.png";
        //string screenShotPath = Application.persistentDataPath + "/" + screenshotName;

        try {
            Texture2D ss = new Texture2D(Inwave.Utils.GetWidth(), Inwave.Utils.GetHeight(), TextureFormat.RGB24, false);
            ss.ReadPixels(new Rect(0, 0, Inwave.Utils.GetWidth(), Inwave.Utils.GetHeight()), 0, 0);
            ss.Apply();

            string filePath = Path.Combine(Application.temporaryCachePath, screenshotName);
            File.WriteAllBytes(filePath, ss.EncodeToPNG());

            // To avoid memory leaks
            Destroy(ss);

            string text =
                "What an awesome trending music game! \nVery funny, exciting, and challenge!\nCheck it Out! \ud83d\udc49" +
                Application.productName + "\ud83d\udc48 \n#" + Application.productName.Replace(" ", "");
            text += " \n" + Util.GetAppUrl();
            new NativeShare().AddFile(filePath).SetSubject(Application.productName).SetText(text).Share();
        } catch (Exception ex) {
            Util.ShowMessage("Error: " + ex.ToString());
        }

        SceneFader.instance.ShowOverlay();
        yield return YieldPool.GetWaitForSeconds(0.5f);

        SceneFader.instance.HideOverlay();
    }

    private void BtnSeeMoreOnClick() {
        Back(); //Go Home
    }

    protected virtual void BtnPlayNextTutorialOnClick() {
        var song = nextButtonHandler?.nextSong ?? SongRecommendation.GetNextSong();
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Next);
        AnalyticHelper.LogEvent(TRACK_NAME.fn_result_nextsong);

        SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.next);
        AnimatePlaySong(song);
    }

    public bool IsShowed() {
        return gameObject.activeInHierarchy;
    }

    public void SetActive(bool isShow) {
        gameObject.SetActive(isShow);
    }

    #region Escape Handlers

    public bool CanHandleEventBack() {
        return gameObject.activeInHierarchy;
    }

    public virtual bool HandleEventBack() {
        if (_canvasGroupForLockUI != null && !_canvasGroupForLockUI.interactable) {
            return false;
        }

        if (!isShowDone) {
            return false;
        }

        Back();
        return true;
    }

    #endregion

    #region LiveEvent

    protected virtual void DisableLiveEventToken() {
        tokenIcon?.gameObject.SetActive(false);
    }

    protected virtual void ShowLiveEventToken(Sprite sprite) {
        isShowToken = true;
        tokenText.text = string.Empty;
        tokenIcon.sprite = sprite;
        tokenIcon.gameObject.SetActive(true);
    }

    protected IEnumerator TokenEffect(int amount) {
        float count = 0;
        while (count < amount) {
            count += Time.deltaTime * amount;
            tokenText.text = $"+{(Mathf.Round(count)).ToString()}";
            yield return null;
        }

        tokenText.text = $"+{amount.ToString()}";

        SoundManager.instance.PlayResultGEM();
        if (diamondFlyEffect && iconToken) {
            diamondFlyEffect.AddTokens(tokenIcon.transform.RootLocalPos(), amount, iconToken);
        }
    }

    protected virtual IEnumerator IEShowTokenEffect() {
        yield return null;
    }

    #endregion

    public virtual IEnumerator OnStartPhase03() {
        yield return null;
    }

    public bool HasNotificationEndlessMode() {
        if (ChallengeMode.IsActive)
            return false;

        return first3Stars && !currentSong.isTutorialSong;
    }

    protected void ToggleScrollRect(bool isEnable) {
        if (scrollRect != null) {
            scrollRect.enabled = isEnable;
        }
    }

    protected IEnumerator IEProcessPopupFlow() {
        var diamondFlyController = diamondFlyEffect != null ? 
            diamondFlyEffect : 
            UIOverlay.instance.GetDiamondFlyController();
        
        // wait for currencies flying anim done
        if (diamondFlyController != null) {
            yield return YieldPool.GetWaitForSeconds(diamondFlyController.maxDuration);
        }
        
        while (GameController.instance.isOpenedOtherPopup) {
            yield return null;
        }

        while (UIOverlay.instance.IsWaitingAdFsReward()) {
            yield return null;
        }

        #region UI elements + Popups có liên quan đến tiến trình chơi

        yield return IEShowNoticePowerCube();
        yield return IECheckUnlockElement();

        if (BoosterManager.isInstanced) {
            BoosterManager.instanceSafe.GameComplete();

            while (UIController.ui.IsPopupTutorialRewardShowed()) {
                yield return null;
            }

            yield return BoosterManager.IECheckShowUnlockBooster();
        }

        yield return MilestoneEvent.IEShowMilestoneEvent();

        #endregion

        #region Các màn hình (full screen)

        yield return IEShowGalaxyQuest();

        yield return IEShowMysteryDoor();

        #endregion

        #region offers - những cái k tác động đến tiến trình chơi

        yield return IAPTripleOffers.IECheckAutoPopup(LOCATION_NAME.result);
        yield return IEShowIaaSegmentation();

        // [Ver2] Try active Special Offer
        if (SpecialOfferManager.isInstanced && SpecialOfferManager.instanceSafe.version == 2) {
            SpecialOfferManager.instanceSafe.TryActive();
            SpecialOfferManager.ShowPopupAtResultScreen();
        }

        #endregion

        EnableInteractable();
    }

    protected IEnumerator IECheckUnlockElement() {
        if (!StarsJourneyManager.isEnable) {
            yield break;
        }

        int totalStar = GameController.instance.GetTotalStar();

        var element = StarsJourneyManager.instanceSafe.CheckUnlockNewElement(totalStar);
        if (element == NoteElementType.None) {
            yield break;
        }

        //Logger.EditorLogError("Stars Journey", $"Unlock element: {element}");
        Configuration.EarnElement(element);

        var elementConfigData = StarsJourneyManager.instanceSafe.GetElementConfig(element);
        if (elementConfigData == null) {
            yield break;
        }

        bool isWaiting = true;
        UIUnlockElement instancePopup = null;
        yield return Util.ShowPopupAsync<UIUnlockElement>(PopupName.ElementUnlock, callback: popup => {
            isWaiting = false;
            if (popup == null) {
                return;
            }
            instancePopup = popup;
            popup.Show(elementConfigData);
        });

        while (isWaiting || instancePopup != null && instancePopup.gameObject.activeInHierarchy) {
            yield return null;
        }

        yield return StarsJourneyManager.instanceSafe.IEShowOnboardingElement();
    }

    protected IEnumerator IEShowIaaSegmentation() {
        yield return IaaSegmentation.CheckShowPopupAsyncAndWait();
    }
}