using UnityEngine;
using UnityEngine.UI;

public class HeaderScript : OptimizedCellView {
    [SerializeField] private Text txtHeader;

    private void SetData(HeaderData headerData) {
        txtHeader.text = LocalizationManager.instance.GetLocalizedValue(headerData.Title);
        LocalizationManager.instance.UpdateFont(txtHeader);
    }

    public override void SetData(IData data) {
        if (data is HeaderData header) {
            SetData(header);
        }
    }
}