using UnityEngine;
using UnityEngine.UI;
using System;
using System.Collections;
using System.Threading;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using System.Collections.Generic;
using GameCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using TilesHop.ChallengeOldUser;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Hybrid;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.StarJourney;
using TilesHop.DiscoveryChallenge;
using TilesHop.EconomySystem;
using TilesHop.GameCore;
using UI1;
using TilesHop.LiveEvent;
using TilesHop.MysteryBox;
using TilesHop.PlaytimeReward;
using TilesHop.Cores.UserProgression;
using TilesHop.UI.HomeUX;
using TilesHop.Cores.Popups.Home;
using TilesHop.LiveEvent.GalaxyQuest;

public partial class HomeManager : MonoBehaviour {
    #region Static

    public static HomeManager instance;
    public static bool        isInstanced;

    /// <summary>
    /// chờ một điều kiện đặc biệt nào đó trước khi show các popup
    /// </summary>
    public static WaitWhile waitOnStart;

    #endregion

    //public ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    [SerializeField] private UIBackgroundHome bgHome;
    [SerializeField] private GameObject       _mainHome;

    [SerializeField]         public UIMenuHome       menuHome;
    [Space] [SerializeField] public BottomMenuScript bottomMenuScript;

    #region Home's Tabs

    [Space] [Header("Home Tabs")] [SerializeField]
    public MainHomeScript mainHomeScript;

    [SerializeField] private SearchScript searchScript;
    [SerializeField] private TabScript    liveEventScript;

    private TabScript _starJourneyTabContainer;
    private TabScript _shopIapTabContainer;
    private TabScript _shopBallTabContainer;

    public TabScript tabContainerStarJourney {
        get {
            if (_starJourneyTabContainer == null) {
                var tabContainer = new GameObject("StarJourney_TabContainer");
                tabContainer.SetActive(false);
                var rect = tabContainer.AddComponent<RectTransform>();
                rect.SetParent(SongList.instance.noneSafeAreaTransform);
                rect.ResetToFullAnchor();

                _starJourneyTabContainer = tabContainer.AddComponent<TabScript>();
                _starJourneyTabContainer.timeAnimationMove = 0.3f;
            }

            return _starJourneyTabContainer;
        }
    }

    public TabScript tabContainerShopIap {
        get {
            if (_shopIapTabContainer == null) {
                var tabContainer = new GameObject("ShopIAP_TabContainer");
                tabContainer.SetActive(false);
                var rect = tabContainer.AddComponent<RectTransform>();
                rect.SetParent(SongList.instance.noneSafeAreaTransform);
                rect.ResetToFullAnchor();

                _shopIapTabContainer = tabContainer.AddComponent<TabScript>();
                _shopIapTabContainer.timeAnimationMove = 0.3f;
            }

            return _shopIapTabContainer;
        }
    }

    public TabScript tabContainerShopBall {
        get {
            if (_shopBallTabContainer == null) {
                var tabContainer = new GameObject("ShopBall_TabContainer");
                tabContainer.SetActive(false);
                var rect = tabContainer.AddComponent<RectTransform>();
                rect.SetParent(SongList.instance.safeAreaTransform);
                rect.SetSiblingIndex(bottomMenuScript == null ? 0 : bottomMenuScript.transform.GetSiblingIndex() - 1);
                rect.ResetToFullAnchor();

                _shopBallTabContainer = tabContainer.AddComponent<TabScript>();
                _shopBallTabContainer.timeAnimationMove = 0.3f;
            }

            return _shopBallTabContainer;
        }
    }

    #endregion

    [Space] [SerializeField] private UIMainDiskHome mainDiskHome;

    [SerializeField] public CharactersPreviewScript charactersPreview;

    [SerializeField] private Transform     canvasBG;
    [SerializeField] private RectTransform categoryScrollerUnmask;
    [SerializeField] private CanvasGroup   songScrollerCanvasGroup;

    [SerializeField] private GameObject       objLockHome;
    [SerializeField] private DiamondFlyEffect diamondFlyEffect;

    public MainHomeScript MainHome => mainHomeScript;
    public Sprite DefaultDiskIcon => mainDiskHome.DefaultDiskIcon;

    //private ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    private RemoteConfig remoteConfig => RemoteConfigBase.instance;
    private bool isShowCharactersInHome => remoteConfig.isShowCharactersInHome;
    public bool isTopBarFixed => remoteConfig.Economy_Topbar_IsFixed && !UserProgressionController.EnableFeature;
    private LiveEventManager liveEventManager => LiveEventManager.instance;

    private GameObject    _objHomeSpeaker;
    private ShopScript    _shopScript;
    private SettingList   _settingPopup;
    private Achievements  _achievementsPopup;
    private GameObject    _popup7DayMission;
    private GameObject    _categoryScroller;
    private RectTransform _cachedRtfCategoryScroller;
    private GameObject    _popupBallSpinner;

    private Song         _currentHomeSong;
    private bool         _isWaitPreviewSong = false;
    private TopBarHome   _topbarHome;
    private HashSet<int> _setFullScreenPopups;

    public GameObject blackBG;
    public bool       isShowingTooltip = false;

    public static  BottomMenuScript.Type CachedTabActived         = BottomMenuScript.Type.Home;
    private static bool                  isShowPopupCommunication = false;
    

    #region Public Element Getter

    public TopBarHome topBarHome => _topbarHome;

    public GameObject CategoryScroller => _categoryScroller;
    public CanvasGroup SongScrollerCanvasGroup => songScrollerCanvasGroup;
    public bool IsLockTopMenuForHome2 => menuHome.isLocked;

    public bool IsShowingShopBall =>
        _shopScript != null && _shopScript.gameObject != null && _shopScript.gameObject.activeInHierarchy;

    public ShopScript shopScript => _shopScript;
    public SearchScript discoverySearchScript => searchScript;

    public bool IsShowButtonShopDiamondInHome => menuHome.IsShowButtonShopDiamondInHome;
    private AppsFlyerInit appsFlyerInit => AppsFlyerInit.instanceAFI;

    public bool isStartCompleted { get; private set; } = false;

    #endregion

    #region Unity Method

    private void Awake() {
        instance = this;
        isInstanced = true;

        Util.EnableCheckDualUnlock = true;
        SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.home);
        GetCurrentHomeSong();

        Configuration.OnBoughtStarterPack += ConfigurationOnOnBoughtStarterPack;
        PreviewSongController.instanceSafe.ClearStack();
        if (UserProgressionController.EnableFeature) {
            ChangeMenuHomeUserProgression();
            ChangeMenuBottomUserProgression();
            _topbarHome = menuHome.topBarHome;
            _isWaitPreviewSong = true;
        } else {
            if (RemoteConfigBase.instance.Hybrid_UI_Rearrange) {
                ChangeMenuHomeForHybrid();
            }

            _topbarHome = menuHome.topBarHome;
            StartPreviewMusic();
        }

        UpdateUIMainButtons();

        if (remoteConfig.HomePopupFlow_IsEnable) {
            SetupPopupFlow();
        }

        if (UIMenuHome.isShowStarJourneyBar || remoteConfig.Hybrid_UI_Rearrange) {
            UIMainDiskHome sjMainDisk = Resources.Load<UIMainDiskHome>("UI/SelectedSongSJ");
            if (sjMainDisk != null) {
                mainDiskHome.gameObject.SetActive(false);
                Transform mainDiskTransform = mainDiskHome.transform;
                int siblingIndex = mainDiskTransform.GetSiblingIndex();
                mainDiskHome = Instantiate(sjMainDisk, mainDiskTransform.parent);
                mainDiskHome.transform.SetSiblingIndex(siblingIndex);

                if (remoteConfig.Hybrid_UI_Rearrange) {
                    var diskTransform = mainDiskHome.transform;
                    var localPos = diskTransform.localPosition;
                    localPos.y -= 30f;
                    diskTransform.localPosition = localPos;
                }
            }
        }
    }

    private void OnEnable() {
        if (menuHome != null) {
            Vector3 oldPos;
            if (mainDiskHome != null) {
                oldPos = mainDiskHome.transform.position;
                mainDiskHome.transform.SetParent(menuHome.transformHolder);
                mainDiskHome.transform.position = oldPos;
            }

            if (charactersPreview != null) {
                oldPos = charactersPreview.transform.position;
                charactersPreview.transform.SetParent(menuHome.transformHolder);
                charactersPreview.transform.position = oldPos;
            }
        }

        isShowingTooltip = false;
        CountNotificationData(this.GetCancellationTokenOnDestroy()).Forget();

        ShowCharactersInHome(isShowCharactersInHome);

        MysteryBox.CheckActivate(); // reinit when reach song start

        LiveEventManager.OnInitCompleted += CheckLiveEventAfterInit;

        if (LiveEventManager.isInstanced) {
            LiveEventManager.instance.ReInit(); // reinit when reach song start
        }

        if (PlaytimeReward.isInstanced) {
            PlaytimeReward.instance.ReInit(); // reinit when reach song start
        }
    }

    private void Start() {
        _ = UniStart();
    }

    private async UniTaskVoid UniStart() {
        try {
            if (diamondFlyEffect) {
                diamondFlyEffect.gameObject.SetActive(true);
            }

            CheckGetTutorialReward();

            if (remoteConfig.ShopDailyDeal_IsEnable) {
                DailyDealShop.instanceSafe.Init();
            }

            VfxTouchUI.TryToActiveVfxTouch();

            // custom wait trước khi show các popup, ví dụ khi navigate từ Halloween item ở result scr đến shop ball
            if (waitOnStart != null) {
                await waitOnStart.ToUniTask(cancellationToken: this.GetCancellationTokenOnDestroy());

                waitOnStart = null;
            }

            if (!remoteConfig.HomePopupFlow_IsEnable) {
                //Edge case check cannot show Consent form due to not finished loading
                if (CMPWrapper.IsEeaOrUK) {
                    CMPWrapper.instance.TryLoadConsentForm();
                }
            }

            RestoreLastState();

            while (SubscriptionUI.instance != null) {
                await UniTask.Yield(this.GetCancellationTokenOnDestroy());
            }

            while (menuHome.isShowVFX) {
                await UniTask.Yield(this.GetCancellationTokenOnDestroy());
            }

            CheckLiveEventAfterInit();

            if (remoteConfig.Subscription_DiscountOffer) {
                while (!RevenueCatPurchases.instance.isLoadPurchaserInfo) {
                    await UniTask.Yield(this.GetCancellationTokenOnDestroy());
                }

                if (RevenueCatPurchases.instance.IsUnSubUser()) {
                    AnalyticHelper.LogEvent(SUBSCRIPTION_EVENT.user_subscription_unsubs.ToString());

                    VipMissionManager.CancelNotificationDay();
                    VipMissionManager.CancelNotificationWeek(true);
                }
            }

            if (isTopBarFixed) {
                UpdateMenuByFixedTopBar();
            } else if (UXHomeDropDown.isEnable && !UserProgressionController.EnableFeature) {
                menuHome.MoveVIPButtonsToTopBar(2);
            }

            UpdateVIPUI();

            BoosterManager.ResetInit();

            // show popups with popup-flow
            isInPopupFlow = true;
            if (remoteConfig.HomePopupFlow_IsEnable) {
                await IEShowOrderedPopupFlow().ToUniTask(this);
            } else {
                await IEShowPopupOldFlow().ToUniTask(this);
            }

            DoNavigateByOneLink();
            await UniTask.Yield(this.GetCancellationTokenOnDestroy());

            bool canTransitionToSongDisk = TransitionInOut.isInstanced && !UserProgressionController.EnableFeature &&
                                           mainDiskHome.mainDiskContainer.gameObject.activeInHierarchy;

            if (canTransitionToSongDisk) {
                TransitionInOut.instance.TransitionIn(mainDiskHome.mainDiskContainer.position);
            }

            menuHome.CheckTooltipShopBall();
            isInPopupFlow = false;
            isStartCompleted = true;
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    private void OnDisable() {
        LiveEventManager.OnInitCompleted -= CheckLiveEventAfterInit;
        if (Configuration.isInstanced) {
            Configuration.instance.isOpenedHome = true;
        }
    }

    private void OnDestroy() {
        Configuration.OnBoughtStarterPack -= ConfigurationOnOnBoughtStarterPack;

        // Cancel manual cancellation tokens (only auto unlock needs manual control)
        _autoUnlockCancellation?.Cancel();
        _autoUnlockCancellation?.Dispose();
        _autoUnlockCancellation = null;

        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }

    #endregion

    private void ChangeMenuHomeUserProgression() {
        var prefab = Resources.Load<UIMenuHomeUserProgression>(ResourcesPath.UserProgression_MenuHome);
        if (prefab == null) {
            Logger.EditorLogError($"Not found prefab at resource path: {ResourcesPath.UserProgression_MenuHome}");
            return;
        }

        var parent = menuHome.transform.parent;
        int sibling = menuHome.transform.GetSiblingIndex();
        menuHome.ProcessBeforeDestroyObject();
        DestroyImmediate(menuHome.gameObject);

        menuHome = Instantiate(prefab, parent);
        menuHome.transform.SetSiblingIndex(sibling);
    }

    private void ChangeMenuHomeForHybrid() {
        var prefab = Resources.Load<UIMenuHome>(ResourcesPath.Hybrid_MenuHome);
        if (prefab == null) {
            Logger.EditorLogError($"Not found prefab at resource path: {ResourcesPath.Hybrid_MenuHome}");
            return;
        }

        var parent = menuHome.transform.parent;
        int sibling = menuHome.transform.GetSiblingIndex();
        menuHome.ProcessBeforeDestroyObject();
        DestroyImmediate(menuHome.gameObject);

        menuHome = Instantiate(prefab, parent);
        menuHome.transform.SetSiblingIndex(sibling);
    }

    private void ChangeMenuBottomUserProgression() {
        var prefab = Resources.Load<BottomMenuUserProgression>(ResourcesPath.UserProgression_MenuBottom);
        if (prefab == null) {
            Logger.EditorLogError($"Not found prefab at resource path: {ResourcesPath.UserProgression_MenuBottom}");
            return;
        }

        var parent = bottomMenuScript.transform.parent;
        int sibling = bottomMenuScript.transform.GetSiblingIndex();
        Destroy(bottomMenuScript.gameObject);

        bottomMenuScript = Instantiate(prefab, parent);
        bottomMenuScript.transform.SetSiblingIndex(sibling);
    }

    private void DoNavigateByOneLink() {
        appsFlyerInit.DeeplinkAction();
    }

    private void GetCurrentHomeSong() {
        _currentHomeSong = Configuration.instance.GetCurrentSong();
        if (_currentHomeSong == null || _currentHomeSong.isTutorialSong || !_currentHomeSong.IsOpened()) {
            _currentHomeSong = SongManager.instance.GetFirstOpenSong();
        }
    }

    private void ConfigurationOnOnBoughtStarterPack() {
        UpdateVIPUI();
    }

    private IEnumerator IEShowTutorialRewardPopup() {
        GameObject popup = null;
        if (PlayerPrefs.GetInt(CONFIG_STRING.CanGetTutorialReward, 0) == 1 && remoteConfig.Economy_IsEnable &&
            PlayerPrefs.GetInt(CONFIG_STRING.TutorialRewardPopUpAppear, 0) == 0) {
            PlayerPrefs.SetInt(CONFIG_STRING.TutorialRewardPopUpAppear, 1);
            popup = Util.ShowPopUp(CONFIG_STRING.TutorialReward);
            if (popup != null) {
                popup.SetActive(true);
            }
        }

        while (popup != null && popup.gameObject.activeSelf) {
            yield return null;
        }
    }

    private void CheckLiveEventAfterInit() {
        if (BallSpinnerManager.IsPendingEvent) {
            if (BallSpinnerManager.instanceSafe.Init()) {
                bottomMenuScript.LiveEvent_CheckActive();
            }
        }

        if (liveEventManager == null || !liveEventManager.IsInit || !liveEventManager.IsActiveEvent ||
            isShowingTooltip) {
            return;
        }

        if (liveEventManager.IsGoEventTab) {
            liveEventManager.IsGoEventTab = false;
            NavigateLiveEvent(TrackingLocation.token_click.ToString());
        } else {
            CheckLiveEventTooltip();
        }
    }

    public void SetActiveFreeBadge(bool isReady) {
        if (BottomMenuScript.isShopIapTabForm && RemoteConfigBase.instance.ShopUI_Revamp_Enable) {
            bottomMenuScript.ShowShopBadge(isReady);
        } else {
            menuHome.SetActiveShopDiamondBadge(isReady);
        }
    }

    private bool IsRestoreLastState() {
        // khôi phục cái trước -> force restore last state
        if (!remoteConfig.GameCompleteUI_ButtonHome_Enable) {
            return true;
        }

        // khôi phục liveEvent
        if (CachedTabActived == BottomMenuScript.Type.LiveEvent) {
            return true;
        }

        if (remoteConfig.FixedScrollviewPositionInHome_IsEnable && Configuration.instance.isOpenedHome) {
            return true;
        }

        return false;
    }

    private void RestoreLastState() {
        if (isShowingTooltip || !IsRestoreLastState()) {
            CachedTabActived = BottomMenuScript.Type.Home; // restore last tab is Home.
        }

        if (liveEventManager != null && liveEventManager.IsGoEventTab) {
            CachedTabActived = BottomMenuScript.Type.LiveEvent;
        }

        if (DiscoveryChallengeManager.isInstanced && DiscoveryChallengeManager.instanceSafe.PlaySongFromChallenge) {
            DiscoveryChallengeManager.instanceSafe.PlaySongFromChallenge = false;
            CachedTabActived = BottomMenuScript.Type.Search;
        }

        switch (CachedTabActived) {
            case BottomMenuScript.Type.Home:
                BtnHomeOnClick(false, false);
                ShowHome();
                break;

            case BottomMenuScript.Type.Search:
                AnalyticHelper.Button_ClickDiscover("result_screen");
                BtnSearchOnClick(false);
                break;

            case BottomMenuScript.Type.LiveEvent:
                if (liveEventManager != null && liveEventManager.IsGoEventTab) { // navigate từ việc bấm icon token
                    liveEventManager.IsGoEventTab = false;
                    NavigateLiveEvent(TrackingLocation.token_click.ToString());
                } else {
                    // khi k phải navigate từ việc bấm icon token
                    NavigateLiveEvent(TrackingLocation.result_button_back.ToString());
                }

                break;

            default:
                Debug.LogError("[RestoreLastState] Chưa handle trường hợp này!!!! -> về Home");
                break;
        }
    }

    private void CheckGetTutorialReward() {
        if (remoteConfig.Economy_IsEnable && PlayerPrefs.GetInt(CONFIG_STRING.CanGetTutorialReward, 0) == 1 &&
            PlayerPrefs.GetInt(CONFIG_STRING.TutorialRewardPopUpAppear, 0) == 1) {
            Configuration.UpdateDiamond(remoteConfig.Economy_TutorialReward_Value, CONFIG_STRING.TutorialReward);
            PlayerPrefs.SetInt(CONFIG_STRING.CanGetTutorialReward, 0);
        }
    }

    private void CheckHomeToolTip() {
        // kiểm tra tooltip

        if (!isShowingTooltip) {
            // check tooltip btn playtime reward
            isShowingTooltip = menuHome.CheckShowTooltipPlayTimeReward();
        }

        CheckLiveEventTooltip();

        if (!isShowingTooltip) {
            // check tooltip search
            if (bottomMenuScript.IsNeedShowExploreNotice) {
                bottomMenuScript.ShowExploreToolTip();
                isShowingTooltip = true;
            }
        }
    }

    private void CheckLiveEventTooltip() {
        if (isShowingTooltip || liveEventManager == null)
            return;

        if (liveEventManager.IsInit && liveEventManager.IsActiveEvent && !liveEventManager.IsGoEventTab) {
            var eventNeedShowToolTip = liveEventManager.GetEventNeedShowToolTip();
            if (eventNeedShowToolTip != null) {
                if (bottomMenuScript != null)
                    bottomMenuScript.ShowEventToolTip(eventNeedShowToolTip.Id);
                isShowingTooltip = true;
            }
        }
    }

    private void UpdateUIMainButtons() {
        bottomMenuScript.SetActive(true);
        bottomMenuScript.btnHomeOnClick = () => {
            SoundManager.PlayGameButton();
            BtnHomeOnClick();
        };
        bottomMenuScript.btnSearchOnClick = () => {
            SoundManager.PlayGameButton();
            AnalyticHelper.Button_ClickDiscover("discover_tab");
            BtnSearchOnClick();
        };
        bottomMenuScript.btnLiveEventOnClick = () => {
            SoundManager.PlayGameButton();
            BtnLiveEventOnClick(TrackingLocation.event_tab.ToString());
        };

        if (BottomMenuScript.isShopIapTabForm) {
            bottomMenuScript.btnShopDiamondOnClick = HandleOnClickTabShopIap;
        }

        mainHomeScript.HasShowMenu();

        // resize shop ball tab in UP to fit with bottom menu
        if (BottomMenuScript.isShowBallTabForm) {
            bottomMenuScript.btnShopBallOnClick = HandleOnClickTabShopBall;
            (tabContainerShopBall.transform as RectTransform).SetRectStretchStretchBottom(BottomMenuScript
                .heightBottomMenu);
        }

        if (BottomMenuScript.isShopIapTabForm) {
            (tabContainerShopIap.transform as RectTransform).SetRectStretchStretchBottom(BottomMenuScript
                .heightBottomMenu);
        }

        if (remoteConfig.Hybrid_UI_Rearrange) {
            bottomMenuScript.btnStarJourneyOnClick = HandleOnClickTabStarJourney;
            (tabContainerStarJourney.transform as RectTransform).SetRectStretchStretchBottom(BottomMenuScript
                .heightBottomMenu);
        }
    }

    private void HandleOnClickTabShopIap() {
        if (BottomMenuScript.isShopIapTabForm && tabContainerShopIap.IsShowed()) {
            return;
        }

        menuHome.BtnShopDiamondOnClick();
    }

    public bool isPopupShopBallOpening => _shopScript != null && _shopScript.IsShopOpened && !_shopScript.isTabForm;

    private GameObject _shopTabInstance;
    private Shop       _shopPopupInstance;

    public GameObject InstantiateShopTab() {
        if (_shopTabInstance == null) {
            _shopTabInstance = Util.InstantiateShopIAP();
        }

        return _shopTabInstance;
    }

    public void ShowShopIapTab() {
        if (_isDoAnimation) {
            return;
        }

        if (IsShowingFullScreenPopup()) {
            if (_shopPopupInstance == null) {
                _shopPopupInstance = Util.ShowPopupShopIAP();
            }

            _shopPopupInstance.Show();
            return;
        }

        bottomMenuScript.HighLight(BottomMenuScript.Type.ShopIAP);
        var hideDir = Direction.None;
        var showDir = Direction.None;

        if (mainHomeScript.IsShowed()) {
            // shop ball and mainhome are shown at the same time
            if (tabContainerShopBall.IsShowed()) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_BALL, HomeTab.SHOP_IAP);

                mainHomeScript.Hide(Direction.None);
                tabContainerShopBall.Hide(hideDir);

                if (_shopScript != null) {
                    _shopScript.HideImmediately();
                }
            } else {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.MAIN_HOME, HomeTab.SHOP_IAP);
                mainHomeScript.Hide(hideDir);
            }
        }

        if (liveEventScript != null && liveEventScript.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.LIVE_EVENT, HomeTab.LIVE_EVENT);
            liveEventScript.Hide(hideDir);
        }

        if (searchScript.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SEARCH, HomeTab.LIVE_EVENT);
            searchScript.Hide(hideDir);
        }

        if (tabContainerStarJourney.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.STAR_JOURNEY, HomeTab.LIVE_EVENT);
            tabContainerStarJourney.Hide(hideDir);
        }

        if (!tabContainerShopIap.IsShowed()) {
            _isDoAnimation = true;
            tabContainerShopIap.Show(showDir, onShowDone: MarkDoneAnimation);
        }
    }

    public void ShowShopBallTab() {
        if (_isDoAnimation) {
            return;
        }

        bottomMenuScript.HighLight(BottomMenuScript.Type.ShopBall);

        var hideDir = Direction.None;
        var showDir = Direction.None;

        if (tabContainerShopIap.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_IAP, HomeTab.SHOP_BALL);
            tabContainerShopIap.Hide(hideDir);
        }

        if (liveEventScript != null && liveEventScript.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.LIVE_EVENT, HomeTab.SHOP_BALL);
            liveEventScript.Hide(hideDir);
        }

        if (searchScript.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SEARCH, HomeTab.SHOP_BALL);
            searchScript.Hide(hideDir);
        }

        if (mainHomeScript.IsShowed()) {
            (_, showDir) = bottomMenuScript.GetTabDirection(HomeTab.MAIN_HOME, HomeTab.SHOP_BALL);
        } else {
            mainHomeScript.Show(Direction.None);
        }

        if (tabContainerStarJourney.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.STAR_JOURNEY, HomeTab.SHOP_BALL);
            tabContainerStarJourney.Hide(hideDir);
        }

        if (!tabContainerShopBall.IsShowed()) {
            _isDoAnimation = true;
            tabContainerShopBall.Show(showDir, onShowDone: MarkDoneAnimation);
        }
    }

    private void HandleOnClickTabShopBall() {
        if (BottomMenuScript.isShowBallTabForm && tabContainerShopBall.IsShowed()) {
            return;
        }

        menuHome.BtnShopBallClick();
    }

    public void HandleOnClickTabStarJourney() {
        if (_isDoAnimation) {
            return;
        }

        SoundManager.PlayGameButton();
        Util.ShowStarJourney(tabContainerStarJourney.transform as RectTransform);

        bottomMenuScript.HighLight(BottomMenuScript.Type.StarJourney);
        Direction hideDir = Direction.None;
        Direction showDir = Direction.None;

        if (_starJourneyTabContainer.IsShowed()) {
            return;
        }

        _isDoAnimation = true;

        if (mainHomeScript.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.MAIN_HOME, HomeTab.STAR_JOURNEY);
            mainHomeScript.Hide(hideDir);
        }

        if (searchScript.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SEARCH, HomeTab.STAR_JOURNEY);
            searchScript.Hide(hideDir);
        }

        if (tabContainerShopIap.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_IAP, HomeTab.STAR_JOURNEY);
            tabContainerShopIap.Hide(hideDir);
        }

        if (tabContainerShopBall.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_BALL, HomeTab.STAR_JOURNEY);
            tabContainerShopBall.Hide(hideDir);
            _shopScript?.HideImmediately();
        }

        if (liveEventScript != null && liveEventScript.IsShowed()) {
            (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.LIVE_EVENT, HomeTab.STAR_JOURNEY);
            liveEventScript.Hide(hideDir);
        }

        _starJourneyTabContainer.Show(showDir, onShowDone: MarkDoneAnimation);
        bottomMenuScript.HighLight(BottomMenuScript.Type.StarJourney);
    }

    public void BtnDiamondShop_Click() {
        AnalyticHelper.Button_Click(BUTTON_NAME.Shop);
        Shop.ShopRemoteData remoteData = remoteConfig.GetShopRemoteData(IAPDefinitionId.diamond1.ToString());
        int amount = ((remoteData != null && remoteData.value > 0) ? remoteData.value : 120);
        IAPDefinitionId id = IAPDefinitionId.diamond1;
        Configuration.instance.BuyProduct(id, string.Empty, (isSuccess, message) => {
            if (isSuccess) {
                //Shop.CompletedPurchase(id);
                if (amount != 0) {
                    Configuration.UpdateDiamond(amount, CurrencyEarnSource.IAP.ToString(),
                        CurrencyEarnSource.iap_shop.ToString());
                }
            }
        });
    }

    public void UpdateVIPUI() {
        menuHome.UpdateVIPUI(isTopBarFixed);
    }

    public void VipButton_Click() {
        SoundManager.PlayGameButton();
        SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.home.ToString(), false, OfferType.Ads,
            userOpen: true);
    }

    public void ShowSubscriptionOnboarding() {
        if (remoteConfig.Economy_IsEnable) {
            var remoteIAPConfig = Configuration.instance.GetEconomyRemoteConfig();
            if (remoteIAPConfig != null && remoteIAPConfig.ModifiedSubscription_Enable) {
                //TH-2187: không show onboarding trong trường hợp modified VIP benefit!!!
                return;
            }
        }

        Dictionary<string, object> param = AnalyticHelper.GetDefaultParam(LOCATION_NAME.home.ToString());

        if (PlayerPrefs.GetInt(CONFIG_STRING.FistClickVIPButton, 0) == 0) {
            param.Add(TRACK_NAME.first_time_enter, "yes");
            PlayerPrefs.SetInt(CONFIG_STRING.FistClickVIPButton, 1);
        } else {
            param.Add(TRACK_NAME.first_time_enter, "no");
        }

        AnalyticHelper.LogEvent(SUBSCRIPTION_ONBOARDING_EVENT.sub_onboarding_page_click.ToString(), param);

        SoundManager.PlayGameButton();
        Util.ShowPopUp(PopupName.SubscriptionOnBoarding);
    }

    public void ShowHome_Click() {
        AnalyticHelper.Button_Click(BUTTON_NAME.Menu);
        if (SongList.instance != null) {
            SongList.instance.BuildList();
        }

        ShowHome();
    }

    private bool _isUpdateAchievementBadgeCount;
    private bool _isUpdateMissionBadgeCount;

    public void UpdateAchievementBadgeCount() {
        IEUpdateAchievementBadgeCount(this.GetCancellationTokenOnDestroy()).Forget();
    }

    private async UniTaskVoid IEUpdateAchievementBadgeCount(CancellationToken cancellationToken = default) {
        try {
            if (_isUpdateAchievementBadgeCount) {
                return;
            }

            _isUpdateAchievementBadgeCount = true;

            while (!BallManager.isInstanced || !BallManager.instance.IsInit()) {
                await UniTask.Yield(cancellationToken);
            }

            int badgeCount = 0;
            if (AchievementItem.IsTwitterAvailable()) {
                badgeCount++;
            }

            bool waitingInitDone = true;
            AchievementCenter.WaitUtilInitDone(() => { waitingInitDone = false; });
            while (waitingInitDone) {
                await UniTask.Yield(cancellationToken);
            }

            List<AchievementObj> achievementList = AchievementCenter.GetAchievementList();
            if (achievementList != null) {
                for (int i = 0; i < achievementList.Count; i++) {
                    if (achievementList[i].status == AchievementStatus.UNLOCK && !achievementList[i].isNewChallenge()) {
                        badgeCount++;
                    }
                }
            }

            menuHome.ShowAchievementBadge(badgeCount);
            _isUpdateAchievementBadgeCount = false;
        } catch (OperationCanceledException) {
            _isUpdateAchievementBadgeCount = false;
            // Operation was cancelled - this is expected behavior
        }
    }

    public void UpdateMissionBadgeCount() {
        if (!gameObject.activeSelf)
            return;

        StartCoroutine(IEUpdateMissionBadgeCount());
    }

    private IEnumerator IEUpdateMissionBadgeCount() {
        if (_isUpdateMissionBadgeCount) {
            yield break;
        }

        if (MissionManager.isInstanced) {
            _isUpdateMissionBadgeCount = true;
            while (!BallManager.isInstanced || !BallManager.instance.IsInit()) {
                yield return null;
            }

            int badgeCount = MissionManager.instanceSafe.GetCountBadge();
            menuHome.ShowMissionBadge(badgeCount);
            _isUpdateMissionBadgeCount = false;
        }
    }

    public void ShowHome() {
        if (isShowCharactersInHome) {
            UpdateCharactersInHome();
        } else {
            UpdateThemeDisk();
        }

        DOVirtual.DelayedCall(1f, () => {
            UpdateAchievementBadgeCount();
            UpdateMissionBadgeCount();
        });

        topBarHome.ToggleDiamond(true);

        if (Tips.instance != null) {
            Destroy(Tips.instance.gameObject);
        }

        SetActiveHome(true);

        if (mainHomeScript.gameObject.activeSelf) {
            bottomMenuScript.HighLight(BottomMenuScript.Type.Home);
            SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.home);
            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.home);
        } else if (liveEventScript != null && liveEventScript.gameObject.activeSelf) {
            bottomMenuScript.HighLight(BottomMenuScript.Type.LiveEvent);
            SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.live_event);
            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.liveEvent);
        } else if (searchScript.gameObject.activeSelf) {
            bottomMenuScript.HighLight(BottomMenuScript.Type.Search);
            SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.discover);
            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.discover);
        }

        CheckHomeToolTip();
    }

    public void HideHome() {
        SetActiveHome(false);
    }

    public void UpdateThemeDisk() {
        // no need to update main disk cause there's no main disk in UP
        if (UserProgressionController.EnableFeature) {
            return;
        }

        if (isShowCharactersInHome) {
            return;
        }

        if (_currentHomeSong == null) {
            return;
        }

        if (UIMenuHome.isShowStarJourneyBar || remoteConfig.Hybrid_UI_Rearrange) {
            mainDiskHome.SetText(_currentHomeSong.GetSongName(), _currentHomeSong.artist);
            mainDiskHome.SetStars(_currentHomeSong.bestStar);
        } else {
            mainDiskHome.SetText(_currentHomeSong.GetSongName(), string.Empty);
        }

        mainDiskHome.ShowFakeDisk(true);
        if (isTopBarFixed) {
            mainDiskHome.HideLight();
        } else {
            mainDiskHome.ShowLight();
        }

        if (remoteConfig.HomeDecor_SongDisk) {
            UIHomeDecorationController.TryGetDecorDiskSprite((sprite) => {
                if (sprite) {
                    mainDiskHome.SetIcon(sprite);
                } else {
                    UpdateIconHomeDisk();
                }
            });
        } else {
            UpdateIconHomeDisk();
        }
    }

    private async void UpdateIconHomeDisk() {
        try {
            if (!SongCards.instance) {
                return;
            }

            (Sprite, bool) data = await SongCards.instance.GetSquareIconSong(_currentHomeSong);
            if (!this || !gameObject) {
                return;
            }

            Sprite card = data.Item1;
            if (card && mainDiskHome) {
                mainDiskHome.SetIcon(card);
            }
        } catch (Exception e) {
            CustomException.Fire("[UpdateIconHomeDisk]", e.Message);
        }
    }

    public SongStarsDisplay GetStarsGroup() {
        return mainDiskHome == null ? null : mainDiskHome.GetStarsGroupTransform();
    }

    public UIMainDiskHome GetMainDiskHome() {
        return mainDiskHome;
    }

    public void AdminButton_click() {
        if (DevInfo.instance == null) {
            GameObject popup = Util.ShowPopUp(PopupName.DevInfo);
            DevInfo.instance = popup.GetComponent<DevInfo>();
        }
    }

    public void ShowBallList(int indexBall = -1) {
        bool isTabForm = BottomMenuScript.isShowBallTabForm;
        if (isTabForm && _isDoAnimation) {
            return;
        }

        if (!remoteConfig) {
            return;
        }

        if (IsShowingShopBall) {
            return;
        }

        ShowHomeWithoutBottomMenuHighlight(false, !isTabForm);
        AnalyticHelper.instance?.SetCurrentLocation(LOCATION_NAME.BALL_LIST);
        SoundManager.PlayGameButton();

        if (SongList.instance != null && SongList.instance.SongScroller != null) {
            SongList.instance.SongScroller.ResetToTop();
        }

        if (isTabForm) {
            ShowShopBallTab();
        } else {
            bottomMenuScript.HighLight(BottomMenuScript.Type.Home);
        }

        if (DiscoveryChallengeManager.isInstanced) {
            DiscoveryChallengeManager.instanceSafe.CloseOpenedPopup();
        }

        if (topBarHome) {
            if (remoteConfig.ShopBall_VerticleScroll) {
                topBarHome.gameObject.SetActive(false);
            } else {
                topBarHome.ToggleDiamond(true);
            }
        }

        AnalyticHelper.Button_Click(BUTTON_NAME.Balls);
        //ChangeContentTo(1);

        if (!_shopScript) {
            string path;
            if (remoteConfig.ShopBall_UseCategoryLayout) {
                path = $"{PopupName.ShopInHome}V{remoteConfig.ShopBall_LayoutStyleIndex}";
            } else if (remoteConfig.ShopBall_VerticleScroll) {
                path = PopupName.ShopInHome_Vert;
            } else {
                path = PopupName.ShopInHome;
            }

            GameObject popup = Util.ShowPopUp(path);
            if (!popup) {
                CustomException.Fire("[ShowBallList]", $"Not found popup at path: {path}");
                return;
            }

            popup.TryGetComponent(out _shopScript);

            if (!_shopScript) {
                CustomException.Fire("[ShowBallList]", $"Not found component ShopScript at path: {path}");
                return;
            }
        }

        Action<bool> onShopClose = null;
        if (remoteConfig.ShopBall_VerticleScroll) {
            onShopClose = HandleShopBallVerticleClose;
        }

        if (isTabForm && tabContainerShopBall != null) {
            var rectShop = (RectTransform) _shopScript.transform;
            rectShop.parent = tabContainerShopBall.transform;
            rectShop.offsetMax = Vector2.zero;
            rectShop.offsetMin = Vector2.zero;

            bool isForceUseTwoRowLayout = Util.IsHomeScene() && !Util.IsLongScreen();

            if (isForceUseTwoRowLayout) {
                _shopScript.ResizeContent();
            }

            var shopSafeArea = _shopScript.GetComponentInChildren<SafeArea>();
            if (shopSafeArea) {
                Destroy(shopSafeArea);
            }
        }

        var shopShowDirection = isTabForm ? Direction.None : Direction.Bottom;
        _shopScript.ShowPopup(shopShowDirection, ShopScript.PreviousLocation.home, indexBall, onClose: onShopClose);

        if (BallManager.instance && BallManager.instance.IsThereNewBalls) {
            menuHome?.TurnOffToolTipShopBall();
            BallManager.instance.StoredListNewBalls();
        }

        // tracking
        string location = ShopScript.PreviousLocation.home.ToString();
        if (remoteConfig.ShopBall_EntryPointV2_IsEnable) {
            location = Util.BuildString('_', location, "v2");
        }

        AnalyticHelper.ShopBallEntryPointClick(location);
    }

    private void HandleShopBallVerticleClose(bool isWatchedAds) {
        if (topBarHome) {
            topBarHome.gameObject.SetActive(true);
        }
    }

    public void ShowAchievement() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Achievement);
        if (AchievementManager.isInstanced) {
            Util.ShowPopup_UPAchievement();
        } else {
            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.ACHIEVEMENT);
            topBarHome.ToggleDiamond(true);
            if (_achievementsPopup == null) {
                _achievementsPopup = Util.ShowPopUp(PopupName.Achievements).GetComponent<Achievements>();
            }

            _achievementsPopup.Show();
        }

        MissionCenter.DoMission(MissionType.view_achievement);
    }

    public void ShowMission() {
        Util.ShowPopup_UPMission();
        MissionCenter.DoMission(MissionType.view_mission);
    }

    public SettingList ShowSettings() {
        AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.setting);
        SoundManager.PlayGameButton();

        if (_settingPopup == null) {
            _settingPopup = Util.ShowPopUp(PopupName.Settings).GetComponent<SettingList>();
        }

        _settingPopup.Show();
        return _settingPopup;
    }

    public void Play() {
        if (Configuration.instance.isLongNotePrototype) {
            Util.ShowPopup_AdminLongNote(_currentHomeSong);
            return;
        }

        if (Configuration.instance.isSpecialTileControl) {
            Util.ShowPopup_AdminSpecialTile(_currentHomeSong);
            return;
        }

        if (Configuration.instance.isSpecialTileV2) {
            Util.ShowPopup_AdminSpecialTileV2(_currentHomeSong);
            return;
        }

        SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.home_button_play);

        if (_currentHomeSong.savedType != SONGTYPE.OPEN) {
            if (_currentHomeSong.savedType == SONGTYPE.LOCK) {
                if (Configuration.instance.GetDiamonds() < _currentHomeSong.diamonds) {
                    // can't unlock song!!!!
                    ShowShopIapTab();
                } else {
                    Configuration.instance.SetOpenSong(_currentHomeSong, _currentHomeSong.diamonds, true,
                        SongUnlockType.currency);
                    PlaySong();
                }
            } else {
                Configuration.instance.SetOpenSong(_currentHomeSong, 0, true, SongUnlockType.ads);
                PlaySong();
            }
        } else {
            PlaySong();
        }

        void PlaySong() {
            _currentHomeSong.ordering = 0;
            Util.GoToGamePlay(_currentHomeSong, this.GetLocation(), isSongClick: true);
        }
    }

    private Coroutine _cTransitionBackground;

    private void SetActiveHome(bool isActive) {
        _mainHome.SetActive(isActive);
    }

    #region Vip Mission

    private VipMissionScript _vipMissionScript;

    public void btnVipMissionOnClick() {
        if (_vipMissionScript == null) {
            _vipMissionScript = Util.ShowPopUp(PopupName.VipMission).GetComponent<VipMissionScript>();
        } else {
            _vipMissionScript.gameObject.SetActive(true);
        }
    }

    public void NotifyVipMission() {
        menuHome.SetActiveVipMissionBadge(true);
    }

    #endregion

    private void SetActiveShopBG(bool isActive) {
        if (isActive) {
            if (_objHomeSpeaker == null) {
                _objHomeSpeaker = Instantiate(Resources.Load<GameObject>("ShopInHome/ShopBG"), this.canvasBG);
            }

            _objHomeSpeaker.SetActive(true);
        } else {
            if (_objHomeSpeaker != null) {
                _objHomeSpeaker.SetActive(false);
            }
        }
    }

    #region 7 Day Mission

    public void OnBtn7DayMissionClick() {
        SoundManager.PlayGameButton();
        if (_popup7DayMission == null) {
            _popup7DayMission = Util.ShowPopUp(PopupName.SevenDayMission);
        } else {
            _popup7DayMission.SetActive(true);
        }
    }

    #endregion

    #region Song Discovery

    private bool _isDoAnimation;

    public void OnBtnHomeClicked() {
        BtnHomeOnClick();
    }

    private void BtnHomeOnClick(bool hasAnimation = true, bool hasScrollToTop = true) {
        if (_isDoAnimation) {
            return;
        }

        ShowHomeWithoutBottomMenuHighlight(hasAnimation, hasScrollToTop);
        bottomMenuScript.HighLight(BottomMenuScript.Type.Home);
    }

    public void ShowHomeWithoutBottomMenuHighlight(bool hasAnimation = true, bool hasScrollToTop = true) {
        if (_isDoAnimation) {
            return;
        }

        bool isInMainHome = mainHomeScript.IsShowed() && (_shopScript == null || !_shopScript.IsShowed());
        if (isInMainHome && hasScrollToTop) { // already show HOME screen
            if (hasAnimation) {
                _isDoAnimation = true;
                mainHomeScript.ScrollToTop(0.3f, () => {
                    MarkDoneAnimation();
                    if (UserProgressionController.EnableFeature) {
                        SongList.instance.SongScroller.ScrollerAdapter.ResetTopHeader();
                    }
                });
            } else {
                mainHomeScript.ScrollToTop(0);
                if (UserProgressionController.EnableFeature) {
                    SongList.instance.SongScroller.ScrollerAdapter.ResetTopHeader();
                }
            }
        }

        TransitionToMainHome(hasAnimation);

        // update badge count
        UpdateAchievementBadgeCount();
        UpdateMissionBadgeCount();
    }

    private void TransitionToMainHome(bool hasAnimation = true) {
        // in UP, [hasAnimation = false] means opening shop ball instead of opening mainhome
        if (!hasAnimation && BottomMenuScript.isShowBallTabForm || _isDoAnimation) {
            return;
        }

        bottomMenuScript.PrepareTabOrderMaping();
        var showDir = Direction.None;
        var hideDir = Direction.None;

        if (searchScript.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SEARCH, HomeTab.MAIN_HOME);
            }

            searchScript.Hide(hideDir);
        }

        if (liveEventScript != null && liveEventScript.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.LIVE_EVENT, HomeTab.MAIN_HOME);
            }

            liveEventScript.Hide(hideDir);
        }

        if (tabContainerShopBall.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_BALL, HomeTab.MAIN_HOME);
            }

            tabContainerShopBall.Hide(hideDir);
            if (_shopScript != null) {
                _shopScript.HideImmediately();
                SoundManager.PlaySFX_PopupClose();
            }
        }

        if (tabContainerShopIap.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_IAP, HomeTab.MAIN_HOME);
            }

            tabContainerShopIap.Hide(hideDir);
            SoundManager.PlaySFX_PopupClose();
        }

        if (tabContainerStarJourney.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.STAR_JOURNEY, HomeTab.MAIN_HOME);
            }

            tabContainerStarJourney.Hide(hideDir);
        }

        bool isSwitchingToMainHome = tabContainerShopBall.IsShowed() || !mainHomeScript.IsShowed();

        if (isSwitchingToMainHome) {
            _isDoAnimation = true;
            mainHomeScript.Show(hasAnimation ? showDir : Direction.None, CheckHomeToolTip);

            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.home);
            SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.home);
            DOVirtual.DelayedCall(hasAnimation ? searchScript.timeAnimationMove : 0, MarkDoneAnimation);
        }
    }

    public void BtnSearchOnClick(bool hasAnimation = true) {
        bottomMenuScript.HideExploreNotice();
        if (_isDoAnimation) {
            return;
        }

        if (searchScript.IsShowed()) {
            if (hasAnimation) {
                _isDoAnimation = true;
                searchScript.ScrollToTop(MarkDoneAnimation);
            }

            return;
        }

        _isDoAnimation = true;

        var hideDir = Direction.None;
        var showDir = Direction.None;

        if (mainHomeScript.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.MAIN_HOME, HomeTab.SEARCH);
            }

            mainHomeScript.Hide(hideDir);
        }

        if (liveEventScript != null && liveEventScript.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.LIVE_EVENT, HomeTab.SEARCH);
            }

            liveEventScript.Hide(hideDir);
        }

        if (tabContainerShopIap.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_IAP, HomeTab.SEARCH);
            }

            tabContainerShopIap.Hide(hideDir);
        }

        if (tabContainerShopBall.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_BALL, HomeTab.SEARCH);
            }

            tabContainerShopBall.Hide(hideDir);
            if (_shopScript != null) {
                _shopScript.HideImmediately();
            }
        }

        if (tabContainerStarJourney.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.STAR_JOURNEY, HomeTab.SEARCH);
            }

            tabContainerStarJourney.Hide(hideDir);
        }

        searchScript.Show(showDir);

        AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.discover);
        SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.discover);
        DOVirtual.DelayedCall(hasAnimation ? searchScript.timeAnimationMove : 0, MarkDoneAnimation);
        bottomMenuScript.HighLight(BottomMenuScript.Type.Search);
    }

    private void MarkDoneAnimation() {
        _isDoAnimation = false;
    }

    public void BtnLiveEventOnClick(string location, bool hasAnimation = true) {
        if (liveEventScript == null) {
            if (LiveEventManager.instance.IsActiveEvent) {
                liveEventScript = Instantiate(Resources.Load<UILiveEvent>(ResourcesPath.LiveEventTab),
                    SongList.instance.noneSafeAreaTransform);
            } else if (BallSpinnerManager.IsActiveEvent) {
                liveEventScript = Instantiate(Resources.Load<UIBallSpinner>(ResourcesPath.BallSpinner),
                    SongList.instance.noneSafeAreaTransform);
            }

            liveEventScript.transform.localScale = Vector3.one;
            liveEventScript.transform.localPosition = Vector3.zero;
            liveEventScript.gameObject.SetActive(false);
        }

        if (_isDoAnimation) {
            return;
        }

        if (liveEventScript.IsShowed()) {
            if (hasAnimation) {
                _isDoAnimation = true;
                if (liveEventScript is UILiveEvent uiLiveEvent) {
                    uiLiveEvent.ScrollToTop(MarkDoneAnimation);
                } else {
                    MarkDoneAnimation();
                    Logger.EditorLogError("not handle this!!!");
                }
            }

            return;
        }

        _isDoAnimation = true;

        if (liveEventManager.CountActiveEvent() == 1) {
            var eventItem = LiveEventManager.instance.GetLiveEvent(LiveEventManager.IdCurrentEvent);
            if (eventItem != null) {
                LiveEventTracker.Track_EventClick(eventItem.eventConfig.Id, eventItem.eventConfig.Title, location,
                    eventItem.progress.TokenAmount);
            }
        }

        var hideDir = Direction.None;
        var showDir = Direction.None;

        if (mainHomeScript.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.MAIN_HOME, HomeTab.LIVE_EVENT);
            }

            mainHomeScript.Hide(hideDir);
        }

        if (searchScript.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SEARCH, HomeTab.LIVE_EVENT);
            }

            searchScript.Hide(hideDir);
        }

        if (tabContainerShopIap.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_IAP, HomeTab.LIVE_EVENT);
            }

            tabContainerShopIap.Hide(hideDir);
        }

        if (tabContainerShopBall.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.SHOP_BALL, HomeTab.LIVE_EVENT);
            }

            tabContainerShopBall.Hide(hideDir);
            _shopScript?.HideImmediately();
        }

        if (tabContainerStarJourney.IsShowed()) {
            if (hasAnimation) {
                (hideDir, showDir) = bottomMenuScript.GetTabDirection(HomeTab.STAR_JOURNEY, HomeTab.LIVE_EVENT);
            }

            tabContainerStarJourney.Hide(hideDir);
        }

        liveEventScript.Show(showDir, onShowDone: MarkDoneAnimation);
        bottomMenuScript.HighLight(BottomMenuScript.Type.LiveEvent, hasAnimation);

        AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.liveEvent);
        SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.live_event);
    }

    #endregion

    public void UpdateCharactersInHome(int ballID = -1, bool isForceShow = false) {
        if (!isShowCharactersInHome && !isForceShow) {
            return;
        }

        ShowCharactersPreview(true, isForceShow);
        int selectedBall = ballID < 0 ? Configuration.GetSelectedBall() : ballID;
        charactersPreview.Init(selectedBall, OnClickCharactersPreview);
    }

    private void OnClickCharactersPreview(CharactersPreviewScript obj) {
        ShowBallList();
    }

    public static void ShowCharactersPreview(bool isShow = true, bool isForceShow = false) {
        if (instance == null) {
            return;
        }

        if (isShow && instance.IsShowingShopBall) {
            isForceShow = true;
        }

        if (isShow || isForceShow) {
            if (VipMissionScript.instance != null && VipMissionScript.instance.IsShowed()) {
                return;
            }
        }

        instance.charactersPreview.SetActive(
            (RemoteConfigBase.instance.isShowCharactersInHome && isShow) || isForceShow);
    }

    public void SetActiveSongList(bool isActive) {
        mainHomeScript.SetActiveSongList(isActive);
        menuHome.SetActiveSongList(isActive);
    }

    public void UpdateCharactersPreview(int stateAnimation) {
        charactersPreview.RunAnimation(stateAnimation);
    }

    public void ShowCharactersInHome(bool isShowCharacters, bool isForceShow = false) {
        //bgHome.ShowHome(isShowCharacters);

        // force deactive if UserProgression is on
        if (UserProgressionController.EnableFeature) {
            mainDiskHome.gameObject.SetActive(false);

            if (!isShowCharacters && SongList.instance && SongList.instance.songPackChoosingScroller &&
                SongList.instance.songPackChoosingScroller.IsInitialized) {
                SongList.instance.UserProgress_UpdateDiskStageScroll(SongManager.UserProgress_CurrentSelectedPackLevel);
            }
        } else {
            mainDiskHome.gameObject.SetActive(!isShowCharacters);
        }

        if (_categoryScroller != null) {
            _categoryScroller.SetActive(!isShowCharacters);
        }

        ShowCharactersPreview(isShowCharacters, isForceShow);
        SetActiveShopBG(isShowCharacters);
    }

    public void ShowBottomMenu(bool isActive) {
        bottomMenuScript.SetActive(isActive);
        menuHome.SetActive(isActive);
    }

    private void UpdateMenuByFixedTopBar() {
        mainHomeScript.UpdateMenuByFixedTopBar();
        searchScript.UpdateMenuByFixedTopBar();
        menuHome.UpdateMenuByFixedTopBar();
    }

    public void StopScrollingScrollView() {
        mainHomeScript.StopScrollingScrollView();
    }

    #region Communication Action Data

    private void ClearOpenPopup(bool clearFreeGift = true, bool clearMission = true, bool clearSubscription = true,
                                bool clearAchievement = true) {
        if (BoardManager.instance != null) { // tắt leaderboard
            BoardManager.instance.Close();
        }

        if (clearFreeGift && FreeVideo.instance != null) {
            FreeVideo.instance.Close();
        }

        if (clearMission && SevenDayMissionUI.instance != null) {
            SevenDayMissionUI.instance.Close();
        }

        if (clearSubscription && SubscriptionUI.instance != null) {
            SubscriptionUI.instance.Close();
        }

        // close Diamonds shop
        if (Shop.instance) {
            if (tabContainerShopIap.IsShowed()) {
                tabContainerShopIap.Hide(Direction.Left);
            } else if (Shop.instance.gameObject.activeInHierarchy) {
                Shop.instance.Close();
            }
        }

        // TH-3657: close setting list
        if (_settingPopup != null && _settingPopup.gameObject.activeSelf) {
            _settingPopup.Close();
        }

        if (clearAchievement && _achievementsPopup != null && _achievementsPopup.gameObject.activeSelf) {
            SetActiveHome(true);
        }

        if (_settingPopup != null && _settingPopup.gameObject.activeSelf) { //Settings
            SetActiveHome(true); //Song List
        }

        if (IsShowingShopBall) {
            _shopScript.HideImmediately();
        }
    }

    public void NavigateSongList(string acm_id = "") {
        ClearOpenPopup();
        BtnHomeOnClick(false); // show home
        if (string.IsNullOrEmpty(acm_id)) {
            mainHomeScript.ScrollToTop(0f);
        } else {
            mainHomeScript.SetPositionToSong(acm_id, 0f, 0f);
        }
    }

    public void NavigateAchievement() {
        ClearOpenPopup(true, true, true, false);
        ShowAchievement();
    }

    public void NavigateFreeGift() {
        ClearOpenPopup(false, true, true, true);
        BtnHomeOnClick(false); // show home
        if (!FreeVideo.instance) {
            Util.ShowPopUp(PopupName.FreeVideo);
        }
    }

    public void Navigate7DayMission() {
        ClearOpenPopup(true, false, true, true);
        BtnHomeOnClick(false); // show home
        if (SevenDayMissionUI.instance == null) {
            Util.ShowPopUp(PopupName.SevenDayMission);
        }
    }

    public void NavigateBallShop(int index = -1) {
        ClearOpenPopup(true, true, true, true);
        ShowBallList(index);
    }

    public void NavigateDiscovery() {
        ClearOpenPopup();
        searchScript.IsNavigateFromCommunicationPopup = true;
        BtnSearchOnClick(false); // show discovery
    }

    public void NavigateDiscoveryGenre(string genre = "") {
        ClearOpenPopup();
        searchScript.IsNavigateFromCommunicationPopup = true;
        if (!searchScript.IsShowed()) {
            BtnSearchOnClick(false); // show discovery
        }

        searchScript.ShowGenre(genre);
    }

    public void NavigateDiscoveryArtist(string artist = "") {
        ClearOpenPopup();
        searchScript.IsNavigateFromCommunicationPopup = true;
        if (!searchScript.IsShowed()) {
            BtnSearchOnClick(false); // show discovery
        }

        searchScript.ShowArtist(artist);
    }

    public void NavigateDiscoveryAlbum(string album = "", string song = "") {
        ClearOpenPopup();
        searchScript.IsNavigateFromCommunicationPopup = true;
        if (!searchScript.IsShowed()) {
            BtnSearchOnClick(false); // show discovery
        }

        searchScript.ShowAlbum(album, song);
    }

    public void NavigateDiscoveryFavorite() {
        ClearOpenPopup();
        searchScript.IsNavigateFromCommunicationPopup = true;
        if (!searchScript.IsShowed()) {
            BtnSearchOnClick(false); // show discovery
        }

        searchScript.ShowFavorite();
    }

    public void NavigateLiveEvent(string location) {
        ClearOpenPopup();
        BtnLiveEventOnClick(location, false); // show Live event
    }

    #endregion

    /// <summary>
    /// Get location hiện tại để tracking event
    /// </summary>
    /// <returns></returns>
    public string GetLocation() {
        if (mainHomeScript.gameObject.activeSelf) {
            return TrackingLocation.home.ToString();
        } else if (liveEventScript != null && liveEventScript.gameObject.activeSelf) {
            return TrackingLocation.event_screen.ToString();
        } else if (searchScript.gameObject.activeSelf) {
            return TrackingLocation.discover.ToString();
        }

        return TrackingLocation.home.ToString();
    }

    public bool IsShowingLiveEventToolTip() {
        return bottomMenuScript.IsShowingLiveEventToolTip();
    }

    public int GetIdLiveEventToolTip() {
        return bottomMenuScript.GetIdLiveEventToolTip();
    }

    public void HideLiveEventToolTip(int idEvent) {
        bottomMenuScript.HideLiveEventToolTip(idEvent);
    }

    public void ForcedUpdateSongList() {
        mainHomeScript.ForcedUpdateSongList();
    }

    public void UpdateCategoryPosition(Vector2 newPos) {
        if (_cachedRtfCategoryScroller == null) {
            _cachedRtfCategoryScroller = (RectTransform) instance.CategoryScroller.transform;
        }

        _cachedRtfCategoryScroller.anchoredPosition = newPos;
    }

    public void UpdateCategoryUnmaskUI(bool isOn, Vector2 pos) {
        if (categoryScrollerUnmask != null) {
            categoryScrollerUnmask.gameObject.SetActive(isOn);
            categoryScrollerUnmask.anchoredPosition = pos;
        }
    }

    public void HideSettings() {
        if (_settingPopup != null) {
            _settingPopup.Hide();
        }
    }

    public async UniTaskVoid CountNotificationData(CancellationToken cancellationToken = default) {
        try {
            menuHome.SetActiveNotificationBox(false, 0);

            if (!remoteConfig.NotificationBox_IsEnable) {
                return;
            }

            Configuration.instance.LoadNotificationData();

            while (!Configuration.instance.isLoadDoneNotificationData) {
                await UniTask.Yield(cancellationToken);
            }

            List<IData> dataNotificationBox = Configuration.instance.dataNotificationBox;
            if (dataNotificationBox != null) {
                int count = dataNotificationBox.Count;
                if (count > 0) {
                    menuHome.SetActiveNotificationBox(true, count);
                }
            }
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    public void StartPreviewMusic() {
        if (_currentHomeSong != null) {
            PreviewSongController.instanceSafe.StartPreviewMusic(_currentHomeSong,
                location: LOCATION_NAME.home.ToString(), false);
            PreviewSongController.instanceSafe.AddPreviewSong(_currentHomeSong);
        }
    }

    public void TryPreviewSong(Song previewSong) {
        if (!_isWaitPreviewSong) {
            return;
        }

        _currentHomeSong = previewSong;
        StartPreviewMusic();
    }

    #region [User progression] Choosing stage buttons

    public Button ButtonGift => menuHome.btnGift;
    public Button Button7DayMission => menuHome.btn7DayMission;
    public Button ButtonAchievement => menuHome.btnAchievement;

    #endregion

    public void UserProgress_UpdateSongPackInformation(int unlockedSongCount, int collectedStars,
                                                       int numberOfSongInPack) {
        if (mainDiskHome != null && mainDiskHome is UISelectDiskPacks selectPacks) {
            selectPacks.UserProgress_UpdateSongPackInformation(unlockedSongCount, collectedStars, numberOfSongInPack);
        }
    }

    public void CheckNoticeDiscover(ChallengeState eventState) {
        bottomMenuScript.CheckNoticeDiscover(eventState);
    }

    public void ResetCurrentSong(Song localSong) {
        if (localSong == null) {
            return;
        }

        if (localSong != _currentHomeSong) {
            return;
        }

        _currentHomeSong = SongManager.instance.GetFirstSong();
    }

    public void UpdateUiNoti7DayMission() {
        menuHome.UpdateUiNoti7DayMission();
    }

    public void CheckShowSpecialOffer() {
        menuHome.CheckShowButtonSpecialOffer();
    }

    public void CheckScrollSongList(Vector2 rtfTargetAnchoredPosition) {
        bool isLockTopMenu = IsLockTopMenuForHome2;

        if (!isLockTopMenu) {
            menuHome.Scroll(rtfTargetAnchoredPosition);
        }
    }

    public GameObject CreateButtonDiscoverChallenge() {
        return menuHome.CreateButtonDiscoverChallenge();
    }

    public int GetMainMenuHeight() {
        return menuHome.GetMenuHeight();
    }

    public int GetAdjustFeatureStickyOffset() {
        return menuHome.GetAdjustFeatureStickyOffset();
    }

    // Manual cancellation control for auto unlock home action (had StopCoroutine)
    private CancellationTokenSource _autoUnlockCancellation;

    public void LockUserAction(bool isLock) {
        if (objLockHome) {
            objLockHome.SetActive(isLock);
        }

        // Cancel any existing auto unlock operation (equivalent to StopCoroutine)
        _autoUnlockCancellation?.Cancel();
        _autoUnlockCancellation?.Dispose();

        if (isLock) {
            if (SongList.instance != null) {
                SongList.instance.SongScroller.ScrollerAdapter.StopScrollingIfAny();
            }

            // Create new manual cancellation token
            _autoUnlockCancellation = new CancellationTokenSource();

            // Combine manual + auto-destroy tokens
            var linkedToken = CancellationTokenSource.CreateLinkedTokenSource(
                _autoUnlockCancellation.Token, this.GetCancellationTokenOnDestroy()).Token;

            IEAutoUnlockHomeAction(linkedToken).Forget();
        }
    }

    private async UniTaskVoid IEAutoUnlockHomeAction(CancellationToken cancellationToken = default) {
        try {
            await UniTask.Delay(TimeSpan.FromSeconds(5f), cancellationToken: cancellationToken);

            if (objLockHome) {
                objLockHome.SetActive(false);
            }
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    public void RegisterFullScreenPopupShowing(int instanceID) {
        _setFullScreenPopups ??= new HashSet<int>();
        _setFullScreenPopups.Add(instanceID);
    }

    public void UnregisterFullScreenPopup(int instanceID) {
        if (_setFullScreenPopups != null && _setFullScreenPopups.Contains(instanceID)) {
            _setFullScreenPopups.Remove(instanceID);
        }
    }

    public bool IsShowingFullScreenPopup() {
        return _setFullScreenPopups != null && _setFullScreenPopups.Count > 0;
    }

    public void ShowStarJourney() {
        if (menuHome) {
            menuHome.ShowStarJourney();
        }
    }

    public void ShowProfile() {
        if (menuHome) {
            menuHome.ShowProfile();
        }
    }

    public void AddDiamondsExplode(Transform source, Transform target, int amount, float explodeRadius = 2,
                                   Action onComplete = null) {
        if (!diamondFlyEffect.gameObject.activeSelf) {
            diamondFlyEffect.gameObject.SetActive(true);
        }

        if (!target) {
            target = menuHome.DiamondTransform;
        }

        diamondFlyEffect.AddDiamondsScatter(source, target, amount, explodeRadius, out float time, false,
            onDone: onComplete);
    }

    public UIMenuHome GetMenuHome() {
        return menuHome;
    }
}