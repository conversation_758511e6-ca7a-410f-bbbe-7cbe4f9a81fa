using System;
using System.Collections;
using System.Collections.Generic;
using GameCore.EndlessOffer;
using GameCore.LiveEvent.MysteryDoor;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using TilesHop.ChallengeOldUser;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Hybrid.TripleOffer;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.UserProgression;
using TilesHop.EconomySystem;
using TilesHop.GameCore.StarsJourney;
using TilesHop.MysteryBox;
using TilesHop.Cores.IAASegmentation;
using TilesHop.Cores.Popups.Home;
using TilesHop.LiveEvent.MilestoneEvent;
using UnityEngine;

public partial class HomeManager {
    /// <summary>
    /// map các popup type với Coroutine show tương ứng
    /// </summary>
    private Dictionary<PopupType, IEnumerator> _popupCoroutinesMapping;

    /// <summary>
    /// các popup đã có config thứ tự show trong popup-flow
    /// </summary>
    private Dictionary<PopupType, bool> _popupFlowShowedState;

    private static List<HomePopupFlowConfig> _popupOrderingConfigs;

    public bool isInPopupFlow { get; private set; } = false;

    #region Popup flow

    /// <summary>
    /// Maps all types of popups that can appear on the home screen.
    /// Configured popups will be shown first, followed by the remaining ones in the map.
    /// </summary>
    private void SetupPopupFlow() {
        _popupFlowShowedState = new Dictionary<PopupType, bool>();
        _popupCoroutinesMapping = new Dictionary<PopupType, IEnumerator> {
            {PopupType.CONSENT, IEShowConsent()},
            {PopupType.SUBSCRIPTION, IEShowSubscription()},
            {PopupType.RATE_US, IEShowRateUs()},
            {PopupType.FORCE_UPDATE, IEShowForceUpdate()},
            {PopupType.ECONOMY_BOOST, IEShowEconomyBoost()},
            {PopupType.MYSTERY_BOX, IEShowMysteryBox()},
            {PopupType.STARTER_PACK, IEShowStarterPack()},
            {PopupType.SPECIAL_OFFER, IEShowSpecialOffer()},
            {PopupType.GEMS_OLD_USER, IEShowOldUserGemPopup()},
            { PopupType.OLD_USER_BLE, IEShowOldUserRewardBLE() },
            {PopupType.SEASONAL_PACK, IEShowIAPSeasonalPack()},
            {PopupType.CHALLENGE_OLD_USER, IEShowChallengeOldUser()},
            {PopupType.COMMUNICATION_SYSTEM, IEShowIngamePopup()},
            {PopupType.ONBOARDING, IEShowGeneralOnboarding()},
            {PopupType.VIP_DAILY_REWARD, IEShowVipDailyReward()},
            {PopupType.ONBOARDING_BOOSTER, BoosterManager.IECheckShowUnlockBooster()},
            {PopupType.IAP_TRIPLE_OFFERS, IAPTripleOffers.IECheckAutoPopup(LOCATION_NAME.home)},
            {PopupType.ENDLESS_OFFERS, EndlessOffer.IECheckAutoPopup(LOCATION_NAME.home)},
            {PopupType.MYSTERY_DOOR, MysteryDoorManager.IECheckAutoPopupHome()},
            {PopupType.MILESTONE_EVENT, MilestoneEvent.IEShowMilestoneEvent()},
        };
    }

    private IEnumerator IEShowPopupOldFlow() {
        // star journey onboarding
        if (UIMenuHome.needOnboardStarJourney) {
            yield return Inwave.Utils.WaitUntil(StarsJourneyManager.CheckCompletedStarFly);
            yield return StarsJourneyManager.instanceSafe.IEShowOnboardingStarJourney();
        }

        // similar to IEShowSubscription()
        if (SubscriptionController.IsUserSubsWeek() && SubsOfferMonthlyScript.IsCanShow()) {
            SubscriptionController.ShowSubscriptionOfferMonthly();
        }

        bool autoShowSub = !SubscriptionController.fisrtSession ||
                           UserProperties.GetPropertyInt(UserProperties.song_start) >=
                           remoteConfig.Subscription_FirstSession_SongStart;

        if (autoShowSub && SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.session.ToString(), true)) {
            //
        } else if (autoShowSub &&
                   SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.DiscountOffer.ToString(), true)) {
            //
        } else {
            yield return IEShowRateUs();
        }

        yield return IEShowForceUpdate();
        SubsOfferNoAdsScript.CheckTriggerLogicShow();

        // need to wait until the VIP subscription popup close before entering popup flow
        while (SubscriptionController.subPopupObject != null && SubscriptionController.subPopupObject.activeInHierarchy) {
            yield return null;
        }

        yield return IEShowOldUserGemPopup();

        yield return IEShowOldUserRewardBLE();

        // Offer1
        yield return IAPTripleOffers.IECheckAutoPopup(LOCATION_NAME.home);

        // Offer2
        yield return EndlessOffer.IECheckAutoPopup(LOCATION_NAME.home);

        // LE2
        yield return MysteryDoorManager.IECheckAutoPopupHome();

        // LE3
        yield return MilestoneEvent.IEShowMilestoneEvent();

        // Seasonal pack
        yield return IEShowIAPSeasonalPack();

        // Hardcore challenge
        yield return IEShowTooltipDiscoveryChallenge();

        // others
        yield return IEShowEconomyBoost();
        yield return IEShowMysteryBox();

        yield return IEShowStarterPack();
        yield return IEShowSpecialOffer();

        yield return IEShowChallengeOldUser();
        yield return IEShowIngamePopup();

        yield return BoosterManager.IECheckShowUnlockBooster();
    }

    private IEnumerator IEShowOrderedPopupFlow() {
        if (!ProcessPopupOrderConfig(ref _popupOrderingConfigs)) {
            yield return IEShowPopupOldFlow();

            yield break;
        }

        int daydiff = UserProperties.GetDayDiff();
        if (daydiff > 0) {
            _popupOrderingConfigs.Sort(ComparePopupFlowForOldUser);
        }

        if (UIMenuHome.isShowStarJourneyBar) {
            yield return Inwave.Utils.WaitUntil(StarsJourneyManager.CheckCompletedStarFly);
        }

        // show các popup đã có config thứ tự
        foreach (HomePopupFlowConfig popupOrderingConfig in _popupOrderingConfigs) {
            var popupType = popupOrderingConfig.type;
            if (!_popupCoroutinesMapping.ContainsKey(popupType)) {
                continue;
            }

            _popupFlowShowedState[popupType] = true;
            yield return _popupCoroutinesMapping[popupType];
        }

        // show các popup còn lại không có config thứ tự
        foreach (PopupType popupType in _popupCoroutinesMapping.Keys) {
            if (!_popupFlowShowedState.ContainsKey(popupType)) {
                yield return _popupCoroutinesMapping[popupType];
            }
        }
    }

    private bool ProcessPopupOrderConfig(ref List<HomePopupFlowConfig> popupFlowConfigs) {
        try {
            popupFlowConfigs = CSVReader.ProcessDataCSV<HomePopupFlowConfig>(
                RemoteConfigBase.instance.HomePopupFlow_Ordering, null);

            if (popupFlowConfigs == null) {
                CustomException.Fire("[ProcessPopupOrderConfig]",
                    $"Cannot get List Ordered popup: {RemoteConfigBase.instance.HomePopupFlow_Ordering}");
                return false;
            }

            return true;
        } catch (Exception e) {
            CustomException.Fire("[ProcessPopupOrderConfig]", $"Wrong config: HomePopupFlow_Ordering => {e.Message}");
            return false;
        }
    }

    private int ComparePopupFlowForOldUser(HomePopupFlowConfig popup1, HomePopupFlowConfig popup2) {
        return popup1.orderOldUser.CompareTo(popup2.orderOldUser);
    }

    private IEnumerator IEShowConsent() {
        //Edge case check cannot show Consent form due to not finished loading
        if (CMPWrapper.IsEeaOrUK) {
            CMPWrapper.instance.TryLoadConsentForm();
            yield return Inwave.Utils.WaitUntil(() => CMPWrapper.instance.ConsentCheckPassed);
        }
    }

    /// <summary>
    /// Subscription popup
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowSubscription() {
        if (SubscriptionController.IsUserSubsWeek() && SubsOfferMonthlyScript.IsCanShow()) {
            if (SubscriptionController.ShowSubscriptionOfferMonthly(out GameObject subMonthly)) {
                yield return Inwave.Utils.WaitUntil(() => subMonthly == null || !subMonthly.activeSelf);
            }
        }

        bool autoShowSub = !SubscriptionController.fisrtSession ||
                           UserProperties.GetPropertyInt(UserProperties.song_start) >=
                           remoteConfig.Subscription_FirstSession_SongStart;

        if (autoShowSub) {
            var popupSubs = SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.session.ToString(), true);
            if (!popupSubs) {
                popupSubs = SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.DiscountOffer.ToString(), true);
            }

            yield return new WaitWhile(() => popupSubs != null);
        }

        // if user purchased VIP subscription successfully, wait VIP reward popup before back to Popup Flow
        while (VipNotice.isShowing) {
            yield return null;
        }
    }

    /// <summary>
    /// Rate us popup
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowRateUs() {
        if (SubscriptionController.CheckAndShowBonus()) {
            yield break;
        }
        
        RateUs.CheckAndOpenRate();

        // chờ popup rate
        yield return Inwave.Utils.WaitUntil(() => RateUs.instancedPopup == null || !RateUs.instancedPopup.activeSelf);
    }

    /// <summary>
    /// Communication Popup
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowIngamePopup() {
        if (!remoteConfig.CommunicationSystem_IsOn) {
            yield break;
        }

        while (!liveEventManager.IsInit) {
            // chờ live event init xong đã
            yield return null;
        }

        NotificationDeepLink.Activate();
        if (!NotificationDeepLink.CheckStartupScene() && !isShowPopupCommunication) {
            while (!IngamePopupSystem.IsReady) {
                yield return null;
            }

            isShowPopupCommunication = true;
            IngamePopupSystem.Instance.Show();
            yield return new WaitWhile(() => IngamePopupSystem.IsShowing);
        }
    }

    /// <summary>
    /// Tooltip Discovery Challenge
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowTooltipDiscoveryChallenge() {
        GameObject popup = null;
        if (DiscoveryChallengeManager.IsEnable() && DiscoveryChallengeManager.instanceSafe.TryActive()) {
            popup = DiscoveryChallengeManager.instanceSafe.CheckShowToolTipHome(transform);
            if (popup != null) {
                popup.SetActive(true);
            }

            yield return new WaitWhile(() => popup && popup.gameObject.activeSelf);
        }
    }

    /// <summary>
    /// Onboarding Flow
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowGeneralOnboarding() {
        // onboards to the Star Journey feature
        if (UIMenuHome.needOnboardStarJourney) {
            yield return StarsJourneyManager.instanceSafe.IEShowOnboardingStarJourney();
        }

        // show tooltip ở nút tab discovery, user có khả năng click để chuyển sang tab discovery
        yield return StartCoroutine(IEShowTooltipDiscoveryChallenge());
    }

    /// <summary>
    /// VIP daily reward popup
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowVipDailyReward() {
        yield return SubscriptionController.IEShowVipDailyReward();
    }

    public void ShowChallengeOldUser() {
        StartCoroutine(IEShowChallengeOldUser());
    }

    /// <summary>
    /// Chalenge Old User
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowChallengeOldUser() {
        GameObject popup = null;

        if (ChallengeOldUserController.IsEnableFeature) {
            popup = ChallengeOldUserController.instanceSafe.ShowPopupStartSession();
            if (popup != null) {
                popup.SetActive(true);
            }

            yield return new WaitWhile(() => popup != null && popup.gameObject.activeSelf);
        }
    }

    private IEnumerator IEShowIAPSeasonalPack() {
        if (!SeasonalPackManager.isEnable || !SeasonalPackManager.isInstanced || SeasonalPackManager.isEndEvent) {
            yield break;
        }

        SeasonalPackPopup.ShowPopup(true, EconomyIAPTracker.TRACK_LOCATION.auto);

        // nếu đã từng setup popup và không phải VIP
        if (SeasonalPackManager.isEnable && SeasonalPackManager.isActive && !SeasonalPackManager.isEndEvent) {
            menuHome.CreateButtonSeasonalPack();
        }

        while (SeasonalPackPopup.isShowing) {
            yield return null;
        }

        // chờ navigate đến shop ball ở Halloween pack popup
        if (waitOnStart != null) {
            yield return waitOnStart;

            waitOnStart = null;
        }
    }

    /// <summary>
    /// Force Update
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowForceUpdate() {
        if (remoteConfig.ForceUpdate != 0 && !Configuration.instance.isShowedForceUpdate) {
            GameObject popup = null;
            try {
                Version version1 = new(remoteConfig.LatestVersion);
                Version version2 = new(Application.version);
                if (version1 > version2) {
                    Configuration.instance.isShowedForceUpdate = true;
                    popup = Util.ShowPopUp(PopupName.ForceUpdate);
                    if (popup != null) {
                        popup.SetActive(true);
                    }
                }
            } catch (Exception e) {
                Debug.Log(e.ToString());
            }

            yield return new WaitWhile(() => popup && popup.activeSelf);
        }
    }

    /// <summary>
    /// Economy Boost popup
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowEconomyBoost() {
        DisableCallbackUI popup = Util.CheckShowEconomyBoost(LOCATION_NAME.home.ToString());
        if (popup == null) {
            yield break;
        }

        yield return new WaitWhile(() => popup && popup.gameObject.activeSelf);
    }

    
    /// <summary>
    /// Popup reward user convert from baseline to new feature Booster & LiveEvent
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowOldUserRewardBLE() {
        yield return Util.CheckShowOldUserRewardBLE();

        while (UIBoosterOldUser.isShowing) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
    }

    /// <summary>
    /// Mystery Box popup
    /// </summary>
    /// <returns></returns>
    private IEnumerator IEShowMysteryBox() {
        yield return new WaitWhile(() =>
            SubscriptionUI.instance != null && SubscriptionUI.instance.gameObject.activeSelf);

        GameObject popup = null;

        if (MysteryBox.HaveAutomaticPopup) {
            popup = MysteryBox.instance.ShowMysteryBox(MysteryBoxLocation.AutoShow);
            if (popup != null) {
                popup.SetActive(true);
            }
        }

        yield return new WaitWhile(() => popup && popup.activeSelf);
    }

    private IEnumerator IEShowStarterPack() {
        GameObject popup = null;
        if (Configuration.instance.CanShowStarterPackAutomatic()) {
            popup = EconomyOfferStarterPack.Show();
            if (popup != null) {
                popup.SetActive(true);
                popup.GetComponent<EconomyOfferStarterPack>().SetLocation(EconomyIAPTracker.TRACK_LOCATION.auto);
                EconomyOfferStarterPack.isAutoShowed = true;
            }

            Configuration.instance.AutomaticShowStarterPack();
            EconomyIAPTracker.Track_StarterPackEnable();
        }

        while (popup != null && popup.gameObject.activeSelf) {
            yield return null;
        }
    }

    private IEnumerator IEShowSpecialOffer() {
        // ver2 only auto show at result screen
        if (remoteConfig.Economy_SpecialOffer_Version == 2) {
            yield break;
        }

        GameObject popup = null;
        bool isLoading = false;
        if (remoteConfig.Economy_IsEnable) {
            if (SpecialOfferManager.isInstanced &&
                SpecialOfferManager.instanceSafe.CanShowPopup(UISpecialOffer.ShowType.auto,
                    LOCATION_NAME.home.ToString()) && SpecialOfferManager.instanceSafe.version != 3) {
                
                isLoading = true;
                SpecialOfferManager.instanceSafe.ShowPopup(UISpecialOffer.ShowType.auto, (callback) => {
                    isLoading = false;
                    popup = callback;
                });
            }
        }

        while (isLoading || (popup != null && popup.gameObject.activeSelf)) {
            yield return null;
        }
    }

    private IEnumerator IEShowOldUserGemPopup() {
        bool isShowGemsPopup = Configuration.CheckToShowOldUserGemPopup();
        if (isShowGemsPopup) {
            GameObject gemsPopup;
            bool isNewUser = PlayerPrefs.HasKey(CONFIG_STRING.ECONOMY_TUTORIAL) &&
                             !RemoteConfigBase.instance.UserProgression_TutoRewardLocatedAtResult;

            if (isNewUser) {
                gemsPopup = Util.ShowPopUp(CONFIG_STRING.TutorialReward);
            } else {
                gemsPopup = Util.ShowPopUpCanvasHeight(PopupName.OldUserGemPopup);
            }

            yield return new WaitWhile(() => gemsPopup && gemsPopup.gameObject.activeSelf);
        }

        if (!PlayerPrefs.HasKey("Onboard_UnlockSongByGems")) {
            GameObject onboarding = null;
            if (UserProgressionController.EnableFeature) {
                onboarding = Util.ShowPopUp(PopupName.OldUserOnboardingEconomyWithUserProgress);
            } else if (isShowGemsPopup ||
                       (StarsJourneyManager.isEnable && RemoteConfigBase.instance.Economy_IsEnable)) {
                onboarding = Util.ShowPopUp(PopupName.OldUserOnboardingEconomy);
            }

            PlayerPrefs.SetInt("Onboard_UnlockSongByGems", 1);
            yield return new WaitWhile(() => onboarding && onboarding.activeSelf);
        }
    }

    #endregion
}