using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;
using DG.Tweening;
using GameCore.LiveEvent.MysteryDoor;
using JetBrains.Annotations;
using TilesHop.Cores.Pooling;
using TilesHop.EconomySystem;
using TilesHop.LiveEvent;
using TilesHop.LiveEvent.MilestoneEvent;
using Resources = UnityEngine.Resources;

public partial class TopBar : MonoBehaviour {
    private const string UNLIMITED_REVIVE_TIME_HUD = "UI/UnlimitedReviveCountdown_Ingame";
    
    // ~~~~~~~~~~~~~~~~~~~~~~ static ~~~~~~~~~~~~~~~~~~~~~~
    public static TopBar instance;
    private       int    currentDiamond;
    private       int    targetDiamond;
    private       bool   needUpdateTargetDiamond;
    private       int    updateSign        = 1;
    private       int    updateAmount      = 0;
    private       int    updateAmountFrame = 1;

    // ~~~~~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~~~~~
    [SerializeField] private GameObject diamondContainer;

    [SerializeField] private GameObject groupDiamond;
    [SerializeField] private Image      iconDiamond;
    [SerializeField] private Text       diamondText;
    [SerializeField] private Text       diamondPlusText;
    [SerializeField] private Animation  diamondPlusAnim;

    [SerializeField] private UIScoreIngame scoreIngame;

    [Space] [SerializeField] private Button btnGameSettings;
    [SerializeField]         private Button btnGameSettingsV2;

    private Button _btnGameSettings;

    [SerializeField] private Button                 btnShopInGame;
    [SerializeField] private Button                 btnShopInGame_v2;
    [SerializeField] private Button                 btnSelectThemes;
    [SerializeField] private MultiDifficultUIScript multiDifficultUIScript;
    [SerializeField] private CanvasGroup            objBeginStart;

    [SerializeField] private Transform  tfContainer;
    [SerializeField] private GameObject songDetail;
    [SerializeField] private Text[]     textToSwapFont;

    [Header("Live event UI")] [SerializeField]
    private GameObject groupToken;

    [SerializeField]             private Image      imgToken;
    [SerializeField]             private Image      imgCollectedToken;
    [SerializeField]             private Text       txtToken;
    [SerializeField] [CanBeNull] private GameObject objTokenNotice;

    [Space]
    [Header("Other token HUDs")]
    [SerializeField] private Transform hudContainer;
    [SerializeField] private IngameTokenHUD milestoneTokenHUD;
    
    [Space]
    [Header("Pause AP")] [SerializeField] private GameObject objButtonPause;
    [SerializeField]                      private Button     btnPause;
    
    private ProgressBarScript _progressBarScript;
    public ProgressBarScript progressBar => _progressBarScript;

    public bool IsShowDiamonds => groupDiamond && groupDiamond.activeInHierarchy;

    private Button _btnShopInGame;

    public Button BtnShopInGame {
        get {
            if (_btnShopInGame == null) {
                _btnShopInGame = remoteConfig.ShopBall_EntryPointV2_IsEnable ? btnShopInGame_v2 : btnShopInGame;
            }

            return _btnShopInGame;
        }
    }

    public Transform DiamondGroup {
        get { return groupDiamond.transform; }
    }

    // ~~~~~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~~~~~
    private int   _currentToken; // = -1;???
    private Tween _tweenUpdateToken;

    private GameObject _objSpecialOfferBenefit;
    private GameObject _objUnlimitedReviveCountdown;
    private GameObject _objTooltipShopBallV2;

    private const string SHOWED_AP_SHOPBALL = "Showed_AP_ShopBall";

    private bool CanShowProgressBar {
        get {
            //Economy TH-1194: lại mở lại progressbar nhưng k có gem milestone
            // if (remoteConfig.Economy_IsEnable && NotesManager.instance.song.isTutorialSong) {
            //     //Economy TH-1194: Hide the progress bar and diamond in tutorial game play
            //     return false;
            // }
            return remoteConfig.OnboardingFlow_EnableProgressBar && !gameController.isUpdateSensitive;
        }
    }

    private RemoteConfig remoteConfig => RemoteConfigBase.instance;
    private LiveEventManager liveEventManager => LiveEventManager.instance;


    #region Alternated Top Gems HUDS

    private Text gemsCountText {
        get {
            if (isAlterHUD && gemsHUDHolder && extraHudContainerObject.activeSelf) {
                return gemsHUDHolder.txtGems;
            }
            return diamondText;
        }
    }

    private Text gemsPlusText {
        get {
            if (isAlterHUD && gemsHUDHolder && extraHudContainerObject.activeSelf) {
                return gemsHUDHolder.txtPlus;
            }
            return diamondPlusText;
        }
    }

    private Animation gemsPlusAnim {
        get {
            if (isAlterHUD && gemsHUDHolder && extraHudContainerObject.activeSelf) {
                return gemsHUDHolder.plusAnim;
            }
            return diamondPlusAnim;
        }
    }

    #endregion
    

    [HideInInspector] public SensitiveToolScript _sensitiveToolScript;

    private GameController _cacheGameController;

    // private bool enableUPTopbar => false;//UserProgressionController.EnableFeature;
    // private UITopbarUP     _topbarUP;
    // public  UITopbarUP     TopbarUserProgress => _topbarUP;

    private GameController gameController {
        get {
            if (_cacheGameController == null) {
                _cacheGameController = SingletonManager.instance.GetSingleton<GameController>();
            }

            return _cacheGameController;
        }
    }

    [SerializeField] private GameObject shopBallNotice;
    public                   Button     diamondNotice;

    [SerializeField] private GameObject objSongName;

    public static event Action OnTokenClicked;

    #region Unity Methods

    private void Awake() {
        instance = this;
        currentDiamond = Configuration.instance.GetDiamonds();
        targetDiamond = currentDiamond;

        UpdateDiamondText(currentDiamond);

        if (BtnShopInGame != null) {
            BtnShopInGame.onClick.AddListener(btnShopInGameOnClick);
        }

        btnShopInGame.gameObject.SetActive(false);
        btnShopInGame_v2.gameObject.SetActive(false);

        if (btnSelectThemes != null) {
            btnSelectThemes.onClick.AddListener(BtnSelectThemesOnClick);
        }

        if (btnGameSettings) {
            btnGameSettings.gameObject.SetActive(false);
        }

        if (btnGameSettingsV2) {
            btnGameSettingsV2.gameObject.SetActive(false);
        }

        _btnGameSettings = RemoteConfigBase.instance.PauseQuitAP_ButtonSettingWithBackground
            ? btnGameSettingsV2
            : btnGameSettings;

        if (_btnGameSettings != null) {
            _btnGameSettings.onClick.AddListener(BtnGameSettingsOnClick);
            _btnGameSettings.gameObject.SetActive(true);
        }

        if (objBeginStart) {
            objBeginStart.gameObject.SetActive(false);
        }

        if (diamondNotice != null) {
            if (remoteConfig.Onboarding_Result_IntroGem_Enable &&
                remoteConfig.Onboarding_Result_IntroGem_RequiredClick) {
                diamondNotice.onClick.AddListener(ShowBuyNotice);
            } else {
                diamondNotice.GetComponent<Image>().raycastTarget = false;
            }
        }

        SetActiveTokens(false);
    }

    private void Start() {
        InitSensitiveTool();

        if (groupToken.TryGetComponent(out Button btnToken)) {
            btnToken.onClick.AddListener(() => { OnTokenClicked?.Invoke(); });
        }

        if (remoteConfig.Theme_IsEnableSelectTheme && Configuration.instance.isAutoOpenSelectTheme) {
            OpenThemeSelection();
            Configuration.instance.isAutoOpenSelectTheme = false; //this late
            SceneFader.instance.HideOverlay();

            TrackingSelectTheme.LogEventItemLoad();
        }

        if (btnPause) {
            btnPause.onClick.AddListener(LogPauseButtonClick);
        }
    }

    private void OnEnable() {
        ChangeTargetDiamond(Configuration.instance.GetDiamonds());
        if (BtnShopInGame != null) {
            bool isShowBtnShop = ThemeManager.IsEnableShopInGame();
            ShowButtonShopInGame(isShowBtnShop);
            if (isShowBtnShop) {
                StartCoroutine(ShopballNoticeAppear());
            }
        }

        if (btnSelectThemes != null) {
            btnSelectThemes.gameObject.SetActive(ThemeManager.IsEnableSelectThemes());
        }

        if (multiDifficultUIScript != null) {
            bool isTutorialSong = NotesManager.instance.song.isTutorialSong;
            multiDifficultUIScript.SetActive(remoteConfig.NotesDifficult_IsEnable && !isTutorialSong);
        }

        LiveEventManager.OnChangeToken += LiveEventManagerOnOnChangeToken;
        MysteryDoorManager.OnChangeDrill += OnChangeTokenAmount;
        MysteryDoorManager.TriggerDrill += MysteryDoorManagerOnTriggerDrill;
        
        //listenters for LE3
        if (MilestoneEvent.isAvailable) {
            MilestoneEvent.instanceSafe.OnStartTriggerItem += HandleOnStartTriggerMilestoneToken;
            MilestoneEvent.instanceSafe.OnSaveCollectedTokens += HandleOnSaveCollectedTokens;
        }
    }
    
    private void OnDisable() {
        LiveEventManager.OnChangeToken -= LiveEventManagerOnOnChangeToken;
        MysteryDoorManager.OnChangeDrill -= OnChangeTokenAmount;
        MysteryDoorManager.TriggerDrill -= MysteryDoorManagerOnTriggerDrill;
        if (btnPause) {
            btnPause.onClick.RemoveListener(LogPauseButtonClick);
        }
        
        // Kill active tweens to prevent memory leaks
        if (_tweenUpdateToken != null && _tweenUpdateToken.IsActive()) {
            _tweenUpdateToken.Kill();
            _tweenUpdateToken = null;
        }
        
        if (MilestoneEvent.isAvailable) {
            MilestoneEvent.instanceSafe.OnStartTriggerItem -= HandleOnStartTriggerMilestoneToken;
            MilestoneEvent.instanceSafe.OnSaveCollectedTokens -= HandleOnSaveCollectedTokens;
        }
    }

    private void Update() {
        if (needUpdateTargetDiamond) {
            currentDiamond += updateAmountFrame * updateSign;
            if (currentDiamond <= targetDiamond && updateSign < 0) {
                needUpdateTargetDiamond = false;
                currentDiamond = targetDiamond;
            } else if (currentDiamond >= targetDiamond && updateSign > 0) {
                needUpdateTargetDiamond = false;
                currentDiamond = targetDiamond;
            }

            UpdateDiamondText(currentDiamond);
        }
    }

    #endregion

    private UIScoreIngame CreateUIScoreInGame() {
        UIScoreIngame prefab;
        if (Spawner.s != null && Spawner.s.platformManager != null &&
            Spawner.s.platformManager.prefabScoreIngame != null) {
            prefab = Spawner.s.platformManager.prefabScoreIngame;
        } else {
            string pathScore = ThemeManager.GetScoreIngameType();
            prefab = Resources.Load<UIScoreIngame>($"UI/{pathScore}");
        }

        UIScoreIngame scoreInGame = Instantiate(prefab, tfContainer);
        scoreInGame.transform.SetAsFirstSibling();
        return scoreInGame;
    }

    public void OnChangeDiamond(int change) {
        if (gameObject.activeInHierarchy) {
            StartCoroutine(PlayTextEffect(change));
        }
    }

    private void ChangeTargetDiamond(int target) {
        targetDiamond = target;
        if (targetDiamond != currentDiamond) {
            needUpdateTargetDiamond = true;
            updateSign = targetDiamond > currentDiamond ? 1 : -1;
            updateAmount = Mathf.Abs(targetDiamond - currentDiamond);
            updateAmountFrame = Mathf.RoundToInt((Time.deltaTime * updateAmount) * 5);
            if (updateAmountFrame == 0) {
                updateAmountFrame = 1;
            }
        }
    }

    private IEnumerator PlayTextEffect(int amount) {
        string updateString = amount > 0 ? $"+{amount.ToString()}" : $"{amount.ToString()}";

        yield return PlayAnimPlusDiamond(updateString);

        ChangeTargetDiamond(Configuration.instance.GetDiamonds());
    }

    public void SetScore(int score) {
        scoreIngame.SetScore(score);
    }

    public void UpdateScore(int score, int perfectCount) {
        if (gameController.isUpdateSensitive || gameController.isSensitiveClosing) {
            return;
        }

        scoreIngame.SetScore(score);

        if (!remoteConfig.EnablePerfectScoreFly) {
            scoreIngame.SetPerfectCount(perfectCount);
        } else {
            GameItems.instance.ShowFlyNumber(perfectCount);
        }

        if (perfectCount > 0) {
            scoreIngame.PlayPerfectVFX();
        } else {
            scoreIngame.PlayGreatVFX();
        }
    }

    public void ToggleDiamondContainer(bool enable) {
        if (diamondContainer == null) {
            return;
        }

        bool rootEnable = enable;
        if (remoteConfig.Economy_IsEnable) {
            enable = false;
        }

        // trong trường hợp game complete =>> force show diamonds
        if (rootEnable && UIController.ui != null && UIController.ui.gameover != null &&
            UIController.ui.gameover.gameObject.activeSelf) {
            enable = true;
        }

        // force enable for LE2
        if (MysteryDoorManager.CanEarnDrill(GameController.enableEndless) || liveEventManager.IsActiveEvent) {
            enable = true;
        }

        if (!enable && MilestoneEvent.isAvailable && MilestoneEvent.instanceSafe.CheckCanEarnToken()) {
            enable = true;
        }
        
        diamondContainer.SetActive(enable);

        if (_progressBarScript != null) {
            _progressBarScript.OnTopBarShowDiamondContainer(enable);
        }
    }

    public void ShowLeaderBoard() {
        SoundManager.PlayGameButton();
        if (Util.ShowLeaderBoard()) {
            AnalyticHelper.Button_Click(BUTTON_NAME.LeaderBoard_Total);
            if (Util.IsHomeScene() && HomeManager.instance.IsShowingShopBall)
                AnalyticHelper.LogEventUserAvatarImpression(location: "home_skinshop");
            else
                AnalyticHelper.LogEventUserAvatarImpression(location: "home");
        } else {
            ShowShop();
        }
    }

    public void ToggleScore(bool enable) {
        if (scoreIngame == null)
            return;

        scoreIngame.SetActive(enable);
    }

    public void AdjustScoreText(float xpos, float time) {
        if (scoreIngame == null)
            return;

        scoreIngame.AdjustScoreText(xpos, time);
    }

    public void GamePrepare() {
        if (extraHudContainerObject != null) {
            extraHudContainerObject.SetActive(false);
        }
        
        if (scoreIngame == null) {
            scoreIngame = CreateUIScoreInGame();
        }

        scoreIngame.SetScore(gameController.Score);
        scoreIngame.SetPerfectCount(0);

        if (CanShowProgressBar) {
            if (!_progressBarScript) {
                _progressBarScript = ProgressBarScript.Init(transform);
            }

            if (_progressBarScript) {
                _progressBarScript.Reset();
            }

            if (_btnGameSettings) {
                _btnGameSettings.gameObject.SetActive(false);
            }

            _progressBarScript.Show();
        } else if (_progressBarScript != null && _progressBarScript.IsActive()) {
            _progressBarScript.Hide(); // hide when prepare game
        }

        if (remoteConfig.ImproveSensitive_IsEnable && _btnGameSettings) {
            _btnGameSettings.gameObject.SetActive(true);
        }

        if (GameController.enableEndless && ChallengeMode.IsAcceptFromNormal) {
            //dont reset text token/diamond
        } else {
            bool mysteryDoor = MysteryDoorManager.CanEarnDrill(GameController.enableEndless);
            if (mysteryDoor || liveEventManager.CanEarnToken) {
                int totalToken = mysteryDoor
                    ? MysteryDoorManager.currentDrill
                    : liveEventManager?.GetCurrentLiveEvent()?.progress.TokenAmount ?? 0;
                txtToken.text = totalToken.ToString();
                txtToken.gameObject.SetActive(true);
                imgToken.sprite = mysteryDoor
                    ? MysteryDoorManager.iconItem
                    : liveEventManager?.GetCurrentLiveEvent()?.eventConfig?.IconToken;
                SetActiveTokens(true);
                SetActiveTokenNotice(false);
            } else {
                SetActiveTokens(false);
            }
        }

        if (MilestoneEvent.isAvailable && MilestoneEvent.instanceSafe.CheckCanEarnToken(GameController.enableEndless)) {
            SetActiveMilestoneTokenHUD(true);
        } else {
            SetActiveMilestoneTokenHUD(false);
        }

        SetActiveGroupDiamond(true);
    }

    public void GameStart() {
        ToggleScore(true);
        ToggleDiamondContainer(false);
        ToggleButtons(false);

        bool isShowSpecialTokenHUDs = false;

        bool mysteryDoor = MysteryDoorManager.CanEarnDrill(GameController.enableEndless);
        if (mysteryDoor || liveEventManager.IsActiveEvent) {
            SetActiveTokens(true);
            _btnGameSettings.gameObject.SetActive(false);
            _currentToken = gameController.GetTokenEarnedByType(TokenType.MysteryDoor_Drill);
            
            txtToken.text = $"+{_currentToken.ToString()}";
            imgToken.sprite = mysteryDoor
                ? MysteryDoorManager.iconItem
                : liveEventManager?.GetCurrentLiveEvent()?.eventConfig?.IconToken;
            
            imgCollectedToken.sprite = imgToken.sprite;
            isShowSpecialTokenHUDs = true;
		}

        if (milestoneTokenHUD != null && milestoneTokenHUD.gameObject.activeSelf) {
            var earnedToken = GameController.instance.DictToken.GetValueOrDefault(TokenType.MilestoneToken, 0);
            milestoneTokenHUD.txtTokenCount.text = $"+{earnedToken}";
            isShowSpecialTokenHUDs = true;
        }

        if (isShowSpecialTokenHUDs) {
            // to avoid gem HUD (total possessed gems) and
            // collected gem HUD (addition number of gems collected while playing) overlapping
            SetActiveGroupDiamond(false);
        }

        // spawn countdown object for unlimited revive time
        if (UnlimitedReviveManager.IsInUnlimitedRevive) {
            if (_objUnlimitedReviveCountdown == null) {
                var asset = Resources.Load<GameObject>(UNLIMITED_REVIVE_TIME_HUD);
                if(asset != null) {
                    _objUnlimitedReviveCountdown = Instantiate(asset, tfContainer);
                }
            }

            _objUnlimitedReviveCountdown.SetActive(true);
        } else if (_objUnlimitedReviveCountdown != null) {
            _objUnlimitedReviveCountdown.SetActive(false);
        }

        if (_objTooltipShopBallV2 != null) {
            _objTooltipShopBallV2.SetActive(false);
        }
    }

    public void GameStop() {
        scoreIngame.SetPerfectCount(0);
        if (objButtonPause) {
            objButtonPause.SetActive(false);
        }

        ActivePauseButton(true);
    }

    public void GameContinue() {
        // Hide the diamond HUD of the progress bar to avoid intersection with the gem HUD in the tokens group
        bool canEarnAnyToken = false;
        
        bool mysteryDoor = MysteryDoorManager.CanEarnDrill(GameController.enableEndless);
        if (mysteryDoor || liveEventManager.IsActiveEvent) {
            canEarnAnyToken = true;
        }

        if (MilestoneEvent.isAvailable && MilestoneEvent.instanceSafe.CheckCanEarnToken(GameController.enableEndless)) {
            canEarnAnyToken = true;
        }
        
        if (canEarnAnyToken && _progressBarScript != null) {
            _progressBarScript.OnTopBarShowDiamondContainer(false);
        }
    }

    public void ShowShop() {
        if (Shop.instance != null) {
            return;
        }

        if (diamondNotice != null && diamondNotice.IsActive()) {
            HideRequiredDiamondNotice();
            return;
        }

        if (UIController.ui == null || !UIController.ui.IsShowingContinueUI()) {
            if (SoundManager.instance != null) {
                SoundManager.PlayGameButton();
            }

            Util.ShowIAPShop(LOCATION_NAME.gameplay); // gần như k dùng nữa
            AnalyticHelper.Button_Click(BUTTON_NAME.Shop);
            if (Util.IsHomeScene() && HomeManager.instance.IsShowingShopBall) {
                AnalyticHelper.LogEventUserDiamondImpression("home_skinshop");
            } else {
                AnalyticHelper.LogEventUserDiamondImpression("home");
            }
        }
    }

    public void ShowFreeVideo() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.FreeVideo);
        FreeVideo.ShowPopUp();
    }

    public void SetProcess(float percent) {
        if (_progressBarScript) {
            _progressBarScript.SetProcess(percent);
        }
    }

    public void SetProcessByScore(float score) {
        if (_progressBarScript) {
            _progressBarScript.SetProcessByScore(score);
        }
    }

    public void SetProcessByTileEndless(int noteID) {
        if (_progressBarScript) {
            _progressBarScript.SetProcessByTileEndless(noteID);
        }
    }

    public void ShowEndlessMode(int round) {
        if (_progressBarScript) {
            _progressBarScript.ShowEndlessMode(round);
        }
    }

    public void UpdateStar(int stars) {
        if (_progressBarScript) {
            _progressBarScript.SetStar(stars);
        }
    }

    public void MoveStar(Vector3 position, int stars) {
        if (_progressBarScript) {
            _progressBarScript.MoveStar(position, stars);
        }
        GameItems.instance?.starManager?.EatCrown();
    }

    #region Shop In Game

    private void btnShopInGameOnClick() {
        if (gameController.game == GameStatus.LIVE) {
            return;
        }

        SoundManager.PlayGameButton();
        HideShopballNotice();
        UIController.ui.ShowShopInGame(ShopScript.PreviousLocation.ingame, false, null);
        AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.BALL_LIST_INGAME);

        PlayerPrefs.SetInt(SHOWED_AP_SHOPBALL, 1);
        if (_objTooltipShopBallV2 != null) {
            Destroy(_objTooltipShopBallV2);
        }
    }

    public void ToggleButtons(bool isShow) {
        bool isTutorialSong = NotesManager.instance.song.isTutorialSong;
        isShow = isShow && !isTutorialSong && !gameController.isUpdateSensitive && !gameController.isSensitiveClosing;

        if (BtnShopInGame != null) {
            bool showBtnShop = isShow && ThemeManager.IsEnableShopInGame();
            ShowButtonShopInGame(showBtnShop);
            if (showBtnShop) {
                Configuration.instance.StartCoroutine(ShopballNoticeAppear());
            }
        }

        if (btnSelectThemes != null) {
            btnSelectThemes.gameObject.SetActive(isShow && ThemeManager.IsEnableSelectThemes());
        }

        if (multiDifficultUIScript != null) {
            multiDifficultUIScript.SetActive(isShow && remoteConfig.NotesDifficult_IsEnable);
        }

        if (isShow && CheckToShowShopBallEntryPointV2Tooltip()) {
            if (_objTooltipShopBallV2 == null) {
                var tooltipPrefab = Resources.Load<GameObject>("UI/ToolTipShopBallInGameV2");
                if (tooltipPrefab != null) {
                    _objTooltipShopBallV2 = Instantiate(tooltipPrefab, btnShopInGame_v2.transform.parent);
                    _objTooltipShopBallV2.transform.localPosition = Vector3.zero;
                    _objTooltipShopBallV2.transform.SetSiblingIndex(btnShopInGame_v2.transform.GetSiblingIndex());
                }
            } else {
                _objTooltipShopBallV2.SetActive(true);
            }
        }

        if (objButtonPause) {
            objButtonPause.SetActive(CheckCanShowPauseButton() && !isShow);
        }
    }

    private bool CheckCanShowPauseButton() {
        return remoteConfig.PauseQuitAP_PauseButton_IsEnable && !Configuration.instance.isTutorial;
    }

    public void ActivePauseButton(bool isActive) {
        if (btnPause) {
            btnPause.interactable = isActive;
        }
    }

    public void LogPauseButtonClick() {
        var param = new Dictionary<string, object>() {
            {"source", "click_pause_ap"}
        };
        AnalyticHelper.LogEvent("pause_ap_click", param);
    }

    private bool CheckToShowShopBallEntryPointV2Tooltip() {
        return remoteConfig.ShopBall_EntryPointV2_IsEnable && !PlayerPrefs.HasKey(SHOWED_AP_SHOPBALL) &&
               UserProperties.GetPropertyInt(UserProperties.song_start) > 1; // show after 2 song_start
    }

    #endregion

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public void HidePauseButton() {
        if (objButtonPause) {
            objButtonPause.SetActive(false);
        }
    }

    public void ToggleUIWhenShowShop(bool isShow) {
        if (objSongName)
            objSongName.SetActive(isShow);
        BtnShopInGame.gameObject.SetActive(isShow);
    }

    #region Sensitive Popup

    private void BtnGameSettingsOnClick() {
        if (CheckCanShowExitButtonInSetting() && !Util.IsGameResultScene()) {
            UIController.ui.ShowGameSetttingV2();
        } else {
            UIController.ui.ShowGameSetting();
        }
    }

    private bool CheckCanShowExitButtonInSetting() {
        return Util.IsActionPhase() && RemoteConfig.instance.PauseQuitAP_ExitButton_IsEnable &&
               !Configuration.instance.isTutorial;
    }

    public void ShowSensitiveTool(bool isTutorialSong) {
        if (_sensitiveToolScript == null) {
            InitSensitiveTool();
        }

        //UIs
        if (_sensitiveToolScript != null) {
            string placement = isTutorialSong ? "ftue" : "game_setting";
            _sensitiveToolScript.SetActive(true, placement);
        }

        if (_progressBarScript != null) {
            _progressBarScript.Hide(); //hide when show sensitive tool
        }

        scoreIngame.SetActive(false);
        UIController.ui.ShowSongName(false);
        ToggleDiamondContainer(false);
        ToggleButtons(false);
        int ballId = Configuration.GetSelectedBall();
        if (ballId != 0) {
            Ball.b.ChangeBall(0);
        }

        UIController.ui.UpdateTextTapHold("SENSITIVE_TEXT");
    }

    public void OnSensitiveClose() {
        StartCoroutine(CSensitiveClose());
    }

    private IEnumerator CSensitiveClose() {
        gameController.isSensitiveClosing = true;
        //UI
        if (CanShowProgressBar && _progressBarScript != null) {
            _progressBarScript.Show();
        }

        scoreIngame.SetActive(true);
        UIController.ui.ShowSongName(true);
        ToggleDiamondContainer(true);

        UIController.ui.UpdateTextTapHold("REPLAY_TEXT");

        //logic
        bool isStopNow = gameController.game != GameStatus.LIVE;
        gameController.game = GameStatus.P_DIE;
        gameController.isUpdateSensitive = false;

        if (!isStopNow) {
            while (!Ball.b.isJumpLastTile) {
                yield return null;
            }

            yield return YieldPool.GetWaitForSeconds(0.3f);
        }

        float time = 1.5f;
        Ball.b.MoveForward(100, time);

        Spawner.s.ReloadDifficult(false, false);

        yield return YieldPool.GetWaitForSeconds(0.5f);

        objBeginStart.gameObject.SetActive(true);
        objBeginStart.alpha = 0;
        objBeginStart.DOFade(1, 0.5f);

        yield return YieldPool.GetWaitForSeconds(time - 0.5f);

        objBeginStart.DOFade(0, 0.5f).OnComplete(() => { objBeginStart.gameObject.SetActive(false); });

        gameController.isSensitiveClosing = false;
        ToggleButtons(true);

        Ball.b.UpdateTrail(false);

        int ballId = Configuration.GetSelectedBall();
        if (ballId != 0) {
            Ball.b.ChangeBall(ballId);
        }

        if (NotesManager.instance.song.isTutorialSong) {
            gameController.GameStart();
        } else {
            GameItems.instance.SetActiveFeetEffect(true, Ball.b.isCharacter, Ball.b.transCache.position);
        }
    }

    #endregion

    private void InitSensitiveTool() {
        if (remoteConfig.ImproveSensitive_IsEnable && tfContainer != null && _sensitiveToolScript == null) {
            GameObject popup = Util.GetPopup(PopupName.SensitiveTool, tfContainer);
            if (popup != null) {
                popup.SetActive(false);
                _sensitiveToolScript = popup.GetComponent<SensitiveToolScript>();
            }
        }
    }

    public void ShowTrySkinImage(int id, float percent) {
        if (_progressBarScript != null) {
            _progressBarScript.ShowSkinImage(id, percent);
        }
    }

    public void UpdateTrySkinImage(int id) {
        if (_progressBarScript != null) {
            _progressBarScript.UpdateTrySkinImage(id);
        }
    }

    public void HideTrySkinImage() {
        if (_progressBarScript != null) {
            _progressBarScript.HideSkinIcon();
        }
    }

    public IEnumerator ShopballNoticeAppear() {
        if (remoteConfig.Onboarding_Ingame_ShopballNotice_Enable &&
            PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ShopballAppear, 0) == 0 &&
            Configuration.GetGameLevel() >= remoteConfig.Onboarding_Ingame_ShopballNotice_LevelAppear &&
            (PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ProgressFirstStar, 0) == 1 ||
             _progressBarScript == null)) {
            while (UIController.ui == null)
                yield return null;

            UIController.ui.bgToolTip.gameObject.SetActive(true);
            shopBallNotice.SetActive(true);
            if (remoteConfig.Onboarding_Ingame_ShopballNotice_RequiredClick) {
                shopBallNotice.transform.parent.SetParent(UIController.ui.bgToolTip.transform);
            } else {
                Configuration.instance.StartCoroutine(SetNoticeParent());
            }
        }

        yield return null;
    }

    public void HideShopballNotice() {
        shopBallNotice.SetActive(false);
        shopBallNotice.transform.parent.SetParent(tfContainer.transform);
        UIController.ui.bgToolTip.HideRequired();
        PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_ShopballAppear, 1);
    }

    public void HideBlackBG() {
        if (remoteConfig.Onboarding_Ingame_ShopballNotice_RequiredClick || !BtnShopInGame.gameObject.activeSelf) {
            return;
        }

        shopBallNotice.SetActive(false);
        shopBallNotice.transform.parent.SetParent(tfContainer.transform);
        PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_ShopballAppear, 1);
        UIController.ui.bgToolTip.HideRequired();
    }

    IEnumerator SetNoticeParent() {
        yield return YieldPool.GetWaitForSeconds(0.1f);

        shopBallNotice.transform.parent.SetParent(UIController.ui.bgToolTip.transform);
    }

    public void SetProgressBarMileStone(int idMileStone, Action onComplete) {
        if (_progressBarScript != null) {
            _progressBarScript.SetMileStone(idMileStone, onComplete);
        } else {
            onComplete?.Invoke();
        }
    }

    public void ShowProgressbarNotice() {
        if (_progressBarScript != null)
            _progressBarScript.ShowToolTip();
    }

    public IEnumerator ShowDiamondNotice() {
        UIController.ui.bgToolTip.gameObject.SetActive(true);

        yield return YieldPool.GetWaitForSeconds(0.05f);

        diamondNotice.transform.parent.gameObject.SetActive(true);
        diamondContainer.transform.SetParent(UIController.ui.bgToolTip.transform);
        EconomyTrackingEvents.TrackOnboardGem(TRACK_LOCATION.tutorial_result);
    }

    public void HideRequiredDiamondNotice() {
        if (PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ResultDiamond, 0) == 1 ||
            remoteConfig.Onboarding_Result_IntroGem_RequiredClick || !diamondNotice.IsActive())
            return;

        PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_ResultDiamond, 1);
        diamondNotice.transform.parent.gameObject.SetActive(false);
        UIController.ui.gameover.ChangeTopBar();
        if (!UIController.ui.gameover.NeedShowUnlockSongByGemOnboarding)
            UIController.ui.bgToolTip.HideRequired();
        else
            ShowBuyNotice();
    }

    public void ShowBuyNotice() {
        diamondNotice.transform.parent.gameObject.SetActive(false);
        PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_ResultDiamond, 1);
        UIController.ui.gameover.ShowBuyNotice();
        EconomyTrackingEvents.TrackOnboardUnlock(TRACK_LOCATION.tutorial_result);
    }

    public void HideRequiredBuyNotice() {
        if (PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_UnlockSongByGem, 0) == 1 ||
            remoteConfig.Onboarding_Result_UnlockSongBuyGem_RequiredClick)
            return;

        PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_UnlockSongByGem, 1);
        diamondNotice.transform.parent.gameObject.SetActive(false);
        UIController.ui.gameover.firstSongItem.HideBuyNotice();
        UIController.ui.bgToolTip.HideRequired();
    }

    public ProgressBarScript GetProgressBarScript() {
        return _progressBarScript;
    }

    public IEnumerator IESetParentDiamondContainer(Transform parent) {
        yield return YieldPool.GetWaitForSeconds(0.1f);
        if (diamondContainer != null) {
            diamondContainer.transform.SetParent(parent.transform, true);
            SetActiveGroupDiamond(true);
            _btnGameSettings.gameObject.SetActive(true);
        }
    }

	public void SetParentDiamondContainer(Transform parent) {
		if (diamondContainer != null) {
			diamondContainer.transform.SetParent(parent.transform, true);
			SetActiveGroupDiamond(true);
			_btnGameSettings.gameObject.SetActive(true);
		}
	}

	public Transform GetParentDiamondContainer() {
        if (diamondContainer != null) {
            return diamondContainer.transform.parent;
        }

        return null;
    }

    public void ShowTokenInResult(Sprite icon, int amount, bool showAmount) {
        SetActiveTokens(true);
        songDetail.gameObject.SetActive(false);

        imgToken.sprite = icon;
        if (showAmount) {
            _currentToken = amount;
            txtToken.text = _currentToken.ToString();
            txtToken.gameObject.SetActive(true);
        } else {
            txtToken.gameObject.SetActive(false);
        }
    }

    public void ShowOtherTokensInResult() {
        if (milestoneTokenHUD != null) {
            milestoneTokenHUD.txtTokenCount.text = MilestoneEvent.instanceSafe.data.currentToken.ToString();
        }
    }

    public void SetActiveTokens(bool isActive) {
        groupToken.SetActive(isActive);
    }

    public void HideAllTokenHUDs() {
        SetActiveTokens(false);
        if (milestoneTokenHUD != null) {
            milestoneTokenHUD.gameObject.SetActive(false);
        }
    }

    public void SetActiveMilestoneTokenHUD(bool isActive) {
        if (isActive) {
            if (milestoneTokenHUD == null) {
                var asset = Resources.Load<IngameTokenHUD>("UI/IngameTokenHUD");
                if (asset != null) {
                    milestoneTokenHUD = Instantiate(asset, hudContainer);
                    milestoneTokenHUD.imgToken.sprite = MilestoneEvent.instanceSafe.tokenSprite;
                    milestoneTokenHUD.imgTokenCollected.sprite = MilestoneEvent.instanceSafe.tokenSprite;
                    milestoneTokenHUD.txtTokenCount.text = MilestoneEvent.instanceSafe.data.currentToken.ToString();
                }
            } else {
                milestoneTokenHUD.gameObject.SetActive(true);
            }
        } else if (milestoneTokenHUD != null) {
            milestoneTokenHUD.gameObject.SetActive(false);
        }
    }

    public void SetActiveTokenNotice(bool isActive) {
        if (objTokenNotice) {
            objTokenNotice.SetActive(isActive);
        }
    }

    private void LiveEventManagerOnOnChangeToken(int idEvent, int tokenAmount) {
        OnChangeTokenAmount(tokenAmount);
    }

    private void OnChangeTokenAmount(int tokenAmount) {
        if (!groupToken.activeInHierarchy) {
            return;
        }

        _tweenUpdateToken?.Complete();

        int amountChange = Mathf.Abs(tokenAmount - _currentToken);
        float time = Mathf.Clamp(amountChange / 15f, 0.2f, 0.5f);
        _tweenUpdateToken = DOTween.To(() => _currentToken, x => _currentToken = x, tokenAmount, time).OnUpdate(() => {
            txtToken.text = _currentToken.ToString();
        }).OnComplete(() => {
            _currentToken = tokenAmount;
            txtToken.text = _currentToken.ToString();
            if (gameObject.activeSelf) {
                SetActiveTokenNotice(liveEventManager.HaveNotice);
            }
        });
    }

    public void OnChangeCollectedToken(TokenType type, int tokenAmount) {
        if (!groupToken.activeInHierarchy) {
            return;
        }

        _tweenUpdateToken?.Complete();

        int amountChange = Mathf.Abs(tokenAmount - _currentToken);
        _tweenUpdateToken = DOTween.To(() => _currentToken, x => _currentToken = x, tokenAmount, amountChange / 150f)
            .OnUpdate(() => {
                this.txtToken.text = $"+{_currentToken.ToString()}";
                //
            }).OnComplete(() => {
                this._currentToken = tokenAmount;
                this.txtToken.text = $"+{_currentToken.ToString()}";
            });
    }

    public void ShowDiamond(bool isShow, bool canInteract) {
        if (diamondContainer != null) {
            diamondContainer.SetActive(isShow);
        }

        if (groupToken.TryGetComponent(out Button btnToken)) {
            btnToken.interactable = canInteract;
        }
    }

    public void ShowButtonShopInGame(bool isShow) {
        if (BtnShopInGame != null) {
            BtnShopInGame.gameObject.SetActive(isShow);
        }
    }

    private void BtnSelectThemesOnClick() {
        if (OpenThemeSelection())
            return;

        TrackingSelectTheme.LogEventButtonClick();
    }

    private bool OpenThemeSelection() {
        if (gameController.game == GameStatus.LIVE) {
            return true;
        }

        SoundManager.PlayGameButton();
        Direction direction = Configuration.instance.isAutoOpenSelectTheme ? Direction.None : Direction.Bottom;
        UIController.ui.ShowSelectThemes(direction);
        AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.select_theme);
        return false;
    }

    public void SwapFont(Font font) {
        foreach (var txt in textToSwapFont) {
            txt.font = font;
        }
    }

    public void SetActiveGroupDiamond(bool isActive) {
        // because the gems HUD was alternated by hybrid one
        if (isActive && isAlterHUD && extraHudContainerObject.activeSelf) {
            return;
        }
        
        groupDiamond.gameObject.SetActive(isActive);
    }

    public void UpdateDiamondText(int amount) {
        gemsCountText.text = amount.ToString();
    }

    private IEnumerator PlayAnimPlusDiamond(string value) {
        var plusText = gemsPlusText;
        plusText.text = value;
        if (!plusText.gameObject.activeSelf) {
            plusText.gameObject.SetActive(true);
        }

        gemsPlusAnim.Play();
        yield return YieldPool.GetWaitForSeconds(0.4f);

        plusText.gameObject.SetActive(false);
    }


    private void MysteryDoorManagerOnTriggerDrill(Vector3 pos) {
        PlayIngameItemCollected(pos, 0.5f);
    }
    
    private void HandleOnStartTriggerMilestoneToken(Vector3 pos) {
        if (milestoneTokenHUD != null) {
            milestoneTokenHUD.PlayItemCollected(pos, MilestoneEvent.TOKEN_COLLECTED_FLY_DURATION);
        }
    }

    private void HandleOnSaveCollectedTokens() {
        if (milestoneTokenHUD != null) {
            var eventData = MilestoneEvent.instanceSafe.data;
            milestoneTokenHUD.txtTokenCount.text = (eventData.currentToken + eventData.earnedToken).ToString();
        }
    }

    public void UpdateMilestoneTokenHUD() {
        var dictTempToken = GameController.instance.DictToken;
        int value = dictTempToken.GetValueOrDefault(TokenType.MilestoneToken, 0);
        milestoneTokenHUD.txtTokenCount.text = $"+{value}";
    }
    
    private void PlayIngameItemCollected(Vector3 worldPosition, float duration) {
        var screenPoint = GameController.instance.mainCamera.WorldToScreenPoint(worldPosition);
        
        Vector2 canvasPos;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            imgToken.rectTransform,
            screenPoint,
            GameController.instance.mainCamera,
            out canvasPos
        );
        imgCollectedToken.enabled = true;
        imgCollectedToken.rectTransform.anchoredPosition = canvasPos;
        imgCollectedToken.rectTransform.DOAnchorPos(Vector3.zero, duration)
            .SetLink(gameObject)
            .OnComplete(CompleteCollectingToken);
    }

    private void CompleteCollectingToken() {
        imgCollectedToken.enabled = false;
        SoundManager.PlayMysteryDoorDrillCounting();
    }
}