using System;
using System.Collections;
using System.Collections.Generic;
using Com.TheFallenGames.OSA.Core;
using Com.TheFallenGames.OSA.DataHelpers;
using UnityEngine;

namespace Com.TheFallenGames.OSA.Core.CustomScroller.SettingPopup {
    public class SettingPopupScrollerAdapter : OSA<PopupParams, BaseItemViewsHolder> {
        // Helper that stores data and notifies the adapter when items count changes
        // Can be iterated and can also have its elements accessed by the [] operator
        public SimpleDataHelper<IData>           Data { get; private set; }
        public event Action<BaseItemViewsHolder> OnItemVisible;

        #region OSA implementation

        protected override void Start() {
            Data = new SimpleDataHelper<IData>(this);
            base.Start();
        }

        public void Init(RectTransform prefHeader, RectTransform prefItem, RectTransform prefItemDeleteAccount) {
            Parameters.prefHeader = prefHeader;
            Parameters.prefItem = prefItem;
            Parameters.prefItemDeleteAccount = prefItemDeleteAccount;
            // remote config cho sự giảm tốc của scroller
            Parameters.effects.InertiaDecelerationRate =
                Mathf.Clamp01(RemoteConfig.instance.Scroller_DeclearationRate);
            // remote config cho bật / tắt navigation
            if (Parameters.Scrollbar != null) {
                Parameters.Scrollbar.gameObject.SetActive(RemoteConfig.instance.ScrollerNavigation_IsEnable);
            }

            Parameters.isInited = true;
        }
        // This is called initially, as many times as needed to fill the viewport,
        // and anytime the viewport's size grows, thus allowing more items to be displayed
        // Here you create the "ViewsHolder" instance whose views will be re-used
        // *For the method's full description check the base implementation
        protected override BaseItemViewsHolder CreateViewsHolder(int itemIndex) {
            //var modelType = Data[itemIndex].GetType(); // _ModelTypes[itemIndex];
            if (Data[itemIndex] is SettingItemData settingData) {
                if (settingData.type == SettingItemType.DeleteAccount) {
                    var vh = new ItemDeleteAccountViewsHolder();
                    vh.Init(_Params.prefItemDeleteAccount, _Params.Content, itemIndex);
                    return vh;
                } else {
                    var vh = new ItemViewsHolder();
                    vh.Init(_Params.prefItem, _Params.Content, itemIndex);
                    return vh;
                }
            } else {
                var vh = new HeaderViewsHolder();
                vh.Init(_Params.prefHeader, _Params.Content, itemIndex);
                return vh;
            }
        }

        // This is called anytime a previously invisible item become visible, or after it's created,
        // or when anything that requires a refresh happens
        // Here you bind the data from the model to the item's views
        // *For the method's full description check the base implementation
        protected override void UpdateViewsHolder(BaseItemViewsHolder newOrRecycled) {
            // In this callback, "newOrRecycled.ItemIndex" is guaranteed to always reflect the
            // index of item that should be represented by this views holder. You'll use this index
            // to retrieve the model from your data set

            var modelData = Data[newOrRecycled.ItemIndex];
            newOrRecycled.cellView.SetData(modelData);
            OnItemVisible?.Invoke(newOrRecycled);
            //RequestChangeItemSizeAndUpdateLayout(newOrRecycled.ItemIndex, newOrRecycled.SizeDelta.y);
#if UNITY_EDITOR
            newOrRecycled.cellView.gameObject.name = $"Item {newOrRecycled.ItemIndex}";
            // Debug.LogError($"{newOrRecycled.SizeDelta} =>{height}");
#endif
        }

        protected override bool IsRecyclable(BaseItemViewsHolder potentiallyRecyclable,
            int indexOfItemThatWillBecomeVisible, double sizeOfItemThatWillBecomeVisible) {
            var model = Data[indexOfItemThatWillBecomeVisible];
            return potentiallyRecyclable.CanPresentModelType(model);
        }


        // You only need to care about this if changing the item count by other means than ResetItems,
        // case in which the existing items will not be re-created, but only their indices will change.
        // Even if you do this, you may still not need it if your item's views don't depend on the physical position
        // in the content, but they depend exclusively to the data inside the model (this is the most common scenario).
        // In this particular case, we want the item's index to be displayed and also to not be stored inside the model,
        // so we update its title when its index changes. At this point, the Data list is already updated and
        // shiftedViewsHolder.ItemIndex was correctly shifted so you can use it to retrieve the associated model
        // Also check the base implementation for complementary info
        /*
        protected override void OnItemIndexChangedDueInsertOrRemove(MyListItemViewsHolder shiftedViewsHolder, int oldIndex, bool wasInsert, int removeOrInsertIndex)
        {
            base.OnItemIndexChangedDueInsertOrRemove(shiftedViewsHolder, oldIndex, wasInsert, removeOrInsertIndex);

            shiftedViewsHolder.titleText.text = Data[shiftedViewsHolder.ItemIndex].title + " #" + shiftedViewsHolder.ItemIndex;
        }
        */

        #endregion

        // These are common data manipulation methods
        // The list containing the models is managed by you. The adapter only manages the items' sizes and the count
        // The adapter needs to be notified of any change that occurs in the data list. Methods for each
        // case are provided: Refresh, ResetItems, InsertItems, RemoveItems

        #region data manipulation

        public void AddItemsAt(int index, IList<IData> items) {
            // Commented: the below 2 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.InsertRange(index, items);
            //InsertItems(index, items.Length);

            Data.InsertItems(index, items);
        }

        public void RemoveItemsFrom(int index, int count) {
            // Commented: the below 2 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.RemoveRange(index, count);
            //RemoveItems(index, count);

            Data.RemoveItems(index, count);
        }

        public void SetItems(IList<IData> items) {
            // Commented: the below 3 lines exemplify how you can use a plain list to manage the data, instead of a DataHelper, in case you need full control
            //YourList.Clear();
            //YourList.AddRange(items);
            //ResetItems(YourList.Count);
            Data.ResetItems(items);
        }
        #endregion
    }

    /// <summary>Contains the 2 prefabs associated with the 2 views holders</summary>
    [Serializable] // serializable, so it can be shown in inspector
    public class PopupParams : BaseParams {
        public bool          isInited;
        public RectTransform prefHeader;
        public RectTransform prefItem;
        public RectTransform prefItemDeleteAccount;

        public override void InitIfNeeded(IOSA iAdapter) {
            base.InitIfNeeded(iAdapter);
            if (!isInited) return;
            AssertValidWidthHeight(prefHeader);
            AssertValidWidthHeight(prefItem);
            AssertValidWidthHeight(prefItemDeleteAccount);
        }
    }

    public class BaseItemViewsHolder : Core.BaseItemViewsHolder {
        public OptimizedCellView cellView;

        // Retrieving the views from the item's root GameObject
        public override void CollectViews() {
            base.CollectViews();
            cellView = root.GetComponent<OptimizedCellView>();
        }

        public virtual bool CanPresentModelType(IData data) {
            return true;
        }
    }

    public class HeaderViewsHolder : BaseItemViewsHolder {
        public override bool CanPresentModelType(IData data) {
            return data.GetType() == typeof(HeaderData);
        }
    }

    public class ItemViewsHolder : BaseItemViewsHolder {
        public override bool CanPresentModelType(IData data) {
            if (!(data is SettingItemData settingItem)) return false;
            return settingItem.type != SettingItemType.DeleteAccount;
        }
    }
    public class ItemDeleteAccountViewsHolder : BaseItemViewsHolder {
        public override bool CanPresentModelType(IData data) {
            if (!(data is SettingItemData settingItem)) return false;
            return settingItem.type == SettingItemType.DeleteAccount;
        }
    }
}