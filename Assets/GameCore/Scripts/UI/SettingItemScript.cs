using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class SettingItemScript : OptimizedCellView {
    [SerializeField] private Button     button;
    [SerializeField] private Image      iconImg;
    [SerializeField] private Text       txtTitle;
    [SerializeField] private Text       txtDesc;
    [SerializeField] private GameObject iconNotification;

    private SettingItemData _data;
    private event Action<SettingItemType> onClickItem;

    private void Start() {
        button.onClick.AddListener(() => { onClickItem?.Invoke(_data.type); });

        LocalizationManager.instance.UpdateFont(txtTitle);
        LocalizationManager.instance.UpdateFont(txtDesc);
    }

    public void SetData(SettingItemData data, Action<SettingItemType> onClickItem) {
        this._data = data;
        this.onClickItem = onClickItem;
        ForceUpdateUI();
    }

    public void ForceUpdateUI() {
        txtTitle.text = string.IsNullOrEmpty(_data.TitleName)
            ? string.Empty
            : LocalizationManager.instance.GetLocalizedValue(_data.TitleName);

        switch (_data.type) {
            case SettingItemType.Language:
                string langID = LocalizationManager.GetLanguageID();
                if (string.IsNullOrEmpty(langID)) {
                    langID = Application.systemLanguage.ToString();
                }

                txtDesc.text = LocalizationManager.instance.GetLocalizedValue(langID.ToUpper());
                break;
            case SettingItemType.Credit:
                txtDesc.text = Util.BuildString(string.Empty, "V", Application.version);
                break;
            case SettingItemType.DeleteAccount:
                txtDesc.text = string.Empty;
                break;
            default:
                if (!string.IsNullOrEmpty(_data.Desc)) {
                    txtDesc.text = LocalizationManager.instance.GetLocalizedValue(_data.Desc);
                } else {
                    txtDesc.text = string.Empty;
                }

                break;
        }

        if (_data.type == SettingItemType.DeleteAccount) {
            txtTitle.transform.SetLocalY(10);
        } else {
            txtTitle.transform.SetLocalY(string.IsNullOrEmpty(_data.Desc) ? 0 : 10);
        }

        iconImg.sprite = _data.icon;
        if (iconNotification != null) {
            iconNotification.SetActive(_data.isNotification);
        }

        if (_data.type == SettingItemType.NotificationBox) {
            NotificationBox.OnEmptyNotification += NotificationBoxOnOnEmptyNotification;
        }

        LocalizationManager.instance.UpdateFont(txtTitle);
        LocalizationManager.instance.UpdateFont(txtDesc);
    }

    private void NotificationBoxOnOnEmptyNotification() {
        _data.isNotification = false;
        iconNotification.SetActive(_data.isNotification);
        NotificationBox.OnEmptyNotification -= NotificationBoxOnOnEmptyNotification;
    }

    public override void SetData(IData _data) {
    }

    public SettingItemType GetItemType() {
        return _data.type;
    }
}

[Serializable]
public class SettingItemData : IData {
    public SettingItemType type;
    public string          TitleName;
    public string          Desc;
    public Sprite          icon;
    public bool            isNotification;
}

[Serializable]
public enum SettingItemType {
    Restore         = 0,
    Shop            = 1,
    GameSetting     = 2,
    Language        = 3,
    Review          = 4,
    Help            = 5,
    Privacy         = 6,
    Credit          = 7,
    DeleteAccount   = 8,
    PrivacyOption   = 9,
    NotificationBox = 10,
}