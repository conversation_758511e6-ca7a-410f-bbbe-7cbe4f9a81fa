using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using DG.Tweening;
using Inwave;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.U2D;
using UnityEngine.UI;
using Random = UnityEngine.Random;

public class SongCard {
    public string cardId;
    public Sprite cardImg;
    public bool   isWhite;

    public SongCardType cardType;

    public enum SongCardType {
        None,
        Default,
        SongCardAC<PERSON>,
        <PERSON>,
        Artist,
        ACMSquare,
        Genre,
        ChallengePack, // discovery challenge
        HardcorePack, // discovery hardcore collection
    }

    public bool HasData() {
        return !string.IsNullOrEmpty(cardId) && cardImg != null;
    }
}

public class SongCards : Singleton<SongCards> {
    //const
    public const string WhitePrefix = "_white";
    public const string DefaultId   = "default";
    public const string DisableId   = "off";
    public const string ButtonId    = "button";

    //static
    public static bool         isInited;
    static        List<string> _whiteCardIds;
    static        List<string> _whiteCardIdsOfGenre;

    //public
    [SerializeField] private TextAsset _defaultArtistConfig;
    [SerializeField] private TextAsset _defaultAlbumConfig;
    [SerializeField] private Sprite    spDefaultArtist;

    //private
    private SpriteAtlas                _cardDefaultSongs; //default
    private Dictionary<string, Sprite> _cardArtistSongs     = new(); //artist
    private List<string>               _cardArtistSongsName = new();
    private SpriteAtlas                _cardGenreSongs; //genre
    private SpriteAtlas                _cardSquareGenreSongs; //square genre
    private SpriteAtlas                _themeDiskAtlas;
    private SpriteAtlas                _cardSongsV2; // newconfig from ACM

    private Sprite _defaultChallengeSongCard;
    private Sprite _defaultHardCoreSongCard;

    private IEnumerator Start() {
        yield return null;
        
        Util.WaitSongListDone(Init);
    }

    public async void Init() {
        try {
            //Load SongCards from resources
            _whiteCardIds = Resources.Load<TextAsset>("SongCards/CardWhite").text.Replace('\n', ',').StringToList();

            await Task.Yield();
            _whiteCardIdsOfGenre = Resources.Load<TextAsset>("SongCards/CardWhiteOfGenre").text.Replace('\n', ',').StringToList();
            if (RemoteConfig.instance.ACM_IsEnableSquareSongCard) {
                await Task.Yield();
                _cardSongsV2 = Resources.Load<SpriteAtlas>("SongCards/SongCardAtlas_3");
            }

            await Task.Yield();
            _cardDefaultSongs = Resources.Load<SpriteAtlas>("SongCards/SongCardDefault");

            if (!DeviceHelper.IsLowEnd()) {
                _cardArtistSongsName = (RemoteConfigBase.instance != null
                    ? RemoteConfigBase.instance.AssetBundle_SongCard_Artist
                    : string.Empty).StringToList();

                await Task.Yield();
                _cardGenreSongs = Resources.Load<SpriteAtlas>("SongCards/SongCardByGenre");

                await Task.Yield();
                _cardSquareGenreSongs = Resources.Load<SpriteAtlas>("SongCards/SongIconByGenre");
            }

            isInited = true;

            await LoadArtistData();
            await LoadAlbumData();
        } catch (Exception e) {
            CustomException.Fire("[SongCards][Init]", e.Message + " => " + e.StackTrace);
        }
    }

    private async void LoadSongCardByAcmID(Song song) {
        try {
            Sprite card = await GetLongSongCardByACM(song);
            if (card != null) {
                bool isWhiteCard = ColorSongCardHelper.IsWhite(card.texture);
                song.UpdateSongCard(card, isWhiteCard);
            } else {
                bool canLoadArtistCover = CanLoadArtistCover(song);
                if (canLoadArtistCover) {
                    StartCoroutine(LoadArtistCoverByURL(song)); // load artist cover after failed song cover
                } else if (CanUseArtistSquare()) {
                    LoadArtistSquare(song); // load artist square after failed song cover
                }
            }
        } catch (Exception e) {
            CustomException.Fire("[LoadSongCardByAcmID]", e.Message + " => " + e.StackTrace);
        }
    }

    private static bool CanUseArtistSquare() {
        return RemoteConfigBase.instance.ACM_IsEnableSquareArtistCover &&
               RemoteConfigBase.instance.ACM_IsEnableSquareSongCard;
    }

    private static bool CanLoadArtistCover(Song song) {
        if (song.urlArtistCover == DisableId) {
            return false;
        }

        if (string.IsNullOrEmpty(song.urlArtistCover)) {
            return false;
        }

        if (RemoteConfigBase.instance.isEnableUrlArtistCover == false) {
            return false;
        }

        if (string.IsNullOrEmpty(RemoteConfigBase.instance.urlArtistCover)) {
            return false;
        }

        return true;
    }

    private async void LoadArtistSquare(Song song) {
        try {
            Sprite card = await GetACMArtistSquare(song);
            if (card != null) {
                song.onUpdateSquareSongCard?.Invoke(song.path, card);
            }
        } catch (Exception e) {
            CustomException.Fire("[LoadArtistSquare]", e.Message + " => " + e.StackTrace);
        }
    }

    public SongCard GetSongCardBySong(Song song, string currentGenre) {
        int indexLog = 0;
        try {
            //~~~~~~~~~~~~~~ LOAD ONLINE SONG CARD ~~~~~~~~~~~~~~
            if (!DeviceHelper.IsLowEnd()) {
                LoadOnlineSongCard(song);
            }

            indexLog++;
            //~~~~~~~~~~~~~~ possible to disable artist songcard at remote songconfig ~~~~~~~~~~~~~~
            if (!song.IsSpecialSongCard) {
                SongCard songCardBySongName = GetSongCardBySongName(song);
                if (songCardBySongName != null) {
                    return songCardBySongName;
                }

                indexLog++;
                if (song.urlArtistCover != DisableId) {
                    //~~~~~~~~~~~~~~ get cached card by urlArtistCover ~~~~~~~~~~~~~~
                    SongCard songCardByUrlArtistCover = GetSongCardByUrlArtistCover(song);
                    if (songCardByUrlArtistCover != null) {
                        return songCardByUrlArtistCover;
                    }

                    //~~~~~~~~~~~~~~ Get card by song artist ~~~~~~~~~~~~~~
                    SongCard songCardByArtist = GetSongCardByArtist(song);
                    if (songCardByArtist != null) {
                        return songCardByArtist;
                    }
                }

                indexLog++;
                //~~~~~~~~~~~~~~ get cached card by genre ~~~~~~~~~~~~~~
                SongCard cardBySong = GetSongCardByGenre(song, currentGenre);
                if (cardBySong != null) {
                    return cardBySong;
                }
            }

            indexLog++;
            //~~~~~~~~~~~~~~ Make sure card is not null ~~~~~~~~~~~~~~
            SongCard songCard = GetSongCardDefault(song);
            return songCard;

        } catch (Exception e) {
            CustomException.Fire("[GetCardBySong]", e.Message + " => indexLog " + indexLog + " => " + e.StackTrace);
            //Make sure card is not null
            return GetSongCardDefault(song);
        }
    }

    private SongCard GetSongCardBySongName(Song song) {
        string nameSong = song.name;
        Sprite card = GetSpriteFromABCardArtist(nameSong, song);

        if (card != null) {
            SongCard.SongCardType cardType = SongCard.SongCardType.Song;
            bool isWhite = !string.IsNullOrEmpty(song.urlArtistCover) && song.urlArtistCover.Contains(WhitePrefix);

            SongCard songCard = new() { cardImg = card, isWhite = isWhite, cardType = cardType };
            return songCard;
        }

        return null;
    }

    private SongCard GetSongCardByUrlArtistCover(Song song) {
        string songUrlArtistCover = song.urlArtistCover;
        if (!string.IsNullOrEmpty(songUrlArtistCover) && songUrlArtistCover.Contains('.')) {
            string cardId = songUrlArtistCover.Substring(0, songUrlArtistCover.LastIndexOf('.'));
            Sprite card = GetSpriteFromABCardArtist(cardId, song);
            if (card != null) {
                SongCard.SongCardType cardType = SongCard.SongCardType.Artist;
                bool isWhite = songUrlArtistCover.Contains(WhitePrefix);

                SongCard songCard = new() { cardImg = card, isWhite = isWhite, cardType = cardType };
                return songCard;
            }
        }

        return null;
    }

    private SongCard GetSongCardByArtist(Song song) {
        string cardId = song.artist;
        Sprite card = GetSpriteFromABCardArtist(cardId, song);
        if (card != null) {
            SongCard.SongCardType cardType = SongCard.SongCardType.Artist;
            bool isWhite = song.urlArtistCover != null && song.urlArtistCover.Contains(WhitePrefix);
            if (isWhite == false) {
                isWhite = _whiteCardIds.Contains(cardId);
            }

            SongCard songCard = new() { cardImg = card, isWhite = isWhite, cardType = cardType };
            return songCard;
        }

        return null;
    }

    private SongCard GetSongCardByGenre(Song song, string currentGenre) {
        string[] songGenres = song.GetGenres();
        if (songGenres == null || _cardGenreSongs == null) {
            return null;
        }

        SongCard songCard = GetSongCardByGenre(_cardGenreSongs, songGenres, currentGenre);
        if (songCard == null) {
            return null;
        }

        string cardId = songCard.cardId;
        songCard.isWhite = _whiteCardIdsOfGenre.Contains(cardId);
        songCard.cardType = SongCard.SongCardType.Genre;
        return songCard;

    }

    private void LoadOnlineSongCard(Song song) {
        //get songCard cover by ACM
        if (RemoteConfigBase.instance.ACM_IsEnableSongCover) {
            SongCard.SongCardType cardType = SongCard.SongCardType.SongCardACM;
            LoadSongCardByAcmID(song);
        }
        //get songCard by URL from config
        else if (CanLoadArtistCover(song)) {
            SongCard.SongCardType cardType = SongCard.SongCardType.Artist;
            Configuration.instance.StartCoroutine(LoadArtistCoverByURL(song));
        }
        //Get songcard by square artist ACM
        else if (CanUseArtistSquare()) {
            LoadArtistSquare(song); // load artist square
            SongCard.SongCardType cardType = SongCard.SongCardType.ACMSquare;
        }
    }

    public SongCard GetSongCardByButton(string cardId) {
        Sprite card = GetSpriteFromCardDefaultSongs(cardId);
        SongCard.SongCardType cardType = SongCard.SongCardType.Default;

        if (card != null) {
            bool isWhite = _whiteCardIds.Contains(cardId);
            SongCard songCard2 = new() { cardImg = card, isWhite = isWhite, cardType = cardType };
            return songCard2;
        } else {
            return GetSongCardDefault(null);
        }
    }

    private SongCard GetSongCardDefault(Song song) {
        SongCard songCard = new() {
            cardImg = GetDefaultSongCard(song),
            isWhite = _whiteCardIds.Contains(DefaultId),
            cardType = SongCard.SongCardType.Default
        };
        return songCard;
    }

    private Sprite GetDefaultSongCard(Song song) {
        if (song is { isChallengeSong: true }) {
            if (_defaultChallengeSongCard == null) {
                _defaultChallengeSongCard = Resources.Load<Sprite>("SongCards/default_challenge");
            }

            return _defaultChallengeSongCard;
        } else if (song is { isHardcoreSong: true }) {
            if (_defaultHardCoreSongCard == null) {
                _defaultHardCoreSongCard = Resources.Load<Sprite>("SongCards/default_hardcore");
            }

            return _defaultHardCoreSongCard;
        } else {
            return GetSpriteFromCardDefaultSongs(DefaultId);
        }
    }

    private Sprite GetSpriteFromCardDefaultSongs(string spriteName) {
        Sprite card = _cardDefaultSongs == null ? null : _cardDefaultSongs.GetSprite(spriteName);
        if (card == null) {
            Debug.LogError($"[GetSpriteFromCardDefaultSongs] cannot get card: {spriteName}");
        }

        return card;
    }

    public (Sprite cardBG, Sprite cardFade) GetThemeBySong(int idTheme) {
        Sprite cardBG = _cardSongsV2.GetSprite($"{(idTheme * 2 + 1):00}");
        Sprite cardFade = _cardSongsV2.GetSprite($"{(idTheme * 2 + 2):00}");
        return (cardBG, cardFade);
    }

    private SongCard GetSongCardByGenre(SpriteAtlas spriteAtlas, string[] genres, string defaultGenre = null) {
        SongCard songCard = new SongCard();

        if (spriteAtlas == null) {
            return null;
        }

        if (!string.IsNullOrEmpty(defaultGenre)) {
            Sprite sprite = spriteAtlas.GetSprite(defaultGenre);
            if (sprite != null) {
                songCard.cardId = defaultGenre;
                songCard.cardImg = sprite;
                return songCard;
            }
        }

        if (genres != null) {
            foreach (string genre in genres) {
                Sprite sprite = spriteAtlas.GetSprite(genre);
                if (sprite != null) {
                    songCard.cardId = genre;
                    songCard.cardImg = sprite;
                    return songCard;
                }
            }

            Logger.CanLog(() =>
                Logger.LogWarning("[GetSongCardByGenre] cannot get song card for: " + string.Join(";", genres)));
        }

        return null;
    }

    private void GetIconSongACM(Song song, Action<string> callback) {
        if (song.IsLocalSong()) {
            callback?.Invoke(string.Empty);
            return;
        }

        if (RemoteConfigBase.instance.ACM_IsEnableSquareArtistCover) {
            if (string.IsNullOrEmpty(song.acm_id_v3)) {
                callback?.Invoke(string.Empty);
            } else {
                SongCachedData item = CacheData.GetSongCardArtist(song.acm_id_v3);
                if (item != null && !string.IsNullOrEmpty(item.coverLocalPath) && File.Exists(item.coverLocalPath)) {
                    callback?.Invoke(item.coverLocalPath);
                } else {
                    ACMSDKv4.DownloadArtistImage(song.acm_id_v3, (() => {
                        SongCachedData item2 = CacheData.GetSongCardArtist(song.acm_id_v3);
                        callback?.Invoke(item2 != null ? item2.coverLocalPath : string.Empty);
                    }), (s => {
                        Logger.LogWarning($"[GetIconSongACM] {song.name} => {s}");
                        callback?.Invoke(string.Empty);
                    }));
                }
            }
        } else {
            callback?.Invoke(string.Empty);
        }
    }

    public async Task<(Sprite, bool)> GetSquareIconSong(Song song) {
        Sprite card = null;

        // 1 - Get card from ACM ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        card = await GetACMArtistSquare(song);
        if (card) {
            return (card, false);
        }

        // 2 - Get card by song genre ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        string[] songGenres = song.GetGenres();
        if (songGenres != null && _cardSquareGenreSongs != null) {
            SongCard songCard = GetSongCardByGenre(_cardSquareGenreSongs, songGenres);
            if (songCard != null) {
                card = songCard.cardImg;
            }
        }

        if (card) {
            return (card, false);
        }

        // 3 - Get card by song artist ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        card = await GetIconArtist(song.artist);
        if (card) {
            return (card, false);
        }

        // 4 - get icon theme ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        string cardId = ThemeManager.GetSelectedTheme(song).ToString();
        card = GetThemeDiskAtlas().GetSprite(cardId);
        if (!card) {
            card = GetThemeDiskAtlas().GetSprite("0");
#if UNITY_EDITOR
            Debug.LogWarning("[GetSquareIconSong] cannot load icon theme for: " + cardId);
#endif
        }

        bool isIconTheme = card;
        return (card, isIconTheme);
    }

    private async Task<Sprite> GetACMArtistSquare(Song song) {
        Sprite card = null;

        bool isWating = true;
        string localPath = string.Empty;
        GetIconSongACM(song, url => {
            isWating = false;
            localPath = url;
        });
        // Sử dụng coroutine thay vì DOVirtual.DelayedCall để tránh DelayedCallManager crash
        StartCoroutine(DelayedTimeout(() => { isWating = false; }, 2f));
        while (isWating) {
            await Task.Delay(100);
        }

        if (this == null) {
            return null;
        }

        if (!string.IsNullOrEmpty(localPath) && !DeviceHelper.IsLowEnd()) {
            card = await DownloadManager.instanceSafe.DownloadImage(localPath, 300);
        }

        if (card != null) {
            int maxSizeDownload = Mathf.Max(card.texture.height, card.texture.width);
            if (maxSizeDownload > 300) {
                CustomException.Fire("[GetACMArtistSquare]",
                    $"Too big: {maxSizeDownload} => Song.Name {song.name} Song.AcmID {song.acm_id_v3}");
            }
        }

        return card;
    }

    private async Task<Sprite> GetLongSongCardByACM(Song song) {
        Sprite card = null;
        bool isWating = true;
        string localPath = string.Empty;
        GetLongSongcardACM(song, url => {
            isWating = false;
            localPath = url;
        });
        // Sử dụng coroutine thay vì DOVirtual.DelayedCall để tránh DelayedCallManager crash
        StartCoroutine(DelayedTimeout(() => { isWating = false; }, 2f));
        while (isWating) {
            await Task.Delay(100);
        }

        if (this == null) {
            return null;
        }

        if (!string.IsNullOrEmpty(localPath) && !DeviceHelper.IsLowEnd()) {
            card = await DownloadManager.instanceSafe.DownloadImage(localPath, 512);
        }

        if (card != null) {
            int maxSizeDownload = Mathf.Max(card.texture.height, card.texture.width);
            if (maxSizeDownload > 512) {
                CustomException.Fire("[GetLongSongCardByACM]",
                    $"Too big: {maxSizeDownload} => Song.Name {song.name} Song.AcmID {song.acm_id_v3}");
            }
        }

        return card;
    }

    public async Task<Sprite> GetIconArtist(string artistName = DefaultId) {
        if (string.IsNullOrEmpty(artistName) || artistName.Equals(DefaultId)) {
            return spDefaultArtist;
        }

        Artist artist = GetArtistByName(artistName);
        if (artist == null) {
            Logger.LogWarning("[GetIconArtist] can not get artist: " + artistName);
            return null;
        }

        if (artist.sprIcon != null) {
            return artist.sprIcon;
        }

        if (!string.IsNullOrEmpty(artist.square_cover)) {
            Sprite downloadImage = await DownloadManager.instanceSafe.DownloadImage(artist.square_cover, 200);
            artist.sprIcon = downloadImage;
        }

        return artist.sprIcon;
    }

    public async Task<(Sprite, bool)> GetIconArtistWithStatus(string artistName = DefaultId) {
        if (string.IsNullOrEmpty(artistName) || artistName.Equals(DefaultId)) {
            return (spDefaultArtist, false);
        }

        Artist artist = GetArtistByContainingName(artistName);
        if (artist == null) {
            Logger.LogWarning("[GetIconArtistWithStatus] can not get artist: " + artistName);
            return (null, false);
        }

        if (artist.sprIcon != null) {
            return (artist.sprIcon, true);
        }

        if (!string.IsNullOrEmpty(artist.square_cover)) {
            Sprite downloadImage = await DownloadManager.instanceSafe.DownloadImage(artist.square_cover, 200);
            artist.sprIcon = downloadImage;
        }

        return (artist.sprIcon, true);
    }

    public Artist GetArtistByName(string artistName) {
        if (artistName == null || _dataArtists == null || _dataArtists.Count == 0) {
            Logger.EditorLogError("[GetArtistByName]", "NULL => init???");
            return null;
        }

        foreach (Artist artist in _dataArtists) {
            if (artist == null) {
                continue;
            }

            if (!string.IsNullOrEmpty(artist.name) &&
                string.Equals(artist.name, artistName, StringComparison.CurrentCultureIgnoreCase)) {
                return artist;
            }
        }

        return null;
    }

    public Artist GetArtistByContainingName(string artistName) {
        if (artistName == null || _dataArtists == null || _dataArtists.Count == 0) {
            Logger.EditorLogError("[GetArtistByContainingName]", "NULL => init???");
            return null;
        }

        foreach (Artist artist in _dataArtists) {
            if (artist == null) {
                continue;
            }

            if (!string.IsNullOrEmpty(artist.name) && artistName.Contains(artist.name)) {
                return artist;
            }
        }

        return null;
    }

    private SpriteAtlas GetThemeDiskAtlas() {
        if (_themeDiskAtlas == null) {
            _themeDiskAtlas = Resources.Load<SpriteAtlas>(ResourcesPath.SpriteAtlas_ThemeDiskUI2);
        }

        return _themeDiskAtlas;
    }

    #region Download Long SongCard By ACM

    private void GetLongSongcardACM(Song song, Action<string> callback) {
        if (song.IsLocalSong()) {
            callback?.Invoke(string.Empty);
            return;
        }

        if (RemoteConfig.instance.ACM_IsEnableSongCover) {
            if (string.IsNullOrEmpty(song.acm_id_v3)) {
                callback?.Invoke(string.Empty);
            } else {
                SongCachedData item = CacheData.GetLongSongCard(song.acm_id_v3);
                if (item != null && !string.IsNullOrEmpty(item.longSongCardPath) &&
                    File.Exists(item.longSongCardPath)) {
                    callback?.Invoke(item.longSongCardPath);
                } else {
                    ACMSDKv4.DownloadLongSongCard(song.acm_id_v3, (() => {
                        SongCachedData item2 = CacheData.GetLongSongCard(song.acm_id_v3);
                        callback?.Invoke(item2 != null ? item2.longSongCardPath : string.Empty);
                    }), (s => {
                        // Logger.EditorLog($"[GetLongSongCardACM] {song.name} => {s}");
                        callback?.Invoke(string.Empty);
                    }));
                }
            }
        } else {
            callback?.Invoke(string.Empty);
        }
    }

    #endregion

    #region Coroutines

    private IEnumerator DelayedTimeout(System.Action action, float delay) {
        yield return new WaitForSeconds(delay);
        action?.Invoke();
    }

    #endregion

    #region Download Artist Cover By URL

    private IEnumerator LoadArtistCoverByURL(Song song) {
        yield return null;

        string urlArtistCover = RemoteConfig.instance.urlArtistCover + song.urlArtistCover;
        string localPathArtistCover = GetPathLocal(urlArtistCover);

        if (string.IsNullOrEmpty(localPathArtistCover)) {
            yield break;
        }

        bool isLoadLocal = File.Exists(localPathArtistCover);
        string linkArtistCover = isLoadLocal ? localPathArtistCover : urlArtistCover;
        if (isLoadLocal && !linkArtistCover.Contains("file://")) {
            linkArtistCover = $"file://{linkArtistCover}";
        }

        UnityWebRequest webRequest = UnityWebRequestTexture.GetTexture(linkArtistCover);
        yield return webRequest.SendWebRequest();

        try {
            if (webRequest.isNetworkError || webRequest.isHttpError) {
                Debug.LogError($"Failed to download image [{linkArtistCover}]: {webRequest.error}");
                //Get card by squareACMImage
                if (CanUseArtistSquare()) {
                    LoadArtistSquare(song); // load artist square after failed artist cover
                }
            } else {
                DownloadHandler downloadHandler = webRequest.downloadHandler;
                Texture2D texture = ((DownloadHandlerTexture) downloadHandler).texture;

                if (texture != null) {
                    if (!isLoadLocal) { //save to local
                        byte[] downloadHandlerData = downloadHandler.data;
                        File.WriteAllBytes(localPathArtistCover, downloadHandlerData);

                        int maxSizeDownload = Mathf.Max(texture.height, texture.width);
                        if (maxSizeDownload > 512) {
                            CustomException.SetKey(FirebaseKey.URL, urlArtistCover);
                            CustomException.Fire("[LoadArtistCoverByURL]",
                                $"Too big: {maxSizeDownload} => more in Keys");
                        }
                    }

                    Sprite sprite = Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height),
                        new Vector2(0, 0), 100);
                    bool isWhiteCard = ColorSongCardHelper.IsWhite(texture);
                    song.UpdateSongCard(sprite, isWhiteCard);
                }
            }
        } catch (Exception e) {
            Debug.LogError(e.Message);
        }
    }

    private string GetPathLocal(string url) {
        if (string.IsNullOrEmpty(url)) {
            return null;
        }

        try {
#if UNITY_EDITOR
            string folderArtistCover = Path.Combine(Application.persistentDataPath, "ArtistCover");
#else
            string folderArtistCover = Path.Combine(Application.temporaryCachePath, "ArtistCover");
#endif
            if (!Directory.Exists(folderArtistCover)) {
                Directory.CreateDirectory(folderArtistCover);
            }

            string ext = url.Substring(url.LastIndexOf('.'));
#if UNITY_EDITOR
            string path = Path.Combine(folderArtistCover, url).Replace(RemoteConfig.instance.urlArtistCover, "");
#else
            string path = Path.Combine(folderArtistCover, Util.MD5Hash(url) + ext);
#endif
            return path;

        } catch (Exception e) {
            CustomException.Fire("[GetPathLocal]", e.Message + " => " + e.StackTrace);
            return null;
        }
    }

    #endregion

    #region Artists

    private List<Artist> _dataArtists;

    private async Task LoadArtistData() {
        string urlArtistsData = RemoteConfig.instance.SongDiscovery_UrlArtistsData;
        if (string.IsNullOrEmpty(urlArtistsData)) {
            return;
        }

        string dataCsv = await DownloadManager.DownloadCsv(urlArtistsData);
        if (string.IsNullOrEmpty(dataCsv)) {
            dataCsv = _defaultArtistConfig.text;
        }

        string[,] grid = CSVReader.SplitCsvGrid(dataCsv, true);
        _dataArtists = GetDataArtists(grid);
    }

    public List<Artist> GetDataArtists() {
        return _dataArtists;
    }

    public bool IsInitedArtists() {
        return _dataArtists != null && _dataArtists.Count > 0;
    }

    private List<Artist> GetDataArtists(string[,] dataCsv) {
        List<Artist> artists = new List<Artist>();

        Dictionary<int, string> header = CSVReader.GetHeader(dataCsv);
        
        // Cache field info để tránh reflection overhead
        Dictionary<string, FieldInfo> fieldCache = new Dictionary<string, FieldInfo>();
        foreach (var kvp in header) {
            string columnName = kvp.Value;
            FieldInfo field = typeof(Artist).GetField(columnName);
            if (field != null) {
                fieldCache[columnName] = field;
            }
        }

        // Process data với cached field info
        for (int row = 1, n = dataCsv.GetUpperBound(1) + 1; row < n; row++) {
            Artist artist = new Artist {
                ordering = row - 1
            };

            for (int column = 0, m = header.Count; column < m; column++) {
                string columnName = header[column];
                string cell = dataCsv[column, row];

                if (cell != null && fieldCache.TryGetValue(columnName, out FieldInfo field)) {
                    string value = cell.Trim();
                    GetFieldValue(artist, field, value);
                }
            }

            if (!string.IsNullOrEmpty(artist.name)) {
                artists.Add(artist);
            }
        }

        return artists;
    }

    private static void GetFieldValue(object item, FieldInfo field, string value) {
        if (field == null || string.IsNullOrEmpty(value)) {
            return;
        }

        if (field.FieldType == typeof(string)) {
            field.SetValue(item, value);
        } else if (field.FieldType == typeof(float)) {
            float tmp = 0;
            float.TryParse(value, out tmp);
            field.SetValue(item, tmp);
        } else if (field.FieldType == typeof(int)) {
            int tmp = 0;
            int.TryParse(value, out tmp);
            field.SetValue(item, tmp);
        } else if (field.FieldType == typeof(bool)) {
            field.SetValue(item, value == "1");
        } else if (field.FieldType == typeof(List<string>)) {
            string[] strings = value.Split(';');
            field.SetValue(item, strings.ToList());
        }
    }

    #endregion

    #region Album

    private List<Album> _dataAlbums;

    private async Task LoadAlbumData() {
        string urlAlbumData = RemoteConfigBase.instance.SongDiscovery_UrlAlbumData;
        string dataCsv;
        if (string.IsNullOrEmpty(urlAlbumData)) {
            dataCsv = _defaultAlbumConfig.text;
        } else {
            dataCsv = await DownloadManager.DownloadCsv(urlAlbumData);
            if (string.IsNullOrEmpty(dataCsv)) {
                dataCsv = _defaultAlbumConfig.text;
            }
        }

        string[,] grid = CSVReader.SplitCsvGrid(dataCsv, true);
        _dataAlbums = GetDataAlbums(grid);
    }

    public List<Album> GetDataAlbums() {
        return _dataAlbums;
    }

    public bool IsInitedAlbums() {
        return _dataAlbums != null && _dataAlbums.Count > 0;
    }

    private List<Album> GetDataAlbums(string[,] dataCsv) {
        List<Album> albums = new List<Album>();

        Dictionary<int, string> header = CSVReader.GetHeader(dataCsv);
        
        // Cache field info để tránh reflection overhead
        Dictionary<string, FieldInfo> fieldCache = new Dictionary<string, FieldInfo>();
        foreach (var kvp in header) {
            string columnName = kvp.Value;
            FieldInfo field = typeof(Album).GetField(columnName);
            if (field != null) {
                fieldCache[columnName] = field;
            }
        }

        // Process data với cached field info
        for (int row = 1, n = dataCsv.GetUpperBound(1) + 1; row < n; row++) {
            Album album = new Album {
                ordering = row - 1
            };

            for (int column = 0, m = header.Count; column < m; column++) {
                string columnName = header[column];
                string cell = dataCsv[column, row];

                if (cell != null && fieldCache.TryGetValue(columnName, out FieldInfo field)) {
                    string value = cell.Trim();
                    GetFieldValue(album, field, value);
                }
            }

            if (!string.IsNullOrEmpty(album.name)) {
                albums.Add(album);
            }
        }

        return albums;
    }

    public async Task<Sprite> GetIconAlbum(string albumName) {
        Album album = GetAlbum(albumName);
        if (album != null) {
            Sprite iconAlbum = await GetIconAlbum(album);
            return iconAlbum;
        }

        return null;
    }

    public Album GetAlbum(string albumName) {
        if (_dataAlbums == null) {
            Logger.Log("[GetAlbum]", "NULL => init???");
            return null;
        }

        foreach (Album album in _dataAlbums) {
            if (string.Compare(album.name, albumName, StringComparison.OrdinalIgnoreCase) == 0) {
                return album;
            }
        }

        return null;
    }

    public async Task<Sprite> GetIconAlbum(Album album) {
        if (album == null) {
            return null;
        }

        if (album.sprIcon != null) {
            return album.sprIcon;
        }

        if (!string.IsNullOrEmpty(album.square_cover)) {
            Sprite downloadImage = await DownloadManager.instanceSafe.DownloadImage(album.square_cover, 200);
            album.sprIcon = downloadImage;
        }

        return album.sprIcon;
    }

    public async Task<Sprite> GetBGAlbum(string albumName) {
        Album album = GetAlbum(albumName);
        if (album != null) {
            Sprite iconAlbum = await GetBGAlbum(album);
            return iconAlbum;
        }

        return null;
    }

    public async Task<Sprite> GetBGAlbum(Album album) {
        if (album == null) {
            return null;
        }

        if (album.sprBg != null) {
            return album.sprBg;
        }

        if (!string.IsNullOrEmpty(album.bg_cover) && !DeviceHelper.IsLowEnd()) {
            Sprite downloadImage = await DownloadManager.instanceSafe.DownloadImage(album.bg_cover, 512);
            album.sprBg = downloadImage;
        }

        return album.sprBg;
    }

    public int GetIdBallForAlbum(string albumName) {
        if (string.IsNullOrEmpty(albumName))
            return -1;

        foreach (var album in _dataAlbums) {
            if (album.name.Equals(albumName, StringComparison.InvariantCultureIgnoreCase)) {
                return album.GetIdBall();
            }
        }

        return -1;
    }

    #endregion

    private Sprite GetSpriteFromABCardArtist(string spriteName, Song song) {
        if (string.IsNullOrEmpty(spriteName)) {
            return null;
        }

        spriteName = AssetBundleManager.ConvertNameToBundleName(spriteName); //to can up to s3
        if (!_cardArtistSongsName.Contains(spriteName)) {
            return null;
        }

        _cardArtistSongs ??= new Dictionary<string, Sprite>();
        if (_cardArtistSongs.TryGetValue(spriteName, out Sprite artistSongs)) {
            return artistSongs;
        }

        StartCoroutine(AssetBundleManager.LoadAsset<Sprite>(AssetBundleManager.pathCardArtistSongs, spriteName,
            spriteCached => {
                if (spriteCached != null) {
                    _cardArtistSongs[spriteName] = spriteCached;
                    song.UpdateSongCard(spriteCached, spriteName.Contains(WhitePrefix));
                }
            }, newSprite => {
                if (newSprite != null) {
                    _cardArtistSongs[spriteName] = newSprite;
                    song.UpdateSongCard(newSprite, spriteName.Contains(WhitePrefix));
                }
            }));

        return null;
    }
}