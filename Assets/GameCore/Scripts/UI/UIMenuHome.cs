using System;
using System.Collections;
using System.Collections.Generic;
using Balancy.Models;
using Com.TheFallenGames.OSA.Core.CustomScroller.SongScroller;
using DG.Tweening;
using GameCore;
using GameCore.EndlessOffer;
using GameCore.LiveEvent.MysteryDoor;
using GameCore.Scripts;
using GameCore.Scripts.Core;
using TilesHop.Cores.Hybrid;
using TilesHop.Cores.Hybrid.TripleOffer;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.UserProgression;
using TilesHop.EconomySystem;
using TilesHop.GameCore.StarsJourney;
using TilesHop.InstantMission;
using TilesHop.LiveEvent.MilestoneEvent;
using TilesHop.MysteryBox;
using TilesHop.PlaytimeReward;
using TilesHop.UI;
using TilesHop.UI.HomeUX;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;

public class UIMenuHome : MonoBehaviour {
    public virtual bool isLocked => /*isShowStarJourneyBar && */ RemoteConfigBase.instance.StarsJourney_IsLockTopMenu;

    public static bool isAlwaysShowFromTop =>
        remoteConfig.Home_DiskFallAnimation_IsEnable && (remoteConfig.StarsJourney_IsLockTopMenu ||
                                                         !remoteConfig.FixedScrollviewPositionInHome_IsEnable);

    [SerializeField] protected TopBarHome topbarHome;

    [SerializeField] private Transform tfTopBarHome;
    [SerializeField] private Transform tfRightMenu;
    [SerializeField] private Transform tfLeftMenu;
    [SerializeField] private Transform safeArea2;

    [SerializeField] private Button        btnSetting;
    [SerializeField] private BadgeOfButton objNotificationBox;

    [SerializeField] protected Button        btnShopBall;
    [SerializeField] protected Button        btnShopBallV2;
    [SerializeField] public    Button        btn7DayMission; //TH-533
    [SerializeField] private   BadgeOfButton mission7DayBadgeHolder;
    [SerializeField] public    Button        btnAchievement; //TH-2917
    [SerializeField] private   BadgeOfButton achievementBadgeHolder;
    [SerializeField] protected Transform     btnMysteryBox;
    [SerializeField] protected Transform     btnSpecialOffer;
    [SerializeField] public    Button        btnGift;
    [SerializeField] protected Button        btnShopDiamond;
    [SerializeField] protected BadgeOfButton shopDiamondBadgeHolder;
    [SerializeField] protected Button        btnMission;
    [SerializeField] protected BadgeOfButton missionBadge;

    [SerializeField] protected Button btnNoFSAds;

    [SerializeField] protected Transform starterPackEntryContainer;
    [SerializeField] protected Button    btnStarterPack;

    [SerializeField] protected GameObject    btnPlayTimeReward;
    [SerializeField] protected Button        btnVipMission;
    [SerializeField] private   BadgeOfButton vipMissionBadgeHolder;
    [SerializeField] protected Button        adminButton;
    [SerializeField] protected GameObject    videoDiamondButton;
    [SerializeField] protected Button[]      vipButtons;

    public bool isSetVipMissionToDropdown;

    private CanvasGroup _canvasGroup;
    private bool        _isPrepareDestroying;

    public CanvasGroup canvasGroup {
        get {
            if (_canvasGroup == null) {
                _canvasGroup = gameObject.AddComponent<CanvasGroup>();
            }

            return _canvasGroup;
        }
    }

    protected static RemoteConfig remoteConfig => RemoteConfigBase.instance;
    protected HomeManager homeManager;

    protected StarJourneyBar            starsJourneyBar;
    protected MilestoneEventProgressBar milestoneEventProgressBar;
    protected InstantMissionUI          instantMission;

    private bool  _showStarJourney;
    private float _offsetStarJourney;

    private bool  _showInstantMission;
    private float _offsetInstantMission;

    private bool showMilestoneEvent {
        get {
            if (!remoteConfig.MileStoneEvent_IsEnable) {
                return false;
            }

            if (remoteConfig.MileStoneEvent_ShowWhenLock) {
                return remoteConfig.MileStoneEvent_IsEnable && MilestoneEvent.isAvailable;
            }

            return MilestoneEvent.instanceSafe.CheckUnlockConditions();
        }
    }

    private float _offsetMilestoneEvent;

    private float _rootMainMenu;
    private int   _offsetFeature;

    protected bool inCoroutineTooltipDualUnlock;

    protected RectTransform rectTransform;
    private   GameObject    _tooltipShopBall;

    public const int SONG_LIST_PADDING_TOP_DEFAULT = 388;
    public const int FEATURE_PADDING_SONGLIST      = 8;
    public Transform DiamondTransform => topbarHome.DiamondTransform;

    public bool isShowVFX = false;

    public bool IsNeedShowBtnGift {
        get {
            if (!remoteConfig.Feature_FreeGift_IsEnable) { // bật/ tắt feature
                return false;
            }

            if (UserProgressionController.EnableFeature) {
                return false;
            }

            if (!Configuration.CanEarnDiamond()) { //VIP user
                return false;
            }

            if (MysteryBox.IsHaveButtonMysteryBoxInHome && remoteConfig.mysterybox_replace_freegift) {
                return false; // mystery box override gift
            }

            return FreeVideo.HasItem(); // nếu k có item -> k show button
        }
    }

    /// <summary>
    /// Điều kiện show của button shopDiamond in Home
    /// </summary>
    public bool IsShowButtonShopDiamondInHome {
        get {
            if (!remoteConfig.Economy_IsEnable) {
                return false; // khi không bật economy -> k show button Shop ở home
            }

            if (!remoteConfig.Economy_buttonShopInHome_Enable) { // không bật button Shop ở home
                return false;
            }

            if (UserProgressionController.EnableFeature) {
                return false;
            }

            return true;
        }
    }

    /// <summary>
    /// Điều kiện show badge của button shopDiamond
    /// </summary>
    public bool IsHaveBadgeShopDiamond {
        get {
            if (!IsShowButtonShopDiamondInHome) { // khi k show btnShop in home -> k cần kiểm tra badge
                return false;
            }

            if (SubscriptionController.IsSubscriptionVip()) {
                return false; // khi VIP -> không bật ads -> không có btn Shop ở Home
            }

            if (!Configuration.instance.IsShowFreeGemPack) {
                return false; // khi k bật config freegem in shop -> k cần check thêm
            }

            var data = Configuration.instance.GetFreeGemData();
            return data.isFreeGem;
        }
    }

    public static bool isShowStarJourneyBar =>
        StarsJourneyManager.isEnable && Configuration.instance.GetCurrentStars() != 0 &&
        (!RemoteConfigBase.instance.Hybrid_UI_Rearrange || UserProgressionController.EnableFeature);

    public static bool needOnboardStarJourney =>
        StarsJourneyManager.isEnable && Configuration.instance.GetCurrentStars() != 0;

    public StarJourneyBar starJourneyBarHome => starsJourneyBar;

    public RectTransform rtfTopMenu => rectTransform;
    public virtual Transform transformHolder => transform;
    public TopBarHome topBarHome => topbarHome;

    protected bool   _isInitMysteryBox;
    protected bool   _isInitSpecialOffer;
    protected Button btnBallSpinner;

    private bool           _waitingAvatarLoad;
    private Coroutine      _loadAvatarCoroutine;
    private DropdownButton _groupDropdownButtons;
    private GameObject     _galaxyQuestEntryPoint;

    private List<Tweener> _activeTweens = new List<Tweener>();
    private Button        _rectangleVipButton;
    private int           _lastIdRectangle;

    protected GameObject tripleOffersEntryPoint;

    private GameObject _mysteryDoorEntryPoint;
    private bool       _isSetupUIMainButtons;

    public static event Action OnChangeSize;

    protected virtual void Awake() {
        rectTransform = transform as RectTransform;
        homeManager = HomeManager.instance;
    }

    protected virtual void OnEnable() {
        if (!_isSetupUIMainButtons) {
            _isSetupUIMainButtons = true;
            SetupUIMainButtons();
        }

        UpdateUIMainButtons();
        UpdateUiNoti7DayMission();
        CheckInitSize();
        UpdateMenuLeftWithButtonsPriority();
        SongItem.OnDualUnlockFeature += SongItemOnOnDualUnlockFeature;
    }

    protected virtual void OnDisable() {
        SongItem.OnDualUnlockFeature -= SongItemOnOnDualUnlockFeature;
    }

    protected void Start() {
        if (RemoteConfigBase.instance.Home_ButtonAppearAnimation_IsEnable && isAlwaysShowFromTop) {
            PlaySideMenuAppearAnimations();
        }
    }

    private void OnDestroy() {
        foreach (var tween in _activeTweens) {
            if (tween != null && tween.IsActive()) {
                tween.Kill();
            }
        }

        _activeTweens.Clear();
        ButtonPriority.buttonsMap.Clear();
    }

    protected virtual void PlaySideMenuAppearAnimations() {
        float delay = 0F;
        if (RemoteConfigBase.instance.Home_DiskFallAnimation_IsEnable) {
            delay = 0.3F;
        }

        List<Transform> leftActiveButtons = ListPool<Transform>.Get();
        List<Transform> rightActiveButtons = ListPool<Transform>.Get();

        foreach (Transform child in tfLeftMenu) {
            if (child.gameObject.activeSelf) {
                leftActiveButtons.Add(child);
            }
        }

        foreach (Transform child in tfRightMenu) {
            if (child.gameObject.activeSelf) {
                rightActiveButtons.Add(child);
            }
        }

        int maxCount = Mathf.Max(leftActiveButtons.Count, rightActiveButtons.Count);
        float duration = 0.5F / maxCount;

        for (int index = 0; index < leftActiveButtons.Count; index++) {
            leftActiveButtons[index].localScale = Vector3.zero;
            _activeTweens.Add(leftActiveButtons[index].DOScale(1f, duration).SetDelay(index * duration + delay)
                .SetEase(Ease.OutBack));
        }

        for (int index = 0; index < rightActiveButtons.Count; index++) {
            rightActiveButtons[index].localScale = Vector3.zero;
            _activeTweens.Add(rightActiveButtons[index].DOScale(1f, duration).SetDelay(index * duration + delay)
                .SetEase(Ease.OutBack));
        }

        ListPool<Transform>.Release(leftActiveButtons);
        ListPool<Transform>.Release(rightActiveButtons);
    }

    public virtual int GetMenuHeight() {
        return isLocked ? 0 : GetMenuOffsetMax();
    }

    public int GetAdjustFeatureStickyOffset() {
        int adjust = 0;
        bool isAdjusted = false;
        if (_showStarJourney) {
            adjust += StarJourneyBar.STAR_JOURNEY_BAR_HEIGHT + FEATURE_UI_SPACING +
                      StarJourneyBar.STICKY_PADDING_BOTTOM;
            isAdjusted = true;
        }

        if (showMilestoneEvent) {
            adjust += MilestoneEventProgressBar.HEIGHT + FEATURE_UI_SPACING +
                      MilestoneEventProgressBar.STICKY_PADDING_BOTTOM;
            isAdjusted = true;
        }

        if (isAdjusted) {
            adjust += (int) SongList.instance.SongScroller.ScrollerAdapter.Parameters.ContentSpacing;
        }

        return adjust;
    }

    public int GetMenuOffsetMax() {
        var absOffsetMax = SONG_LIST_PADDING_TOP_DEFAULT + _offsetFeature;
        if (HomeManager.instance.isTopBarFixed) {
            return absOffsetMax - TopBarHome.heightOfTopBar;
        }

        return absOffsetMax;
    }

    protected virtual void CheckInitSize() {
        ((RectTransform) transform).sizeDelta = new Vector2(480, 388);
    }

    protected virtual void SetupUIMainButtons() {
        if (_isPrepareDestroying) {
            return;
        }

        bool isEnableGroupButtons = UXHomeDropDown.isEnable && !UserProgressionController.EnableFeature;
        if (isEnableGroupButtons) {
            EnableGroupButtons();
        }

        SetupStarterPackEntryPoint();

        if (achievementBadgeHolder) {
            achievementBadgeHolder.ShowBadge(false);
        }

        if (vipMissionBadgeHolder) {
            vipMissionBadgeHolder.ShowBadge(false);
        }

        CheckShowStarJourney();
        if (remoteConfig.MileStoneEvent_IsEnable) {
            HomeManager.instance.StartCoroutine(IECheckShowMilestoneEventBar());
        }

        CheckShowInstantMission();
        if (_showStarJourney || _showInstantMission || showMilestoneEvent) {
            CalculateOffsetFeature();
        }

        SetupListeners();
    }

    protected virtual void CheckShowInstantMission() {
        if (!InstantMissionManager.EnableFeature)
            return;

        InstantMissionManager.OnChangeFeature += InstantMissionManagerOnOnChangeFeature;
        if (instantMission == null) {
            InstantMissionUI prefInstantMission =
                ResourcesManager.Load<InstantMissionUI>(ResourcesPath.InstantMissionUI);
            if (prefInstantMission != null) {
                instantMission = Instantiate(prefInstantMission,
                    HomeManager.instance.mainHomeScript.songScroller.transform);
                _showInstantMission = true;
            }
        }
    }

    private void InstantMissionManagerOnOnChangeFeature() {
        if (!InstantMissionManager.EnableFeature) {
            if (instantMission) {
                instantMission.gameObject.SetActive(false);
                _showInstantMission = false;
                CalculateOffsetFeature();
            }
        }
    }

    protected virtual void CheckShowStarJourney() {
        if (!isShowStarJourneyBar) {
            if (StarsJourneyManager.isEnable) {
                if (RemoteConfigBase.instance.Hybrid_UI_Rearrange) {
                    StarFlying prefStarFlying = ResourcesManager.Load<StarFlying>(ResourcesPath.StarFlying);
                    if (prefStarFlying != null) {
                        Instantiate(prefStarFlying, HomeManager.instance.mainHomeScript.songScroller.transform);
                    }
                } else {
                    StarJourneyBar.InitWhenNotYetShowStarJourneyBar();
                }
            }

            return;
        }

        if (starsJourneyBar == null) {
            StarJourneyBar prefStarJourneyBar = ResourcesManager.Load<StarJourneyBar>(ResourcesPath.StarJourneyBar);
            if (prefStarJourneyBar != null) {
                starsJourneyBar = Instantiate(prefStarJourneyBar,
                    HomeManager.instance.mainHomeScript.songScroller.transform);
                _showStarJourney = true;
            }
        }
    }

    protected IEnumerator IECheckShowMilestoneEventBar() {
        if (!remoteConfig.MileStoneEvent_IsEnable) {
            yield break;
        }

        while (!MilestoneEvent.isDoneProcessConfig) {
            yield return null;
        }

        if (MilestoneEvent.instanceSafe.GetInstantiatedProgressBar() != null) {
            yield break;
        }

        if (!RemoteConfigBase.instance.MileStoneEvent_ShowWhenLock) {
            if (!MilestoneEvent.instanceSafe.CheckUnlockConditions()) {
                yield break;
            }
        }

        var container = HomeManager.instance.mainHomeScript.songScroller.transform;
        yield return MilestoneEvent.instanceSafe.InstantiateProgressBar(container);

        milestoneEventProgressBar = MilestoneEvent.instanceSafe.GetInstantiatedProgressBar();
        CalculateOffsetFeature();
    }

    public const int START_OFFSET       = -10;
    public const int FEATURE_UI_SPACING = 5;

    private void CalculateOffsetFeature() {
        if (UserProgressionController.EnableFeature) {
            return;
        }

        _offsetFeature =
            _showInstantMission || _showStarJourney || showMilestoneEvent
                ? START_OFFSET
                : 0; // độ lệch của UI với từng feature
        _rootMainMenu = 0; // tọa độ trục y

        if (isLocked) {
            if (HomeManager.instance.isTopBarFixed) {
                _rootMainMenu = -SONG_LIST_PADDING_TOP_DEFAULT + TopBarHome.heightOfTopBar;
            } else {
                _rootMainMenu = -SONG_LIST_PADDING_TOP_DEFAULT;
            }
        }

        _rootMainMenu += FEATURE_PADDING_SONGLIST;

        if (remoteConfig.Hybrid_UI_Rearrange) {
            _offsetFeature += 35;
        }

        if (_showInstantMission) {
            _offsetInstantMission = _offsetFeature;
            instantMission.rectTransCache.localPosition = new Vector3(0, _rootMainMenu - _offsetInstantMission);
            _offsetFeature += InstantMissionUI.INSTANT_MISSION_HEIGHT + FEATURE_UI_SPACING;
        }

        if (showMilestoneEvent) {
            if (milestoneEventProgressBar != null) {
                milestoneEventProgressBar.transform.localPosition = new Vector3(0, _rootMainMenu - _offsetFeature);
            }

            _offsetMilestoneEvent = _offsetFeature;
            _offsetFeature += MilestoneEventProgressBar.HEIGHT + FEATURE_UI_SPACING;
        }

        if (_showStarJourney) {
            starsJourneyBar.transform.localPosition = new Vector3(0, _rootMainMenu - _offsetFeature);
            _offsetStarJourney = _offsetFeature;
            _offsetFeature += StarJourneyBar.STAR_JOURNEY_BAR_HEIGHT + FEATURE_UI_SPACING;
        }

        ChangeSizeMenu();
    }

    protected virtual void UpdateFeatureAnchor(Vector2 rtfTargetAnchoredPosition) {
        Vector2 anchorPosition = Vector2.zero;
        if (_showInstantMission) {
            anchorPosition.y = rtfTargetAnchoredPosition.y - SONG_LIST_PADDING_TOP_DEFAULT - _offsetInstantMission +
                               FEATURE_PADDING_SONGLIST * 2;
            instantMission.UpdateAnchor(anchorPosition);
        }

        if (showMilestoneEvent && milestoneEventProgressBar != null) {
            anchorPosition.y = rtfTargetAnchoredPosition.y - SONG_LIST_PADDING_TOP_DEFAULT - _offsetMilestoneEvent +
                               FEATURE_PADDING_SONGLIST * 2;

            float alignAnchor = MilestoneEventProgressBar.ALIGN_ANCHOR;

            if (anchorPosition.y > alignAnchor) {
                anchorPosition.y = alignAnchor;
            }

            milestoneEventProgressBar.UpdateAnchor(anchorPosition);
        }

        if (_showStarJourney) {
            anchorPosition.y = rtfTargetAnchoredPosition.y - SONG_LIST_PADDING_TOP_DEFAULT - _offsetStarJourney +
                               FEATURE_PADDING_SONGLIST * 2;

            float alignAnchor = StarJourneyBar.alignAnchor;

            if (anchorPosition.y > alignAnchor && !showMilestoneEvent) {
                anchorPosition.y = alignAnchor;
            }

            starsJourneyBar.UpdateAnchor(anchorPosition);
        }
    }

    protected virtual string GetStarterPackEntryResourceName() {
        return "UI/btnStarterPackLeft_V";
    }

    private void SetupStarterPackEntryPoint() {
        if (btnStarterPack == null) {
            var asset = Resources.Load<Button>(Util.BuildString(string.Empty, GetStarterPackEntryResourceName(),
                RemoteConfigBase.instance.StarterPack_Version));

            if (asset != null) {
                btnStarterPack = Instantiate(asset, starterPackEntryContainer);
            }
        }

        if (btnStarterPack != null) {
            btnStarterPack.onClick.AddListener(btnStarterPackOnClick);
        }
    }

    private void BtnAdminClick() {
        homeManager.AdminButton_click();
    }

    private void OnBtn7DayMissionClick() {
        homeManager.OnBtn7DayMissionClick();
    }

    protected virtual void BtnAchievementClick() {
        homeManager.ShowAchievement();
    }

    public void BtnShopBallClick() {
        homeManager.ShowBallList(-1);
    }

    private void ShowSettings() {
        AnalyticHelper.Button_Click(BUTTON_NAME.Settings);
        homeManager.ShowSettings();
    }

    private void BtnGiftOnClick() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.FreeVideo);
        FreeVideo.ShowPopUp();
    }

    public void BtnShopDiamondOnClick() {
        topbarHome.ShowShop(true);
    }

    private void btnVipMissionOnClick() {
        homeManager.btnVipMissionOnClick();
    }

    protected virtual void UpdateUIMainButtons() {
        byte tempCount = 0;
        try {
            adminButton.gameObject.SetActive(Configuration.instance.enableContentTool ||
                                             Configuration.instance.isDebug);
            tempCount++;

            if (remoteConfig.ShopBallInHome_IsEnable && !remoteConfig.Hybrid_UI_Rearrange) {
                bool isShopBallButtonV2 = remoteConfig.ShopBall_EntryPointV2_IsEnable;
                btnShopBall.gameObject.SetActive(!isShopBallButtonV2);
                btnShopBallV2.gameObject.SetActive(isShopBallButtonV2);
            } else {
                btnShopBall.gameObject.SetActive(false);
                btnShopBallV2.gameObject.SetActive(false);
            }

            tempCount++;
            btn7DayMission.gameObject.SetActive(SevenDayMission.isInstanced && SevenDayMission.instanceSafe.IsEnable());
            btnAchievement.gameObject.SetActive(IsActiveAchievement());
            tempCount++;
            btnMission.gameObject.SetActive(MissionManager.isInstanced);

            tempCount++;
            btnShopDiamond.gameObject.SetActive(IsShowButtonShopDiamondInHome);
            shopDiamondBadgeHolder.ShowBadge(IsHaveBadgeShopDiamond);
            tempCount++;
            btnPlayTimeReward.SetActive(PlaytimeReward.isInstanced && PlaytimeReward.instance.IsActive);

            tempCount++;
            CheckShowButtonGift();
            tempCount++;
            CheckShowButtonMysteryBox();
            tempCount++;
            CheckShowButtonSpecialOffer();
            tempCount++;
            CheckShowButtonGalaxyQuest();
            tempCount++;
            CheckShowButtonMysteryDoor();
            tempCount++;
            CheckShowButtonEndlessOffer();
            tempCount++;
            CheckShowButtonIAPTripleOffers();
            tempCount++;
        } catch (Exception) {
            CustomException.Fire("[UIMenuHome][UpdateUIMainButtons]", $"TempCount : {tempCount}");
        }
    }

    public virtual void CheckNoAds() {
        if (remoteConfig.VideoDiamondAtHome_Enable && !remoteConfig.Subscription_Enable) {
            videoDiamondButton.SetActive(true);
            if ((Configuration.IsNoAllAds() || Configuration.IsNoSomeAds()) && remoteConfig.Enable_IAP) {
                videoDiamondButton.transform.Find("BtnVideo").gameObject.SetActive(false);
                videoDiamondButton.transform.Find("BtnShop").gameObject.SetActive(true);
            } else {
                videoDiamondButton.transform.Find("BtnVideo").gameObject.SetActive(true);
                videoDiamondButton.transform.Find("BtnShop").gameObject.SetActive(false);
            }
        } else {
            videoDiamondButton.SetActive(false);
        }
    }

    private void btnNoFSAdsOnClick() {
        SoundManager.PlayGameButton();
        Util.ShowPopUp(PopupName.EconomyOfferNoFSAds);
        AnalyticHelper.Button_Click(BUTTON_NAME.HomeNoAds);
        EconomyIAPTracker.Track_NoFSAdsHomeEntryClick();
    }

    private void btnStarterPackOnClick() {
        SoundManager.PlayGameButton();
        var popup = EconomyOfferStarterPack.Show();
        popup.GetComponent<EconomyOfferStarterPack>().SetLocation(EconomyIAPTracker.TRACK_LOCATION.home);
        EconomyIAPTracker.Track_StarterPackEntryClick(EconomyIAPTracker.TRACK_LOCATION.home,
            Configuration.instance.GetDiamonds());
        AnalyticHelper.Button_Click(BUTTON_NAME.HomeStarterPack);
    }

    protected virtual void SetActiveButtonNoFSAds(bool isShow) {
        btnNoFSAds.gameObject.SetActive(isShow);
    }

    public virtual void SetActiveButtonStarterPack(bool isShow) {
        if (starterPackEntryContainer != null) {
            starterPackEntryContainer.gameObject.SetActive(isShow);
        } else if (btnStarterPack != null) {
            btnStarterPack.gameObject.SetActive(isShow);
        }

        if (!isShow) {
            return;
        }

        if (remoteConfig.Hybrid_UI_Rearrange) {
            PutToRightMenu(starterPackEntryContainer, -1);
        } else {
            UpdateMenuLeftWithButtonsPriority();
        }
    }

    public void ShowAchievementBadge(int badgeCount) {
        achievementBadgeHolder.ShowBadge(badgeCount);
    }

    public void SetActiveVipMissionBadge(bool isShow) {
        vipMissionBadgeHolder.ShowBadge(isShow);
    }

    public virtual void SetActiveButtonVipMission(bool isShow) {
        btnVipMission.gameObject.SetActive(isShow);
    }

    public void SetActiveButton7DayMission(bool isShow) {
        btn7DayMission.gameObject.SetActive(isShow);
    }

    public void UpdateUiNoti7DayMission() {
        if (SevenDayMission.instanceSafe.IsEnable() && mission7DayBadgeHolder != null) {
            int notificationCount = SevenDayMission.instanceSafe.GetNotificationCount();
            mission7DayBadgeHolder.ShowBadge(notificationCount);
        }
    }

    protected virtual void CheckShowButtonMysteryBox() {
        if (MysteryBox.IsHaveButtonMysteryBoxInHome) {
            if (UXHomeDropDown.isEnable &&
                UXHomeDropDown.supportedGroupButtons.Contains(HomeEntryPointName.MysteryBox)) {
                if (!_isInitMysteryBox) {
                    _isInitMysteryBox = true;
                    MysteryBox.CreateButtonAtHomeDropdownGroup(_groupDropdownButtons.itemContainer);
                }

                btnMysteryBox.gameObject.SetActive(false);
            } else {
                if (!_isInitMysteryBox) {
                    _isInitMysteryBox = true;
                    MysteryBox.CreateButtonAtHome(btnMysteryBox);
                }

                btnMysteryBox.gameObject.SetActive(true);
            }
        } else {
            btnMysteryBox.gameObject.SetActive(false);
        }
    }

    public virtual void CheckShowButtonSpecialOffer() {
        bool isShownSpecialOffer = SpecialOfferManager.isInstanced && SpecialOfferManager.instanceSafe.IsActive();
        if (isShownSpecialOffer) {
            if (!_isInitSpecialOffer) {
                _isInitSpecialOffer = true;
                SpecialOfferManager.instanceSafe.CreateButtonAtHome(btnSpecialOffer, false);
            }

            btnSpecialOffer.gameObject.SetActive(true);

            if (remoteConfig.Hybrid_UI_Rearrange) {
                PutToRightMenu(btnSpecialOffer, -1);
            } else {
                UpdateMenuLeftWithButtonsPriority();
            }
        } else {
            btnSpecialOffer.gameObject.SetActive(false);
        }
    }

    public virtual void CheckShowButtonGift() {
        btnGift.gameObject.SetActive(IsNeedShowBtnGift);
    }

    public void SetActiveShopDiamondBadge(bool isReady) {
        // shopDiamondBadge.SetActive(isReady);
        shopDiamondBadgeHolder.ShowBadge(isReady);
    }

    public void SetActiveNotificationBox(bool isShow, int value) {
        if (isShow) {
            objNotificationBox.ShowBadge(value);
        } else {
            objNotificationBox.ShowBadge(false);
        }
    }

    public virtual bool CheckShowTooltipPlayTimeReward() {
        if (!PlaytimeReward.isInstanced)
            return false;

        if (PlaytimeReward.instance.IsActive && btnPlayTimeReward.gameObject.activeSelf) {
            if (!PlaytimeReward.instance.IsShownTooltipButton) {
                if (btnPlayTimeReward.TryGetComponent(out UIButtonPlaytimeReward uiButtonPlaytimeReward)) {
                    uiButtonPlaytimeReward.ShowToolTip();
                    return true;
                }
            }
        }

        return false;
    }

    protected void SetActiveButtonVip(int index, bool active) {
        if (index < 0)
            return;
        if (index >= vipButtons.Length)
            return;

        vipButtons[index].gameObject.SetActive(active);
    }

    /// <summary>
    /// Donot call in Awake()
    /// </summary>
    public void UpdateMenuLeftWithButtonsPriority() {
        if (!remoteConfig.Home_LeftMenu_ButtonPriorities_IsEnable) {
            return;
        }

        if (tfLeftMenu == null) {
            return;
        }

        int count = tfLeftMenu.childCount;
        List<KeyValuePair<GameObject, int>> priorities = ListPool<KeyValuePair<GameObject, int>>.Get();

        // init list priorities
        int adminButtonInstanceId = adminButton.gameObject.GetInstanceID();
        for (int i = 0; i < count; i++) {
            GameObject child = tfLeftMenu.GetChild(i).gameObject;
            int instanceId = child.GetInstanceID();
            if (!child.activeSelf || instanceId == adminButtonInstanceId) {
                continue;
            }

            if (ButtonPriority.buttonsMap.ContainsKey(instanceId) && ButtonPriority.buttonsMap[instanceId] != null) {
                priorities.Add(
                    new KeyValuePair<GameObject, int>(child, ButtonPriority.buttonsMap[instanceId].priority));
            } else if (child.TryGetComponent(out ButtonPriority buttonPriority)) {
                priorities.Add(new KeyValuePair<GameObject, int>(child, buttonPriority.priority));
            } else {
                priorities.Add(new KeyValuePair<GameObject, int>(child, 100));
            }
        }

        // Sort by descending priority
        priorities.Sort((pair1, pair2) => pair2.Value.CompareTo(pair1.Value));

        // Show only 3 buttons with highest priority
        int showCount = 0;
        foreach (var pair in priorities) {
            if (showCount < 3) {
                pair.Key.SetActive(true);
                showCount++;
            } else {
                pair.Key.SetActive(false);
            }
        }
    }

    //TODO: mong muống sử dụng siblingIndex để set thứ tự của button nhưng đang bị tính cả những disabled object 
    public void PutToLeftMenu(Transform tfChild, int siblingIndex) {
        tfChild.SetParent(tfLeftMenu);
        tfChild.SetSiblingIndex(siblingIndex < 0 ? tfLeftMenu.childCount + siblingIndex - 1 : siblingIndex);
        tfChild.SetLocalZ(0f);
        UpdateMenuLeftWithButtonsPriority();

        if (remoteConfig.Hybrid_UI_Rearrange && UXHomeDropDown.isEnable) {
            PutGroupButtonToLast();
        }
    }

    public void PutToRightMenu(Transform tfChild, int siblingIndex) {
        tfChild.SetParent(tfRightMenu);
        tfChild.SetSiblingIndex(siblingIndex < 0 ? tfRightMenu.childCount + siblingIndex - 1 : siblingIndex);
        tfChild.SetLocalZ(0f);

        CheckUpdateSeasonalPackBtnPositionIfRightMenuOverflow();
    }

    private void CheckUpdateSeasonalPackBtnPositionIfRightMenuOverflow() {
        if (_btnSeasonal == null) {
            return;
        }

        if (!_btnSeasonal.transform.IsChildOf(tfRightMenu)) {
            return;
        }

        if (!CheckRightMenuOverflow()) {
            return;
        }

        PutToLeftMenu(_btnSeasonal.transform, -1);
    }

    private int CountRightMenuVisibleButtons() {
        byte visibleCount = 0;
        foreach (Transform child in tfRightMenu) {
            if (child.gameObject.activeSelf) {
                visibleCount++;
            }
        }

        return visibleCount;
    }

    public bool CheckRightMenuFull() {
        return CountRightMenuVisibleButtons() >= 5; // tính cả button setting + max 4 buttons
    }

    public bool CheckRightMenuOverflow() {
        return CountRightMenuVisibleButtons() > 5; // quá slot tối đa là 5
    }

    public virtual void UpdateMenuByFixedTopBar() {
        float localY = tfLeftMenu.GetLocalY();
        tfRightMenu.SetLocalY(localY); // kéo right xuống bằng left
        topbarHome.CompleteTween();

        // move button 7-day mission sang left menu nếu button này không có trong list gom nhóm
        if (!UXHomeDropDown.isEnable ||
            !UXHomeDropDown.supportedGroupButtons.Contains(HomeEntryPointName.SevenDayMission)) {
            PutToLeftMenu(btn7DayMission.transform, 1);
        }

        MoveVIPButtonsToTopBar(style: 1);

        topbarHome.CheckTurnOffStarterPack();

        tfTopBarHome.SetParent(safeArea2);
        if (HomeManager.CachedTabActived == BottomMenuScript.Type.Search) {
            //Force show fixed top bar home in discovery/search tab
            tfTopBarHome.localScale = Vector3.one;
        } else {
            tfTopBarHome.localScale = this.gameObject.activeSelf ? Vector3.one : Vector3.zero;
        }

        tfTopBarHome.GetComponent<RectTransform>().DOAnchorPosY(0f, 0f);

        rectTransform.SetAnchoredPosition3DPosY(TopBarHome.heightOfTopBar);
    }

    private float _cacheLocalXLeftMenu;
    private float _cacheLocalXRightMenu;

    public virtual void SetActiveSongList(bool isActive) {
        if (isActive) { //Show
            _activeTweens.Add(tfLeftMenu.DOLocalMoveX(_cacheLocalXLeftMenu, 0.3f));
            _activeTweens.Add(tfRightMenu.DOLocalMoveX(_cacheLocalXRightMenu, 0.3f));

            ToggleVipButtonHolder(true);
        } else { //Hide
            if (_cacheLocalXLeftMenu == 0 || _cacheLocalXRightMenu == 0) {
                _cacheLocalXLeftMenu = tfLeftMenu.GetLocalX();
                _cacheLocalXRightMenu = tfRightMenu.GetLocalX();
            }

            _activeTweens.Add(tfLeftMenu.DOLocalMoveX(2 * _cacheLocalXLeftMenu, 0.3f));
            _activeTweens.Add(tfRightMenu.DOLocalMoveX(2 * _cacheLocalXRightMenu, 0.3f));

            ToggleVipButtonHolder(false);
        }
    }

    private void ToggleVipButtonHolder(bool isShow) {
        var vipHolder = topbarHome.GetVipHolder(HomeManager.instance.isTopBarFixed ? 1 : 2);
        if (vipHolder != null) {
            vipHolder.gameObject.SetActive(isShow);
        }
    }

    public void Scroll(Vector2 rtfTargetAnchoredPosition) {
        rectTransform.anchoredPosition = rtfTargetAnchoredPosition;
        UpdateFeatureAnchor(rtfTargetAnchoredPosition);
    }

    public virtual void UpdateVIPUI(bool isTopBarFixed) {
        for (int i = 0; i < 3; i++) {
            SetActiveButtonVip(i, false);
        }

        bool isShowNoFSAdsBtn = false;
        bool isShownStarterPack = Configuration.instance.CanShowStarterPack();

        if (remoteConfig.Economy_IsEnable) {
            var economyIAPConfig = Configuration.instance.GetEconomyRemoteConfig();
            if (economyIAPConfig != null) {
                if (economyIAPConfig.NoFsAds_Enable) {
                    isShowNoFSAdsBtn = Configuration.CanShowOfferNoFSAds();
                }
            }
        }

        if (isShowNoFSAdsBtn) {
            isShownStarterPack = false;
        }

        if (isTopBarFixed && topbarHome.IsShowingStarterPack()) {
            // tránh duplicate button starterPack
            isShownStarterPack = false;
        }

        SetActiveButtonNoFSAds(isShowNoFSAdsBtn);
        SetActiveButtonStarterPack(isShownStarterPack);

        CheckShowButtonSpecialOffer();

        int index = -1;
        if (SubscriptionController.IsEnableSubscription()) {
            if (remoteConfig.SubscriptionOnboarding_Enable && (SubscriptionController.IsSubscriptionVip() ||
                                                               SubscriptionController.IsSubscription())) {
                index = 2; //Đã mua vip -> hiển thị nút vip benefit
            } else if (remoteConfig.SubscriptionOnboarding_Enable && !SubscriptionController.IsSubscriptionVip() &&
                       !SubscriptionController.IsSubscription()) {
                index = 1;
            } else if (!remoteConfig.SubscriptionOnboarding_Enable && !SubscriptionController.IsSubscriptionVip() &&
                       !SubscriptionController.IsSubscription()) {
                index = 0;
            }
        }

        if (index >= 0 && (index == 2 || !isTopBarFixed)) {
            if (isTopBarFixed) {
                SetActiveButtonVip(index, true);
                topbarHome.CheckTurnOffStarterPack();
            } else {
                if (!isShowNoFSAdsBtn) {
                    bool isShowRectButtonVip = remoteConfig.Hybrid_UI_Rearrange;
                    if (isShowRectButtonVip) {
                        bool canSwitchButtonVip = true;
                        if (_rectangleVipButton == null || _lastIdRectangle != index) {
                            canSwitchButtonVip = false;
                            var assetVipButton = ResourcesManager.Load<Button>(index != 2
                                ? "UI/VipButtonRectangle" //no vip
                                : "UI/VipButtonv3Rectangle"); //vip
                            if (assetVipButton != null) {
                                _lastIdRectangle = index;
                                _rectangleVipButton = Instantiate(assetVipButton, topBarHome.tfGroupHUD);
                                _rectangleVipButton.onClick.RemoveAllListeners();
                                _rectangleVipButton.onClick = vipButtons[index].onClick;
                                canSwitchButtonVip = true;
                            }
                        }

                        if (canSwitchButtonVip) {
                            SetActiveButtonVip(index, false);
                            vipButtons[index] = _rectangleVipButton;
                            SetActiveButtonVip(index, true);
                        }
                    } else {
                        SetActiveButtonVip(index, true);
                    }
                }
            }
        }

        CheckNoAds();

        if (SubscriptionController.IsEnableSubscription() && SubscriptionController.IsSubscriptionVip()) {
            topbarHome.ShowMaskProfileVip(true);

            Util.WaitBallManagerInitDone(UpdateVipMissionEntryPoint);

            if (!Configuration.CanEarnDiamond()) {
                SetActiveButton7DayMission(false);
            }

            CheckShowButtonGift();
        } else {
            topbarHome.ShowMaskProfileVip(false);
            SetActiveButtonVipMission(false);
        }
    }

    protected virtual void UpdateVipMissionEntryPoint() {
        if (VipMissionManager.IsShowVipMission()) {
            SetActiveButtonVipMission(true);
            if (VipMissionManager.vipMissionData.IsCompleteAndNotOpenGift()) {
                SetActiveVipMissionBadge(true);
            }
        } else {
            SetActiveButtonVipMission(false);
        }
    }

    public virtual void CheckTooltipShopBall() {
        if (remoteConfig.ShopBall_TooltipNewBalls_IsEnable && BallManager.instance.CheckHaveNewBalls()) {
            GameObject tooltipPrefab = ResourcesManager.Load<GameObject>("UI/TooltipExploreNewBalls");
            if (tooltipPrefab != null) {
                _tooltipShopBall = Instantiate(tooltipPrefab, transform);
                _tooltipShopBall.transform.position = remoteConfig.ShopBall_EntryPointV2_IsEnable
                    ? btnShopBallV2.transform.position
                    : btnShopBall.transform.position;
            }
        }
    }

    public void TurnOffToolTipShopBall() {
        if (_tooltipShopBall != null) {
            _tooltipShopBall.SetActive(false);
        }
    }

    GameObject _btnSeasonal;

    public virtual void CreateButtonSeasonalPack() {
        var prefButton = ResourcesManager.Load<GameObject>(IAP_SeasonalPackCaller.buttonName);
        if (prefButton) {
            _btnSeasonal = Instantiate(prefButton);

            if (!remoteConfig.Hybrid_UI_Rearrange || CheckRightMenuFull()) {
                PutToLeftMenu(_btnSeasonal.transform, -1);
            } else {
                PutToRightMenu(_btnSeasonal.transform, 100);
            }

            _btnSeasonal.transform.localScale = Vector3.one;
        } else {
            Logger.LogError($"[CreateButtonSeasonalPack] => {IAP_SeasonalPackCaller.buttonName} not found");
        }
    }

    public virtual GameObject CreateButtonDiscoverChallenge() {
        var entryPointPrefab = ResourcesManager.Load<GameObject>(ResourcesPath.DiscoverChallenge_HomeButton);
        if (entryPointPrefab != null) {
            if (UXHomeDropDown.isEnable &&
                UXHomeDropDown.supportedGroupButtons.Contains(HomeEntryPointName.DiscoverChallenge)) {
                ButtonWithBadge discoverChallengeButton = null;
                if (_groupDropdownButtons.AddButton(HomeEntryPointName.DiscoverChallenge.ToString(),
                        ref discoverChallengeButton)) {
                    return discoverChallengeButton.gameObject;
                }

                return null;
            }

            var btnDiscoverChallenge = Instantiate(entryPointPrefab);
            PutToLeftMenu(btnDiscoverChallenge.transform, -1);
            btnDiscoverChallenge.transform.localScale = Vector3.one;
            btnDiscoverChallenge.transform.SetLocalZ(0);
            return btnDiscoverChallenge;
        }

        return null;
    }

    protected virtual void CreateButtonGalaxyQuest() {
        if (_galaxyQuestEntryPoint != null) {
            return;
        }

        var prefButton = ResourcesManager.Load<GameObject>(ResourcesPath.GalaxyQuest_HomeButton);
        if (prefButton) {
            _galaxyQuestEntryPoint = Instantiate(prefButton);

            if (remoteConfig.Hybrid_UI_Rearrange) {
                PutToLeftMenu(_galaxyQuestEntryPoint.transform, 0);
            } else {
                PutToLeftMenu(_galaxyQuestEntryPoint.transform, -1);
                StartCoroutine(CreateTempButtonForGalaxyQuestEntryIfNeeded());
            }

            _galaxyQuestEntryPoint.transform.localScale = Vector3.one;
        } else {
            Logger.LogError($"[CreateButtonGalaxy] => {ResourcesPath.GalaxyQuest_HomeButton} not found");
        }
    }

    protected GameObject _btnEndlessOffer;

    protected virtual void CreateButtonEndlessOffer() {
        if (_btnEndlessOffer != null) {
            if (_btnEndlessOffer.gameObject.activeSelf) {
                return;
            }

            _btnEndlessOffer.gameObject.SetActive(true);
        } else {
            var prefButton = ResourcesManager.Load<GameObject>(ResourcesPath.Endless_HomeButton);
            if (prefButton != null) {
                _btnEndlessOffer = Instantiate(prefButton);
            }
        }

        if (_btnEndlessOffer != null) {
            var data = EndlessOffer.instanceSafe.GetBalancyDataUI();
            if (data is {DisplayHomeIcon: not null}) {
                var position = data.DisplayHomeIcon.Position;
                switch (position) {
                    case HomeIconPosition.LeftTop:
                        PutToLeftMenu(_btnEndlessOffer.transform, 0);
                        break;

                    case HomeIconPosition.LeftBottom:
                        PutToLeftMenu(_btnEndlessOffer.transform, 100);
                        break;

                    case HomeIconPosition.RightTop:
                        PutToRightMenu(_btnEndlessOffer.transform, 1);
                        break;

                    case HomeIconPosition.RightBottom:
                        PutToRightMenu(_btnEndlessOffer.transform, 100);
                        break;

                    default:
                        PutToLeftMenu(_btnEndlessOffer.transform, 0);
                        break;
                }
            } else if (remoteConfig.Hybrid_UI_Rearrange) {
                PutToRightMenu(_btnEndlessOffer.transform, 1);
            } else {
                PutToLeftMenu(_btnEndlessOffer.transform, -1);
            }

            _btnEndlessOffer.transform.localScale = Vector3.one;
        } else {
            Logger.LogError($"[CreateButtonIAPEndlessOffers] => {ResourcesPath.Endless_HomeButton} not found");
        }
    }

    protected virtual void CreateButtonIAPTripleOffers() {
        if (tripleOffersEntryPoint != null) {
            if (tripleOffersEntryPoint.gameObject.activeSelf) {
                return;
            }

            tripleOffersEntryPoint.gameObject.SetActive(true);
        } else {
            var prefButton = ResourcesManager.Load<GameObject>(ResourcesPath.IAPTripleOffers_HomeButton);
            if (prefButton != null) {
                tripleOffersEntryPoint = Instantiate(prefButton);
            }
        }

        if (tripleOffersEntryPoint != null) {
            var data = IAPTripleOffers.instanceSafe.GetBalancyDataUI();
            if (data is {DisplayHomeIcon: not null}) {
                var position = data.DisplayHomeIcon.Position;
                switch (position) {
                    case HomeIconPosition.LeftTop:
                        PutToLeftMenu(tripleOffersEntryPoint.transform, 0);
                        break;

                    case HomeIconPosition.LeftBottom:
                        PutToLeftMenu(tripleOffersEntryPoint.transform, 100);
                        break;

                    case HomeIconPosition.RightTop:
                        PutToRightMenu(tripleOffersEntryPoint.transform, 1);
                        break;

                    case HomeIconPosition.RightBottom:
                        PutToRightMenu(tripleOffersEntryPoint.transform, 100);
                        break;

                    default:
                        PutToLeftMenu(tripleOffersEntryPoint.transform, 0);
                        break;
                }
            } else if (remoteConfig.Hybrid_UI_Rearrange) {
                PutToRightMenu(tripleOffersEntryPoint.transform, 1);
            } else {
                PutToLeftMenu(tripleOffersEntryPoint.transform, -1);
            }

            tripleOffersEntryPoint.transform.localScale = Vector3.one;
        } else {
            Logger.LogError($"[CreateButtonIAPTripleOffers] => {ResourcesPath.IAPTripleOffers_HomeButton} not found");
        }
    }

    public virtual void SetActive(bool isActive) { }

    protected void ChangeSizeMenu() {
        OnChangeSize?.Invoke();
    }

    public void ShowMissionBadge(int badgeCount) {
        if (missionBadge != null) {
            missionBadge.ShowBadge(badgeCount);
        }
    }

    public virtual void MoveVIPButtonsToTopBar(int style = 1) {
        topbarHome.vipHolderPosition1.gameObject.SetActive(false);
        topbarHome.vipHolderPosition2.gameObject.SetActive(false);

        Transform parent;
        int siblingIndex = 0;

        if (remoteConfig.Hybrid_UI_Rearrange) {
            parent = topBarHome.tfGroupHUD;
            siblingIndex = 100;

            // set listeners because of replacing UI-Menu
            for (int i = 0; i < 3; i++) {
                var vipButtonObject = vipButtons[i].gameObject;
                vipButtonObject.SetActive(false);
                switch (i) {
                    case 0:
                    case 1:
                        vipButtons[i].onClick.AddListener(BtnVipClick);
                        break;

                    case 2:
                        vipButtons[i].onClick.AddListener(BtnVipBenefitClick);
                        break;

                    default:
                        vipButtons[i].onClick.AddListener(BtnVipClick);
                        break;
                }
            }
        } else {
            parent = style switch {
                1 => topbarHome.vipHolderPosition1,
                2 => topbarHome.vipHolderPosition2,
                _ => topbarHome.vipHolderPosition1
            };
        }

        parent.gameObject.SetActive(true);
        foreach (var vipButton in vipButtons) {
            topbarHome.MoveButtonVip(vipButton.transform, parent, siblingIndex);
        }
    }

    protected void BtnVipBenefitClick() {
        homeManager.ShowSubscriptionOnboarding();
    }

    protected void BtnVipClick() {
        homeManager.VipButton_Click();
    }

    private void PutGroupButtonToLast() {
        // Due to some async calls, it's a need to check whether the object still existed or not 
        if (!_groupDropdownButtons) {
            return;
        }

        // Set sibling index rất lớn để chắc chắn groupbutton luôn ở cuối
        _groupDropdownButtons.transform.SetSiblingIndex(100);
    }

    private void EnableGroupButtons() {
        string resourceName;
        if (RemoteConfigBase.instance.Home_RightMenu_Toggle_Version == 2) {
            resourceName = "Buttons/GroupToggleMenu";
        } else {
            resourceName = remoteConfig.Hybrid_UI_Rearrange ? "Buttons/LeftDropdownMenu" : "Buttons/RightDropdownMenu";
        }

        var resource = Resources.Load<DropdownButton>(resourceName);
        if (resource == null) {
            Debug.LogError("[SetupUIMainButtons][Ux Icons] Cannot load resource");
            return;
        }

        // create button at the bottom of right menu
        _groupDropdownButtons = Instantiate(resource, remoteConfig.Hybrid_UI_Rearrange ? tfLeftMenu : tfRightMenu);
        _groupDropdownButtons.transform.SetSiblingIndex(tfRightMenu.childCount);

        var listButtons = remoteConfig.Home_RightMenu_Toggle_Features.StringToList();
        isSetVipMissionToDropdown = false;
        foreach (string entryPointName in listButtons) {
            bool isAvailable = Enum.TryParse(entryPointName, true, out HomeEntryPointName homeEntryPointName);
            if (!isAvailable) {
                Debug.LogError($"[SetupUIMainButtons] Cannot parse entry point name: {entryPointName}");
                continue;
            }

            UXHomeDropDown.supportedGroupButtons.Add(homeEntryPointName);

            ButtonWithBadge item = null;
            switch (homeEntryPointName) {
                // static button
                case HomeEntryPointName.FreeGift:
                    if (_groupDropdownButtons.AddButton(homeEntryPointName.ToString(), ref item)) {
                        btnGift.gameObject.SetActive(false);
                        btnGift = item.itemButton;
                    }

                    break;

                // static button
                case HomeEntryPointName.ShopIAP:
                    if (_groupDropdownButtons.AddButton(homeEntryPointName.ToString(), ref item)) {
                        btnShopDiamond.gameObject.SetActive(false);
                        btnShopDiamond = item.itemButton;
                        shopDiamondBadgeHolder = item.GetBadgeHolderAndSetupTrigger();
                    }

                    break;

                // static button
                case HomeEntryPointName.SevenDayMission:
                    if (_groupDropdownButtons.AddButton(homeEntryPointName.ToString(), ref item)) {
                        btn7DayMission.gameObject.SetActive(false);
                        btn7DayMission = item.itemButton;
                        mission7DayBadgeHolder = item.GetBadgeHolderAndSetupTrigger();
                    }

                    break;

                // static button
                case HomeEntryPointName.Achievement:
                    if (IsActiveAchievement() &&
                        _groupDropdownButtons.AddButton(homeEntryPointName.ToString(), ref item)) {
                        btnAchievement.gameObject.SetActive(false);
                        btnAchievement = item.itemButton;
                        achievementBadgeHolder = item.GetBadgeHolderAndSetupTrigger();
                    }

                    break;

                // dynamic button
                case HomeEntryPointName.DiscoverChallenge:
                    UXHomeDropDown.supportedGroupButtons.Add(HomeEntryPointName.DiscoverChallenge);
                    break;

                // dynamic button
                case HomeEntryPointName.MysteryBox:
                    UXHomeDropDown.supportedGroupButtons.Add(HomeEntryPointName.MysteryBox);
                    break;

                // static button
                case HomeEntryPointName.DailyMission:
                    if (_groupDropdownButtons.AddButton(homeEntryPointName.ToString(), ref item)) {
                        btnMission.gameObject.SetActive(false);
                        btnMission = item.itemButton;
                        missionBadge = item.GetBadgeHolderAndSetupTrigger();
                    }

                    break;

                case HomeEntryPointName.VipMission:
                    if (_groupDropdownButtons.AddButton(homeEntryPointName.ToString(), ref item)) {
                        GameObject oldButton = btnVipMission.gameObject;
                        item.gameObject.SetActive(oldButton.activeSelf);
                        oldButton.SetActive(false);
                        btnVipMission = item.itemButton;
                        vipMissionBadgeHolder = item.GetBadgeHolderAndSetupTrigger();
                        isSetVipMissionToDropdown = true;
                    }

                    break;
            }
        }

        if (isSetVipMissionToDropdown || remoteConfig.Hybrid_UI_Rearrange) {
            return;
        }

        // move vip mission to topbar
        var vipMissionButtonAsset = Resources.Load<ButtonWithBadge>("Buttons/btnVipMissionOnTopBar");
        if (vipMissionButtonAsset != null) {
            btnVipMission.gameObject.SetActive(false);
            var vipHolder = topbarHome.GetVipHolder(HomeManager.instance.isTopBarFixed ? 1 : 2);
            var itemVipMission = Instantiate(vipMissionButtonAsset, vipHolder);
            itemVipMission.transform.SetSiblingIndex(vipHolder.childCount);
            btnVipMission = itemVipMission.itemButton;
            vipMissionBadgeHolder = itemVipMission.GetBadgeHolderOnly();
        }
    }

    public void ShowStarJourney() {
        if (starsJourneyBar) {
            if (HomeManager.instance.IsShowingFullScreenPopup() || !remoteConfig.Hybrid_UI_Rearrange) {
                Util.ShowStarJourney();
            } else {
                HomeManager.instance.HandleOnClickTabStarJourney();
            }
        }
    }

    public void ShowProfile() {
        if (topbarHome) {
            topbarHome.ShowProfile();
        }
    }

    public void ProcessBeforeDestroyObject() {
        if (starsJourneyBar != null) {
            Destroy(starsJourneyBar.gameObject);
        }

        if (instantMission != null) {
            Destroy(instantMission.gameObject);
        }

        _isPrepareDestroying = true;
    }

    private void SongItemOnOnDualUnlockFeature() {
        if (inCoroutineTooltipDualUnlock) {
            return;
        }

        if (PlayerPrefs.HasKey(Util.KEY_TOOLTIP_DUALUNLOCK)) // already show tooltip
            return;

        StartCoroutine(IEShowTooltipDualUnlock());
    }

    protected virtual IEnumerator IEShowTooltipDualUnlock() {
        inCoroutineTooltipDualUnlock = true;

        while (HomeManager.isInstanced && HomeManager.instance.isInPopupFlow) {
            yield return null;
        }

        var songScroller = SongList.instance.SongScroller.ScrollerAdapter;
        yield return Inwave.Utils.WaitUntil(() => songScroller.IsInitialized);

        bool _foundItem = false;
        var visibleCount = songScroller.VisibleItemsCount;
        for (int i = 0; i < visibleCount; i++) {
            var viewHolder = songScroller.GetItemViewsHolder(i);
            if (viewHolder is SongCardViewHolder songCardItem && ((SongItem) songCardItem.cellView).song.isDualUnlock) {
                _foundItem = true;
                break;
            }
        }

        if (_foundItem) {
            Util.CheckShowTooltipDualUnlock(homeManager.transform);
        }

        inCoroutineTooltipDualUnlock = false;
        yield return null;
    }

    private void SetupListeners() {
        // setup listeners
        adminButton.onClick.RemoveAllListeners();
        adminButton.onClick.AddListener(BtnAdminClick);
        btnSetting.onClick.AddListener(ShowSettings);
        btnShopBall.onClick.RemoveAllListeners();
        btnShopBall.onClick.AddListener(BtnShopBallClick);
        btnShopBallV2.onClick.RemoveAllListeners();
        btnShopBallV2.onClick.AddListener(BtnShopBallClick);

        btnAchievement.onClick.RemoveAllListeners();
        btnAchievement.onClick.AddListener(BtnAchievementClick);

        btn7DayMission.onClick.AddListener(OnBtn7DayMissionClick);

        btnGift.onClick.AddListener(BtnGiftOnClick);
        btnShopDiamond.onClick.AddListener(BtnShopDiamondOnClick);
        btnNoFSAds.onClick.AddListener(btnNoFSAdsOnClick);
        btnVipMission.onClick.AddListener(btnVipMissionOnClick);
    }

    public void CheckShowButtonGalaxyQuest() {
        if (RemoteConfigBase.instance.GalaxyQuest_IsEnable) {
            CreateButtonGalaxyQuest();
        }
    }

    public void CheckShowButtonIAPTripleOffers() {
        if (IAPTripleOffers.IsActive) {
            CreateButtonIAPTripleOffers();
        }
    }

    public void CheckShowButtonEndlessOffer() {
        if (EndlessOffer.CanActiveFearture) {
            CreateButtonEndlessOffer();
        }
    }

    protected void CheckShowButtonMysteryDoor() {
        if (!MysteryDoorManager.EnableFeature) {
            return;
        }

        if (!MysteryDoorManager.NeedShowButtonAtHome()) {
            return;
        }

        CreateButtonMysteryDoor();
    }

    protected virtual void CreateButtonMysteryDoor() {
        if (_mysteryDoorEntryPoint != null) {
            return;
        }

        var prefButton = ResourcesManager.Load<GameObject>(ResourcesPath.MysteryDoor_HomeButton);
        if (prefButton != null) {
            _mysteryDoorEntryPoint = Instantiate(prefButton);
            PutToLeftMenu(_mysteryDoorEntryPoint.transform, -1);
            _mysteryDoorEntryPoint.transform.localScale = Vector3.one;
            MysteryDoorManager.entryPoint = _mysteryDoorEntryPoint.GetComponent<UIButtonMysteryDoor>();
        } else {
            Logger.LogError($"[CreateButtonMysteryDoor] => {ResourcesPath.MysteryDoor_HomeButton} not found");
        }
    }

    private IEnumerator CreateTempButtonForGalaxyQuestEntryIfNeeded() {
        while (!HomeManager.instance.isInPopupFlow) {
            yield return null;
        }

        if (_galaxyQuestEntryPoint == null) {
            yield break;
        }

        Transform parent = _galaxyQuestEntryPoint.transform.parent;
        int indexOfEntryPoint = 0;
        foreach (Transform child in parent.GetChilds()) {
            if (child.gameObject.activeInHierarchy) {
                indexOfEntryPoint++;
                if (child == _galaxyQuestEntryPoint.transform) {
                    break;
                }
            }
        }

        List<GameObject> tempBtns = new List<GameObject>();
        for (int i = indexOfEntryPoint; i < 3; i++) {
            GameObject tempBtn = new GameObject();
            tempBtn.transform.SetParent(parent);
            tempBtn.transform.SetSiblingIndex(Mathf.Max(0, _galaxyQuestEntryPoint.transform.GetSiblingIndex() - 1));
            RectTransform rect = tempBtn.gameObject.AddComponent<RectTransform>();
            rect.SetSizeDelta(Vector2.one * 77);
            tempBtns.Add(tempBtn);
        }

        yield return YieldPool.GetWaitForEndOfFrame();

        LayoutElement layoutElement = _galaxyQuestEntryPoint.AddComponent<LayoutElement>();
        layoutElement.ignoreLayout = true;

        foreach (GameObject tempBtn in tempBtns) {
            if (tempBtn) {
                Destroy(tempBtn);
            }
        }
    }

    protected virtual bool IsActiveAchievement() {
        return AchievementBase.EnableAchievement;
    }
}