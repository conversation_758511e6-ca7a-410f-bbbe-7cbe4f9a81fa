using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.IO;
using System;
using DG.Tweening;
using Sirenix.OdinInspector;
using TilesHop.Cores.Hybrid;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.UserProgression;
using TilesHop.GameCore;
using TilesHop.LiveEvent;

public partial class SongItem : OptimizedCellView {
    public enum Action {
        Leaderboard = 0,
        Play        = 1,
        Preview     = 2,
    }

    #region Fields

    [HideInInspector] public string songPath = "";

    [SerializeField] public  int     styleIndex = 1; //TH-1039 => 1 + 2 + 3 + 4
    [SerializeField] private Text    nameText;
    [SerializeField] private Text    unlockText; //artist name
    [SerializeField] private Button  btnRank;
    [SerializeField] private Button  btnPreview;
    [SerializeField] private Image[] btnPreviewIcons;

    [SerializeField] private But<PERSON> btnFavorite;
    [SerializeField] private Button btnShowDefault;

    [SerializeField] private Image iconBestScore;
    [SerializeField] private Text  bestScoreText;
    [SerializeField] private Text  priceText;
    [SerializeField] private Text  iapText;

    [SerializeField] public    GameObject scoreObj;
    [SerializeField] private   GameObject rankObj;
    [SerializeField] protected GameObject star1;
    [SerializeField] protected GameObject star2;
    [SerializeField] protected GameObject star3;
    [SerializeField] private   Image[]    imgStarDarks;

    [SerializeField] private GameObject infinityIcon;
    [SerializeField] private GameObject infinityLabel;

    [SerializeField] private GameObject diskWrapper;
    [SerializeField] public  Image      bgDiskWrapper;
    [SerializeField] private GameObject diskDotIcon;
    [SerializeField] private GameObject diskUpIcon;
    [SerializeField] private GameObject diskPlayIcon;
    [SerializeField] private GameObject diskPauseIcon;
    [SerializeField] private GameObject diskLoadingIcon;
    [SerializeField] private Text       diskProgressText;

    public                   GameObject strokeEffect;
    public                   GameObject buyButton;
    [SerializeField] private GameObject iconDualUnlock;
    [SerializeField] private Image      imgCurrency;
    [SerializeField] private Sprite     sprtDiamond;
    [SerializeField] private GameObject videoButton;
    [SerializeField] private Text       videoButtonValue;
    [SerializeField] private GameObject videoButtonAnim;
    [SerializeField] private GameObject iapButton;
    public                   GameObject playButton;
    [SerializeField] private GameObject objPlayHighlight;
    [SerializeField] private GameObject objFree1Day;
    [SerializeField] private Text       txtRefresh1Day;

    [SerializeField] private   Image      favoriteImg;
    [SerializeField] private   GameObject challengeButton;
    [SerializeField] protected Text       labelText;
    [SerializeField] private   GameObject favoriteTip;
    [SerializeField] private   GameObject vip;

    [SerializeField] private Text vipText;

    [SerializeField] private Image imgBgRank;
    [SerializeField] private Image imgBgFavorite;

    [SerializeField] private Image imgBgPreview;

    [Space] [SerializeField] private Button btnUnlockToken;
    public                           Image  imgToken;
    [SerializeField] private         Text   txtTokenPrice;

    [SerializeField] private UICountDown btnUnlockTimer;

    [SerializeField] protected DifficultyTagUI difficultyTag;

    [Space]

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    [HideInInspector]
    public SONGTYPE type = SONGTYPE.LOCK;

    public SONG_PLAY_TYPE song_play_type { get; private set; }
    [HideInInspector] public int ordering;

    [HideInInspector] public int  price                    = 0;
    [ReadOnly]        public bool waitActionWhenUnlockSong = false;

    public  Animation                    diskAnim;
    private CoreUser.FriendLoadedHandler handler;

    public static            string currentPlayPath = null;
    [ReadOnly]        public Song   song;
    [HideInInspector] public string location;
    [HideInInspector] public string song_play_type_detail = null;

    private Image buyButtonImage;

    private RectTransform rect;

    Coroutine                  visibleCheckIE;
    public static List<String> visibledSong = new List<string>();

    private                    Camera cameraMain;
    [SerializeField] protected Image  imgBg;
    private                    Color  _darkTextColor;
    private                    Color  _pinkTextColor;
    private const              string VipStr      = "VIP";
    private const              string VipTrialStr = "TRIAL";

    private bool                  _cachedColor    = false;
    private bool                  _isItemTutorial = false;
    public  Action<Song, Vector3> OnClickItemPlay;
    public  Action<Song>          OnPreviewSongClick;
    public  Action<Song>          onClickPlay;

    private RenderMode _renderMode;

    const float NameTextPositionCenter = 2;
    const float NameTextPositionTop    = 13;

    private   string   _acmID;
    private   bool     forceUpdateSongCard;
    protected SongCard currentSongCard;

    [SerializeField] private GameObject previewTooltip;
    [SerializeField] private GameObject previewGroup;
    public                   GameObject buyNotice;
    private                  GameObject _grStar;
    private                  bool       _isActive;

    private UIButtonLockUP _btnLockUP;

    [Header("Square Songcard")] [SerializeField]
    private Image imgFade;

    [SerializeField] private Image iconACM;
    [SerializeField] private int   idCardStyle = -1;

    protected RemoteConfig remoteConfig => RemoteConfigBase.instance;
    public Transform Star => star1.transform;

    private readonly Color _crownOff = new Color(128f / 255, 130f / 255, 133f / 255);

    public static event System.Action OnDualUnlockFeature;

    [Space]
    [Header("Completed Status")]
    [SerializeField] private GameObject completedMark;

    #endregion

    private Coroutine _appearCoroutine;
    private Transform _cachedTransform;

    public Transform cachedTransform {
        get {
            if (_cachedTransform == null) {
                _cachedTransform = transform;
            }

            return _cachedTransform;
        }
    }

    #region Unity Method

    private void Awake() {
        if (CoreUser.instance != null) {
            Subscribe(CoreUser.instance);
        }

        // rect = GetComponent<RectTransform>();
        rect = (RectTransform) transform;
        cameraMain = Camera.main;
            Canvas canvas = GetComponentInParent<Canvas>();
            _renderMode = canvas != null ? canvas.renderMode : RenderMode.ScreenSpaceCamera;
        CacheColor();

        if (btnRank != null) {
            btnRank.onClick.AddListener(BtnRankClick);
        }

        if (btnFavorite != null) {
            btnFavorite.onClick.AddListener(BtnFavoriteClick);
        }

        if (btnPreview != null) {
            btnPreview.onClick.AddListener(BtnPreviewClick);
        }

        if (btnShowDefault != null) {
            btnShowDefault.onClick.AddListener(ShowDetails_Click);
        }

        _grStar = star1.transform.parent.gameObject; //don't put in Start because: Awake => SetSong => Start

        CheckToShowDecorations();
        if (completedMark != null) {
            completedMark.SetActive(false);
        }

        LocalizationManager.instance.UpdateFont(unlockText);
        LocalizationManager.instance.UpdateFont(nameText);
        LocalizationManager.instance.UpdateFont(priceText);
        LocalizationManager.instance.UpdateFont(labelText);
        LocalizationManager.instance.UpdateFont(bestScoreText);
        LocalizationManager.instance.UpdateFont(txtRefresh1Day);
        LocalizationManager.instance.UpdateFont(diskProgressText);
        LocalizationManager.instance.UpdateFont(vipText);
        LocalizationManager.instance.UpdateFont(iapText);
    }

    private void OnEnable() {
        Configuration.OnChangeDiamond += UpdateUIButtonDiamond;
        LiveEventManager.OnChangeToken += UpdateUIButtonToken;
        SongList.OnUnlockSong += SongListOnOnUnlockSong;
        _isActive = true;
        if (Configuration.instance != null) {
            UpdateUIButtonDiamond(Configuration.instance.GetDiamonds());
        }

        PreviewSongController.OnStartLoadPreviewSong += PreviewSongControllerOnOnStartLoadPreviewSong;
        PreviewSongController.OnDoneLoadPreviewSong += PreviewSongControllerOnOnDoneLoadPreviewSong;
        PreviewSongController.OnStopPreviewSong += PreviewSongControllerOnOnStopPreviewSong;

        if (song != null) {
            song.isSongOfDayFromDiscover = song_play_type == SONG_PLAY_TYPE.discover_song_of_the_day;
        }
    }

    public virtual void Reset() {
        StopAllCoroutines();
        CacheColor();

        strokeEffect.SetActive(false);
        buyButton.SetActive(false);
        videoButton.SetActive(false);
        HighlightPlayButton(false);

        challengeButton.SetActive(false);
        iapButton.SetActive(false);
        playButton.SetActive(false);
        objPlayHighlight.SetActive(false);
        objFree1Day.SetActive(false);
        scoreObj.SetActive(false);
        if (btnUnlockToken != null) {
            btnUnlockToken.gameObject.SetActive(false);
        }

        if (btnUnlockTimer != null) {
            btnUnlockTimer.gameObject.SetActive(false);
        }

        if (_btnLockUP != null) {
            _btnLockUP.gameObject.SetActive(false);
        }

        labelText.transform.parent.gameObject.SetActive(false);
        infinityIcon.SetActive(false);
        infinityLabel.SetActive(false);

        if (favoriteTip != null) {
            favoriteTip.SetActive(false);
        }

        nameText.gameObject.SetActive(true);
        diskWrapper.SetActive(true);
        if (diskDotIcon != null) {
            diskDotIcon.SetActive(false);
        }

        if (diskUpIcon != null) {
            diskUpIcon.SetActive(false);
        }

        diskProgressText.gameObject.SetActive(false);
        vip.SetActive(false);
        SetActivePreviewToolTip(false);

        if (difficultyTag) {
            difficultyTag.gameObject.SetActive(false);
        }

        ResetByStyle();
    }

    private void FixedUpdate() {
        if (_isActive && song.isSongOfDay) {
            _countTime += Time.fixedDeltaTime;
            if (_countTime >= 1) {
                _countTime--;
                UpdateLabelFreeTime();
            }
        }
    }

    protected virtual void OnDisable() {
        _isActive = false;
        LiveEventManager.OnChangeToken -= UpdateUIButtonToken;
        Configuration.OnChangeDiamond -= UpdateUIButtonDiamond;
        SongList.OnUnlockSong -= SongListOnOnUnlockSong;

        PreviewSongController.OnStartLoadPreviewSong -= PreviewSongControllerOnOnStartLoadPreviewSong;
        PreviewSongController.OnDoneLoadPreviewSong -= PreviewSongControllerOnOnDoneLoadPreviewSong;
        PreviewSongController.OnStopPreviewSong -= PreviewSongControllerOnOnStopPreviewSong;

        if (_appearCoroutine != null) {
            StopCoroutine(_appearCoroutine);
        }
    }

    private void OnDestroy() {
        if (CoreUser.isInstanced) {
            CoreUser.instance.onFriendLoaded -= handler;
        }

        visibledSong.Clear();

        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }

    #endregion

    #region Cached value

    private void CacheColor() {
        if (!_cachedColor) {
            _cachedColor = true;

            _darkTextColor = nameText.color; //!!! color of song name must is dark
            if (unlockText != null) {
                //_pinkTextColor = _darkTextColor;
                _pinkTextColor = unlockText.color;
                //_pinkTextColor.a = 0.6f;
            }
        }
    }

    private Dictionary<Transform, Vector3> dicTransformPostition = new Dictionary<Transform, Vector3>();
    private bool                           _cachedPosition;

    private void CachePosition() {
        if (_cachedPosition) {
            return;
        }

        _cachedPosition = true;

        Transform nameTextTransform = nameText.transform;
        Vector3 position = nameTextTransform.localPosition;
        dicTransformPostition.Add(nameTextTransform, position);

        Transform unlockTextTransform = unlockText.transform.parent;
        Vector3 vector3 = unlockTextTransform.localPosition;
        dicTransformPostition.Add(unlockTextTransform, vector3);
    }

    private void RestorePosition() {
        if (!_cachedPosition) {
            return;
        }

        foreach (KeyValuePair<Transform, Vector3> keyValuePair in dicTransformPostition) {
            Transform key = keyValuePair.Key;
            Vector3 value = keyValuePair.Value;
            key.SetLocalPosition(value);
        }
    }

    #endregion

    #region Preview Songs

    private void PreviewSongControllerOnOnStopPreviewSong(string path) {
        if (this.song == null)
            return;
        if (!this.song.path.Equals(path))
            return;

        ChangeDiskStatus(false);
    }

    private void PreviewSongControllerOnOnDoneLoadPreviewSong(string path) {
        if (this.song == null)
            return;

        //if (!this.song.path.Equals(path)) return;
        ChangeDiskStatus(false);
    }

    private void PreviewSongControllerOnOnStartLoadPreviewSong(string path) {
        if (this.song == null)
            return;
        if (!this.song.path.Equals(path))
            return;

        ChangeDiskStatus(true);
        _cPreviewSong = StartCoroutine(ShowLoadingProgress());
    }

    #endregion

    protected virtual void CheckToShowDecorations() {
        if (RemoteConfigBase.instance.HomeDecor_SongCard) {
            UIHomeDecorationController.TryDecorSongCardButton(playButton.transform.parent, Vector3.zero);
        }
    }

    private void SongListOnOnUnlockSong(string acm_id_v3) {
        if (song == null) {
            return;
        }

        if (string.IsNullOrEmpty(acm_id_v3)) {
            return;
        }

        if (string.IsNullOrEmpty(song.acm_id_v3)) {
            return;
        }

        if (!song.acm_id_v3.Equals(acm_id_v3)) {
            return;
        }

        SetSong(SONGTYPE.OPEN, song, song_play_type, this.location, song.ordering, _isItemTutorial, _currentGenre);
    }

    private void FriendLoaded() {
        try {
            if (type == SONGTYPE.OPEN && !SongManager.instance.IsLocalSong(songPath)) {
                long rank = 0;
                if (CoreUser.instance.loadedFriend && CoreUser.instance.user != null) {
                    if (Configuration.GetBestScore(songPath) > 0) {
                        rank = CoreUser.instance.user.GetMyRank(Util.SongToBoardId(songPath));
                    }
                }

                Transform findText = rankObj.transform.Find("Text");
                if (findText != null) {
                    findText.GetComponent<Text>().text = rank > 0 ? rank.ToString() : "--";
                }
            }
        } catch (Exception ex) {
            Debug.Log(ex.Message);
        }
    }

    private void Subscribe(CoreUser coreUser) {
        handler = new CoreUser.FriendLoadedHandler(FriendLoaded);
        coreUser.onFriendLoaded += handler;
    }

    public bool IsPreviewing() {
        return PreviewSongController.instanceSafe.IsPreviewingSong(this.song);
    }

    public void ChangeDiskStatus(bool isLoading) {
        if (IsPreviewing()) {
            if (diskAnim != null) {
                diskAnim.transform.parent.gameObject.SetActive(true);
                diskAnim.Play();
            }

            diskPauseIcon.SetActive(true);
            diskPlayIcon.SetActive(false);
        } else {
            if (diskAnim != null) {
                diskAnim.transform.parent.gameObject.SetActive(false);
            }

            diskPauseIcon.SetActive(false);
            diskPlayIcon.SetActive(true);
            diskProgressText.gameObject.SetActive(false);
            if (_cPreviewSong != null) {
                StopCoroutine(_cPreviewSong);
            }
        }

        diskLoadingIcon.SetActive(isLoading);
    }

    private bool _songInformationHasChanged = false;

    public void ItemClick() {
        SoundManager.PlayGameButton();
        if (Configuration.instance.isApprovalProcess && !_songInformationHasChanged) {
            ItemClick_ApprovalProcess();
            return;
        }

        if (Configuration.instance.isSelectMIDI && !_songInformationHasChanged && MidiMultipleVersion.isInstanced &&
            MidiMultipleVersion.instanceSafe.HasMultipleVersionOfMidi(song.acm_id_v3)) {
            ItemClick_MidiMultiple();
            return;
        }

        if (!song.isTutorialSong && Configuration.instance.isLongNotePrototype) {
            Util.ShowPopup_AdminLongNote(song);
            return;
        }

        if (!song.isTutorialSong && Configuration.instance.isSpecialTileControl) {
            Util.ShowPopup_AdminSpecialTile(song);
            return;
        }

        if (!song.isTutorialSong && Configuration.instance.isSpecialTileV2) {
            Util.ShowPopup_AdminSpecialTileV2(song);
            return;
        }

        AnalyticHelper.SongCard_Click(type.ToString().ToLower(), this.location);
        if (OnClickItemPlay != null) {
            CheckHideToolTipUnlockByGem();
            OnClickItemPlay.Invoke(song, playButton.transform.RootLocalPos());
        } else {
            ItemClick_Normal();
        }

        onClickPlay?.Invoke(song); //put end of method
    }

    private void ItemClick_Normal() {
        song.ordering = ordering;

        switch (type) {
            case SONGTYPE.USER_PROGRESSION:
                //ShowToolTipUnlockSong();
                break;

            case SONGTYPE.COUNTDOWN:
                //do nothing
                break;

            case SONGTYPE.VIDEO:
                SongLocationTracker.SetSongPlayType(song_play_type);
                AdsManager.instance.ShowRewardAds(VIDEOREWARD.song_unlock, song, this.location, true,
                    OnRewardVideoCompleted);
                break;

            case SONGTYPE.CHALLENGE:
                SongLocationTracker.SetSongPlayType(song_play_type);
                AdsManager.instance.ShowRewardAds(VIDEOREWARD.CHALLENGE, song, this.location, true,
                    OnRewardVideoCompleted);
                break;

            case SONGTYPE.OPEN:
#if UNITY_ANDROID
                if (SongManager.instance.IsLocalSong(songPath) && !File.Exists(song.GetLocalMp3Path()) &&
                    string.IsNullOrEmpty(song.key)) {
                    Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("MISSING_SONG_FILE") +
                                     "\n<color='#00E031'>" + SongManager.instance.userLocalSongs[songPath].path +
                                     "</color>");
                    break;
                }
#endif

                CheckAndFireChooseSong();

                if (TransitionInOut.isInstanced) {
                    TransitionInOut.instance.TransitionOut(playButton.transform.position, PlaySong);
                } else {
                    PlaySong();
                }

                break;

            case SONGTYPE.PAY:
                Configuration.instance.BuyProduct(IAPDefinitionId.song_st, string.Empty, (success, message) => {
                    if (success) {
                        SongList.ShopPaySingleCompleted(song);
                    }
                });
                break;

            case SONGTYPE.ALBUM:
                Configuration.instance.BuyProduct(IAPDefinitionId.package_st, string.Empty, (success, message) => {
                    if (success) {
                        SongList.ShopPayPackageCompleted();
                    }
                });
                break;

            case SONGTYPE.BUTTON:
                SongList.instance.UploadButton_Click();
                break;

            case SONGTYPE.VIP:
                if (SubscriptionController.IsSubscriptionVip()) {
                    if (TransitionInOut.isInstanced) {
                        TransitionInOut.instance.TransitionOut(playButton.transform.position, PlaySong);
                    } else {
                        PlaySong();
                    }
                } else {
                    SubscriptionController.ShowSubscription(SUBSCRIPTION_SOURCE.Song.ToString(), userOpen: true);
                }

                break;

            case SONGTYPE.EVENT:
                if (LiveEventManager.instance.UnlockSong(LiveEventManager.IdCurrentEvent, song.acm_id_v3)) {
                    SongList.OpenSong(song, 0, SongUnlockType.token);
                    TurnOffLabel();
                    UIOverlay.instance.ShowVFXSubCustom(imgToken.sprite, price, btnUnlockToken.transform.RootLocalPos(),
                        new Vector2(-45, 0), () => {
                            if (HomeManager.CachedTabActived != BottomMenuScript.Type.LiveEvent) {
                                PlaySong();
                            }
                        });
                    LiveEventManager.instance.CompleteUnlockSong();
                } else {
                    GameObject popup = Util.ShowPopUp(PopupName.LiveEventNotEnoughToken);
                    if (popup != null && popup.TryGetComponent(out UINotEnoughToken notEnoughToken)) {
                        notEnoughToken.Show(HomeManager.instance.GetLocation());
                    }
                }

                break;

            case SONGTYPE.LOCK:
            default:
                if (UIController.ui != null) {
                    UIController.ui.bgToolTip.HideRequired();
                }

                if (song.isDualUnlock) {
                    SongLocationTracker.SetSongPlayType(song_play_type);
                    AdsManager.instance.ShowRewardAds(VIDEOREWARD.song_unlock, song, this.location, true,
                        OnRewardVideoCompleted);
                } else {
                    song.TryToUnLockSongByGem((result) => {
                        if (result) {
                            PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_UnlockSongByGem, 1);
                            UIOverlay.instance.ShowVFXSubDiamond(price, playButton.transform.RootLocalPos(),
                                new Vector2(-55, 0), () => {
                                    if (!waitActionWhenUnlockSong) {
                                        if (TransitionInOut.isInstanced) {
                                            TransitionInOut.instance.TransitionOut(playButton.transform.position,
                                                PlaySong);
                                        } else {
                                            PlaySong();
                                        }
                                    }
                                });
                            if (MissionManager.isInstanced) {
                                MissionManager.instanceSafe.DoMission(MissionType.unlock_x_songs_with_diamond);
                            }
                        }
                    });
                }

                break;
        }
    }

    private void ItemClick_ApprovalProcess() {
        ACMApprovalProcess popupApprovalProcess =
            Util.ShowPopUp(CONFIG_STRING.ACM_ApprovalProcess).GetComponent<ACMApprovalProcess>();
        popupApprovalProcess.FetchData(song.acm_id_v3, ACMSDKv4.GetSlotIDMp3(), ACMSDKv4.GetSlotIDMidi(),
            (mp3ContentId, binContentId) => {
                _songInformationHasChanged = true;
                song.mp3ContentID = mp3ContentId;
                song.binContentID = binContentId;
                popupApprovalProcess.ClosePopUp();
                ItemClick();
                _songInformationHasChanged = false;
            }, ACMSDKv4.GetSlotIDPreview());
    }

    private void ItemClick_MidiMultiple() {
        List<MidiMultipleItem> data = MidiMultipleVersion.instanceSafe.GetMultipleMIDI(song.acm_id_v3);
        data.Insert(0, new MidiMultipleItem() {
            acmId = song.acm_id_v3,
            slotID = ACMSDKv4.GetSlotIDMidi(),
            VersionTag = "Default",
            MidiControl = false
        });
        UIMIDIMultipleVersion popupSelectMidi =
            Util.ShowPopUp(CONFIG_STRING.SelectMIDIVersion).GetComponent<UIMIDIMultipleVersion>();
        popupSelectMidi.FetchData(song.acm_id_v3, data, (slotId, forceMIDI) => {
            _songInformationHasChanged = true;
            song.slotID = slotId;
            popupSelectMidi.Close();
            ItemClick();
            _songInformationHasChanged = false;
        });
    }

    void CheckHideToolTipUnlockByGem() {
        if (UIController.ui != null)
            UIController.ui.bgToolTip.HideRequired();
        if (type == SONGTYPE.LOCK) {
            if (Configuration.instance.GetDiamonds() >= price) {
                PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_UnlockSongByGem, 1);
            }
        }
    }

    private void CheckAndFireChooseSong() {
        if (song.path == SongList.previewSongID && Time.time - SongList.startPreviewTime > 2) {
            var param = AnalyticHelper.GetDefaultParam(location);
            AnalyticHelper.LogSong(SONG_STATUS.song_preview_click, song, this.location, param, song_play_type_detail);
        } else {
            SongList.previewSongID = null;
        }
    }

    public void SetActivePreviewToolTip(bool isActive, Transform newParent = null) {
        if (previewTooltip == null)
            return;

        previewTooltip.SetActive(isActive);
        if (newParent != null) {
            previewTooltip.transform.SetParent(newParent);
        }
    }

    public void OnStartPreviewSong() {
        OnPreviewSongClick?.Invoke(song);

        SongLocationTracker.SetSongPlayType(song_play_type);
        StartPreviewSong();
    }

    public void StartPreviewSong(bool isForce = false, bool isTracking = true) {
        SetActivePreviewToolTip(false);
        PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_PreviewSong_Appear, 1);

        if (UIController.ui != null) {
            ResultOnboarding resultOnboarding = UIController.ui.gameover.GetResultOnboarding();
            if (resultOnboarding != null && resultOnboarding.IsActive()) {
                resultOnboarding.HideBlackBG();
            }
        }

        if (GroundMusic.instance != null) {
            if (isForce || diskPlayIcon.activeSelf) {
                PreviewSongController.instanceSafe.StartPreviewMusic(song, location, isTracking, song_play_type_detail);
                SoundManager.PlaySongCardPreview();
            } else {
                if (IsPreviewing()) {
                    PreviewSongController.instanceSafe.StopPreviewMusic(song, false);
                }
            }
        }
    }

    private IEnumerator ShowLoadingProgress() {
        diskProgressText.gameObject.SetActive(true);
        float percent = 0f;
        while (GroundMusic.IsDownloadContent) {
            percent = GroundMusic.PercentDownloadContent;
            diskProgressText.text = $"{Mathf.Round(100 * percent)}%";
            yield return null;
        }

        while (GroundMusic.instance.www != null && !GroundMusic.instance.www.isDone) {
            if (GroundMusic.instance.www.progress > percent) {
                percent = GroundMusic.instance.www.progress;
                diskProgressText.text = $"{Mathf.Round(100 * percent)}%";
            }

            yield return null;
        }

        diskProgressText.gameObject.SetActive(false);
        diskLoadingIcon.SetActive(false);
    }

    private void ResetByStyle() {
        switch (styleIndex) {
            case 1:
            case 4:
            case 6: {
                nameText.transform.SetLocalY(NameTextPositionCenter);
                unlockText.gameObject.SetActive(false);

                rankObj.SetActive(false);
                rankObj.transform.SetLocalY(0);
                scoreObj.transform.SetLocalY(0);

                if (favoriteImg != null) {
                    Transform grFavorite = favoriteImg.transform.parent.parent;
                    grFavorite.gameObject.SetActive(false);
                    grFavorite.SetLocalY(0);
                }

                unlockText.transform.parent.SetLocalY(0);
                break;
            }

            case 2:
            case 3: {
                CachePosition();
                RestorePosition();
                unlockText.gameObject.SetActive(true);

                if (btnFavorite != null) {
                    Transform grFavorite = btnFavorite.transform.parent;
                    grFavorite.gameObject.SetActive(false);
                }

                if (btnRank != null) {
                    rankObj.SetActive(false);
                }

                break;
            }
        }
    }

    public void SetSong(Song item, SONG_PLAY_TYPE songPlayType, string location, int ordering, Action<Song> onClick) {
        if (item == null) { // for test
            //Reset();
            return;
        }

        SetSong(item.savedType, item, songPlayType, location, ordering);
        onClickPlay = onClick;
        OnPreviewSongClick = onClick;
    }

    private string _currentGenre;

    public virtual void SetSong(SONGTYPE songType, Song item, SONG_PLAY_TYPE songPlayType, string location,
                                int ordering, bool isItemTutorial = false, string genre = "") {
        if (item == null) {
            CustomException.Fire("[SetSong]", "song is null");
            return;
        }

        Reset();
        bool isSameSong = song != null && song.acm_id_v3 == item.acm_id_v3;
        if (isSameSong && string.IsNullOrEmpty(item.acm_id_v3)) {
            isSameSong = false;
        }

        type = songType;
        songPath = item.path;
        _acmID = item.acm_id_v3;
        nameText.text = item.name;
        song = item;
        song_play_type = songPlayType;
        song.isSongOfDayFromDiscover = songPlayType == SONG_PLAY_TYPE.discover_song_of_the_day;

        ChangeDiskStatus(false);
        this.location = location;
        this.ordering = ordering;
        song.ordering = ordering;
        item.desc = song.artist;
        _isItemTutorial = isItemTutorial;
        _currentGenre = genre;

        SetCheckVisible();
        if (remoteConfig.ACMv4_EnableCheckLicense) {
            CheckLicenseSong();
        }

        if (!isSameSong) { // chỉ update songcard khi cần thiết
            SetSongCard();
            //CheckGrayVFX();
        }

        bool isOpened = song.IsOpened();
        bool isFavoriteSong = UserEntry.GetFavoriteList().Contains(_acmID);

        switch (type) {
            case SONGTYPE.USER_PROGRESSION:
                if (!_btnLockUP) {
                    var asset = Resources.Load<UIButtonLockUP>(ResourcesPath.UserProgression_ButtonLockSongItem);
                    if (asset != null) {
                        _btnLockUP = Instantiate(asset, playButton.transform.parent);
                        _btnLockUP.onClick.AddListener(() => {
                            ShowToolTipUnlockSong(_btnLockUP.transform, _btnLockUP.message);
                        });
                    }
                }

                int id = song.songPackId ?? -1;
                if (id < 0) {
                    id = UserProgressionController.instanceSafe.IdLastSongPack;
                }

                string themeName = UserProgressionController.instanceSafe.packThemeData
                    .GetInfo(UserProgressionController.instanceSafe.GetIdTheme(id)).themeName.ToUpper();
                _btnLockUP.SetPackId(id);
                _btnLockUP.locationName = themeName;
                _btnLockUP.gameObject.SetActive(true);
                break;

            case SONGTYPE.BUTTON:
                strokeEffect.SetActive(true);
                diskWrapper.SetActive(false);
                if (diskUpIcon != null) {
                    diskUpIcon.SetActive(true);
                }

                int uploadType = SongList.GetUploadType();

                if (uploadType == 1) {
                    playButton.SetActive(true);
                    if (!SubscriptionController.IsSubscriptionVip()) {
                        item.desc = LocalizationManager.instance.GetLocalizedValue("FREE_FIRST_TIME");
                    }
                } else if (uploadType == 2) {
                    videoButton.SetActive(true);
                    item.desc = null;
                } else {
                    price = remoteConfig.Price_Upload_Song;
                    priceText.text = price.ToString();
                    UpdateUIButtonDiamond(Configuration.instance.GetDiamonds());
                    buyButton.SetActive(true);
                }

                if (IsStyle_2_3()) {
                    CenterSongText();

                    Transform grFavorite = btnFavorite.transform.parent;
                    grFavorite.gameObject.SetActive(false);

                    Transform grRank = btnRank.transform.parent;
                    grRank.gameObject.SetActive(false);
                }

                break;

            case SONGTYPE.ALBUM:
                strokeEffect.SetActive(true);
                iapButton.SetActive(true);
                string priceString = IapBase.GetPriceString(IAPDefinitionId.package_st);
                iapText.text = priceString ?? "4$";

                diskWrapper.SetActive(false);
                diskDotIcon.SetActive(true);
                break;

            case SONGTYPE.VIDEO:
                videoButton.SetActive(true);
                if (Util.IsGameResultScene()) {
                    HighlightPlayButton(true);
                }

                UpdateVideoUnlockStatus();
                break;

            case SONGTYPE.CHALLENGE:
                challengeButton.SetActive(true);
                break;

            case SONGTYPE.LOCK:
                price = item.diamonds;
                priceText.text = price.ToString();
                imgCurrency.sprite = sprtDiamond;
                UpdateUIButtonDiamond(Configuration.instance.GetDiamonds());
                buyButton.SetActive(true);
                break;

            case SONGTYPE.OPEN:
            case SONGTYPE.VIP:
                if (isOpened && !song.isTutorialSong && song.IsPlayed()) {
                    scoreObj.SetActive(true);
                    UpdateUIStars();

                    if (bestScoreText != null) {
                        int bestScore = Configuration.GetBestScore(songPath);
                        bestScoreText.text = bestScore > 0 ? bestScore.ToString() : "--";
                    }
                }

                if (isOpened && (SongList.selectedTag == SONG_TAG.FAVORITES ||
                                 SongList.selectedTag == SONG_TAG.UNFAVORITES)) {
                    if (favoriteImg != null) {
                        favoriteImg.transform.parent.parent.gameObject.SetActive(true);
                        if (isFavoriteSong) {
                            favoriteImg.color = Configuration.instance.favoriteColor;
                        } else {
                            favoriteImg.color = Configuration.instance.likeInactiveColor;
                        }
                    }
                }

                if (type == SONGTYPE.VIP) {
                    vip.SetActive(true);
                    item.desc = song.label;
                    vipText.text = VipStr;
                }

                playButton.SetActive(!song.isSongOfDay);
                objPlayHighlight.SetActive(song.isSongOfDay);
                objFree1Day.SetActive(song.isSongOfDay);

                if ((song.isSongOfDay && Util.IsGameResultScene()) || _isItemTutorial) {
                    HighlightPlayButton(true);
                }

                break;

            case SONGTYPE.PAY:
                iapButton.gameObject.SetActive(true);
                string p2 = IapBase.GetPriceString(IAPDefinitionId.song_st);
                iapText.text = p2 ?? "1$";

                break;

            case SONGTYPE.FAVORITE_TIP:
                strokeEffect.SetActive(true);
                nameText.gameObject.SetActive(false);
                favoriteTip.SetActive(true);
                diskWrapper.SetActive(false);
                if (diskDotIcon != null) {
                    diskDotIcon.SetActive(true);
                }

                break;

            case SONGTYPE.EVENT:
                var eventItem = LiveEventManager.instance.GetLiveEvent(LiveEventManager.IdCurrentEvent);
                price = eventItem.detailData.GetSongPrice();
                txtTokenPrice.text = price.ToString();
                imgToken.sprite = eventItem.eventConfig.IconToken;
                btnUnlockToken.gameObject.SetActive(true);
                UpdateUIButtonToken(LiveEventManager.IdCurrentEvent, eventItem.progress.TokenAmount);
                break;

            case SONGTYPE.COUNTDOWN:
                //do nothing
                if (song.CanUnlockByTimer()) {
                    song.UnlockByTimer();
                } else {
                    if (btnUnlockTimer) {
                        TimeSpan remainTime = song.GetRemainTimeToUnlock();
                        btnUnlockTimer.SetData(remainTime, false, OnCountDownEnd);
                        btnUnlockTimer.gameObject.SetActive(true);
                    }
                }

                break;
        }

        if (IsStyle_2_3()) {
            if (isOpened && songType != SONGTYPE.BUTTON && favoriteImg != null) {
                favoriteImg.gameObject.SetActive(isFavoriteSong);
            }

            if (isOpened && btnFavorite != null) {
                Transform grFavorite = btnFavorite.transform.parent;
                grFavorite.gameObject.SetActive(true);
            }

            if (isOpened && btnRank != null) {
                rankObj.SetActive(true);
            }

            if (!isOpened) {
                CenterSongText();
            }
        }

        if (!string.IsNullOrEmpty(item.desc)) {
            unlockText.text = item.desc;

            if (IsStyle_2_3()) {
                unlockText.gameObject.SetActive(true);
            } else { // styleIndex 1 4
                if (!scoreObj.activeSelf) {
                    unlockText.gameObject.SetActive(true);
                }
            }
        }

        if (styleIndex == 1 || styleIndex == 4 || styleIndex == 6) {
            if ((!string.IsNullOrEmpty(item.desc) || favoriteImg.transform.parent.parent.gameObject.activeSelf ||
                 scoreObj.activeSelf) && !song.isSongOfDay) {
                nameText.transform.SetLocalY(NameTextPositionTop);
            }
        }

        if (_isItemTutorial) {
            ProcessItemTutorial();
        }

        if (type != SONGTYPE.EVENT && !song.IsBelongToLiveEvent) { // tắt lable nếu type là event
            UpdateLabel();
        } else {
            TurnOffLabel();
        }

        if (song.isSongOfDay) {
            ProcessFree1Day();
        }

        if ((remoteConfig.DifficultyTag_IsEnable || song.IsSpecialSongCard) && difficultyTag != null) {
            if (type != SONGTYPE.BUTTON && !song.IsLocalSong() && !song.isTutorialSong) {
                difficultyTag.ShowTag(song);
            }
        }

        if (CoreUser.instance != null) {
            Subscribe(CoreUser.instance);
            FriendLoaded();
        }

        if (completedMark != null && remoteConfig.SongItem_ShowCompletedStatus) {
            completedMark.SetActive(stars >= 3);
        }
    }

    private void OnCountDownEnd() {
        song.UnlockByTimer();
    }

    public void SetTypePlaySongDetail(string locationDetail) {
        this.song_play_type_detail = locationDetail;
    }

    protected int stars = 0;

    protected virtual void UpdateUIStars() {
        UpdateScoreObj();
        if (styleIndex == 6) {
            UpdateUIStarsByStyle();
        } else {
            stars = song.bestStar;
        }

        //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
        if (_grStar == null) {
            _grStar = star1.transform.parent.gameObject;
        }

        if (remoteConfig.ChallengeMode_Enable && ChallengeMode.CanProcessSong(song)) {
            int crowns = song.bestCrown;
            if (crowns != 0 || (stars >= 3 && (remoteConfig.ChallengeMode_ForceEmptyCrown || song.existCrownMode))) {
                //show crown
                UpdateSpriteCrown();
                ShowCrown(crowns);
            } else {
                UpdateSpriteStar();
                ShowAllStar(stars);
            }
        } else if (stars < 3) { // stars = 0 | 1 | 2
            ShowAllStar(stars);
        } else { // stars >= 3
            if (IsStyle_2_3() && !_isItemTutorial) {
                ShowAllStar();
            } else if (styleIndex == 6) {
                ShowAllStar();
            } else { //styleIndex = 1 + 4
                bool isEndless = Configuration.EnableEndlessModeByUser();
                if (UserProgressionController.EnableFeature) {
                    ShowAllStar();
                    //show infinity but another position
                    infinityIcon.gameObject.SetActive(isEndless);
                    infinityIcon.transform.SetParent(playButton.transform);
                    infinityIcon.transform.localPosition = new Vector3(0, -23);
                    if (infinityIcon.TryGetComponent(out Graphic graphic)) {
                        graphic.color = Color.white;
                    }
                } else {
                    if (isEndless) {
                        _grStar.SetActive(false);
                        infinityIcon.gameObject.SetActive(true);
                    } else {
                        ShowAllStar();
                    }
                }
            }
        }
    }

    public void ShowStarsEffect() {
        Image[] starImages = new Image[3];
        star1.TryGetComponent(out starImages[0]);
        star2.TryGetComponent(out starImages[1]);
        star3.TryGetComponent(out starImages[2]);

        Vector3 initScale = Vector3.one * 4;
        float delay;

        for (int i = 0; i < 3; i++) {
            var star = starImages[i];
            star.SetAlpha(0f);
            star.transform.localScale = initScale;
            delay = 0.5f * i;

            star.transform.DOScale(1f, 0.5f).SetDelay(delay);
            star.DOFade(1f, 0.3f).SetDelay(delay).SetEase(Ease.OutQuint);
        }
    }

    protected virtual void UpdateUIStarsByStyle() { }

    protected void ShowAllStar(int stars = 3) {
        _grStar.SetActive(true); //Show all star
        star1.SetActive(stars > 0);
        star2.SetActive(stars > 1);
        star3.SetActive(stars > 2);

        infinityIcon.gameObject.SetActive(false); //when hide infinityIcon
        infinityLabel.SetActive(false);
    }

    protected void ShowCrown(int crown) {
        star1.SetActive(crown > 0);
        star2.SetActive(crown > 1);
        star3.SetActive(crown > 2);

        _grStar.SetActive(true); //Show all star
        infinityIcon.gameObject.SetActive(false); //when hide infinityIcon
    }

    private void ProcessItemTutorial() {
        if (styleIndex == 2) {
            CenterSongText();
        }

        if (IsStyle_2_3()) {
            Transform grFavorite = btnFavorite.transform.parent;
            grFavorite.gameObject.SetActive(false);

            Transform grRank = btnRank.transform.parent;
            grRank.gameObject.SetActive(false);
            previewGroup.transform.SetLocalY(-13);
        }
    }

    private void CenterSongText() {
        nameText.transform.SetLocalY(15f);
        unlockText.transform.parent.SetLocalY(-12f);
    }

    private void UpdateLabel() {
        if (!objFree1Day.activeSelf) {
            Transform parentLabelText = labelText.transform.parent;
            if (!_isItemTutorial && !infinityLabel.activeSelf && type != SONGTYPE.VIP) {
                if (!string.IsNullOrEmpty(song.label)) {
                    parentLabelText.gameObject.SetActive(true);
                    labelText.text = LocalizationManager.instance.GetLocalizedUpperCase(song.label);
                } else if (song.IsContainTag(SONG_TAG.NEW)) {
                    if (!song.IsPlayed()) {
                        parentLabelText.gameObject.SetActive(true);
                        labelText.text = LocalizationManager.instance.GetLocalizedValue("NEW");
                    }
                }
            }
        }
    }

    public void TurnOffLabel() {
        labelText.transform.parent.gameObject.SetActive(false);
    }

    public void TurnOffButtonsAction() {
        playButton.transform.parent.gameObject.SetActive(false);
    }

    protected virtual void SetSongCard() {
        if (song == null || !SongCards.instance)
            return;

        song.onUpdateSongCard = OnUpdateSongCard;
        song.onUpdateSquareSongCard = OnUpdateSquareSongCard;
        forceUpdateSongCard = true;
        //~~~~~~~~~~~~~~ Get button card ~~~~~~~~~~~~~~
        if (song.savedType == SONGTYPE.BUTTON || song.savedType == SONGTYPE.FAVORITE_TIP ||
            (SongManager.instance && SongManager.instance.IsLocalSong(song.path))) {
            string cardId = (styleIndex == 3) ? SongCards.DefaultId : SongCards.ButtonId;
            currentSongCard = SongCards.instance.GetSongCardByButton(cardId);
        } else {
            currentSongCard = SongCards.instance.GetSongCardBySong(song, _currentGenre);
        }

        if (forceUpdateSongCard && currentSongCard != null) {
            OnUpdateSongCard(song.path, currentSongCard.cardImg, currentSongCard.isWhite);
        }
    }

    private readonly Color _colorBtnLight = new Color(1, 1, 1, 0.34f); // 0.34 = 87/255
    private readonly Color _colorBtnDark  = new Color(0, 0, 0, 0.34f); // 0.34 = 87/255

    protected void SetTextColor(bool isCardWhite) {
        nameText.color = !isCardWhite ? ColorHelper.white : _darkTextColor;

        if (unlockText != null) {
            unlockText.color = !isCardWhite ? ColorHelper.whiteAlpha : _pinkTextColor;
        }

        if (bestScoreText != null) {
            bestScoreText.color = !isCardWhite ? ColorHelper.white : _darkTextColor;
        }

        if (imgBgRank != null) {
            imgBgRank.color = isCardWhite ? _colorBtnDark : _colorBtnLight;
        }

        if (imgBgFavorite != null) {
            imgBgFavorite.color = isCardWhite ? _colorBtnDark : _colorBtnLight;
        }

        if (imgBgPreview != null) {
            imgBgPreview.color = isCardWhite ? _colorBtnDark : _colorBtnLight;
        }

        if (IsStyle_2_3() && imgStarDarks != null) {
            foreach (Image image in imgStarDarks) {
                if (image != null) {
                    image.color = isCardWhite ? _colorBtnDark : _colorBtnLight;
                }
            }

            if (btnPreviewIcons != null) {
                foreach (var item in btnPreviewIcons) {
                    item.color = nameText.color;
                }
            }
        }
    }

    public void OnUpdateSongCard(string path, Sprite sprite, bool isWhiteCard) {
        if (this == null || gameObject == null) {
            return;
        }

        if (!forceUpdateSongCard) {
            if (this.song == null) {
                return;
            }

            if (!this.song.path.Equals(path)) {
                return;
            }
        }

        //Logger.EditorLog($"[SongCard] update {song.path}");
        if (imgFade != null) {
            imgFade.gameObject.SetActive(false);
        }

        if (iconACM != null) {
            iconACM.gameObject.SetActive(false);
        }

        if (imgBg != null) {
            forceUpdateSongCard = false;
            imgBg.sprite = sprite;
        }

        SetTextColor(isWhiteCard);
    }

    private void OnUpdateSquareSongCard(string path, Sprite sprite) {
        if (!forceUpdateSongCard) {
            if (this.song == null) {
                //Logger.EditorLogError($"[SquareSongCard-Wrong] update song card but song is null");
                return;
            }

            if (!this.song.path.Equals(path)) {
                //Logger.EditorLogError($"[SquareSongCard-Wrong] update song card but wrong id {path} | {song?.path}");
                return;
            }
        }

        //Logger.EditorLog($"[SquareSongCard] update {song.path}");

        if (imgFade != null && iconACM != null) {
            if (sprite == null) {
                imgFade.gameObject.SetActive(false);
                iconACM.gameObject.SetActive(false);
            } else {
                // khi cần dùng fade/background
                idCardStyle = ColorSongCardHelper.GetIdTheme(sprite.texture);
                bool isWhite = ColorSongCardHelper.IsWhite(idCardStyle);
                var songCardStyle = SongCards.instance.GetThemeBySong(idCardStyle);

                imgFade.sprite = songCardStyle.cardFade;
                imgBg.sprite = songCardStyle.cardBG;
                iconACM.sprite = sprite;

                imgFade.gameObject.SetActive(true);
                iconACM.gameObject.SetActive(true);

                SetTextColor(isWhite);
                forceUpdateSongCard = false;
            }
        }
    }

    protected virtual void ShowDetails_Click() {
        SoundManager.PlayGameButton();
        string defaultAction = remoteConfig.SongItem_DefaultAction;
        bool isItemClick = false;
        switch (type) {
            case SONGTYPE.USER_PROGRESSION:
                //ShowToolTipUnlockSong();
                break;

            case SONGTYPE.BUTTON:
                AnalyticHelper.Button_Click(BUTTON_NAME.PlayMySong);
                SongList.instance.UploadButton_Click();
                break;

            case SONGTYPE.ALBUM:
                Configuration.instance.BuyProduct(IAPDefinitionId.package_st, string.Empty, (success, message) => {
                    if (success) {
                        SongList.ShopPayPackageCompleted();
                    }
                });
                break;

            case SONGTYPE.FAVORITE_TIP:
                // if (remoteConfig.enableSearchBar) {
                //     SongList.instance.ClearSearchBar();
                // }

                AnalyticHelper.Button_Click(BUTTON_NAME.Favorite_Tip);
                SongList.isShowingFavorite = !SongList.isShowingFavorite;
                //SongList.instance.BuildList(SONG_TAG.FAVORITES);

                break;

            case SONGTYPE.OPEN:
                if (song.IsLocalSong()) {
                    AnalyticHelper.Button_Click(BUTTON_NAME.Song_Details);
                    GameObject popUp = Util.ShowPopUp(PopupName.SongDetails);
                    popUp.GetComponent<SongDetails>().SetSong(SongManager.instance.userLocalSongs[songPath]);
                } else if (_isItemTutorial) {
                    OnStartPreviewSong();
                } else {
                    if (string.IsNullOrEmpty(defaultAction)) { //old
                        if (Util.ShowLeaderBoard(song)) {
                            AnalyticHelper.Button_Click(BUTTON_NAME.LeaderBoard_Song);
                        } else {
                            OnStartPreviewSong();
                        }
                    } else {
                        isItemClick = ProcessDefaultAction(defaultAction);
                    }
                }

                break;

            default:
                if (string.IsNullOrEmpty(defaultAction)) { //old
                    OnStartPreviewSong();
                } else {
                    ProcessDefaultAction(defaultAction);
                }

                break;
        }

        if (!isItemClick) {
            AnalyticHelper.SongCard_Click(TRACK_NAME.default_action, this.location);
        }
    }

    private static UITooltipUnlockSongByPack _tooltipUnlockSongPack;

    private void ShowToolTipUnlockSong(Transform source, string text) {
        if (_tooltipUnlockSongPack == null) {
            var tooltipUnlock = Util.ShowUI(UIName.TooltipUnlockSong);
            if (tooltipUnlock != null) {
                tooltipUnlock.TryGetComponent(out _tooltipUnlockSongPack);
            }
        }

        _tooltipUnlockSongPack.SetActive(true);
        _tooltipUnlockSongPack.Show(text, source != null ? source.position : btnShowDefault.transform.position);
    }

    private bool ProcessDefaultAction(string defaultAction) {
        bool tryParse = Enum.TryParse(defaultAction, true, out Action action);
        if (tryParse) {
            switch (action) {
                case Action.Leaderboard:
                    if (Util.ShowLeaderBoard(song)) {
                        AnalyticHelper.Button_Click(BUTTON_NAME.LeaderBoard_Song);
                    } else {
                        OnStartPreviewSong();
                    }

                    break;

                case Action.Play:
                    ItemClick();
                    return true;

                case Action.Preview:
                    OnStartPreviewSong();
                    break;

                default:
                    Logger.LogWarning("[ProcessDefaultAction] need process " + defaultAction);
                    break;
            }
        } else {
            OnStartPreviewSong();
        }

        return false;
    }

    #region style3

    private void BtnRankClick() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.LeaderBoard_Song);
        AnalyticHelper.SongCard_Click(TRACK_NAME.rank, this.location);
        if (song.IsLocalSong()) {
            Util.ShowLeaderBoard();
        } else {
            Util.ShowLeaderBoard(song);
        }
    }

    private void BtnFavoriteClick() {
        SoundManager.PlayGameButton();
        bool noFavorite = !UserEntry.GetFavoriteList().Contains(_acmID);

        //Update UI
        switch (styleIndex) {
            case 1: //ver old
            case 4:
            case 6:
                favoriteImg.color = noFavorite
                    ? Configuration.instance.favoriteColor
                    : Configuration.instance.likeInactiveColor;
                break;

            case 2:
            case 3:
                favoriteImg.gameObject.SetActive(noFavorite);
                break;

            default:
                Logger.LogWarning($"[BtnFavoriteClick] Need process UI for styleIndex {styleIndex}");
                break;
        }

        //Update DB
        if (noFavorite) {
            UserEntry.SaveFavorite(song);
            AnalyticHelper.SongCard_Click(TRACK_NAME.favorite_add, this.location);
            AnalyticHelper.Button_Click(BUTTON_NAME.Favorite_Add);
        } else {
            UserEntry.RemoveFavorite(song);
            AnalyticHelper.SongCard_Click(TRACK_NAME.favorite_remove, this.location);
            AnalyticHelper.Button_Click(BUTTON_NAME.Favorite_Remove);
        }
    }

    private void BtnPreviewClick() {
        AnalyticHelper.SongCard_Click(TRACK_NAME.preview, this.location);
        SoundManager.PlayGameButton();
        OnStartPreviewSong();
    }

    #endregion

    private void SetCheckVisible() {
        if (visibleCheckIE != null) {
            StopCoroutine(visibleCheckIE);
        }

        if (song != null && !string.IsNullOrWhiteSpace(song.path) && SongManager.instance.IsOfficial(song.path) &&
            gameObject.activeInHierarchy) {
            visibleCheckIE = StartCoroutine(CheckVisible());
        }
    }

    private IEnumerator CheckVisible() {
        yield return YieldPool.GetWaitForSeconds(remoteConfig.Song_Impression_Time);

        if (!visibledSong.Contains(song.path)) {
            if (_renderMode == RenderMode.ScreenSpaceCamera && rect.IsFullyVisibleFrom(cameraMain) ||
                _renderMode != RenderMode.ScreenSpaceCamera && rect.IsFullyVisibleOnScreen()) {
                var param = AnalyticHelper.GetDefaultParam(location);
                param[TRACK_NAME.song_play_type] = song_play_type;
                AnalyticHelper.LogSong(SONG_STATUS.song_impression, song, this.location, param, song_play_type_detail);
                visibledSong.Add(song.path);
            } else {
                SetCheckVisible();
            }
        }
    }

    private void OnRewardVideoCompleted(bool isCompleted) {
        if (!isCompleted) {
            return;
        }

        bool isSuccessUnlock = SongList.TryToUnlockSongByAds(song);
        if (isSuccessUnlock) {
            MissionCenter.DoMission(MissionType.unlock_x_songs_with_ads);

            if (waitActionWhenUnlockSong) {
                SongList.OpenSong(song, 0, SongUnlockType.ads);
            } else {
                SongList.OpenSong(song, 0, SongUnlockType.ads);

                // play transition-VFX
                if (TransitionInOut.isInstanced) {
                    TransitionInOut.instance.TransitionOut(playButton.transform.position, PlaySong);
                } else {
                    PlaySong();
                }
            }
        } else {
            UpdateVideoUnlockStatus();
        }

        AirfluxTracker.TrackRewardAdsImpression();
    }

    #region Free 1 day

    private double _timeRemaining;
    private double totalSecondInDay  = 86400; // 24 * 60 * 60;
    private double totalSecondInHour = 3600; //  60 * 60;
    private float  _countTime;

    private void ProcessFree1Day() {
        double timeRemainingInDay = totalSecondInDay - DateTime.Now.TimeOfDay.TotalSeconds;
        _timeRemaining = timeRemainingInDay;
        UpdateLabelFreeTime();

        //update UI
        if (styleIndex == 1 || styleIndex == 4 || styleIndex == 6) {
            nameText.transform.SetLocalY(17.5f);
            unlockText.transform.parent.SetLocalY(11.12f);
            scoreObj.transform.SetLocalY(11.12f);
        } else if (styleIndex == 2) {
            nameText.transform.SetLocalY(12);
            unlockText.gameObject.SetActive(false);
        }
    }

    private void UpdateLabelFreeTime() {
        _timeRemaining -= 1;
        double timeRemaining = _timeRemaining;

        int hour = (int) (timeRemaining / totalSecondInHour);
        timeRemaining -= hour * totalSecondInHour;

        int minute = (int) (timeRemaining / 60);
        timeRemaining -= minute * 60;

        string label = LocalizationManager.instance.GetLocalizedValue("REFRESH_IN");
        txtRefresh1Day.text = $"{label} {hour.ToString("00")}:{minute.ToString("00")}:{timeRemaining.ToString("00")}";
    }

    #endregion

    #region Calc songitem loaded

    private Coroutine _cCheckLicenseSong;
    private Coroutine _cPreviewSong;

    private void CheckLicenseSong() {
        if (song != null && SongManager.instance.IsOfficial(song.path) && gameObject.activeInHierarchy &&
            song.isMainSong && !song.isCheckedLicense && !string.IsNullOrEmpty(song.acm_id_v3)) {
            SongManager.instance.AddSongCheckLicense(song);
        }
    }

    #endregion

    private void PlaySong() {
        SongLocationTracker.SetSongPlayType(song_play_type);
        song.isSongOfDayFromDiscover = song_play_type == SONG_PLAY_TYPE.discover_song_of_the_day;
        Util.GoToGamePlay(song, location: this.location, isSongClick: true, song_play_type_detail);
    }

    public void ShowButtonPlay(bool isShow) {
        playButton.transform.parent.gameObject.SetActive(isShow);
    }

    private static SongItem _songItemPrefabUp;
    private static SongItem _songItemPrefab;

    public static SongItem GetSongItemPrefab() {
        if (UserProgressionController.EnableFeature && Util.IsHomeScene() &&
            HomeManager.instance.MainHome.gameObject.activeInHierarchy) {
            if (_songItemPrefabUp == null) {
                _songItemPrefabUp = Resources
                    .Load<GameObject>(Util.BuildString(string.Empty, "SongItem/", "[UP] SongItem"))
                    .GetComponent<SongItem>();
            }

            return _songItemPrefabUp;
        }

        int songCardStyle = RemoteConfigBase.instance.SongItem_StyleIndex;
        if (songCardStyle >= 1) {
            if (_songItemPrefab == null) {
                _songItemPrefab = Resources.Load<GameObject>($"SongItem/SongItem_{songCardStyle.ToString()}")
                    .GetComponent<SongItem>();
            }

            return _songItemPrefab;
        } else {
            Logger.LogError($"[GetSongItemPrefab] Cannot get songItem of StyleIndex = {songCardStyle.ToString()}");
        }

        return null;
    }

    public static SongItem GetSongItemTutorialPrefab() {
        return GetSongItemPrefab();
    }

    public void UpdateUIForReward(Song rewardSong, SONG_PLAY_TYPE songPlayType, string location, int ordering,
                                  bool canGetReward, bool isShowPlayButton = true) {
        gameObject.SetActive(true);
        SetSong(rewardSong.savedType, rewardSong, songPlayType, location, ordering);
        strokeEffect.SetActive(true);
        foreach (Transform child in buyButton.transform.parent) {
            child.gameObject.SetActive(false);
        }

        if (canGetReward && styleIndex == 6) {
            ShowButtonPlay(false);
        } else {
            ShowButtonPlay(true);
            if (canGetReward && isShowPlayButton) {
                playButton.SetActive(true);

                // sau khi ẩn hết children của buyButton.transform.parent, object decor cũng bị ẩn theo
                // cần spawn lại object decor (khi button play active)
                CheckToShowDecorations();
            } else {
                playButton.SetActive(false);
            }

            playButton.transform.parent.Find("Lock").gameObject.SetActive(!canGetReward);
        }
    }

    public GameObject GetPreviewGroup() {
        return previewGroup;
    }

    private void UpdateUIButtonDiamond(int amount) {
        if (song.savedType != SONGTYPE.LOCK)
            return; // chỉ update UI diamond with Lock Item
        if (!buyButton)
            return;

        if (!buyButtonImage) {
            if (!buyButton.TryGetComponent(out buyButtonImage)) {
                return;
            }
        }

        bool canBuy = amount >= price;
        buyButtonImage.color =
            canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;

        if (canBuy && remoteConfig.Economy_IsEnable && Util.IsGameResultScene()) {
            HighlightPlayButton(true);
        }

        if (iconDualUnlock) {
            if (!canBuy) {
                bool isDual = CheckDualUnlock();
                if (isDual) {
                    buyButtonImage.color = GameConstant._buttonDiamondEnableColor;
                    if (!song.isDualUnlock) {
                        song.isDualUnlock = true;
                        song.ads = 1;
                        OnDualUnlockFeature?.Invoke();
                        UserProgressionTracking.Track_DualUnlockImpression(song, amount, price);
                    }
                } else {
                    song.isDualUnlock = false;
                }
            } else {
                song.isDualUnlock = false;
            }

            iconDualUnlock.SetActive(song.isDualUnlock);
        }
    }

    private bool CheckDualUnlock() {
        if (!Util.EnableCheckDualUnlock) {
            return false;
        }

        if (!remoteConfig.UserProgression_DualUnlockSong) {
            return false;
        }

        if (Configuration.IsNoAllAds())
            return false;

        if (Configuration.CheckToShowOldUserGemPopup()) {
            return false; //not received tutorial reward yet
        }

        return true;
    }

    private void UpdateUIButtonToken(int idEvent, int amount) {
        if (song.savedType != SONGTYPE.EVENT)
            return; // chỉ update UI event Item
        if (btnUnlockToken == null)
            return;

        bool canBuy = amount >= price;
        btnUnlockToken.image.color =
            canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;
        HighlightPlayButton(canBuy);
    }

    public void ShowBuyNotice() {
        UIController.ui.bgToolTip.gameObject.SetActive(true);
        buyNotice.SetActive(true);
        buyButton.transform.SetParent(UIController.ui.bgToolTip.transform);
    }

    public void HideBuyNotice() {
        UIController.ui.bgToolTip.HideRequired();
        buyNotice.SetActive(false);
        buyButton.transform.SetParent(playButton.transform.parent);
    }

    private bool IsStyle_2_3() {
        return styleIndex is 2 or 3;
    }

    public override void SetData(IData data) { }

    public void ForceUpdateButtonAfterUnlock() {
        if (song.savedType == SONGTYPE.OPEN) {
            SetSong(song.savedType, song, song_play_type, location, ordering);
        }
    }

    public void HighlightPlayButton(bool isOn) {
        if (videoButtonAnim != null) {
            videoButtonAnim.SetActive(isOn);
        }
    }

    public void HighlightBestScore() {
        if (bestScoreText == null) {
            return;
        }

        int myNumber = 0;
        int bestScore = Configuration.GetBestScore(songPath);
        float duration = Mathf.Clamp(bestScore / 100f, 0.3f, 3f);
        DOTween.To(() => myNumber, x => myNumber = x, bestScore, duration).OnUpdate(() => {
            bestScoreText.text = myNumber.ToString();
        });
    }

    public virtual IEnumerator HighlightStars() {
        yield return null;
    }

    public void HighlightCurrentSongIfNeed(Song lastSong) {
        if (lastSong == null || song == null || lastSong.GetKeyOfSong() != song.GetKeyOfSong()) {
            return;
        }

        if (HomeManager.CachedTabActived == BottomMenuScript.Type.Search &&
            Configuration.instance.isHighlightCurrentSong) {
            HighlightBestScore();
            StartCoroutine(HighlightStars());

            DOVirtual.DelayedCall(0.5f, () => { Configuration.instance.isHighlightCurrentSong = false; });
        }
    }

    public void UpdateVideoUnlockStatus() {
        if (song.ads < 2) {
            //need 1 ads
            if (videoButtonValue) {
                if (styleIndex == 2 || styleIndex == 3 || styleIndex == 4) {
                    videoButtonValue.text = LocalizationManager.instance.GetLocalizedValue("PLAY");
                    videoButtonValue.gameObject.SetActive(true);
                } else {
                    videoButtonValue.gameObject.SetActive(false);
                }
            }
        } else {
            //need more ads
            if (videoButtonValue) {
                videoButtonValue.text = $"{song.viewedAds}/{song.ads}";
                videoButtonValue.gameObject.SetActive(true);
            }
        }
    }

    public bool GetBestStarTransform(int bestStar, out Transform point) {
        switch (bestStar) {
            case 1:
                point = star1.transform;
                return true;

            case 2:
                point = star2.transform;
                return true;

            case 3:
                point = star3.transform;
                return true;

            default:
                point = null;
                return false;
        }
    }

    public Vector3 GetButtonPreviewPosition() {
        return btnPreview.transform.position;
    }

    protected virtual void CheckGrayVFX() {
        if (!UserProgressionController.EnableFeature)
            return;

        if (song_play_type.Equals(SONG_PLAY_TYPE.home)) {
            if (song.savedType == SONGTYPE.USER_PROGRESSION) {
                imgBg.material = Configuration.instance.grayMaterial;
            } else {
                imgBg.material = null;
            }
        }
    }

    public bool CheckBehideBottomMenu() {
        if (!Util.IsHomeScene()) {
            return false;
        }

        return Util.IsRectOverlapping(rect, HomeManager.instance.bottomMenuScript.transform as RectTransform);
    }

    public void PlayAppearAnimation(int direction, float duration, Ease ease) {
        if (_appearCoroutine != null) {
            StopCoroutine(_appearCoroutine);
        }

        _appearCoroutine = HomeManager.instance.StartCoroutine(IEPlayAppearAnimation(direction, duration, ease));
    }

    /// <summary>
    /// </summary>
    /// <param name="direction">-1: from left, 1: from right</param>
    /// <param name="duration"></param>
    private IEnumerator IEPlayAppearAnimation(int direction, float duration, Ease ease) {
        yield return YieldPool.GetWaitForEndOfFrame();

        cachedTransform.SetLocalX(direction);
        cachedTransform.DOLocalMoveX(0f, duration).SetEase(ease);
    }

    public void ForceStopAppearAnimation() {
        if (_appearCoroutine != null) {
            StopCoroutine(_appearCoroutine);
        }

        cachedTransform.DOKill();
        cachedTransform.SetLocalX(0f);
    }
}