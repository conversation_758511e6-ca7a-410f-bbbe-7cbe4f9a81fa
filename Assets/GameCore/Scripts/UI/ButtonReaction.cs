using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.EventSystems;

public class ButtonReaction : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler {
    private   Vector3 normalScale  = Vector3.one;
    protected Vector3 pressedScale = new Vector3(1.1f, 1.1f, 1.1f);
    private   float   timeAnimated = .1f;

    private Sequence  _sequence;
    private Transform _transform;

    protected void OnEnable() {
        _transform = this.transform;
        _transform.localScale = normalScale;
    }

    public void OnPointerDown(PointerEventData eventData) {
        _sequence?.Kill();
        _transform.localScale = normalScale;
        _sequence = DOTween.Sequence().SetUpdate(true) // ignore timescale
            .Append(_transform.DOScale(pressedScale, timeAnimated))
            .Append(_transform.DOScale(normalScale, timeAnimated));
    }

    private void OnDisable() {
        _sequence?.Kill();
    }

    private void OnDestroy() {
        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }

    public void ChangeScale(float multiplier) {
        _sequence?.Kill();
        normalScale *= multiplier;
        pressedScale *= multiplier;
        transform.localScale = normalScale;
    }
}