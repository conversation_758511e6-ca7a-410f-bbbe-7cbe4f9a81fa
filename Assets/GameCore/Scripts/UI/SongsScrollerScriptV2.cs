using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using Com.TheFallenGames.OSA.Core;
using Com.TheFallenGames.OSA.Core.CustomScroller.SongScroller;
using DG.Tweening;
using Music.ACM;
using Music.ACM.Interfaces;
using TilesHop.Cores.Pooling;
using TilesHop.GameCore;
using TilesHop.Cores.UserProgression;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Random = UnityEngine.Random;

public class SongsScrollerScriptV2 : MonoBehaviour {
    #region Fields

    [Header("Components")]
    [SerializeField] private SongScrollerAdapter songScroller;

    [SerializeField] private Image         viewportGradientBackground;
    [SerializeField] private RectTransform container;

    public Transform rtfUserExpProgressBar;

    private UserExpSongPackProgress _userExpSongPackProgress;
    public UserExpSongPackProgress userExpProgressBar => _userExpSongPackProgress;

    //~~~~~~~~~~~~ private ~~~~~~~~~~~~
    private int      _lastGradientThemeId;
    private Material _gradientBackgroundMaterial;
    private bool     _isFirstTimeChangeBackgroundGradient = true;

    private OptimizedCellView _prefSongItem;
    private List<IData>       _data     = new();
    private List<IData>       _moreList = new();

    //private ScrollRect scrollMain => SongList.instance.scrollMainHome;
    private RectTransform  _rtfSongsScroll;
    private float          _scrollMainVerticalNormalizedPosition;
    private string         _location     = LOCATION_NAME.home.ToString();
    private SONG_PLAY_TYPE _songPlayType = SONG_PLAY_TYPE.home;

    private string _tagSong;
    private string _keySearch;

    private bool _isInitedScrollview;

    private          bool           _isLoadingMore;
    private          int            _totalDataACM;
    private readonly List<SongData> _songDatasACM = new List<SongData>();
    private          bool           _isLoadEnd;

    private        string        _pageToken     = string.Empty;
    private        string        _lastToken     = String.Empty;
    private static StringBuilder _otherTagQuery = new StringBuilder();
    private        Coroutine     _cProcessOnSuccess;

    private bool  _isCachedLocalPosX;
    private bool  _isCachedLocalPosY;
    private float _cacheLocalX;
    private float _cacheLocalY;
    private int   countLocalSong;
    private bool  _needRefresh;

    private bool  _isDragByUser;
    private int   _indexAutoPreview;
    private Tween _tweenChangePreview;
    private bool  _finishStart = false;

    private static SongPackThemeInfo _lastThemeInfo;

    public SongScrollerAdapter ScrollerAdapter => songScroller;
    [Space] public CanvasGroup CanvasGroup;
    public event Action OnSetDataDone;

    #endregion

    #region Unity Methods

    private void Awake() {
        SongManager.OnSongListChanged += OnSongChanged;
        songScroller.OnItemVisible += SongScroller_OnItemVisible;
        UIMenuHome.OnChangeSize += CheckPaddingValue;

        _rtfSongsScroll = (RectTransform) transform;
        _prefSongItem = SongItem.GetSongItemPrefab();
        songScroller.Init(_prefSongItem.transform as RectTransform);
        CheckPaddingValue();
        if (UserProgressionController.EnableFeature) {
            // đẩy song list lệch sang phải 20đv
            container.pivot = new Vector2(0.5f, 1f);
            container.anchoredPosition = new Vector2(20, 0);
            songScroller.Parameters.ContentSpacing = UserProgressionController.SONGLIST_CONTENT_SPACING;
            songScroller.Parameters.effects.TransientSpeedBetweenDrags = false;
        }
    }

    private IEnumerator Start() {
        songScroller.ScrollPositionChanged += OnScrollPositionChanged;
        yield return Inwave.Utils.WaitUntil(() => _isInitedScrollview);

        if (RemoteConfigBase.instance.FixedScrollviewPositionInHome_IsEnable &&
            LastStateData.LastLocation == LastStateData.Locations.MainHome) {
            Song currentSong = SceneFader.instance.LastPlaySong;
            int index = GetIndexOfSong(currentSong);
            if (index >= 0) {
                songScroller.ScrollTo(index, 0.5f, 0.5f);
            } else if (UserProgressionController.EnableFeature) {
                // scroll to lastest unlocked pack
                int idLastestUnlockedPack = Mathf.Min(
                    UserProgressionController.instanceSafe.SongPacksHolder.levelSongPackRewarded,
                    UserProgressionController.instanceSafe.levelManager.maxLevelIndex);

                ScrollToHeader(idLastestUnlockedPack, 0.1f);
            }
        }

        if (_lastThemeInfo != null) {
            _changeBg = StartCoroutine(IESetThemeColor());
        }

        _finishStart = true;
    }

    private void OnEnable() {
        StopScrollingScrollView();
        songScroller.OnDragEvent += SongScroller_OnOnDragEvent;
        songScroller.OnEndDragEvent += SongScroller_OnEndDragEvent;
        songScroller.OnUpdateHeader += SongScroller_OnChangeTopHeader;
        PreviewSongController.OnStartLoadPreviewSong += PreviewSongControllerOnOnStartLoadPreviewSong;
        if (_needRefresh) {
            RefreshAllSongItem();
        }

        CheckPreviewStatus();
    }

    private void OnDisable() {
        _tweenChangePreview?.Kill();
        if (songScroller != null) {
            songScroller.OnDragEvent -= SongScroller_OnOnDragEvent;
            songScroller.OnEndDragEvent -= SongScroller_OnEndDragEvent;
            songScroller.OnUpdateHeader -= SongScroller_OnChangeTopHeader;
            PreviewSongController.OnStartLoadPreviewSong -= PreviewSongControllerOnOnStartLoadPreviewSong;
            if (songScroller.IsInitialized && HomeManager.CachedTabActived == BottomMenuScript.Type.Home) {
                LastStateData.SetLocation(LastStateData.Locations.MainHome, songScroller.GetNormalizedPosition(),
                    string.Empty);
            }
        }
    }

    private void OnDestroy() {
        if (songScroller) {
            songScroller.OnItemVisible -= SongScroller_OnItemVisible;
            songScroller.ScrollPositionChanged -= OnScrollPositionChanged;
        }

        UIMenuHome.OnChangeSize -= CheckPaddingValue;
        SongManager.OnSongListChanged -= OnSongChanged;
    }

    private void OnApplicationPause(bool pauseStatus) {
        if (pauseStatus) {
            StopScrollingScrollView();
        }
    }

    private void LateUpdate() {
        if (_isDragByUser) {
            if (songScroller.Velocity.y is < 0.2f and > -0.2f) {
                StopAutoChangePreview();
                CheckAutoFixScrollview();
                CheckAutoChangePreviewMusic();
            }
        }
    }

    #endregion

    private void CheckPaddingValue() {
        int paddingValue = HomeManager.instance.GetMainMenuHeight();
        int adjust = HomeManager.instance.GetAdjustFeatureStickyOffset();
        if (UserProgressionController.EnableFeature) {
            songScroller.Parameters.ContentPadding.top = 0;
            songScroller.Parameters.Viewport.offsetMax = new Vector2(0, -paddingValue);
            songScroller.Parameters.Viewport.offsetMin = new Vector2(0, -BottomMenuScript.heightBottomMenu);
        } else {
            if (HomeManager.instance.menuHome.isLocked) {
                songScroller.Parameters.ContentPadding.top = paddingValue;
                songScroller.Viewport.offsetMax = new Vector2(0, -HomeManager.instance.menuHome.GetMenuOffsetMax());
            } else {
                songScroller.Parameters.ContentPadding.top = paddingValue - adjust;
                songScroller.Viewport.offsetMax = new Vector2(0, -adjust);
            }

            songScroller.Viewport.offsetMin = Vector2.zero;
            container.localPosition = Vector3.zero;
        }

        if (songScroller.IsInitialized) {
            songScroller.ForceRebuildLayoutNow();
        }
    }

    private IEnumerator SetData() {
        yield return Inwave.Utils.WaitUntil(() => songScroller.IsInitialized);

        songScroller.SetItems(_data);
        _isInitedScrollview = true;
        if (UserProgressionController.EnableFeature) {
            ShowExpProgressBar();
            songScroller.RefreshVirtualScrollSize();
        }

        if (UserProgressionController.EnableFeature ||
            RemoteConfigBase.instance.FixedScrollviewPositionInHome_IsEnable &&
            LastStateData.LastLocation == LastStateData.Locations.MainHome && Configuration.instance.isOpenedHome) {

            songScroller.SetNormalizedPosition(LastStateData.LastScrollPosition);

            // reset last scroll position
            LastStateData.LastScrollPosition = 1f;

            if (HomeManager.CachedTabActived == BottomMenuScript.Type.Home &&
                Configuration.instance.isHighlightCurrentSong) {
                Configuration.instance.isHighlightCurrentSong = false;
                Song currentSong = Configuration.instance.GetCurrentSong();
                if (currentSong != null) {
                    OptimizedCellView songObjectByPath = GetSongObjectByPath(currentSong.path);
                    if (songObjectByPath != null && songObjectByPath is SongItem songItem) {
                        songItem.HighlightBestScore();
                        songItem.StartCoroutine(songItem.HighlightStars());
                    }
                }
            }

            OnSetDataDone?.Invoke();
        }
    }

    public void ResetToTop() {
        Configuration.instance.StartCoroutine(IEResetToTop());
    }

    private IEnumerator IEResetToTop() {
        while (!ScrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
        
        ScrollerAdapter?.SetNormalizedPosition(1f);
    }

private int GetIndexOfSong(Song song) {
        if (song == null) {
            return -1;
        }

        if (_data == null || _data.Count == 0) {
            return -1;
        }

        int total = _data.Count;
        for (int i = 0; i < total; i++) {
            if (_data[i] == null) {
                continue;
            }

            if (_data[i] is Song song2) {
                if (string.IsNullOrEmpty(song2.acm_id_v3)) {
                    continue;
                }

                if (song2.acm_id_v3.Equals(song.acm_id_v3)) {
                    return i;
                }
            } else {
                continue;
            }
        }

        return -1;
    }

    public void ShowExpProgressBar() {
        if (_userExpSongPackProgress) {
            _userExpSongPackProgress.SetupProgressBar();
            return;
        }

        var progressBar = Resources.Load<RectTransform>("SongPackTilesProgresses");
        rtfUserExpProgressBar = Instantiate(progressBar, container);
        _userExpSongPackProgress = rtfUserExpProgressBar.GetComponent<UserExpSongPackProgress>();
    }


    private const int   SCROLL_SPEED_LIMITATION = 1000;
    private const float APPEAR_DURATION         = 0.5f;
    private const float FAST_APPEAR_DURATION    = 0.25f;
    private const int   DIRECTION               = 75;
    private       int   _directionSign          = 1;
    private       bool  _isReachMaxScrollSpeed  = false;
    
    private void SongScroller_OnItemVisible(SongItemViewsHolder item) {
        if (_data == null || _data.Count <= item.ItemIndex) {
            return;
        }

        if (item.cellView is SongItem songItem) {
            if (RemoteConfigBase.instance.UITransition_IsSongCardsAppearTransition) {
                var dragSpeed = ScrollerAdapter.Velocity.y;
                if (Mathf.Abs(dragSpeed) < SCROLL_SPEED_LIMITATION) {
                    _isReachMaxScrollSpeed = false;
                    switch (dragSpeed) {
                        case > 0:
                            songItem.PlayAppearAnimation(-DIRECTION, FAST_APPEAR_DURATION, Ease.OutCubic);
                            break;

                        case < 0:
                            songItem.PlayAppearAnimation(DIRECTION, FAST_APPEAR_DURATION, Ease.OutCubic);
                            break;

                        default:
                            songItem.PlayAppearAnimation(DIRECTION * _directionSign, APPEAR_DURATION, Ease.OutCubic);
                            _directionSign *= -1;
                            break;
                    }
                } else if (!_isReachMaxScrollSpeed) {
                    _isReachMaxScrollSpeed = true;
                    for (int i = 0; i < ScrollerAdapter.VisibleItemsCount; i++) {
                        var itemView = ScrollerAdapter.GetItemViewsHolder(i);
                        if (itemView != null && itemView.cellView is SongItem songItemView) {
                            songItemView.ForceStopAppearAnimation();
                        }
                    }
                }
            }

            Song song = (Song) _data[item.ItemIndex];
            if (song == null) {
                Logger.EditorLog("ScrollView", "Wrong data???");
            } else {
                int ordering = item.ItemIndex + 1 - countLocalSong;
                songItem.SetSong(song, _songPlayType, location: _location, ordering, null);
                

				if (_finishStart) {
					SoundManager.PlayScrollTick(1);
				}

				if (UserProgressionController.EnableFeature) {
                    songItem.transform.localScale = Vector3.one * 0.93f;
                }
            }
        }
    }

    private void PreviewSongControllerOnOnStartLoadPreviewSong(string obj) {
        _tweenChangePreview?.Kill();
    }

    private void SongScroller_OnOnDragEvent(PointerEventData obj) {
        StopAutoChangePreview();
        songScroller.StopScrollingIfAny();
    }

    private Color     _dark, _light, _brill;
    private Coroutine _changeBg;

    private void SongScroller_OnChangeTopHeader(SongPackHeaderBaseItem header) {
        if (_changeBg != null) {
            StopCoroutine(_changeBg);
        }

        _changeBg = StartCoroutine(IEChangeBG(header));
    }

    private double _lastScrollNormalizedPosition;

    private void SongScroller_OnScroll(double normalizedPosition) {
        if (!rtfUserExpProgressBar)
            return;

        if (Math.Abs(_lastScrollNormalizedPosition - normalizedPosition) < 1e-5) {
            return;
        }

        _lastScrollNormalizedPosition = normalizedPosition;

        var h = songScroller.GetContentSize();
        var viewSize = songScroller.GetViewHeight();
        rtfUserExpProgressBar.localPosition = new Vector3(0, (float) ((1 - normalizedPosition) * (h - viewSize)), 0);
    }

    private IEnumerator IEChangeBG(SongPackHeaderBaseItem header) {
        if (!header) {
            yield break;
        }

        if (header is SongPackBigLockedHeaderItem) {
            _lastThemeInfo = UserProgressionController.instanceSafe.packThemeData.GetInfo(0);
        } else {
            _lastThemeInfo = header.themeInfo;
        }

        yield return IESetThemeColor();
    }

    private IEnumerator IESetThemeColor() {
        if (_isFirstTimeChangeBackgroundGradient) {
            viewportGradientBackground.gameObject.SetActive(true);
            _gradientBackgroundMaterial = new Material(viewportGradientBackground.material);
            viewportGradientBackground.material = _gradientBackgroundMaterial;

            _dark = _lastThemeInfo.gradientDark;
            _light = _lastThemeInfo.gradientLight;
            _brill = _lastThemeInfo.gradientBrilliant;
            _gradientBackgroundMaterial.SetColor(SongPackThemeData.colorDarkId, _dark);
            _gradientBackgroundMaterial.SetColor(SongPackThemeData.colorLightId, _light);
            
            songScroller.RefreshSongItemsDiskUI(_light, _brill, _isFirstTimeChangeBackgroundGradient);

            _isFirstTimeChangeBackgroundGradient = false;
            yield return Inwave.Utils.WaitUntil(() => _userExpSongPackProgress);

            _userExpSongPackProgress.SetProgressColor(_light);
        } else {
            var t = 0f;
            while (t < 1) {
                t += Time.deltaTime / 5f;
                _dark = Color.Lerp(_dark, _lastThemeInfo.gradientDark, t);
                _light = Color.Lerp(_light, _lastThemeInfo.gradientLight, t);
                _brill = Color.Lerp(_brill, _lastThemeInfo.gradientBrilliant, t);

                _gradientBackgroundMaterial.SetColor(SongPackThemeData.colorDarkId, _dark);
                _gradientBackgroundMaterial.SetColor(SongPackThemeData.colorLightId, _light);
                _userExpSongPackProgress.SetProgressColor(_light);
                songScroller.RefreshSongItemsDiskUI(_light, _brill);
                yield return YieldPool.GetWaitForEndOfFrame();
            }
        }
    }

    private void SongScroller_OnEndDragEvent(PointerEventData obj) {
        _isDragByUser = true;
    }

    private void CheckAutoFixScrollview() {
        if (!RemoteConfig.instance.PreviewMusic_Improve_IsEnable) {
            return;
        }

        if (!RemoteConfig.instance.PreviewMusic_AutoFixScrollviewPosition) {
            return;
        }

        int visibleItemsCount = songScroller.VisibleItemsCount;
        if (visibleItemsCount != 0) {
            BaseItemViewsHolder fistItem = songScroller.GetItemViewsHolder(0);
            if (fistItem.ItemIndex == 0 || fistItem is not SongCardViewHolder) {
                //at top -> do nothing
            } else {
                Logger.EditorLog("AutoFix", $"Move to songitem index {fistItem.ItemIndex}");
                songScroller.SmoothScrollTo(fistItem.ItemIndex, 0.2f, 0f, 0);
            }
        }
    }

    public void StopAutoChangePreview() {
        _tweenChangePreview?.Kill();
        _isDragByUser = false;
    }

    public bool GetRandomVisibleSongToPreview(out Song result) {
        int visibleItemsCount = songScroller.VisibleItemsCount;
        if (visibleItemsCount != 0) {
            var currentSongPreview = PreviewSongController.instanceSafe.CurrentSongPreview;
            if (currentSongPreview != null) {
                for (int i = 0; i < visibleItemsCount; i++) {
                    if (_data[songScroller.GetItemViewsHolder(i).ItemIndex] is Song song) {
                        if (song == currentSongPreview) {
                            result = currentSongPreview;
                            return true;
                        }
                    }
                }
            }

            int indexAutoPreview = Random.Range(0, 9999) % (visibleItemsCount - 2);

            bool isInValid;
            do {
                isInValid = false;
                _indexAutoPreview = songScroller.GetItemViewsHolder(indexAutoPreview).ItemIndex;
                if (_data[_indexAutoPreview] is Song song) {
                    if (song.path.Equals("button")) {
                        isInValid = true;
                        indexAutoPreview++;
                    }
                } else {
                    isInValid = true;
                    indexAutoPreview++;
                }

                if (indexAutoPreview >= visibleItemsCount) {
                    result = null;
                    return false;
                }
            } while (isInValid);

            result = _data[_indexAutoPreview] as Song;
            return true;
        }

        result = null;
        return false;
    }

    public void CheckAutoChangePreviewMusic() {
        if (!RemoteConfigBase.instance.PreviewMusic_Improve_IsEnable) {
            return;
        }

        if (songScroller == null || !songScroller.IsInitialized) {
            return;
        }

        int visibleItemsCount = songScroller.VisibleItemsCount;
        if (visibleItemsCount == 0) {
            return;
        }

        var currentSongPreview = PreviewSongController.instanceSafe.CurrentSongPreview;
        bool existInScreenView = false;
        if (currentSongPreview != null) {
            for (int i = 0; i < visibleItemsCount; i++) {
                if (_data[songScroller.GetItemViewsHolder(i).ItemIndex] is Song song) {
                    if (song == currentSongPreview) {
                        existInScreenView = true;
                        break;
                    }
                }
            }
        }

        if (existInScreenView) {
            return;
        }

        int indexAutoPreview = RemoteConfig.instance.PreviewMusic_Index;
        if (indexAutoPreview < 0 || indexAutoPreview > (visibleItemsCount - 2)) {
            if (visibleItemsCount <= 2) {
                indexAutoPreview = 0;
            } else {
                indexAutoPreview = Random.Range(0, 9999) % (visibleItemsCount - 2);
            }
        }

        bool isInValid = false;
        do {
            isInValid = false;
            _indexAutoPreview = songScroller.GetItemViewsHolder(indexAutoPreview).ItemIndex;
            if (_data[_indexAutoPreview] is Song song) {
                if (song.path.Equals("button")) {
                    isInValid = true;
                    indexAutoPreview++;
                }
            } else {
                isInValid = true;
                indexAutoPreview++;
            }

            if (indexAutoPreview >= visibleItemsCount)
                break;
        } while (isInValid);

        if (isInValid)
            return;

        _tweenChangePreview = DOVirtual.DelayedCall(RemoteConfig.instance.PreviewMusic_TimeDelay,
            () => {
                PreviewSongController.instanceSafe.StartPreviewMusic(_data[_indexAutoPreview] as Song,
                    LOCATION_NAME.home.ToString(), false);
            });

    }

    private void RefreshAllSongItem() {
        if (!songScroller.IsInitialized) {
            return;
        }

        int visibleCount = songScroller.VisibleItemsCount;
        for (int i = 0; i < visibleCount; i++) {
            var item = songScroller.GetItemViewsHolder(i);
            var view = songScroller.GetItemViewsHolder(i).cellView as SongItem;
            if (view != null) {
                Song song = (Song) _data[item.ItemIndex];
                if (song != null) {
                    int ordering = item.ItemIndex + 1 - countLocalSong;
                    view.SetSong(song, _songPlayType, location: _location, ordering, null);
                } else {
                    Logger.EditorLog("ScrollView", "Wrong data???");
                }
            }
        }

        _needRefresh = false;
    }

    public SongItem GetFirstViewSong() {
        if (!songScroller.IsInitialized) {
            return null;
        }

        int visibleCount = songScroller.VisibleItemsCount;
        for (int i = 0; i < visibleCount; i++) {
            var item = songScroller.GetItemViewsHolder(i);
            var view = songScroller.GetItemViewsHolder(i).cellView as SongItem;
            if (view != null && !string.IsNullOrEmpty(view.song.acm_id_v3)) {
                return view;
            }
        }

        return null;
    }

    public void LoadSongs(List<SongItemRaw> songItemsRaw, int localSong, string tagName, string keySearch,
                          bool loadMoreSongs = true) {
        _tagSong = tagName;
        _data.Clear();

        foreach (SongItemRaw item in songItemsRaw) {
            Type type = item.GetType();
            if (type == typeof(SongPackHeaderItemRaw)) {
                _data.Add(((SongPackHeaderItemRaw) item).songPackHeaderData);
            } else if (type == typeof(SongPackLockedHeaderItemRaw)) {
                _data.Add(((SongPackLockedHeaderItemRaw) item).songPackLockedHeaderData);
            } else if (type == typeof(SongPackLockedBigHeaderItemRaw)) {
                _data.Add(((SongPackLockedBigHeaderItemRaw) item).songPackLockedHeaderData);
            } else if (type == typeof(SongPackEndingHeaderItemRaw)) {
                _data.Add(((SongPackEndingHeaderItemRaw) item).headerData);
            } else if (type == typeof(SongItemRaw)) {
                _data.Add(item.song);
            } else {
                Logger.LogError("[LoadSongs] Wrong type of song item => " + type);
            }
        }

        _keySearch = keySearch;

        if (_data.Count == 0) {
            ShowObjNoItem(true);
        }

        countLocalSong = localSong;

        // scrollerSongs.ReloadData();
        Configuration.instance.StartCoroutine(SetData());

        ResetDownloadACM();

        if (!loadMoreSongs) {
            return;
        }

        if (RemoteConfigBase.instance.ACMv4_IsLoadMore && !Configuration.instance.enableContentTool &&
            _data.Count < ACMSDKv4.LOAD_WHEN_DATA_LEFT && IsCanLoadMoreSongs() &&
            !UserProgressionController.EnableFeature) {
            if (_cProcessOnSuccess != null) {
                StopCoroutine(_cProcessOnSuccess);
            }

            LoadMoreSongs();
        }
    }

    public bool IsSetDataDone() {
        return !_data.IsNullOrEmpty() && _isInitedScrollview;
    }

    public OptimizedCellView GetSongObjectByPath(string songPath) {
        int totalItem = _data.Count;
        for (int i = 0; i < totalItem; i++) {
            if (_data[i] is Song song) {
                if (song.path != songPath) {
                    continue;
                }
            } else {
                continue;
            }

            var itemSong = songScroller.GetItemViewsHolderIfVisible(i);
            return itemSong?.cellView;
        }

        return null;
    }

    public Song GetSongByPath(string songPath) {
        int totalItem = _data.Count;
        for (int i = 0; i < totalItem; i++) {
            if (_data[i] is Song song) {
                if (song.path != songPath) {
                    continue;
                }
            } else {
                continue;
            }

            return song;
        }

        return null;
    }

    public OptimizedCellView GetSongPackHeader(int idHeader) {
        int totalItem = _data.Count;
        for (int i = 0; i < totalItem; i++) {
            if (_data[i] is SongPackHeaderData pack) {
                if (pack.packLevel != idHeader) {
                    continue;
                }
            } else {
                continue;
            }

            var viewsHolder = songScroller.GetItemViewsHolderIfVisible(i);
            return viewsHolder?.cellView;
        }

        return null;
    }

    public void UpdateItem(string songPath, SongItemRaw newItem) {
        OptimizedCellView optimizedCellView = GetSongObjectByPath(songPath);
        if (optimizedCellView != null && optimizedCellView is SongItem songItem) {
            songItem.SetSong(newItem.songType, newItem.song, _songPlayType, location: _location, newItem.song.ordering);
        }
    }

    public void ScrollToTop(float timeAnimation = 0.5f) {
        if (!songScroller.IsInitialized) {
            return;
        }

        if (songScroller.VisibleItemsCount <= 0) {
            return;
        }

        int normalizedOffsetFromViewportStart = songScroller.Parameters.ContentPadding.top;
        if (timeAnimation > 0) {
            songScroller.SmoothScrollTo(0, timeAnimation, normalizedOffsetFromViewportStart);
        } else {
            songScroller.ScrollTo(0, normalizedOffsetFromViewportStart);
        }
    }

    /// <summary>
    /// Set vị trí của songlist tại 1 bài hát cụ thể
    /// </summary>
    /// <param name="acmID">acm id v3 của bài hát</param>
    public void SetPositionToSong(string acmID, float offset = 0, float pivot = 0, bool isSmooth = false) {
        if (_data.Count == 0) {
            Logger.EditorLog("CommunicationSystem-ActionSystem", $"_data count == 0 . => can't show right position");
            songScroller.SetNormalizedPosition(1f);
            return;
        }

        int total = _data.Count;
        int index = -1;
        for (int i = 0; i < total; i++) {
            if (_data[i] is Song song && song.acm_id_v3 == acmID) {
                index = i;
                break;
            }
        }

        if (index < 0) { // Không tìm thấy bài hát trong list -> đưa về đầu danh sách
            Logger.EditorLog("Communication System-ActionSystem", $"Not found song {acmID} ->> show top scrollview");
            songScroller.SetNormalizedPosition(1f);
            CustomException.Fire($"[Communication System - SongList]",
                $" Not found song with acm_id {acmID} in song list");
        } else { // Tìm thấy bài hát -> scroll tới bài hát đó
            if (isSmooth) {
                songScroller.SmoothScrollTo(index, 1, offset, pivot);
            } else {
                songScroller.ScrollTo(index, offset, pivot);
            }
        }
    }

    private void OnScrollPositionChanged(double position) {
        SongScroller_OnScroll(position);

        if (_data == null || _data.Count == 0) {
            return;
        }

        if (RemoteConfigBase.instance.ACMv4_IsLoadMore && !Configuration.instance.enableContentTool) {
            // start to load new page when there is only a certain number of song is left & is not loading
            if (songScroller == null || songScroller.VisibleItemsCount == 0) {
                return;
            }

            var lastItemViewed = songScroller.GetItemViewsHolder(songScroller.VisibleItemsCount - 1);
            if (lastItemViewed == null) {
                return;
            }

            bool isPrepareEnd = lastItemViewed.ItemIndex > _data.Count - ACMSDKv4.LOAD_WHEN_DATA_LEFT;
            Directions directions = songScroller.Velocity.y >= 0 ? Directions.Up : Directions.Down;
            if (isPrepareEnd && directions == Directions.Up && !_isLoadEnd && IsCanLoadMoreSongs()) {
                LoadMoreSongs();
            }
        }
    }

    #region Load More Song

    private void ResetDownloadACM() {
        _totalDataACM = 0;
        _songDatasACM.Clear();
        _isLoadingMore = false;
        _isLoadEnd = false;
        _pageToken = string.Empty;
        _lastToken = string.Empty;
    }

    private void LoadMoreSongs() {
        if (_isLoadingMore || (_pageToken == _lastToken && !string.IsNullOrEmpty(_lastToken))) {
            return;
        }
#if UNITY_EDITOR
        Debug.Log("LoardMoreSongs" + _data.Count);
#endif

        _isLoadingMore = true;
        string lastToken = _lastToken;
        _lastToken = _pageToken;

        ISearchAction action = null;
        if (string.IsNullOrEmpty(_keySearch)) {
            switch (_tagSong) {
                case "ALL":
                    action = ACMSDK.Instance.SearchSong("");
                    break;

                case SONG_TAG.FAVORITES:
                    List<string> favoriteList = UserEntry.GetFavoriteList();
                    if (favoriteList != null && favoriteList.Count > 0) {
                        favoriteList = SongManager.instance.ExcludeMainSong(favoriteList);
                    }

                    if (favoriteList != null && favoriteList.Count > 0) {
                        favoriteList = SongManager.GetLimitSongs(favoriteList, 200); //200 = max of api
                        action = ACMSDK.Instance.SearchSong_ByIds(favoriteList);
                    }

                    break;

                case SONG_TAG.UNLOCKED:
                    List<string> unlockSongs = UserEntry.GetUnlockSongs();
                    if (unlockSongs != null && unlockSongs.Count > 0) {
                        unlockSongs = SongManager.instance.ExcludeMainSong(unlockSongs);
                    }

                    if (unlockSongs != null && unlockSongs.Count > 0) {
                        unlockSongs = SongManager.GetLimitSongs(unlockSongs, 200); //200 = max of api
                        action = ACMSDK.Instance.SearchSong_ByIds(unlockSongs);
                    }

                    break;

                case SONG_TAG.OTHER:
                    action = ACMSDK.Instance.SearchSong_ByWhere(GetOtherQuery());
                    break;

                default:
                    action = ACMSDK.Instance.SearchSong_ByGenre(_tagSong);
                    break;
            }
        } else {
            switch (_tagSong) {
                case SONG_TAG.UNLOCKED:
                    //nothing
                    break;

                default:
                    action = ACMSDK.Instance.SearchSong_ByName(_keySearch);
                    break;
            }
        }

        OnStartSearch(action, lastToken);
    }

    /// <summary>
    /// Get ACMV4 query for other tag
    /// </summary>
    /// <returns>string format: "music_genres!=Pop,music_genres!=Rock,music_genres!=EDM"</returns>
    private string GetOtherQuery() {
        if (string.IsNullOrEmpty(_otherTagQuery.ToString())) {
            string rawTag = RemoteConfigBase.instance.Song_Tags.Replace(SONG_TAG.OTHER, string.Empty);
            rawTag = rawTag.Replace(SONG_TAG.ALL, string.Empty);
            var tags = (rawTag).StringToList();
            _otherTagQuery.Clear();
            var prefix = "music_genres!=";
            for (int i = 0; i < tags.Count; i++) {
                _otherTagQuery.Append(prefix);
                _otherTagQuery.Append(tags[i]);
                if (i < tags.Count - 1) {
                    _otherTagQuery.Append(prefix);
                    _otherTagQuery.Append(',');
                }
            }
        }

        return _otherTagQuery.ToString();
    }

    private void OnStartSearch(ISearchAction action, string lastToken) {
        action?.PageToken(_pageToken).ExtraFields("songParams,speed") //
            .OnError((string error) => {
                _lastToken = lastToken; //Restore token
                Debug.LogError(error);
                _isLoadingMore = false;
            }).OnSuccess((SearchSongResponse response) => {
                _cProcessOnSuccess = StartCoroutine(IeProcessSearchSuccess(response));
                //
            }).Run();
    }

    private IEnumerator IeProcessSearchSuccess(SearchSongResponse response) {
        yield return null;

        if (songScroller == null) {
            yield break;
        }

        SongData[] songDatas = null;
        string nextToken = string.Empty;
        if (response != null) {
            _totalDataACM = response.total;
            songDatas = response.data;
            if (response.paging != null) {
                nextToken = response.paging.cursors.after;
            }
        }

        if (songDatas != null && songDatas.Length > 0) {
            _songDatasACM.AddRange(songDatas);
            _isLoadEnd = _songDatasACM.Count == _totalDataACM;

            int countBefore = _data.Count;
            _moreList.Clear();
            for (int i = 0; i < songDatas.Length; i++) {
                SongData songData = songDatas[i];
                Song song = ACMSDKv4.ParseSong(songData);
                if (song != null && !SongManager.instance.IsHideSong(song.acm_id_v3)) {
                    song.isMainSong = false;
                    if (AddSong(song, _data)) {
                        _moreList.Add(song);
                    }
                    //SongManager.instance.AddGlobalSong(song);
                }
            }

            int countNewData = _data.Count - countBefore;
            if (countNewData > 0) {
                songScroller.AddItemsAtEnd(_moreList);
                if (countBefore == 0) {
                    ShowObjNoItem(false);
                }
            }
        } else {
            _isLoadEnd = _totalDataACM == 0;
        }

        yield return YieldPool.GetWaitForSeconds(0.1f);

        _pageToken = nextToken;
        _isLoadingMore = false;

        yield return YieldPool.GetWaitForSeconds(0.4f);

        if (songScroller == null || songScroller.VisibleItemsCount == 0) {
            yield break;
        }

        var lastItemViewed = songScroller.GetItemViewsHolder(songScroller.VisibleItemsCount - 1);
        if (lastItemViewed == null) {
            yield break;
        }

        bool isPrepareEnd = lastItemViewed.ItemIndex > _data.Count - ACMSDKv4.LOAD_WHEN_DATA_LEFT;
        if (isPrepareEnd && !_isLoadEnd) {
            LoadMoreSongs();
        }
    }

    private void ShowObjNoItem(bool b) {
        Debug.LogWarning("[ShowObjNoItem] todo");
    }

    private bool AddSong(Song song, List<IData> data) {
        //exist
        foreach (var item in data) {
            if (item is Song song2) {
                if (song2.acm_id_v3 == song.acm_id_v3) {
                    return false;
                }

                if (song2.path == song.path) {
                    return false;
                }
            }
        }

        //not
        data.Add(song);
        return true;
    }

    private bool IsCanLoadMoreSongs() {
        if (UserProgressionController.EnableFeature) {
            return false;
        }

        if (_tagSong == null) {
            return false;
        }

        string tagSong = _tagSong.ToUpper();
        if (tagSong == GenreType.POPULAR || tagSong == "MY_SONGS") {
            return false;
        }

        return true;
    }

    #endregion

    private void OnSongChanged(bool forceUpdate, HashSet<string> invalidSong) {
        bool isDataChange = false;
        if (invalidSong != null && invalidSong.Count != 0) {
            for (int index = 0; index < _data.Count; index++) {
                Song songItemRaw = _data[index] as Song;
                if (songItemRaw == null) {
                    continue;
                }

                if (!string.IsNullOrEmpty(songItemRaw.acm_id_v3) && invalidSong.Contains(songItemRaw.acm_id_v3)) {
                    _data.Remove(songItemRaw);
                    isDataChange = true;
                    index--;
                    //Debug.Log("[OnSongChanged] invalid song: " + songItemRaw.song.name + " => " + songItemRaw.song.acm_id_v3);
                }
            }
        }

        if (forceUpdate || isDataChange) {
            if (songScroller != null && songScroller.IsInitialized && this != null) {
                StartCoroutine(UpdateScrollerSafely());
            }
        }

        Song currentSong = Configuration.instance.GetCurrentSong();
        if (invalidSong != null && invalidSong.Contains(currentSong.acm_id_v3)) {
            HomeManager.instance.UpdateThemeDisk();
        }
    }

    private IEnumerator UpdateScrollerSafely() {
        yield return YieldPool.GetWaitForEndOfFrame();
        if (songScroller != null && songScroller.IsInitialized) {
            songScroller.SetItems(_data);
            songScroller.RefreshVirtualScrollSize();
        }
    }

    public void AddSongToTop(Song song) {
        if (_data.IsNullOrEmpty()) {
            return;
        }

        int n = _data.Count;
        int topSongIndex = 0;
        for (int i = 0; i < n; i++) {
            if (_data[i] is Song songData && !string.IsNullOrEmpty(songData.acm_id_v3)) {
                topSongIndex = i;
                break;
            }
        }

        _data.Insert(topSongIndex, song);
        if (this != null) {
            StartCoroutine(SetData());
        }
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public void Show(bool isShow) {
        if (BottomMenuScript.isShowBallTabForm) {
            if (!_isCachedLocalPosX) {
                _isCachedLocalPosX = true;
                _cacheLocalX = ((RectTransform) transform).anchoredPosition.x;
            }

            if (isShow) {
                gameObject.SetActive(true);
                _rtfSongsScroll.DOAnchorPosX(_cacheLocalX, 0.3f);
            } else {
                //_rtfSongsScroll.anchoredPosition -= Vector2.right * TabScript.widthCanvas;
                gameObject.SetActive(false);
            }
        } else {
            if (!_isCachedLocalPosY) {
                _isCachedLocalPosY = true;
                _cacheLocalY = ((RectTransform) transform).anchoredPosition.y;
            }

            if (isShow) { //show
                gameObject.SetActive(true);
                _rtfSongsScroll.DOAnchorPosY(_cacheLocalY, 0.3f);
            } else { //hide
                _rtfSongsScroll.DOAnchorPosY(_cacheLocalY + 853, 0.3f).OnComplete(() => {
                    //
                    gameObject.SetActive(false);
                });
            }
        }
    }

    public SongScrollerAdapter GetScrollerSongs() {
        return songScroller;
    }

    public void StopScrollingScrollView() {
        songScroller.Velocity = Vector2.zero;
    }

    public bool StartPreviewSong(string currentPreview, bool isTracking) {
        int visibleItem = songScroller.VisibleItemsCount;
        for (int i = 0; i < visibleItem; i++) {
            SongItem cellViewAtDataIndex = songScroller.GetItemViewsHolder(i).cellView as SongItem;
            if (cellViewAtDataIndex != null) {
                if (cellViewAtDataIndex.song.path.Equals(currentPreview)) {
                    // đúng bài
                    cellViewAtDataIndex.StartPreviewSong(isForce: true, isTracking: isTracking);
                    return true;
                }
            }
        }

        return false;
    }

    public void Refresh() {
        if (!songScroller.IsInitialized) {
            return;
        }

        songScroller.Refresh();
    }

    public void ForcedUpdateSongList() {
        if (this.gameObject.activeSelf) {
            Refresh();
        } else {
            _needRefresh = true;
        }
    }

    private void CheckPreviewStatus() {
        if (!songScroller.IsInitialized) {
            return;
        }

        int visibleItemsCount = songScroller.VisibleItemsCount;
        if (visibleItemsCount != 0) {
            for (int i = 0; i < visibleItemsCount; i++) {
                int index = songScroller.GetItemViewsHolder(i).ItemIndex;
                var songItem = songScroller.GetItemViewsHolder(i).cellView as SongItem;
                if (songItem != null) {
                    songItem.ChangeDiskStatus(false);
                }
            }
        }

        if (RemoteConfig.instance.PreviewMusic_AutoResetWhenComeBackHome) {
            StopAutoChangePreview();
            CheckAutoChangePreviewMusic();
        }
    }

    public void RemoveItem(SongItemRaw songItemRaw) {
        _data.Remove(songItemRaw.song);
    }

    public void ScrollToHeader(int idPack, float normalizeOffset = 0.5f, float normalizedPosition = 0.5f) {
        int index = -1;
        for (int i = 0; i < _data.Count; i++) {
            if (_data[i] is SongPackHeaderBaseData header and not SongPackLockedHeaderData) {
                if (header.packLevel.Equals(idPack)) {
                    index = i;
                    break;
                }
            }
        }

        if (index < 0) {
            return;
        }

        songScroller.ScrollTo(index, normalizeOffset, normalizedPosition);
    }

    public IEnumerator SetScrollToHeader(int idPack, float time = 0.3f, float normalizeOffset = 0.5f,
                                         float normalizedPosition = 0.5f) {
        while (!_finishStart) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        int index = -1;
        for (int i = 0; i < _data.Count; i++) {
            if (_data[i] is SongPackHeaderBaseData header and not SongPackLockedHeaderData) {
                if (header.packLevel.Equals(idPack)) {
                    index = i;
                    break;
                }
            }
        }

        if (index < 0) {
            yield break;
        }

        songScroller.SmoothScrollTo(index, time, normalizeOffset, normalizedPosition);

        yield return new WaitWhile(() => songScroller.IsScrollToAnimationRunning());
    }

    public IEnumerator SmoothScrollToSongItem(Song song, float time = 0.3f, float normalizeOffset = 0.5f,
                                              float normalizedPosition = 0.5f) {
        if (song == null) {
            yield break;
        }

        int index = -1;
        for (int i = 0; i < _data.Count; i++) {
            if (_data[i] is Song item) {
                if (item.acm_id_v3.Equals(song.acm_id_v3)) {
                    index = i;
                    break;
                }
            }
        }

        if (index < 0)
            yield break;

        songScroller.SmoothScrollTo(index, time, normalizeOffset, normalizedPosition);
        yield return new WaitWhile(() => songScroller.IsScrollToAnimationRunning());
    }

    public void SetScrollToSong(Song song, float time = 0.3f, float normalizeOffset = 0.5f,
                                float normalizedPosition = 0.5f) {
        int index = -1;
        for (int i = 0; i < _data.Count; i++) {
            if (_data[i] is Song item) {
                if (item.acm_id_v3.Equals(song.acm_id_v3)) {
                    index = i;
                    break;
                }
            }
        }

        if (index < 0)
            return;

        songScroller.SmoothScrollTo(index, time, normalizeOffset, normalizedPosition);
    }
}