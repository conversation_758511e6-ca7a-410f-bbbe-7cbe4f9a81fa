using System;
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using TilesHop.ChallengeOldUser;
using TilesHop.EconomySystem;
using TilesHop.LiveEvent.GalaxyQuest;
using GameCore.LiveEvent.MysteryDoor;

public class ContinueUI : MonoBehaviour, IEscapeHandler {
    #region Fields

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~~~~~~~~~
    [SerializeField] private Transform  window;
    [SerializeField] private GameObject closeButton;
    [SerializeField] private Text       vipText;
    [SerializeField] private Text       vipContinueText;
    [SerializeField] private Text       videoLeftText;

    [Space] [Header("Count down")]
    [SerializeField] private GameObject objCountDownTime;

    [SerializeField] private Image imgCountDownTimeFill;
    [SerializeField] private Text  txtCountDownTime;

    [SerializeField] private GameObject objCountDownFreeRevive;
    [SerializeField] private Image      imgCountDownFreeReviveFill;
    [SerializeField] private Text       txtCountDownFreeRevive;

    [SerializeField] private   GameObject nonFreeReviveGroupObject;
    [SerializeField] protected Button     btnVideoContinue;

    [SerializeField]
    protected Button btnVipContinue;

    [SerializeField] private GameObject txtVipContinue;
    [SerializeField] private GameObject txtVipFreeRevive;

    [SerializeField]
    protected GameObject objVipOffer;

    [SerializeField]
    protected GameObject objVipFree;

    [SerializeField] private Button btnFreeContinue;

    [Header("Style 4")] [SerializeField] private Animation[] buttonAnims;
    [SerializeField]                     private AudioSource audioBG;

    [SerializeField] private GameObject objIconText;
    [SerializeField] private GameObject objText3DayFree;

    [Header("Config last Continue")]
    [SerializeField] private bool isLastContinue;

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~~~~~~~~~
    private RemoteConfig remoteConfig => RemoteConfigBase.instance;
    protected int                _continueGemsPrice = 200;
    protected ReviveType         _reviveType        = ReviveType.AD;
    private   bool               _alreadyUseDiamond = false;
    protected List<ReviveConfig> reviveConfigs;

    private Vector3 _windowPos;
    private Vector3 _bottomPos;

    private bool _isHideBannerAfterClose;

    private IEnumerator _ieCountDown;
    private float       _countDownTime = 3f;
    private float       _totalTime     = 3f;

    protected bool  isCounting;
    private   bool  _isAutoClose;
    private   float _timePlaySFXCountDown;

    private int  _countFreeReviveVipUsed;
    private bool _isFreeReviveByVIP;
    private bool _isShowFreeRemain = false;

    private LOCATION_NAME _location = LOCATION_NAME.revive;
    private bool          _isRevive;
    private Action<bool>  _callback;

    public Action<bool> Callback {
        get { return _callback; }
    }

    public int continueGemsPrice => _continueGemsPrice;

    #endregion

    #region Unity method

    protected virtual void Awake() {
        btnVideoContinue.onClick.AddListener(BtnVideoContinueClick);
        btnVipContinue.onClick.AddListener(BtnVipContinueClick);

        if (btnFreeContinue != null) {
            btnFreeContinue.onClick.AddListener(btnFreeContinueOnClick);
        }

        SubscriptionController.OnChange += OnChangeSubscription;
        if (audioBG != null) {
            audioBG.enabled = true;
        }

        if (nonFreeReviveGroupObject == null) {
            nonFreeReviveGroupObject = btnVideoContinue.gameObject;
        }

        _windowPos = btnVideoContinue.transform.parent.localPosition;
        _bottomPos = btnVipContinue.transform.parent.localPosition;
        LocalizationManager.instance.UpdateFont(vipText);
        LocalizationManager.instance.UpdateFont(vipContinueText);
        LocalizationManager.instance.UpdateFont(videoLeftText);
        LocalizationManager.instance.UpdateFont(txtCountDownTime);
    }

    private bool _isInited = false;

    protected virtual void OnEnable() {
        if (!_isInited) {
            return;
        }

        CheckUIChallengeForOldUser();
        OnOpenContinueUI();
    }

    public virtual void OnOpenContinueUI() {
        _isAutoClose = !remoteConfig.Reborn_AlwaysKeep;
        _countDownTime = remoteConfig.Reborn_CountDownTime;
        _countFreeReviveVipUsed = GameController.instance._freeReviveVipUsed;

        _isAutoClose = !RemoteConfig.instance.Reborn_AlwaysKeep;
        _countDownTime = RemoteConfig.instance.Reborn_CountDownTime;
        _totalTime = _countDownTime;
        isCounting = true;

        if (SubscriptionController.IsSubscriptionVip()) {
            var remoteIAPConfig = Configuration.instance.GetEconomyRemoteConfig();
            if (remoteIAPConfig != null && remoteIAPConfig.ModifiedSubscription_Enable &&
                remoteIAPConfig.Subscription_Revive_Free > 0) {
                _isAutoClose = false;
            } else {
                _isAutoClose = true;
            }
        }

        AnalyticHelper.instance.SetCurrentLocation(_location);

        Dictionary<string, object> param = AnalyticHelper.GetDefaultParam(_location.ToString());
        param.TryAdd(TRACK_NAME.song_mode, GameController.enableEndless ? "endless" : "normal");

        if (ChallengeOldUserController.IsPlayingChallenge) {
            param.TryAdd("song_start", ChallengeOldUserController.instanceSafe.CurrentTimes);
        }

        AnalyticHelper.LogSong(songStatus: SONG_STATUS.song_revive_impression, song: NotesManager.instance.song,
            location: _location.ToString(), param);

        if ((!remoteConfig.Economy_MileStone_Enable) && !(TopBar.instance is null)) {
            if (remoteConfig.ProgressBar_StyleIndex != 3) {
                TopBar.instance.ToggleDiamondContainer(true);
            }
        }

        if (closeButton != null) {
            closeButton.SetActive(false);
        }

        UpdateUIButtons();

        StartCountDown();

        EventEscapeManager.Push(this);
    }

    protected virtual void StartCountDown() {
        if (_ieCountDown != null) {
            StopCoroutine(_ieCountDown);
        }

        _ieCountDown = IECountDown();
        StartCoroutine(_ieCountDown);
    }

    protected virtual void OnDisable() {
        if (!_isInited) {
            return;
        }

        if (AdsManager.instance != null) {
            AdsManager.instance.HideBanner();
        }

        EventEscapeManager.Pop(this);
        if (isLastContinue) {
            this._callback?.Invoke(_isRevive);
        }
    }

    protected virtual void OnDestroy() {
        SubscriptionController.OnChange -= OnChangeSubscription;
    }

    #endregion

    #region Escape Handlers

    public bool CanHandleEventBack() {
        return gameObject.activeInHierarchy;
    }

    public bool HandleEventBack() {
        Cancel();
        return true;
    }

    #endregion

    #region On Click Buttons

    protected void BtnVipContinueClick() {
        SoundManager.PlayGameButton();
        if (UnlimitedReviveManager.IsInUnlimitedRevive) {
            DoGameContinue("unlimited_revive");
        } else if (SubscriptionController.IsSubscriptionVip()) {
            AnalyticHelper.FireString(BUTTON_NAME.Continue_Click_Vip.ToString());
            DoGameContinue("vip");
            GameController.instance._freeReviveVipUsed++; //increase count free revive
        } else {
            // TH-3832: cho user free revive nốt khi hết Unlimited revive đúng lúc dừng ở màn hình revive
            if (objVipFree.activeSelf) {
                DoGameContinue("unlimited_revive");
                return;
            }

            _isAutoClose = false;
            //SubscriptionController.SetScreen(SUBSCRIPTION_SCREEN.revive.ToString());
            bool showSubscription =
                SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.revive.ToString(), false, userOpen: true);
            if (showSubscription) {
                if (audioBG != null) {
                    audioBG.enabled = false;
                }

                isCounting = false;
                SubscriptionController.OnPopupClose += OnPopupSubsClose;
            }
        }
    }

    protected void btnFreeContinueOnClick() {
        SoundManager.PlayGameButton();
        AnalyticHelper.FireString(BUTTON_NAME.Continue_Free.ToString());
        DoGameContinue("free");
    }

    private void BtnVideoContinueClick() {
        SoundManager.PlayGameButton();
        AnalyticHelper.Button_Click(BUTTON_NAME.Continue_Video);
        AnalyticHelper.FireString(BUTTON_NAME.Continue_Click_Video.ToString());
        if (Configuration.instance.isTutorial) {
            AnalyticHelper.LogEvent(TRACK_NAME.fn_continue_click);
        }

        _isAutoClose = false;
        isCounting = false;
        AdsManager.instance.ShowRewardAds(VIDEOREWARD.revive, GameController.instance?._song  , location: _location.ToString(), true,
            OnRewardVideoCompleted);

        if (closeButton != null) {
            closeButton.SetActive(true);
        }
    }

    #endregion

    #region Callback Action

    private void OnPopupSubsClose() {
        SubscriptionController.OnPopupClose -= OnPopupSubsClose;
        isCounting = true;
    }

    private void OnChangeSubscription(bool isSuccess) {
        if (this.gameObject.activeSelf && isSuccess) {
            DoGameContinue("vip");
        }
    }

    private void OnRewardVideoCompleted(bool isCompleted) {
        isCounting = true;
        if (GameController.CheckInstanced() && this != null && gameObject.activeSelf) {
            if (!isCompleted) {
                return;
            }

            DoGameContinue("ads");
        }
    }

    #endregion

    public virtual void EndWaitPay() {
        if (Configuration.instance.GetDiamonds() >= _continueGemsPrice) {
            Configuration.UpdateDiamond(-_continueGemsPrice, CurrencySpendSource.revive_screen.ToString());
            _alreadyUseDiamond = true;
            AnalyticHelper.Button_Click(BUTTON_NAME.Continue_Gems);
            DoGameContinue(TRACK_PARAM.currency);

            if (_continueGemsPrice > 0) {
                AirfluxTracker.TrackSpendCredits(_continueGemsPrice);
            }
        } else {
            Cancel();
        }
    }

    protected virtual void DoGameContinue(string source) {
        _isRevive = true;
        if (TopBar.instance is not null) {
            TopBar.instance.ToggleDiamondContainer(false);
        }

        SoundManager.StopRevive_BG();
        GameController.instance.GameContinue(source);
        SoundManager.PlayRevived();
        gameObject.SetActive(false);
        if (_ieCountDown != null) {
            StopCoroutine(_ieCountDown);
        }
    }

    protected virtual void ActiveBtnVipRevive(bool isActive, bool isFree) {
        btnVipContinue.gameObject.SetActive(isActive);
        if (isActive) {
            objVipFree.SetActive(isFree);
            objVipOffer.SetActive(!isFree);
        }
    }

    private bool CanReviveByAdsVideo() {
        return !btnVipContinue.gameObject.activeInHierarchy || (GameController.CheckInstanced() &&
                                                                GameController.instance.continueCount <
                                                                remoteConfig.Reborn_MaxRebornVideo);
    }

    protected void UpdateUIButtons() {
        _isShowFreeRemain = false;
        bool isFreeRevive = UnlimitedReviveManager.IsInUnlimitedRevive;
        if (SubscriptionController.IsSubscriptionVip() || isFreeRevive) { //VIP
            _isFreeReviveByVIP = true; // default always free revive
            if (!isFreeRevive && remoteConfig.Economy_IsEnable) {
                var remoteIAPConfig = Configuration.instance.GetEconomyRemoteConfig();
                if (remoteIAPConfig != null && remoteIAPConfig.ModifiedSubscription_Enable &&
                    remoteIAPConfig.Subscription_Revive_Free > 0) {

                    if (_countFreeReviveVipUsed >= remoteIAPConfig.Subscription_Revive_Free) {
                        _isFreeReviveByVIP = false;
                        _isShowFreeRemain = false;
                    } else {
                        _isShowFreeRemain = remoteConfig.ReviveScreen_ShowSubscription_RemainRevive;
                        if (_isShowFreeRemain) {
                            LocalizationManager.instance.UpdateFont(txtCountDownFreeRevive);
                            txtCountDownFreeRevive.text =
                                $"<size=60>{remoteIAPConfig.Subscription_Revive_Free - _countFreeReviveVipUsed}/{remoteIAPConfig.Subscription_Revive_Free}</size>\n{LocalizationManager.instance.GetLocalizedValue("FREE_REVIVE").ToUpper()}\n{LocalizationManager.instance.GetLocalizedValue("REMAIN").ToUpper()}";
                        }
                    }
                }
            }

            if (_isFreeReviveByVIP) {
                // tắt button VIP revive và bật button Free revive
                ActiveBtnVipRevive(true, true);
                if (_isShowFreeRemain) {
                    txtVipFreeRevive.SetActive(true);
                    txtVipContinue.SetActive(false);
                } else {
                    txtVipFreeRevive.SetActive(false);
                    txtVipContinue.SetActive(true);
                }

                nonFreeReviveGroupObject.SetActive(false);

                objIconText.SetActive(false);
                objText3DayFree.SetActive(false);
            } else {
                ActiveBtnVipRevive(false, false);
                nonFreeReviveGroupObject.SetActive(true);
            }
        } else {
            if (remoteConfig.Subscription_Enable) {
                ActiveBtnVipRevive(true, false);
                vipText.text = LocalizationManager.instance.GetLocalizedValue("FREE_REVIVE").ToUpper();
            } else {
                ActiveBtnVipRevive(false, false);
            }

            nonFreeReviveGroupObject.SetActive(CanReviveByAdsVideo());

            bool isDefaultLanguage = LocalizationManager.GetCurrentLanguageID() == LocalizationManager.DefaultLanguage;
            objIconText.SetActive(isDefaultLanguage);
            objText3DayFree.SetActive(!isDefaultLanguage);
        }

        UpdateSiblingIndexes();

        if (buttonAnims != null) {
            foreach (Animation buttonAnim in buttonAnims) {
                if (remoteConfig.Reborn_ShowButtonAnimation) {
                    buttonAnim.Play();
                } else {
                    buttonAnim.Stop();
                }
            }
        }

        if (remoteConfig.Reborn_ShowMusicBackground) {
            audioBG.enabled = true;
            audioBG.time = 0;
            audioBG.Play();
        } else {
            audioBG.enabled = false;
        }

        if (_isShowFreeRemain) {
            objCountDownFreeRevive.SetActive(true);
            objCountDownTime.SetActive(false);
        } else {
            objCountDownFreeRevive.SetActive(false);
            objCountDownTime.SetActive(true);
        }
    }

    protected virtual void UpdateSiblingIndexes() {
        nonFreeReviveGroupObject.transform.SetSiblingIndex(remoteConfig.Reborn_ShowAdFirst ? 0 : 1);
        btnVipContinue.transform.SetSiblingIndex(remoteConfig.Reborn_ShowAdFirst ? 1 : 0);
    }

    private IEnumerator IECountDown() {
        SoundManager.PlayRevive_BG();
        _timePlaySFXCountDown = 2f;

        do {
            if (isCounting) {
                float percent = 1f - (_countDownTime / _totalTime);
                if (_isShowFreeRemain) {
                    imgCountDownFreeReviveFill.fillAmount = 1f - percent;
                } else {
                    imgCountDownTimeFill.fillAmount = 1f - percent;
                }

                UpdateCountdownText();

                _countDownTime -= Time.deltaTime;

                if (closeButton != null && !closeButton.activeSelf) {
                    if (_countDownTime <= 1) {
                        closeButton.SetActive(true);
                    }
                }

                if (_countDownTime < _timePlaySFXCountDown) {
                    _timePlaySFXCountDown -= 1f;
                    SoundManager.instance.PlayRevive_CountDown();
                }
            }

            yield return null;
        } while (_countDownTime >= 0);

        UpdateUIEndCountDown();
        yield return null;

        if (_isAutoClose) {
            Cancel();
        }
    }

    private void UpdateCountdownText() {
        if (!_isShowFreeRemain) {
            txtCountDownTime.text = Mathf.Ceil(_countDownTime).ToString();
        }
    }

    private void UpdateUIEndCountDown() {
        if (_isShowFreeRemain) {
            imgCountDownFreeReviveFill.fillAmount = 0;
        } else {
            txtCountDownTime.text = "0";
            imgCountDownTimeFill.fillAmount = 0f;
        }

        if (audioBG != null) {
            audioBG.Stop();
        }
    }

    public virtual void Cancel() {
        CloseContinueUI();
    }

    private void CloseContinueUI() {
        if (_ieCountDown != null) {
            StopCoroutine(_ieCountDown);
        }

        SoundManager.StopAll();
        SoundManager.PlayGameButton();

        bool isShowSecondContinue = CheckCanShowSecondContinue();

        if (remoteConfig.ReviveScreen_StyleIndex == 2 && isShowSecondContinue) {
            UIController.ui.ShowContinueBooster();
        } else if (CheckCanShowBoosterReviveOffer()) {
            UIController.ui.ShowReviveOfferWithBoosters();
        } else {
            AnalyticHelper.Button_Click(BUTTON_NAME.Continue_Cancel);
            AnalyticHelper.FireString(BUTTON_NAME.Continue_Cancel.ToString());
            if (EndlessIteration.IsActive) {
                GameController.instance.isContinueEndless = false;
            }

            GameController.instance.ShowResultUi(isCheckTrySkin: true, isCheckSongOfDay: true,
                isCancelReborn: true); // game cancel Revive
            this.isLastContinue = true;
        }

        gameObject.SetActive(false);
    }

    private bool CheckCanShowSecondContinue() {
        if (isLastContinue) {
            return false;
        }

        bool hasCompletedSong = GameController.instance.BestStars >= 3;

        if (remoteConfig.Hybrid_UniqueStarIngame && hasCompletedSong) {
            return false;
        }

        if (GameController.enableEndless) {
            if (!ChallengeMode.IsActive || hasCompletedSong) {
                return false;
            }
        }

        return CanLostBenefit();
    }

    private bool CanLostBenefit() {
        if (remoteConfig.PowerCube_IsEnable && PowerCubeManager.instanceSafe.streak > 0) {
            return true;
        }

        // if (remoteConfig.GalaxyQuest_IsEnable && GalaxyQuest.GetStateData().currentStep > 0) {
        //     return true;
        // }

        if (GameController.instance.canLoseCurrenciesOnFail) {
            int potentialGems = GameController.instance.GetDiamond();
            if (potentialGems > 0) {
                return true;
            }
            
            if (GameController.instance.GetCollectedToken(TokenType.MysteryDoor_Drill) > 0) {
                return true;
            }

            if (GameController.instance.GetCollectedToken(TokenType.MilestoneToken) > 0) {
                return true;
            }
        }

        return false;
    }

    protected bool CheckCanShowBoosterReviveOffer() {
        if (remoteConfig.Booster_IsEnable && remoteConfig.Booster_ReviveOffer) {
            int continueCount = GameController.instance.continueCount;

            if (continueCount >= remoteConfig.Reborn_MaxRebornVideo) {
                return false;
            }

            int x = remoteConfig.Booster_ReviveOffer_Capping;
            if (x <= 1) {
                return true;
            }

            if (continueCount > 0 && continueCount % (x - 1) == 0) {
                return true;
            }
        }

        return false;
    }

    private bool IsActiveContinueBooster() {
        if (remoteConfig.ReviveScreen_StyleIndex != 2 || GameController.enableEndless || isLastContinue) {
            return false;
        }

        if (remoteConfig.PowerCube_IsEnable && PowerCubeManager.instanceSafe.streak > 0) {
            return true;
        }

        if (remoteConfig.GalaxyQuest_IsEnable && GalaxyQuest.GetStateData().currentStep > 0) {
            return true;
        }

        if (remoteConfig.MysteryDoor_IsEnable &&
            MysteryDoorManager.EnableFeature /*&& GameController.instance.TokenEarn > 0*/) {
            return true;
        }

        return false;
    }

    public void OverlayCancel() {
        _countDownTime -= 1f;
        if (_countDownTime < 0) {
            Cancel();
        }
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public void ShowPopup(Action<bool> callback) {
        _isInited = true;
        if (callback != null) {
            this._callback = callback;
        }

        _isRevive = false;
        SetActive(true);
    }

    #region ChallengeForOldUser

    private GameObject _tooltipCOU;

    protected virtual void CheckUIChallengeForOldUser() {
        if (window == null)
            return;

        if (ChallengeOldUserController.NeedShowReviveToolTip()) {
            if (_tooltipCOU == null) {
                var prefab = Resources.Load<GameObject>("UI/ChallengeForOldUser_KeepGoing");
                if (prefab != null) {
                    _tooltipCOU = Instantiate(prefab, window);
                }
            }

            ChallengeOldUserTracking.TrackReviveKeepGoingImpression();
        }
    }

    #endregion

    protected void ProcessGemPricesConfig() {
        var configs = GetReviveConfig();
        reviveConfigs ??= new List<ReviveConfig>();
        foreach (string rawConfig in configs) {
            var details = rawConfig.Split('/');
            if (details.Length == 2) {
                if (Enum.TryParse(details[0], true, out ReviveType reviveType)) {
                    var config = new ReviveConfig();
                    config.reviveType = reviveType;

                    if (!int.TryParse(details[1], out config.price)) {
                        config.price = 100;
                    }

                    reviveConfigs.Add(config);
                }
            }
        }
    }

    private string[] GetReviveConfig() {
        string config = remoteConfig.ReviveScreen_GemsPriceList;
        if (ChallengeMode.IsActive) {
            if (GameController.enableEndless) {
                if (!string.IsNullOrEmpty(remoteConfig.ChallengeMode_ReviveEndless_Config)) {
                    config = remoteConfig.ChallengeMode_ReviveEndless_Config;
                }
            } else {
                if (!string.IsNullOrEmpty(remoteConfig.ChallengeMode_ReviveNormal_Config)) {
                    config = remoteConfig.ChallengeMode_ReviveNormal_Config;
                }
            }
        }

        return config.Split(';');
    }

    protected void UpdateReviveConfig() {
        if (reviveConfigs.IsNullOrEmpty()) {
            _continueGemsPrice = remoteConfig.ReviveScreen_GemsPrice;
            _reviveType = remoteConfig.ReviveScreen_UsingGems ? ReviveType.AD_GEMS : ReviveType.AD;
            return;
        }

        ReviveConfig config;
        if (GameController.instance.continueCount < reviveConfigs.Count) {
            config = reviveConfigs[GameController.instance.continueCount];
        } else {
            config = reviveConfigs[^1];
        }

        _continueGemsPrice = config.price;
        _reviveType = config.reviveType;
    }
}

[Serializable]
public struct ReviveConfig {
    public ReviveType reviveType;
    public int        price;
}

public enum ReviveType : byte {
    AD,
    GEMS,
    AD_GEMS,
}