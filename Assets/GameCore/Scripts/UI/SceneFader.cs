using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using System.Collections;
using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using GamePlay.Levels;
using System.Collections.Generic;
using System.Globalization;
using DG.Tweening;
using Music.ACM;
using TilesHop.LiveEvent;
using OnboardingFlow;
using TilesHop.GameCore;
using Debug = UnityEngine.Debug;

public class SceneFader : MonoBehaviour, IEscapeHandler {
    public static SceneFader instance;

    [SerializeField] private Image          img;
    [SerializeField] private Text           text;
    [SerializeField] private AnimationCurve curve;
    [SerializeField] private GameObject     canvas;
    [SerializeField] private GameObject     loadingBar;
    [SerializeField] private GameObject     overlay;
    [SerializeField] private Image          loadingProgress;
    [SerializeField] private Text           percent;

    private GameObject _selectThemeTransition;
    private Animator   _animator;

    private bool  _isAnimated = false;
    private float _tSpeed     = 0;
    private Color _tColor     = new Color(0f, 0f, 0f, 0);

    private Song   _loadingSong;
    private bool   _isFromHome;
    private float  _loadSongTime  = 5f;
    private float  _startLoadTime = 0;
    private float  _percentLoading;
    private string _messageError;

    private TipsV2 _tipsV2;

    private int _fadeIn  = Animator.StringToHash("Trans_in");
    private int _fadeOut = Animator.StringToHash("Trans_open");

    public Song LastPlaySong { private set; get; }
    public Song CurrentLoadingSong => _loadingSong;

    private CancellationTokenSource _coroutineDownload;
    private string                  _location;

    public static event Action<bool, bool> OnLoadingScene;

    public bool IsShowLoading => _isAnimated || canvas.activeSelf;

    #region Unity Methods

    private void Awake() {
        if (instance == null) {
            instance = this;
            DontDestroyOnLoad(gameObject);
        } else if (instance != null) {
            Destroy(gameObject);
        }
    }

    private void Start() {
        Util.WaitRemoteConfigDone(EditPosition, true);

        SceneManager.sceneLoaded += OnSceneLoaded;
        _tSpeed = Mathf.Max(Time.deltaTime, 0.0167f) * 3f;
    }

    #endregion

    #region Escape Handlers

    public bool CanHandleEventBack() {
        return IsOverlay() || _isAnimated;
    }

    public bool HandleEventBack() {
        if (overlay.activeInHierarchy)
            HideOverlay();
        else if (_isAnimated)
            AbortDownload();
        return true;
    }

    #endregion

    private void EditPosition() {
        //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

        if (AdsManager.instance.allowBanner) {
            loadingBar.transform.localPosition += Vector3.up * 40;
            percent.gameObject.SetActive(false);
        }

        if (RemoteConfigBase.instance.Theme_IsEnableSelectTheme) {
            _selectThemeTransition = Instantiate(Resources.Load<GameObject>("UI/SelectThemesSceneFader"), transform);
            _selectThemeTransition.SetActive(false);
            _selectThemeTransition.transform.localPosition = Vector3.zero;
            _animator = _selectThemeTransition.GetComponentInChildren<Animator>();
        }
    }

    public void BlockUserInput(bool isBlock) {
        SetActiveCanvas(isBlock);
    }

    public bool IsOverlay() {
        return overlay.activeInHierarchy;
    }

    public void ShowOverlay() {
        loadingBar.SetActive(false);
        SetActiveCanvas(true);
        overlay.SetActive(true);
        EventEscapeManager.Push(this);
    }

    public void HideOverlay() {
        SetActiveCanvas(false);
        overlay.SetActive(false);
        EventEscapeManager.Pop(this);
    }

    private void SetActiveLoadingBar(bool isActive) {
        if (isActive && !loadingBar.activeSelf) {
            loadingBar.SetActive(true);
            EventEscapeManager.Push(this);
        } else if (!isActive && loadingBar.activeSelf) {
            loadingBar.SetActive(false);
            EventEscapeManager.Pop(this);
        }
    }

    public void SetError(string error) {
        _messageError = error;
    }

    private void OnSceneLoaded(Scene scene, LoadSceneMode mode) {
        _isAnimated = false;
        FadeIn(this.GetCancellationTokenOnDestroy()).Forget();
    }

    public void FadeTo(string scene, Song song = null, string location = "") {
        if (_isAnimated) {
            return;
        }

        _isAnimated = true;
        OnLoadingScene?.Invoke(true, false);
        if (song != null) {
            this._location = location;
            bool isFromHome = SceneManager.GetActiveScene().name.Equals("Home");
            _coroutineDownload = new CancellationTokenSource();
            FadeOutSong(song, isFromHome: isFromHome, true, _coroutineDownload.Token).Forget();
        } else {
            FadeOut(scene, this.GetCancellationTokenOnDestroy()).Forget();
        }
    }

    private async UniTask FadeIn(CancellationToken cancellationToken = default) {
        OnLoadingScene?.Invoke(false, true);
        SetActiveCanvas(true);
        text.transform.gameObject.SetActive(false);
        SetActiveLoadingBar(false);
        await IEFadeBlackScreen(false, cancellationToken: cancellationToken);

        if (_messageError != null) {
            Util.ShowMessage(_messageError, 1);
            _messageError = null;
        }

        SetActiveCanvas(false);
    }

    private async UniTask FadeOut(string scene, CancellationToken cancellationToken = default) {
        SetActiveCanvas(true);
        SetActiveLoadingBar(false);

        AsyncOperation async = SceneManager.LoadSceneAsync(scene);
        if (async == null) {
            return;
        }

        async.allowSceneActivation = false;
        text.transform.gameObject.SetActive(false);

        // Loading overlay
        await IEFadeBlackScreen(true, cancellationToken: cancellationToken);

        if (!IsSDKsLoaded()) {
            text.transform.gameObject.SetActive(true);
            while (!IsSDKsLoaded()) {
                await UniTask.Yield(cancellationToken);
            }
        }

        async.allowSceneActivation = true;
    }

    private bool IsSDKsLoaded() {
        while (!SongManager.isInitedSongList) {
            return false;
        }

        while (!SongCards.isInited) {
            return false;
        }

        while (!LiveEventManager.isInstanced || !LiveEventManager.instance.IsInit) {
            return false;
        }

        return true;
    }

    void SetActiveCanvas(bool value) {
        if (canvas != null)
            canvas.SetActive(value);
    }

    private bool _isPauseFadeOutSong;

    private async UniTask FadeOutSong(Song song, bool isFromHome, bool setScreenView = true,
                                      CancellationToken cancellationToken = default) {
        _isPauseFadeOutSong = false;
        if (song == null) {
            return;
        }

        InitializeSongLoading(song, isFromHome);

        AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_mp3_start, song);
        bool newData = DownloadManager.instanceSafe.CheckDataAndDownload(song);

        if (newData) {
            await HandleNewDataDownload(song, setScreenView, cancellationToken);
        } else {
            await HandleExistingData(song, cancellationToken);
        }

        if (_isPauseFadeOutSong) {
            return;
        }

        await LoadMusicWithSDK(song, cancellationToken);
        if (_isPauseFadeOutSong) {
            return;
        }

        await ProcessSongData(song, cancellationToken);
        await LoadNoteData(song);
        if (_isPauseFadeOutSong) {
            return;
        }

        RestoreOldData(song);
        ConfigureSongSettings(song);
        FinalizeAndLoadScene(song, isFromHome);
    }

    private void InitializeSongLoading(Song song, bool isFromHome) {
        _loadingSong = song;
        _isFromHome = isFromHome;
        SetActiveCanvas(true);
        SetActiveLoadingBar(false);
    }

    private async UniTask HandleNewDataDownload(Song song, bool setScreenView,
                                                CancellationToken cancellationToken = default) {
        try {
            PreventScreenSleep();
            SetupDownloadUI(setScreenView);
            SetupLoadingProgress();

            _startLoadTime = Time.time;

            while (DownloadManager.instanceSafe.IsNotReady()) {
                if (CheckDownloadError(song)) {
                    _isPauseFadeOutSong = true;
                    return;
                }

                if (CheckDownloadTimeout(song)) {
                    _isPauseFadeOutSong = true;
                    return;
                }

                UpdateProgress(song);
                await UniTask.Yield(cancellationToken);
            }
        } catch (OperationCanceledException) {
            _isPauseFadeOutSong = true;
        }
    }

    private void SetupDownloadUI(bool setScreenView) {
        if (setScreenView) {
            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.DOWNLOAD_SONG);
            _location = LOCATION_NAME.DOWNLOAD_SONG.ToString();
        }

        if (Tips.instance == null) {
            var tips = Util.ShowPopUpTip(Tips.ShowType.Downloading, LOCATION_NAME.DOWNLOAD_SONG);
            if (tips is TipsV2 tipsV2) {
                _tipsV2 = tipsV2;
            }
        }

        if (_tipsV2 != null) {
            SetActiveCanvas(false);
        }
    }

    private void SetupLoadingProgress() {
        if (percent != null)
            percent.text = string.Empty;
        if (loadingProgress != null)
            loadingProgress.fillAmount = 0;
        SetActiveLoadingBar(true);
    }

    private bool CheckDownloadError(Song song) {
        string error = DownloadManager.instanceSafe.GetError();
        if (error != null) {
            AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_mp3_fail, song, error);
            DownloadFailed(song, error, false);
            return true;
        }

        return false;
    }

    private bool CheckDownloadTimeout(Song song) {
        if (RemoteConfig.instance.DownloadSong_TimeLimit_Enable) {
            if (Time.time - _startLoadTime > RemoteConfig.instance.DownloadSong_TimeLimit_Value) {
                string errorMsg = "OverTime Download";
                AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_mp3_fail, song, errorMsg);
                DownloadFailed(song, errorMsg);
                Logger.EditorLogError(
                    $"Failed vì quá {RemoteConfig.instance.DownloadSong_TimeLimit_Value}s vẫn không có data trả về");
                return true;
            }
        }

        return false;
    }

    private async UniTask HandleExistingData(Song song, CancellationToken cancellationToken = default) {
        try {
            AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_midi_start, song);
            await IEFadeBlackScreen(true, cancellationToken: cancellationToken);

            if (text != null) {
                text.transform.gameObject.SetActive(true);
            }

            _startLoadTime = Time.time;
            while (DownloadManager.instanceSafe.IsNotReady() && !SongManager.instance.IsLocalSong(song.path)) {
                if (Time.time - _startLoadTime > _loadSongTime) {
                    string errorMsg = "OverTime";
                    AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_mp3_fail, song, errorMsg);
                    DownloadFailed(song, errorMsg);
                    _isPauseFadeOutSong = true;
                    return;
                }

                await UniTask.Yield(cancellationToken);
            }
        } catch (OperationCanceledException) {
            _isPauseFadeOutSong = true;
        }
    }

    private async UniTask LoadMusicWithSDK(Song song, CancellationToken cancellationToken = default) {
        try {
            AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_mp3_success, song);

            if (_tipsV2 != null) {
                _tipsV2.OnLoadingSceneSuccess();
            }

            if (SuperpoweredSDK.instance == null) {
                HandleSDKError(song, "SuperpoweredSDK instance is null");
                return;
            }

            SuperpoweredSDK.instance.LoadMusic(song);
            _startLoadTime = Time.time;

            while (!SuperpoweredSDK.instance.IsReady()) {
                if (Time.time - _startLoadTime > _loadSongTime) {
                    HandleSDKError(song, "OverTime SuperpoweredSDK Load Music");
                    return;
                }

                await UniTask.Yield(cancellationToken);
            }

            if (SongManager.instance.IsLocalSong(song.path)) {
                song.length = SuperpoweredSDK.instance.musicLength;
                FileHelper.SaveFileLocalSong();
            }
        } catch (OperationCanceledException) {
            _isPauseFadeOutSong = true;
        }
    }

    private void HandleSDKError(Song song, string errorMsg) {
        Logger.LogError($"SceneFader superpowered load failed. {errorMsg}");
        DownloadFailed(song, errorMsg);
        _isPauseFadeOutSong = true;
    }

    private async UniTask ProcessSongData(Song song, CancellationToken cancellationToken = default) {
        try {
            if (DownloadManager.isUseReplaceSong) {
                song.isTutorialSong = false;
            }

            if (RemoteConfig.instance.isUseRhythmToolForACM && !song.path.Contains(FILE_EXT.MP3)) {
                song.fullPathMp3 = song.GetLocalMp3Path();
                song.midiLocalPath = null;
                FileSelectorV2Script.MakeNoteAndSave(song).ToUniTask(this).Forget();
                while (FileSelectorV2Script.isMakeNoteAndSaving) {
                    await UniTask.Yield(cancellationToken);
                }
            }

            if (RemoteConfig.instance.NotesDifficult_IsEnable && RemoteConfig.instance.NotesDifficult_Easy_UseBpm &&
                Configuration.GetSongBpmACM(song.path) == 0 && Configuration.GetSongBpmLocal(song.path) == 0) {
                UpdateBPM(song, Note, cancellationToken).Forget();
                while (RemoteConfig.instance.NotesDifficult_IsEnable && Configuration.GetSongBpmACM(song.path) == 0 &&
                       Configuration.GetSongBpmLocal(song.path) == 0) {
                    await UniTask.Yield(cancellationToken);
                }
            }
        } catch (OperationCanceledException) {
            _isPauseFadeOutSong = true;
        }
    }

    private async UniTask LoadNoteData(Song song) {
        if (string.IsNullOrEmpty(song.midiLocalPath) || NotesManager.instance.NeedReloadNoteData(song)) {
            await NotesManager.instance.LoadNoteData(song).ToUniTask(this);
        }

        if (NotesManager.instance.noteCount == 0) {
            string errorMsg = "Midi file is empty note count";
            DownloadFailed(song, errorMsg);
            _isPauseFadeOutSong = true;
            return;
        }
    }

    private void RestoreOldData(Song song) {
        if (DownloadManager.isUseReplaceSong) {
            DownloadManager.isUseReplaceSong = false;
            if (song != null) {
                song.isTutorialSong = false;
                song.path = DownloadManager.oldPath;
                song.msc_path = DownloadManager.oldMscPath;
                song.midiContentType = DownloadManager.oldMidiContentType;
            }

            DownloadManager.ResetReplaceSongData();

            if (NotesManager.instance?.song != null) {
                NotesManager.instance.song.isTutorialSong = false;
            }

            if (GameController.instance != null) {
                GameController.instance._song.isTutorialSong = false;
            }
        }
    }

    private void ConfigureSongSettings(Song song) {
        if (song != null) {
            if (!song.oneTimePlay && song.storage == SONG_STORAGE.OFFICIAL && song.savedType == SONGTYPE.LOCK) {
                Configuration.instance.SetOpenSong(song, song.diamonds, true, SongUnlockType.currency);
            }

            if (song.storage != SONG_STORAGE.SOCIAL) {
                Configuration.SetCurrentSong(song);
            }

            Configuration.instance.AddLastestPlayedSong(song);
        }

        ResumeScreenSleep();
    }

    private void FinalizeAndLoadScene(Song song, bool isFromHome) {
        if (isFromHome) {
            LastPlaySong = song;
        }

        AnalyticHelper.LogSong(SONG_STATUS.song_loaded, song, location: _location);

        string scene = ThemeManager.GetThemeSceneName(song);
        if (RemoteConfigBase.instance.Theme_IsEnableSelectTheme) {
            int cachedThemeId = song.GetSelectedTheme();
            var themeString = Util.BuildString(null, "Theme", cachedThemeId);
            if (Application.CanStreamedLevelBeLoaded(themeString)) {
                scene = themeString;
            }
        }

        SceneManager.LoadScene(scene);
    }

    private async UniTaskVoid UpdateBPM(Song song, Action<float> percentProcessed,
                                        CancellationToken cancellationToken = default) {
        try {
            NoteLocalController noteLocalController = NoteLocalController.Load();
            if (song != null && noteLocalController != null) {
                string pathLocal;
#if UNITY_EDITOR
                pathLocal = song.GetLocalMp3Path();
#else
                pathLocal = string.IsNullOrEmpty(song.mp3LocalPath) ? song.GetLocalMp3Path() : song.mp3LocalPath;
#endif
                //pathLocal = $"file://{pathLocal}";

                await noteLocalController.GetBPMData(pathLocal, percentProcessed, dataBpm => {
                    if (dataBpm != null) {
                        song.UpdateBPMLocal(dataBpm[0], dataBpm[1]);
                    }
                }, Debug.LogError).ToUniTask(this);
            }
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    /// <summary>
    /// Fade in/out black screen
    /// </summary>
    /// <param name="fadeIn">True if fade in. False if fade out</param>
    /// <param name="time">Time of fade</param>
    /// <returns></returns>
    private async UniTask IEFadeBlackScreen(bool fadeIn, float time = 0.5f,
                                            CancellationToken cancellationToken = default) {
        float t = 0f;
        while (t < time) {
            t += _tSpeed;
            _tColor.a = curve.Evaluate((fadeIn ? t : (time - t)) / time);
            img.color = _tColor;
            await UniTask.Yield(cancellationToken);
        }
    }

    private void Note(float pecent) {
        return;
    }

    private void AbortDownload() {
        // Cancel all UniTask operations
        if (_coroutineDownload != null) {
            _coroutineDownload.Cancel();
            _coroutineDownload.Dispose();
            _coroutineDownload = null;
        }

        SetActiveCanvas(false);
        _isAnimated = false;

        if (HomeManager.instance == null) {
            Util.GoHome();
        } else {
            HomeManager.instance.ShowHome();
        }
    }

    private void DownloadFailed(Song song, string errorMsg, bool fireException = true) {
        if (fireException && Utils.IsInternetReachable && !errorMsg.Contains("Download request is timeout")) {
            bool fire = true;
            if (string.IsNullOrEmpty(song.midiLocalPath) && errorMsg.Equals("Midi file is empty note count")) {
                //local song
                float length = SuperpoweredSDK.instance.musicLength;
                if (length < 3) {
                    // mp3 length is too short
                    fire = false;
                } else {
                    errorMsg += $". Length mp3 {(int) length}";
                }
            }

            if (fire) {
                CustomException.SetKey(FirebaseKey.SongData, $"{song.name} => {song.acm_id_v3}");
                CustomException.Fire("[DownloadFailed]", $"DownloadFailed => {errorMsg}");
            }
        }

        ResumeScreenSleep();
        OnLoadingScene?.Invoke(false, false);
        _isAnimated = false;

        if (_tipsV2 == null) {
            SetActiveCanvas(false);
            if (!SongManager.instance.IsLocalSong(song.path)) {
                if (Inwave.Utils.IsAndroid()) {
                    NoInternetPopup.Show();
                } else {
                    Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("DOWNLOAD_FAILED"), 1);
                }
            } else {
                Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("CANT_LOAD_SONG") + ": " + song.path,
                    1);
            }

            if (Tips.instance != null) {
                Tips.instance.Close();
            }
        } else {
            _tipsV2.OnDownloadFailed(errorMsg);
        }
    }

    private void UpdateProgress(Song song) {
        _percentLoading = DownloadManager.instanceSafe.GetProgress(song);
        if (_percentLoading > loadingProgress.fillAmount) {
            _startLoadTime = Time.time;

            loadingProgress.fillAmount = _percentLoading;
            percent.text = Math.Round(100 * _percentLoading) + "%";
            if (_tipsV2 != null) {
                _tipsV2.SetPercent(_percentLoading);
            }
        }
    }

    private void PreventScreenSleep() {
        if (RemoteConfig.instance.preventSleepOnDownload) {
            Screen.sleepTimeout = SleepTimeout.NeverSleep;
        }
    }

    private void ResumeScreenSleep() {
        if (RemoteConfig.instance.preventSleepOnDownload) {
            Screen.sleepTimeout = SleepTimeout.SystemSetting;
        }
    }

    /// <summary>
    /// Fade out with only black screen
    /// </summary>
    /// <param name="animationTime"></param>
    public void ToggleOverlay(float animationTime) {
        IEToggleOverlay(animationTime, this.GetCancellationTokenOnDestroy()).Forget();
    }

    private async UniTask IEToggleOverlay(float animationTime, CancellationToken cancellationToken = default) {
        SetActiveCanvas(true);
        text.transform.gameObject.SetActive(false);
        SetActiveLoadingBar(false);
        await IEFadeBlackScreen(false, animationTime, cancellationToken);

        SetActiveCanvas(false);
    }

    public void RetryDownload() {
        if (_loadingSong == null) {
            return;
        }

        IERetryDownload().Forget();
    }

    private async UniTaskVoid IERetryDownload() {
        try {
            CancelDownload();
            _coroutineDownload = new CancellationTokenSource();
            await FadeOutSong(_loadingSong, isFromHome: _isFromHome, setScreenView: false, _coroutineDownload.Token);
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    public void CancelDownload() {
        if (_coroutineDownload != null) {
            _coroutineDownload.Cancel();
            _coroutineDownload.Dispose();
            _coroutineDownload = null;
        }

        _isAnimated = false;
        _percentLoading = 0f;
        loadingProgress.fillAmount = 0f;
        if (DownloadManager.instanceSafe.IsDownloading()) {
            DownloadManager.instanceSafe.StopDownload();
        }
    }

    public void FadeInSelectTheme() {
        if (_selectThemeTransition) {
            _selectThemeTransition.SetActive(true);
            _animator.Play(_fadeIn);
        }
    }

    public void FadeOutSelectTheme() {
        if (_selectThemeTransition) {
            _animator.Play(_fadeOut);
            DOVirtual.DelayedCall(1f, () => _selectThemeTransition.SetActive(false));
        }
    }

    private void OnDestroy() {
        // Cancel any running UniTask operations
        if (_coroutineDownload != null) {
            _coroutineDownload.Cancel();
            _coroutineDownload.Dispose();
            _coroutineDownload = null;
        }

        // Note: GetCancellationTokenOnDestroy() automatically cancels when GameObject is destroyed
        // No manual cancellation needed - UniTask framework handles this

        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }
}