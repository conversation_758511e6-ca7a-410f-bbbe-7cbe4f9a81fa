using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using CielaSpike;
using Inwave;
using Sirenix.OdinInspector;
using Random = UnityEngine.Random;
using Task = System.Threading.Tasks.Task;

/// <summary>
/// trungvt
/// </summary>
public class BallManager : Singleton<BallManager> {
    #region Fields

    //static

    #region Static fields

    public const int DefaultBall = 0;

    #region Humand

    public const int Boy     = 23;
    public const int Girl    = 24;
    public const int Santa   = 25;
    public const int Pumpkin = 26;

    public const int Penguin       = 53;
    public const int GirlJapan     = 54;
    public const int GirlWednesday = 70;
    public const int Hiphop_1      = 76;
    public const int Hiphop_2      = 77;
    public const int Hiphop_3      = 78;
    public const int Hiphop_4      = 79;
    public const int Hiphop_5      = 80;

    public const int CatRolling   = 71;
    public const int DogRolling   = 72;
    public const int HorseRolling = 73;
    public const int SheepRolling = 74;
    public const int TigerRolling = 75;

    //HLW2023
    public const int HLW1 = 81;
    public const int HLW2 = 82;
    public const int HLW3 = 83;
    public const int HLW4 = 84;
    public const int HLW5 = 85;

    //Rolling Character v2
    public const int YellowDog    = 86;
    public const int Monster      = 87;
    public const int CatHat       = 88;
    public const int CatHeadphone = 89;
    public const int Rapid        = 90;

    public const int SnowBall     = 91;
    public const int Deer         = 92;
    public const int Santa2       = 93;
    public const int Elsa         = 94;
    public const int Poppy        = 95;
    public const int Mewsday      = 96;
    public const int SquidBall    = 97;
    public const int Rocker       = 98;
    public const int Panda        = 99;
    public const int LightYear    = 100;
    public const int Pirate       = 101;
    public const int IronBall     = 102;
    public const int HarryPotBall = 103;
    public const int JokeBall     = 104;
    public const int Angel        = 105;
    public const int Sharky       = 106;
    public const int Evil         = 107;
    public const int Dragon       = 108;
    public const int Genie        = 109;
    public const int KingFur      = 110;
    public const int EDM          = 111;
    public const int RoboCat      = 112;
    public const int DiscoQueen   = 113;
    public const int Fluffy       = 114;
    public const int HopInTheBox  = 115;
    public const int Fankenstein  = 116;
    public const int StarBounce   = 117;
    public const int Deadverin    = 118;
    public const int MaracasHop   = 119;
    public const int Chickie      = 120;
    public const int Ballruto     = 121;
    public const int DJ_THop      = 122;
    public const int SnowHop      = 123;
    public const int TinHopper    = 124;
    public const int ElfMas       = 125;
    public const int MisterClaus  = 126;
    public const int MadameClaus  = 127;
    public const int Valentile    = 128;
    public const int CutePit      = 129;
    public const int SimpBall     = 130;
    public const int SweetBunny   = 131;
    public const int Amari        = 132;
    public const int SquidDoll    = 133;
    public const int Player456    = 134;
    public const int EasterBunny  = 135;
    public const int Ballphin     = 136;
    public const int SurfMuff     = 137;
    public const int BiggyPiggy   = 138;
    public const int FinePine     = 139;

    public static readonly List<int> itemsHuman = new() {
        Boy, Girl, Santa,
        Penguin, //TH-628
        GirlJapan, //54
        CatRolling, DogRolling, HorseRolling, SheepRolling, TigerRolling, //71-75
        Hiphop_1, Hiphop_2, Hiphop_3, Hiphop_4, Hiphop_5, //76-80
        HLW1, HLW2, HLW3, //81-83
        YellowDog, Monster, CatHat, CatHeadphone, Rapid, //86-90
        SnowBall, Deer, Santa2, Elsa, //91-94
        Poppy, Rocker, Panda, SquidBall, Mewsday,
        Pirate, HarryPotBall, LightYear, Genie, IronBall, Angel, Evil, JokeBall, Sharky, Dragon,
        KingFur, EDM, RoboCat, DiscoQueen, Fluffy, HopInTheBox, Fankenstein, Deadverin, MaracasHop,
        TinHopper,
        SnowHop, StarBounce, MisterClaus, MadameClaus, Chickie,
        Ballruto, DJ_THop, ElfMas, Amari, CutePit, Valentile, SimpBall, SquidDoll, Player456, SweetBunny,
        EasterBunny, Ballphin, SurfMuff, BiggyPiggy, FinePine
    };

    #endregion

    // list ball có anim idle shop riêng
    public static readonly List<int> haveShopAnimList = new() {
        HopInTheBox,
        Fankenstein,
        Deadverin,
        MaracasHop,
        Chickie,
        ElfMas,
        TinHopper,
        Amari,
        MisterClaus,
        CutePit,
        SimpBall,
        SquidDoll,
        Player456,
        SweetBunny,
        EasterBunny,
        BiggyPiggy,
        Ballphin,
        SurfMuff,
        FinePine,
    };

    //public static List<int> ignoreTheme = new List<int>() { ThemeConfig.ThemeIdHalloween };
    public static readonly List<int> itemSpecialBall = new List<int>() { Pumpkin };

    public static readonly List<int> ballsPlanet = new List<int>() { 66, 67, 68 };
    public static readonly int       ballSaturn  = 67;

    #endregion

    [SerializeField] private TextAsset      defaultBallConfig;
    [SerializeField] private CharactersData charactersData;

    private                   List<BallConfig>               _ballConfigs;
    [ShowInInspector] private Dictionary<int, BallConfig>    _indexedBallConfigs;
    [ShowInInspector] private Dictionary<int, CharacterData> _indexedOriginalCharacterDatas;

    public List<string> DefaultUnlockedBalls { get; private set; }

    private RemoteConfig remoteconfig => RemoteConfigBase.instance;

    public static bool Inited = false;
    public static event Action OnInitedDone;

    private const string Stored_ActiveBalls = "Stored_ActiveBalls";
    public int ConfigCount => _ballConfigs?.Count ?? 0;
    private string _activeBallsStringList;
    public bool IsThereNewBalls { get; private set; }

    private const byte DefaultAdsPrice     = 1;
    private const byte DefaultDiamondPrice = 100;

    #endregion

    #region Unity Methods

    private IEnumerator Start() {
        yield return null;

        Util.WaitRemoteConfigDone(LoadRemoteBallConfig, true);

        if (Configuration.instance.isTutorial) {
            while (_ballConfigs == null || _ballConfigs.Count == 0) {
                yield return null;
            }

            Configuration.SetOpenBall(remoteconfig.Default_BallID, -1, location: null, false);
        }
    }

    #endregion

    #region Load data

    private async void LoadRemoteBallConfig() {
        try {
            string urlBallConfig = remoteconfig.urlBallConfig;
            Coroutine ieGetDefaultConfig =
                this.StartCoroutineAsync(IENProcessDataBallConfig(defaultBallConfig.text, null));

            string dataCsv = null;
            try {
                if (remoteconfig.enableRemoteSync || remoteconfig.enableLocalSync) {
                    if (!string.IsNullOrEmpty(urlBallConfig)) {
                        dataCsv = await DownloadManager.DownloadCsv(urlBallConfig);
                    }
                }
            } catch (Exception e) {
                CustomException.Fire("[LoadRemoteBallConfig]", " ERR: " + e.Message);
            }

            if (!string.IsNullOrEmpty(dataCsv)) {
                if (ieGetDefaultConfig != null) {
                    StopCoroutine(ieGetDefaultConfig);
                }

                this.StartCoroutineAsync(IENProcessDataBallConfig(dataCsv, urlBallConfig));
            }
        } catch (Exception e) {
            CustomException.Fire("[LoadRemoteBallConfig]", " ERR: " + e.Message);
        }
    }

    private IEnumerator IENProcessDataBallConfig(string dataCsv, string urlBallConfig) {
        yield return Ninja.JumpBack;

        List<BallConfig> configs = CSVReader.ProcessDataCSV<BallConfig>(dataCsv, urlBallConfig);

        yield return Ninja.JumpToUnity;

        if (configs is { Count: > 0 }) {
            _ballConfigs = configs;

            // indexing ball configs theo ball ID
            _indexedBallConfigs ??= new Dictionary<int, BallConfig>();
            foreach (BallConfig ballConfig in _ballConfigs) {
                _indexedBallConfigs[ballConfig.id] = ballConfig;
            }

            // indexing character data theo ball ID
            _indexedOriginalCharacterDatas ??= new Dictionary<int, CharacterData>();
            foreach (CharacterData data in charactersData.listCharacter) {
                _indexedOriginalCharacterDatas.TryAdd(data.id, data);
            }

            UpdateBallConfig();

            if (StarsJourneyManager.isEnable) {
                while (!StarsJourneyManager.instanceSafe.isReady) {
                    yield return null;
                }

                UpdateStarjourneyBalls();
            }
        }

        InitCompleted();
    }

    #endregion

    #region Methods

    private void InitCompleted() {
        if (Inited) {
            return;
        }

        Inited = true;
        OnInitedDone?.Invoke();
    }

    public UnlockType GetUnlockType(int idBall) {
        BallConfig ballConfig = GetBallConfig(idBall);
        if (ballConfig == null) {
            return UnlockType.Open;
        } else {
            return ballConfig.unlockType;
        }
    }

    public bool ExistBallConfig(int idBall) {
        if (_ballConfigs.IsNullOrEmpty()) {
            Logger.LogError("[GetBallConfig] ballConfigs is null or empty");
            return false;
        }

        // sử dụng indexing để giảm chi phí do LinQ (Any)
        if (_indexedBallConfigs != null && _indexedBallConfigs.ContainsKey(idBall)) {
            return true;
        }

        if (_ballConfigs.Any(ballConfig => ballConfig.id == idBall)) {
            return true;
        }

        Logger.LogError($"[GetBallConfig] cannot get ballConfigs of {idBall}");
        return false;
    }

    public BallConfig GetBallConfig(int idBall) {
        if (_ballConfigs.IsNullOrEmpty()) {
            Logger.LogError("[GetBallConfig] ballConfigs is null or empty");
            return null;
        }

        if (_indexedBallConfigs != null) {
            if (_indexedBallConfigs.TryGetValue(idBall, out var config)) {
                return config;
            }
        } else {
            foreach (BallConfig ballConfig in _ballConfigs) {
                if (ballConfig.id == idBall) {
                    return ballConfig;
                }
            }
        }

        Logger.LogError($"[GetBallConfig] cannot get ballConfigs of {idBall}");
        return null;
    }

    public List<BallConfig> GetBallConfig() {
        if (_ballConfigs.IsNullOrEmpty()) {
            Logger.LogError("[GetBallConfig] ballConfigs.Count == 0");
            return null;
        }

        return _ballConfigs;
    }

    private void UpdateBallConfig() {
        if (_ballConfigs == null || _ballConfigs.Count == 0) {
            return;
        }

        DefaultUnlockedBalls = new List<string>();

        for (int index = 0; index < _ballConfigs.Count; index++) {
            BallConfig ballConfig = _ballConfigs[index];

            if (!IsSupportBall(ballConfig.id)) {
                _ballConfigs.RemoveAt(index);
                index--;
                continue;
            }

            if (Configuration.IsOpenBall(ballConfig.id)) {
                ballConfig.unlockType = UnlockType.Open;
            }

            switch (ballConfig.unlockType) {
                case UnlockType.VipMission:
                    if (!remoteconfig.WeeklyMission_IsEnable) {
                        ballConfig.unlockType = UnlockType.Vip;
                    }

                    break;

                case UnlockType.Open:
                    DefaultUnlockedBalls.Add(ballConfig.id.ToString());
                    break;

                case UnlockType.Lock:
                    if (ballConfig.diamond != 0) {
                        if (ballConfig.ads != 0) {
                            //nothing
                        } else {
                            ballConfig.unlockType = UnlockType.Diamond;
                        }
                    } else {
                        if (ballConfig.ads != 0) {
                            ballConfig.unlockType = UnlockType.Video;
                        } else {
                            //force use ads to unlock
                            ballConfig.ads = DefaultAdsPrice;
                            ballConfig.unlockType = UnlockType.Video;
                            Logger.LogWarning(
                                $"[BallConfig-newVersion] Ball {ballConfig.name} unlockType {ballConfig.unlockType} but the ads = 0 && diamond = 0.\n Set default value ads = 1");
                        }
                    }

                    break;

                case UnlockType.Video:
                    if (ballConfig.AdsPrice.Equals(0)) {
                        ballConfig.ads = DefaultAdsPrice;
                        Logger.LogWarning(
                            $"[BallConfig-oldVersion] Ball {ballConfig.name} unlockType {ballConfig.unlockType} but the price = 0.\n Set default value ads = 1");
                    }

                    break;

                case UnlockType.Diamond:
                    if (ballConfig.DiamondPrice.Equals(0)) {
                        ballConfig.diamond = DefaultDiamondPrice;
                        Logger.LogWarning(
                            $"[BallConfig-oldVersion] Ball {ballConfig.name} unlockType {ballConfig.unlockType} but the price = 0.\n Set default value diamond = {DefaultDiamondPrice}");
                    }

                    break;
            }
        }

        // gán nhãn hidden cho các ball đặc biệt mà user chưa sỡ hữu
        UpdateHiddenBalls();

        // xử lý các ball đặc biệt theo feature
        UpdateStarterPackBall();
        UpdateSeasonalPackBall();
    }

    #endregion

    public List<BallConfig> GetBallConfig(UnlockType unlockType) {
        List<BallConfig> result = new List<BallConfig>();

        if (_ballConfigs.IsNullOrEmpty()) {
            return result;
        }

        // result.AddRange(ballConfigs.Where(ballConfig => ballConfig.unlockType == unlockType));

        // sử dụng foreach thay vì LinQ (Where)
        foreach (BallConfig ballConfig in _ballConfigs) {
            if (ballConfig.unlockType == unlockType) {
                result.Add(ballConfig);
            }
        }

        return result;
    }

    public List<BallConfig> GetBallConfig(List<UnlockType> unlockTypes) {
        List<BallConfig> listBallConfigs = new();

        if (_ballConfigs == null || _ballConfigs.Count == 0)
            return listBallConfigs;

        if (unlockTypes == null || unlockTypes.Count == 0)
            return listBallConfigs;

        foreach (BallConfig ballConfig in _ballConfigs) {
            if (unlockTypes.Contains(ballConfig.unlockType)) {
                listBallConfigs.Add(ballConfig);
            }
        }

        return listBallConfigs;
    }

    public static int GetShowAdOfBall(int idBall) {
        return PlayerPrefs.GetInt("GetShowAdOfBall_" + idBall, 0);
    }

    public static void SetShowAdOfBall(int idBall, int count) {
        PlayerPrefs.SetInt("GetShowAdOfBall_" + idBall, count);
    }

    public List<int> GetActiveBalls() {
        if (_ballConfigs != null) {
            return GetActiveBallConfigs().Select(ballConfig => ballConfig.id).ToList();
        }

        Logger.EditorLogError("Call too soon -> ball config is Null");
        return null;
    }

    public List<BallConfig> GetActiveBallConfigs() {
        if (_ballConfigs != null) {
            return _ballConfigs.Where(ballConfig => ballConfig is { order: >= 0 }).OrderBy(config => config.order)
                .ToList();
        }

        Logger.EditorLogError("Call too soon -> ball config is Null");
        return null;
    }

    public bool CheckHaveNewBalls() {
        IsThereNewBalls = false;
        if (_ballConfigs == null) {
            Logger.EditorLogError("Call check new balls too soon -> ball config is Null");
            return false;
        }

        try {
            List<BallConfig> activeBalls = GetActiveBallConfigs();

            if (activeBalls.Count == 0) {
                return false;
            }

            StringBuilder builder = new StringBuilder();
            foreach (var ball in activeBalls) {
                builder.Append(ball.id);
                builder.Append(';');
            }

            _activeBallsStringList = builder.ToString();

            string storedActiveBallsId = PlayerPrefs.GetString(Stored_ActiveBalls);
            if (!storedActiveBallsId.Equals(_activeBallsStringList, StringComparison.Ordinal)) {
                if (storedActiveBallsId.Length > _activeBallsStringList.Length) {
                    // khi bỏ bớt ball từ remote config
                    StoredListNewBalls();
                    return false;
                }

                IsThereNewBalls = true;
                return true;
            }
        } catch (Exception e) {
            CustomException.Fire("[Check have new balls]", "Error when process with ball configs => " + e.Message);
        }

        return false;
    }

    public void StoredListNewBalls() {
        PlayerPrefs.SetString(Stored_ActiveBalls, _activeBallsStringList);
    }

    public List<int> GetActiveVideoLockedBalls() {
        if (_ballConfigs == null) {
            Logger.EditorLogError("Call too soon -> ball config is Null");
            return null;
        }

        IOrderedEnumerable<BallConfig> data = _ballConfigs.Where(ballConfig =>
            (ballConfig is { order: >= 0 } && !Configuration.IsOpenBall(ballConfig.id) && ballConfig.CanUnlockByAds() &&
             !itemsHuman.Contains(ballConfig.id) // not character
            )).OrderBy(config => config.order);
        return data.Select(ballConfig => ballConfig.id).ToList();
    }

    #region Reward ball & charactor

    public static int GetRewardBall(bool isRandom = true) {
        List<int> balls = new List<int>();
        HashSet<int> ignoreReward = GetIgnoreReward();
        List<int> activeBalls = instance == null ? null : instance.GetActiveBalls();

        if (activeBalls != null) {
            foreach (int idBall in activeBalls) {
                if (Configuration.IsOpenBall(idBall) || ignoreReward.Contains(idBall)) {
                    continue;
                }

                balls.Add(idBall);
            }
        }

        if (balls.Count <= 0) {
            return -1;
        }

        if (isRandom) {
            int random = Random.Range(0, balls.Count);
            return balls[random];
        }

        return balls[0];
    }

    public static int GetRewardBallFromPool(bool isRandom = true, BallRewardSource source = BallRewardSource.None) {
        List<int> balls = new List<int>();
        int[] activeBalls = null;
        switch (source) {
            case BallRewardSource.AdBreak:
                activeBalls = RemoteConfigBase.instance.AdBreak_Rewards_Ball_Pool;
                break;

            case BallRewardSource.MysteryBox:
                activeBalls = RemoteConfigBase.instance.mysterybox_RewardBall_Pool;
                break;

            case BallRewardSource.SevenDayMission:
                activeBalls = RemoteConfigBase.instance.SevenDayMission_RewardBall_Pool;
                break;

            case BallRewardSource.Mission:
                activeBalls = RemoteConfigBase.instance.Hybrid_Mission_RewardBall_Pool;
                break;

            case BallRewardSource.NotificationBox:
                activeBalls = RemoteConfigBase.instance.NotificationBox_RewardBall_Pool;
                break;
        }

        if (activeBalls != null) {
            HashSet<int> ignoreReward = GetIgnoreReward();
            foreach (int idBall in activeBalls) {
                if (Configuration.IsOpenBall(idBall) || ignoreReward.Contains(idBall)) {
                    continue;
                }

                balls.Add(idBall);
            }
        }

        if (balls.IsNullOrEmpty()) {
            return GetRewardBall(isRandom);
        }

        if (isRandom) {
            int random = Random.Range(0, balls.Count);
            return balls[random];
        }

        return balls[0];
    }

    private static HashSet<int> GetIgnoreReward() {
        HashSet<int> ignoreBalls = new();
        List<UnlockType> ignoreType = new() {
            UnlockType.Event,
            UnlockType.StarterPack,
            UnlockType.FreeChallenge,
            UnlockType.Progression,
            UnlockType.SeasonalPack,
            UnlockType.Hidden,
            UnlockType.StarJourney,
        };

        if (SubscriptionController.IsEnableSubscription()) {
            ignoreType.Add(UnlockType.Vip);
            ignoreType.Add(UnlockType.VipMission);
        }

        List<BallConfig> ignoreConfig = instance.GetBallConfig(ignoreType);
        foreach (BallConfig item in ignoreConfig) {
            ignoreBalls.Add(item.id);
        }

        return ignoreBalls;
    }

    #endregion

    public Sprite GetIcon(int id) {
        if (_indexedOriginalCharacterDatas != null) {
            if (_indexedOriginalCharacterDatas.TryGetValue(id, out var data)) {
                return data.sprIcon;
            }
        } else {
            foreach (CharacterData dataBall in charactersData.listCharacter) {
                if (dataBall.id == id) {
                    return dataBall.sprIcon;
                }
            }
        }

        Logger.LogError($"[GetIconBall] cannot find icon of: " + id);
        return null;
    }
    
    
    /// <summary>
    /// Instead of using the old method that could return null, where comparing to 'null' is expensive
    /// </summary>
    /// <param name="id"></param>
    /// <param name="icon">output</param>
    /// <returns>Return true if the ball icon is successfully retrieved; otherwise, return false</returns>
    public bool TryGetIcon(int id, out Sprite icon) {
        if (_indexedOriginalCharacterDatas != null) {
            if (_indexedOriginalCharacterDatas.TryGetValue(id, out var data)) {
                icon = data.sprIcon;
                return true;
            }
        } else {
            foreach (CharacterData dataBall in charactersData.listCharacter) {
                if (dataBall.id != id) continue;
                icon = dataBall.sprIcon;
                return true;
            }
        }
        
        icon = null;
        return false;
    }

    public bool IsRollingBall(int id) {
        return id is Santa or CatRolling or DogRolling or HorseRolling or SheepRolling or TigerRolling;
    }

    public bool IsRollingBallV2(int id) {
        return id is YellowDog or Monster or CatHat or CatHeadphone or Rapid;
    }

    public bool IsHiphopBall(int id) {
        return id is Hiphop_1 or Hiphop_2 or Hiphop_3 or Hiphop_4 or Hiphop_5;
    }

    public bool IsXmasBall(int id) {
        return id is SnowBall or Deer or Santa2 or Elsa;
    }

    public static bool ForceNotRotateOnLongTile(int id) {
        return id is LightYear or HarryPotBall or IronBall or KingFur;
    }

    public bool IsHalloweenBall(int id) {
        return id is HLW4 or HLW5;
    }

    public bool IsHalloweenCharacter(int id) {
        return id is HLW1 or HLW2 or HLW3;
    }

    public List<CharacterData> GetAllBallsData() {
        return charactersData.Clone();
    }

    public bool IsInit() {
        return !_ballConfigs.IsNullOrEmpty();
    }

    public bool IsHumanBehaviour(int idBall) {
        return IsHumanBall(idBall) || IsSpecialBall(idBall);
    }

    public bool IsHumanBall(int idBall) {
        return itemsHuman.Contains(idBall);
    }

    public bool IsSpecialBall(int idBall) {
        return itemSpecialBall.Contains(idBall);
    }

    private bool IsSupportBall(int id) {
        if (_indexedOriginalCharacterDatas != null) {
            if (_indexedOriginalCharacterDatas.TryGetValue(id, out var data)) {
                return data.sprIcon != null;
            }

            return false;
        }

        return (from ball in charactersData.listCharacter where ball.id == id select ball.sprIcon != null)
            .FirstOrDefault();
    }

    public CharacterData GetCharacterData(int idBall) {
        if (_indexedOriginalCharacterDatas != null) {
            if (_indexedOriginalCharacterDatas.TryGetValue(idBall, out var data)) {
                return data;
            }
        } else {
            foreach (CharacterData data in charactersData.listCharacter) {
                if (data.id == idBall) {
                    return data;
                }
            }
        }

        return null;
    }

    /// <summary>
    /// Convert ball unlockType sang dạng Hidden để đánh dấu những ball đặc biệt có thể ẩn đi khi chưa sỡ hữu
    /// 
    /// [!] gọi trước khi convert unlockType tương ứng với các tính năng đặc biệt như Seasonal pack (hiển thị dạng đặc biệt)
    /// nếu các tính năng đặc biệt trên tắt thì các ball đặc biệt sẽ không hiển thị trong shop nữa
    /// </summary>
    private void UpdateHiddenBalls() {
        if (_indexedBallConfigs == null) {
            return;
        }

        var listHiddenBalls = (RemoteConfigBase.instance.ListHiddenBalls).StringToList();
        foreach (string ballID in listHiddenBalls) {
            if (int.TryParse(ballID, out int id) && !Configuration.IsOpenBall(id) &&
                _indexedBallConfigs.TryGetValue(id, out var ballConfig)) {
                ballConfig.unlockType = UnlockType.Hidden;
            }
        }
    }

    private void UpdateStarjourneyBalls() {
        if (_indexedBallConfigs == null) {
            return;
        }

        var starjourneyBalls = StarsJourneyManager.instanceSafe.rewardBalls;
        foreach (var ballID in starjourneyBalls.Keys) {
            if (!Configuration.IsOpenBall(ballID) && _indexedBallConfigs.TryGetValue(ballID, out var ballConfig)) {
                ballConfig.unlockType = UnlockType.StarJourney;
            }
        }
    }

    private void UpdateStarterPackBall() {
        if (_indexedBallConfigs == null) {
            return;
        }

        if (remoteconfig.Economy_IsEnable == false)
            return;

        if (_ballConfigs == null || _ballConfigs.Count == 0) {
            return;
        }

        var config = Configuration.instance.GetStarterPackConfig();
        if (config == null || config.Skins == null)
            return;

        var rewardSkin = config.Skins;

        int count = rewardSkin.Length;
        for (int i = 0; i < count; i++) {
            int id = rewardSkin[i];
            if (!Configuration.IsOpenBall(id)) {
                if (_indexedBallConfigs.TryGetValue(id, out var ballConfig)) {
                    ballConfig.unlockType = UnlockType.StarterPack;
                }
            }
        }
    }

    public void UpdateSeasonalPackBall() {
        if (_indexedBallConfigs == null) {
            return;
        }

        if (!SeasonalPackManager.enableFeature || !SeasonalPackManager.isInstanced) {
            return;
        }

        var overrideUnlockType = SubscriptionController.IsSubscriptionVip() ? UnlockType.Open : UnlockType.SeasonalPack;

        foreach (int id in SeasonalPackManager.Config.BallRewards) {
            if (!Configuration.IsOpenBall(id)) {
                if (_indexedBallConfigs.TryGetValue(id, out var ballConfig)) {
                    ballConfig.unlockType = overrideUnlockType;
                }
            }
        }
    }

    public int GetReplaceGiftBallReward(List<int> ignoreBall) {
        HashSet<int> ignoreReward = GetIgnoreReward();
        foreach (BallConfig ballConfig in _ballConfigs) {
            if (Configuration.IsOpenBall(ballConfig.id)) {
                continue;
            }

            if (ignoreBall.Contains(ballConfig.id)) {
                continue;
            }

            if (ignoreReward.Contains(ballConfig.id)) {
                continue;
            }

            return ballConfig.id;
        }

        return -1;
    }

    public bool IsPlanetBall(int ballId) {
        if (ballsPlanet.IsNullOrEmpty()) {
            return false;
        }

        return ballsPlanet.Contains(ballId);
    }

    public static int GetDefaultShopIdleAnimState(int ballId) {
        return haveShopAnimList.Contains(ballId) ? CharacterAnimator.IdleShop : CharacterAnimator.StartAnim;
    }

    public void SetDefaultBallConfig(TextAsset text) {
        defaultBallConfig = text;
    }

    public string GetBallName(int ballId) {
        var ballConfig = GetBallConfig(ballId);
        if (ballConfig != null) {
            return ballConfig.name;
        } else {
            return $"Undefined_{ballId}";
        }
    }

    public (int, int) CountCurrentOpenAndMaxNumberSkins() {
        int count = 0;
        int maxCount = 0;
        if (_ballConfigs != null) {
            BallConfig ballConfig;
            for (int i = 0, total = _ballConfigs.Count; i < total; i++) {
                ballConfig = _ballConfigs[i];
                maxCount++;
                if (ballConfig != null && Configuration.IsOpenBall(ballConfig.id)) {
                    count++;
                }
            }
        }

        return (count, maxCount);
    }

    public static bool CanEarnBall(int idBall) {
        if (!isInstanced)
            return false;

        if (instance == null) {
            return false;
        }

        var config = instance.GetBallConfig(idBall);
        if (config == null)
            return false;

        if (Configuration.IsEarnBall(idBall)) {
            return false;
        }
        
        return true;
    }
}