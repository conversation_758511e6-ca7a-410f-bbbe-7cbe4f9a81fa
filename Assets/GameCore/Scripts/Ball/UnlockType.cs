using System;

/// <summary>
/// trungvt
/// </summary>
[Serializable]
public enum UnlockType : byte {
    Open           = 0,
    Vip            = 1,
    VipMission     = 2,
    Video          = 3,
    Diamond        = 4,
    Lock           = 5, //diamond + ads
    Event          = 6, // Nhận được thông qua sự kiện
    StarterPack    = 7, // Nhận được thông qua gói starter pack
    Progression    = 8, // nhận được thông qua hệ thống user progresion
    FreeChallenge  = 9, //TH-3057: challenge for old user
    SeasonalPack   = 10,
    Hidden         = 11, //TH-3876
    Star           = 12, //TH-4474: required a number of stars to unlock (use in Theme selection)
    StarJourney    = 13, // themes or balls unlock from Star journey
    MilestoneEvent = 14,
}