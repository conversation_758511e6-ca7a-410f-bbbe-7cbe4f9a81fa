#if UNITY_PURCHASING
#if UNITY_ANDROID || UNITY_IPHONE || UNITY_STANDALONE_OSX || UNITY_TVOS
// WARNING: Do not modify! Generated file.

namespace UnityEngine.Purchasing.Security {
    public class GooglePlayTangle
    {
        private static byte[] data = System.Convert.FromBase64String("/dXLnCrL3a4dgC0PuXyJpM7HIesz+Z2g3gqhh6rWSLJcLo91QE5Dc6+e0G3vi1dfsg41KIv6TFmchTrvH3+rNiX1LCU36eaXU8h1yT1xfCUXBvVGIJFcJFhOOtpMOZkU7W1/ep/0xOPQjAHbU++tGLwNUGf6RNAaxpDqAXVMZbCPmwY6WmFMn8oS6AhqoGc/aBNMFfa61DdDF28fG59Ez1ozq/FrOCnjDZnHf+ccVCS1lpXCamyWwR3+rJgvRWbYlco26LW+HcQ+jA8sPgMIBySIRoj5Aw8PDwsODTCSouPaDNmYh3RDUxDhfC+igYlmjA8BDj6MDwQMjA8PDtjo39HmBDbYzn0HFCBcpzF6Jnhp1fRqM1fVuDdvq7ovjwHlIQwNDw4P");
        private static int[] order = new int[] { 8,3,4,10,5,5,9,12,13,12,13,11,13,13,14 };
        private static int key = 14;

        public static readonly bool IsPopulated = true;

        public static byte[] Data() {
        	if (IsPopulated == false)
        		return null;
            return Obfuscator.DeObfuscate(data, order, key);
        }
    }
}
#endif
#endif
