using UnityEngine;

/// <summary>
/// CuongVM
/// TH - 1280: [DEV] Cheat tutorial (support for testing)
/// Tạo 1 thao tác ẩn nào đó để bỏ qua tutorial mỗi khi test game: V<PERSON> dụ ấn vào góc trên màn hình onboarding 10 lần sẽ bỏ qua tutorial
/// </summary>
public class CheatTutorial : MonoBehaviour {
    private       Vector2 _mousePosition;
    private       int     _countUserAction   = 0;
    private const int     RequiredUserAction = 10;

    private void LateUpdate() {
        if (Configuration.instance.gameloaded && Input.GetMouseButtonDown(0)) {
            _mousePosition = Input.mousePosition;
            HandleUserInput(_mousePosition);
        }
    }

    private void HandleUserInput(Vector2 position) {
        if (ValidateInputPosition(position)) {
            // accept value
            _countUserAction += Application.isEditor ? 5 : 1;

            Logger.EditorLog($"Cheat Tutorial {_countUserAction.ToString()}/{RequiredUserAction.ToString()}");
            if (_countUserAction >= RequiredUserAction) {
                // Passed password -> fake data
                FakeData();
                // Load scene Home
                Util.GoHome();
            }
        }
    }

    /// <summary>
    /// Kiểm tra user có bấm vào góc trên bên phải không?
    /// </summary>
    /// <param name="position"></param>
    /// <returns></returns>
    private bool ValidateInputPosition(Vector2 position) {
        if (position.x / Inwave.Utils.GetWidth() < 0.9f) {
            return false;
        }

        if (position.y / Inwave.Utils.GetHeight() < 0.9f) {
            return false;
        }

        return true;
    }

    /// <summary>
    /// Thực hiện fake các data cần thiết để bỏ qua inapp survey và tutorial
    /// </summary>
    private void FakeData() {
#if UNITY_EDITOR
        Debug.Log($"Fake data");
#endif

        // reset tutorial
        Configuration.instance.isTutorial = false;
        PlayerPrefs.SetInt(CONFIG_STRING.Intro, 0); // fake complete tutorial

        //reset inapp survey
        PlayerPrefs.SetInt(PlayerPrefsKey.survey_demographic_done, 1); // fake complete inapp survey

    }
}