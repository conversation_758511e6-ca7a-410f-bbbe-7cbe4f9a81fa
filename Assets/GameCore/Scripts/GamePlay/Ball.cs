using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using Facebook.MiniJSON;
using GamePlay.Levels;
using PathCreation;
using Sirenix.OdinInspector;
using TileHop.EventDispatcher;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Pooling;

public partial class Ball : MonoBehaviour {
    private const float POSITION_Y_INSHOP_LOW     = 0.5f;
    private const float POSITION_Y_INSHOP_MEDIUM  = 0.65f;
    private const float POSITION_Y_INSHOP_HIGH    = 0.75f;
    private const float POSITION_Y_INSHOP_EXTREME = 1f;

    #region Variables

    #region Public Variables

    public static Ball b;

    public static Action<int>   OnJump;
    public static Action<float> OnTimeScaleUpdated;
    public static Action        OnPrepareGameCompleted;

    //[SerializeField] private MeshRenderer mr;
    [SerializeField] private SquashAndStretchEffectController stretchController;
    [SerializeField] private Rigidbody                        rb;

    [SerializeField] private BallInstance ballInstance;
    public                   Transform    transCacheRotation;

    [Header("Ball Animation")] [SerializeField]
    private Animator ballAnimator;

    public ParticleSystem ballFXTransition;

    [SerializeField] private GameObject shadowScaler;
    [SerializeField] private float      maxValue    = 1.2f; //highest local position of ball
    [SerializeField] private Vector2    shadowValue = new Vector2(0.2f, 1f);

    #endregion

    #region Public None Serialize Fields

    [HideInInspector] public float     timeScale        = 1;
    [HideInInspector] public float     timeScaleRoot    = 1;
    [HideInInspector] public byte      endlessModeCount = 0;
    [HideInInspector] public bool      isPassRound1     = false;
    [HideInInspector] public float     trustTime        = 0;
    [HideInInspector] public float     forwardSpeed     = 0;
    [HideInInspector] public Transform transCache;
    [HideInInspector] public int       tryBallId;

    [HideInInspector]    public bool isHumanPlayer;
    [Music.ACM.ReadOnly] public bool isPausePlayer = false;
    [HideInInspector]    public bool isJumpLastTile;

    #endregion

    #region Private Variables

    [ShowInInspector] private float _ballSpeed;

    private Vector3 _ballPositionInit;

    private Quaternion _ballRotation;
    private int        _streakPerfectCount = 0;
    private float      _beginTime          = 0;
    private Vector3    _centerPosition;
    private Vector3    _prevPlatformPosition;
    private Platform   _destinationObj;
    private float      _prevAngle;
    private float      _destinationAngle;

    private float    _autoCurrentX = 0;
    private float    _autoTime     = 0;
    private float    _autoCurTime  = 0;
    private float    _jumHeight    = 5;
    private float    _ballSize     = 1;
    private float    _minDistanceWithBlock;
    private Material _matOriginal;

    private Material _matFever;
    private int      _currentTryBallId;
    private bool     isFirstShow = true;

    private int  _ballId         = BallManager.DefaultBall;
    private bool _isRotation     = false;
    private bool _lockedGravity  = false;
    private int  _countForceMove = 0;
    private bool _isFirstShow    = true;
    private bool _isSpecialBall  = false;

    private bool      _isDead = false;
    private BallTrailer ballTrailer => GameItems.instance.ballTrailer;
    private CharacterTrailer charTrailer => GameItems.instance.charTrailer;

    private Dictionary<int, float[]> _ratioJumpHeightByBall;
    private float[]                  _ratioJumpHeight;

    private float jumpTime;
    private bool  isJumpRight;

    private RemoteConfig remoteConfig => RemoteConfig.instance;
    private int _tilePerfectTrigger;
    private int _tileBadTrigger;

    private bool         isForceMove;
    private PlatformType forceMoveType;
    private PipePlatform _currentPipePlatform;

    private bool        isOnRainbow = false;
    private PathCreator currentRainbow;
    private Vector3     startRainbow;
    private Vector3     endRainbow;
    private float       centerX;
    private float DistanceOfRainbow => endRainbow.z - startRainbow.z;

    private GameController _cacheGameController;
    private Platform       _platformSliding;

    private bool      _isFacingScreen;
    public  int       _countToChange = 5;
    private bool      _isAnimating;
    private CancellationTokenSource _rotateRoutine;
    private float     _deltaTime;
    private Vector3   _cachePosition;
    private bool      _isPlanetBall = false;
    private bool      _isHalloweenBall;
    private Tweener   _tweenMove;

    #endregion

    //Ball animation
    public static readonly int _isPlayingHash    = Animator.StringToHash("isPlaying");
    public static readonly int _isPreviewingHash = Animator.StringToHash("isPreviewing");

    public const float JUPITER_BALL_Y_ON_LONG_TILE = 1f;

    #endregion

    public bool CanInteract {
        get => !isPausePlayer;
        set => isPausePlayer = !value;
    }

    public Platform destinationBlock => _destinationObj;

    private Transform _shadowScaler;

    private Transform ShadownScaler {
        get {
            if (_shadowScaler == null) {
                _shadowScaler = shadowScaler.transform;
            }

            return _shadowScaler;
        }
    }

    public static event Action<byte> OnChangeEndlessRound;
    public static event Action<bool> OnChangeStageForceMove;
    public static event Action OnActionJump;
    public static Action OnJumpToEndHuman;

    public GameController gameController {
        get {
            if (_cacheGameController == null) {
                _cacheGameController = SingletonManager.instance.GetSingleton<GameController>();
            }

            return _cacheGameController;
        }
    }

    public float JumpTime => jumpTime;
    public bool isCharacter => isHumanPlayer || _isSpecialBall;

    public int BallId => _ballId;
    public int RandomAnim => ballInstance != null ? ballInstance.RandomAnim : 0;

    public SquashAndStretchEffectController StretchController => stretchController;

    #region Unity functions

    private void Awake() {
        b = this;
        _jumHeight = remoteConfig.JumpHeight + 2 * (remoteConfig.MapSize - 1);
        _ballSize = remoteConfig.BallSize * remoteConfig.MapSize;
        transCache = transform;
        _ballRotation = transCacheRotation.localRotation;
        transCache.localScale = _ballSize * Vector3.one;
    }

    private async void Start() {
        try {
            if (TransitionInOut.isInstanced) {
                await Task.Delay(500);
                var mainCamera = Camera.main;
                if (mainCamera != null && transCache != null) {
                    TransitionInOut.instance.TransitionIn(transCache.position +
                                                          (mainCamera.transform.position - transCache.position) * 0.5f);
                }
            }
        } catch (Exception e) {
            CustomException.Fire("[Ball]", "[Start] => " + e.Message);
        }
    }

    private void OnEnable() {
        GameController.OnChangeSectionType += GameControllerOnOnChangeSectionType;
    }

    private void OnDisable() {
        GameController.OnChangeSectionType -= GameControllerOnOnChangeSectionType;
        TimeoutInvincible();
    }

    public void Update() {
        if (_isPlanetBall) {
            transCacheRotation.Rotate(Vector3.up * (forwardSpeed * 0.1f), Space.World);
        }

        if (gameController.game != GameStatus.DIE && GameController.IsPlaying) {
            UpdateBall();
        } else {
            UpdateShadowAnimation(); //Shadow animation
        }
    }

    private void OnDestroy() {
        if (_tweenMove != null && _tweenMove.IsActive()) {
            _tweenMove.Kill();
        }
        
        // Cancel manual cancellation tokens for controllable operations
        _rotateRoutine?.Cancel();
        _rotateRoutine?.Dispose();
        _rotateRoutine = null;
        
        // Cancel manual cancellation tokens from Ball.Invincible.cs (only rainbow needs manual control)
        _rainbowCancellation?.Cancel();
        _rainbowCancellation?.Dispose();
        _rainbowCancellation = null;
        
        // Note: GetCancellationTokenOnDestroy() automatically cancels when GameObject is destroyed
        // No manual cancellation needed - UniTask framework handles this
    }

    private void LateUpdate() {
        if (gameController.game != GameStatus.DIE && GameController.IsPlaying) {
            if (isInHyperBoostSection) {
                UpdateBallSpecialSectionHyperBoost();
            }

            if (isInZicZacSection) {
                UpdateBallSpecialSectionZicZac();
            }
        }
    }

    private void ForceMoveForPipeTile(Vector3 position) {
        Vector3 target = _currentPipePlatform.GetAutoPosition(_autoCurTime);

        position = Vector3.Lerp(position, target, _autoCurTime / _currentPipePlatform.timeSlide);
        transCache.position = position;
        if (_autoCurTime > _currentPipePlatform.timeSlide) {
            _currentPipePlatform.ForceMove(false);
            isForceMove = false;
            OnChangeStageForceMove?.Invoke(false);
            _autoCurTime = 0f;
            _autoCurrentX = position.x;
        }
    }

    private void ForceMoveForSkewSpiral(Vector3 position) {
        Vector3 bezier = Inwave.Utils.DoPathBezier(_prevPlatformPosition, _centerPosition,
            _destinationObj.GetLandingPosition(), _autoCurTime / _autoTime);
        position.x = bezier.x;
        position.y = bezier.y;
        transCache.position = position;

        float angle = Mathf.Lerp(_prevAngle, _destinationAngle, _autoCurTime / _autoTime);
        transCacheRotation.rotation = Quaternion.Euler(0, 0, angle);

        if (_autoCurTime > _autoTime) {
            // Đây là lúc cần force trigger, tránh trường hợp bóng bay nhanh quá, bay vượt qua cả vòng skew_spiral
            _countForceMove++;
            if (_countForceMove > 3) {
                _destinationObj.ForceTriggerStay();
                _countForceMove = 0;
                Logger.LogWarning($"Force Trigger Platform {_destinationObj.name}");
            }
        }
    }

    #endregion

    #region ball functions

    private void CheckRotate(int ballId) {
        if (ThemeManager.GetPlayingThemeId() == ThemeManager.ThemeWorldCup) {
            _isRotation = true;
        } else if (BallManager.itemsHuman.Contains(ballId) || ballId == BallManager.DefaultBall) {
            _isRotation = false;
        } else {
            _isRotation = true;
        }
    }

    private void LockGravity() {
        _lockedGravity = true;
        rb.useGravity = false;
    }

    private void UnlockGravity() {
        _lockedGravity = false;
        rb.useGravity = true;
    }

    private void SetVelocity(Vector3 velocity) {
        rb.velocity = velocity;
    }

    public float GetBallSize() {
        return _ballSize;
    }

    public void SetEndlessRound(byte round) {
        if (!isPassRound1 && round > 0) {
            isPassRound1 = true;
        }

        if (endlessModeCount != round) {
            endlessModeCount = round;
            OnChangeEndlessRound?.Invoke(round);
        }
    }

    public void Rotate(float deltaMove) {
        if (_isRotation && !_isPlanetBall) {
            if (!remoteConfig.BouncingBall_UseBouncingBall) {
                transCacheRotation.Rotate(
                    Vector3.right * (forwardSpeed * remoteConfig.BallRotateSpeed_Forward) +
                    Vector3.up * (deltaMove * 2 * remoteConfig.BallRotateSpeed_LeftRight), Space.World);
            } else {
                if (onLongTile || remoteConfig.BouncingBall_UseBallRotation || onHitchTile) {
                    transCacheRotation.Rotate(
                        Vector3.right * (forwardSpeed * remoteConfig.BallRotateSpeed_Forward) +
                        Vector3.up * (deltaMove * 2 * remoteConfig.BallRotateSpeed_LeftRight), Space.World);
                }
            }
        }
    }

    public void ShopRotate(float angle) {
        transCacheRotation.Rotate(0, angle, 0);
    }

    public void EnableBall(float z, float positionX) {
        _isDead = false;
        _isInMirrorSection = false;
        OffSpecialSectionHyperBoost();
        _tileBadTrigger = remoteConfig.GetTile_BadTrigger();
        _tilePerfectTrigger = remoteConfig.GetTile_PerfectTrigger();

        if (!gameController.isSensitiveClosing) {
            _ballPositionInit.z = z;
            _ballPositionInit.x = positionX;
            transCache.position = _ballPositionInit;
        }

        stretchController.ResetBouncing();
        transCacheRotation.localRotation = _ballRotation;

        ballInstance.EnableBall(transCache);

        if (gameController.continueCount == 0) {
            if (ChallengeMode.IsActive && GameController.enableEndless) {
                float scale = ChallengeMode.steadyIncreaseSpeed ? 1f : ChallengeMode.GetSpeedUp(1);
                UpdateTimeScale(scale);
                timeScaleRoot = scale;
                SetEndlessRound(0);
            } else if (EndlessIteration.IsActive && gameController.isContinueEndless) {
                float scale = 1 + remoteConfig.EndlessMode_SpeedUp * b.endlessModeCount;
                UpdateTimeScale(scale);
                timeScaleRoot = scale;
                SetEndlessRound(endlessModeCount);
            } else {
                UpdateTimeScale(1);
                timeScaleRoot = 1;
                SetEndlessRound(0);
            }

            isPassRound1 = false;
            if (!isHumanPlayer && !gameController.isSensitiveClosing) {
                ballTrailer.UpTrailerStrength(0);
            }
        }

        LockGravity(); // lock khi show up ball
        SetVelocity(Vector3.zero);

        IEShowUpBall(this.GetCancellationTokenOnDestroy()).Forget();
    }

    private async UniTaskVoid IEShowUpBall(CancellationToken cancellationToken = default) {
        try {
            gameObject.SetActive(false);
            isJumpLastTile = false;
            isPausePlayer = true;
            GameItems.instance.HideShadowBall();

            if (!gameController.isSensitiveClosing) {
                if (!isHumanPlayer) {
                    ballTrailer.Stop();
                }

                GameItems.instance.SetActiveFeetEffect(true, isCharacter, transCache.position);
            }

            if (RemoteConfig.instance.VFX_Opening_IsEnable) {
                if (ballInstance.usingBeltWay) {
                    ballInstance.ForceUpdateBeltWayPosition(ballInstance.currentTransformManager.position +
                                                            Vector3.up * 15);
                }

                if (isHumanPlayer) {
                    GameItems.instance.SetActiveFeetEffect(false, isCharacter, transCache.position);
                }
            }

            while (_isFirstShow && SceneFader.instance.IsShowLoading) {
                await UniTask.Yield(cancellationToken);
            }

            Follow.instance.ForceUpdate();
            if (RemoteConfig.instance.VFX_Opening_IsEnable) {
                await UniTask.Delay(300, cancellationToken: cancellationToken);

            float animTime = 0.2f;
            GameItems.instance.ShowOpeningVFX(transCache.position);
            transCache.position += Vector3.up * 15;
            gameObject.SetActive(true);
            ballInstance.EnableBall(transCache);
            TryShowIntroBall();
            GameItems.instance.SetAnimShadowBall(animTime);
            if (_tweenMove != null && _tweenMove.IsActive()) {
                _tweenMove.Kill();
            }

            _tweenMove = transCache.DOMoveY(_ballPositionInit.y, animTime).SetEase(Ease.Linear)
                .OnUpdate(HandleBallMoveYOnUpdate).OnComplete(HandleBallMoveYOnComplete);
        } else {
            gameObject.SetActive(true);
            ballInstance.EnableBall(transCache);
            TryShowIntroBall();
            isPausePlayer = false;
            ballAnimator.SetBool(_isPlayingHash, true);
            GameItems.instance.SetActiveFeetEffect(true, isCharacter, transCache.position);
            GameItems.instance.SetAnimShadowBall(0f);
        }

            CheckAndSetStartingPosition();
            _isFirstShow = false;
        }
        catch (OperationCanceledException) {
            // Ball show operation was cancelled, cleanup if needed
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(IEShowUpBall)}: {ex.Message}");
        }
    }

    public void StopDownloadBallFromAssetBundle() {
        ballInstance.StopDownloadBallCharacter();
    }

    private void HandleBallMoveYOnUpdate() {
        if (ballInstance.usingBeltWay) {
            ballInstance.ForceUpdateBeltWayPosition(transCache.position);
        }
    }

    private void HandleBallMoveYOnComplete() {
        isPausePlayer = false;
        ballAnimator.SetBool(_isPlayingHash, true);
        GameItems.instance.SetActiveFeetEffect(true, isCharacter, transCache.position);
    }

    private void CheckAndSetStartingPosition() {
        Platform firstPlatform = Spawner.s.platformManager.GetFirstPlatform();
        if (firstPlatform == null) {
            Util.GoHome();
            return;
        }

        if (firstPlatform.isSlideTile || firstPlatform.isHitchhikeTile) {
            var gameItemTrans = GameItems.instance.transform;
            gameItemTrans.position += Vector3.forward * 1.1f;
            GameItems.instance.ActiveVFXSlide(false, gameItemTrans.position);
        }
    }

    public void RunFirstBall(Platform bl) {
        GameItems.instance.transform.position = Vector3.zero;
        GameItems.instance.SetActiveFeetEffect(false, isCharacter);
        ballAnimator.SetBool(_isPlayingHash, false);

        // if (RemoteConfigBase.instance.BouncingBall_UseBouncingBall) {
        //     stretchController.ResetScale();
        // }

        if (_ballId == BallManager.Pumpkin) {
            ballInstance.SetTrigger(_isPlayingHash);
        }

        if (_mirrorBall) {
            _mirrorBall.RunFirstBall();
        }

        if (!isHumanPlayer) {
            ballTrailer.Run();
        }

        ballTrailer.trailTransCache.gameObject.SetActive(true);

        if (transCache == null) {
            transCache = transform;
        }

        _prevPlatformPosition = transCache.position;
        _destinationObj = bl;
        trustTime = 0;
        _streakPerfectCount = 0;
        _beginTime = Time.time;
        UnlockGravity();
        Jump(bl);
    }

    public void DeactivateBall() {
        if (!isHumanPlayer) {
            ballTrailer.Stop();
        }

        gameObject.SetActive(false);
        ballInstance.DisableBall();

        GameItems.instance.SetActiveFeetEffect(false, isCharacter);

        DestroyMirrorBall(false);
    }

    private bool CanRotateCharacter() {
        return isCharacter && !_isAnimating && remoteConfig.CharacterRotation_EnableCharacterRotation &&
               !remoteConfig.CharacterRotation_CharacterRotateAtStart;
    }

    private void TryRotateCharacter() {
        if (!CanRotateCharacter()) {
            return;
        }

        // Cancel any existing rotation
        _rotateRoutine?.Cancel();
        _rotateRoutine?.Dispose();

        // Start new rotation with manual control
        _rotateRoutine = new CancellationTokenSource();
        var linkedToken = CancellationTokenSource.CreateLinkedTokenSource(
            _rotateRoutine.Token,
            this.GetCancellationTokenOnDestroy()
        ).Token;

        IERotateCharacter(linkedToken).Forget();
    }

    //For when rotating and hit a long note
    private void ResetCharacterRotation(float time) {
        _isAnimating = false;

        if (!CanRotateCharacter()) {
            return;
        }

        // Cancel any ongoing character rotation
        _rotateRoutine?.Cancel();
        _rotateRoutine?.Dispose();
        _rotateRoutine = null;

        ballInstance.StopRotate();

        _isAnimating = false;
        _isFacingScreen = false;
        _countToChange = UnityEngine.Random.Range(5, 16); //random btw 5-15 notes before changing state

        ballInstance.Rotate(0, time);
    }

    private async UniTaskVoid IERotateCharacter(CancellationToken cancellationToken = default) {
        try {
            _isAnimating = true;

            var rotateAngle = _isFacingScreen ? 0 : UnityEngine.Random.value < 0.5f ? 180 : -180;

            ballInstance.Rotate(rotateAngle, remoteConfig.CharacterRotation_CharacterRotateSpeed);

            await UniTask.Delay((int)(remoteConfig.CharacterRotation_CharacterRotateSpeed * 1000), cancellationToken: cancellationToken);

            _isAnimating = false;
            _isFacingScreen = !_isFacingScreen;
            _countToChange = UnityEngine.Random.Range(5, 16); //random btw 5-15 notes before changing state
        }
        catch (OperationCanceledException) {
            // Character rotation was cancelled, ensure state cleanup
            _isAnimating = false;
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(IERotateCharacter)}: {ex.Message}");
            _isAnimating = false;
        }
    }

    public bool CanInteractWithFakePlatform(int noteID) {
        if (_isDead) {
            return false;
        }

        if (rb.velocity.y > 0 && !isForceMove) {
            return false;
        }

        int currentJumpNote = Spawner.s.currentJumpNoteID;
        if (currentJumpNote > 0) {
            int indexNextNote = currentJumpNote + 1;
            if (indexNextNote == NotesManager.instance.numberNoteInRound) {
                indexNextNote = 0;
            }

            //Không cho player nhảy cách note
            if (noteID != indexNextNote) {
                return false;
            }
        }

        return true;
    }

    private void HandleCharacterRotation(Platform bl, float time, float jumpTime) {
        if (bl.noteID >= NotesManager.instance.noteCount - 1) {
            return;
        }

        if (bl.noteID >= NotesManager.instance.noteCount - 2) { //Reached ending, should force reset facing
            if (_isFacingScreen) {
                ResetCharacterRotation(time);
                return;
            }
        }

        var nextNoteIndex = bl.noteID + 1;
        var nextNoteData = MapManager.instance.GetPlatformData(nextNoteIndex);
        bool isLongNote = nextNoteData.IsLongNote;
        float nextJumpTime = trustTime + nextNoteData.nextNoteObj.distance / timeScale -
                             (Time.time + jumpTime - _beginTime);
        bool nextNoteSlideTile = nextNoteIndex <= NotesManager.instance.noteCount - 2 && isLongNote;
        if (nextNoteSlideTile || (BallManager.ForceNotRotateOnLongTile(_ballId) &&
                                  nextJumpTime >= remoteConfig.HumanAmin_LongJumpTime)) {
            ResetCharacterRotation(time);
        } else {
            if (jumpTime < remoteConfig.HumanAmin_LongJumpTime && !bl.isSlideTile && !bl.isHitchhikeTile) {
                if (_countToChange > 0) {
                    _countToChange--;
                    if (_countToChange <= 0) {
                        TryRotateCharacter();
                    }
                }
            }
        }
    }

    [Music.ACM.ReadOnly] public bool  onLongTile = false;
    [Music.ACM.ReadOnly] public float timeOnLongTile;
    [Music.ACM.ReadOnly] public bool  onHitchTile = false;

    private async UniTaskVoid IESlideOnTile(Vector3 velocity, Platform bl, float jumpTime, CancellationToken cancellationToken = default) {
        try {
            await UniTask.Yield(cancellationToken);

            Vector3 pos = transCache.position;
            GameItems.instance.ActiveVFXSlide(true, pos);
            if (isHumanPlayer) {
                ballInstance.SetStateSlide();
                var angle = remoteConfig.CharacterRotation_EnableCharacterRotation &&
                            remoteConfig.CharacterRotation_CharacterRotateAtStart
                    ? 540
                    : 720;
                ballInstance.Rotate(angle, bl.timeSlide);

                if (_mirrorBall) {
                    _mirrorBall.StartSlide(bl.timeSlide);
                }
            }

            timeOnLongTile = 0f;
            onLongTile = true;
            _platformSliding = bl;
            bl.OnSlide(true, transCache.position);

            float nextPercent = 0.1f;
            int pointEachTenPercent = remoteConfig.LongTile_TotalPoint / 10;

            float totalTime = bl.timeSlide / timeScale;

            if (ballInstance.usingBeltWay) {
                Vector3 tempPos = transCache.position;

                //TH-2553: beltway của ball jupiter chìm vào longtile
                tempPos.y = JUPITER_BALL_Y_ON_LONG_TILE;
                transCache.position = tempPos;
            } else if (RemoteConfigBase.instance.BouncingBall_UseBouncingBall) {
                transCache.SetLocalY(0.8f);
            }

            while (timeOnLongTile < totalTime && onLongTile) {
                pos = transCache.position;
                pos.y = 0;
                timeOnLongTile += Time.deltaTime;
                GameItems.instance.UpdatePositionVFXSlide(pos);
                bl.Slide(timeOnLongTile, pos);
                float currentPercent = timeOnLongTile / bl.timeSlide;
                if (currentPercent > nextPercent) {
                    nextPercent += 0.1f;
                    UpdateScoreOnLongNote(pointEachTenPercent);
                }
            if (transCache.position.y < -5) {
                Logger.EditorLogError("1 Force die because ball is too low ");
                FallToDeath();
            }
                await UniTask.Yield(cancellationToken);
            }

            if (onLongTile && !(EndlessIteration.IsActive && Spawner.s.platformManager.GetTotalTileFront() == 0)) {
                onLongTile = false;
                if (!isForceMove) {
                    _autoCurTime = 0f;
                }

                _autoCurrentX = transCache.position.x;
                _platformSliding = null;

            bl.OnSlide(false);
            UnlockGravity();
            SetVelocity(velocity);
            trustTime += totalTime;
            GameItems.instance.ActiveVFXSlide(false, pos);
            if (ballInstance.usingCharacterManager) {
                bool isJumpRight = bl.noteID % 2 == 0;
                if (jumpTime > 0) {
                    ballInstance.SetAnimationCharacterJump(jumpTime, isJumpRight);
                    if (_mirrorBall) {
                        _mirrorBall.SetAnimationCharacterJump(jumpTime, isJumpRight);
                    }
                }
            }

                if (bl.endingMoodChange)
                    MapManager.instance.PrepareMoodChange(MapManager.instance.GetPlatformData(bl.noteID + 1));
                if (transCache.position.y < -5) {
                    Logger.EditorLogError("2 Force die because ball is too low");
                    FallToDeath();
                }
            }
        }
        catch (OperationCanceledException) {
            // Slide operation was cancelled, cleanup state
            onLongTile = false;
            _platformSliding = null;
            GameItems.instance.ActiveVFXSlide(false, Vector3.zero);
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(IESlideOnTile)}: {ex.Message}");
            // Ensure cleanup on error
            onLongTile = false;
            _platformSliding = null;
        }
    }

    private async UniTaskVoid IEHitchHikeOnTile(Vector3 velociy, Platform bl, float jumpTime, CancellationToken cancellationToken = default) {
        try {
            await UniTask.Yield(cancellationToken);

            if (isHumanPlayer) {
                ballInstance.SetStateStart();
                ballInstance.Rotate(720, bl.timeSlide);
                if (_mirrorBall) {
                    _mirrorBall.StartSlide(bl.timeSlide);
                }
            }

            float timer = 0f;
            onHitchTile = true;
            bl.OnHitchhike(true);
            float totalTime = bl.timeSlide / timeScale;

            while (timer < totalTime && onHitchTile) {
                Vector3 pos = transform.position;
                pos.y = 0;
                bl.transform.position = pos;
                timer += Time.deltaTime;
                await UniTask.Yield(cancellationToken);
            }

            if (onHitchTile) {
                onHitchTile = false;
                bl.OnHitchhike(false);
                UnlockGravity();
                SetVelocity(velociy);
                trustTime += totalTime;

                if (isHumanPlayer) {
                    bool isJumpRight = true; //(bl.isSlideCombineTile ? bl.noteID + 1 : bl.noteID) % 2 == 0;
                    if (jumpTime > 0) {
                        ballInstance.SetAnimationCharacterJump(jumpTime, isJumpRight);
                        if (_mirrorBall) {
                            _mirrorBall.SetAnimationCharacterJump(jumpTime, isJumpRight);
                        }
                    }
                }

                if (bl.endingMoodChange)
                    MapManager.instance.PrepareMoodChange(MapManager.instance.GetPlatformData(bl.noteID + 1));
            }
        }
        catch (OperationCanceledException) {
            // Hitchhike operation was cancelled, cleanup state
            onHitchTile = false;
            if (bl != null) bl.OnHitchhike(false);
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(IEHitchHikeOnTile)}: {ex.Message}");
            // Ensure cleanup on error
            onHitchTile = false;
            if (bl != null) bl.OnHitchhike(false);
        }
    }

    public void FallToDeath() {
        if (isInvincible) {
            return;
        }

        _isDead = true;
        UnlockGravity();
        if (!isHumanPlayer)
            GameItems.instance.ActiveVFXSlide(false, Vector3.zero);
        onLongTile = false;
        if (_platformSliding != null) {
            _platformSliding.OnSlide(false);
            _platformSliding = null;
        }
    }

    private void CheckPositionOnRainbow() {
        centerX = startRainbow.x + (transCache.position.z - startRainbow.z) / DistanceOfRainbow *
            (endRainbow.x - startRainbow.x);
        if (Mathf.Abs(transCache.position.x - centerX) > 2.5f)
            SkipRainbow();
    }

    private void SkipRainbow() {
        if (currentRainbow == null)
            return;

        GameItems.instance.DisableRainbow(currentRainbow);
        GameItems.instance.RainbowImpactStatus(false);
        isOnRainbow = false;
        currentRainbow = null;
    }

    private async UniTaskVoid IEPlayOnRainBow(PathCreator rainbow, float time, CancellationToken cancellationToken = default) {
        try {
            if (currentRainbow != null) {
                SkipRainbow();
            }

            this.currentRainbow = rainbow;
            this.startRainbow = rainbow.StartPoint;
            this.endRainbow = rainbow.EndPoint;
            await UniTask.Yield(cancellationToken);

            float timer = 0;
            float value = 0;
            isOnRainbow = true;
            float current_percent = 0f;
            float next_percent = 0.1f;
            int pointEachTenPercent = remoteConfig.LongTile_TotalPoint / 10;

            float totalTime = time / timeScale;

            float startValue = Mathf.PI * 2 / 9;
            float endValue = -Mathf.PI * 2 / 9;

            while (timer < totalTime && isOnRainbow) {
                timer += Time.deltaTime;
                current_percent = timer / totalTime;

                if (current_percent > next_percent) {
                    next_percent += 0.1f;
                    UpdateScoreOnLongNote(pointEachTenPercent);
                }

                value = Mathf.Tan(Mathf.Lerp(startValue, endValue, current_percent));

                GameItems.instance.SetRainbowAlpha(currentRainbow, value);
                await UniTask.Yield(cancellationToken);
            }

            SkipRainbow();
        }
        catch (OperationCanceledException) {
            // Rainbow play was cancelled, cleanup state
            isOnRainbow = false;
            if (currentRainbow != null) {
                SkipRainbow();
            }
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(IEPlayOnRainBow)}: {ex.Message}");
            // Ensure cleanup on error
            isOnRainbow = false;
            if (currentRainbow != null) {
                SkipRainbow();
            }
        }
    }

    private void UpdateScoreOnLongNote(int score) {
        gameController.AddScore(score);
        TopBar.instance.SetScore(gameController.Score);
    }

    private void UpdateTryNewSkin(Platform bl) {
        if (Spawner.s.DisableTrySkinOnPlatForm(bl.platformData.canTrySkin)) {
            bl.platformData.canTrySkin = false;
        }

        tryBallId = bl.trySkinID;
        PlayerPrefs.SetInt(PlayerPrefsKey.trySkinID, tryBallId);
        if (remoteConfig.TrySkin_Style == 2) {
            bl.trySkinParent.gameObject.SetActive(false);
        } else { //TrySkin_Style == 1
            GameItems.instance.tryCharacterManagerScript.transform.parent.gameObject.SetActive(false);
            Transform transformParent = GameItems.instance.tfTrySkin;
            transformParent.gameObject.SetActive(true);
            transformParent.SetParent(bl.trySkinParent);
            transformParent.localPosition = Vector3.zero;
            transformParent.localScale = Vector3.one;
        }

        UIController.ui.UpdateTimeStartTrySkin();

        UpdateBall(tryBallId, true);
        NotesManager.instance.UpdateLastNoteDataAfterTryNewSkin(BallManager.itemsHuman.Contains(tryBallId));

        Dictionary<string, object> param = new Dictionary<string, object> {
            {"item_id", tryBallId},
            {"skin_type", BallManager.itemsHuman.Contains(tryBallId) ? "character" : "ball"},
            {"time_equip", GameController.instance.timePlay}, //thời gian từ lúc song_start đến khi equip new skin
            {TRACK_NAME.song_name, NotesManager.instance.song.name},
            {"accumulate_song_start", UserProperties.GetPropertyInt(UserProperties.song_start)},
        };
        AnalyticHelper.FireEvent(FIRE_EVENT.skin_gameplay_equip, param);
    }

    public float GetBalLSpeed() {
        if (remoteConfig.NotesDifficult_IsEnable) {
            float easyBpm = Configuration.GetSongBpmACM(NotesManager.instance.song.path) != 0
                ? Configuration.GetSongBpmACM(NotesManager.instance.song.path)
                : Configuration.GetSongBpmLocal(NotesManager.instance.song.path);
            Spawner.s.bpm =
                NotesManager.instance.Difficulty == NotesManager.Difficulties.Easy &&
                remoteConfig.NotesDifficult_Easy_UseBpm && remoteConfig.NotesDifficult_Easy_UseBpmLocalSong
                    ? easyBpm
                    : NotesManager.instance.song.bmp;
        }

        _ballSpeed = remoteConfig.BallSpeed *
            (remoteConfig.BallSpeedFixBpm > 0 ? remoteConfig.BallSpeedFixBpm : Spawner.s.bpm) / 100;

        if (remoteConfig.NotesDifficult_IsEnable) {
            if (NotesManager.instance.Difficulty == NotesManager.Difficulties.Hard) {
                _ballSpeed *= remoteConfig.NotesDifficult_Hard_MultiplySpeed;
            }
        }

        return _ballSpeed;
    }

    #endregion

    #region Game Modes

    private async UniTaskVoid GameCompleted(bool isHumanEnd = false, CancellationToken cancellationToken = default) {
        try {
            if (remoteConfig.isEnableAnzu && AnzuManager.instance != null) {
                AnzuManager.instance.StopMove();
            }

            if (remoteConfig.Adverty_IsEnable && AdvertyManager.instance != null) {
                AdvertyManager.instance.StopMove();
            }

            if (!isHumanPlayer) {
                ballTrailer.GameCompletedEffect();
            }

        SoundManager.PlayCheckPoint();
        if (!Spawner.s.isMusicalizationType) {
            Spawner.s.SwitchBackGround(1f);
        }

        if (isHumanEnd) {
            await UniTask.Delay(1000, cancellationToken: cancellationToken); //need wait SwitchBackGround ?
        } else {
            float time = 2f;
            Vector3 startVelocity = Vector3.zero;
            Vector3 endVelocity = Vector3.up * 10;
            //rb.useGravity = false;
            LockGravity();
            float start = forwardSpeed;
            float t = 0;
            float destination = forwardSpeed * 40f;
            while (t < time) {
                t += Time.deltaTime;
                //rb.velocity = Vector3.Lerp(startVelocity, endVelocity, t / time);
                SetVelocity(Vector3.Lerp(startVelocity, endVelocity, t / time));
                forwardSpeed = Mathf.Lerp(start, destination, t / time);
                await UniTask.Yield(cancellationToken);
                }
            }

            while (gameController.IsWaitingFlyDiamondVFXFromPrgogressbar) {
                await UniTask.Yield(cancellationToken);
            }

            isPausePlayer = false;
            gameController.CheckCompleteGame();
        }
        catch (OperationCanceledException) {
            // Game completion was cancelled, restore player state
            isPausePlayer = false;
            UnlockGravity();
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(GameCompleted)}: {ex.Message}");
            // Ensure player state restoration on error
            isPausePlayer = false;
            UnlockGravity();
        }
    }

    private void UpdateEndlessMode() {
        trustTime = 0;
        _beginTime = Time.time;
        timeScaleRoot = timeScale;
        SetEndlessRound((byte) (endlessModeCount + 1));

        if (!isHumanPlayer) {
            ballTrailer.UpTrailerStrength(1);
        }

        if (remoteConfig.ImproveSensitive_IsEnable && remoteConfig.ImproveSensitive_UseNewNotes &&
            gameController.isUpdateSensitive || EndlessIteration.IsActive) {
            //dont play music
        } else {
            Spawner.s.PlayMusic();
        }

        if (GameItems.instance.edmController != null)
            GameItems.instance.edmController.EnableTimeLine(true);

        if (GameItems.instance.hiphopModernController != null)
            GameItems.instance.hiphopModernController.EnableTimeLine(true);

        GameController.instance.currentAnimation = 0;
        if (endlessModeCount > 0) {
            foreach (NoteData node in GameController.instance.animationNotes) {
                node.timeAppear += GameController.instance.timePlay / endlessModeCount;
            }
        }

        if (remoteConfig.ProgressBar_StyleIndex != 3 && !EndlessIteration.IsActive) { //old progress bar
            TopBar.instance?.ShowEndlessMode(endlessModeCount);
        }
    }

    #endregion

    #region Gravity & Time Scale

    private void UpdateTimeScale(float scale) {
        timeScale = scale;
        forwardSpeed = GetBalLSpeed() * timeScale;
        SuperpoweredSDK.instance.SetSpeed(timeScale);
        if (GameItems.instance.edmController != null) {
            GameItems.instance.edmController.UpdateAnimationSpeed();
        }

        if (GameItems.instance.vfxController != null) {
            GameItems.instance.vfxController.UpdateAnimationSpeed();
        }

        OnTimeScaleUpdated?.Invoke(scale);
    }

    public float GetTimeScale(int noteID) {
        float scale = timeScaleRoot + remoteConfig.EndlessMode_SpeedUp *
            ((float) (noteID) / NotesManager.instance.noteCount);
        if (scale > timeScale) {
            return scale;
        } else {
            return timeScale;
        }
    }

    public void UpdateMinDistanceWithBlock() {
        _minDistanceWithBlock = _ballSize / 2 + Spawner.s.blockScaleCurrent.x / 2;
    }

    #endregion

    public void ColliderWithFakeBlock(int noteId, GameObject fakeTile) {
        if (!CanInteractWithFakePlatform(noteId)) {
            return;
        }

        // if (isInvincible) {
        //     ProcessJumpOnAir(noteId);
        //     Debug.Log("JUMP FAKE");
        // }

        SoundManager.PlayDiamond();
        GameItems.instance.BrokenEffect(transCache.position, Spawner.s.GetCurrentSkin().tileColor);
        fakeTile.SetActive(false);
    }

    public void ProcessJumpOnAir(int nodeId) {
        Spawner.s.platformManager.CheckSpawner();
        InvincibleJump(CheckHumanEnd(nodeId));
    }

    public bool CheckHumanEnd(int nodeId) {
        return isHumanPlayer && CheckEnd(nodeId);
    }

    private bool CheckEnd(int nodeId) {
        if (ChallengeMode.IsActive && GameController.enableEndless) {
            return Spawner.s.platformManager.GetTotalTileFront() == 0;
        }

        if (EndlessIteration.IsActive) {
            return Spawner.s.platformManager.GetTotalTileFront() == 1;
        }

        return !GameController.enableEndless && nodeId == NotesManager.instance.noteCount - 1;
    }

    public void UpdateBall(int ballID, bool isTryBall = false, int timeout = 0) {
        if (ballID < 0) {
            return;
        }

        transform.localEulerAngles = Vector3.zero;
        ChangeBall(ballID, isTryBall, timeout);
        CheckRotate(ballID);
    }

    public void ColliderWithBlock(Platform bl, bool forcePerfect = false) {
        if (gameController == null) {
            return;
        }

        if (bl.platformType == PlatformType.FAKE && bl.platformData.stage != Stage.FAKE_TILE) {
            ColliderWithFakeBlock(bl.noteID, bl.gameObject);
            Spawner.s.platformManager.RecycleTile(bl);
        } else {
            bool isHumanEnd = CheckHumanEnd(bl.noteID);

            bool jump = Jump(bl, isHumanEnd);
            if (bl.hitted || !jump) {
                return;
            }

            bool isPerfect = forcePerfect || bl.IsPerfectLanding(transCache.position, remoteConfig.GetPerfectSize());

            if (isPerfect && (!GameController.enableEndless || endlessModeCount == 0)) {
                Spawner.s.totalPerfectCount++;
            }

            UpdateTrail(isPerfect);

            Spawner.s.SetJumpCount(Spawner.jumpCount + 1);
            _streakPerfectCount = UpdateStreakPerfectCount(isPerfect, _streakPerfectCount);

            if (gameController) {
                gameController.UpdateScore(_streakPerfectCount);

                if (gameController.IsNewTutorialGamePlay) {
                    gameController.passedTile = bl;
                }
            }

            bl.Hit(_streakPerfectCount);

            if (_streakPerfectCount >= _tilePerfectTrigger) {
                Spawner.s.ChangeBlockSize(_streakPerfectCount);
                if (!GameController.IsAutoPlay()) {
                    SoundManager.instance.PlayImpactExtend();
                }
            }

            if (gameController != null) {
                if (isPerfect) {
                    gameController.perfectCount++;
                }

                gameController.perfectCountMax = Mathf.Max(_streakPerfectCount, gameController.perfectCountMax);
            }

            if (_streakPerfectCount * -1 == _tileBadTrigger) {
                Spawner.s.ChangeBlockSize(_streakPerfectCount);
                _streakPerfectCount = 0;
            }

            //end of road
            CheckEndOfRoad(bl, isHumanEnd);
        }
    }

    public void CheckEndOfRoad(Platform bl, bool isHumanEnd) {
        if (CheckEnd(bl.noteID)) {
            if (GameController.enableEndless && ChallengeMode.IsActive) {
                Vector3 positionStar = bl.transCache.position;
                TopBar.instance?.MoveStar(positionStar, 3);
            }

            if (isHumanEnd) {
				PrepareGameCompleted(true, this.GetCancellationTokenOnDestroy()).Forget();
			} else {
				GameCompleted(false, this.GetCancellationTokenOnDestroy()).Forget();
			}
        } else if (gameController != null && gameController.game == GameStatus.P_DIE &&
                   Spawner.s.platformManager.GetTotalTileFront() == 0) {
            isJumpLastTile = true;
        }

        if (MaskManager.instance != null) {
            MaskManager.instance.PlayMaskEffect(bl.platformData.isStrongNote, bl.nextNoteDistance);
        }
    }

    public void UpdateTrail(bool isPerfect) {
        if (!remoteConfig.BallTrailPerfect_Enable) {
            return;
        }

        if (gameObject == null || ballTrailer == null || charTrailer == null) {
            return;
        }

        if (isPerfect) {
            if (!isHumanPlayer || ThemeManager.IsThemePrideMonth())
                ballTrailer.UpdateTrailEffect(_ballId, _streakPerfectCount);
            else
                charTrailer.UpdateTrailEffect(_streakPerfectCount);
        } else {
            if (!isHumanPlayer || ThemeManager.IsThemePrideMonth())
                ballTrailer.StopTrailEffect();
            else
                charTrailer.StopTrailEffect();
        }

        GameItems.instance.OnTrailUpdate?.Invoke(isPerfect, _streakPerfectCount);
    }

    private void UpdateScore(int perfectCount) {
        int plusScore;
        bool isPerfect = perfectCount > 0;

        if (remoteConfig.NotesDifficult_IsEnable) {
            switch (NotesManager.instance.Difficulty) {
                case NotesManager.Difficulties.Easy:
                    plusScore = isPerfect ? Mathf.Min(perfectCount, 20) : 1;
                    break;

                case NotesManager.Difficulties.Normal:
                    plusScore = isPerfect ? Mathf.Min(2 * perfectCount, 40) : 2;
                    break;

                case NotesManager.Difficulties.Hard:
                    plusScore = isPerfect ? Mathf.Min(3 * perfectCount, 60) : 3;
                    break;

                default:
                    plusScore = isPerfect ? Mathf.Min(perfectCount, 20) : 1; //default
                    break;
            }
        } else { //default
            plusScore = isPerfect ? Mathf.Min(perfectCount, 20) : 1;
        }

        if (gameController != null) {
            gameController.AddScore(plusScore);
            TopBar.instance.UpdateScore(gameController.Score, perfectCount);
        }
    }

    private int UpdateStreakPerfectCount(bool perfect, int perfectCount) {
        if (!perfect) {
            if (perfectCount <= 0) {
                perfectCount--;
            } else {
                perfectCount = -1;
            }
        } else {
            if (perfectCount >= 0) {
                perfectCount++;
            } else {
                perfectCount = 1;
            }
        }

        return perfectCount;
    }

    private async UniTaskVoid PrepareGameCompleted(bool isHumanEnd, CancellationToken cancellationToken = default) { //human
        try {
            await UniTask.Yield(cancellationToken);

            if (!isPausePlayer) {
                isPausePlayer = true;
                Spawner.s.ClearDiamondLine();
                if (isHumanEnd) {
                    if (!EndlessIteration.IsActive) {
                        OnJumpToEndHuman?.Invoke();
                    }
                }

                OnPrepareGameCompleted?.Invoke();

            //move player to center
            float shortAnimTime = 0.5f;
            float destinationPositionX = _destinationObj.transCache.position.x;
            if (_tweenMove != null && _tweenMove.IsActive()) {
                _tweenMove.Kill();
            }

            _tweenMove = transCache.DOMoveX(destinationPositionX, shortAnimTime);

                if (BallManager.instance.IsRollingBall(_ballId) || isCharacter) {
                    float moveHigherAmount = 0.5f;
                    _tweenMove = transCache.DOMoveY(transCache.position.y + moveHigherAmount, shortAnimTime);
                }

                Ball.ResetGravity();
                GameItems.instance.ShowFxWin(true);
                float timeAnimation = ballInstance.PrepareFinished() - 1; // SwitchBackGround = 1

                Follow.instance.SeePlayer(destinationPositionX, Mathf.Min(2, timeAnimation * shortAnimTime));
                await UniTask.Delay((int)(timeAnimation * 1000), cancellationToken: cancellationToken);
                
                GameCompleted(isHumanEnd, cancellationToken).Forget();
            }
        }
        catch (OperationCanceledException) {
            // Prepare game completion was cancelled, cleanup state
            if (isPausePlayer) {
                isPausePlayer = false;
            }
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(PrepareGameCompleted)}: {ex.Message}");
            // Ensure cleanup on error
            if (isPausePlayer) {
                isPausePlayer = false;
            }
        }
    }

    private void ProcessPlatformDataAfterJump(Platform bl) {
        PlatformData platformData = bl.platformData;
        if (!platformData.isInited) {
            return;
        }

        Platform nextPlatform = Spawner.s.platformManager.GetBlockByNoteID(bl.noteID + 1);
        if (platformData.isStartStage) { //~~~~~~~~~~~~ Start Stage ~~~~~~~~~~~~
            if (platformData.stage == Stage.COLORING) {
                //do nothing
            } else if (platformData.stage == Stage.COLOR_CHOOSING) {
                //Đổi màu toàn bộ tile trong stage color choosing
                Spawner.s.ChangeColorForStageColorChoosing((byte) bl.indexSkin, platformData.currentIndexNote);
            }
        } else if (platformData.isEndStage) { //~~~~~~~~~~~~ End Stage ~~~~~~~~~~~~
            if (nextPlatform != null && nextPlatform.platformType == PlatformType.COLOR_CHOOSING) {
                //không tự đổi màu bg nếu note tiếp theo là COLOR_CHOOSING
            } else {
                //TH-1069: tắt tính năng đổi BG của none musicalization đi, sẽ áp dụng moodchange tại end stage
                // if (!(Spawner.s.isMusicalizationType && RemoteConfig.instance.Musicalization_MoodChange_IsEnable)) {
                //     Spawner.s.UpdateBg(); //Change color by old logic
                //
                // }
            }
        }

        //Musicalization: Change color by mood changing
        MapManager.instance.PrepareMoodChange(bl.platformData);

        if (nextPlatform != null && nextPlatform.platformData.stage == Stage.LATE_FAKE) {
            //ẩn hiệu ứng focus của tile late fake
            nextPlatform.SetActiveEfxFocus(false);

            List<Platform> fakeTiles = nextPlatform.GetFakeTiles();
            foreach (Platform fakeTile in fakeTiles) {
                fakeTile.SetActiveEfxFocus(false);
            }

            Spawner.s.platformManager.ClearClone(fakeTiles, Vector3.back); //fake tile lùi lại rồi biến mất
        }
    }

    public float GetJumpHeight(float distance) {
        float time = distance / timeScale;
        float jumpHeight = 10;
        float scaleJumpByType = time > 0 ? GetScaleJumpByType(time) : 1;
        float maxHeight = 1.4f * _jumHeight * scaleJumpByType;
        jumpHeight = maxHeight * Mathf.Sqrt(time * timeScale) * Mathf.Sqrt(Spawner.s.bpm / 110f);
        if (jumpHeight > maxHeight) {
            jumpHeight = maxHeight;
        }

        return jumpHeight;
    }

    public float PrepareStart() {
        return ballInstance.PrepareStart();
    }

    #region GetScaleJumpByType

    private float GetScaleJumpByType(float jumpTime) {
        CharacterAnimator.JumpType jumpType = CharacterAnimator.GetJumpType(jumpTime);

        switch (jumpType) {
            case CharacterAnimator.JumpType.LongJump:
                return _ratioJumpHeight[0];

            case CharacterAnimator.JumpType.NormalJump:
                return _ratioJumpHeight[1];

            case CharacterAnimator.JumpType.ShortJump:
                return _ratioJumpHeight[2];

            default:
                return _ratioJumpHeight[1];
        }
    }

    private float[] UpdateRatioJumpHeightByBall(int ballID) {
        if (_ratioJumpHeightByBall == null || _ratioJumpHeightByBall.Count == 0) {
            _ratioJumpHeightByBall = new Dictionary<int, float[]>();

            string ratioJumpHeightByBall = remoteConfig.HumanAmin_RatioJumpHeightByBall;
            if (!string.IsNullOrEmpty(ratioJumpHeightByBall)) {
                try {
                    Dictionary<string, object> dict =
                        (Dictionary<string, System.Object>) Json.Deserialize(ratioJumpHeightByBall);
                    foreach (KeyValuePair<string, object> item in dict) {
                        float[] ratioJumpByType = GetRatioJumpByType(item.Value.ToString());
                        _ratioJumpHeightByBall.Add(int.Parse(item.Key), ratioJumpByType);
                    }
                } catch (Exception e) {
                    Debug.LogError("[UpdateJumpHeightByRatio] " + e.Message);
                }
            }
        }

        if (_ratioJumpHeightByBall.ContainsKey(ballID)) {
            return _ratioJumpHeightByBall[ballID];
        } else if (_ratioJumpHeightByBall.ContainsKey(-1)) {
            return _ratioJumpHeightByBall[-1];
        } else {
            return new float[3] {1, 1, 1};
        }
    }

    private float[] GetRatioJumpByType(string itemValue) {
        float[] jumpType = new float[3];
        string[] values = itemValue.Split('|');
        for (byte i = 0; i < jumpType.Length; i++) {
            if (i < values.Length) {
                bool tryParse = float.TryParse(values[i], out jumpType[i]);
                if (!tryParse) {
                    jumpType[i] = 1;
                }
            } else {
                jumpType[i] = 1;
            }
        }

        return jumpType;
    }

    #endregion

    public static void ResetGravity() {
        Physics.gravity = new Vector3(0, -Platform.GravityEarth, 0);
    }

    /// <summary>
    /// TH-3513
    /// </summary>
    public void ResetInteractableWithPlatform() {
        onLongTile = false;
        onHitchTile = false;
    }

    public void ShowInShop(int ballId, bool isShow, float timeAnimation) {
        _ballPositionInit = transCache.position;
        Vector3 newPosition = _ballPositionInit;
        if (isShow) {
            newPosition.y = GetInShopNewPositionY(ballId);
        }

        transCache.position = newPosition;
        if (ballInstance.usingBeltWay) {
            ballInstance.ForceUpdateBeltWayPosition(newPosition);
        }
    }

    private float GetInShopNewPositionY(int ballId) {
        switch (ballId) {
            case BallManager.Fluffy:
            case BallManager.HopInTheBox:
                return POSITION_Y_INSHOP_MEDIUM;

            case BallManager.Hiphop_1:
            case BallManager.Hiphop_2:
            case BallManager.Hiphop_3:
            case BallManager.Hiphop_4:
            case BallManager.Hiphop_5:
            case BallManager.Pirate:
            case BallManager.Poppy:
            case BallManager.HarryPotBall:
            case BallManager.Deadverin:
            case BallManager.MaracasHop:
            case BallManager.Fankenstein:
            case BallManager.EDM:
            case BallManager.KingFur:
            case BallManager.IronBall:
            case BallManager.SquidBall:
                return POSITION_Y_INSHOP_LOW;

            case BallManager.HLW1:
            case BallManager.HLW3:
            case BallManager.Dragon:
            case BallManager.Panda:
                return POSITION_Y_INSHOP_EXTREME;

            default:
                bool isHuman = BallManager.itemsHuman.Contains(ballId);
                if (!isHuman || BallManager.instance.IsRollingBall(ballId) || BallManager.instance.IsXmasBall(ballId)) {
                    return ballId == BallManager.Pumpkin
                        ? 0.9f
                        : (!isHuman && RemoteConfigBase.instance.BouncingBall_UseBouncingBall
                            ? POSITION_Y_INSHOP_LOW
                            : POSITION_Y_INSHOP_EXTREME);
                }

                return POSITION_Y_INSHOP_HIGH;
        }
    }

    [HideInInspector] public bool isActiveBall;

    public void ChangeBall(int ballId = -1, bool isTryBall = false, int timeout = 0) {
        if (ballId < 0) {
            ballId = Configuration.GetSelectedBall();
        }

        _ballId = ballId;
        _isPlanetBall = BallManager.instance.IsPlanetBall(_ballId);

        InitBall(ballId);
        isActiveBall = false;
        UpdateModelCharacter(ballId, isTryBall, onInitDone: (isDone) => {
            if (_ballId == ballId) {
                if (!isDone) {
                    ChangeBall(BallManager.DefaultBall);
                    Util.ShowSmallMessage("Cannot load this ball!");
                } else {
                    isActiveBall = true;
                }
            } else {
                isActiveBall = true;
            }
        }, timeout);

        if (RemoteConfigBase.instance.BouncingBall_UseBouncingBall && isCharacter && stretchController) {
            stretchController.ResetBouncing();
            stretchController.StopPreviousEffect();
        }
    }

    private void InitBall(int ballId) {
        _ratioJumpHeight = UpdateRatioJumpHeightByBall(ballId);
        _isPlanetBall = BallManager.ballsPlanet.Contains(ballId);
    }

    private void UpdateModelCharacter(int ballId, bool isTrySkin = false, Action<bool> onInitDone = null,
                                      int timeout = 0) {
        _currentTryBallId = ballId;
        transCache.SetPositionY(_ballId == BallManager.Pumpkin ? 0.9f : 0.5f);
        transCacheRotation.localRotation = Quaternion.identity;
        _ballPositionInit = transCache.position;

        isHumanPlayer = BallManager.instance.IsHumanBehaviour(ballId);

        ballTrailer.UpdateTrailFollowBall(ballId);

        if (ShadownScaler != null) {
            ShadownScaler.gameObject.SetActive(false);
        }

        if (ShopScript.instance != null) {
            ShopScript.instance.ActiveLoadingIndicator(true);
        }

        ballInstance.Init(ballId, _ballPositionInit, transCache.localScale, onInitDone: (isSuccess => {
            if (!this) {
                return;
            }

            if (isSuccess) {
                InitBallSuccess(isTrySkin);
                if (ShadownScaler != null) {
                    ShadownScaler.gameObject.SetActive(true);
                }
            }

            if (ShopScript.instance != null) {
                ShopScript.instance.ActiveLoadingIndicator(false);
            }

            onInitDone?.Invoke(isSuccess);
        }), timeout);
    }

    private void InitBallSuccess(bool isTrySkin) {
        if (isHumanPlayer && transCache.GetChild(0).childCount > 0) {
            gameController.objectRotate = transCache.GetChild(0).GetChild(0);
        } else {
            gameController.objectRotate = null;
        }

        ballTrailer.Stop();

        if (!isHumanPlayer && !isTrySkin) {
            ballInstance.ShowVFX();
            ballAnimator.SetBool(_isPlayingHash, true);
        }

        if (isCharacter && isTrySkin) {
            if (ballInstance.usingCharacterManager) {
                ballInstance.SetTrigger(_isPlayingHash);
            }
        }

        GameItems.instance.OnSwapCharacterAndBall?.Invoke();
        if (!isPausePlayer) {
            TryShowIntroBall();
            GameItems.instance.SetActiveFeetEffect(true, isCharacter);
        }

        if (_mirrorBall != null) {
            _mirrorBall.UpdateModelCharacter(_currentTryBallId);
        }
    }

    private void TryShowIntroBall() {
        if ((isHumanPlayer || BallManager.instance.IsHiphopBall(_ballId) ||
             BallManager.instance.IsHalloweenBall(_ballId) || _isSpecialBall) && this.gameObject.activeSelf) {
            IntroBall(_ballId, this.GetCancellationTokenOnDestroy()).Forget();
        }
    }

    public void MoveForward(int distance, float time = 1.5f) {
        isPausePlayer = true;
        LockGravity();
        SetVelocity(Vector3.zero);

        float positionZ = transCache.GetPositionZ() + distance;
        float posY = _ballPositionInit.y;
        Vector3 newPos = new Vector3(0, posY, positionZ);
        if (_tweenMove != null && _tweenMove.IsActive()) {
            _tweenMove.Kill();
        }

        _tweenMove = transCache.DOMove(newPos, time);

        Follow.instance.DoAddMoveZ(distance, time);
        Follow.instance.DoMoveX(0, time);

        MapManager.instance.SetFirstPositionZ(positionZ);
    }

    private async UniTaskVoid IntroBall(int ballId, CancellationToken cancellationToken = default) {
        try {
            while (!isActiveBall) {
                await UniTask.Yield(cancellationToken);
            }

            _isHalloweenBall = BallManager.instance.IsHalloweenBall(ballId);
            bool setRotate = false;
            while (GameController.instance.game != GameStatus.LIVE) {
                if (!setRotate) {
                    if (_inPreviewRotate) {
                        if (!_isHalloweenBall) {
                            ballInstance.SetChildRotation(new Vector3(0, 180, 0));
                        } else {
                            ballInstance.SetRotation(new Vector3(0, 180, 0));
                        }
                    } else {
                        if (!_isHalloweenBall) {
                            ballInstance.SetChildRotation(new Vector3(-10, 180, 0));
                        } else {
                            ballInstance.SetRotation(new Vector3(-30, 180, 0));
                        }
                    }

                    setRotate = true;
                }

                await UniTask.Yield(cancellationToken);
            }

            if (!_isHalloweenBall) {
                if (!(RemoteConfigBase.instance.CharacterRotation_EnableCharacterRotation &&
                      RemoteConfigBase.instance.CharacterRotation_CharacterRotateAtStart)) {
                    ballInstance.Rotate(0, 0);
                } else {
                    ballInstance.Rotate(180, 0);
                }
            } else {
                ballInstance.SetRotation(Vector3.zero);
            }
        }
        catch (OperationCanceledException) {
            // Ball intro was cancelled, no specific cleanup needed
        }
        catch (System.Exception ex) {
            Debug.LogError($"Error in {nameof(IntroBall)}: {ex.Message}");
        }
    }

    public void StartRewind() {
        this.gameObject.SetActive(true);
        _isDead = false;
        SetVelocity(Vector3.zero);
        LockGravity();
    }

    public void ToggleAnimShopPreview(bool isPreviewing) {
        ballAnimator.SetBool(_isPreviewingHash, isPreviewing);
        if (_mirrorBall) {
            _mirrorBall.ToggleAnimShopPreview(isPreviewing);
        }
    }

    public void AddPerfect() {
        _streakPerfectCount = UpdateStreakPerfectCount(true, _streakPerfectCount);
        UpdateScore(_streakPerfectCount);
    }

    private void UpdateBall() {
        if (isPausePlayer) {
            return;
        }

        _deltaTime = Time.deltaTime;
        _cachePosition = transCache.position;
        _cachePosition.z += forwardSpeed * _deltaTime; // tiến về phía trước

        if (isForceMove) {
            OnForceMove(_cachePosition);
        } else {
            if (GameController.IsAutoPlay()) {
                OnAutoPlay();
            }

            transCache.position = _cachePosition;
        }

        UpdateOther();
    }

    private void OnForceMove(Vector3 cachePosition) {
        switch (forceMoveType) {
            case PlatformType.SKEW_SPIRAL: {
                ForceMoveForSkewSpiral(cachePosition);
                break;
            }

            case PlatformType.PIPE_TILE: {
                ForceMoveForPipeTile(cachePosition);
                break;
            }

            default:
                Logger.EditorLogError("Force move follow ????");
                break;
        }

        _autoCurTime += _deltaTime * timeScale;
    }

    private void OnAutoPlay() {
        if (!Input.GetMouseButton(0)) {
            if (!onLongTile) {
                if (isInZicZacSection) {
                    _cachePosition.x = Spawner.s.GetZicZacAutoPosition(_cachePosition);
                } else {
                    _cachePosition.x =
                        Configuration.instance.defaultCurve.Evaluate(_autoCurTime / _autoTime) *
                        (_destinationObj.GetLandingPosition().x - _autoCurrentX) + _autoCurrentX;
                }
            } else {
                _cachePosition.x = _platformSliding.GetAutoPosition(timeOnLongTile).x;
            }
        } else {
            _autoCurrentX = _cachePosition.x;
        }

        _autoCurTime += _deltaTime * timeScale;
    }

    private void UpdateOther() {
        if (ballInstance.usingBeltWay) {
            ballInstance.ForceUpdateBeltWayPosition(transCache.position);
        }

        if (!isHumanPlayer) {
            ballTrailer.trailTransCache.position = transCache.position;
        } else {
            charTrailer.trailTransCache.position = transCache.position;
            if (isForceMove)
                charTrailer.trailTransCache.rotation = transCacheRotation.rotation;
        }

        if (gameController.isSensitiveClosing && !isHumanPlayer) {
            ballTrailer.trailTransCache.position = transCache.position;
        }

        if (isOnRainbow) {
            CheckPositionOnRainbow();
        }
    }

    private void UpdateShadowAnimation() {
        if (ballInstance == null || ballInstance.usingCharacterManager ||
            ballInstance.currentTransformManager == null) {
            return;
        }

        if (ShadownScaler != null) {
            float currentPercentage = 1 - ballInstance.currentTransformManager.localPosition.y / maxValue;
            ShadownScaler.localScale = (shadowValue.x + (shadowValue.y - shadowValue.x) * currentPercentage) *
                                       shadowValue.y * Vector3.one;
        }

        if (ballInstance.usingBeltWay) {
            ballInstance.ForceUpdateBeltWayPosition(ballInstance.currentTransformManager.position);
        }
    }

    public void UpdateDestinationObj() {
        _destinationObj = Spawner.s.mainBlockQueue.Dequeue();
    }

    public void HideBall() {
        ballInstance.gameObject.SetActive(false);
        ballTrailer.Stop();
    }

    public void TransitionFXBall() {
        ballFXTransition.gameObject.SetActive(true);
        ballFXTransition.Play();
    }

    public void ShowBall() {
        ballInstance.gameObject.SetActive(true);
        ballTrailer.Run();
    }

    #region Special Section

    private bool       _isInMirrorSection;
    private MirrorBall _mirrorBall;
    private GameObject _mirrorTrail;

    internal bool  isInHyperBoostSection;
    private  float _maxHeightBoostSection;
    private  bool  _isFlyingUp;
    private  bool  _isFlyingDown;
    private  float _gravityScaleHyperBoost;
    internal bool  isInZicZacSection;
    private  float _timeInSpecialSection;

    private void GameControllerOnOnChangeSectionType(IngameSectionType sectionType) {
        switch (sectionType) {
            case IngameSectionType.Normal:
                DestroyMirrorBall(true);
                DestroyHyperBoostTrail();
                OffSpecialSectionHyperBoost();
                break;

            case IngameSectionType.Mirror:
                EnableSpecialSectionMirror();
                break;

            case IngameSectionType.UpsideDown:
                //do nothing
                break;

            case IngameSectionType.HyperBoost:
                StartSpecialSectionHyperBoost();
                EnableHyperBoostTrail();
                break;

            case IngameSectionType.ZicZac:
                //StartSpecialSectionZicZac();
                break;

            default:
                Logger.EditorLogError($"Not handle this type!!! {sectionType}");
                break;
        }
    }

    private void EnableMirrorTrail() {
        if (!_mirrorTrail) {
            _mirrorTrail = Instantiate(Resources.Load<GameObject>(ResourcesPath.MirrorTrailBase), transCache);
        }

        if (_mirrorTrail) {
            _mirrorTrail.SetActive(true);
            SetActiveTrail(false);
        }
    }

    private void CreateMirrorBall() {
        if (!_mirrorBall) {
            _mirrorBall = Instantiate(Resources.Load<MirrorBall>("Ingame/BallMirror"), transCache.parent);
        } else {
            _mirrorBall.transform.position = transCache.position;
            _mirrorBall.gameObject.SetActive(true);
            _mirrorBall.UpdateModelCharacter(_currentTryBallId);
        }

        GameItems.instance.CreateMirrorBoxExplosion();
        _mirrorBall.SetTargetFollow(this, jumpTime, isJumpRight);
    }

    private void DestroyMirrorBall(bool isUseAnimation) {
        if (!_isInMirrorSection) {
            return;
        }

        if (_mirrorBall) {
            if (isUseAnimation) {
                _mirrorBall.Hide(CompleteDisableMirror);
            } else {
                _mirrorBall.gameObject.SetActive(false);
                CompleteDisableMirror();
            }
        }

        void CompleteDisableMirror() {
            _isInMirrorSection = false;
            if (_mirrorTrail) {
                _mirrorTrail.SetActive(false);
                SetActiveTrail(true);
            }
        }
    }

    private void EnableSpecialSectionMirror() {
        _isInMirrorSection = true;
        CreateMirrorBall();
        EnableMirrorTrail();
    }

    private GameObject _hyperBoostTrail;

    private void EnableHyperBoostTrail() {
        if (!_hyperBoostTrail) {
            _hyperBoostTrail = Instantiate(Resources.Load<GameObject>(ResourcesPath.HyperBoostTrail), transCache);
        }

        if (_hyperBoostTrail) {
            _hyperBoostTrail.SetActive(true);
            SetActiveTrail(false);
        }
    }

    private void DestroyHyperBoostTrail() {
        if (_hyperBoostTrail) {
            _hyperBoostTrail.SetActive(false);
            SetActiveTrail(true);
        }
    }

    private void SetActiveTrail(bool isActive) {
        if (isCharacter) {
            ballInstance.SetActiveTrail(isActive);
        } else {
            ballTrailer.trailTransCache.gameObject.SetActive(isActive);
        }
    }

    public void SetActiveSphereBallTrail(bool isActive) {
        ballTrailer.trailTransCache.gameObject.SetActive(isActive);
    }

    public (Transform clone, Transform root) CloneBeltWay() {
        Transform beltWayTransform = ballInstance.BeltWay;
        var obj = Instantiate(beltWayTransform, beltWayTransform.parent);
        return (obj, beltWayTransform);
    }

    private void StartSpecialSectionHyperBoost() {
        _isFlyingUp = true;
        _isFlyingDown = false;
        GameItems.instance.CreateHyperBoostExplosion();
        LockGravity();
        SetVelocity(Vector3.zero);
        _maxHeightBoostSection = remoteConfig.NewElements_HyperBoost_FlyHeight;

        float offset = _destinationObj.transCache.position.z - _cachePosition.z;
        float standard = _maxHeightBoostSection * forwardSpeed / Platform.GravityEarth;
        if (offset < standard) {
            _gravityScaleHyperBoost = standard / offset;
        } else {
            _gravityScaleHyperBoost = 1;
        }

        if (_gravityScaleHyperBoost < 0) {
            _gravityScaleHyperBoost = 1;
        }

        BoosterManager.CheckUseHyperStar(Spawner.s.currentJumpNoteID);

        if (!isInHyperBoostSection) {
            this.PostEvent(EventID.HyperSectionStart);
        }
        isInHyperBoostSection = true;
    }

    private void EndSpecialSectionHyperBoost() {
        _isFlyingUp = false;
        _isFlyingDown = true;
        float offset = _destinationObj.transCache.position.z - _cachePosition.z;
        float standard = _cachePosition.y * forwardSpeed / Platform.GravityEarth;
        if (offset < standard) {
            _gravityScaleHyperBoost = standard / offset;
        } else {
            _gravityScaleHyperBoost = 1;
        }

        if (_gravityScaleHyperBoost < 0) {
            _gravityScaleHyperBoost = 1;
        }
    }

    private void OffSpecialSectionHyperBoost() {
        bool isChangeStatus = isInHyperBoostSection;
        isInHyperBoostSection = false;
        _isFlyingDown = false;
        _isFlyingUp = false;
        
        if (isChangeStatus) {
            this.PostEvent(EventID.HyperSectionEnd);
        }
    }

    private void UpdateBallSpecialSectionHyperBoost() {
        _cachePosition = transCache.position;
        if (_isFlyingUp) {
            _cachePosition.y += Platform.GravityEarth * _gravityScaleHyperBoost * _deltaTime; // bay lên
            if (_cachePosition.y >= _maxHeightBoostSection) {
                _cachePosition.y = _maxHeightBoostSection;
                _isFlyingUp = false;
            }

            transCache.position = _cachePosition;
        } else if (_isFlyingDown) {
            // hạ player 
            float offset = _destinationObj.transCache.position.z - _cachePosition.z;
            float standard = _cachePosition.y * forwardSpeed / Platform.GravityEarth;
            if (offset < standard) {
                _cachePosition.y -= Platform.GravityEarth * _gravityScaleHyperBoost * _deltaTime; // hạ xuống
                transCache.position = _cachePosition;
            }
        }

        if (!_isFlyingDown && _cachePosition.z >= _destinationObj.transCache.position.z) {
            _destinationObj.TriggerWithBall();
            if (!_destinationObj.IsNoteHyperBoost()) {
                EndSpecialSectionHyperBoost();
            }
        }
    }

    private void StartSpecialSectionZicZac() {
        isInZicZacSection = true;
        _isFlyingUp = true;
        _gravityScaleHyperBoost = 1f;
        _maxHeightBoostSection = 0.5f;
        _timeInSpecialSection = 0f;
        LockGravity();
        SetVelocity(Vector3.zero);
        var position = transCache.position;
        Spawner.s.OnSlideZicZac(true, position);
        GameItems.instance.ActiveVFXSlide(true, position);
    }

    private void OffSpecialSectionZiczac() {
        isInZicZacSection = false;
        GameItems.instance.ActiveVFXSlide(false, transCache.position);
    }

    private void UpdateBallSpecialSectionZicZac() {
        _cachePosition = transCache.position;
        _timeInSpecialSection += Time.deltaTime;
        if (_isFlyingUp) {
            _cachePosition.y += Platform.GravityEarth * _gravityScaleHyperBoost * _deltaTime; // bay lên
            if (_cachePosition.y >= _maxHeightBoostSection) {
                _cachePosition.y = _maxHeightBoostSection;
                _isFlyingUp = false;
            }

            transCache.position = _cachePosition;
        }

        if (_destinationObj != null && _cachePosition.z >= _destinationObj.transCache.position.z) {
            _destinationObj.TriggerWithBall(true);
        }

        GameItems.instance.UpdatePositionVFXSlide(_cachePosition);
        Spawner.s.RollinZicZacTile(_timeInSpecialSection, _cachePosition);
    }

    #endregion

    private bool _inPreviewRotate;

    public void PlayRandomAnimation() {
        if (!ballInstance) {
            return;
        }

        if (ballInstance.PlayRandomAnimation()) {
            ballAnimator.SetTrigger(Animator.StringToHash("PopHigh"));
        }
    }

    public void StartViewRotate() {
        _inPreviewRotate = true;
        if (!_isHalloweenBall) {
            ballInstance.SetChildRotation(new Vector3(0, 180, 0));
        } else {
            ballInstance.SetRotation(new Vector3(0, 180, 0));
        }
    }

    public void EndViewRotate() {
        _inPreviewRotate = false;
    }

    private string _albumName;
    private int    _initIdBall = -1;
    private bool   _useForceBallAlbum;
    private bool   _useForceBallTheme;

    public void GamePrepare(bool isReplay, Song song) {
        tryBallId = -1;
        _useForceBallTheme = false;
        _useForceBallAlbum = false;
        if ((isReplay && !string.IsNullOrEmpty(_albumName)) ||
            AnalyticHelper.GetSongPlayType(song) is SONG_PLAY_TYPE.discover_album or SONG_PLAY_TYPE.song_of_the_day) {
            if (!isReplay) {
                _albumName = song.song_play_type_detail;
            }

            _initIdBall = SongCards.instance.GetIdBallForAlbum(_albumName);
            if (_initIdBall < 0) {
                _albumName = string.Empty;
            } else {
                _useForceBallAlbum = true;
                Logger.EditorLog("Album Force Ball", $"From album: {_albumName} -> ballId: {_initIdBall}");
            }
        } else {
            _initIdBall = -1;
            _albumName = string.Empty;
        }

        if (_initIdBall < 0) {
            _albumName = string.Empty;
            _initIdBall = ThemeManager.GetCharacterByCurrentTheme();
            if (_initIdBall >= 0) {
                _useForceBallTheme = true;
            }
        }

        if (_initIdBall < 0) {
            _initIdBall = Configuration.GetSelectedBall();
        }

        UpdateBall(_initIdBall, timeout: 3);
    }

    public bool UsingForceBallTheme() {
        return _useForceBallTheme;
    }

    public bool UsingForceBallAlbum() {
        return _useForceBallAlbum;
    }
}