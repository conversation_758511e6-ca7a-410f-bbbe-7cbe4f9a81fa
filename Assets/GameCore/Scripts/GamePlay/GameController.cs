using System;
using UnityEngine;
using System.Collections;
using System.Threading;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using DG.Tweening;
using GameCore.LiveEvent.MysteryDoor;
using GamePlay.Levels;
using Newtonsoft.Json;
using TilesHop.LiveEvent;
using Random = UnityEngine.Random;
using Sirenix.OdinInspector;
using TileHop.EventDispatcher;
using TilesHop.ChallengeOldUser;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Pooling;
using TilesHop.GameCore;
using TilesHop.LiveEvent.MilestoneEvent;
using Utils = Inwave.Utils;

[Serializable]
public enum GameStatus {
    DIE    = 0,
    P_DIE  = 1,
    LIVE   = 2,
    REWIND = 3,
}

public partial class GameController : SingletonV2<GameController> {
    #region Fields

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Static ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public static bool enableEndless;
    public static bool IsPlaying = true;

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ HideInInspector ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    [Music.ACM.ReadOnly] public GameStatus game = GameStatus.DIE;
    [HideInInspector]    public int        maxScore;
    [HideInInspector]    public int        perfectCountMax = 0;
    [HideInInspector]    public int        perfectCount    = 0;
    public                      int        continueCount   = 0;
    [HideInInspector] public    int        stars;
    [HideInInspector] public    float      pauseTime;
    [HideInInspector] public    Transform  objectRotate;
    [HideInInspector] public    bool       isUpdateSensitive;
    [HideInInspector] public    string     location = LOCATION_NAME.gameplay.ToString();

    public static string songReviveType;
    public static bool isInstanced { get; private set; }

    public Action onGamePrepare;

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Public ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public                   ArrayList gameSubcribers;
    [NonSerialized]   public float     timePlay;
    [HideInInspector] public bool      hasBestScore = false;

    public int _freeReviveVipUsed = 0;

    private static int? _gameCount;

    public static int gameplayCount {
        get {
            if (_gameCount != null)
                return (int) _gameCount;

            _gameCount = PlayerPrefs.GetInt("gameplay_count", 0);
            return (int) _gameCount;
        }
        set {
            _gameCount = value;
            PlayerPrefs.SetInt("gameplay_count", value);
        }
    }

    public int BestStars => _highestStar;
    public int BestCrowns => _highestCrown;
    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Private ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    private bool _isSavedScore;
    public bool IsSavedScore => _isSavedScore;
    private RemoteSecureInt _score = new RemoteSecureInt();

    public int Score {
        get => _score.GetValue();
        private set => _score.SetValue(value);
    }

    [ShowInInspector]           private Dictionary<CurrencyEarnSource, int> _earnedDiamond;
    [ShowInInspector, ReadOnly] private int                                 _totalDiamond;

    [ShowInInspector] private IngameSectionType _sectionType = IngameSectionType.Normal;
    public static event Action<IngameSectionType> OnChangeSectionType;

    public IngameSectionType sectionType {
        get => _sectionType;
        set {
            _sectionType = value;
            OnChangeSectionType?.Invoke(value);
            Logger.EditorLog("Section Type: " + value);
        }
    }

    private int                  _ateMileStone    = 0;
    private int                  _mileStone       = 0;
    private Dictionary<int, int> _mileStoneValues = new Dictionary<int, int>();

    private                float   _destination     = 0;
    private                int     _lastEndlessMode = 0;
    private                float   _fingerSpeed;
    private                bool    _fireSongEnd    = false;
    private                bool    _songStartFirst = false;
    private                bool    _isAutoPlay     = false;
    [NonSerialized] public Song    _song;
    private                Vector2 _pos;
    private                float   _centerX;
    private                Vector2 _lastPosMouse;
    private                Vector2 _lastPosBall;
    private                bool    _isMouseButton;
    private                bool    _isShowedContinue;
    public                 bool    needToReloadMp3 = false;
    public bool IsWaitingFlyDiamondVFXFromPrgogressbar { private set; get; } = false;
    internal Camera mainCamera;
    private RemoteConfig remoteConfig => RemoteConfigBase.instance;

    private bool _isFollowPlayerInput = true;
    private int  _highestStar;
    private int  _highestCrown;

    public int LastAddStars { get; private set; }
    public int HighestMileStone { get; private set; }

    [ShowInInspector] private Dictionary<TokenType, int> _dictToken = new();

    public Dictionary<TokenType, int> DictToken => _dictToken;

    /// <summary>
    /// % tiến trình bài hát đã chơi được [0,1]
    /// </summary>
    public float Progress =>
        Ball.b.endlessModeCount > 0 ? 1 : ((float) Spawner.s.currentJumpNoteID / NotesManager.instance.noteCount);

    public static event Action<int> OnChangeDiamondCollected;
    public static event Action<string, int> OnEarnStar;
    public static event Action<int> OnUpdateExp;

    private bool canShowInterstitial;
    [HideInInspector] public List<NoteData> animationNotes => NotesManager.instance.animationNotesDatas;
    public bool IsTutorial => _song.isTutorialSong;

    [Music.ACM.ReadOnly] public TutorialGamePlayConfig _tutorialGamePlayConfig;
    public bool IsNewTutorialGamePlay => _tutorialGamePlayConfig != null && IsTutorial;

    [HideInInspector] public Platform passedTile;
    public int LastEndlessMode => _lastEndlessMode;

    public int currentAnimation = 0;

    private Vector3 _lastBallPos = Vector3.zero;
    private Vector3 _currentBallPos;
    private float   _speed = 20f;

    private string _eventSongForceClose = null;
    private float  _songStartTime;

    public int TotalEarnDiamond {
        get { return _totalDiamond; }
    }

    public bool isContinueEndless = false;

    public bool isAlreadyCompletedSong;

    public bool isShowingFsAd { get; private set; } = false;
    public static bool isSkipAffectStreak => enableEndless || (isInstanced && instance.isAlreadyCompletedSong);

    public bool isSkipAffectGalaxyQuest {
        get {
            if (enableEndless) {
                return ChallengeMode.IsActive && ChallengeMode.IsAcceptFromNormal && isAlreadyCompletedSong;
            }

            return isAlreadyCompletedSong;
        }
    }

    #endregion

    #region Properties

    public bool canLoseCurrenciesOnFail {
        get {
            bool canLoseCurrencies = remoteConfig.Hybrid_LoseGemsOnFailed && stars < 3;
            if (!canLoseCurrencies) {
                // Trường hợp win
                return false;
            }

            // fail ở endless (original endless hoặc challenge mode - CM)
            if (enableEndless) {
                // nếu chơi một mạch từ đầu sang CM thì vẫn collect được rewards do ở phase chuyển tiếp sang CM chưa save
                if (ChallengeMode.IsAcceptFromNormal) {
                    return false;
                }
                
                // nếu chơi CM từ đầu, cần 3 crowns (3 stars trong code) để win rewards
                if (ChallengeMode.IsActive) {
                    return true;
                }

                // original endless mode không có định nghĩa thắng => không lose rewards
                return false;
            }
            
            // Trường hợp fail ở normal mode
            return true;
        }
    }

    #endregion
    
    #region Unity method

    protected void Awake() {
        isInstanced = true;
        Util.EnableCheckDualUnlock = true;
        Configuration.instance.SetGamePlayTutorial();
        gameSubcribers = new ArrayList();
        _song = NotesManager.instance.song;
        _isAutoPlay = Configuration.instance.isAutoPlay;
        _indexTime = 0;
        if (IsTutorial) {
            string remoteTutorial = RemoteConfigBase.instance.onboarding_tutorial_gameplay;
            try {
                _tutorialGamePlayConfig = JsonUtility.FromJson<TutorialGamePlayConfig>(remoteTutorial);
            } catch (Exception) {
                // ignored
            }

            passedTile = null;
        } else {
            _tutorialGamePlayConfig = null;
        }

        TransitionReverseScript.onStartReversing += OnStartReversing;
        TransitionReverseScript.onEndReversing += OnEndReversing;
    }

    private List<Transform> _childsOfCamera;

    private void Start() {
        mainCamera = Camera.main;
        if (NotesManager.IsExistUpsideDown() && mainCamera != null) {
            _childsOfCamera = mainCamera.transform.GetChilds();
        }

        if (remoteConfig.ImproveSensitive_IsEnable &&
            (_song.isTutorialSong || Configuration.instance.isOpenSensitiveFromHome)) {
            TopBar.instance.ShowSensitiveTool(_song.isTutorialSong && !_song.IsPlayed());
            isUpdateSensitive = true;
        }

        if (SuperpoweredSDK.instance.IsReady()) {
            GamePrepare(false);
        } else {
            Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("CANT_LOAD_SONG"), 1);
        }

        Ball.OnChangeStageForceMove += Ball_OnChangeStageForceMove;
        if (_song.isTutorialSong) {
            GroundMusic.CurrentPreviewSong = null;
        }

        Resources.UnloadUnusedAssets();
        CustomException.SetKey(FirebaseKey.CurrentTheme, ThemeManager.GetPlayingThemeId().ToString());
        PreviewSongController.instanceSafe.ClearStack();
    }

    private void OnEnable() {
        Ball.OnChangeEndlessRound += OnChangeEndlessRound;
        Platform.OnHit += PlatformOnOnHit;
        if (MysteryDoorManager.EnableFeature) {
            MysteryDoorManager.CollectDrill += MysteryDoorManagerOnCollectDrill;
        }

        if (MilestoneEvent.isAvailable) {
            MilestoneEvent.instanceSafe.OnFinishCollecting += HandleOnFinishCollectingMilestoneToken;
        }
    }

    private void OnDisable() {
        Ball.OnChangeEndlessRound -= OnChangeEndlessRound;
        Platform.OnHit -= PlatformOnOnHit;
        if (MysteryDoorManager.EnableFeature) {
            MysteryDoorManager.CollectDrill -= MysteryDoorManagerOnCollectDrill;
        }

        if (MilestoneEvent.isAvailable) {
            MilestoneEvent.instanceSafe.OnFinishCollecting -= HandleOnFinishCollectingMilestoneToken;
        }
    }

    private void OnDestroy() {
        isInstanced = false;
        Ball.OnChangeStageForceMove -= Ball_OnChangeStageForceMove;
        TransitionReverseScript.onStartReversing -= OnStartReversing;
        TransitionReverseScript.onEndReversing -= OnEndReversing;

        // Kill all DOTween instances to prevent memory leaks and null reference exceptions
        DOTween.Kill(gameObject);

        // Note: GetCancellationTokenOnDestroy() automatically cancels when GameObject is destroyed
        // No manual cancellation needed - UniTask framework handles this
    }

    private void Update() {
        if (game == GameStatus.LIVE && !Ball.b.isPausePlayer && !isSensitiveClosing) {
            if (Input.GetMouseButtonDown(0)) {
                //Resume();
                _pos = Input.mousePosition;
                if (_isReversing) {
                    _pos.x = -_pos.x;
                }

                _centerX = Ball.b.transCache.position.x;
            } else if (Input.GetMouseButtonUp(0)) {
                if (IsShouldPause() && !isUpdateSensitive) {
                    Pause();
                } else if (Ball.b.isHumanPlayer && RemoteConfigBase.instance.BallRotateFollowFinger) {
                    objectRotate.localRotation = Quaternion.LookRotation(Vector3.zero);
                }
            }

            _isMouseButton = Input.GetMouseButton(0);
            timePlay += Time.deltaTime * Ball.b.timeScale;
            CheckAnimation();
            LogTimePlayTutorial(timePlay);
        }
    }

    private void FixedUpdate() {
        if (game == GameStatus.LIVE && !Ball.b.isPausePlayer && IsPlaying) {
            if (_isMouseButton && _isFollowPlayerInput) {
                _tempMousePosition = Input.mousePosition; // Use pre-declared field
                if (_isReversing) {
                    _tempMousePosition.x = -_tempMousePosition.x;
                }

                _destination = _centerX + (_tempMousePosition.x - _pos.x) * _fingerSpeed;
                _tempPosition = Ball.b.transCache.position; // Use pre-declared field
                _tempDeltaMove = Ball.b.timeScale * (_destination - _tempPosition.x) / // Use pre-declared field
                                 RemoteConfigBase.instance.FingerSensitivity;
                _tempDeltaMove *= (Spawner.s.bpm / 100f);

                //if (enableEndless || Spawner.s.currentJumpNoteID != 0 || score == 0) {
                if (RemoteConfigBase.instance.FingerMaxX < 1 || Mathf.Abs(_tempPosition.x + _tempDeltaMove) <
                    RemoteConfigBase.instance.FingerMaxX) {
                    _tempPosition.x += _tempDeltaMove;
                    _lastPosMouse = _pos;
                } else {
                    // di chuyển quá nhanh, cân bằng lại
                    _tempPosition = Util.SetPositionX(_tempPosition,
                        RemoteConfigBase.instance.FingerMaxX * Mathf.Sign(_tempPosition.x));
                    _centerX -= _tempDeltaMove - (_tempPosition.x - _lastPosBall.x);
                    _pos = _lastPosMouse;
                }

                _lastPosBall = _tempPosition;
                Ball.b.transCache.position = _tempPosition;

                if (Ball.b.isHumanPlayer && RemoteConfigBase.instance.BallRotateFollowFinger) {
                    UpdatePositionFollowMouse();
                }

                Ball.b.Rotate(_tempDeltaMove);
            }
            // else if (IsAutoPlay()) {
            //     Ball.b.Rotate(0);
            // }
        }
    }

    private void CheckOnboardingNewTile() {
        if (!NotesManager.instance.HasElementType()) {
            return;
        }

        if (!remoteConfig.NewElements_Onboarding_Enable) {
            return;
        }

        var listElement = NotesManager.instance.GetAvailableElementTypes();
        IntroducingTileElementsHolder.ShowIntroducing(listElement);
    }

    private void UpdatePositionFollowMouse() {
        _currentBallPos = Ball.b.transCache.position;
        _currentBallPos.y = 0f;
        if (_lastBallPos != Vector3.zero) {
            Vector3 direct = _currentBallPos - _lastBallPos;

            objectRotate.localRotation = Quaternion.Slerp(objectRotate.localRotation, Quaternion.LookRotation(direct),
                _speed * Time.deltaTime);
            _lastBallPos = _currentBallPos;
        } else
            _lastBallPos = _currentBallPos;
    }

    private void OnApplicationPause(bool pauseStatus) {
        if (pauseStatus) {
            CacheSongForceClose();
        } else {
            UnCacheSongForceClose();
        }

        if (Application.platform == RuntimePlatform.WindowsEditor ||
            Application.platform == RuntimePlatform.OSXEditor) {
            if (pauseStatus) {
                CheckPauseGame();
            }
        }
    }

    private void OnApplicationFocus(bool hasFocus) {
        if (Application.platform != RuntimePlatform.WindowsEditor &&
            Application.platform != RuntimePlatform.OSXEditor) {
            if (!hasFocus) {
                CheckPauseGame();
            }
        }
    }

    #endregion

    #region GameSubscriber

    //---------------------------------------------------
    delegate void EventCall(GameSubscriber s);

    private void CallEvent(EventCall call) {
        foreach (GameSubscriber s in gameSubcribers) {
            if (s != null) {
                call(s);
            }
        }
    }

    public void addSubcriber(GameSubscriber s) {
        gameSubcribers.Add(s);
    }

    //--------------------------------------

    #endregion

    public void CheckSaveData() {
        if (_isSavedScore || Score == 0) {
            return;
        }

        SaveScoreAndDiamond(); //force save score when interrup result screen
    }

    private void CacheSongForceClose() {
        Dictionary<string, object> param = AnalyticHelper.GetDefaultParam(location: location);
        AnalyticHelper.UpdateBasicSongParams(_song, param);
        AnalyticHelper.UpdateNetWorkState(param);

        int progress = GetCurrentProgress();
        param.Add(TRACK_NAME.song_progress, progress); //song_progress
        param.Add(TRACK_NAME.song_score, Score); //song_score
        param.Add(TRACK_NAME.song_last_note, Spawner.s.currentJumpNoteID); //song_last_note
        param.Add(TRACK_NAME.song_play_time, AnalyticHelper.instance.timePlayManager.GetPlayDuration());
        param.Add(TRACK_NAME.song_mode, enableEndless ? "endless" : "normal");
        param.Add(TRACK_NAME.revive_count, _song.GetReviveCount());
        param.Add(TRACK_NAME.audiodevice_info, Utils.GetAudioDeviceInfor());

        AnalyticHelper.AddGamePlayCount(param);
        AnalyticHelper.AddNewElement(param);
        AnalyticHelper.AddSongObstacleType(param); //song_force_close

        _eventSongForceClose = JsonConvert.SerializeObject(param);
        PlayerPrefs.SetString(PlayerPrefsKey.SongForceClose, _eventSongForceClose);
        //Logger.Log("song_force_close cache", _eventSongForceClose);
    }

    private void UnCacheSongForceClose() {
        if (_eventSongForceClose == null) {
            return;
        }

        _eventSongForceClose = null;
        PlayerPrefs.DeleteKey(PlayerPrefsKey.SongForceClose);
        //Logger.Log("song_force_close cache", "deleted");
    }

    private void CheckAnimation() {
        if (currentAnimation < animationNotes.Count && game == GameStatus.LIVE) {
            NoteData animationNote = animationNotes[currentAnimation];
            if (timePlay >= animationNote.timeAppear) {
                PlayAnimation(animationNote.nodeID);
                currentAnimation++;
            }
        }
    }

    private void PlayAnimation(int nodeID) {
        if (GameItems.instance.vfxController != null) {
            GameItems.instance.vfxController.PlayAnimationByNoteID(nodeID);
        }
    }

    private bool IsShouldPause() {
        if (IsNewTutorialGamePlay) {
            return false;
        }

        if (!Configuration.instance.isGamePlayTutorial)
            return false;
        if (stars >= 3)
            return false;

        if (Spawner.s.currentJumpNoteID >= NotesManager.instance.noteCount - 1) {
            //TH-1194: ẩn star trên đường đi của tutorial -> user không thể đạt 3 sao -> check complete bằng currentJumpNoteID
            return false;
        }

        return enableEndless || Spawner.s.currentJumpNoteID != 0 || Score == 0;
        //bool isPause = Configuration.instance.isGamePlayTutorial &&
        //               (enableEndless || Spawner.s.currentJumpNoteID != 0 || score == 0) && stars < 3;
        //return isPause;
    }

    private void Ball_OnChangeStageForceMove(bool isForceMove) {
        _isFollowPlayerInput = !isForceMove;
        if (!isForceMove) {
            // khi kết thúc forcemove => lấy lại tâm move
            _pos = Input.mousePosition;
            _centerX = Ball.b.transCache.position.x;
        }
    }

    private void CheckPauseGame() {
        if (game != GameStatus.LIVE) {
            return;
        }

        if (GameSessionManager.Instance != null && !NotesManager.instance.song.IsLocalSong()) {
            GameSessionManager.Instance.PauseGame(Reason.SystemPause);
        }

        Pause();

        if (RemoteConfigBase.instance.PauseQuitAP_PauseButton_IsEnable) {
            // event tracking
            var param = new Dictionary<string, object>() {
                {"source", "minimize_ap"}
            };
            AnalyticHelper.LogEvent("pause_ap_click", param);
        }
    }

    public static bool GetEndlessModeOfSong(bool isCheckSelectOfUser = true) {
        var song = NotesManager.instance.song;
        bool isEndless = Configuration.IsEndlessModeEnable(song.path);

        if (!isEndless && RemoteConfigBase.instance.NotesDifficult_IsEnable) {
            isEndless = IsUnlockEndlessModeByPlayer(song);
        }

        if (RemoteConfigBase.instance.NotesDifficult_IsEnable && MultiDifficultUIScript.instance != null &&
            isCheckSelectOfUser) {
            NotesManager.Difficulties selectedDifficult = MultiDifficultUIScript.instance.GetSelectedDifficult();
            if (selectedDifficult != NotesManager.Difficulties.None) {
                isEndless = selectedDifficult == NotesManager.Difficulties.Endless;
            }
        }

        if (Configuration.instance.enableContentTool) {
            isEndless = false;
            Configuration.instance.EndlessMode_Type = EndlessModeType.SpeedUpFromRound1;
        } else {
            Configuration.instance.EndlessMode_Type =
                isEndless ? EndlessModeType.SpeedUpFromRound0 : EndlessModeType.SpeedUpFromRound1;
        }

        if (RemoteConfigBase.instance.ImproveSensitive_IsEnable && CheckInstanced() && instance.isUpdateSensitive) {
            isEndless = true;
        }

        CheckChallengeMode(ref isEndless, song);

        enableEndless = isEndless;
        return isEndless;
    }

    public void BackToHome() {
        Util.GoHome();
    }

    public void SaveScoreAndDiamond(bool saveCollectedData = true) {
        if (_isSavedScore) {
            return;
        }

        SaveEarnedGems();
        if (saveCollectedData) {
            SaveEarnedToken();
        }

        SaveScoreAndStar();
        _isSavedScore = true;
    }

    private void SaveScoreAndStar() {
        _song.LastestStar = Mathf.Clamp(stars, 0, 3);
        _song.LastestScore = Score;
        _song.LastestTile = GetCurrentExp();

        int highestScore = Configuration.GetBestScore(_song.path);
        hasBestScore = highestScore < Score;
        if (hasBestScore) {
            _song.bestScore = Score;
            CoreUser.instance.ReportScore(Score, _song.path);
        }

        ChallengeMode.CheckCompleteSong(_song, stars, enableEndless);
        bool hasBestStar = _highestStar < stars;
        if (remoteConfig.ResultScreen_IsShowCrownEndless && enableEndless) {
            int crown = stars;
            bool hasBestCrown = _highestCrown < crown;
            if (hasBestCrown) {
                _song.bestCrown = crown;
                int earnedCrown = Mathf.Clamp(crown - _highestCrown, 0, 3);
                Configuration.instance.EarnCrowns(earnedCrown);
            }
            LastAddStars = 0;
        } else {
            if (hasBestStar) {
                LastAddStars = Mathf.Clamp(stars - _highestStar, 0, 3);
                _song.bestStar = stars;
            } else {
                LastAddStars = 0;
            }

            if (DiscoveryChallengeManager.IsEnable()) {
                DiscoveryChallengeManager.instanceSafe.GameComplete(_song);
            }
        }

        if (_song.LastestTile != 0) {
            OnUpdateExp?.Invoke(_song.LastestTile);
            _song.totalTile += _song.LastestTile;
            Configuration.instance.EarnTiles(_song.LastestTile);
        }

        if (!IsTutorial && LastAddStars > 0 && !string.IsNullOrEmpty(_song.path) && !enableEndless) {
            OnEarnStar?.Invoke(_song.acm_id_v3, LastAddStars);
            Configuration.instance.EarnStars(LastAddStars);
        }

        if (_mileStone > HighestMileStone) {
            Configuration.SetBestMileStone(_mileStone, _song.path);
        }

        PlayerData playerData = CoreData.GetPlayerData();

        //leaderboard daily
        int bestScoreOfDay = playerData.GetBestScoreOfDay(_song.path);
        if (bestScoreOfDay < Score) {
            playerData.SetBestScoreOfDay(_song.path, Score, isAutoSave: false);
        }

        //leaderboard weekly
        int bestScoreOfWeek = playerData.GetBestScoreOfWeek(_song.path);
        if (bestScoreOfWeek < Score) {
            playerData.SetBestScoreOfWeek(_song.path, Score, isAutoSave: false);
        }

        //TH-1176: cache previous star to tracking economy
        if (stars >= 3 && enableEndless) {
            _song.previousStar = "endless";
        } else {
            _song.previousStar = stars.ToString();
        }

        playerData.AddRecentSong(_song, false);
        playerData.Save();
    }

    private void SaveEarnedGems() {
        if (_acceptChallengeMode) {
            return;
        }
        
        if (canLoseCurrenciesOnFail) {
            _earnedDiamond?.Clear();
            _totalDiamond = 0;
            return;
        }

        if (_earnedDiamond != null) {
            if (_earnedDiamond.ContainsKey(CurrencyEarnSource.milestone_)) {
                int collectFromMileStone = 0;
                foreach (var mileStone in _mileStoneValues) {
                    if (mileStone.Value <= 0)
                        continue;

                    collectFromMileStone += mileStone.Value;
                    Configuration.UpdateDiamond(mileStone.Value, CurrencyEarnSource.COLLECT_DIAMOND.ToString(),
                        $"{CurrencyEarnSource.milestone_.ToString()}{mileStone.Key}");
                }

                _earnedDiamond.Remove(CurrencyEarnSource.milestone_);
            }

            if (_earnedDiamond.ContainsKey(CurrencyEarnSource.booster)) {
                Configuration.UpdateDiamond(_earnedDiamond[CurrencyEarnSource.booster],
                    CurrencyEarnSource.booster.ToString());
                _earnedDiamond.Remove(CurrencyEarnSource.booster);
            }

            int remain = 0;
            foreach (var value in _earnedDiamond.Values) {
                remain += value;
            }

            if (remain > 0) {
                Configuration.UpdateDiamond(remain, CurrencyEarnSource.COLLECT_DIAMOND.ToString(), location);
            }
        }
    }

    /// <summary>
    /// Normal mode: 3 sao mới được nhận drill
    /// Endless mode: nếu là CM thì luôn được nhận. nếu không thì cần pass round 1
    /// </summary>
    /// <returns></returns>
    private bool CanReceiveDrill() {
        if (enableEndless) {
            if (ChallengeMode.IsActive) {
                return true;
            }

            return Ball.b.isPassRound1;
        }

        return !remoteConfig.Hybrid_LoseGemsOnFailed || stars >= 3;
    }

    public void GamePrepare(bool isReplay, bool isAutoReplay = false) {
        if (!ChallengeMode.IsAcceptFromNormal) {
            isAlreadyCompletedSong = _song is {bestStar: >= 3};
        }

        if (isReplay) {
            if (_song != null) {
                _song.song_play_type_detail = null;
            }
        } else {
            if (enableEndless && _song is {isTutorialSong: false, isTutorialOnlineSong: false}) {
                EndlessIteration.TryActive();
            }
        }

        if (!isAutoReplay) {
            SuperpoweredSDK.instance.PauseMusic();
            SoundManager.PlayStartSong();
        }

        GroundMusic.instance.StopMusic();
        if (!ChallengeMode.IsActive) {
            // must call for replay to original endless mode
            GetEndlessModeOfSong();
        }
        ResetFixedDeltaTime();
        sectionType = IngameSectionType.Normal;
        
        if (_song != null) {
            _song.ResetIngameData();
            if (!(EndlessIteration.IsActive && isContinueEndless)) {
                stars = 0;
                Score = 0;
            }

            _highestStar = _song.bestStar;
            _highestCrown = _song.bestCrown;

            Ball.b.GamePrepare(isReplay, _song);
        }

        if (!isReplay) {
            Configuration.LastPlayedSongOldHighestStars = _highestStar;
        }

        UpdateHighestMilestone();
        _ateMileStone = 0;
        _mileStone = HighestMileStone;
        canShowInterstitial = true;

        _isSavedScore = false;
        _earnedDiamond ??= new Dictionary<CurrencyEarnSource, int>(); 
        
        if(!ChallengeMode.IsAcceptFromNormal) {
            _earnedDiamond.Clear();
            _totalDiamond = 0;
            _mileStoneValues.Clear();
            _dictToken.Clear();
            _dictTokenEndless.Clear();
            if (UIController.ui.gameover is GameCompletedUI7 gameOverUI7) {
                gameOverUI7.ClearTokenItems();
            }
        }
        
        _lastEndlessMode = 0;
        _freeReviveVipUsed = 0;
        game = GameStatus.DIE;
        continueCount = 0;
        _isShowedContinue = false;
        perfectCount = 0;
        maxScore = GetMaxScore(NotesManager.instance.noteCount - 1);
        _isReversing = false;

        if (objectRotate != null) {
            objectRotate.localRotation = Quaternion.LookRotation(Vector3.zero);
        }

        int[] diamondLineRandom = remoteConfig.LongTile_RangeRandom;
        if (diamondLineRandom != null && diamondLineRandom.Length != 0) {
            remoteConfig.LongTile_IndexLine = diamondLineRandom[Random.Range(0, diamondLineRandom.Length)];
        }

        currentAnimation = 0;
        if (isReplay && !isAutoReplay) {
            FireSongEvent(SONG_STATUS.song_replay);
            _countRestart++;
            if (NotesManager.instance.Difficulty == NotesManager.Difficulties.Endless) {
                bool changeToMidi = NotesManager.instance.ChangeToMidi();
                if (changeToMidi) {
                    MapManager.instance.InitStageLevelManager();
                    //MapManager.instance.CreatePlatformDatas(true);
                    Spawner.s.ChangeNoteDatas();
                }
            }

            if (TrySkinFeature.IsActive) {
                MapManager.instance.CreatePlatformDatas(true);
                GameItems.instance.InitTrySkin();
            }
        } else {
            if (remoteConfig.ImproveSensitive_IsEnable && remoteConfig.ImproveSensitive_UseNewNotes &&
                isUpdateSensitive) {
                NotesManager.instance.ChangeToSensitiveNotes();
                Spawner.s.ChangeNoteDatas();
            }

            if (GameItems.instance.edmController != null)
                GameItems.instance.edmController.ChangeContentFollowTimeline();
            if (GameItems.instance.hiphopModernController != null)
                GameItems.instance.hiphopModernController.ChangeContentFollowTimeline();
        }

        GameItems.instance.ballTrailer.Bind();

        GameItems.instance.currentBallManagerScript.transform.parent.gameObject.SetActive(false);

        if (!isAutoReplay) {
            FireSongEvent(SONG_STATUS.song_ap);
        }

        ToggleOverlay(true);
        IEShowPrepare(this.GetCancellationTokenOnDestroy()).Forget();
        if (TopBar.instance) {
            TopBar.instance.GamePrepare();
        }

        Spawner.s.gamePrepare();
        UIController.ui.gamePrepare();

        if (BoosterManager.isInstanced) {
            BoosterManager.instanceSafe.GamePrepare();
        }

        NotesManager.instance.numberNoteInRound = NotesManager.instance.noteCount;
        if (!isAutoReplay) {
            CoreData.GetPlayerData().IncreaseCountSongPlayInDay(false);
        }

        if (!remoteConfig.TutorialSong_IsMustComplete && Configuration.instance.isTutorial) {
            Configuration.SetIntroOff();
        }

        onGamePrepare?.Invoke();

        if (remoteConfig.FPSTracker_Enable) {
            StartTrackFPS(this.GetCancellationTokenOnDestroy()).Forget();
        }

        if (remoteConfig.OnboardingFlow_EnableProgressBar && remoteConfig.Onboarding_Ingame_Progressbar_Enable &&
            Configuration.GetGameLevel() >= remoteConfig.Onboarding_Ingame_Progressbar_LevelAppear &&
            PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ProgressFirstStar, 0) == 0) {
            ShowNoticeProgressbar();
        }

        UIController.ui.InitPopupContinue();

        if (TransitionInOut.isInstanced) {
            TransitionInOut.instance.StopTransitionOut();
            var ballPosition = Ball.b.transCache.position;
            TransitionInOut.instance.TransitionIn(ballPosition + (mainCamera.transform.position - ballPosition) * 0.5f);
        }

        PlatformManager.ProcessTilesAppearType();

        CheckOnboardingNewTile();
    }

    private async UniTaskVoid IEShowPrepare(CancellationToken cancellationToken = default) {
        try {
            if (!BoosterManager.isInstanced) {
                UIController.ui.gameui.GamePrePare(isNewTutorial: IsNewTutorialGamePlay);
                return;
            }

            UIController.ui.gameui.PendingStart = true;

            if (!CanActiveStreakBox()) {
                PowerCubeManager.SetActive(false);
            } else {
                PowerCubeManager.SetActive(true);
            }

            BoosterManager.instanceSafe.Init(_song);

            await UniTask.Delay(100, cancellationToken: cancellationToken);
            await UniTask.WaitWhile(() => !BoosterManager.instanceSafe.initDone, cancellationToken: cancellationToken);

            List<BoosterType> onboardList = BoosterManager.instanceSafe.GetOnboardList();
            bool onboardPowerCube = PowerCubeManager.HasToast();

            if (!onboardList.IsNullOrEmpty() || onboardPowerCube) {
                //onboard!!!!
                IEShowToast(onboardList, onboardPowerCube, this.GetCancellationTokenOnDestroy()).Forget();
            }

            if (BoosterManager.instanceSafe.SetupIngameBooster()) {
                await UniTask.WaitWhile(() => !BoosterManager.instanceSafe.SetUpDone,
                    cancellationToken: cancellationToken);
            }

            //if(MysteryDoorManager.EnableFeature && MysteryDoorManager.instanceSafe.userState == MysteryDoorManager.State.Active) {
            //    var mysteryDoorNotify = Resources.Load<GameObject>("Popups/MysteryDoorIngameNotify");
            //    if(mysteryDoorNotify != null) {
            //        Instantiate(mysteryDoorNotify, UIController.ui.transform);
            //    }
            //}

            if (!onboardList.IsNullOrEmpty()) {
                foreach (var onboardItem in onboardList) {
                    BoosterTracking.BoosterOnboarding(onboardItem, BoosterTracking.OnboardState.ready);
                }
            }

            UIController.ui.gameui.PendingStart = false;
            UIController.ui.gameui.GamePrePare(isNewTutorial: IsNewTutorialGamePlay);
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    private bool CanActiveStreakBox() {
        if (_song.isTutorialSong) {
            return false;
        }

        if (ChallengeMode.IsActive) {
            return !isAlreadyCompletedSong || remoteConfig.ChallengeMode_PowerCube_ApplyCompleteSong;
        }

        if (enableEndless) {
            return false;
        }

        if (!Configuration.EnableEndlessMode() && isAlreadyCompletedSong) {
            return false;
        }

        return true;
    }

    private async UniTaskVoid IEShowToast(List<BoosterType> onboardList, bool toastStreak,
                                          CancellationToken cancellationToken = default) {
        try {
            if (!onboardList.IsNullOrEmpty()) {
                foreach (var onboardItem in onboardList) {
                    BoosterTracking.BoosterOnboarding(onboardItem, BoosterTracking.OnboardState.setup);
                    var onboardingTextPref = Resources.Load<UIOnboardingBoosterText>("Booster/OnboardingBoosterText");
                    if (onboardingTextPref) {
                        var obj = Instantiate(onboardingTextPref, UIController.ui.transform);
                        obj.SetType(onboardItem);
                        await UniTask.Delay(4000, cancellationToken: cancellationToken);
                    }
                }
            }

            if (toastStreak) {
                var onboardingTextPref = Resources.Load<UIOnboardingBoosterText>("Booster/OnboardingPowerCubeText");
                if (onboardingTextPref) {
                    var obj = Instantiate(onboardingTextPref, UIController.ui.transform);
                    PowerCubeManager.DoneShowToast();
                    await UniTask.Delay(4000, cancellationToken: cancellationToken);
                }
            }
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    public void UpdateHighestMilestone() {
        HighestMileStone = Configuration.GetBestMileStone(_song.path);
    }

    private async UniTaskVoid StartTrackFPS(CancellationToken cancellationToken = default) {
        try {
            await UniTask.Delay(200, cancellationToken: cancellationToken);

            FPSTracker.Instance.EnableTracker(FPSTracker.GAMECONTEXT_GAME_PREPARE);
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    public void ToggleOverlay(bool enable) {
        if (Follow.instance != null) {
            Follow.instance.ShowOverlay(enable);
        }
    }

    public void GameStart() {
        int indexLine = 0;

        // mark in order to distinguish economy users from users switching from ad-based to economy 
        if (Configuration.instance.isTutorial && RemoteConfigBase.instance.Economy_IsEnable) {
            PlayerPrefs.SetInt(CONFIG_STRING.ECONOMY_TUTORIAL, 1);
        }

        try {
            if (Spawner.s.isReloadDifficult) {
                return;
            }

            if (_isAutoPlay) {
                Screen.sleepTimeout = SleepTimeout.NeverSleep;
            }

            indexLine = 1;
            _pos = Input.mousePosition;
            _centerX = 0;
            game = GameStatus.LIVE;
            UpdateFingerSpeed();
            indexLine = 2;
            CallEvent((s) => { s.gameStart(); });

            indexLine = 3;
            Platform firstPlatform = Spawner.s.platformManager.GetFirstPlatform();
            if (firstPlatform == null) {
                Util.GoHome();
                return;
            }

            Ball.b.RunFirstBall(firstPlatform);

            indexLine = 4;
            if (GameItems.instance.edmController) {
                GameItems.instance.edmController.EnableTimeLine();
            }

            if (GameItems.instance.hiphopModernController) {
                GameItems.instance.hiphopModernController.EnableTimeLine();
            }

            if (GameItems.instance.vfxController) {
                GameItems.instance.vfxController.GameStart();
            }

            if (BoosterManager.isInstanced) {
                BoosterManager.instanceSafe.GameStart(continueCount == 0);
            }

            ShowVFXChallengeMode(Ball.b.timeScale);

            indexLine = 5;
            firstPlatform.Hit(0);
            indexLine = 6;
            if (continueCount > 0) {
                AnalyticHelper.instance.timePlayManager.ResumePlayTimeCount();
            } else {
                PlayerPrefs.SetInt(CONFIG_STRING.PlayCount, PlayerPrefs.GetInt(CONFIG_STRING.PlayCount, 0) + 1);
                if (!isUpdateSensitive) {
                    _songStartTime = Time.time;
                    gameplayCount++;
                    FireSongEvent(SONG_STATUS.song_start);
                    if (enableEndless && ChallengeMode.IsActive) {
                        ChallengeModeTracking.TrackPlayCMStart(_song);
                    }

                    indexLine = 7;
                    if (SubscriptionController.IsSubscription()) {
                        PlayerPrefs.SetInt(CONFIG_STRING.PlayAfterSubs, 1);
                    }

                    indexLine = 8;
                    TrySkinFeature.IncreaseCountSongToTrySkin(); //TH-978
                    indexLine = 9;
                    ChallengeOldUserController.CountSongStart(_song.acm_id_v3);
                    indexLine = 10;
                    if (!Configuration.instance.isTutorial) {
                        Util.SongStartInSession++;
                    }
                }
            }

            indexLine = 11;
            if (remoteConfig.FPSTracker_Enable) {
                FPSTracker.Instance.DisableTracker();
                FPSTracker.Instance.EnableTracker(FPSTracker.GAMECONTEXT_GAME_PLAY);
            }
        }
        catch (Exception e) {
            CustomException.Fire("[GameStart]",
                $"IndexLine:{indexLine}. Message => {e.Message}; Song => {_song.name}; acm_id => {_song.GetKeyOfSong()}; {e.StackTrace}");
        } finally {
            this.PostEvent(EventID.GameStart);
        }
    }

    public void ShowNoticeProgressbar() {
        TopBar.instance.ShowProgressbarNotice();
    }

    public void UpdateFingerSpeed() {
        float screenWith = RemoteConfigBase.instance.FingerSpeedScreenFix > 0
            ? RemoteConfigBase.instance.FingerSpeedScreenFix
            : Screen.width;

        float touchSensitiveUser = GameSettings.GetTouchSensitive_User();
        _fingerSpeed = (touchSensitiveUser * 2 + RemoteConfigBase.instance.FingerSpeed) / screenWith;
        if (touchSensitiveUser > 0) {
            _fingerSpeed += touchSensitiveUser * 2 / screenWith;
        }
    }

    public void ZenModeStart() {
        StartCoroutine(ZenModeStartEffect());
    }

    IEnumerator ZenModeStartEffect() {
        //Ball.b.magicEffect.gameObject.SetActive(true);
        //Ball.b.magicEffect.Play();
        yield return new WaitForSeconds(2.5f);

        GameStart();
    }

    public void GameContinue(string song_revive_type) {
        if (Ball.b.endlessModeCount > 0 && Spawner.s.currentJumpNoteID == 0) {
            Ball.b.SetEndlessRound((byte) (Ball.b.endlessModeCount - 1));
        }

        songReviveType = song_revive_type;
        continueCount++;
        Spawner.s.platformManager.StopHideAllPlatform();

        if (objectRotate != null) {
            objectRotate.localRotation = Quaternion.LookRotation(Vector3.zero);
        }

        if (song_revive_type.Equals(TRACK_PARAM.currency)) {
            _song.transactionIDSpend = Configuration.transactionIDSpend;
        }

        FireSongEvent(SONG_STATUS.song_revive);
        UIController.ui.gameui.GameContinue();
        if (game != GameStatus.DIE) {
            return;
        }

        UIController.ui.gameui.GamePrePare(IsNewTutorialGamePlay);

        if (GameItems.instance.edmController != null) {
            GameItems.instance.edmController.SetFaceSkin(Spawner.s.currentIndexSkin, true);
        }

        if (GameItems.instance.hiphopRetroController != null)
            GameItems.instance.hiphopRetroController.SetThemeEnvironment(Spawner.s.currentIndexSkin, true);

        if (GameItems.instance.hiphopModernController != null)
            StartCoroutine(GameItems.instance.hiphopModernController.ContinueWithTimeline());

        for (int i = 0; i < animationNotes.Count; i++) {
            if (animationNotes[i].timeAppear > timePlay) {
                currentAnimation = i;
                break;
            }
        }

        if (GameItems.instance.vfxController != null) {
            StartCoroutine(GameItems.instance.vfxController.GameContinue());
        }

        CallEvent((s) => { s.gameContinue(); });

        if (remoteConfig.FPSTracker_Enable) {
            FPSTracker.Instance.EnableTracker(FPSTracker.GAMECONTEXT_GAME_PLAY);
        }
    }

    private void ResetFixedDeltaTime() {
        Time.fixedDeltaTime = 0.02f;
    }

    public void GameStop(bool force = false) {
        ResetFixedDeltaTime();

        FireSongEvent(SONG_STATUS.song_fail);
        if (Ball.b.endlessModeCount > 0) {
            if (!remoteConfig.ProgressByScore_IsEnable) {
                if (!EndlessIteration.IsActive) {
                    stars += Ball.b.endlessModeCount - 1;
                }
            }

            for (int i = _lastEndlessMode + 1; i <= Ball.b.endlessModeCount; i++) {
                AnalyticHelper.Endless(i, _song, false);
                if (i == Ball.b.endlessModeCount) {
                    AnalyticHelper.Endless(i, _song, true);
                }
            }

            _lastEndlessMode = Ball.b.endlessModeCount;
        }

        //eventually set game flag false;
        game = GameStatus.DIE;
        bool autoRestart = false;
        bool autoRevive = false;

        MapManager.instance.OffSoundWave();

        if (GameItems.instance.edmController != null) {
            GameItems.instance.edmController.StopAnimation();
        }

        if (GameItems.instance.hiphopRetroController != null) {
            GameItems.instance.hiphopRetroController.SetSpeedEnvironment(0);
        }

        if (GameItems.instance.hiphopModernController != null) {
            GameItems.instance.hiphopModernController.StopAnimation();
        }

        if (GameItems.instance.vfxController != null) {
            GameItems.instance.vfxController.StopAnimation();
        }

        if (BoosterManager.isInstanced) {
            BoosterManager.instanceSafe.GameStop();
        }

        if (_song.isTutorialSong && remoteConfig.ReviveTutorial_Enable) {
            autoRevive = true;
        } else if (_song.isTutorialSong && timePlay <= RemoteConfigBase.instance.OnboardingFlow_SafetyNetTime) {
            autoRestart = true;
        }

        Ball.b.UpdateTrail(false);

        if (TopBar.instance != null) {
            TopBar.instance.GameStop();
        }

        CallEvent((s) => { s.gameOver(); });

        if (remoteConfig.FPSTracker_Enable) {
            FPSTracker.Instance.DisableTracker();
        }

        if (autoRestart) { //isTutorialSong
            AutoRestart();
        } else if (autoRevive) {
            AutoRevive();
        }

        if (autoRestart || autoRevive) {
            return; // tự động hồi sinh ->> k care việc chết
        }

        Ball.b.CanInteract = false;
        float timeWaitEnd = remoteConfig.VFX_Dead_IsEnable ? 1f : 0f;
        if (remoteConfig.VFX_Dead_IsEnable) {
            GameItems.instance.ShowDeadVFX(Ball.b.transCache.position, timeWaitEnd);
        }

        NotesManager.instance.PlatformDead(Spawner.jumpCount);
        DOVirtual.DelayedCall(timeWaitEnd, () => {
            if (IsAbleToContinue() && !force) {
                ShowContinueUi((isRevive) => {
                    if (!isRevive) {
                        if (enableEndless && Ball.b.endlessModeCount > 0) {
                            // fire when die at endless mode and round != 0 and not revive
                            FireSongEnd();
                        }

                        AirfluxTracker.TrackAchieveLevel();
                    }
                });
            } else {
                if (enableEndless && Ball.b.endlessModeCount > 0) {
                    // fire when die at endless mode and round != 0 and can't show revive
                    FireSongEnd();
                }

                AirfluxTracker.TrackAchieveLevel();
                ShowResultUi(isCheckTrySkin: true, isCheckSongOfDay: true); // game failed
            }
        });
    }

    private int _countRestart;

    private void AutoRestart() {
        GamePrepare(true, true);
        SceneFader.instance.ToggleOverlay(1f);
    }

    public void ShowResultUi(bool isCheckTrySkin, bool isCheckSongOfDay, bool isCancelReborn = false) {
        // hide button pause
        if (RemoteConfigBase.instance.PauseQuitAP_PauseButton_IsEnable) {
            TopBar.instance.HidePauseButton();
        }

        if (Configuration.instance.isEndlessModeByUser &&
            ((!enableEndless && stars >= remoteConfig.EndlessMode_UnlockByStar && _highestStar < stars) ||
             (enableEndless && Spawner.s.platformManager.GetTotalTileFront() == 0)) && !_song.isTutorialSong &&
            !_song.isTutorialOnlineSong) {

            if (!EndlessIteration.IsActive) {
                EndlessIteration.TryActive();
            }

            if (EndlessIteration.IsActive) {
                ShowEndlessModeTransition(this.GetCancellationTokenOnDestroy()).Forget();
                return;
            }
        }

        if (ChallengeMode.IsActive) {
            TopBar.instance.ShowExtraHuds();
        }

        if (remoteConfig.Economy_IsEnable) {
            ShowResultUI_Economy(isCheckTrySkin, isCheckSongOfDay, isCancelReborn, this.GetCancellationTokenOnDestroy())
                .Forget();
        } else if (remoteConfig.ResultScreen_StyleIndex.IsInListNumber(2, 3, 4)) {
            ShowResultUI_234(isCheckTrySkin, isCheckSongOfDay, isCancelReborn, this.GetCancellationTokenOnDestroy())
                .Forget();
        } else {
            ShowResultUI_Normal(isCheckTrySkin, isCheckSongOfDay, isCancelReborn, this.GetCancellationTokenOnDestroy())
                .Forget();
        }

        EndlessIteration.DeActive();
    }

    private async UniTaskVoid ShowResultUI_Economy(bool isCheckTrySkin, bool isCheckSongOfDay, bool isCancelReborn,
                                                   CancellationToken cancellationToken = default) {
        try {
            bool isWaiting = true;

            isShowingFsAd = true;
            ShowInterstitial(() => {
                isWaiting = false;
                DoneShowingFs();
            });
            while (isWaiting) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            if (isCheckTrySkin && TrySkinFeature.IsActive && Ball.b.tryBallId > 0) {
                isWaiting = true;
                UIController.ui.ShowTrySkinPopUp((result) => { isWaiting = false; });
            }

            while (isWaiting) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            if (isCheckSongOfDay && _song.isSongOfDay && IsNeedToShowOfferUnlockSong(_song)) {
                GameObject prefab = Resources.Load<GameObject>(ResourcesPath.Popups_UnlockSongOfDay);
                if (prefab) {
                    UnlockSongOfDay unlockSongOfDay = Instantiate(prefab, UIController.ui.transform, false)
                        .GetComponent<UnlockSongOfDay>();
                    isWaiting = true;
                    unlockSongOfDay.Active(() => { isWaiting = false; });
                }
            }

            while (isWaiting) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            ShowSevenDayMissionPopup(() => {
                if (this != null) {
                    ShowVipMissionPopup(() => {
                        if (this != null) {
                            if (UIOverlay.instance != null && UIOverlay.instance.CheckShowAdFsReward()) {
                                AdFsReward.OnCloseAdFsReward += AdFsReward_OnCloseAdFsReward;
                            } else if (UIController.ui != null && UIController.ui.gameover != null) {
                                UIController.ui.gameover.Active();
                            }
                        }
                    });
                }
            });
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    [HideInInspector] public bool isOpenedOtherPopup;

    private async UniTaskVoid ShowResultUI_234(bool isCheckTrySkin, bool isCheckSongOfDay, bool isCancelReborn,
                                               CancellationToken cancellationToken = default) {
        try {
            UIController.ui.gameover.Active();

            bool isWaiting = true;
            isOpenedOtherPopup = true;

            if (remoteConfig.ResultScreen_ShowBeforeInterAd) {
                UIController.ui.gameover.showInterstitial = () => {
                    isShowingFsAd = true;
                    ShowInterstitial(() => { //
                        if (this == null) {
                            return;
                        }

                        isWaiting = false;
                        DoneShowingFs();
                        StartCoroutine(UIController.ui.gameover.OnStartPhase03());
                    });
                };
            } else {
                isShowingFsAd = true;
                ShowInterstitial(() => { //
                    if (this == null) {
                        return;
                    }

                    isWaiting = false;
                    DoneShowingFs();
                });
            }

            while (isWaiting || UIOverlay.instance.IsWaitingAdFsReward()) {
                await UniTask.Yield(cancellationToken);
            }

            if (isCheckTrySkin && TrySkinFeature.IsActive && Ball.b.tryBallId > 0) {
                isWaiting = true;
                UIController.ui.ShowTrySkinPopUp((result) => { isWaiting = false; });
                isOpenedOtherPopup = false;
            }

            while (isWaiting) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            if (isCheckSongOfDay && _song.isSongOfDay && IsNeedToShowOfferUnlockSong(_song)) {
                GameObject prefab = Resources.Load<GameObject>(ResourcesPath.Popups_UnlockSongOfDay);
                if (prefab) {
                    UnlockSongOfDay unlockSongOfDay = Instantiate(prefab, UIController.ui.transform, false)
                        .GetComponent<UnlockSongOfDay>();
                    isWaiting = true;
                    unlockSongOfDay.Active(() => { isWaiting = false; });
                }
            }

            while (isWaiting) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            ShowSevenDayMissionPopup(() => {
                if (this != null) {
                    ShowVipMissionPopup(() => {
                        if (this != null) {
                            isOpenedOtherPopup = false;
                        }
                    });
                }
            }, false);

            while (isOpenedOtherPopup || !UIController.ui.gameover.isShowDone) {
                await UniTask.Yield(cancellationToken);
            }

            if (SevenDayMission.instanceSafe.HasNotification()) {
                if (UIController.ui.gameover.HasNotificationEndlessMode()) {
                    await UniTask.Delay(2000, cancellationToken: cancellationToken);
                }

                SevenDayMission.instanceSafe.ShowSingleMission();
            }
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    private async UniTaskVoid ShowResultUI_Normal(bool isCheckTrySkin, bool isCheckSongOfDay, bool isCancelReborn,
                                                  CancellationToken cancellationToken = default) {
        try {
            bool isWaiting = false;

            bool isShowTrySkin = isCheckTrySkin && TrySkinFeature.IsActive && Ball.b.tryBallId > 0;
            bool isShowTrySkinBefore = isShowTrySkin && remoteConfig.TrySkin_ShowBeforeFSAds;
            if (isShowTrySkinBefore) {
                isWaiting = true;
                UIController.ui.ShowTrySkinPopUp((result) => {
                    isWaiting = false;
                    if (result) {
                        bool isShowAdsAfterUnlockBall = remoteConfig.TrySkin_ShowFSAdsAfterUnlockBall;
                        if (isShowAdsAfterUnlockBall) {
                            isShowingFsAd = true;
                            ShowInterstitial(DoneShowingFs);
                        }
                    } else {
                        isShowingFsAd = true;
                        ShowInterstitial(DoneShowingFs);
                    }
                });
            } else {
                isShowingFsAd = true;
                ShowInterstitial(DoneShowingFs);
                if (isShowTrySkin) {
                    isWaiting = true;
                    UIController.ui.ShowTrySkinPopUp((result) => { isWaiting = false; });
                }
            }

            while (isWaiting) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            if (isCheckSongOfDay && _song.isSongOfDay && IsNeedToShowOfferUnlockSong(_song)) {
                GameObject prefab = Resources.Load<GameObject>(ResourcesPath.Popups_UnlockSongOfDay);
                if (prefab) {
                    UnlockSongOfDay unlockSongOfDay = Instantiate(prefab, UIController.ui.transform, false)
                        .GetComponent<UnlockSongOfDay>();
                    isWaiting = true;
                    unlockSongOfDay.Active(() => { isWaiting = false; });
                }
            }

            while (isWaiting) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            ShowSevenDayMissionPopup(() => {
                if (this != null) {
                    ShowVipMissionPopup(() => {
                        if (this != null) {
                            if (UIOverlay.instance != null && UIOverlay.instance.CheckShowAdFsReward()) {
                                AdFsReward.OnCloseAdFsReward += AdFsReward_OnCloseAdFsReward;
                            } else if (UIController.ui != null && UIController.ui.gameover != null) {
                                UIController.ui.gameover.Active();
                            }
                        }
                    });
                }
            });
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    private void DoneShowingFs() {
        isShowingFsAd = false;
    }

    private async UniTaskVoid ShowEndlessModeTransition(CancellationToken cancellationToken = default) {
        try {
            await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);

            while (UIOverlay.instance.IsWaitingAdFsReward()) {
                await UniTask.Yield(PlayerLoopTiming.LastPostLateUpdate, cancellationToken);
            }

            UIController.ui.ShowEndlessIteration();
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    public void ShowInterstitial(Action complete) {
        if (remoteConfig.ChallengeMode_AdBreak_Result && ChallengeMode.IsActive && stars >= 3) {
            complete?.Invoke();
            return;
        }

        if (Configuration.IsNoFSAds()) {
            complete?.Invoke();
            return;
        }

        //show interstitial
        if (canShowInterstitial && (remoteConfig.Ad_Enable_TutorialAd || !NotesManager.instance.song.isTutorialSong)) {
            if (NotesManager.instance.noteDatas[Spawner.s.currentJumpNoteID].timeAppear >=
                remoteConfig.Ad_SongTimeShowFS) {

                bool isDiscoveryChallengeSong = NotesManager.instance.song.isChallengeSong;
                if (remoteConfig.OnboardingFlow_IsEnable) {
                    int countEvent = AnalyticHelper.CountEvent(SONG_STATUS.song_ap.ToString());
                    if (countEvent > remoteConfig.OnboardingFlow_AdAfterTutorial) {
                        RequestFsAd(isDiscoveryChallengeSong, complete);
                        canShowInterstitial = false;
                    } else {
                        complete?.Invoke();
                    }
                } else {
                    RequestFsAd(isDiscoveryChallengeSong, complete);
                    canShowInterstitial = false;
                }
            } else {
                complete?.Invoke();
            }
        } else {
            complete?.Invoke();
        }
    }

    private void RequestFsAd(bool isDiscoveryChallengeSong, Action complete) {
        if (isDiscoveryChallengeSong) {
            float now = Time.time;
            float lastRwTime = AdsManager.instance.lastRwAdsTime;
            bool isReachLimitNoAdSongStart;

            // If the player fails within [NoAdTimeFromSongStart] seconds from the song_start, the consecutive count
            // increases by one; otherwise, the number will be reset to zero
            if (now - _songStartTime < RemoteConfigBase.instance.DiscoveryChallenge_NoAdTimeFromSongStart) {
                DiscoveryChallengeManager.consecutiveShortSongStart++;
                isReachLimitNoAdSongStart = DiscoveryChallengeManager.consecutiveShortSongStart >
                                            RemoteConfigBase.instance.DiscoveryChallenge_ConsecutiveNoAdSongStartLimit;

                // if the consecutive count reaches the limit, it'll be set to zero
                if (isReachLimitNoAdSongStart) {
                    DiscoveryChallengeManager.consecutiveShortSongStart = 0;
                }
            } else {
                DiscoveryChallengeManager.consecutiveShortSongStart = 0;
                isReachLimitNoAdSongStart = true;
            }

            bool isShowFs = isReachLimitNoAdSongStart &&
                            now - lastRwTime > RemoteConfigBase.instance.DiscoveryChallenge_RwFsInterval;

            if (!isShowFs) {
                complete?.Invoke();
                return;
            }
        }

        if (AdsManager.instance.IsEnableInterstitial) {
            AdsManager.instance.ShowInterstitial(LOCATION_NAME.result.ToString(), _song, complete);
        } else {
            complete?.Invoke();
        }
    }

    private void AdFsReward_OnCloseAdFsReward(bool forceQuitResult) {
        AdFsReward.OnCloseAdFsReward -= AdFsReward_OnCloseAdFsReward;
        if (!forceQuitResult) {
            if (UIController.ui != null && UIController.ui.gameover != null) {
                UIController.ui.gameover.Active();
            } else {
                CustomException.Fire("[AdFsReward_OnCloseAdFsReward]", "UIController.ui.gameover.Active() is NULL");
            }
        }
    }

    private void ShowSevenDayMissionPopup(Action callback, bool isShowSingleMission = true) {
        SevenDayMission.instanceSafe.ShowPopupNotification(callback, isShowSingleMission);
    }

    private void ShowVipMissionPopup(Action callback) {
        if (SubscriptionController.IsSubscriptionVip() && stars > 0 && VipMissionManager.IsShowVipMission()) {
            bool isUpdated = VipMissionManager.UpdateStar(NotesManager.instance.song, stars);
            if (isUpdated) {
                var gameObject = Util.ShowPopUp(PopupName.VipMission);
                if (gameObject.TryGetComponent(out DisableCallbackUI popup)) {
                    popup.Show(callback);
                } else {
                    callback?.Invoke();
                }
            } else {
                callback?.Invoke();
            }
        } else {
            callback?.Invoke();
        }
    }

    private void AutoRevive() {
        if (GameItems.instance.starManager.IsActive()) {
            GameItems.instance.starManager.Deactive();
        }

        if (IsNewTutorialGamePlay) {
            IEAutoRevive(this.GetCancellationTokenOnDestroy()).Forget();
        } else {
            GameContinue("auto");
            SceneFader.instance.ToggleOverlay(1f);
        }
    }

    private async UniTaskVoid IEAutoRevive(CancellationToken cancellationToken = default) {
        try {
            UIController.ui.gameui.HideBounce();
            UIController.ui.gameui.ShowRewind();
            await UniTask.Delay(2000, cancellationToken: cancellationToken);

            UIController.ui.gameui.HideRewind();
            Spawner.s.platformManager.GameRewind();
            IsPlaying = false;
            game = GameStatus.REWIND;
            Vector3 startPosition = Ball.b.transCache.position;
            int lastIndex = passedTile.GetComponent<Platform>().noteID - 1;
            if (lastIndex < 0) {
                lastIndex = 0;
            }

            Vector3 endPosition = MapManager.instance.GetPlatformData(lastIndex).positionPlatform + Vector3.up;
            Vector3 currentPosition;
            float h = 20f;
            float timeRewind = 0;
            float totalTimeRewind =
                Mathf.Clamp((startPosition.z - endPosition.z) / Ball.b.GetBalLSpeed() / 3f, 0.5f, 1f);
            Ball.b.StartRewind();
            do {
                timeRewind += Time.deltaTime;
                currentPosition = Vector3.Lerp(startPosition, endPosition, timeRewind / totalTimeRewind);
                currentPosition.y = Util.Bezier(startPosition.y, h, endPosition.y, timeRewind / totalTimeRewind);
                Ball.b.transCache.position = currentPosition;
                await UniTask.Yield(cancellationToken);
            } while (timeRewind < totalTimeRewind);

            //Debug.LogError($"last Position: {endPosition}");
            game = GameStatus.DIE;
            IsPlaying = true;
            GameContinue("auto");
        } catch (OperationCanceledException) {
            // Task was cancelled
        }
    }

    private void ShowContinueUi(Action<bool> callback) {
        _isShowedContinue = true;
        // Continue game
        UIController.ui.ShowContinueUI(callback);
    }

    private bool IsAbleToContinue() {
        bool isSubscriptionReborn = (RemoteConfigBase.instance.Subscription_Enable &&
                                     continueCount < RemoteConfigBase.instance.Reborn_MaxRebornVip);
        return ((continueCount < RemoteConfigBase.instance.Reborn_MaxRebornVideo || isSubscriptionReborn) &&
                Score >= RemoteConfigBase.instance.Reborn_MinScore);
    }

    [HideInInspector] public bool isChangedLevel;

    public void GameCompleted() {
        ResetFixedDeltaTime();
        NotesManager.instance.GameComplete();
        if (Configuration.instance.isTutorial) {
            PlayerPrefs.SetInt(CONFIG_STRING.CanGetTutorialReward, 1);
        }

        FireSongEnd(); // fire when complete normal mode
        AirfluxTracker.TrackAchieveLevel();

        //eventually set game flag false;
        game = GameStatus.DIE;
        MapManager.instance.OffSoundWave();

        if (NotesManager.instance.song.isTutorialSong) {
            stars = 3; // always set 3 stars for tutorial song
        } else if (ChallengeMode.IsActive && enableEndless) {
            stars = 3; //complete challenge mode!
            ChallengeModeTracking.TrackPlayCMEnd(_song);
        }
        ShowResultUi(isCheckTrySkin: true, isCheckSongOfDay: true); // Game Win
        if (remoteConfig.FPSTracker_Enable) {
            FPSTracker.Instance.DisableTracker();
        }

        CallEvent((s) => { s.gameOver(); });

        if (_isAutoPlay) {
            Screen.sleepTimeout = SleepTimeout.SystemSetting;
        }

        if (RemoteConfigBase.instance.NotesDifficult_IsEnable) {
            isChangedLevel = UpdateLevelDifficult();
            if (MultiDifficultUIScript.instance != null && MultiDifficultUIScript.instance.GetSelectedDifficult() !=
                NotesManager.Difficulties.Endless) {
                isChangedLevel = true;
                MultiDifficultUIScript.instance.SetSelectedDifficult(NotesManager.Difficulties.Endless);
            }
        }
    }

    public void CompleteEndlessRound() {
        SuperpoweredSDK.instance.PauseMusic();
        ResetFixedDeltaTime();

        game = GameStatus.DIE;
        MapManager.instance.OffSoundWave();

        ShowResultUi(isCheckTrySkin: true, isCheckSongOfDay: true);
        if (remoteConfig.FPSTracker_Enable) {
            FPSTracker.Instance.DisableTracker();
        }

        CallEvent((s) => { s.gameOver(); });

        if (_isAutoPlay) {
            Screen.sleepTimeout = SleepTimeout.SystemSetting;
        }
    }

    private void FireSongEnd() {
        _song.LastestStar = Mathf.Clamp(stars, 0, 3);
        _song.LastestScore = Score;
        _song.LastestTile = GetCurrentExp();
        FireSongEvent(SONG_STATUS.song_end);
    }

    public void FireSongResult() {
        _song.LastestStar = Mathf.Clamp(stars, 0, 3);
        _song.LastestScore = Score;
        _song.LastestTile = GetCurrentExp();
        FireSongEvent(SONG_STATUS.song_result, LOCATION_NAME.result.ToString());
        if (AdsManager.instance) {
            AdsManager.instance.RecheckBannerStatus();
        }

        if (!_song.isTutorialSong) {
            StarsJourneyManager.TrackLevelProgression(_song);
        }
    }

    public void Resume() {
        if (GameSessionManager.Instance != null && !NotesManager.instance.song.IsLocalSong()) {
            GameSessionManager.Instance.ResumeGame(Reason.GameResume);
        }

        UIController.ui.Resume();
    }

    public void Pause() {
        if (IsPlaying && Ball.b.transCache.position.y > Spawner.s.blockPosition.y && stars < 3) {
            Ball.b.Update(); //Prevent pause/resume sync frame-update issue
            SuperpoweredSDK.instance.SmoothPause(0.5f);
            pauseTime = SuperpoweredSDK.instance.GetPosition();
            SetGameState(false);
            UIController.ui.pauseui.gameObject.SetActive(true);
        }
    }

    public void FireSongEvent(SONG_STATUS status, string locationSource = "") {
        var currentLocation = string.IsNullOrEmpty(locationSource) ? this.location : locationSource;

        int progress = GetCurrentProgress();
        Dictionary<string, object> param = AnalyticHelper.GetDefaultParam(currentLocation);

        switch (status) {
            case SONG_STATUS.song_impression:
            case SONG_STATUS.song_ap:
                AnalyticHelper.LogSong(status, _song, currentLocation);
                break;

            case SONG_STATUS.song_start:
                _songStartFirst = !_song.IsPlayed();
                _fireSongEnd = false;

                if (_songStartFirst) {
                    AnalyticHelper.LogSong(SONG_STATUS.song_start_first, _song, currentLocation);
                }

                AnalyticHelper.LogSong(status, _song, currentLocation);
                break;

            case SONG_STATUS.song_replay:
                _fireSongEnd = false;
                AnalyticHelper.LogSong(status, _song, currentLocation);
                break;

            case SONG_STATUS.song_end:
                param.TryAdd(TRACK_NAME.song_progress, progress); //song_progress

                if (!_fireSongEnd) {
                    AnalyticHelper.LogSong(status, _song, currentLocation, param);
                    _fireSongEnd = true;
                }

                break;

            case SONG_STATUS.song_revive:
            case SONG_STATUS.song_fail:
            case SONG_STATUS.song_result:
                param.TryAdd(TRACK_NAME.song_progress, progress); //song_progress
                param.TryAdd(TRACK_NAME.song_score, Score); //song_score
                param.TryAdd(TRACK_NAME.stars, Mathf.Clamp(stars, 0, 3));
                param.TryAdd(TRACK_NAME.song_last_note, Spawner.s.currentJumpNoteID); //song_last_note
                if (status == SONG_STATUS.song_fail) { //able_to_revive
                    param.Add(TRACK_NAME.able_to_revive, IsAbleToContinue() ? "yes" : "no");
                    //death position
                    if (Spawner.s.currentJumpNoteID < 3 && Ball.b.endlessModeCount == 0) {
                        param.Add(TRACK_NAME.death_positionX + Spawner.s.currentJumpNoteID,
                            Mathf.Round(Ball.b.transCache.position.x).ToString());
                    }
                }

                AnalyticHelper.LogSong(status, _song, currentLocation, CloneParam(param));
                break;
        }

        if (_songStartFirst && status == SONG_STATUS.song_result) {
            AnalyticHelper.LogSong(SONG_STATUS.song_result_first, _song, currentLocation, CloneParam(param));
        }
    }

    public int GetCurrentProgress() {
        float progress = (Mathf.Clamp01(((float) (Spawner.s.currentJumpNoteID + 1) / NotesManager.instance.noteCount)) *
                          100);
        if (enableEndless) {
            return (Ball.b.endlessModeCount + 1) * 100 + (int) progress;
        } else {
            return (int) progress;
        }
    }

    private Dictionary<string, object> CloneParam(Dictionary<string, object> objects) {
        return new Dictionary<string, object>(objects);
    }

    public static bool IsAutoPlay() {
        return Configuration.instance.isAutoPlay;
    }

    public bool IsContinued() {
        return continueCount > 0;
    }

    #region Log Time Tutorial

    private List<int> _timeTutorialLog = new List<int>() {15, 30, 45, 60, 80};
    private int       _indexTime       = 0;

    [HideInInspector] public bool isSensitiveClosing;

    private void LogTimePlayTutorial(float time) {
        if (!Configuration.instance.isTutorial) {
            return;
        }

        if (_indexTime >= _timeTutorialLog.Count) {
            return;
        }

        int trackTime = _timeTutorialLog[_indexTime];
        if (time >= trackTime) {
            string eventName = String.Format(TRACK_NAME.fn_song_s, trackTime);

            Dictionary<string, object> param = AnalyticHelper.GetDefaultParam(this.location);
            param.Add(TRACK_NAME.loading_duration, Time.time);

            AnalyticHelper.LogEvent(eventName, param);

            _indexTime++;
        }
    }

    #endregion

    private bool IsNeedToShowOfferUnlockSong(Song song) {
        if (Configuration.GetSaveTypeOfSong(song.path, song.type).Equals(SONGTYPE.OPEN)) {
            //already open
            return false;
        }

        // int highestStar = Configuration.GetBestStars(_song.path);
        // bool first3Stars = stars >= 3 && highestStar < 3;
        // return first3Stars;
        return true;
    }

    private bool UpdateLevelDifficult() {
        bool isChanged = false;
        int countEvent = Mathf.Max(_countRestart, continueCount);

        if (_song.isTutorialSong) {
            if (countEvent <= 0) {
                //NotesManager.instance.SetCurrentDifficultiesPlayer(NotesManager.Difficulties.Hard);
                NotesManager.instance.SetCurrentDifficultiesPlayer(NotesManager.Difficulties.Normal);
                isChanged = true;
            } else if (countEvent <= 2) {
                NotesManager.instance.SetCurrentDifficultiesPlayer(NotesManager.Difficulties.Normal);
                isChanged = true;
            } else {
                NotesManager.instance.SetCurrentDifficultiesPlayer(NotesManager.Difficulties.Easy);
                isChanged = true;
            }
        } else {
            // kiểm tra độ khó hiện tại so với độ khó player -> k nên dùng cached Difficulty
            if (NotesManager.instance.GetDifficultiesOfCurrentSong() >=
                NotesManager.instance.GetCurrentDifficultiesPlayer()) {
                if (countEvent >= 3) {
                    isChanged = NotesManager.instance.DecreaseDifficulties();
                } else if (!_isShowedContinue) {
                    isChanged = NotesManager.instance.IncreaseDifficulties();
                }
            }
        }

        if (isChanged) {
            ResetCountEvent();
        }

        return isChanged;
    }

    public void ResetCountEvent() {
        _countRestart = 0;
        continueCount = 0;
        _isShowedContinue = false;
    }

    public static bool IsUnlockEndlessModeByPlayer(Song song) {
        bool endlessMode = PlayerPrefs.GetInt("EndlessMode_" + song.acm_id_v3, 0) == 1;
        return endlessMode;
    }

    public static void SetUnlockEndlessModeByPlayer(Song song) {
        PlayerPrefs.SetInt("EndlessMode_" + song.acm_id_v3, 1);
    }

    #region Diamond

    public int GetDiamond() {
        return _totalDiamond;
    }

    public int GetCollectedToken(TokenType type) {
        return _dictToken?.GetValueOrDefault(type) ?? 0;
    }

    public int AddDiamond(int addDiamond, CurrencyEarnSource source) {
        if (_earnedDiamond.ContainsKey(source)) {
            _earnedDiamond[source] += addDiamond;
        } else {
            _earnedDiamond.Add(source, addDiamond);
        }

        _totalDiamond += addDiamond;
        OnChangeDiamondCollected?.Invoke(_totalDiamond);
        return _totalDiamond;
    }

    public void CalcDiamond() {
        int totalNotes = (NotesManager.instance.noteCount - 1);
        float pctProgression = Spawner.jumpCount * 1f / totalNotes;
        float pctPerfect = Spawner.s.totalPerfectCount * 1f / totalNotes;

        int diamondByProgression = 0;
        int diamondByPerfect = 0;

        switch (NotesManager.instance.Difficulty) {
            case NotesManager.Difficulties.Easy:
                diamondByProgression = (int) (pctProgression * remoteConfig.NotesDifficult_Diamond_Progression_Easy);
                diamondByPerfect = (int) (pctPerfect * remoteConfig.NotesDifficult_Diamond_Perfect_Easy);
                break;

            case NotesManager.Difficulties.Normal:
                diamondByProgression = (int) (pctProgression * remoteConfig.NotesDifficult_Diamond_Progression_Normal);
                diamondByPerfect = (int) (pctPerfect * remoteConfig.NotesDifficult_Diamond_Perfect_Normal);
                break;

            case NotesManager.Difficulties.Hard:
                diamondByProgression = (int) (pctProgression * remoteConfig.NotesDifficult_Diamond_Progression_Hard);
                diamondByPerfect = (int) (pctPerfect * remoteConfig.NotesDifficult_Diamond_Perfect_Hard);
                break;

            case NotesManager.Difficulties.Endless:
                diamondByProgression = (int) (pctProgression * remoteConfig.NotesDifficult_Diamond_Progression_Easy);
                diamondByPerfect = (int) (pctPerfect * remoteConfig.NotesDifficult_Diamond_Perfect_Easy);
                break;
        }

        AddDiamond(diamondByProgression + diamondByPerfect, CurrencyEarnSource.COLLECT_DIAMOND);
    }

    #endregion

    public void HandleBackEvent() {
        if (instance.game != GameStatus.DIE) {
            if (!NotesManager.instance.song.IsLocalSong() && GameSessionManager.Instance != null) {
                GameSessionManager.Instance.PauseGame(Reason.PlayerPause);
            }

            instance.Pause();
        }
    }

    public bool HaveRewardAtMileStone(int idMileStone) {
        if (NotesManager.instance.song.isTutorialSong) {
            return false;
        }

        if (ChallengeMode.IsActive) {
            if (idMileStone > 3) {
                return false;
            }
        }

        if (idMileStone > 3) {
            if (remoteConfig.Economy_MileStone_Endless_Detailed) {
                if (idMileStone % 4 == 0) {
                    return false;
                }

                return remoteConfig.Economy_MileStone_RepeatedEat_Endless || HighestMileStone < idMileStone;
            } else {
                if (idMileStone % 4 != 0) {
                    return false;
                }

                return remoteConfig.Economy_MileStone_RepeatedEat_Endless || HighestMileStone < idMileStone;
            }
        } else {
            if (enableEndless && !remoteConfig.Economy_MileStone_Endless_Detailed) {
                // chơi endless mà k có các mốc detail =>> return false
                return false;
            }

            return remoteConfig.Economy_MileStone_RepeatedEat_Normal || HighestMileStone < idMileStone;
        }
    }

    public void EatMileStone(int idMileStone, int amount) {
        if (!HaveRewardAtMileStone(idMileStone))
            return; // không có phần quà ở mốc này
        if (_ateMileStone >= idMileStone)
            return; // trong map này đã ăn milestone này rồi

        _ateMileStone = idMileStone;
        if (idMileStone > _mileStone) {
            _mileStone = idMileStone;
        }

        IsWaitingFlyDiamondVFXFromPrgogressbar = true;

        TopBar.instance.SetProgressBarMileStone(idMileStone, () => {
            if (amount != 0) { // thêm chút thời gian để tăng text diamond
                AddDiamond(amount, CurrencyEarnSource.milestone_);
                _mileStoneValues.TryAdd(idMileStone, amount);
                DOVirtual.DelayedCall(0.25f + amount / 120f, () => { IsWaitingFlyDiamondVFXFromPrgogressbar = false; });
            } else {
                IsWaitingFlyDiamondVFXFromPrgogressbar = false;
            }
        });
    }

    public static void SetGameState(bool isPlaying) {
        IsPlaying = isPlaying;
        Time.timeScale = isPlaying ? 1 : 0;
    }

    public void EarnTokenInRoad(TokenType type, int valueToken) {
        if (!_dictToken.TryAdd(type, valueToken)) {
            _dictToken[type] += valueToken;
        }

        if (ChallengeMode.IsActive && enableEndless) {
            if (!_dictTokenEndless.TryAdd(type, valueToken)) {
                _dictTokenEndless[type] += valueToken;
            }
        }

        TopBar.instance.OnChangeCollectedToken(type, _dictToken[type]);
    }

    public void UpdateScore(int streakPerfect) {
        int plusScore;
        bool isPerfect = streakPerfect > 0;

        if (remoteConfig.NotesDifficult_IsEnable) {
            switch (NotesManager.instance.Difficulty) {
                case NotesManager.Difficulties.Easy:
                    plusScore = isPerfect ? Mathf.Min(streakPerfect, 20) : 1;
                    break;

                case NotesManager.Difficulties.Normal:
                    plusScore = isPerfect ? Mathf.Min(2 * streakPerfect, 40) : 2;
                    break;

                case NotesManager.Difficulties.Hard:
                    plusScore = isPerfect ? Mathf.Min(3 * streakPerfect, 60) : 3;
                    break;

                default:
                    plusScore = isPerfect ? Mathf.Min(streakPerfect, 20) : 1; //default
                    break;
            }
        } else { //default
            if (isPerfect) {
                int streak = Mathf.Min(streakPerfect, remoteConfig.ScoreConfig_MaxStreak);
                plusScore = remoteConfig.ScoreConfig_Great + (streak - 1) * remoteConfig.ScoreConfig_Perfect;
            } else {
                plusScore = remoteConfig.ScoreConfig_Great; //great
            }
        }

        AddScore(plusScore);
        TopBar.instance.UpdateScore(Score, streakPerfect);

        if (remoteConfig.ProgressByScore_IsEnable && !enableEndless) {
            int tmpStars = GetStarByScore(Score);
            if (tmpStars > stars) {
                stars = tmpStars;
                Logger.Log("[Star] " + stars);

                Platform lastPlatform = Spawner.s.platformManager.GetLastPlatform();
                if (lastPlatform != null) {
                    Vector3 positionStar = lastPlatform.transCache.position;
                    TopBar.instance.MoveStar(positionStar, stars);
                }
            }

            TopBar.instance.SetProcessByScore(Score);
        }
    }

    private int GetStarByScore(int currentScore) {
        int[] scoreStar = remoteConfig.ProgressByScore_Star;
        if (scoreStar == null || !(scoreStar.Length == 3 || scoreStar.Length == 4)) {
            return 0;
        }

        float pctScore = currentScore * 1f / maxScore * 100;
        for (int index = 0; index < scoreStar.Length; index++) {
            if (pctScore < scoreStar[index]) {
                return index; //0 1 2 ||3
            }
        }

        return scoreStar.Length; //||3 4
    }

    private int GetMaxScore(int totalNotes) {
        int great = remoteConfig.ScoreConfig_Great;
        int perfect = remoteConfig.ScoreConfig_Perfect;
        int maxStreak = remoteConfig.ScoreConfig_MaxStreak;

        if (totalNotes <= maxStreak) {
            int getMaxScore = totalNotes * great + ((totalNotes - 1) * totalNotes / 2) * perfect;
            return getMaxScore;
        } else {
            int score1 = maxStreak * great + ((maxStreak - 1) * maxStreak / 2) * perfect;
            int score2 = (totalNotes - maxStreak) * (great + (maxStreak - 1) * perfect);
            return score1 + score2;
        }
    }

    public void CollectStar() {
        stars++;
        Platform lastPlatform = Spawner.s?.platformManager?.GetLastPlatform();
        if (lastPlatform != null) {
            Vector3 positionStar = lastPlatform.transCache.position;
            TopBar.instance?.MoveStar(positionStar, stars);
        }

        if (remoteConfig.PlayNewSFX_Enable) {
            SoundManager.PlayIngame_Star();
        }
    }

    private void OnChangeEndlessRound(byte round) {
        if (enableEndless && remoteConfig.ProgressBar_StyleIndex == 3 && round > stars) {
            stars = round;
            if (EndlessIteration.IsActive) {
                EndlessIterationAnalysis.LogSongLoop(stars, true);
            }

            Platform lastPlatform = Spawner.s.platformManager.GetLastPlatform();
            if (lastPlatform != null) {
                Vector3 positionStar = lastPlatform.transCache.position;
                TopBar.instance?.MoveStar(positionStar, stars);
            }
        }
    }

    public void CheckAndSaveSongOfDay() {
        if (!_song.isSongOfDay) {
            return;
        }

        SaveScoreAndDiamond();
    }

    public void AddScore(int amount) {
        if (remoteConfig.FeverMode_IsEnable) {
            if (UIController.ui.InFever()) {
                int after = Mathf.RoundToInt(amount * remoteConfig.FeverMode_Multiply);
                if (after == amount) {
                    amount += 1;
                } else {
                    amount = after;
                }
            }
        }

        Score += amount;
    }

    private bool _isReversing;

    // Performance optimization: Pre-declared variables for FixedUpdate loop
    private Vector3 _tempMousePosition;
    private Vector3 _tempPosition;
    private float   _tempDeltaMove;

    private void OnStartReversing() {
        ShowChildsOfCamera(false);
        _isReversing = true;
    }

    private void OnEndReversing() {
        ShowChildsOfCamera(true);
        _isReversing = false;
    }

    private void ShowChildsOfCamera(bool isShow) {
        if (_childsOfCamera != null) {
            foreach (Transform child in _childsOfCamera) {
                child.gameObject.SetActive(isShow);
            }
        }
    }

    private void PlatformOnOnHit(Platform platform, int streakCount) {
        if (platform.sectionType != sectionType) {
            sectionType = platform.sectionType;
        }
    }

    public int GetCurrentEarnMoreStar() {
        return _highestStar - stars;
    }

    public int GetCurrentExp() {
        try {
            if (enableEndless && remoteConfig != null && remoteConfig.UserProgression_EndlessMode_LimitTile > 0) {
                return Mathf.Min(Spawner.jumpCount, remoteConfig.UserProgression_EndlessMode_LimitTile);
            }

            return Spawner.jumpCount;
        } catch (Exception e) {
            CustomException.Fire("[GetCurrentExp]", e.Message + " => " + e.StackTrace);
            return 0;
        }
    }

    public int GetTotalStar() {
        if (_isSavedScore || enableEndless)
            return Configuration.instance.GetCurrentStars();
        else
            return Configuration.instance.GetCurrentStars() + stars;
    }

    public bool ReachMaxTile() {
        if (enableEndless && remoteConfig.UserProgression_EndlessMode_LimitTile > 0) {
            return Spawner.jumpCount >= remoteConfig.UserProgression_EndlessMode_LimitTile;
        }

        return false;
    }

    private void MysteryDoorManagerOnCollectDrill() {
        EarnTokenInRoad(TokenType.MysteryDoor_Drill, 1);
    }

    private void HandleOnFinishCollectingMilestoneToken() {
        const TokenType type = TokenType.MilestoneToken;
        var value = _dictToken.GetValueOrDefault(type, 0);
        _dictToken[type] = value + 1;
        TopBar.instance.UpdateMilestoneTokenHUD();
    }

    public int GetTokenEarnResult() {
        if (MysteryDoorManager.CanEarnDrill(enableEndless)) {
            return CanReceiveDrill() ? GetTokenEarnedByType(TokenType.MysteryDoor_Drill) : 0;
        }

        if (LiveEventManager.instance && LiveEventManager.instance.IsActiveEvent) {
            return 0;
        }

        return 0;
    }

    public int GetTokenEarnedByType(TokenType type) {
        return _dictToken.GetValueOrDefault(type, 0);
    }

    private void SaveEarnedToken() {
        if (_acceptChallengeMode) {
            return;
        }

        if (canLoseCurrenciesOnFail) {
            _dictToken?.Clear();
            return;
        }

        string locationEarnToken;
        if (enableEndless) {
            locationEarnToken = TRACK_LOCATION.song_result_endless;
        } else {
            switch (stars) {
                case 0:
                    locationEarnToken = TRACK_LOCATION.song_result_0star;
                    break;

                case 1:
                    locationEarnToken = TRACK_LOCATION.song_result_1star;
                    break;

                case 2:
                    locationEarnToken = TRACK_LOCATION.song_result_2star;
                    break;

                default:
                    locationEarnToken = TRACK_LOCATION.song_result_3star;
                    break;
            }
        }

        foreach (var pair in _dictToken) {
            var type = pair.Key;
            var earned = pair.Value;

            switch (type) {
                case TokenType.MysteryDoor_Drill:
                    if (MysteryDoorManager.CanEarnDrill(enableEndless)) {
                        if (CanReceiveDrill()) {
                            if (enableEndless && ChallengeMode.IsActive) {
                                if (stars < 3) {
                                    //chỉ nhận normal
                                    earned -= GetTokenEarnedEndlessByType(type);
                                }
                            }

                            if (earned > 0) {
                                MysteryDoorManager.AddDrill(earned, _song.acm_id_v3, gameplayCount);
                            }
                        }
                    }
                    break;

                case TokenType.LiveEventOld:
                    if (LiveEventManager.isInstanced && LiveEventManager.instance.IsActiveEvent) {
                        int tokenEarn = GetTokenEarnedByType(type);
                        if (tokenEarn > 0) {
                            LiveEventManager.instance.AddToken(LiveEventManager.IdCurrentEvent, earned,
                                locationEarnToken);
                        }
                    }

                    break;

                case TokenType.MilestoneToken:
                    if (earned <= 0 || !MilestoneEvent.isAvailable) {
                        break;
                    }
                    if (enableEndless && ChallengeMode.IsActive) {
                        if (stars < 3) {
                            //chỉ nhận normal
                            earned -= GetTokenEarnedEndlessByType(type);
                        }
                    }

                    if (earned <= 0) {
                        break;
                    }
                    MilestoneEvent.instanceSafe.SaveEarnedTokens(earned);
                    MilestoneEventTracker.Track_TokenEarn(earned,
                        MilestoneEvent.instanceSafe.GetCurrentToken() + earned, _song.acm_id_v3, gameplayCount);
                    break;
            }
        }
    }

    public void CheckCompleteGame() {
        if (ChallengeMode.IsActive) {
            CompleteAndCheckChallengeMode();
        } else if (EndlessIteration.IsActive) {
            CompleteEndlessRound();
        } else {
            GameCompleted();
        }
    }
}

public enum TokenType {
    None              = 0,
    Hat               = 1,
    LiveEventOld      = 2,
    MysteryDoor_Drill = 11,
    MilestoneToken,
}