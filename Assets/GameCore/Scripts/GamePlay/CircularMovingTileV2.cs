using DG.Tweening;
using UnityEngine;

public class CircularMovingTileV2 : CircularMovingTile {
    private Tweener _currentTween;
    private Tweener _nextTween;

    protected override void UpdateTilePosition() { }

    protected override void UpdateDesiredStartPosition(Vector3 targetPosition, bool isCurrent,
                                                       CircularWayPoint wayPoint, bool isClockwise) {
        this.isClockwise = isClockwise;
        int id = isClockwise ? (isCurrent ? 1 : 3) : (isCurrent ? 0 : 2);

        wayPoint.currentPosition = _wayPoints[id];
        wayPoint.targetIndex = id;
    }

    protected override void OnHit(Platform pl, int streak) {
        base.OnHit(pl, streak);
        if (_currentPlatform != null && _nextPlatform != null) {
            if (_currentTween != null) {
                _currentTween.Kill();
            }

            _currentPoint.UpdateTargetPoint(isClockwise);
            _currentTween = DOTween
                .To(() => _currentPoint.currentPosition, x => _currentPoint.currentPosition = x,
                    _wayPoints[_currentPoint.targetIndex], Ball.b.JumpTime).OnUpdate(() => {
                    _currentPlatform.transCache.position = _currentPoint.currentPosition;
                });

            if (_nextTween != null) {
                _nextTween.Kill();
            }

            _nextPoint.UpdateTargetPoint(isClockwise);
            _nextTween = DOTween
                .To(() => _nextPoint.currentPosition, x => _nextPoint.currentPosition = x,
                    _wayPoints[_nextPoint.targetIndex], Ball.b.JumpTime).OnUpdate(() => {
                    _nextPlatform.transCache.position = _nextPoint.currentPosition;
                });
        }
    }

    private void OnDestroy() {
        if (_currentTween != null && _currentTween.IsActive()) {
            _currentTween.Kill();
        }
        if (_nextTween != null && _nextTween.IsActive()) {
            _nextTween.Kill();
        }
    }
}