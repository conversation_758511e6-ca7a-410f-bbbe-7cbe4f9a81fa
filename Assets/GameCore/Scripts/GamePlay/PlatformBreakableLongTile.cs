using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PlatformBreakableLongTile : PlatformLongTile {
    [Header("Breakable walls")]
    [SerializeField] private BoxCollider _boxCollider;

    [SerializeField] private GameObject[] wallVariants;
    [SerializeField] private Transform    parentHolder;

    private readonly float            _sizeWall = 1.2f;
    private          List<GameObject> _wallList    = new();

    protected override void UpdateTileWhenChangeSize(float scaleZ) {
        base.UpdateTileWhenChangeSize(scaleZ);
        var size = _boxCollider.size.z;
        parentHolder.transform.localPosition = new Vector3(size / 2f, 0, 0);
        parentHolder.SetScale(new Vector3(1 * GetRatioLength() / scaleZ, 1.5f, 1 * GetRatioWidth()));
        var scale = scaleZ * 10f;
        var remainder = scale % Mathf.FloorToInt(scale);
        var count = Mathf.FloorToInt(scaleZ * 10);

        //Scale up to fill the remainder amount
        parentHolder.SetScaleX(parentHolder.transform.localScale.x + remainder / count);

        for (int i = 0; i < count; i++) {
            var variant = i % 2 == 0 ? 1 : 0;
            var wall = Instantiate(wallVariants[variant], parentHolder.transform);
            wall.transform.localScale = Vector3.one;
            wall.transform.localPosition = Vector3.zero - new Vector3(i * _sizeWall, 0, 0);
            _wallList.Add(wall);
        }
    }

    public override void SetHyperBoost() {
        base.SetHyperBoost();
        foreach (var wall in _wallList) {
            if (!wall)
                continue;

            wall.gameObject.SetActive(false);
        }
    }
}