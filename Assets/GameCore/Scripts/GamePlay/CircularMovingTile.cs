using System;
using System.Collections;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

[Serializable]
public class CircularWayPoint {
    public int     targetIndex;
    public Vector3 currentPosition;

    public void UpdateTargetPoint(bool isClockwise) {
        if (isClockwise) {
            targetIndex = (targetIndex + 3) % 4;
        } else {
            targetIndex = (targetIndex + 1) % 4;
        }
    }
}

public class CircularMovingTile : MonoBehaviour {
    public    bool     isClockwise = false;
    protected Platform _currentPlatform;
    protected Platform _nextPlatform;
    private   float    _speed;
    private   float    _pathLength;
    private   int      _nextId;

    protected CircularWayPoint _currentPoint;
    protected CircularWayPoint _nextPoint;
    protected Vector3[]        _wayPoints = new Vector3[4];

    private readonly float _threshold = 0.1f;

    private void OnEnable() {
        Spawner.OnCompleteSpawnPlatform += FinalizeInitialization;
        Platform.OnHit += OnHit;
    }

    private void OnDisable() {
        Spawner.OnCompleteSpawnPlatform -= FinalizeInitialization;
        Platform.OnHit -= OnHit;
    }

    public void Update() {
        UpdateTilePosition();
    }

    private void UpdateTilePositionClockwise() {
        if (_currentPlatform != null) {
            switch (_currentPoint.targetIndex) {
                case 0:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.left;
                    if (_currentPoint.currentPosition.x < _wayPoints[0].x) {
                        _currentPoint.currentPosition.x = _wayPoints[0].x;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 1:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.back;
                    if (_currentPoint.currentPosition.z < _wayPoints[1].z) {
                        _currentPoint.currentPosition.z = _wayPoints[1].z;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 2:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.right;
                    if (_currentPoint.currentPosition.x > _wayPoints[2].x) {
                        _currentPoint.currentPosition.x = _wayPoints[2].x;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 3:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.forward;
                    if (_currentPoint.currentPosition.z > _wayPoints[3].z) {
                        _currentPoint.currentPosition.z = _wayPoints[3].z;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;
            }

            _currentPlatform.transCache.position = _currentPoint.currentPosition;
        }

        if (_nextPlatform != null) {
            switch (_nextPoint.targetIndex) {
                case 0:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.left;
                    if (_nextPoint.currentPosition.x < _wayPoints[0].x) {
                        _nextPoint.currentPosition.x = _wayPoints[0].x;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 1:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.back;
                    if (_nextPoint.currentPosition.z < _wayPoints[1].z) {
                        _nextPoint.currentPosition.z = _wayPoints[1].z;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 2:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.right;
                    if (_nextPoint.currentPosition.x > _wayPoints[2].x) {
                        _nextPoint.currentPosition.x = _wayPoints[2].x;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 3:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.forward;
                    if (_nextPoint.currentPosition.z > _wayPoints[3].z) {
                        _nextPoint.currentPosition.z = _wayPoints[3].z;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;
            }

            _nextPlatform.transCache.position = _nextPoint.currentPosition;
        }
    }

    private void UpdateTilePositionCounterClockwise() {
        if (_currentPlatform != null) {
            switch (_currentPoint.targetIndex) {
                case 0:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.back;
                    if (_currentPoint.currentPosition.z < _wayPoints[0].z) {
                        _currentPoint.currentPosition.z = _wayPoints[0].z;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 1:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.right;
                    if (_currentPoint.currentPosition.x > _wayPoints[1].x) {
                        _currentPoint.currentPosition.x = _wayPoints[1].x;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 2:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.forward;
                    if (_currentPoint.currentPosition.z > _wayPoints[2].z) {
                        _currentPoint.currentPosition.z = _wayPoints[2].z;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 3:
                    _currentPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.left;
                    if (_currentPoint.currentPosition.x < _wayPoints[3].x) {
                        _currentPoint.currentPosition.x = _wayPoints[3].x;
                        _currentPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;
            }

            _currentPlatform.transCache.position = _currentPoint.currentPosition;
        }

        if (_nextPlatform != null) {
            switch (_nextPoint.targetIndex) {
                case 0:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.back;
                    if (_nextPoint.currentPosition.z < _wayPoints[0].z) {
                        _nextPoint.currentPosition.z = _wayPoints[0].z;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 1:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.right;
                    if (_nextPoint.currentPosition.x > _wayPoints[1].x) {
                        _nextPoint.currentPosition.x = _wayPoints[1].x;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 2:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.forward;
                    if (_nextPoint.currentPosition.z > _wayPoints[2].z) {
                        _nextPoint.currentPosition.z = _wayPoints[2].z;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;

                case 3:
                    _nextPoint.currentPosition += Time.deltaTime * _speed * Ball.b.timeScale * Vector3.left;
                    if (_nextPoint.currentPosition.x < _wayPoints[3].x) {
                        _nextPoint.currentPosition.x = _wayPoints[3].x;
                        _nextPoint.UpdateTargetPoint(isClockwise);
                    }

                    break;
            }

            _nextPlatform.transCache.position = _nextPoint.currentPosition;
        }
    }

    protected virtual void UpdateTilePosition() {
        if (!GameController.IsPlaying || GameController.instance.game != GameStatus.LIVE)
            return;

        if (isClockwise) {
            UpdateTilePositionClockwise();
        } else {
            UpdateTilePositionCounterClockwise();
        }
    }

    private void FinalizeInitialization(Platform platform) {
        if (platform.noteID == _nextId) {
            _nextPoint = new CircularWayPoint();
            _nextPlatform = platform;
            _nextPlatform.elementType = NoteElementType.MovingCircle;
            _nextPlatform.DisableFlyEffect();
            UpdateDesiredStartPosition(_nextPlatform.transCache.position, false, _nextPoint, isClockwise);
            _nextPlatform.transCache.position = _nextPoint.currentPosition;
            if (_nextPlatform.Trail != null) {
                _nextPlatform.Trail.SetActive(true);
            }

            _nextPlatform.platformData.currNoteObj.elementType = NoteElementType.MovingCircle;
        }
    }

    public void Init(Platform currentPlatform, Vector3 nextPos) {
        _currentPlatform = currentPlatform;
        var curPos = _currentPlatform.transCache.position;
        var zRange = new Vector2(curPos.z, nextPos.z);
        var xRange = curPos.x <= nextPos.x ? new Vector2(curPos.x, nextPos.x) : new Vector2(nextPos.x, curPos.x);

        var minRange = Spawner.s.LineMap[4] - Spawner.s.LineMap[0]; //size equal to 2 lane
        var delta = Mathf.Abs(curPos.x - nextPos.x);
        if (delta < minRange) {
            var missingDist = minRange - delta;
            var half = missingDist / 2f;
            var leftPoint = curPos.x <= nextPos.x ? curPos.x - half : nextPos.x - half;
            var rightPoint = curPos.x <= nextPos.x ? nextPos.x + half : curPos.x + half;

            //Check bounding
            if (leftPoint < Spawner.s.LineMap[0]) {
                var carryOver = Mathf.Abs(leftPoint - Spawner.s.LineMap[0]);
                leftPoint = Spawner.s.LineMap[0];
                rightPoint += carryOver;
            }

            //Check bounding
            if (rightPoint > Spawner.s.LineMap[4]) {
                var carryOver = Mathf.Abs(rightPoint - Spawner.s.LineMap[4]);
                rightPoint = Spawner.s.LineMap[4];
                leftPoint -= carryOver;
            }

            xRange = new Vector2(leftPoint, rightPoint);
        }

        _wayPoints[0] = new Vector3(xRange.x, 0, zRange.x);
        _wayPoints[1] = new Vector3(xRange.y, 0, zRange.x);
        _wayPoints[2] = new Vector3(xRange.y, 0, zRange.y);
        _wayPoints[3] = new Vector3(xRange.x, 0, zRange.y);

        _nextId = currentPlatform.noteID + 1;
        _pathLength = Mathf.Abs(_wayPoints[1].x - _wayPoints[0].x + _wayPoints[2].z - _wayPoints[1].z) * 2f;
        _speed = RemoteConfigBase.instance.NewElements_MovingCircle_Speed;
        InitCurrentWaypoint(curPos);
    }

    private void InitCurrentWaypoint(Vector3 curPos) {
        _currentPoint = new CircularWayPoint();
        bool isClockwise = curPos.x > 0 ? true : false;
        UpdateDesiredStartPosition(curPos, true, _currentPoint, isClockwise);
        _currentPlatform.transCache.position = _currentPoint.currentPosition;
        if (_currentPlatform.Trail != null) {
            _currentPlatform.Trail.SetActive(true);
        }

        _currentPlatform.platformData.currNoteObj.elementType = NoteElementType.MovingCircle;
    }

    protected virtual void UpdateDesiredStartPosition(Vector3 targetPosition, bool isCurrent, CircularWayPoint wayPoint,
                                                      bool isClockwise) {
        this.isClockwise = isClockwise;

        wayPoint.currentPosition = targetPosition;
        float ballPos = Ball.b.transCache.position.z;
        float timeToMove = (targetPosition.z - ballPos) / Ball.b.GetBalLSpeed();

        //Get desired starting position,
        //so that when ball reached this tile, it is positioned at correct position according to midi file
        var travelBackDistance = timeToMove * _speed % _pathLength;

        if (isClockwise) {
            CalculatePositionClockwise(travelBackDistance, isCurrent, wayPoint);
        } else {
            CalculatePositionCounterClockwise(travelBackDistance, isCurrent, wayPoint);
        }
    }

    private void CalculatePositionClockwise(float travelBackDistance, bool isCurrent, CircularWayPoint wayPoint) {
        wayPoint.targetIndex = isCurrent ? 0 : 2;

        while (travelBackDistance > 0) {
            int previousIndex = (wayPoint.targetIndex + 1) % 4;
            var distToPrevious = previousIndex % 2 == 1
                ? Mathf.Abs(wayPoint.currentPosition.x - _wayPoints[previousIndex].x)
                : Mathf.Abs(wayPoint.currentPosition.z - _wayPoints[previousIndex].z);
            if (distToPrevious < _threshold) {
                wayPoint.targetIndex = previousIndex;
                previousIndex = (wayPoint.targetIndex + 1) % 4;
                distToPrevious = previousIndex % 2 == 1
                    ? Mathf.Abs(wayPoint.currentPosition.x - _wayPoints[previousIndex].x)
                    : Mathf.Abs(wayPoint.currentPosition.z - _wayPoints[previousIndex].z);
            }

            switch (wayPoint.targetIndex) {
                case 0:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.right * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.right * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;

                case 1:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.forward * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.forward * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;

                case 2:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.left * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.left * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;

                case 3:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.back * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.back * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;
            }
        }
    }

    private void
        CalculatePositionCounterClockwise(float travelBackDistance, bool isCurrent, CircularWayPoint wayPoint) {
        wayPoint.targetIndex = isCurrent ? 1 : 3;

        while (travelBackDistance > 0) {
            int previousIndex = (wayPoint.targetIndex - 1 + 4) % 4;
            var distToPrevious = previousIndex % 2 == 0
                ? Mathf.Abs(wayPoint.currentPosition.x - _wayPoints[previousIndex].x)
                : Mathf.Abs(wayPoint.currentPosition.z - _wayPoints[previousIndex].z);
            if (distToPrevious < _threshold) {
                wayPoint.targetIndex = previousIndex;
                previousIndex = (wayPoint.targetIndex - 1 + 4) % 4;
                distToPrevious = previousIndex % 2 == 0
                    ? Mathf.Abs(wayPoint.currentPosition.x - _wayPoints[previousIndex].x)
                    : Mathf.Abs(wayPoint.currentPosition.z - _wayPoints[previousIndex].z);
            }

            switch (wayPoint.targetIndex) {
                case 0:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.forward * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.forward * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;

                case 1:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.left * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.left * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;

                case 2:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.back * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.back * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;

                case 3:
                    if (travelBackDistance > distToPrevious) {
                        travelBackDistance -= distToPrevious;
                        wayPoint.currentPosition += Vector3.right * distToPrevious;
                    } else {
                        wayPoint.currentPosition += Vector3.right * travelBackDistance;
                        travelBackDistance = 0;
                    }

                    break;
            }
        }
    }

    protected virtual void OnHit(Platform pl, int streak) {
        if (_nextPlatform && pl.noteID == _nextPlatform.noteID) {
            DOVirtual.DelayedCall(3f, () => {
                Spawner.s.HitByCollector(_nextPlatform);
                Spawner.s.HitByCollector(_currentPlatform);
            });
        }
    }
}