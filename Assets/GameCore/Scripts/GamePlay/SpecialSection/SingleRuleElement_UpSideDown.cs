using System;
using System.Collections.Generic;
using GamePlay.Levels;

namespace TilesHop.GamePlay.NewElement {
    public class SingleRuleElement_UpSideDown : SingleRuleElement {
        public override NoteElementType elementType => NoteElementType.SpecialUpsideDown;
        private int[] _repeat;
        private int   _start;
        private int   _end;

        public SingleRuleElement_UpSideDown() {
            moodAppear = RemoteConfigBase.instance.NewElements_UpSideDown_MoodChange;
            songAppear = RemoteConfigBase.instance.NewElements_UpSideDown_Appear;
            starAppear = RemoteConfigBase.instance.NewElements_UpSideDown_Star;
        }

        public override void CheckExist(int songStart, int stars) {
            if (!Configuration.instance.isSpecialTileV2 && (songStart < songAppear || stars < starAppear)) {
                isExist = false;
                return;
            }

            isExist = true;
            _repeat = RemoteConfigBase.instance.NewElements_UpSideDown_Repeat;
            if (_repeat.Length != 2 || _repeat[1] < _repeat[0] || _repeat[0] <= 0) {
                _repeat = new[] {3, 5};
            }
        }

        public override NoteElementType Process(byte mood, NoteElementType defaultElementType) {
            return defaultElementType;
        }

        public override (int start, int end, NoteElementType type) ProcessSpecialSection(
            List<NoteData> noteList, byte count, float longDuration) {
            if (!Configuration.instance.isSpecialTileV2) {
                if (count < _repeat[0]) {
                    return (0, 0, NoteElementType.None);
                } else if (count < _repeat[1]) {
                    if (UnityEngine.Random.value < 0.5f) {
                        return (0, 0, NoteElementType.None);
                    }
                }
            }

            return ProcessSpecialSection(noteList, longDuration);
        }

        public override (int start, int end, NoteElementType type) ProcessSpecialSection(
            List<NoteData> noteList, float longDuration) {
            try {
                _start = 0;
                _end = 0;
                int total = noteList.Count;
                byte mood = 1;
                float timeAppear = 0;
                float timeDisappear = 0;

                for (int i = 6; i < total - 2; i++) {
                    if (noteList[i].mood != noteList[i + 1].mood) { //current note is mood change
                        mood++;
                        if (mood >= moodAppear) {
                            if (_start == 0) {
                                _start = i;
                                timeAppear = noteList[i].timeAppear;
                                continue;
                            } else if (_end == 0) {
                                _end = i - 1;
                                timeDisappear = noteList[_end].timeAppear;
                            } else {
                                break;
                            }
                        }

                        continue;
                    }

                    if (_start != 0 && _end == 0) {
                        if (noteList[i].timeAppear - timeAppear < 15)
                            continue;
                        if (noteList[i - 1].elementType == NoteElementType.MovingCircle)
                            continue;

                        if (noteList[i].elementType != NoteElementType.None) {
                            continue;
                        }

                        if (noteList[i].fakeTile != NoteData.FakeTile.NONE) {
                            continue;
                        }

                        if (noteList[i].duration >= longDuration) {
                            continue;
                        }

                        if (noteList[i].distance <= NotesManager.ShortDistanceValue) {
                            continue;
                        }

                        _end = i;
                        timeDisappear = noteList[_end].timeAppear;
                        break;
                    }
                }

                if (_start != 0 && _end != 0 && _end > _start) {
                    float time = 3;

                    int indexNote = _start;
                    for (int index = 1; index < total; index++) {
                        if (indexNote + index >= total) {
                            break;
                        }

                        float appear = noteList[indexNote + index].timeAppear - timeAppear;
                        if (appear < time) {
                            noteList.RemoveAt(indexNote + index);
                            total--;
                            index--;
                            _end--;
                        } else {
                            break;
                        }
                    }

                    indexNote = _end;
                    for (int index = 1; index < total; index++) {
                        if (indexNote + index >= total) {
                            break;
                        }

                        float appear = noteList[indexNote + index].timeAppear - timeDisappear;
                        if (appear < time) {
                            noteList.RemoveAt(indexNote + index);
                            total--;
                            index--;
                        } else {
                            break;
                        }
                    }

                    if (_start == 0 || _end == 0 || _end <= _start || _end >= noteList.Count - 1) {
                        //invalid data
                        return (0, 0, NoteElementType.None);
                    } else {
                        noteList[_start + 1].distance = noteList[_start + 1].timeAppear - noteList[_start].timeAppear;
                        noteList[_end + 1].distance = noteList[_end + 1].timeAppear - noteList[_end].timeAppear;
                        return (_start, _end, elementType);
                    }
                } else {
                    return (0, 0, NoteElementType.None);
                }
            } catch (Exception e) {
                CustomException.Fire("SingleRuleElement_UpSideDown.ProcessSpecialSection",e.Message.ToString());
            } 
            return (0, 0, NoteElementType.None);
        }
    }
}