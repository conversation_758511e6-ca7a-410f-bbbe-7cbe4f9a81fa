using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using UnityEngine;

namespace TilesHop.GamePlay.NewElement {
    public class SpecialTileConfig {
        public HashSet<NoteElementType>         orderSpecial;
        public List<int>                        songEndUnlockFeatures;
        public Dictionary<NoteElementType, int> showedSpecial;

        private const string KEY_SHOWED_SPECIAL_ELEMENT = "showed_special_element";

        public bool IsExist() {
            return orderSpecial != null && orderSpecial.Count != 0;
        }

        public SpecialTileConfig(string elementOrders, int[] elementSongCount) {
            orderSpecial = new HashSet<NoteElementType>();
            string[] splitData = elementOrders.Split(',');
            NoteElementType element;
            foreach (var data in splitData) {
                if (Enum.TryParse(data, out element)) {
                    orderSpecial.Add(element);
                }
            }

            songEndUnlockFeatures = elementSongCount.ToList();

            if (PlayerPrefs.<PERSON>ey(KEY_SHOWED_SPECIAL_ELEMENT)) {
                showedSpecial =
                    JsonConvert.DeserializeObject<Dictionary<NoteElementType, int>>(
                        PlayerPrefs.GetString(KEY_SHOWED_SPECIAL_ELEMENT));
            }

            if (showedSpecial == null) {
                showedSpecial = new Dictionary<NoteElementType, int>();
            }
        }

        public NoteElementType GetNewElementType(int songEnd, List<NoteElementType> ignores = null) {

            int targetForSongEnd = 0;
            foreach (int milestone in songEndUnlockFeatures) {
                if (milestone <= songEnd)
                    targetForSongEnd++;
                else
                    break;
            }

            if (showedSpecial.Count >= targetForSongEnd) {
                return NoteElementType.None;
            }

            foreach (var element in orderSpecial) {
                if (showedSpecial.ContainsKey(element))
                    continue;

                if (ignores != null && ignores.Contains(element)) {
                    continue;
                }

                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"NEW [{element}]");
                return element;
            }

            return NoteElementType.None;
        }

        public void ShowElementType(NoteElementType element, int songEnd) {
            if (showedSpecial.ContainsKey(element))
                return;

            showedSpecial.Add(element, songEnd);

            PlayerPrefs.SetString(KEY_SHOWED_SPECIAL_ELEMENT, JsonConvert.SerializeObject(showedSpecial));
        }

        public List<NoteElementType> GetAvailableElements() {
            return showedSpecial.Keys.Where(element => !element.IsSpecialSection()).ToList();
        }

        public IngameSectionType GetAvailableSpecialSection() {
            List<IngameSectionType> sections = new List<IngameSectionType>();
            foreach (var element in showedSpecial.Keys) {
                if (element.IsSpecialSection()) {
                    sections.Add(NotesManager.ElementTypeToSectionType(element));
                }
            }

            if (sections.Count == 0) {
                return IngameSectionType.Normal;
            } else {
                byte[] percents = new byte[sections.Count];
                int countTotal = 0;
                for (byte i = 0; i < sections.Count; i++) {
                    var section = sections[i];
                    switch (section) {
                        case IngameSectionType.Mirror:
                        case IngameSectionType.ZicZac:
                        case IngameSectionType.UpsideDown:
                            percents[i] = 45;
                            break;

                        case IngameSectionType.HyperBoost:
                            percents[i] = 10;
                            break;

                        default:
                            percents[i] = 20;
                            break;
                    }

                    countTotal += percents[i];
                }

                countTotal += 15;
                int random = UnityEngine.Random.Range(0, 99999) % countTotal;
                for (int i = 0; i < percents.Length; i++) {
                    random -= percents[i];
                    if (random <= 0) {
                        return sections[i];
                    }
                }

                return IngameSectionType.Normal;
            }
        }
    }
}