using UnityEngine;

namespace TilesHop.GamePlay.NewElement {
    public class SingleRuleElement_FadeOut : SingleRuleElement {
        public override NoteElementType elementType => NoteElementType.FadeOut;
        private byte _amount;
        private byte _lastMood = 0;

        public SingleRuleElement_FadeOut() {
            curRatio = ratio;
            moodAppear = RemoteConfigBase.instance.NewElements_FadeOut_MoodChange;
            songAppear = RemoteConfigBase.instance.NewElements_FadeOut_Appear;
            starAppear = RemoteConfigBase.instance.NewElements_FadeOut_Star;
        }

        public override void CheckExist(int songStart, int stars) {
            if (!Configuration.instance.isSpecialTileV2 && (songStart < songAppear || stars < starAppear)) {
                isExist = false;
                return;
            }

            isExist = true;
        }

        public override NoteElementType Process(byte mood, NoteElementType defaultElementType) {
            if (!isExist)
                return defaultElementType;
            if (mood < moodAppear)
                return defaultElementType;

            if (!mood.Equals(_lastMood)) {
                //new mood
                _lastMood = mood;
                _amount = 0;
            }

            if (_amount >= RemoteConfigBase.instance.NewElements_FadeOut_Amount)
                return defaultElementType;

            if (Random.Range(0, 9999) % 100 > curRatio) {
                curRatio++;
                return defaultElementType;
            } else {
                curRatio = ratio;
            }

            _amount++;
            return elementType;
        }
    }
}