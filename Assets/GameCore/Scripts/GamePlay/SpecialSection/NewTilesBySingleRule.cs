using System.Collections.Generic;
using System.Text;
using GamePlay.Levels;
using UnityEngine;

namespace TilesHop.GamePlay.NewElement {
    public class NewTilesBySingleRule {
        protected const NoteElementType DefaultLongNote   = NoteElementType.LongTile;
        private const   NoteElementType DefaultNormalNote = NoteElementType.None;

        protected HashSet<NoteElementType> _availableList;

        protected List<SingleRuleElement> _longElements;
        protected List<SingleRuleElement> _movingElements;
        protected List<SingleRuleElement> _trapElements;
        protected List<SingleRuleElement> _sectionElements;

        private int _lastIdElementNote = 0;

        public    bool            hasSpecialSection;
        public    int             specialSectionStart;
        public    int             specialSectionEnd;
        public    NoteElementType specialSectionType;
        protected byte            lastCountHasSpecial = 5;
        protected float           longNoteDuration;

        private Dictionary<NoteElementType, List<int>> _dictElements = new Dictionary<NoteElementType, List<int>>();

        private RemoteConfig _remoteConfig;

        public NewTilesBySingleRule() {
            _availableList = Configuration.GetListAutoGenElements();
            _availableList ??= new HashSet<NoteElementType>();
            _longElements = new List<SingleRuleElement>();
            _movingElements = new List<SingleRuleElement>();
            _trapElements = new List<SingleRuleElement>();
            _sectionElements = new List<SingleRuleElement>();
            _remoteConfig = RemoteConfigBase.instance;
        }

        public void SetAutoList(HashSet<NoteElementType> current) {
            _availableList = current;
        }

        public virtual void ResetData() {
            int songStart = AnalyticHelper.CountEvent(SONG_STATUS.song_start.ToString());
            int stars = Configuration.instance.GetCurrentStars();

            _longElements.Clear();
            _movingElements.Clear();
            _trapElements.Clear();
            _sectionElements.Clear();
            SingleRuleElement _priorityElement = null;

            foreach (var element in _availableList) {
                switch (element) {
                    case NoteElementType.LongTileBreak:
                        var brick = new SingleRuleElement_LongTileBrick();
                        brick.CheckExist(songStart, stars);
                        if (brick.isExist) {
                            _longElements.Add(brick);
                            if (brick.IsPriority(songStart)) {
                                _priorityElement = brick;
                            }
                        }

                        break;

                    case NoteElementType.MovingTile:
                        var movingTile = new SingleRuleElement_MovingTile();
                        movingTile.CheckExist(songStart, stars);
                        if (movingTile.isExist) {
                            _movingElements.Add(movingTile);
                            if (movingTile.IsPriority(songStart)) {
                                _priorityElement = movingTile;
                            }
                        }

                        break; 
                    case NoteElementType.Teleport:
                        var teleport = new SingleRuleElement_Teleport();
                        teleport.CheckExist(songStart, stars);
                        if (teleport.isExist) {
                            _movingElements.Add(teleport);
                            if (teleport.IsPriority(songStart)) {
                                _priorityElement = teleport;
                            }
                        }

                        break;

                    case NoteElementType.MovingCircle:
                        var movingCircle = new SingleRuleElement_MovingCircle();
                        movingCircle.CheckExist(songStart, stars);
                        if (movingCircle.isExist) {
                            _movingElements.Add(movingCircle);
                            if (movingCircle.IsPriority(songStart)) {
                                _priorityElement = movingCircle;
                            }
                        }

                        break;
                    
                    case NoteElementType.FakeConveyor:
                        var conveyor = new SingleRuleElement_Conveyor();
                        conveyor.CheckExist(songStart, stars);
                        if (conveyor.isExist) {
                            _trapElements.Add(conveyor);
                            if (conveyor.IsPriority(songStart)) {
                                _priorityElement = conveyor;
                            }
                        }

                        break;

                    case NoteElementType.FadeOut:
                        var fadeOut = new SingleRuleElement_FadeOut();
                        fadeOut.CheckExist(songStart, stars);
                        if (fadeOut.isExist) {
                            _trapElements.Add(fadeOut);
                            if (fadeOut.IsPriority(songStart)) {
                                _priorityElement = fadeOut;
                            }
                        }

                        break; 
                    case NoteElementType.FakeTile:
                        var fakeTile = new SingleRuleElement_FakeTile();
                        fakeTile.CheckExist(songStart, stars);
                        if (fakeTile.isExist) {
                            _trapElements.Add(fakeTile);
                            if (fakeTile.IsPriority(songStart)) {
                                _priorityElement = fakeTile;
                            }
                        }

                        break;

                    case NoteElementType.SpecialMirror:
                        var mirror = new SingleRuleElement_Mirror();
                        mirror.CheckExist(songStart, stars);
                        if (mirror.isExist) {
                            _sectionElements.Add(mirror);
                            if (mirror.IsPriority(songStart)) {
                                _priorityElement = mirror;
                            }
                        }

                        break;

                    case NoteElementType.SpecialHyperBoost:
                        var hyperBoost = new SingleRuleElement_HyperBoost();
                        hyperBoost.CheckExist(songStart, stars);
                        if (hyperBoost.isExist) {
                            _sectionElements.Add(hyperBoost);
                            if (hyperBoost.IsPriority(songStart)) {
                                _priorityElement = hyperBoost;
                            }
                        }

                        break;

                    case NoteElementType.SpecialUpsideDown:
                        var upsideDown = new SingleRuleElement_UpSideDown();
                        upsideDown.CheckExist(songStart, stars);
                        if (upsideDown.isExist) {
                            _sectionElements.Add(upsideDown);
                            if (upsideDown.IsPriority(songStart)) {
                                _priorityElement = upsideDown;
                            }
                        }

                        break;
                }
            }

            if (_remoteConfig.NewElements_AutoGen_Group_Start > 0 &&
                songStart >= _remoteConfig.NewElements_AutoGen_Group_Start) {
                SingleRuleElement temp;
                if (_movingElements.Count != 0) {
                    if (_movingElements.Contains(_priorityElement)) {
                        temp = _priorityElement;
                    } else {
                        int rand = Random.Range(0, 999) % _movingElements.Count;
                        temp = _movingElements[rand];
                    }

                    _movingElements.Clear();
                    _movingElements.Add(temp);
                }

                if (_trapElements.Count != 0) {
                    if (_trapElements.Contains(_priorityElement)) {
                        temp = _priorityElement;
                    } else {
                        int rand = Random.Range(0, 999) % _trapElements.Count;
                        temp = _trapElements[rand];
                    }

                    _trapElements.Clear();
                    _trapElements.Add(temp);
                }

                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                    $"Group setting applied!! song_start: {songStart} _priority: {(_priorityElement != null ? _priorityElement.elementType.ToString() : "NULL")}");
            }
        }

        public void ResetRuleShowSpecialSection(bool isReset) {
            lastCountHasSpecial = isReset ? byte.MinValue : byte.MaxValue; //for sure
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"lasCountHasSpecial after set: {lastCountHasSpecial}");
        }

        public virtual void Process(ref List<NoteData> noteList, ref HashSet<NoteElementType> elementList,
                                    float longNoteDuration) {
            hasSpecialSection = false;
            this.longNoteDuration = longNoteDuration;
            _dictElements.Clear();
            ProcessSpecialSection(ref noteList);

            if (hasSpecialSection) {
                lastCountHasSpecial = 0;
                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                    $"SpecialSection {specialSectionType} from {specialSectionStart} to {specialSectionEnd}");
            } else {
                lastCountHasSpecial++;
            }

            _lastIdElementNote = 0;
            byte moodChange = 1;
            int total = noteList.Count;
            for (int i = 0; i < total; i++) {
                if (hasSpecialSection) {
                    if (i == specialSectionStart || i == specialSectionEnd) {
                        _lastIdElementNote = i;
                        noteList[i].elementType = specialSectionType;
                        elementList.Add(noteList[i].elementType);
                        TryAdd(i, noteList[i].elementType);
                        continue;
                    }
                }

                //long tile
                if (noteList[i].duration >= this.longNoteDuration) {
                    noteList[i].elementType = GetLongNoteType(moodChange);
                    if (IsElementNew(noteList[i].elementType)) {
                        elementList.Add(noteList[i].elementType);
                        TryAdd(i, noteList[i].elementType);
                    }

                    continue;
                }

                //mood change
                if (i >= 6 && (i + 1 < total) && noteList[i].mood != noteList[i + 1].mood) {
                    moodChange++;
                    continue;
                }

                if (i < 5 || i > total - 5) {
                    continue;
                }

                if (noteList[i - 1].elementType == NoteElementType.MovingCircle)
                    continue;

                if (noteList[i].elementType != NoteElementType.None) {
                    _lastIdElementNote = i;
                    continue;
                }

                if (noteList[i].fakeTile != NoteData.FakeTile.NONE) {
                    //fake tile -> hold!!!
                    if (!noteList[i].elementType.IsFakeElement()) {
                        noteList[i].elementType = NoteElementType.FakeTile;
                    }

                    elementList.Add(noteList[i].elementType);
                    TryAdd(i, noteList[i].elementType);
                    continue;
                }

                if (noteList[i].distance <= NotesManager.ShortDistanceValue) {
                    continue;
                }

                if (i - _lastIdElementNote < 5) {
                    continue;
                }

                bool circle = noteList[i + 1].mood == noteList[i + 2].mood //next note isn't mood change
                              && noteList[i + 1].elementType == NoteElementType.None //next note isn't special tile
                              && noteList[i + 1].distance >
                              NotesManager.ShortDistanceValue //next note isn't short distance
                              && noteList[i + 1].fakeTile == NoteData.FakeTile.NONE //next note isn't fake note
                              && noteList[i + 1].duration < this.longNoteDuration //next note isn't long note
                    ;
                var elementType = GetElementType(moodChange, circle);
                noteList[i].elementType = elementType;
                if (IsElementNew(noteList[i].elementType)) {
                    _lastIdElementNote = i;
                    elementList.Add(noteList[i].elementType);
                    TryAdd(i, noteList[i].elementType); //add normal
                }

            }

#if UNITY_EDITOR
            ShowDict();
#endif

            void TryAdd(int i, NoteElementType type) {
                if (_dictElements.ContainsKey(type)) {
                    _dictElements[type].Add(i);
                } else {
                    _dictElements.Add(type, new List<int>() {i});
                }
            }
#if UNITY_EDITOR
            void ShowDict() {
                StringBuilder builder = new StringBuilder();
                builder.Append("All Element Note:\n");
                foreach (var element in _dictElements) {
                    builder.Append($"{element.Key.ToString()} : ");
                    foreach (var index in element.Value) {
                        builder.Append($"{index} ;");
                    }

                    builder.AppendLine();
                }

                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"{builder.ToString()}");
            }
#endif
        }

        protected virtual bool IsElementNew(NoteElementType elementType) {
            switch (elementType) {
                case DefaultNormalNote:
                case DefaultLongNote:
                    return false;
                case NoteElementType.MovingTile:
                    if (RemoteConfigBase.instance.NewElements_MovingTile_UsingGameStage) {
                        return false;
                    }
                    break;
                case NoteElementType.FakeTile:
                    if (RemoteConfigBase.instance.NewElements_FakeTile_UsingGameStage) {
                        return false;
                    }
                    break;
                default:
                    return true;
            }

            return true;
        }

        protected virtual void ProcessSpecialSection(ref List<NoteData> noteList) {
            if (_sectionElements.Count == 0) {
                return;
            }

            foreach (var sectionElement in _sectionElements) {
                (specialSectionStart, specialSectionEnd, specialSectionType) =
                    sectionElement.ProcessSpecialSection(noteList, lastCountHasSpecial, longNoteDuration);
                if (specialSectionType == NoteElementType.None) {
                    continue;
                }

                hasSpecialSection = true;
                break;
            }
        }

        protected virtual NoteElementType GetLongNoteType(byte mood) {
            foreach (var longElement in _longElements) {
                NoteElementType element = longElement.Process(mood, DefaultLongNote);
                if (element != DefaultLongNote) {
                    return element;
                }
            }

            return DefaultLongNote;
        }

        private NoteElementType GetElementType(byte mood, bool circle) {
            foreach (var trap in _trapElements) {
                if (!circle && trap is SingleRuleElement_MovingCircle)
                    continue;

                NoteElementType element = trap.Process(mood, DefaultNormalNote);
                if (element != DefaultNormalNote) {
                    return element;
                }
            }

            foreach (var moving in _movingElements) {
                if (!circle && moving is SingleRuleElement_MovingCircle)
                    continue;

                NoteElementType element = moving.Process(mood, DefaultNormalNote);
                if (element != DefaultNormalNote) {
                    return element;
                }
            }

            return DefaultNormalNote;
        }

        public string GetAllElement() {
            StringBuilder builder = new StringBuilder();
            foreach (var element in _dictElements) {
                if (builder.Length != 0) {
                    builder.Append(";");
                }

                builder.Append($"{element.Key.ToString()}:{element.Value.Count}");
            }

            return builder.ToString();
        }
    }
}