using UnityEngine;

namespace TilesHop.GamePlay.NewElement {
    public class SingleRuleElement_LongTile : SingleRuleElement {
        public override NoteElementType elementType => NoteElementType.LongTile;

        public SingleRuleElement_LongTile() {
            curRatio = ratio;
        }

        public override void CheckExist(int songStart, int stars) {
            isExist = true;
        }

        public override NoteElementType Process(byte mood, NoteElementType defaultElementType) {
            if (!isExist) {
                return defaultElementType;
            }

            if (Random.Range(0, 9999) % 100 > curRatio) {
                curRatio++;
                return defaultElementType;
            } else {
                curRatio = ratio;
            }

            return elementType;
        }
    }
}