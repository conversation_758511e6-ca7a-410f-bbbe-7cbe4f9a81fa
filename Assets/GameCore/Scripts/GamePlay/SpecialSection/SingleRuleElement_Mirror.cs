using System.Collections.Generic;
using GamePlay.Levels;

namespace TilesHop.GamePlay.NewElement {
    public class SingleRuleElement_Mirror : SingleRuleElement {
        public override NoteElementType elementType => NoteElementType.SpecialMirror;
        private int[] _repeat;
        private int   _start;
        private int   _end;

        public SingleRuleElement_Mirror() {
            moodAppear = RemoteConfigBase.instance.NewElements_Mirror_MoodChange;
            songAppear = RemoteConfigBase.instance.NewElements_Mirror_Appear;
            starAppear = RemoteConfigBase.instance.NewElements_Mirror_Star;
        }

        public override void CheckExist(int songStart, int stars) {
            if (!Configuration.instance.isSpecialTileV2 && (songStart < songAppear || stars < starAppear)) {
                isExist = false;
                return;
            }

            isExist = true;
            _repeat = RemoteConfigBase.instance.NewElements_Mirror_Repeat;
            if (_repeat.Length != 2 || _repeat[1] < _repeat[0] || _repeat[0] <= 0) {
                _repeat = new[] {3, 5};
            }
        }

        public override NoteElementType Process(byte mood, NoteElementType defaultElementType) {
            return defaultElementType;
        }

        public override (int start, int end, NoteElementType type) ProcessSpecialSection(
            List<NoteData> noteList, byte count, float longDuration) {
            if (!Configuration.instance.isSpecialTileV2) {
                if (count < _repeat[0]) {
                    return (0, 0, NoteElementType.None);
                } else if (count < _repeat[1]) {
                    if (UnityEngine.Random.value < 0.5f) {
                        return (0, 0, NoteElementType.None);
                    }
                }
            }

            return ProcessSpecialSection(noteList, longDuration);
        }

        public override (int start, int end, NoteElementType type) ProcessSpecialSection(
            List<NoteData> noteList, float longDuration) {
            _start = 0;
            _end = 0;
            int total = noteList.Count;
            byte mood = 1;
            for (int i = 6; i < total - 2; i++) {
                if (noteList[i].mood != noteList[i + 1].mood) { //current note is mood change
                    mood++;
                    if (mood >= moodAppear) {
                        if (_start == 0) {
                            _start = i + 1;
                            i++;
                        } else if (_end == 0) {
                            _end = i - 1;
                        } else {
                            break;
                        }
                    }

                    continue;
                }

                if (_start != 0 && _end == 0) {
                    if (noteList[i - 1].elementType == NoteElementType.MovingCircle)
                        continue;

                    if (noteList[i].elementType != NoteElementType.None) {
                        continue;
                    }

                    if (noteList[i].fakeTile != NoteData.FakeTile.NONE) {
                        continue;
                    }

                    if (noteList[i].duration >= longDuration) {
                        continue;
                    }

                    if (noteList[i].distance <= NotesManager.ShortDistanceValue) {
                        continue;
                    }

                    if (i - _start > 15) {
                        _end = i;
                        break;
                    }
                }
            }

            if (_start != 0 && _end != 0 && _end > _start) {
                return (_start, _end, elementType);
            } else {
                return (0, 0, NoteElementType.None);
            }
        }
    }
}