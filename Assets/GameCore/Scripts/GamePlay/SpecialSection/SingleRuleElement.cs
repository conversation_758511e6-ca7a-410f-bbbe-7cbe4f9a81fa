using System.Collections.Generic;
using GamePlay.Levels;

namespace TilesHop.GamePlay.NewElement {
    public abstract class SingleRuleElement {
        public    bool isExist;
        protected int  ratio = 70;
        protected int  curRatio;

        protected int moodAppear;
        protected int songAppear;
        protected int starAppear;

        public virtual NoteElementType elementType => NoteElementType.None;

        public bool IsPriority(int songStart) {
            return songStart == this.songAppear;
        }

        public void SetMoodAppear(int mood) {
            this.moodAppear = mood;
        }

        public void SetExist(bool value) {
            isExist = value;
        }

        public abstract void CheckExist(int songStart, int stars);
        
        public abstract NoteElementType Process(byte mood, NoteElementType defaultElementType);

        public virtual (int start, int end, NoteElementType type) ProcessSpecialSection(
            List<NoteData> noteList, byte count, float longDuration) {
            return (0, 0, NoteElementType.None);
        }

        public virtual (int start, int end, NoteElementType type) ProcessSpecialSection(
            List<NoteData> noteList, float longDuration) {
            return (0, 0, NoteElementType.None);
        }
    }
}