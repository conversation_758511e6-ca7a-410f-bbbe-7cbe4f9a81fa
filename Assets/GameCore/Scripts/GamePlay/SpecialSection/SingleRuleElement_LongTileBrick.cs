using UnityEngine;

namespace TilesHop.GamePlay.NewElement {
    public class SingleRuleElement_LongTileBrick : SingleRuleElement {
        public override NoteElementType elementType => NoteElementType.LongTileBreak;

        public SingleRuleElement_LongTileBrick() {
            curRatio = ratio;
            moodAppear = RemoteConfigBase.instance.NewElements_Brick_MoodChange;
            songAppear = RemoteConfigBase.instance.NewElements_Brick_Appear;
            starAppear = RemoteConfigBase.instance.NewElements_Brick_Star;
        }
        
        public override void CheckExist(int songStart, int stars) {
            if (!Configuration.instance.isSpecialTileV2 && (songStart < songAppear || stars < starAppear)) {
                isExist = false;
                return;
            }

            isExist = true;
        }

        public override NoteElementType Process(byte mood, NoteElementType defaultElementType) {
            if (!isExist) {
                return defaultElementType;
            }

            if (mood < moodAppear)
                return defaultElementType;

            return elementType;
        }
    }
}