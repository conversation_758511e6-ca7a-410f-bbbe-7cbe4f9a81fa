using System.Collections.Generic;

public class NewElementsTracking {
    public static void Track_OnboardingElementsImpression(NoteElementType obstacle_type, string song_acm_id) {
        string eventKey = "OnboardingElements_Impression";
        Dictionary<string, object> param = new Dictionary<string, object> {
            {TRACK_PARAM.obstacle_type, obstacle_type.ToString()},
            {"song_acm_id", song_acm_id},
            {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
        };
        AnalyticHelper.LogEvent(eventKey, param);
    }

    public static void Track_NewElementHit(NoteElementType obstacle_type, int timePass, bool is_fail) {
        string eventKey = "new_element_hit";
        Dictionary<string, object> param = new Dictionary<string, object> {
            {TRACK_PARAM.obstacle_type, obstacle_type.ToString()},
            {"is_fail", (is_fail ? 1 : 0)},
            {TRACK_PARAM.gameplay_count, GameController.gameplayCount},
            {TRACK_PARAM.song_note, timePass}
        };
        AnalyticHelper.LogEvent(eventKey, param);
    }
}