using System.Collections.Generic;
using GamePlay.Levels;
using UnityEngine;

namespace TilesHop.GamePlay.NewElement {
    public class NewTilesByUnlockElement : NewTilesBySingleRule {
        private NoteElementType _priorityElement = NoteElementType.None;

        public NewTilesByUnlockElement() {
            _longElements = new List<SingleRuleElement>();
            _movingElements = new List<SingleRuleElement>();
            _trapElements = new List<SingleRuleElement>();
            _sectionElements = new List<SingleRuleElement>();
        }

        public override void ResetData() {
            _availableList = new HashSet<NoteElementType>(Configuration.GetEarnedElements());
            _priorityElement = Configuration.GetPriorityEarnedElements();

            _longElements.Clear();
            _movingElements.Clear();
            _trapElements.Clear();
            _sectionElements.Clear();

            ProcessElementList();
#if UNITY_EDITOR
            TestElementList();
#endif
            foreach (var element in _availableList) {
                switch (element) {
                    case NoteElementType.LongTile:
                        var longTile = new SingleRuleElement_LongTile();
                        longTile.SetExist(true);
                        if (longTile.isExist) {
                            _longElements.Add(longTile);
                        }

                        break;

                    case NoteElementType.LongTileBreak:
                        var brick = new SingleRuleElement_LongTileBrick();
                        brick.SetExist(true);
                        if (brick.isExist) {
                            _longElements.Add(brick);
                        }

                        break;

                    case NoteElementType.MovingTile:
                        var movingTile = new SingleRuleElement_MovingTile();
                        movingTile.SetExist(true);
                        if (movingTile.isExist) {
                            _movingElements.Add(movingTile);
                        }

                        break;

                    case NoteElementType.FakeConveyor:
                        var conveyor = new SingleRuleElement_Conveyor();
                        conveyor.SetExist(true);
                        if (conveyor.isExist) {
                            _movingElements.Add(conveyor);
                        }

                        break;

                    case NoteElementType.Teleport:
                        var teleport = new SingleRuleElement_Teleport();
                        teleport.SetExist(true);
                        if (teleport.isExist) {
                            _movingElements.Add(teleport);
                        }

                        break;

                    case NoteElementType.MovingCircle:
                        var movingCircle = new SingleRuleElement_MovingCircle();
                        movingCircle.SetExist(true);
                        if (movingCircle.isExist) {
                            _movingElements.Add(movingCircle);
                        }

                        break;

                    case NoteElementType.FadeOut:
                        var fadeOut = new SingleRuleElement_FadeOut();
                        fadeOut.SetExist(true);
                        if (fadeOut.isExist) {
                            _trapElements.Add(fadeOut);
                        }

                        break;

                    case NoteElementType.FakeTile:
                        var fakeTile = new SingleRuleElement_FakeTile();
                        fakeTile.SetExist(true);
                        if (fakeTile.isExist) {
                            _trapElements.Add(fakeTile);
                        }

                        break;

                    case NoteElementType.SpecialMirror:
                        var mirror = new SingleRuleElement_Mirror();
                        mirror.SetExist(true);
                        if (mirror.isExist) {
                            _sectionElements.Add(mirror);
                        }

                        break;

                    case NoteElementType.SpecialHyperBoost:
                        var hyperBoost = new SingleRuleElement_HyperBoost();
                        hyperBoost.SetExist(true);
                        if (hyperBoost.isExist) {
                            _sectionElements.Add(hyperBoost);
                        }

                        break;

                    case NoteElementType.SpecialUpsideDown:
                        var upsideDown = new SingleRuleElement_UpSideDown();
                        upsideDown.SetExist(true);
                        if (upsideDown.isExist) {
                            _sectionElements.Add(upsideDown);
                        }

                        break;
                }
            }
        }

        private void TestElementList() {
            // _availableList = new HashSet<NoteElementType>() {
            //     NoteElementType.LongTile, NoteElementType.SpecialMirror
            // };
            //
            // Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Test process: {string.Join(";", _availableList)}");
        }

        private void ProcessElementList() {
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Priority element: {_priorityElement.ToString()}");
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Before process: {string.Join(";", _availableList)}");
            List<NoteElementType> longs = new List<NoteElementType>();
            List<NoteElementType> moving = new List<NoteElementType>();
            List<NoteElementType> fakes = new List<NoteElementType>();
            List<NoteElementType> sections = new List<NoteElementType>();

            bool priorityLong = _priorityElement.IsLongTileElement();
            bool priorityFake = _priorityElement.IsFakeElement();
            bool priorityMoving = _priorityElement.IsMovingElement();
            bool prioritySection = _priorityElement.IsSpecialSection();

            foreach (var element in _availableList) {
                if (!priorityLong && element.IsLongTileElement()) {
                    longs.Add(element);
                    continue;
                }

                if (!prioritySection && element.IsSpecialSection()) {
                    sections.Add(element);
                    continue;
                }

                if (!priorityFake && element.IsFakeElement()) {
                    fakes.Add(element);
                    continue;
                }

                if (!priorityMoving && element.IsMovingElement()) {
                    moving.Add(element);
                    continue;
                }
            }

            _availableList.Clear();
            if (_priorityElement != NoteElementType.None) {
                _availableList.Add(_priorityElement);
            }

            if (longs.Count != 0) {
                _availableList.Add(longs[Random.Range(0, 999) % longs.Count]);
            }

            if (moving.Count != 0) {
                _availableList.Add(moving[Random.Range(0, 999) % moving.Count]);
            }

            if (fakes.Count != 0) {
                _availableList.Add(fakes[Random.Range(0, 999) % fakes.Count]);
            }

            if (sections.Count != 0) {
                _availableList.Add(sections[Random.Range(0, 999) % sections.Count]);
            }

            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"After process: {string.Join(";", _availableList)}");
        }

        public override void Process(ref List<NoteData> noteDatas, ref HashSet<NoteElementType> listSpecialTiles,
                                     float longNoteDuration) {
            base.Process(ref noteDatas, ref listSpecialTiles, longNoteDuration);
            Configuration.ResetPriorityEarnedElements();
        }

        protected override bool IsElementNew(NoteElementType elementType) {
            switch (elementType) {
                case NoteElementType.None:
                    return false;

                case NoteElementType.MovingTile:
                    if (RemoteConfigBase.instance.NewElements_MovingTile_UsingGameStage) {
                        return false;
                    }
                    break;
                case NoteElementType.FakeTile:
                    if (RemoteConfigBase.instance.NewElements_FakeTile_UsingGameStage) {
                        return false;
                    }
                    break;

                default:
                    return true;
            }

            return true;
        }

        protected override NoteElementType GetLongNoteType(byte mood) {
            if (_longElements.Count != 0) {
                return _longElements[0].elementType;
            }

            return NoteElementType.None;
        }

        protected override void ProcessSpecialSection(ref List<NoteData> noteList) {
            if (_sectionElements.Count == 0) {
                return;
            }

            // rate 100% for priority
            bool foundPriority = false;
            if (_sectionElements[0].elementType == _priorityElement) {
                (specialSectionStart, specialSectionEnd, specialSectionType) =
                    _sectionElements[0].ProcessSpecialSection(noteList, longNoteDuration);
                foundPriority = true;

            }

            if (lastCountHasSpecial == 0) {
                //don't show too close
                return;
            } else if (lastCountHasSpecial == 1) {
                //rate 50% show up
                if (Random.Range(0, 9999) % 2 == 0) {
                    return; //dont show up!
                }
            } else {
                //rate 100%
            }

            if (!foundPriority) {
                (specialSectionStart, specialSectionEnd, specialSectionType) =
                    _sectionElements[0].ProcessSpecialSection(noteList, longNoteDuration);
            }

            if (specialSectionType != NoteElementType.None) {
                hasSpecialSection = true;
            }
        }
    }
}