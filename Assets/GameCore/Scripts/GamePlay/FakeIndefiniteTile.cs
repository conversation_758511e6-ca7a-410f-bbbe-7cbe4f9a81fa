using System;
using Music.ACM;
using Sirenix.OdinInspector;
using UnityEngine;

public class FakeIndefiniteTile : MonoBehaviour {

    private const float StandardAlterTime = 0.5f;

    [ShowInInspector] private int _myIndex;

    private Platform _platform;
    private bool     _isActive;

    private VFXFakeIndefiniteTile _vfxTransfer;
    private Vector3               _scaleVFX;
    private int                   _indexAlter;
    private float    _alterTime = 0.2f;

    private void Awake() {
        _indexAlter = Mathf.Clamp(RemoteConfigBase.instance.NewElements_TransformTrap_Index, 1, 5);
        _alterTime = Mathf.Clamp(RemoteConfigBase.instance.NewElements_TransformTrap_Time, 0.1f, 1f);
    }

    public void PlayVFX(int index, Platform platform) {
        this._platform = platform;
        this._myIndex = index;
        _isActive = true;
        _platform.ShowTopFire(false);
    }

    public void Stop() {
        _isActive = false;
        if (_vfxTransfer != null) {
            _vfxTransfer.Recycle();
            _vfxTransfer = null;
        }
    }

    public void OnHit(int index) {
        if (!_isActive) return;
        if (_myIndex != index + _indexAlter) return;
        _platform.ShowTopFire(true);
        _vfxTransfer = GameItems.instance.GetVFX(FakeTileStyle.FakeTransform, _platform.sq.transform);
        if (_vfxTransfer != null) {
            float speed = (StandardAlterTime / _alterTime) * Ball.b.timeScale;
            _vfxTransfer.Play(speed);
        }
    }
}