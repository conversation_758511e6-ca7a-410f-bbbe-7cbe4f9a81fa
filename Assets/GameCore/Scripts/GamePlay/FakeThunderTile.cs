using System;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;

public class FakeThunderTile : MonoBehaviour {
    [ShowInInspector]private bool     _isRunningVFX;
    private Tween    _tween;

    private const int OffsetStartShake = 2;
    private       int _noteId;
    public void SetData(int noteId) {
        this._noteId = noteId;
        Stop();
    }

    private void PlayVFX() {
        _isRunningVFX = true;
        Shake();
    }

    private void Shake() {
        _tween = this.transform.DOPunchRotation(new Vector3(0, 0, 10), 1f, 5).SetLoops(-1);
    }

    public void Stop() {
        if (_tween != null) {
            _tween.Kill();
            this.transform.localRotation = Quaternion.identity;
        }

        _isRunningVFX = false;
    }

    public void OnHit(int index) {
        if (_isRunningVFX) return;
        int offset = _noteId - index;
        if (offset <= OffsetStartShake) {
            PlayVFX();
        }
    }

    private void OnDestroy() {
        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }
}