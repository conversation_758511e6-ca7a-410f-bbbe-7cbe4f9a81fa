using System.Collections;
using System.Collections.Generic;
using Spine.Unity;
using UnityEngine;

public class Decorations : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameSubscriber {
    [SerializeField] private GameObject[]        skins;
    [SerializeField] private SkeletonAnimation[] bands;
    [SerializeField] private string[]            skinName;
    [SerializeField] private bool                allowChangingScale = true;
    [SerializeField] private float               animTimeScale      = 0.75f;

    private void Awake() {
        Spawner.onBgChange += OnBgChange;
        StopAnim();
    }

    private void Start() {
        GameController.instance.addSubcriber(this);
    }

    private void OnBgChange(byte current, byte next, float duration) {
        for (byte i = 0; i < skins.Length; i++) {
            skins[i].SetActive(false);
        }

        if (skins.Length > next) {
            skins[next].SetActive(true);
        }

        for (byte i = 0; i < bands.Length; i++) {
            bands[i].Skeleton.SetSkin(skinName[next]);
        }
    }

    private void StartAnim() {
        if (allowChangingScale) {
            for (byte i = 0; i < bands.Length; i++) {
                bands[i].timeScale = animTimeScale;
            }
        }
    }

    private void StopAnim() {
        if (allowChangingScale) {
            for (byte i = 0; i < bands.Length; i++) {
                bands[i].timeScale = 0;
            }
        }
    }

    public void gameStart() {
        StartAnim();
    }

    public void gameContinue() { }

    public void gameOver() {
        StopAnim();
    }
}