using System.Collections;
using System.Collections.Generic;
using Spine;
using Spine.Unity;
using UnityEngine;

public class SpineChangeSkin : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameSubscriber {
    [SerializeField] private SkeletonAnimation[] skeletonAnimations;
    private List<Slot> _slots = new List<Slot>();
    [SerializeField] int[] skinColorIndexes;

    private void Awake() {
        Spawner.onBgChange += OnBgChange;
    }

    private void Start() {
        GameController.instance.addSubcriber(this);
        foreach (var sm in skeletonAnimations) {
            foreach (Slot slot in sm.skeleton.Slots) {
                _slots.Add(slot);
            }
        }
    }
    
    private void OnBgChange(byte current, byte next, float duration) {
        StopAllCoroutines();
        StartCoroutine(Transition(current, next, duration));
    }

    private IEnumerator Transition(byte current, byte next, float duration) {
        if (next > skinColorIndexes.Length - 1) {
            Debug.LogError("[Transition] index out of skinColors");
            yield break;
        }

        float t = 0;
        float a = 1;

        foreach (Slot slot in _slots) {
            slot.A = a;
        }

        while (t < duration) {
            t += Time.deltaTime;
            a = Mathf.Lerp(1f, 0.1f, t / duration);
            foreach (Slot slot in _slots) {
                slot.A = a;
            }

            yield return null;
        }

        t = 0;
        foreach (SkeletonAnimation sm in skeletonAnimations) {
            if (!sm.gameObject.activeSelf) {
                continue;
            }
            var animations = sm.skeleton.Data.Animations;
            var shift = sm.skeleton.Data.Skins.Items.Length - skinColorIndexes.Length;
            sm.Skeleton.SetSkin(sm.skeleton.Data.Skins.Items[skinColorIndexes[next] + shift]);
            if (animations.Count > 1) {
                sm.state.SetAnimation(0, animations.Items[Random.Range(0, animations.Count)].Name, true);
            }
        }

        while (t < duration) {
            t += Time.deltaTime;
            a = Mathf.Lerp(0.1f, 1f, t / duration);
            foreach (Slot slot in _slots) {
                slot.A = a;
            }

            yield return null;
        }
    }

    public void gameStart() {
    }

    public void gameContinue() {
    }

    public void gameOver() {
    }
}