using DG.Tweening;
using UnityEngine;

public class SingleTileBrokenEffect : MonoBehaviour {
    private static           bool                   _isSetMaterialColored = false;
    [SerializeField] private ParticleSystemRenderer piecesParticleRenderer;
    [SerializeField] private SpriteRenderer[]       sprites;
    [SerializeField] private ParticleSystem         effect;
    [SerializeField] private GameObject             plinth;
    // private                  MaterialPropertyBlock  _materialPropertyBlock;
    private SpriteRenderer _referSpriteRenderer;
    private Transform      _movingPart;

    private GameObject _gameObject;
    private Transform  _transform;
    private float _startTrainPositionZ = float.MaxValue;
    
    public GameObject CachedGameObject {
        get {
            if (_gameObject == null) {
                _gameObject = gameObject;
            }
            return _gameObject;
        }
    }

    public Transform CachedTransform {
        get {
            if (_transform == null) {
                _transform = transform;
            }

            return _transform;
        }
    }

    #region Public Methods

    public void SetActive(bool isShow) {
        CachedGameObject.SetActive(isShow);
    }

    public void MakeFalldownEffect(int offset) {
        SetColor();
        DOVirtual.DelayedCall(0.05f * offset, FalldownEffect);
    }

    public void MakeBrokeInstantEffect(int offset) {
        SetColor();
        DOVirtual.DelayedCall(0.065f * offset, PlayEffect);
    }

    public void MakeTrainEffect(float startPositionZ) {
        SetColor();
        TrainEffect(startPositionZ);
    }

    public void SetRefers(SpriteRenderer spriteRenderer, Transform movingPart) {
        _referSpriteRenderer = spriteRenderer;
        _movingPart = movingPart;
        CachedTransform.localScale = _movingPart.localScale;
        var spriteScale = spriteRenderer.transform.localScale;
        foreach (SpriteRenderer sr in sprites) {
            sr.transform.localScale = spriteScale;
        }
    }
    
    public void SetColor() {
        if (!_referSpriteRenderer || !_referSpriteRenderer.sprite) {
            return;
        }
        
        foreach (SpriteRenderer spriteRenderer in sprites) {
            spriteRenderer.sprite = _referSpriteRenderer.sprite;
        }

        if (!_isSetMaterialColored) {
            _isSetMaterialColored = true;
            piecesParticleRenderer.sharedMaterial.SetTexture(ShaderAttribute.MainTex, _referSpriteRenderer.sprite.texture);
        }
    }
    
    #endregion
    
    private void FalldownEffect() {
        ShowSprites(true);
        plinth.SetActive(true);
        _movingPart.SetLocalY(6f);
        _movingPart.DOLocalMoveY(0f, 0.3f).SetEase(Ease.InQuint).OnComplete(PlayEffect);
    }

    private void TrainEffect(float startPositionZ) {
        ShowSprites(true);
        plinth.SetActive(true);
        _startTrainPositionZ = Mathf.Min(_startTrainPositionZ, startPositionZ);
        var t = (_startTrainPositionZ - _movingPart.position.z) / 200f;
        _movingPart.SetPositionZ(_startTrainPositionZ);
        _movingPart.DOLocalMoveZ(0f, t).SetEase(Ease.Linear).OnComplete(PlayEffect);
    }

    private void PlayEffect() {
        _isSetMaterialColored = false;
        effect.Play();
        ShowSprites(false);
        plinth.SetActive(false);
        _startTrainPositionZ = float.MaxValue;
    }

    private void ShowSprites(bool isShow) {
        foreach (SpriteRenderer spriteRenderer in sprites) {
            spriteRenderer.enabled = isShow;
        }
    }
}
