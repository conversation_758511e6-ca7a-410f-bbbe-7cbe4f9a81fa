using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using LDG.SoundReactor;

public class SpectrumVFX : MonoBehaviour {
    [SerializeField] private float            radius = 1.0f;
    [SerializeField] private Vector2          speed;
    [SerializeField] private SpectrumSource[] spectrumSources;

    private Vector2 _sinCos = Vector2.zero;
    private Vector3 _originalPos;

    private Vector2 _offset = Vector2.zero;

    // Use this for initialization
    private void Start() {
        _originalPos = transform.localPosition;

        var audioSource = SuperpoweredSDK.instance.GetAudioSource();
        SetAudioSource(audioSource);
    }

    private void OnEnable() {
        GroundMusic.OnUseMusic += GroundMusicOnOnUseMusic;
    }

    private void OnDisable() {
        GroundMusic.OnUseMusic -= GroundMusicOnOnUseMusic;
    }

    // Update is called once per frame
    private void Update() {
        _sinCos.x += speed.x * Time.deltaTime;
        _sinCos.y += speed.y * Time.deltaTime;

        _offset.x = Mathf.Sin(_sinCos.x) * radius;
        _offset.y = Mathf.Sin(_sinCos.y) * radius;

        transform.localPosition = new Vector3(_offset.x + _originalPos.x, _offset.y + _originalPos.y, _originalPos.z);
    }

    private void SetAudioSource(AudioSource audioSource) {
        if (audioSource != null) {
            foreach (var spectrumSource in spectrumSources) {
                if (spectrumSource != null) {
                    spectrumSource.SetAudioSource(audioSource);
                } else {
                    Logger.EditorLogError("Null spectrum sources!!!!");
                }
            }
        } else {
            Logger.EditorLogError("Null auido sources!!!!");
        }
    }

    private void GroundMusicOnOnUseMusic() {
        var audioSource = GroundMusic.instance.GetAudioSource();
        SetAudioSource(audioSource);
    }

    public void OnLevel(PropertyDriver driver) {
        Vector3 level = driver.LevelVector();
        speed.x = level.x;
        speed.y = level.y;
        radius = level.z;
    }
}