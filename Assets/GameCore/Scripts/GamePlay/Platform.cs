using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using GamePlay.Levels;
using Inwave;
using PathCreation;
using UnityEngine.Serialization;
using Random = UnityEngine.Random;
using Music.ACM;
using TilesHop.GamePlay.NewElement;
using UnityEngine.Events;
using Utils = Inwave.Utils;
using UnityEngine.Pool;

public struct ShaderAttribute {
    public static readonly string ColorFromName = "_ColorFrom";
    public static readonly string ColorToName   = "_ColorTo";
    public static readonly string MainTexName   = "_MainTex";
    public static readonly string ColorName     = "_Color";
    public static readonly int    MainTex       = Shader.PropertyToID(MainTexName);
    public static readonly int    ColorFrom     = Shader.PropertyToID(ColorFromName);
    public static readonly int    ColorTo       = Shader.PropertyToID(ColorToName);
}

[Serializable]
public struct VFXHLWInfo {
    public List<ParticleSystem> hitEffs;
    public List<ParticleSystem> perfectHitEffs;
    public List<ParticleSystem> strongnoteEffs;
}

public partial class Platform : MonoBehaviour {
    #region Fields

    [HideInInspector] public UnityEvent<bool> OnChangeSlideState;
    public static event Action<Platform, int> OnHit;
    public static event Action<Platform, int> OnReachPlatform;

    //~~~~~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~~~~~
    [SerializeField] public bool isCustom         = false;
    [SerializeField] public bool isMoodTile       = false;
    [SerializeField] public bool isTileTrySkin    = false;
    [SerializeField] public bool isSlideTile      = false;
    [SerializeField] public bool endingMoodChange = false;

    internal IngameSectionType sectionType = IngameSectionType.Normal;

    static                   ParticleSystem.MinMaxCurve brustGravityModifier = new ParticleSystem.MinMaxCurve(0);
    private                  Color                      fireColor            = Color.black;
    [HideInInspector] public bool                       forward              = false;

    public                      bool isMoving        = false;
    [ReadOnly] public bool isHitchhikeTile = false;
    [ReadOnly] public bool isForceMove     = false;

    private                  float     sinkTime = 0;
    private                  float     flyTime  = 0;
    private                  Vector3   flyDistancePosition;
    private                  Vector3   flyEndPosition;
    [HideInInspector] public float     distance         = 0; //5
    private                  float     currentCurveTime = 0; //0 => 1
    private                  float     movingTime       = 0;
    [SerializeField]         Transform container;
    protected                bool      isClone             = false;
    protected                bool      _isGradientMaterial = false;

    [SerializeField, ReadOnly] private FakeTileStyle _fakeTileStyle = FakeTileStyle.None;

    public                     GameObject       sq;
    [SerializeField] protected SpriteRenderer[] sr;
    [SerializeField] protected SpriteRenderer   srStrongNoteFx;

    public Animator perfectAnim;

    #region Vfxs Controller

    [Header("VFXs Controller ~~~~~~~~~~~~~~~~~~~~~~~~~")]
    [Header("Perfect Count < 4 ~~~~~")]
    [Tooltip("Perfect Count < 4, cannot be empty")]
    [SerializeField]
    private ParticleSystem psHit1;

    [SerializeField] private ParticleSystemRenderer psHit1Renderer;
    [SerializeField] private bool                   isChangeColorHit1 = true;

    [Tooltip("Perfect Count < 4, can be empty")] [SerializeField]
    private ParticleSystem psHit2;

    [SerializeField] private ParticleSystemRenderer psHit2Renderer;
    [SerializeField] private bool                   isChangeColorHit2 = true;

    [Header("Perfect Count >= 4 ~~~~~")] [Tooltip("Perfect Count >= 4")] [SerializeField]
    private ParticleSystem[] psHit3;

    [SerializeField] private bool isChangeColorHit3 = true;

    [Header("Strong note ~~~~~")] [Tooltip("Strong Note")] [SerializeField]
    private ParticleSystem[] psHit4;

    [SerializeField] private bool isChangeColorHit4 = true;

    [Header("Center perfect vfx ~~~~~")] [Tooltip("Perfect, can be on/off on remote")] [SerializeField]
    private ParticleSystem psPerfectLightPillar;

    [Tooltip("Perfect Childs")] [FormerlySerializedAs("perfectLightPillar")] [SerializeField]
    private ParticleSystem[] psPerfectLightPillarChilds;

    [Header("Halloween VFX ~~~~~")] [Tooltip("HLW")] [SerializeField]
    private VFXHLWInfo psHitHLW;

    [Header("~~~~~~~~~~~~~~~~~~~~~~~~~")] [Header("VFXs Controller V2 ~~~~~~~~~~~~~~~~~~~~~~~~~")] [SerializeField]
    private PlatformVFXControllerV2 vfxControllerV2;

    [SerializeField] private GameObject[] listObjectsOffWhenFade;

    [Header("~~~~~~~~~~~~~~~~~~~~~~~~~")]

    #endregion

    [SerializeField]
    private ParticleSystem tileStarEffect;

    [SerializeField] private ParticleSystem focusPerfect;

    [SerializeField] private ParticleSystem psSnow;

    [Tooltip("Only for theme 16 HLW")] [SerializeField]
    private ParticleSystem psMagicEffect;

    public                     LineBreak      text;
    [SerializeField] protected SpriteRenderer topFire;
    public                     Transform      transCache;
    [SerializeField] protected Transform      sqTransCache;
    public                     ParticleSystem brustEffect;
    ParticleSystem.MainModule                 brustEffectMain;

    private List<Platform> _fakeTiles = ListPool<Platform>.Get(); //dùng cho stage FAKE_TILE + LATE_FAKE
    private List<Diamond> _attachDiamonds = new List<Diamond>();
    [ReadOnly] public bool  hitted          = false;
    public static     float lastSwitchTime  = 0;
    public static     bool  updatedTextures = false;
    [SerializeField]  bool  isHitImageType  = true;

    private TrySkin         _trySkinObject;
    private ISpecialPointer _specialItem;
    public  Transform       trySkinParent;

    [HideInInspector] public int trySkinID;

    //public GameObject model3D;
    [ReadOnly] public int noteID; // index of note in noteDatas(NotesManager)

    [ReadOnly] public float nextNoteDistance;
    [ReadOnly] public float timeSlide = 0;

    public NoteData.Timbre timbre;
    public PlatformType    platformType; //có thể cùng stage nhưng khác platformType, nên ko dùng platformData được
    public NoteElementType elementType; // dùng làm tính năng các tile đặc biệt
    public PlatformData    platformData;

    [SerializeField] protected MeshRenderer plinthMeshRenderer;
    
    private MaterialPropertyBlock _plinthMaterialPropertyBlock;

    [HideInInspector] public  bool       isMade = false;
    [HideInInspector] public  int        indexSkin; // index skin of platform
    [SerializeField]  private GameObject objEfxFocus;

    [SerializeField] List<MeshRenderer> tileMeshRenderers;
    [SerializeField] Animation          tileAnimation;

    [SerializeField] private bool cloneUsePlinth = true;

    [SerializeField] private Transform  pivot;
    [SerializeField] private GameObject trail;

    public GameObject Trail => trail;

    //~~~~~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~~~~~
    protected Color _currentColor;

    private List<KeyValuePair<ParticleSystem.MainModule, float>> _gravityList =
        ListPool<KeyValuePair<ParticleSystem.MainModule, float>>.Get();

    public const float   GravityEarth = 9.8f;
    private      Vector3 topFireScale;
    private      float   topFireCloneScale = 1.053f;

    private Tweener _tweenMove;

    private bool      _isFindAnimStrongNote;
    private Animation _animStrongNote;

    private Vector3 containerLocalPosition => container.localPosition;
    protected SpriteRenderer spritePerfect;
    protected bool           isSliding;

    private AnimationCurve curveMoving => Configuration.instance.curve_moving;
    private bool    _isOnlyMoveWhenPlay;
    private float   _tileMaxPositionX;
    private SkinSet _skinSet;

    [NonSerialized] public bool        isRainbowTile = false;
    [NonSerialized] public PathCreator rainbow;

    private Vector3 _originalPosition;
    private Vector3 _newPosition;
    private Vector3 _originalSqrScale;

    private Follow        _cacheFollow;
    private BoxCollider   _cacheCollider;
    private TileBehaviour _tileBehaviour;
    private Transform     tfTrySkin;

    private bool    _isScaledVFX = false;
    private Vector3 _oldScale    = new Vector3(4.3f, 0.1f, 3.225f);

    protected BoxCollider CacheCollider {
        get {
            if (_cacheCollider == null) {
                TryGetComponent(out _cacheCollider);
            }

            return _cacheCollider;
        }
    }

    private Follow FollowInstance {
        get {
            if (_cacheFollow == null) {
                _cacheFollow = SingletonManager.instance.GetSingleton<Follow>();
            }

            return _cacheFollow;
        }
    }

    private GameController _cacheGameController;

    private GameController GameControllerInstance {
        get {
            if (_cacheGameController == null) {
                _cacheGameController = SingletonManager.instance.GetSingleton<GameController>();
            }

            return _cacheGameController;
        }
    }

    private BoxCollider _boxColliderTile;

    protected BoxCollider BoxColliderTile {
        get {
            if (_boxColliderTile == null) {
                _boxColliderTile = GetComponent<BoxCollider>();
            }

            return _boxColliderTile;
        }
    }

    private bool CanInteractWithBall {
        get {
            if (hitted) {
                return false; //  đã va chạm rồi
            }

            if (Ball.b.isInZicZacSection) {
                return false; // vẫn đang trong ziczac tile
            }

            if (Ball.b.onLongTile) {
                return false; //  vẫn đang trên longtile
            }

            if (Ball.b.onHitchTile) {
                return false; //  vẫn đang trên longtile
            }

            if (GameControllerInstance.game == GameStatus.LIVE) {
                return true; // game đang play
            }

            if (GameControllerInstance.game == GameStatus.P_DIE) {
                return true; // game đang play
            }

            return false;
        }
    }

    private bool IsColorStage {
        get {
            if (platformData.stage == Stage.COLORING) {
                return true;
            }

            if (platformData.stage == Stage.COLOR_CHOOSING) {
                return true;
            }

            if (platformData.platformType == PlatformType.MOOD_SHORT) {
                return true;
            }

            if (platformData.platformType == PlatformType.MOOD_FULL &&
                !RemoteConfigBase.instance.Musicalization_MoodChange_FullTileSameColor) {
                return true;
            }

            return false;
        }
    }

    protected const string FadeInOut = "FadeInOut";
    protected const string AlphaOne  = "AlphaOne";

    private HatItemScript _hatItemScript;

    private const            int   MinSortingOrder = 50;
    public const             int   MaxSortingOrder = 2000;
    [SerializeField] private float SafeDistance    = 6;

    protected static int  _currentOrder = MaxSortingOrder;
    private          bool _isUsePlinth; //If don't use plinth => don't use sr[]

    public static float tileAppearSpeed = 5f;

    private static readonly int _color = Shader.PropertyToID("_Color");

    public bool IsUsePlinth => _isUsePlinth;

    public bool IsSingleNote {
        get {
            return !platformType.IsLongTile();
            // if (platformType is PlatformType.FAKE) {
            //     return true;
            // }
            // return elementType is NoteElementType.None && platformType is PlatformType.NORMAL;
        }
    }

    #endregion

    #region Unity method


    protected virtual void Awake() {
        _isUsePlinth = plinthMeshRenderer != null;
        
        if (perfectAnim != null) {
            perfectAnim.gameObject.TryGetComponent(out spritePerfect);
        }

        _tileMaxPositionX = RemoteConfigBase.instance.GetTileMaxPositionX();
    }

    private void OnEnable() {
        OnHit += OnPlatformHit;
        Ball.OnJumpToEndHuman += DisableOnJumpToHuman;
        FeverModeController.OnFeverMode += FeverModeControllerOnOnFeverMode;
        GameController.OnChangeSectionType += GameControllerOnOnChangeSectionType;
        if (RemoteConfigBase.instance.FeverMode_IsEnable && UIController.ui != null) {
            FeverModeControllerOnOnFeverMode(UIController.ui.InFever());
        }
    }

    protected virtual void OnDisable() {
        sinkTime = 0;
        flyTime = 0;
        OnHit -= OnPlatformHit;
        Ball.OnJumpToEndHuman -= DisableOnJumpToHuman;
        FeverModeController.OnFeverMode -= FeverModeControllerOnOnFeverMode;
        GameController.OnChangeSectionType -= GameControllerOnOnChangeSectionType;
        TryRecycleTeleportingComponent();
        
        ShowTidyFx(false);
        _hasDoneTidy = false;
        
        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }

    protected virtual void Update() {
        if (GameController.IsPlaying || GameControllerInstance.game == GameStatus.REWIND) {
            _newPosition = transCache.position;

            if (flyTime > 0) {
                flyTime -= Time.deltaTime * tileAppearSpeed;
                _newPosition = flyEndPosition +
                               flyDistancePosition * Configuration.instance.defaultCurve.Evaluate(flyTime);
            } else if (flyTime < 0) {
                _newPosition = flyEndPosition;
                flyTime = 0;
                OnCompletedMoving();
            }

            if (isMoving) {
                if (!_isOnlyMoveWhenPlay || (_isOnlyMoveWhenPlay && GameControllerInstance.game == GameStatus.LIVE)) {
                    if (forward && currentCurveTime > 1) {
                        forward = false;
                    } else if (!forward && currentCurveTime < 0) {
                        forward = true;
                    }

                    if (forward) {
                        currentCurveTime += Time.deltaTime / movingTime; // 0 => 1
                    } else {
                        currentCurveTime -= Time.deltaTime / movingTime; // 1 => 0
                    }

                    _newPosition.x = distance * curveMoving.Evaluate(currentCurveTime); // -5 <=> +5
                }
            }

            if (_tileConveyorRunner != null) {
                _newPosition.x = _tileConveyorRunner.Process(_newPosition.x);
            }

            if (_teleportMovingTile != null) {
                _newPosition.x = _teleportMovingTile.xPos;
            }

            if (sinkTime > 0) {
                sinkTime -= Time.deltaTime * 7f;
                float a = Configuration.instance.curve.Evaluate(sinkTime) * RemoteConfigBase.instance.Tile_Sag;

                if (this.platformType == PlatformType.SKEW_SPIRAL || this.platformType == PlatformType.SKEW) {
                    // Lùi theo theo vector quay của tile
                    _newPosition = _originalPosition + a * transCache.up;
                } else {
                    _newPosition.y = a;
                }
            } else {
                if (platformType == PlatformType.SKEW_SPIRAL) {
                    // platform SkewSpiral thì không reset Y
                } else if(PlatformManager.tileAppearanceType is TileAppearanceType.BASIC) {
                    _newPosition.y = 0;
                }
            }

            float positionZOfCam = FollowInstance.transformCamera.position.z;
            float scaleZ = Mathf.Lerp(1f, 2f, (transCache.position.z - 10 - positionZOfCam) / 45);
            if (!isSlideTile && platformData.currNoteObj.elementType != NoteElementType.MovingCircle) {
                transCache.SetScaleZ(scaleZ);
            }

            if (platformType.CanAnimateShowUp() &&
                platformData.currNoteObj.elementType != NoteElementType.MovingCircle) {
                container.SetLocalZ(Mathf.Lerp(0, 5, (_newPosition.z - 10 - positionZOfCam) / 45));
            }

            if (Spawner.s.fadeOutTileDistance > 0) {
                Color c = _currentColor;
                c.a = GetTileAlpha();
                if (_fakeTileStyle.IsClone()) {
                    fireColor.a = containerLocalPosition.z >= Spawner.s.fadeOutTileDistance
                        ? GetAlphaByPosition(RemoteConfigBase.instance.fakeTileAlpha)
                        : RemoteConfigBase.instance.fakeTileAlpha;
                    SetTopFireColor(fireColor);
                }

                SetTopColor(c);
            }

            transCache.position = _newPosition;
        }

        _tileBehaviour?.Update();
    }

    private void OnDestroy() {
        if (_tweenMove != null && _tweenMove.IsActive()) {
            _tweenMove.Kill();
        }
        ListPool<Platform>.Release(_fakeTiles);
        ListPool<KeyValuePair<ParticleSystem.MainModule, float>>.Release(_gravityList);
    }

    private void DoBaseAppearance() {
        _newPosition = flyEndPosition +
                       flyDistancePosition * Configuration.instance.defaultCurve.Evaluate(flyTime);
    }

    public virtual void Reset() {
        hitted = false;
        ResetFade();
        sinkTime = 0;
        isMoving = false;
        isRainbowTile = false;
        endingMoodChange = false;
        isHitchhikeTile = false;
        isForceMove = false;
        if (trail != null) {
            trail.SetActive(false);
        }

        listOrbPositions = new List<Vector3>();
        if (listOrbs == null) {
            listOrbs = new List<RhythmNote>();
        } else {
            RecycleOrbs();
        }

        if (elementType != NoteElementType.None) {
            ResetElementType();
        }

        RecycleFakeTiles(); // có khả năng là ko cần

        if (isMirrorTile) {
            RecycleMirrorTile();
        }

        mirrorTile = null;

        ResetHyperBoost();
        ResetZicZac();
        ResetHatItem();
        ResetDiamond();
        ShowTopFire(false);
        platformData.Reset();
        indexSkin = -1; //reset index skin
        HideTextInTile();
        if (objEfxFocus != null) {
            objEfxFocus.SetActive(false);
        }

        //characters
        ResetTrySkin();

        if (_specialItem != null) {
            GameItems.instance.DisableVFXSpecialSection(_specialItem);
            _specialItem = null;
        }

        SetPerfectSize();
        HidePerfectAnim();
        gameObject.SetActive(true);

        if (_isUsePlinth) {
            plinthMeshRenderer.gameObject.SetActive(true);
        }
    }

    /// <summary>
    /// Check va chạm với ball ở Trigger Stay
    /// </summary>
    /// <param name="other"></param>
    private void OnTriggerStay(Collider other) {
        if (!CanInteractWithBall)
            return; // không đủ điều kiện để va chạm với ball

        if (other.CompareTag(GAMETAG.Ball)) {
            TriggerWithBall();
        }
    }

    //Check va cham voi Collector
    protected virtual void OnTriggerExit(Collider col) {
        if (GameControllerInstance.game != GameStatus.LIVE) {
            return;
        }

        if (elementType == NoteElementType.MovingCircle) {
            return;
        }

        if (col.CompareTag(GAMETAG.Collector)) {
            if (Ball.b.CheckHumanEnd(noteID)) {
                return;//dont pool end tile for characters
            }

            Spawner.s.HitByCollector(this);
        }
    }

    #endregion

    private void OnPlatformHit(Platform hitPlatform, int streakPerfect) {
        OnProcessPlatformHit(hitPlatform.noteID);

        switch (hitPlatform.elementType) {
            case NoteElementType.MoodChangeInstant:
                if (noteID > hitPlatform.noteID && _fakeTileStyle is FakeTileStyle.None) {
                    StartCoroutine(IEBrokenInstant(noteID - hitPlatform.noteID - 1));
                }

                break;

            case NoteElementType.MoodChangeOrder:
                if (noteID > hitPlatform.noteID && _fakeTileStyle is FakeTileStyle.None) {
                    StartCoroutine(IEBrokenFallback(noteID - hitPlatform.noteID - 1));
                }

                break;

            case NoteElementType.MoodChangeTrain:
                if (noteID > hitPlatform.noteID && _fakeTileStyle is FakeTileStyle.None) {
                    StartCoroutine(IEBrokenTrain(hitPlatform.transCache.position.z + 100));
                }

                break;
        }
    }

    public void ForceProcessPlatformHit(int currentIndexNote) {
        OnProcessPlatformHit(currentIndexNote);
        foreach (var fakeTile in _fakeTiles) {
            fakeTile.OnProcessPlatformHit(currentIndexNote);
        }
    }

    private void OnProcessPlatformHit(int currentIndexNote) {
        _tileBehaviour?.OnHit(currentIndexNote);
        switch (_fakeTileStyle) {
            case FakeTileStyle.Fake_Thunder:
                _fakeThunderTile?.OnHit(currentIndexNote);
                break;

            case FakeTileStyle.FakeTransform:
                _fakeTransform?.OnHit(currentIndexNote);
                break;
        }

        switch (elementType) {
            case NoteElementType.MoodChangeBroken: {
                if (currentIndexNote <= this.noteID) {
                    GameItems.instance.ShowMoodChangeBroken((byte) (this.noteID - currentIndexNote));
                }

                break;
            }
        }

        // handle on hit for this note
        if (currentIndexNote == noteID) {
            if (platformType is PlatformType.MOOD_FULL || elementType.IsMoodChangeElement()) {
                PlatformManager.SwitchTileAppearanceOnMoodChange();
            }
        }
    }

    private SingleTileBrokenEffect _brokenEffect;

    private IEnumerator IEBrokenInstant(int offset) {
        if (sr.IsNullOrEmpty()) {
            yield break;
        }

        if (_brokenEffect == null) {
            SpawnBrokenVFX();
        }

        _brokenEffect.SetActive(true);
        _brokenEffect.MakeBrokeInstantEffect(offset);
    }

    private IEnumerator IEBrokenFallback(int offset) {
        if (sr.IsNullOrEmpty()) {
            yield break;
        }

        if (_brokenEffect == null) {
            SpawnBrokenVFX();
        }

        _brokenEffect.SetActive(true);
        _brokenEffect.MakeFalldownEffect(offset);
    }

    private IEnumerator IEBrokenTrain(float startPositionZ) {
        if (sr.IsNullOrEmpty()) {
            yield break;
        }

        if (_brokenEffect == null) {
            SpawnBrokenVFX();
        }

        _brokenEffect.SetActive(true);
        _brokenEffect.MakeTrainEffect(startPositionZ);
    }

    private void SpawnBrokenVFX() {
        Logger.EditorLog("broken spawned");
        _brokenEffect = Instantiate(Spawner.s.platformManager.GetPlatformBrokenEffectAsset(), container);
        _brokenEffect.SetRefers(sr[0], sq.transform);
    }

    protected virtual void UpdateTrySkin() {
        if (trySkinParent == null)
            return;

        if (isTileTrySkin) {
            trySkinParent.gameObject.SetActive(true);
            trySkinID = GameItems.instance.tryBallId;

            tfTrySkin = GameItems.instance.tfTrySkin;
            tfTrySkin.gameObject.SetActive(true);
            tfTrySkin.SetParent(trySkinParent);
            tfTrySkin.localPosition = Vector3.zero;
        } else {
            trySkinParent.gameObject.SetActive(false);
            trySkinID = -1;
        }
    }

    public void UpdateSize(float tileRatio) {
        if (topFire != null) {
            topFireScale = topFire.transform.localScale;
        }

        if (RemoteConfigBase.instance.MapSize > 1 && text != null) {
            text.transform.localScale *= RemoteConfigBase.instance.MapSize;
        }

        if (!isCustom && tileRatio < 1) {
            if (!Utils.IsNullOrEmpty(sr)) {
                Vector3 scale = sr[0].transform.localScale;
                scale.x *= tileRatio;
                scale.y *= tileRatio;
                sr[0].transform.localScale = scale;
                sr[1].transform.localScale = scale;
            }

            psHit1.startSize = 1.31f;
            if (psHit2 != null) {
                psHit2.startSize = 1.2f;
            }
        }
    }

    public void RecycleFakeTiles() {
        if (_fakeTiles.Count <= 0) {
            return;
        }

        while (_fakeTiles.Count > 0) {
            Platform fakeTile = _fakeTiles[^1];
            _fakeTiles.Remove(fakeTile);
            bool recycleFakeTile = Spawner.s.platformManager.RecycleFakeTile(fakeTile);
            if (!recycleFakeTile) {
                Spawner.s.platformManager.RecycleTile(fakeTile);
            }
        }
    }

    public void AddFakeTiles(Platform fakeTile) {
        CheckFakeTilePlinth(fakeTile);
        _fakeTiles.Add(fakeTile);
    }

    private void CheckFakeTilePlinth(Platform fakeTile) {
        if (!_isUsePlinth)
            return;

        fakeTile.plinthMeshRenderer.gameObject.SetActive(cloneUsePlinth);
    }

    public void Make(FakeTileStyle fakeTileStyle = FakeTileStyle.None) {
        isMade = true;

        _currentColor = Color.white;
        this._fakeTileStyle = fakeTileStyle;
        flyDistancePosition = Vector3.forward * 40;

        if (!_fakeTileStyle.IsClone()) {
            if (!isCustom && topFire != null) {
                topFire.transform.localScale = topFireScale;
            }

            foreach (var t in psHit3) {
                if (t != null) {
                    _gravityList.Add(
                        new KeyValuePair<ParticleSystem.MainModule, float>(t.main, t.main.gravityModifier.constant));
                }
            }

            foreach (var t in psHit4) {
                if (t != null) {
                    _gravityList.Add(
                        new KeyValuePair<ParticleSystem.MainModule, float>(t.main, t.main.gravityModifier.constant));
                }
            }

            foreach (var mesh in tileMeshRenderers) {
                if (mesh != null) {
                    mesh.sharedMaterial = Spawner.s.platformManager.GetPlatformMaterial();
                }
            }
        } else { // is clone
            if (!isCustom) {
                if (!Utils.IsNullOrEmpty(sr)) {
                    sr[0].sortingOrder = 8;
                    sr[1].sortingOrder = 8;
                }

                if (topFire != null) {
                    topFire.sortingOrder = 9;
                    topFire.transform.localScale = topFireScale * topFireCloneScale;
                }
            } else {
                if (!Utils.IsNullOrEmpty(sr)) {
                    sr[0].sortingOrder = 7;
                    sr[1].sortingOrder = 7;
                    sr[2].sortingOrder = 8;
                }

                if (topFire != null) {
                    topFire.sortingOrder = 8;
                }
            }

            foreach (var mesh in tileMeshRenderers) {
                mesh.sharedMaterial = Spawner.s.platformManager.GetFakePlatformMaterial();
            }

            var boxCollider = sq.GetComponent<BoxCollider>();
            boxCollider.size = new Vector3(0.7f, 40f, 1f);
            boxCollider.center = new Vector3(0, -20, 0);

            Destroy(text.gameObject);
            Destroy(psHit1.transform.parent.gameObject);
            Destroy(srStrongNoteFx.gameObject);
            gameObject.SetActive(false);
            MakeCloneTile(_fakeTileStyle);
        }

        // if (!Utils.IsNullOrEmpty(sr)) {
        //     _isGradientMaterial = sr[0].sharedMaterial.HasProperty(ShaderAttribute.ColorFrom);
        // }
    }

    public virtual void SetOrdering() {
        if (!_fakeTileStyle.IsClone() && !isCustom) {
            _currentOrder -= 3;
            LoopSortingOrder(3);
            if (!Utils.IsNullOrEmpty(sr)) {
                sr[0].sortingOrder = _currentOrder + 1;
                sr[1].sortingOrder = _currentOrder;
            }

            if (topFire != null) {
                topFire.sortingOrder = _currentOrder + 2;
            }

            if (srStrongNoteFx != null) {
                srStrongNoteFx.sortingOrder = _currentOrder + 3;
            }

            if (spritePerfect != null) {
                spritePerfect.sortingOrder = _currentOrder + 15;
            }
        }

        if (isCustom) {
            _currentOrder -= 3;
            LoopSortingOrder(3);

            //sr[0].sortingOrder =  currentOrder;
            if (!Utils.IsNullOrEmpty(sr)) {
                sr[0].sortingOrder = _isGradientMaterial ? 0 : _currentOrder;
                sr[1].sortingOrder = _currentOrder;
                sr[2].sortingOrder = _currentOrder + 1;
            }

            if (topFire != null) {
                topFire.sortingOrder = _currentOrder + 1;
            }

            if (srStrongNoteFx != null) {
                srStrongNoteFx.sortingOrder = _currentOrder + 2;
            }

            if (spritePerfect != null) {
                spritePerfect.sortingOrder = _currentOrder + 15;
            }
        }
    }

    protected void LoopSortingOrder(int step) {
        float distanceToPrev = platformData.positionZ - MapManager.instance.GetPlatformData(noteID - 1).positionZ;
        if (platformData.isInited && distanceToPrev >= SafeDistance) {
            _currentOrder = MaxSortingOrder;
        }
    }

    public virtual void SetEffectTexture(int skinIndex, bool isIgnoreStageColor = true) {
        if (isIgnoreStageColor && IsColorStage) {
            //2 stage COLORING và COLOR_CHOOSING không tự đổi màu
            return;
        }

        SkinSet skin = Spawner.s.GetSkinSet(skinIndex, isCustom);

        //Set Effect Texture for default tiles
        if (!updatedTextures || MapManager.MoodTile != PlatformType.NORMAL) {
            if (!isCustom && isHitImageType) {
                if (psHit1Renderer != null) {
                    psHit1Renderer.sharedMaterial.SetTexture(ShaderAttribute.MainTex, skin.psTexture);
                }

                if (psHit2Renderer != null) {
                    psHit2Renderer.sharedMaterial.SetTexture(ShaderAttribute.MainTex, skin.ps2Texture);
                }
            }

            updatedTextures = true;
        }

        //Set Effect Color for custom tiles
        if (isCustom || !isHitImageType) {
            skin.tileColor.a = 0.4f;
            if (isChangeColorHit1) {
                psHit1.startColor = skin.tileColorLighter;
            }

            if (psHit2 != null && isChangeColorHit2) {
                psHit2.startColor = skin.tileColor;
            }

            skin.tileColor.a = 1f;
        }
    }

    public void SetSkin(int index, bool isSwitchColor = false, bool isForceUpdateSr0 = false) {
        _skinSet = Spawner.s.GetSkinSet(index, isCustom, isMoodTile);

        if (IsColorStage) {
            //2 stage COLORING và COLOR_CHOOSING không tự đổi màu
            return;
        }

        if (!isCustom) {
            if (!isSwitchColor) {
                if (!Spawner.s.onTransition || isForceUpdateSr0) {
                    if (!Utils.IsNullOrEmpty(sr)) {
                        sr[0].sprite = _skinSet.blockSprite;
                    }
                }

                if (_isUsePlinth) {
                    plinthMeshRenderer.gameObject.SetActive(true);
                }

                foreach (var item in sr) {
                    item.enabled = true;
                }
            } else { // prepare switch color
                if (!Utils.IsNullOrEmpty(sr)) {
                    sr[1].sprite = _skinSet.blockSprite;
                    if (_isUsePlinth) {
                        sr[1].gameObject.SetActive(true);
                    }
                }
            }
        } else { // isCustom = true
            foreach (var item in sr) {
                item.enabled = true;
            }

            if (!isSwitchColor) {
                if (!ThemeManager.IsUseNewTile() && _isUsePlinth) {
                    plinthMeshRenderer.gameObject.SetActive(true);
                    if (!Utils.IsNullOrEmpty(sr)) {
                        sr[2].gameObject.SetActive(true);
                    }
                }
            }

            _currentColor = _skinSet.tileColor;
            SetTopColor(_currentColor);
        }

        if (!_fakeTileStyle.IsClone()) {
            srStrongNoteFx.gameObject.SetActive(platformData.isStrongNote && !hitted);
        }

        if (UseNewVFXController() && isSwitchColor == false) {
            vfxControllerV2.SetColor(index);
            vfxControllerV2.SetVFX(index, GetBlockSize(), _oldScale);
        }

        SetPlinthColor(_skinSet.tileColor);

        fireColor = _skinSet.fireColor;
        SetTopFireColor(fireColor);
    }

    protected virtual void SetTopFireColor(Color color) {
        if (topFire != null) {
            topFire.color = color;
        }
    }

    public void FinishedSwitchColor(int index) {
        if (IsColorStage) {
            //2 stage COLORING và COLOR_CHOOSING không tự đổi màu
            return;
        }

        indexSkin = index; //update index Skin cho tile ở dạng chuyển màu

        if (!isCustom) {
            if (!Utils.IsNullOrEmpty(sr)) {
                sr[0].sprite = sr[1].sprite;
                sr[1].gameObject.SetActive(false);
                _currentColor.a = GetTileAlpha();
                SetTopColor(_currentColor);
            }
        }

        _skinSet = Spawner.s.GetSkinSet(index, isCustom);
        //UpdateHit3Color(_skinSet);
    }

    public void SwitchColor(float pctTime, int currentIndex, SkinSet currentSkinSet, int nextIndex,
                            SkinSet nextSkinSet) {
        if (IsColorStage) {
            //2 stage COLORING và COLOR_CHOOSING không tự đổi màu
            return;
        }

        if (!isCustom) {
            float tileAlpha = GetTileAlpha();
            _currentColor.a = (1f - pctTime) * tileAlpha;
            SetTopColor(_currentColor, pctTime * tileAlpha);
        } else { //isCustom = true
            _currentColor = Color.Lerp(currentSkinSet.tileColor, nextSkinSet.tileColor, pctTime);
            _currentColor.a = GetTileAlpha();
            SetTopColor(_currentColor);
        }

        //Fake color:
        fireColor = Color.Lerp(currentSkinSet.fireColor, nextSkinSet.fireColor, pctTime);
        if (topFire != null) {
            fireColor.a = topFire.color.a;
            SetTopFireColor(IsFireColor() ? GetEndlessColor(nextSkinSet) : fireColor);
        }

        if (UseNewVFXController()) {
            vfxControllerV2.ChangeColor(currentIndex, nextIndex, pctTime);
            vfxControllerV2.ChangeVFX(currentIndex, nextIndex, pctTime, GetBlockSize(), _oldScale);
        }

        SetPlinthColor(Color.Lerp(currentSkinSet.tileColor, nextSkinSet.tileColor, pctTime));
    }

    private bool IsFireColor() {
        return !_fakeTileStyle.IsClone() && Ball.b.endlessModeCount > 0;
    }

    protected virtual void SetTopColor(Color targetColor, float secondaryAlpha = -1) {
        if (Utils.IsNullOrEmpty(sr))
            return;

        if (_isGradientMaterial) {
            sr[0].sharedMaterial.SetColor(ShaderAttribute.ColorFrom, Color.Lerp(_currentColor, Color.white, 0.2f));
            sr[0].sharedMaterial.SetColor(ShaderAttribute.ColorTo, targetColor);
        } else {
            var color = targetColor;
            color.a *= _globalAlpha;
            sr[0].color = color;
        }

        if (secondaryAlpha > 0) {
            _currentColor.a = secondaryAlpha;
        }

        var color2 = _currentColor;
        color2.a *= _globalAlpha;
        sr[1].color = color2;

        _targetColor1 = targetColor;
        _targetColor2 = _currentColor;
    }

    private Color GetEndlessColor(SkinSet skinSet) {
        //var newColor = Color.Lerp(fireColor, ColorHelper.black, 0.3f);
        var newColor = skinSet.fireColorDarker;
        if (topFire != null) {
            newColor.a = topFire.color.a;
            SetTopFireColor(newColor);
        }

        return newColor;
    }

    private float GetTileAlpha() {
        return Spawner.s.fadeOutTileDistance > 0 && containerLocalPosition.z >= Spawner.s.fadeOutTileDistance
            ? GetAlphaByPosition(1f)
            : 1f;
    }

    private float GetAlphaByPosition(float t) {
        return Mathf.Lerp(t, 0, (containerLocalPosition.z - Spawner.s.fadeOutTileDistance) / 4);
    }

    public void Moving(bool moving, int movingType = 0, bool fw = false, float totalTimeMove = 0) {
        sinkTime = 0;

        if (_fakeTileStyle.IsClone()) {
            moving = false;
        }

        this.isMoving = moving;

        if (!isMoving) {
            return;
        }

        this.forward = fw;
        _isOnlyMoveWhenPlay = totalTimeMove > 0;
        movingTime = RemoteConfigBase.instance.GetTile_MovingTime() / Ball.b.timeScale;
        distance = _isOnlyMoveWhenPlay ? _tileMaxPositionX : Spawner.s.distanceMinMax4[1];

        if (_isOnlyMoveWhenPlay) {
            float xEnd = transCache.GetPositionX();
            float valueCurve = xEnd / distance;
            float timeByValue = Configuration.instance.GetTimeByValue(valueCurve);
            float endCurveTime = 0;

            bool forwardEnd = forward;
            if (forwardEnd) { //nếu hướng di chuyển cuốn cùng là sang phải
                endCurveTime = timeByValue * movingTime;
            } else { //nếu hướng di chuyển cuốn cùng là sang trái
                endCurveTime = (1 - timeByValue) * movingTime;
            }

            float centerCurveTime = totalTimeMove - endCurveTime;
            int step = (int) Math.Floor(centerCurveTime / movingTime);
            float startCurveTime = centerCurveTime - step * movingTime;

            if (step % 2 == 0) { //nếu center step là chắn
                if (forwardEnd) { //vị trí cuối của start step là mép trái => bắt đầu di chuyển là hướng sang trái
                    forward = false;
                } else { //vị trí cuối của start step là mép phải => bắt đầu di chuyển là hướng sang phải
                    forward = true;
                }
            } else {
                if (forwardEnd) { //vị trí cuối của start step là mép phải => bắt đầu di chuyển là hướng sang phải
                    forward = true;
                } else { //vị trí cuối của start step là mép trái => bắt đầu di chuyển là hướng sang trái
                    forward = false;
                }
            }

            if (forward) { //hướng sang phải
                currentCurveTime = 1 - startCurveTime / movingTime;
            } else { //hướng sang trái
                currentCurveTime = startCurveTime / movingTime;
            }
        } else if (movingType == (int) NoteData.Moving.Normal) {
            currentCurveTime = Random.Range(0, 100f) / 100f;
        } else if (movingType == (int) NoteData.Moving.Snake) {
            currentCurveTime = 0;
        } else if (movingType == (int) NoteData.Moving.Sync) {
            if (Spawner.s.lastMovingObject != null) {
                currentCurveTime = Spawner.s.lastMovingObject.currentCurveTime; // cùng offset
                forward = Spawner.s.lastMovingObject.forward; // cùng hướng
            }
        } else if (movingType == (int) NoteData.Moving.ZicZac) {
            if (Spawner.s.lastMovingObject != null) {
                currentCurveTime = 1 - Spawner.s.lastMovingObject.currentCurveTime; // ngược offset
                forward = !Spawner.s.lastMovingObject.forward; // ngược hướng
            }
        }

        if (moving) {
            float newPositionX = curveMoving.Evaluate(currentCurveTime) * distance;
            transCache.SetPositionX(newPositionX);
        }
    }

    protected virtual void SinkEffect() {
        sinkTime = 1f;
        _originalPosition = transCache.position;
    }

    public IEnumerator Hide(Action onDone = null, Vector3 direction = default, float time = 0.3f) {
        float t = 0;
        GameObject gameObjectCached = gameObject;
        while (t < time) {
            if (gameObjectCached.activeSelf) {
                t += Time.deltaTime;

                if (direction != default) {
                    transCache.position += direction * (Time.deltaTime * 40);
                } else {
                    if (Spawner.s.themeId == ThemeManager.ThemeIdEmpty) {
                        transCache.position += Vector3.down * (Time.deltaTime * 30);
                    } else {
                        transCache.position += Vector3.back * (Time.deltaTime * 30);
                    }
                }

                yield return null;
            } else {
                break;
            }
        }

        if (gameObjectCached != null) {
            transCache.SetPositionY(0);
            gameObjectCached.SetActive(false);
        }

        onDone?.Invoke();
    }

    public void Hide(float delay, float time, float deltaZ) {
        float newPosZ = transCache.GetPositionZ() + deltaZ;
        if (_tweenMove != null && _tweenMove.IsActive()) {
            _tweenMove.Kill();
        }
        _tweenMove = transCache.DOMoveZ(newPosZ, time).OnComplete(HandleOnHideComplete);
        DOVirtual.DelayedCall(delay, () => { _tweenMove.Play(); });
    }

    private void HandleOnHideComplete() {
        if (gameObject != null) {
            transCache.SetPositionY(0);
            gameObject.SetActive(false);
        }
    }

    public void Show(float delay, float time, float deltaZ) {
        if (_tweenMove != null && _tweenMove.IsActive())
        {
            _tweenMove.Kill();
        }
        
        DOVirtual.DelayedCall(delay, () => {
            if(this == null || gameObject == null) return;
            
            float cachePosition = transCache.position.z;
            transCache.SetPositionZ(cachePosition + deltaZ);

            gameObject.SetActive(true);
            _tweenMove = transCache.DOMoveZ(cachePosition, time).OnComplete(OnCompletedMoving);
        });
    }

    private bool CanShowHitVFX() {
        switch (sectionType) {
            case IngameSectionType.HyperBoost:
            case IngameSectionType.ZicZac:
                return false;
        }

        return true;
    }

    public void Hit(int streakCount, bool ballSource = true) {
        hitted = true;
        bool canShowVFX = CanShowHitVFX();
        try {

            if (text.gameObject.activeSelf) {
                text.StartHide();
                if (ballSource) {
                    SoundManager.PlayCheckPoint();
                }
            }

            // clear copy
            if (_fakeTiles.Count != 0) {
                Spawner.s.platformManager.ClearClone(_fakeTiles);
            }

            SetActiveHitEffect(true);

            if (streakCount > 0) {
                Spawner.s.platformManager.CenterPerfectEffect(); // show center effect in all visible platform
                if (canShowVFX && RemoteConfigBase.instance.CenterAnimation_Enable) { //show vfx center
                    HitPerfectEffect();
                }
            } else {
                if (canShowVFX) {
                    Spawner.s.platformManager.DisableCenterEffect();
                }
            }

            if (RemoteConfigBase.instance.CenterAnimation_Enable) { // hide anim focus center
                FocusPerfectStatus(false);
                ActivePerfectAnim(false);
            }

            if (RemoteConfigBase.instance.Tile_PerfectIcon_Enable) {
                ActivePerfectAnim(false);
            }

            if (canShowVFX) {
                ShowTopFire(true);
                if (IsFireColor() && topFire != null) {
                    SkinSet skinSet = Spawner.s.GetSkinSet(indexSkin, isCustom);
                    SetTopFireColor(GetEndlessColor(skinSet));
                }
            }

            if (!platformData.isStrongNote && canShowVFX) {
                SinkEffect();
            } else {
                if (ballSource) {
                    Vibration.PlaySingleHaptic();
                }
            }

            if (ballSource) {
                if (platformData.isStrongNote && GameItems.instance.wednesdayController != null) {
                    GameItems.instance.wednesdayController.ControllMode();
                }

                if (GameItems.instance.edmController != null) {
                    if (platformData.isStrongNote) {
                        GameItems.instance.edmController.PlayStrongEffect();
                    }

                    GameItems.instance.edmController.HitNote(nextNoteDistance);
                }

                if (GameItems.instance.hiphopRetroController != null) {
                    if (platformData.isStrongNote) {
                        GameItems.instance.hiphopRetroController.PlayStrongEffect();
                    }
                }

                if (GameItems.instance.hiphopModernController != null) {
                    if (platformData.isStrongNote) {
                        GameItems.instance.hiphopModernController.PlayStrongEffect();
                    }

                    GameItems.instance.hiphopModernController.HitNote();
                }

                if (GameItems.instance.vfxController != null) {
                    if (platformData.isStrongNote) {
                        GameItems.instance.vfxController.PlayStrongNoteEffect();
                    }

                    if (streakCount > 0) {
                        GameItems.instance.vfxController.PlayPerfectEffect();
                    }
                }
            }

            srStrongNoteFx.gameObject.SetActive(false);

            if (_hatItemScript != null) {
                _hatItemScript.Eat();
                _hatItemScript = null;
            }

            if (tileAnimation != null) {
                tileAnimation.Play();
            }
        } catch (Exception e) {
            CustomException.Fire("[Hit]", e.Message + " => " + e.StackTrace);
        }

        if (ballSource && mirrorTile != null) {
            mirrorTile.Hit(streakCount, false);
            mirrorTile.PlayHitVFX(streakCount);
        }

        if (ballSource) {
            switch (elementType) {
                case NoteElementType.MoodChangeInstant:
                case NoteElementType.MoodChangeOrder:
                case NoteElementType.MoodChangeTrain:
                    if (!Spawner.s.platformManager.CheckConsecutiveSingleTiles(noteID)) {
                        elementType = NoteElementType.MoodChange;
                    }
                    break;
            }

            OnHit?.Invoke(this, streakCount);
        }
    }
    
    public void HitWithoutEffect() {
        hitted = true;
        try {
            // clear copy
            if (_fakeTiles.Count != 0) {
                Spawner.s.platformManager.ClearClone(_fakeTiles);
            }
        } catch (Exception e) {
            CustomException.Fire("[HitWithoutEffect]", e.Message);
        }

        if (mirrorTile != null) {
            mirrorTile.Hit(0, false);
            mirrorTile.PlayHitVFX(0);
        }
        
        OnReachPlatform?.Invoke(this, -1);
    }

    #region Vfxs

    public bool PlayHitVFX(int perfectCount) {
        //~~~~~~~~~~~~~~~~~~ Hit Effect ~~~~~~~~~~~~~~~~~~
        if (IsNoteHyperBoost() || IsNoteZicZac()) {
            //do nothing
            return false;
        }

        if (_specialItem != null) {
            //do nothing
            return false;
        } else if (UseNewVFXController()) {
            HitNewVfxController(perfectCount);
        } else if (isSlideTile) {
            Hit1Effect();
        } else if (Spawner.s.isMusicalizationType &&
                   platformData.currNoteObj.nodeID == NoteData.NoteIDLastNoteForHuman) {
            //dont show efx for last note
        } else if (RemoteConfigBase.instance.Musicalization_Intensity_IsEnable && platformData.isStrongNote) {
            Hit4Effect(); //TH-303 vfx for strong note
            Hit1Effect();
            return true;
        } else {
            //Musicalization
            switch (timbre) {
                case NoteData.Timbre.Rhythmic:
                    Hit3Effect();
                    break;

                case NoteData.Timbre.Vocal:
                case NoteData.Timbre.Melodic:
                    //Musicalization
                    Hit1Effect();
                    Hit2Effect();
                    break;

                default: {
                    if (perfectCount < Spawner.GetPerfectMax(2)) {
                        Hit1Effect();
                        Hit2Effect();
                    } else {
                        Hit3Effect();
                    }

                    break;
                }
            }
        }

        return false;
    }

    private void Hit1Effect() {
        psHit1.Play();
    }

    protected void Hit2Effect() {
        if (psHit2 != null) {
            psHit2.Play();
        }

        if (Spawner.s.themeId == ThemeManager.ThemeIdHalloween && psMagicEffect != null) {
            psMagicEffect.gameObject.SetActive(true);
            psMagicEffect.startColor = Spawner.s.GetCurrentSkin().tileColorLighter;
            psMagicEffect.transform.position =
                Util.SetPositionY(Ball.b.transCache.position, psSnow.transform.position.y);
            psMagicEffect.Play();
        }
    }

    /// <summary>
    /// Call when reaching perfect level 2
    /// </summary>
    private void Hit3Effect() {
        UpdateHit3Color(_skinSet);
        UpdateGravity();
        if (psHit3.Length != 0 && psHit3[0] != null) {
            psHit3[0].Play();
        }
    }

    /// <summary>
    /// Method create effect hit for musicalization strong note
    /// </summary>
    private void Hit4Effect() {
        UpdateHit4Color(_skinSet);

        // Hide platform
        foreach (var item in sr) {
            if (!isTileTrySkin) {
                item.enabled = false;
            }
        }

        ShowTopFire(false);

        if (_isUsePlinth) {
            plinthMeshRenderer.gameObject.SetActive(false);
        }

        HidePerfectAnim();

        //Change gravity
        UpdateGravity();

        //Play effect
        if (psHit4.Length != 0 && psHit4[0] != null) {
            psHit4[0].Play();
            if (platformType == PlatformType.MOOD_FULL) {
                psHit4[0].transform.SetPositionX(Ball.b.transform.position.x);
            }
        }
    }

    public void Hit4OnlyEffect() {
        if (psHit4.Length != 0 && psHit4[0] != null) {
            psHit4[0].transform.parent.gameObject.SetActive(true);
        }

        if (psHit3.Length != 0 && psHit3[0] != null) {
            psHit3[0].Play();
        }
    }

    private void HitPerfectEffect() {
        if (ThemeManager.IsUseNewTile() || psPerfectLightPillar == null) {
            return;
        }

        UpdateHitPerfectColor(_skinSet);
        psPerfectLightPillar.Play();
    }

    public bool UseNewVFXController() {
        return vfxControllerV2 != null;
    }

    public void HitNewVfxController(int perfectCount) {
        if (vfxControllerV2 == null)
            return;

        if (platformData.isStrongNote) {
            foreach (var eff in vfxControllerV2.strongNoteVfx.platformParticle) {
                TryPlayEffect(eff, vfxControllerV2.strongNoteVfx);
            }
        } else if (perfectCount > 0) {
            foreach (var eff in vfxControllerV2.perfectVfx.platformParticle) {
                TryPlayEffect(eff, vfxControllerV2.perfectVfx);
            }
        } else {
            foreach (var eff in vfxControllerV2.greatVfx.platformParticle) {
                TryPlayEffect(eff, vfxControllerV2.greatVfx);
            }
        }
    }

    private void TryPlayEffect(PlatformParticleSystem eff,
                               PlatformParticleSystemColorMoodChange platformColorMoodChange) {
        if (eff.particleSystem == null)
            return;

        if (Spawner.s.currentIndexSkin >= 0 && Spawner.s.currentIndexSkin < platformColorMoodChange.colors.Count) {
            foreach (var effect in eff.colorParticleSystems) {
                ParticleSystem.MainModule psModule = effect.main;
                psModule.startColor = platformColorMoodChange.colors[Spawner.s.currentIndexSkin];
            }
        }

        if (!_isScaledVFX) {
            foreach (var effect in eff.scalingParticleSystems) {
                if (effect == null)
                    continue;

                if (_oldScale.x == 0 || _oldScale.z == 0) {
                    continue;
                }

                var multiplierX = GetBlockSize().x / _oldScale.x;
                var multiplierZ = GetBlockSize().z / _oldScale.z;

                var temp = effect.transform.localScale;
                temp.x *= multiplierX;
                temp.z *= multiplierZ;
                effect.transform.localScale = temp;
            }

            _isScaledVFX = true;
        }

        eff.particleSystem.Play();
    }

    private void UpdateHit3Color(SkinSet skinSet) {
        if (_fakeTileStyle.IsClone() || !isChangeColorHit3) {
            return;
        }

        foreach (ParticleSystem particle in psHit3) {
            ParticleSystem.ColorOverLifetimeModule colorModule = particle.colorOverLifetime;
            ParticleSystem.MinMaxGradient mmGradient = colorModule.color;
            GradientColorKey[] colorKeys = mmGradient.gradient.colorKeys;
            colorKeys[^2].color = skinSet.tileColorLighter;
            colorKeys[^1].color = skinSet.fireColorDarker;
            mmGradient.gradient.colorKeys = colorKeys;
            colorModule.color = mmGradient;
        }
    }

    private void UpdateHit4Color(SkinSet skinSet) {
        if (_fakeTileStyle.IsClone() || !isChangeColorHit4) {
            return;
        }

        for (int i = 0; i < psHit4.Length; i++) {
            ParticleSystem.MainModule colorModule = psHit4[i].main;
            colorModule.startColor = i == 0 ? skinSet.tileColorLighter : skinSet.tileColorVeryLight;
        }
    }

    private void UpdateHitPerfectColor(SkinSet skinSet) {
        if (_fakeTileStyle.IsClone()) {
            return;
        }

        Color skinSetColor = skinSet.tileColorVeryLight;
        foreach (ParticleSystem particle in psPerfectLightPillarChilds) {
            ParticleSystem.MainModule colorModule = particle.main;
            skinSetColor.a = colorModule.startColor.color.a;
            colorModule.startColor = skinSetColor;
        }
    }

    #endregion

    public void FocusPerfectStatus(bool isEnable) {
        if (ThemeManager.IsUseNewTile()) {
            return;
        }

        focusPerfect.gameObject.SetActive(isEnable);
    }

    /// <summary>
    /// Adjust particle system gravity modifier (hit3+hit4) when Physics.gravity is changed
    /// </summary>
    private void UpdateGravity() {
        for (int i = 0; i < _gravityList.Count; i++) {
            if (Math.Abs(_gravityList[i].Value) > 0) {
                brustGravityModifier.constant = _gravityList[i].Value * GravityEarth / Mathf.Abs(Physics.gravity.y);
                var module = _gravityList[i].Key;
                module.gravityModifier = brustGravityModifier;
            }
        }
    }

    /// <summary>
    /// GetHeightBlock
    /// </summary>
    /// <param name="blockScaleY"></param>
    /// <returns></returns>
    public float GetHeightBlock(float blockScaleY) {
        BoxCollider boxCollider = sq.GetComponent<BoxCollider>();
        return boxCollider.size.y * blockScaleY;
    }

    /// <summary>
    /// GetFakeTiles
    /// </summary>
    /// <returns></returns>
    public List<Platform> GetFakeTiles() {
        return _fakeTiles;
    }

    /// <summary>
    /// IsShow
    /// </summary>
    /// <returns></returns>
    public bool IsShow() {
        return gameObject.activeInHierarchy;
    }

    public void SetBlockSize(Vector3 scale) {
        _originalSqrScale = sqTransCache.localScale;

        var boxCollider = GetComponent<BoxCollider>();
        Vector3 oldSize = boxCollider.size;

        sqTransCache.localScale = scale;
        Vector3 newSize = oldSize;

        if (container.localRotation == Quaternion.identity) {
            //normal block and full mood block
            newSize.x = oldSize.x * scale.x / _originalSqrScale.x;
            newSize.z = oldSize.z * scale.z / _originalSqrScale.z;
        } else {
            //long tile
            newSize.x = oldSize.x * scale.z / _originalSqrScale.z;
            newSize.z = oldSize.z * scale.x / _originalSqrScale.x;
        }

        boxCollider.size = newSize;
    }

    public Vector3 GetBlockSize() {
        return sqTransCache.localScale;
    }

    public virtual void AdjustBoxColliderLongType() {
        Vector3 size = CacheCollider.size;
        var localScale = sqTransCache.localScale;
        size.z = 12 * localScale.x / 4.3f;
        size.x = localScale.z;
        CacheCollider.size = size;
        var boxColliderCenter = CacheCollider.center;
        boxColliderCenter.z = size.z / 2f;
        CacheCollider.center = boxColliderCenter;
    }

    public virtual void UpdatePivot() {
        if (pivot != null) {
            pivot.transform.localPosition = new Vector3(-CacheCollider.size.z / 2f, 0, 0);
        }
    }
    public virtual void UpdatePivot(Vector3 position) {
        if (pivot != null) {
            pivot.transform.localPosition = position;
        }
    }

    public void ShowTileStarEffect(bool isShow) {
        tileStarEffect.gameObject.SetActive(isShow);
    }

    public void SetSkinImmediately(int index) {
        indexSkin = index; //update index skin cho tile có skin cố định
        _skinSet = Spawner.s.GetSkinSet(indexSkin, isCustom);

        if (!isCustom) {
            if (!Utils.IsNullOrEmpty(sr)) {
                sr[0].sprite = _skinSet.blockSprite;
                sr[0].enabled = true;
                sr[1].gameObject.SetActive(false);
            }
        } else if (isCustom) {
            foreach (var item in sr) {
                item.enabled = true;
            }

            if (!Utils.IsNullOrEmpty(sr)) {
                SpriteRenderer sprBg = sr[0];
                SpriteRenderer sprGlow = sr[1];
                SpriteRenderer sprStroke = sr[2];

                sprBg.enabled = true;
                sprGlow.enabled = true;
                sprStroke.gameObject.SetActive(true);

                _currentColor = _skinSet.tileColor;
                sprBg.color = _currentColor;
                sprGlow.color = _currentColor;
            }
        }

        if (!_fakeTileStyle.IsClone()) {
            srStrongNoteFx.gameObject.SetActive(platformData.isStrongNote);
        }

        if (_isUsePlinth) {
            plinthMeshRenderer.gameObject.SetActive(true);
        }

        if (UseNewVFXController()) {
            vfxControllerV2.SetColor(index);
            vfxControllerV2.SetVFX(index, GetBlockSize(), _oldScale);
        }

        SetPlinthColor(_skinSet.tileColor);

        fireColor = _skinSet.fireColor;
        SetTopFireColor(fireColor);

        //UpdateHit3Color(_skinSet);
        //UpdateHit4Color(_skinSet);
        //UpdateHitLightPillarColor(_skinSet);

        SetEffectTexture(indexSkin, false);
    }

    public virtual void ShowTopFire(bool isShow) {
        if (topFire != null) {
            topFire.gameObject.SetActive(isShow);
        }
    }

    public void SetActiveEfxFocus(bool isShow) {
        //if (objEfxFocus != null) {
        //    objEfxFocus.SetActive(isShow); //Temporary disable by hungtx
        //}
    }

    public void SetActiveHitEffect(bool isShow) {
        psHit1.transform.parent.gameObject.SetActive(isShow);
    }

    public void ShowPerfectAnim(bool hasAnimation = true) {
        if (platformData.isStrongNote)
            return;
        if (IsNoteHyperBoost() || IsNoteZicZac())
            return;

        ActivePerfectAnim(true);
        bool isAnim = hasAnimation && !RemoteConfigBase.instance.CenterAnimation_Enable;
        FadePerfectAnim(isAnim);

        if (elementType == NoteElementType.FakeTransform || elementType == NoteElementType.FakeThunder) {
            foreach (var fakeTile in _fakeTiles) {
                if (fakeTile != null) {
                    fakeTile.ShowPerfectAnim();
                }
            }
        }
    }

    public void HidePerfectAnim(bool isFistPlatform = false) {
        if (!RemoteConfigBase.instance.CenterAnimation_Enable || isFistPlatform) {
            SetAlphaPerfectSprite(0);
            ActivePerfectAnim(false);
        }
    }

    protected virtual void SetAlphaPerfectSprite(float alpha) {
        if (spritePerfect) {
            spritePerfect.SetAlpha(alpha * _globalAlpha);
        }
    }

    protected virtual void SetPerfectSize() {
        if (!RemoteConfigBase.instance.Tile_PerfectIcon_Enable) {
            return; // config is off
        }

        if (platformData.isStrongNote || perfectAnim == null) {
            return;
        }

        perfectAnim.transform.localScale = RemoteConfigBase.instance.Tile_PerfectIcon_Size;
    }

    protected virtual void SetPlinthColor(Color c) {
        if (_isUsePlinth) {
            c.a = _globalAlpha;
            if (!RemoteConfigBase.instance.Musicalization_MoodChange_FullTileSameColor && isMoodTile) {
                _plinthMaterialPropertyBlock ??= new MaterialPropertyBlock();
                _plinthMaterialPropertyBlock.SetColor(_color, c);
                plinthMeshRenderer.SetPropertyBlock(_plinthMaterialPropertyBlock);   
            } else {
                plinthMeshRenderer.sharedMaterial.color = c;    
            }
        }
    }

    public float GetPositionZ() {
        return transCache.GetPositionZ();
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    /// <summary>
    /// Dùng khi force move follow stage
    /// Dùng để force trigger trong trường hợp bóng bay quá nhanh -> xuyên thủng block mà k bật physic trigger
    /// </summary>
    public void ForceTriggerStay() {
        if (!CanInteractWithBall)
            return; // không đủ điều kiện để va chạm với ball

        TriggerWithBall();
    }

    /// <summary>
    /// Hàm được gọi khi va chạm với ball
    /// </summary>
    public void TriggerWithBall(bool forcePerfect = false) {
        if (!_fakeTileStyle.IsClone()) {
            Spawner.s.platformManager.CheckSpawner();
            Ball.b.ColliderWithBlock(this, forcePerfect);
			if (isMoodTile) {
				SoundManager.PlayGameplayMoodChange(1);
			} else if (platformData.isStrongNote) {
				SoundManager.PlayGameplayStrongNote(1);
			}
		} else {
            Ball.b.ColliderWithFakeBlock(noteID, gameObject);
            if (_fakeTileStyle == FakeTileStyle.Fake_Thunder) {
                GameItems.instance.PlayVFXThunderFakeTile(transCache.position);
            }
        }
    }

    public void HideTextInTile() {
        if (text != null) {
            text.SetActive(false);
        }
    }

    public Transform GetContainer() {
        return container;
    }

    public void SetHatItemScript(HatItemScript hatItemScript) {
        _hatItemScript = hatItemScript;
    }

    public void ResetHatItem() {
        if (_hatItemScript) {
            if (_hatItemScript.IsActive()) {
                _hatItemScript.Deactive();
            }

            _hatItemScript = null;
        }
    }

    public virtual void FadePerfectAnim(bool isFadeIn) {
        if (perfectAnim) {
            if (isFadeIn) {
                perfectAnim.Play(FadeInOut, -1, 0f);
            } else {
                perfectAnim.Play(AlphaOne, -1, 0f);
                SetAlphaPerfectSprite(1);
            }
        }
    }

    protected virtual void DestroyPerfectAnim() {
        if (perfectAnim) {
            Destroy(perfectAnim.gameObject);
        }
    }

    public virtual void ActivePerfectAnim(bool isActive) {
        if (perfectAnim) {
            perfectAnim.gameObject.SetActive(isActive);
        }
    }

    public void ForceUpdateFirstFrame() {
        float positionZOfCam = FollowInstance.transformCamera.position.z;
        float scaleZ = Mathf.Lerp(1f, 2f, (transCache.position.z - 10 - positionZOfCam) / 45);
        if (!isSlideTile) {
            transCache.SetScaleZ(scaleZ);
        }

        if (platformType != PlatformType.LONG_TILE && platformType != PlatformType.BREAKABLE_LONG_TILE) {
            container.SetLocalZ(Mathf.Lerp(0, 5, (_newPosition.z - 10 - positionZOfCam) / 45));
        }
    }

    private GameObject _idleBouncingFX;

    public void ChangeIdleBouncing(bool isShow) {
        if (isShow && _idleBouncingFX == null) {
            GameObject platform = Resources.Load<GameObject>(ResourcesPath.Platforms_Idle_bouncing_fx);
            _idleBouncingFX = Instantiate(platform, transform);
        }

        if (_idleBouncingFX != null) {
            _idleBouncingFX.SetActive(isShow);
        }
    }

    public void SetPosition(Vector3 position) {
        transCache.position = position;
    }

    public virtual void SetScaleAndPosition(Vector3 scale, Vector3 position) {
        transCache.localScale = scale;
        transCache.position = position;
    }

    public virtual void SetText(string str) {
        this.text.SetText(str);
    }

    public virtual void OnSlide(bool slide, Vector3 position = new Vector3()) {
        this.isSliding = slide;
        OnChangeSlideState?.Invoke(slide);
    }

    public virtual void Slide(float timeSlide, Vector3 position) { }

    public virtual Vector3 GetLandingPosition() {
        return transCache == null ? Vector3.zero : transCache.position;
    }

    public virtual Vector3 GetTakeOffPosition() {
        return transCache.position;
    }

    public virtual void OnHitchhike(bool isActive) { }

    #region Orbs

    protected List<Vector3>    listOrbPositions;
    protected List<RhythmNote> listOrbs;

    protected virtual void CreateOrbs() {
        if (listOrbPositions == null)
            return;
        if (listOrbPositions.Count == 0)
            return;

        Spawner.s.CreateOrbs(this, listOrbPositions);
    }

    public void AddOrb(RhythmNote orbs) {
        listOrbs.Add(orbs);
    }

    protected virtual void RecycleOrbs() {
        foreach (var rhythm in listOrbs) {
            if (rhythm != null) {
                rhythm.MakeRecycle();
            }
        }

        listOrbs.Clear();
    }

    #endregion

    public virtual bool IsPerfectLanding(Vector3 ballPosition, float perfectSize) {
        return Mathf.Abs(ballPosition.x - GetLandingPosition().x) <= perfectSize;
    }

    public virtual Vector3 GetAutoPosition(float slideTime) {
        return transCache.position;
    }

    public virtual Vector3 GetAutoPosition(Vector3 position) {
        return transCache.position;
    }

    private void DisableOnJumpToHuman() {
        if (noteID != NotesManager.instance.noteCount - 1) {
            Spawner.s.platformManager.RecycleTile(this);
        }
    }
    
    public virtual void CompleteSpawn() {
        SetElementType(this.elementType);
    }

    #region Single Hidden

    private float     _globalAlpha = 1f;
    private bool      _isChangingGlobalAplpha;
    private Coroutine _ieFadeControl;
    private Color     _targetColor1;
    private Color     _targetColor2;

    private bool  _isFadeOut;
    private float _countTimeFade;

    private IEnumerator IEFade(float targetFadeValue, float time = 0.1f) {
        if (_ieFadeControl != null) {
            StopCoroutine(_ieFadeControl);
        }

        float startValue = _globalAlpha;
        _isChangingGlobalAplpha = true;
        _countTimeFade = 0f;
        while (_countTimeFade < time) {
            _countTimeFade += Time.deltaTime;
            SetFadeValue(Mathf.Lerp(startValue, targetFadeValue, _countTimeFade / time));
            yield return null;
        }

        SetFadeValue(targetFadeValue);
        _isChangingGlobalAplpha = false;
    }

    public virtual void SetFadeValue(float value) {
        if (Math.Abs(_globalAlpha - value) < Mathf.Epsilon) {
            return;
        }

        _globalAlpha = value;
        bool activeVFX = Math.Abs(value - 1f) < Mathf.Epsilon;

        if (!Utils.IsNullOrEmpty(sr)) {
            if (sr.Length > 0 && sr[0] != null) {
                var color = sr[0].color;
                color.a = _targetColor1.a * value;
                sr[0].color = color;
            }

            if (sr.Length > 1 && sr[1] != null) {
                var color = sr[1].color;
                color.a = _targetColor2.a * value;
                sr[1].color = color;
            }

            if (sr.Length > 2 && sr[2] != null) { // for custom tile
                var color = sr[2].color;
                color.a = value;
                sr[2].color = color;
            }
        }

        if (spritePerfect != null) {
            perfectAnim.enabled = activeVFX;
            var color = spritePerfect.color;
            color.a = value;
            spritePerfect.color = color;
        }

        if (_isUsePlinth) {
            var color = plinthMeshRenderer.material.color;
            var material = plinthMeshRenderer.material;
            color = material.color;
            color.a = value;
            material.color = color;
        }

        if (topFire) {
            var color = topFire.color;
            color.a = value;
            topFire.color = color;
        }

        if (srStrongNoteFx) {
            var color = srStrongNoteFx.color;
            color.a = value;
            srStrongNoteFx.color = color;

            if (!_isFindAnimStrongNote) {
                _isFindAnimStrongNote = true;
                srStrongNoteFx.TryGetComponent(out _animStrongNote);
            }

            if (_animStrongNote != null) {
                _animStrongNote.Play(activeVFX ? "TileTwist" : "TileTwist_NoneAlpha");
            }
        }

        for (int i = 0; i < listObjectsOffWhenFade.Length; i++) {
            if (listObjectsOffWhenFade[i] == null)
                continue;

            listObjectsOffWhenFade[i].SetActive(activeVFX);
        }
    }

    private void ResetFade() {
        if (_ieFadeControl != null) {
            StopCoroutine(_ieFadeControl);
        }

        SetFadeValue(1f);
    }

    #endregion

    public void SetElementType(NoteElementType newElementType, bool isForce = true) {
        if (this.elementType == newElementType) {
            if (!isForce) {
                return;
            }
        } else {
            ResetElementType();
        }

        this.elementType = newElementType;
        switch (newElementType) {
            case NoteElementType.FadeOut:
                _tileBehaviour = new TileFadeOut();
                _tileBehaviour.OnSpawn(this);
                break;

            case NoteElementType.FadeInOut:
                _tileBehaviour = new TileFadeInOut();
                _tileBehaviour.OnSpawn(this);
                break;

            case NoteElementType.FakeConveyor:
                Vector3 targetPosition = platformData.positionPlatform;
                Vector3 startPosition = Spawner.s.CalculateStartConveyorPosition(platformData, targetPosition);
                transCache.position = startPosition;
                Spawner.s.ProcessConveyorFakeTile(this, platformData, startPosition, flyTime);
                CreateConveyorRunner();
                break;

            case NoteElementType.FakeThunder:
                transCache.position = platformData.positionPlatform;
                var fakePlatform = Spawner.s.ProcessCustomFakeTile(this, FakeTileStyle.Fake_Thunder, platformData,
                    platformData.positionPlatform, flyTime);
                fakePlatform.ProcessFakeTile();
                break;

            case NoteElementType.FakeTile:
                if (platformData.stage != Stage.FAKE_TILE) {
                    transCache.position = platformData.positionPlatform;
                    if (Spawner.s.isPitch) {
                        if (platformData.fakeTile == NoteData.FakeTile.NONE) {
                            platformData.fakeTile = NotesManager.instance.GetRandomFake(platformData.currNoteObj.pitch);
                        }

                        Spawner.s.ProcessTypeFakeForStageFakeTile(this, platformData, platformData.positionPlatform,
                            flyTime);
                    } else {
                        Spawner.s.ProcessCustomFakeTile(this, FakeTileStyle.Fake_Normal, platformData,
                            platformData.positionPlatform, flyTime);
                    }
                }

                break;

            case NoteElementType.MovingTile:
                if (platformData.stage != Stage.MOVING_TILE) {
                    Spawner.s.ProcessStageNameMovingTile(this, true, false, platformData);
                }

                break;

            case NoteElementType.Teleport:
                transCache.position = platformData.positionPlatform;
                CreateTeleportTile();
                break;

            case NoteElementType.MovingCircle:
                if (platformData.beforeNoteObj.elementType == NoteElementType.MovingCircle) {
                    return;
                }

                DisableFlyEffect();
                CreateCircularMovingTile();
                break;

            case NoteElementType.FakeTransform:
                transCache.position = platformData.positionPlatform;
                var fakeTile = Spawner.s.ProcessCustomFakeTile(this, FakeTileStyle.FakeTransform, platformData,
                    platformData.positionPlatform, flyTime);
                break;
        }
    }

    private void ProcessFakeTile() {
        if (_fakeThunderTile) {
            _fakeThunderTile.SetData(noteID);
        }
    }

    public void DisableFlyEffect() {
        flyTime = 0;
        transCache.position = flyEndPosition;
    }

    public void ResetElementType() {
        switch (this.elementType) {
            case NoteElementType.FadeOut:
            case NoteElementType.FadeInOut:
                ResetFade();
                _tileBehaviour = null;
                break;

            case NoteElementType.FakeConveyor:
                ResetFade();
                if (_tileConveyorRunner) {
                    _tileConveyorRunner.Stop();
                }

                RecycleFakeTiles();
                transCache.position = platformData.positionPlatform;
                break;

            case NoteElementType.FakeThunder:
                if (_fakeThunderTile) {
                    _fakeThunderTile.Stop();
                }

                RecycleFakeTiles();
                break;

            case NoteElementType.MovingCircle:
                transCache.position = platformData.positionPlatform;
                if (_circularMovingTile) {
                    Destroy(_circularMovingTile);
                }

                break;

            case NoteElementType.FakeTransform:
                if (_fakeTransform) {
                    _fakeTransform.Stop();
                }

                RecycleFakeTiles();
                break;
        }

        elementType = NoteElementType.None;
    }

    public FakeTileStyle GetFakeStyleType() {
        return _fakeTileStyle;
    }

    private void ResetFakeTileVFX() {
        switch (_fakeTileStyle) {
            case FakeTileStyle.Fake_Conveyor:
                if (_tileConveyorRunner) {
                    _tileConveyorRunner.Stop();
                }

                ResetFade();
                break;

            case FakeTileStyle.Fake_Thunder:
                if (_fakeThunderTile) {
                    _fakeThunderTile.Stop();
                }

                ResetFade();
                break;

            case FakeTileStyle.FakeTransform:
                if (_fakeTransform) {
                    _fakeTransform.Stop();
                }

                ResetFade();
                break;
        }
    }

    public void MakeCloneTile(FakeTileStyle style) {
        if (this._fakeTileStyle != style) {
            ResetFakeTileVFX();
        }

        this._fakeTileStyle = style;

        switch (_fakeTileStyle) {
            case FakeTileStyle.Fake_Normal:
                ShowTopFire(true);
                break;

            case FakeTileStyle.Fake_Conveyor:
                ShowTopFire(true);
                CreateConveyorRunner();
                break;

            case FakeTileStyle.Fake_Thunder:
                ShowTopFire(false);
                CreateThunderTile();
                break;

            case FakeTileStyle.FakeTransform:
                ShowTopFire(false);
                CreateIndefiniteTile();
                break;

            default:
                ShowTopFire(false);
                break;
        }

        ActivePerfectAnim(false);
    }

    private FakeThunderTile _fakeThunderTile;

    private void CreateThunderTile() {
        if (_fakeThunderTile == null) {
            if (!container.TryGetComponent(out _fakeThunderTile)) {
                _fakeThunderTile = container.gameObject.AddComponent<FakeThunderTile>();
            }
        }
    }

    private TileConveyorRunner _tileConveyorRunner;

    private void CreateConveyorRunner() {
        if (_tileConveyorRunner == null) {
            if (!container.TryGetComponent(out _tileConveyorRunner)) {
                _tileConveyorRunner = container.gameObject.AddComponent<TileConveyorRunner>();
            }
        }

        _tileConveyorRunner.Run(this);
    }

    private TeleportMovingTile _teleportMovingTile;

    private void CreateTeleportTile() {
        if (_teleportMovingTile == null) {
            if (!container.TryGetComponent(out _teleportMovingTile)) {
                _teleportMovingTile = container.gameObject.AddComponent<TeleportMovingTile>();
            }
        }

        _teleportMovingTile.Init(this);
    }

    private CircularMovingTile _circularMovingTile;

    private void CreateCircularMovingTile() {
        if (_circularMovingTile == null) {
            _circularMovingTile = RemoteConfigBase.instance.NewElements_MovingCircle_UseMidiPos
                ? gameObject.AddComponent<CircularMovingTile>()
                : gameObject.AddComponent<CircularMovingTileV2>();
            var data = MapManager.instance.GetPlatformData(noteID + 1);
            var posX = Spawner.s.GetPositionXOfPlatform(noteID + 1, ref data, transCache.position.x);
            var posZ = platformData.positionZ + platformData.nextNoteObj.distance * Ball.b.GetBalLSpeed();
            var nextPos = new Vector3(posX, transCache.position.y, posZ);
            _circularMovingTile.Init(this, nextPos);
        }
    }

    private void TryRecycleTeleportingComponent() {
        if (_teleportMovingTile != null) {
            Destroy(_teleportMovingTile);
        }
    }

    #region Special Section

    [SerializeField, ReadOnly] internal bool     isMirrorTile;
    [SerializeField, ReadOnly] private  Platform mirrorTile;

    public void SetMirror(bool isMirror) {
        isMirrorTile = isMirror;
    }

    public void SetMirror(Platform mirror) {
        mirrorTile = mirror;
    }

    public virtual void ProcessMirrorTile() {
        //BoxCollider.enabled = false;
    }

    protected virtual void RecycleMirrorTile() {
        isMirrorTile = false;
        //BoxCollider.enabled = true;
    }

    public void CreateMirrorBox() {
        _specialItem = GameItems.instance.CreateMirrorBox(this.container);
    }

    public void CreatePointHyperBoost() {
        _specialItem = GameItems.instance.CreatePointHyperBoost(this.container);
    }

    private void ResetHyperBoost() {
        if (elementType != NoteElementType.SpecialHyperBoost)
            return;

        elementType = NoteElementType.None;
        SetFadeValue(1f);
        foreach (var diamond in _attachDiamonds) {
            if (diamond) {
                diamond.transform.localPosition = Vector3.up * 0.5f;
            }
        }
    }

    public virtual void SetHyperBoost() {
        elementType = NoteElementType.SpecialHyperBoost;
        SetFadeValue(GameController.instance.sectionType == IngameSectionType.HyperBoost ? 0f : 1f);
        platformData.isStrongNote = false;
        srStrongNoteFx.gameObject.SetActive(false);

        ActivePerfectAnim(false);

        if (text) {
            text.SetActive(false);
        }

        foreach (Platform fakeTile in _fakeTiles) {
            fakeTile.SetActive(false);
        }
    }

    public bool IsNoteHyperBoost() {
        return elementType == NoteElementType.SpecialHyperBoost;
    }

    private bool isStartNoteZicZac;
    private bool isLastNoteZicZac;

    public void AddPointSpecialSectionZicZac(bool isStartNote, bool isLastNote) {
        sectionType = IngameSectionType.ZicZac;
        SetFadeValue(0f);
        isSlideTile = false;
        isHitchhikeTile = false;

        isStartNoteZicZac = isStartNote;
        isLastNoteZicZac = isLastNote;
    }

    public bool IsNoteZicZac() {
        return sectionType == IngameSectionType.ZicZac;
    }

    public bool IsLastNoteZicZac() {
        return sectionType == IngameSectionType.ZicZac && isLastNoteZicZac;
    }

    public bool IsStartNoteZicZac() {
        return sectionType == IngameSectionType.ZicZac && isStartNoteZicZac;
    }

    private void ResetZicZac() {
        if (sectionType != IngameSectionType.ZicZac)
            return;

        sectionType = IngameSectionType.Normal;
        SetFadeValue(1f);
        isLastNoteZicZac = false;
        isStartNoteZicZac = false;
    }

    private void GameControllerOnOnChangeSectionType(IngameSectionType specialType) {
        switch (specialType) {
            case IngameSectionType.HyperBoost:
                StartCoroutine(IEFade(0, 0.2f));
                break;
        }
    }

    #endregion

    #region Try Skin

    private void ResetTrySkin() {
        if (isTileTrySkin) {
            if (_trySkinObject == null) {
                _trySkinObject = Instantiate(Resources.Load<TrySkin>("TrySkin"), this.container.transform);
            }

            _trySkinObject.Init();
        } else {
            ClearTrySkin();
        }
    }

    public void ClearTrySkin() {
        if (_trySkinObject) {
            _trySkinObject.Clear();
        }
    }

    public Transform GetTransformTrySkin() {
        return _trySkinObject ? _trySkinObject.GetParentTransform() : null;
    }

    #endregion

    private FakeIndefiniteTile _fakeTransform;

    private void CreateIndefiniteTile() {
        if (_fakeTransform == null) {
            if (!container.TryGetComponent(out _fakeTransform)) {
                _fakeTransform = container.gameObject.AddComponent<FakeIndefiniteTile>();
            }
        }

        _fakeTransform.PlayVFX(noteID, this);
    }

    public Vector3 FixPositionOnHit(Vector3 position, float _ballPositionInit) {
        if (sectionType == IngameSectionType.HyperBoost || sectionType == IngameSectionType.ZicZac)
            return position;

        float distance = transCache.position.z - position.z;
        if (isSlideTile || isHitchhikeTile)
            distance = 0;
        position = Util.SetPositionYZ(position, _ballPositionInit - 0.2f, position.z + distance / 12f);
        if (platformType == PlatformType.SKEW_SPIRAL) {
            // Với block nghiêng
            position = transCache.position - transCache.up * 0.2f;
            position.z = position.z + distance / 12f;
        }

        return position;
    }

    public bool IsActiveDiamond() {
        return _attachDiamonds.Count != 0;
    }

    private void ResetDiamond() {
        foreach (var diamond in _attachDiamonds) {
            if(!diamond) continue;

            if (diamond.IdOnTile == noteID) {
                diamond.RecycleDiamond();
            }
        }
        _attachDiamonds.Clear();
    }

    public virtual bool CanSpawnDiamondOnTile() {
        return true;
    }

    protected virtual Vector3 GetDiamondPosition(float positionZ) {
        if (isMoodTile) {
            float range = RemoteConfigBase.instance.GetTileMaxPositionX();
            return new Vector3(Random.Range(-range / 2, range / 2), 0.5f, 0);
        } else {
            return new Vector3(0, 0.5f);
        }
    }

    public void ActiveDiamond(Diamond diamond, float positionZ) {
        if (diamond) {
            diamond.transform.localPosition = GetDiamondPosition(positionZ);
            _attachDiamonds.Add(diamond);
        }
    }

    public void Attach(Diamond diamond) {
        diamond.IdOnTile = platformData.currentIndexNote;
        diamond.transCache.SetParent(sq.transform);
        _attachDiamonds.Add(diamond);
    }
    public Vector3 GetPivotPosition() {
        return pivot ? pivot.localPosition : Vector3.zero;
    }


    #region Fever Mode

    private bool     _inFever;
    private Material _originalPlinthMaterial;

    private void FeverModeControllerOnOnFeverMode(bool isFever) {
        if (isFever) {
            if (!_inFever) {
                ActiveFeverFX();
            }
        } else {
            if (_inFever) {
                DeActiveFeverFX();
            }
        }
    }

    private void ActiveFeverFX() {
        _inFever = true;
        if (IsUsePlinth) {
            if (!_originalPlinthMaterial) {
                _originalPlinthMaterial = plinthMeshRenderer.sharedMaterial;
            }
            plinthMeshRenderer.sharedMaterial = Configuration.instance.GetFeverMaterial();
            var plinthTransform = plinthMeshRenderer.transform;
            Vector3 scale = plinthTransform.localScale;
           scale.y = 48;
           plinthTransform.localScale = scale;
           
           Vector3 position = plinthTransform.localPosition;
           if (plinthTransform.transform.localRotation.Equals(Quaternion.identity)) {
               position.y = -37.6f;
           } else {
               position.z = 37.6f;
           }
           plinthTransform.localPosition = position;
        }
    }
    private void DeActiveFeverFX() {
        _inFever = false;
        if (IsUsePlinth) {
            plinthMeshRenderer.sharedMaterial = _originalPlinthMaterial;
            
            var plinthTransform = plinthMeshRenderer.transform;
            Vector3 scale = plinthTransform.localScale;
            scale.y = 28;
            plinthTransform.localScale = scale;
           
            Vector3 position = plinthTransform.localPosition;
            if (plinthTransform.transform.localRotation.Equals(Quaternion.identity)) {
                position.y = -22f;
            } else {
                position.z = 22f;
            }
            plinthTransform.localPosition = position;
        }
    }

    #endregion

}