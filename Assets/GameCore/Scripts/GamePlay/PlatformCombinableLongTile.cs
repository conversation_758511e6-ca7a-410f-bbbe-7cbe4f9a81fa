using System;
using System.Collections;
using System.Collections.Generic;
using Dreamteck.Splines;
using Inwave;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.U2D;
using Random = UnityEngine.Random;
using Spline = Dreamteck.Splines.Spline;
using SplineUtility = UnityEngine.U2D.SplineUtility;

public class PlatformCombinableLongTile : PlatformLongTileHaptic {
    [Header("Combinable Long Tile")] [SerializeField]
    private Transform shapeParent;

    [SerializeField] private Transform splineParent;

    [SerializeField] private SpriteShapeController[] shapeControllers;
    [SerializeField] private SpriteShapeRenderer[]   shapeRenderers;
    [SerializeField] private SplineComputer[]        splineComputers;
    [SerializeField] private SplineMesh[]            splineMeshes;

    [SerializeField] private SpriteShapeRenderer   topFireShape;
    [SerializeField] private SpriteShapeController topFireShapeController;

    [SerializeField] private SplineComputer plinthSplineComputer;
    [SerializeField] private SplineMesh     plinthSplineMesh;
    [SerializeField] private SplineComputer splineHaptic;

    [SerializeField] private SplinePositioner trailPositioner;
    [SerializeField] private SplinePositioner pointer;
    [SerializeField] private ParticleSystem[] lineTrails;

    private float _endingZ;
    private bool  _calculatedPositions;
    private bool  _initializedHaptics;

    private List<Vector3> _positions;
    private SplinePoint[] _splinePoints;
    private SplinePoint[] _splinePointsPlinth;
    private Vector3[]     _shapePoints;
    private Coroutine     _spawnHapticRoutine;
    private Vector3       _dir;

    private const    Corner           CornerSetting = Corner.Automatic;
    private          ShapeTangentMode _tangent      = ShapeTangentMode.Linear;
    private          Spline.Type      _sPlineType   = Spline.Type.Linear;
    private readonly float            _offsetShape  = 0.35f;
    private readonly float            _offsetPlinth = 0.7f;

    protected override void OnTriggerExit(Collider col) { }

    private void LateUpdate() {
        if (GameController.instance.game == GameStatus.LIVE && _calculatedPositions) {
            if (_endingZ <= Ball.b.transCache.position.z - 15) {
                Spawner.s.HitByCollector(this);
            }
        }
    }

    public override void SetOrdering() {
        if (!isClone && !isCustom) {
            _currentOrder -= 3;
            LoopSortingOrder(3);
            if (!shapeRenderers.IsNullOrEmpty()) {
                shapeRenderers[0].sortingOrder = _currentOrder + 1;
                shapeRenderers[1].sortingOrder = _currentOrder;
            }

            if (topFireShape != null) {
                topFireShape.sortingOrder = _currentOrder + 2;
            }

            if (srStrongNoteFx != null) {
                srStrongNoteFx.sortingOrder = _currentOrder + 3;
            }

            if (spritePerfect != null) {
                spritePerfect.sortingOrder = _currentOrder + 15;
            }
        }

        if (isCustom) {
            _currentOrder -= 3;
            LoopSortingOrder(3);

            if (!shapeRenderers.IsNullOrEmpty()) {
                shapeRenderers[0].sortingOrder = _isGradientMaterial ? 0 : _currentOrder;
                shapeRenderers[1].sortingOrder = _currentOrder;
                shapeRenderers[2].sortingOrder = _currentOrder + 1;
            }

            if (topFireShape != null) {
                topFireShape.sortingOrder = _currentOrder + 1;
            }

            if (srStrongNoteFx != null) {
                srStrongNoteFx.sortingOrder = _currentOrder + 2;
            }

            if (spritePerfect != null) {
                spritePerfect.sortingOrder = _currentOrder + 15;
            }
        }

        if (spritePerfect != null) {
            var sortingOrder = spritePerfect.sortingOrder;
            hapticPrefab.sortingOrder = sortingOrder;
            foreach (var spriteRenderer in _hapticPoints) {
                spriteRenderer.sortingOrder = sortingOrder;
            }
        }

    }

    public override void ShowTopFire(bool isShow) {
        if (topFireShape != null) {
            topFireShape.gameObject.SetActive(isShow);
        }
    }

    public override void SetScaleAndPosition(Vector3 scale, Vector3 position) {
        transCache.position = position;

        if (!_perfectGetOriginalScale) {
            _perfectGetOriginalScale = true;
            _perfectOriginalScale = spritePerfect.transform.localScale;
        }

        _scaleZ = scale.z;

        perfectResizeScale = new Vector3(_perfectOriginalScale.x / scale.z, _perfectOriginalScale.y / scale.x,
            _perfectOriginalScale.z);
        spritePerfect.transform.localScale = perfectResizeScale;

        UpdateTileWhenChangeSize(scale.z);
        OnSlide(false);
    }

    public void Init(bool dynamicPosition = true) {
        CalculatePositions(dynamicPosition);

        //Update trails
        trailSlide.transform.localPosition = new Vector3(0, 0, 0);
        foreach (var t in lineTrails) {
            var pos = t.transform.localPosition;
            pos = new Vector3(pos.x * GetRatioWidth(), pos.y, pos.z);
            t.transform.localPosition = pos;
        }
    }

    public override void OnSlide(bool slide, Vector3 position = new Vector3()) {
        lineTrails[0].gameObject.SetActive(slide);
        lineTrails[1].gameObject.SetActive(slide);
        var mainModule = lineTrails[0].main;
        mainModule.startColor = _currentColor;
        var mainModule1 = lineTrails[1].main;
        mainModule1.startColor = _currentColor;
        base.OnSlide(slide, position);
    }

    public override void AdjustBoxColliderLongType() {
        shapeParent.transform.localScale = Vector3.one * (0.635f * GetRatioWidth());
        splineParent.transform.localScale = Vector3.one * (0.635f * GetRatioWidth());
        plinthSplineComputer.transform.localPosition = Vector3.down*(3.55f / GetRatioWidth());
        //base.AdjustBoxColliderLongType();
    }
    public void ForceControlScale() {
        shapeParent.transform.localScale = Vector3.one ;
        splineParent.transform.localScale = Vector3.one;
    }

    protected override void UpdateTrailOnSlide(Vector3 position) { }

    public override void UpdatePivot() {
        shapeParent.localPosition = new Vector3(0f, 0f, 0f);
        base.UpdatePivot();
    }

    protected override void SetTopColor(Color c, float secondaryAlpha = -1) {
        if (shapeRenderers.IsNullOrEmpty())
            return;

        if (_isGradientMaterial) {
            shapeRenderers[0].sharedMaterial
                .SetColor(ShaderAttribute.ColorFrom, Color.Lerp(_currentColor, Color.white, 0.2f));
            shapeRenderers[0].sharedMaterial.SetColor(ShaderAttribute.ColorTo, c);
        } else {
            shapeRenderers[0].color = c;
        }

        if (secondaryAlpha > 0) {
            _currentColor.a = secondaryAlpha;
        }

        shapeRenderers[1].color = _currentColor;
    }

    protected override void SetTopFireColor(Color color) {
        if (topFireShape != null) {
            topFireShape.color = color;
        }
    }

    public override void Slide(float slideTime, Vector3 position) {
        if (trailSlide == null)
            return;

        if (isSliding) {
            SplineSample sample = new SplineSample();
            splineHaptic.Project(position, ref sample);
            var percentage = sample.percent;// Mathf.Min(slideTime / timeSlide, 1);
            trailPositioner.SetPercent(percentage);
            trailSlide.transform.eulerAngles = new Vector3(90f, 90f, 90);
            PlayHaptic(platformData.currNoteObj.timeAppear + slideTime);
        }
    }

    public override Vector3 GetAutoPosition(float slideTime) {
        var percentage = Mathf.Min(slideTime / timeSlide, 1f);
        pointer.SetPercent(percentage);
        return pointer.transform.position;
    }
    
    public override Vector3 GetAutoPosition(Vector3 position) {
        SplineSample sample = new SplineSample();
        splineHaptic.Project(position, ref sample);
        return sample.position;
    }
    protected override Vector3 GetDiamondPosition(float positionZ) {
        SplineSample sample = new SplineSample();
        splineHaptic.Project(Vector3.forward * positionZ, ref sample);
        Vector3 pos = sample.position;
        pos.y = 0.5f;
        return pos - transCache.position;
    }

    protected override void CreateHapticPoints() { }

    private IEnumerator IEGenerateHapticPoints() {
        yield return null;

        _haptics = platformData.currNoteObj.haptics;
        int index = 0;

        if (_haptics != null) {
            int lengthHaptic = _haptics.Length;
        if (_haptics != null && lengthHaptic != 0) {
                float startTime = platformData.currNoteObj.timeAppear;
                float duration = platformData.currNoteObj.duration;
                for (index = 0; index < lengthHaptic; index++) {
                    if (_haptics[index] > startTime + duration) {
                        break;
                    }

                    ShowHapticPoint(index, GetSplinePosition(_haptics[index] - startTime));
                }
            }
        }

        for (int i = index; i < _hapticPoints.Count; i++) {
            _hapticPoints[i].gameObject.SetActive(false);
        }
    }

    protected override void ShowHapticPoint(int i, Vector3 position) {
        SpriteRenderer point;
        if (i >= _hapticPoints.Count) {
            var transform2 = hapticPrefab.transform;
            point = Instantiate(hapticPrefab, transform2.parent);
            _hapticPoints.Add(point);
            var transform1 = point.transform;
            transform1.localRotation = transform2.localRotation;
            transform1.localScale = transform2.localScale;
        } else {
            point = _hapticPoints[i];
        }

        point.transform.position = new Vector3(position.x, 0, position.z);
        point.gameObject.SetActive(true);
    }

    public void UpdateNodePositions(List<Vector3> nodePos, ShapeTangentMode tangent = ShapeTangentMode.Linear,
                                    Spline.Type splineType = Spline.Type.Linear) {
        _positions = nodePos;
        if (_positions.Count == 1) {
            _positions.Add(_positions[0] + Vector3.forward * 2.2f);
        }
        
        _tangent = tangent;
        _sPlineType = splineType;
        shapeRenderers[1].gameObject.SetActive(_tangent == ShapeTangentMode.Linear);
    }

    private void CalculatePositions(bool dynamicPosition = true) {
        int totalCount = _positions.Count;
        
        _splinePoints = new SplinePoint[totalCount];
        _splinePointsPlinth = new SplinePoint[totalCount];

        _shapePoints = new Vector3[totalCount];
        for (int i = 0; i < totalCount; i++) {
            if (i == totalCount - 1) {
                _endingZ = _positions[i].z;
            }
            _positions[i] = transCache.InverseTransformPoint(_positions[i]);
        }

        for (int i = 0; i < totalCount; i++) {
            int flipVal = i == 0 ? -1 : 1;
            _splinePoints[i] = new SplinePoint(new Vector3(-_positions[i].z, 0, _positions[i].x));
            if (i == 0) {
                _dir = _positions[i + 1] - _positions[i];
            } else if (i == totalCount - 1) {
                _dir = _positions[i] - _positions[i - 1];
            }

            _dir = Vector3.Normalize(_dir);
            var plinth = i > 0 && i < totalCount - 1
                ? _positions[i]
                : _positions[i] + _dir * ((_offsetShape + _offsetPlinth) * flipVal);
            _splinePointsPlinth[i] = new SplinePoint(new Vector3(-plinth.z, 0, plinth.x));
            var shape = i > 0 && i < totalCount - 1
                ? _positions[i]
                : _positions[i] + _dir * (_offsetShape * flipVal);
            _shapePoints[i] = new Vector3(-shape.z, shape.x, 0);
        }

        UpdateSplineType();
        splineHaptic.SetPoints(_splinePoints, SplineComputer.Space.Local);

        UpdateShapeNodes();
        UpdateDeathLines();
        UpdatePlinth();

        _calculatedPositions = true;
    }

    private void UpdateSplineType() {
        splineHaptic.type = _sPlineType;
        plinthSplineComputer.type = _sPlineType;
        foreach (var spline in splineComputers) {
            spline.type = _sPlineType;
        }
        plinthSplineComputer.gameObject.SetActive(_sPlineType == Spline.Type.Linear);
    }

    private void UpdateShapeNodes() {
        foreach (var shape in shapeControllers) {
            var spline = shape.spline;
            spline.Clear();

            shape.cornerAngleThreshold = 90f;
            for (int i = 0; i < _shapePoints.Length; i++) {
                SplineControlPoint point = new SplineControlPoint() {
                    spriteIndex = i,
                    position = _shapePoints[i],
                    height = 1f,
                    corner = false,
                    cornerMode = CornerSetting,
                    mode = _tangent,
                };
                spline.InsertPointAt(i, point);
            }

            if (_tangent != ShapeTangentMode.Linear) {
                Smoothen(shape);
            }
        }

        topFireShapeController.spline.Clear();
        topFireShapeController.cornerAngleThreshold = 90f;
        for (int i = 0; i < _shapePoints.Length; i++) {
            SplineControlPoint point = new SplineControlPoint() {
                position = _shapePoints[i],
                height = 1f,
                corner = false,
                cornerMode = CornerSetting,
                mode = _tangent
            };
            topFireShapeController.spline.InsertPointAt(i, point);
        }

        if (_tangent != ShapeTangentMode.Linear) {
            Smoothen(topFireShapeController);
        }
    }

    private void UpdateDeathLines() {
        for (int i = 0; i < 2; i++) {
            splineComputers[i].SetPoints(_splinePoints, SplineComputer.Space.Local);
            splineMeshes[i].GetChannel(0).count = _positions.Count - 1;
            splineMeshes[i].GetChannel(0).minScale = new Vector3(2f * GetRatioWidth(), 1, 1);
        }
    }

    private void UpdatePlinth() {
        plinthSplineComputer.SetPoints(_splinePointsPlinth, SplineComputer.Space.Local);
        plinthSplineMesh.GetChannel(0).count = _positions.Count - 1;
        plinthSplineMesh.GetChannel(0).minScale = new Vector3(4.94f * GetRatioWidth(), 2.865f, 1);
    }

    private Vector3 GetSplinePosition(float time) {
        var percentage = Mathf.Min(time / timeSlide, 1f);
        pointer.SetPercent(percentage);
        return pointer.transform.position;
    }

    protected override void OnCompletedMoving() {
        StartCoroutine(IEGenerateHapticPoints());
    }

    #region S Long Tile

    public void SetSPositions(List<Vector3> nodes) {
        UpdateNodePositions(nodes, ShapeTangentMode.Continuous, Spline.Type.CatmullRom);
    }

    private void Smoothen(SpriteShapeController spriteShapeController) {
        byte amount = (byte) spriteShapeController.spline.GetPointCount();
        for (byte pointIndex = 0; pointIndex < amount; pointIndex++) {
            Smoothen(spriteShapeController, pointIndex);
        }
    }

    private void Smoothen(SpriteShapeController sc, byte pointIndex) {
        Vector3 position = sc.spline.GetPosition(pointIndex);

        Vector3 positionNext = sc.spline.GetPosition(SplineUtility.NextIndex(pointIndex, sc.spline.GetPointCount()));

        Vector3 positionPrev =
            sc.spline.GetPosition(SplineUtility.PreviousIndex(pointIndex, sc.spline.GetPointCount()));

        if (pointIndex == 0) {
            positionPrev = GetReflectionPoint(positionNext, position);
        } else if (pointIndex == sc.spline.GetPointCount() - 1) {
            positionNext = GetReflectionPoint(positionPrev, position);
        }

        Vector3 forward = gameObject.transform.forward;

        float scale = Mathf.Min((positionNext - position).magnitude, (positionPrev - position).magnitude) * 0.33f;

        Vector3 leftTangent = (positionPrev - position).normalized * scale;

        Vector3 rightTangent = (positionNext - position).normalized * scale;

        sc.spline.SetTangentMode(pointIndex, ShapeTangentMode.Continuous);

        SplineUtility.CalculateTangents(position, positionPrev, positionNext, forward, scale, out rightTangent,
            out leftTangent);

        sc.spline.SetLeftTangent(pointIndex, leftTangent);
        sc.spline.SetRightTangent(pointIndex, rightTangent);
    }

    private static Vector3 GetReflectionPoint(Vector3 root, Vector3 center) {
        return new Vector3(center.x * 2 - root.x, center.y * 2 - root.y, center.z * 2 - root.z);
    }

    #endregion
}