using UnityEngine;

public class PlatformLongTile : Platform {
    protected const float srStandardScale = 0.91f;

    [Header("Platform LongTile")] [SerializeField]
    protected TrailRenderer trailSlide;

    [SerializeField] protected Transform  landingPoint;
    [SerializeField] protected Transform  takeOffPoint;
    [SerializeField] protected GameObject deathLine;
    [SerializeField] private   bool       isChangeStartColorChild = true;

    //private
    protected Vector3 _perfectOriginalScale;
    protected bool    _perfectGetOriginalScale = false;
    protected float   _scaleZ;
    protected Vector3 _pointSlide = new Vector3(0, 0, 0);

    private ParticleSystem[] _particlesTrailSlide;

    protected Vector3 perfectResizeScale;

    private readonly Vector3 _defaultSquareScale = new Vector3(4.3f, 0.1f, 3.225f);

    protected override void Awake() {
        base.Awake();

        if (trailSlide != null && isChangeStartColorChild) {
            _particlesTrailSlide = trailSlide.GetComponentsInChildren<ParticleSystem>();
        }
    }

    public override void SetScaleAndPosition(Vector3 scale, Vector3 position) {
        base.SetScaleAndPosition(scale, position);
        if (!_perfectGetOriginalScale) {
            _perfectGetOriginalScale = true;
            _perfectOriginalScale = spritePerfect.transform.localScale;
        }

        _scaleZ = scale.z;

        perfectResizeScale = new Vector3(_perfectOriginalScale.x / scale.z, _perfectOriginalScale.y / scale.x,
            _perfectOriginalScale.z);
        spritePerfect.transform.localScale = perfectResizeScale;

        UpdateTileWhenChangeSize(scale.z);
        OnSlide(false);
        if (trySkinParent) {
            Vector3 scaleTrySkin = trySkinParent.localScale;
            scaleTrySkin.z /= scale.z;
            trySkinParent.localScale = scaleTrySkin;
        }
    }

    protected virtual void UpdateTileWhenChangeSize(float scaleZ) {
        if (float.IsNaN(scaleZ)) {
            return;
        }
        float srScale = srStandardScale / scaleZ;
        float srWidth = scaleZ * 17.264f + 11.25f;
        Vector3 scale = Vector3.zero;
        Vector2 size = Vector2.zero;
        for (int i = 0; i < sr.Length; i++) {
            scale = sr[i].transform.localScale;
            scale.x = srScale;
            sr[i].transform.localScale = scale;

            size = sr[i].size;
            size.x = srWidth;
            //size.y = (scaleZ < 1 ? 17.1f : Mathf.Clamp( 17.1f - scaleZ / 5f,16.5f,17.1f));
            sr[i].size = size;
        }

        if (topFire != null) {
            topFire.transform.localScale = scale;
            topFire.size = size;
        }
    }

    public override void SetText(string str) {
        base.SetText(str);
        this.text.transform.rotation = Quaternion.identity;
    }

    public override void SetOrdering() {
        base.SetOrdering();
        if (trailSlide != null) {
            trailSlide.sortingOrder = _currentOrder + 4;
        }
    }

    public override void Reset() {
        if (deathLine != null) {
            deathLine.SetActive(false);
        }

        base.Reset();
    }

    public override void OnSlide(bool slide, Vector3 position = new Vector3()) {
        base.OnSlide(slide, position);
        if (slide) {
            if (trailSlide != null) {
                trailSlide.startColor = _currentColor;
                if (isChangeStartColorChild) {
                    foreach (var particle in _particlesTrailSlide) {
                        ParticleSystem.MainModule main = particle.main;
                        main.startColor = _currentColor;
                    }
                }

                UpdateTrailOnSlide(position);
            }

            if (deathLine != null) {
                deathLine.SetActive(true);
            }
        }

        if (trailSlide != null) {
            trailSlide.gameObject.SetActive(slide);
        }
    }

    protected virtual void UpdateTrailOnSlide(Vector3 position) {
        _pointSlide.z = (position - transCache.position).z / _scaleZ;

        trailSlide.Clear();
        trailSlide.emitting = false;
        trailSlide.transform.localPosition = _pointSlide;
        trailSlide.emitting = true;
    }

    public override void Slide(float slideTime, Vector3 position) {
        base.Slide(slideTime, position);
        if (trailSlide == null)
            return;

        if (isSliding) {
            _pointSlide.z = (position - transCache.position).z / _scaleZ;
            trailSlide.transform.localPosition = _pointSlide;
            PlayHaptic(platformData.currNoteObj.timeAppear + slideTime);
        }
    }

    public override Vector3 GetLandingPosition() {
        return landingPoint != null ? landingPoint.position : base.GetLandingPosition();
    }

    public override Vector3 GetTakeOffPosition() {
        if (takeOffPoint != null) {
            return takeOffPoint.position;
        } else {
            return base.GetTakeOffPosition();
        }
    }

    public override Vector3 GetAutoPosition(float slideTime) {
        return Vector3.Lerp(GetLandingPosition(), GetTakeOffPosition(), slideTime / timeSlide);
    }

    protected virtual void PlayHaptic(float time) {
    }

    protected override void SinkEffect() {
        // base.SinkEffect();
        //do nothing
    }

    protected virtual float GetRatioLength() {
        return sqTransCache.localScale.x / _defaultSquareScale.x;
    }

    protected virtual float GetRatioWidth() {
        return sqTransCache.localScale.z / _defaultSquareScale.z;
    }
    
    // protected override Vector3 GetDiamondPosition(float positionZ) {
    //     Vector3 zStart = GetLandingPosition();
    //     Vector3 zEnd = GetTakeOffPosition();
    //     float ratio = Mathf.Clamp01((positionZ - zStart.z) / (zEnd.z - zStart.z));
    //     Vector3 pos = zStart + ratio * (zEnd - zStart);
    //     return pos - transCache.position;
    // }
}