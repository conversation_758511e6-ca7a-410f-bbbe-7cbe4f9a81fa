using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Serialization;

public class TimelineManager : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameSubscriber {
    [SerializeField] protected PlayableDirector playableDirector;
    
    protected virtual void Start() {
        GameController.instance.addSubcriber(this);
        Spawner.OnStartMusic += SetTimelineDuration;
        Ball.OnTimeScaleUpdated += SetTimeLineSpeed;
    }

    private void OnDestroy() {
        Spawner.OnStartMusic -= SetTimelineDuration;
        Ball.OnTimeScaleUpdated -= SetTimeLineSpeed;
    }

    protected virtual void OnStart() {
        playableDirector.Play();
    }

    protected virtual void OnStop() {
        playableDirector.Stop();
    }

    protected virtual void OnPause() {
        playableDirector.Pause();
    }

    protected virtual void OnResume() {
        playableDirector.Resume();
    }

    protected virtual void SetTimelineDuration(float duration) {
        playableDirector.time = duration;
    }

    private void SetTimeLineSpeed(float speed) {
        if (playableDirector.playableGraph.IsValid()) {
            playableDirector.playableGraph.GetRootPlayable(0).SetSpeed(speed);
        }
    }
    
    public virtual void gameStart() {
        OnStart();
    }

    public virtual void gameContinue() {
    }

    public virtual void gameOver() {
        OnStop();
    }
}
