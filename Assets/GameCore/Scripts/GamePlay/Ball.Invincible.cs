using System;
using System.Collections;
using System.Threading;
using Cysharp.Threading.Tasks;
using TileHop.EventDispatcher;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Pooling;
using UnityEngine;

public partial class Ball {
    private byte _amountShield;

    private ParticleSystem _invincibleHitFx; // vfx hit when runout of road
    private ParticleSystem _shieldFX; // vfx normal
    private ParticleSystem _shieldActiveFX; // vfx active
    private ParticleSystem _shieldPreDeactiveFX;
    private ParticleSystem _shieldBrokenFX;

    private bool _isTriggeredInvincible;
    private bool _isInActiveInvincible;

    private BoosterAngle _boosterAngle;
    // private BoosterAngle _boosterAngleStreak;

    // Manual cancellation control for rainbow operations only (has StopCoroutine in original)
    private CancellationTokenSource _rainbowCancellation;

    public bool isInvincible => _amountShield > 0;

    public void InvincibleJump(bool isHumanEnd = false) {
        if (_isDead) {
            return;
        }

        if (isInHyperBoostSection && _isFlyingUp) {
            return;
        }

        var bl = _destinationObj;
        bl.HitWithoutEffect();

        _streakPerfectCount = UpdateStreakPerfectCount(false, _streakPerfectCount);
        UpdateTrail(isPerfect: false);

        if (!ProcessEndless(bl)) {
            return;
        }

        if (bl.IsNoteZicZac()) {
            if (bl.IsLastNoteZicZac()) {
                OffSpecialSectionZiczac();
            } else if (bl.IsStartNoteZicZac()) {
                StartSpecialSectionZicZac();
            }
        } else {
            OffSpecialSectionZiczac();
        }

        CalculateJump(bl, isHumanEnd);
        PlayJumpAnimation(bl);

        TriggerInvincible();

        if (_isInActiveInvincible && !_isTriggeredInvincible && isInvincible) {
            CompleteDeactiveInvincible();
        }

        OnActionJump?.Invoke();
    }

    private bool Jump(Platform bl, bool isHumanEnd = false) {
        if (_isDead) {
            return false;
        }

        if (rb.velocity.y > 0 && !isForceMove) {
            return false;
        }

        if (!ProcessEndless(bl)) {
            return false;
        }

        if (isInHyperBoostSection && !bl.IsNoteHyperBoost()) {
            this.PostEvent(EventID.HyperSectionEnd);
            isInHyperBoostSection = false;
            
            ResetGravity();
        }

        if (bl.IsNoteZicZac()) {
            if (bl.IsLastNoteZicZac()) {
                OffSpecialSectionZiczac();
            } else if (bl.IsStartNoteZicZac()) {
                StartSpecialSectionZicZac();
            }
        } else {
            OffSpecialSectionZiczac();
        }

        if (bl.isTileTrySkin) {
            UpdateTryNewSkin(bl);
        }

        CalculateJump(bl, isHumanEnd);
        ProcessPlatformDataAfterJump(bl);
        PlayJumpAnimation(bl);

        OnActionJump?.Invoke();

        if (_isInActiveInvincible && !_isTriggeredInvincible && isInvincible) {
            CompleteDeactiveInvincible();
        }

        return true;
    }

    private bool ProcessEndless(Platform bl) {
        int currentJumpNote = Spawner.s.currentJumpNoteID;
        if (currentJumpNote > 0) {
            int indexNextNote = currentJumpNote + 1;
            if (indexNextNote == NotesManager.instance.numberNoteInRound) {
                indexNextNote = 0;
            }

            //Không cho player nhảy cách note
            if (bl.noteID != indexNextNote) {
                return false;
            }

            if (GameController.enableEndless) {
                if (gameController.Score > 0 && bl.noteID == 0) { //end of road
                    UpdateEndlessMode();
                    NotesManager.instance.numberNoteInRound = NotesManager.instance.noteCount;
                }
            }

            if (GameController.enableEndless && ChallengeMode.IsActive) {
                if (ChallengeMode.steadyIncreaseSpeed) {
                    ProcessTimeScaleSteadyIncrease(bl);
                } else {
                    ProcessTimeScaleStaticIncrease(bl);
                }
            } else if (Configuration.instance.EndlessMode_Type == EndlessModeType.SpeedUpFromRound0) {
                UpdateTimeScale(GetTimeScale(bl.noteID));
            } else if (Configuration.instance.EndlessMode_Type == EndlessModeType.SpeedUpFromRound1 &&
                       endlessModeCount >= 1) {
                UpdateTimeScale(GetTimeScale(bl.noteID));
            }

            if (endlessModeCount == 0) {
                // trailer
                ballTrailer.UpTrailerStrength((float) _streakPerfectCount / Spawner.GetPerfectMax(2));
            }
        }

        return true;
    }

    private void ProcessTimeScaleStaticIncrease(Platform bl) {
        if (endlessModeCount == 0) {
            if (bl.noteID % NotesManager.instance.noteCount == 0) {
                //speed up 1
                timeScale = ChallengeMode.GetSpeedUp(1);
                UpdateTimeScale(timeScale);
            } else if (bl.noteID % NotesManager.instance.noteCount == ((int) (NotesManager.instance.noteCount / 2))) {
                //speed up 2
                timeScale = ChallengeMode.GetSpeedUp(2);
                UpdateTimeScale(timeScale);
                gameController.CollectStar();
            }
        } else {
            if (bl.noteID % NotesManager.instance.noteCount == 0) {
                //speed up 3
                timeScale = ChallengeMode.GetSpeedUp(3);
                UpdateTimeScale(timeScale);
                gameController.CollectStar();
                gameController.ShowVFXChallengeMode(Ball.b.timeScale);
            }
        }
    }

    private void ProcessTimeScaleSteadyIncrease(Platform bl) {
        int totalNote = NotesManager.instance.noteCount;
        if (totalNote <= 1) {
            return;
        }
        int idNote = bl.noteID % totalNote;
        float startTimeScale;
        float endTimeScale;
        if (endlessModeCount == 0) {
            int middleNote = (int) (totalNote / 2);
            if (idNote < middleNote) {
                //crown 1
                startTimeScale = 1f;
                endTimeScale = ChallengeMode.GetSpeedUp(1);
                timeScale = Mathf.Lerp(startTimeScale, endTimeScale, idNote / (float) middleNote);
            }else if (idNote == middleNote) {
                gameController.CollectStar();
                timeScale = ChallengeMode.GetSpeedUp(1);
            } else {
                //crown 2
                startTimeScale = ChallengeMode.GetSpeedUp(1);
                endTimeScale = ChallengeMode.GetSpeedUp(2);
                timeScale = Mathf.Lerp(startTimeScale, endTimeScale, (idNote- middleNote) / (float) middleNote);
            }
            UpdateTimeScale(timeScale);
        } else {
            if (idNote == 0) {
                //speed up 3
                gameController.CollectStar();
                gameController.ShowVFXChallengeMode(Ball.b.timeScale);
            }
            
            startTimeScale = ChallengeMode.GetSpeedUp(2);
            endTimeScale = ChallengeMode.GetSpeedUp(3);
            timeScale = Mathf.Lerp(startTimeScale, endTimeScale, idNote / ((float) totalNote - 1));
            UpdateTimeScale(timeScale);
        }
    }

    private void Bouncing(Platform bl, float jumpHeight, float maxHeight) {
        // không play bouncing ở note đầu
        if (remoteConfig.BouncingBall_UseBouncingBall && !isCharacter) {
            // reset scale when jump into last note
            if (CheckEnd(bl.noteID) || bl.isSlideTile || bl.isHitchhikeTile || isInHyperBoostSection ||
                isInZicZacSection) {
                stretchController.StopPreviousEffect();
                stretchController.ResetBouncing();
            } else {
                float percentage = jumpHeight / maxHeight;

                // bouncing: bounce when jumping and sliding on long tile
                bool isSliding = bl.isSlideTile || bl.isHitchhikeTile;
                stretchController.Play(Mathf.Sqrt(percentage), isSliding ? bl.timeSlide / timeScale : jumpTime,
                    isSliding: isSliding);

                if (_mirrorBall) {
                    _mirrorBall.stretchController.Play(percentage, jumpTime);
                }
            }
        }
    }

    private void CalculateJump(Platform bl, bool isHumanEnd) {
        Vector3 velocity = rb.velocity;

        float time = bl.nextNoteDistance / timeScale;
        MapManager.instance.UpdateSoundWave(time);
        trustTime += time;
        if (isInHyperBoostSection) {
            if (bl.isSlideTile) {
                trustTime += bl.timeSlide;
            }
        }

        jumpTime = trustTime - (Time.time - _beginTime);
        if (jumpTime < 0.01f) {
            jumpTime = 0.01f;
        }

        Vector3 gravity = Physics.gravity;

        float jumpHeight = 10;
        float scaleJumpByType = jumpTime > 0 ? GetScaleJumpByType(jumpTime) : 1;
        float maxHeight = 1.4f * _jumHeight * scaleJumpByType;
        jumpHeight = maxHeight * Mathf.Sqrt(time * timeScale) * Mathf.Sqrt(Spawner.s.bpm / 110f);
        if (jumpHeight > maxHeight) {
            jumpHeight = maxHeight;
        }

        Bouncing(bl, jumpHeight, maxHeight);

        gravity.y = -(8 * jumpHeight) / (jumpTime * jumpTime);
        Physics.gravity = gravity;
        velocity.y = Mathf.Abs((jumpTime / 2) * gravity.y);

        HandleCharacterRotation(bl, time, jumpTime);

        if (isHumanEnd || isInHyperBoostSection || isInZicZacSection) {
            LockGravity();
            SetVelocity(Vector3.zero);
        } else if (bl.isSlideTile) {
            LockGravity();
            SetVelocity(Vector3.zero);
            IESlideOnTile(velocity, bl, jumpTime, this.GetCancellationTokenOnDestroy()).Forget();
        } else if (bl.isHitchhikeTile) {
            LockGravity();
            SetVelocity(Vector3.zero);
            IEHitchHikeOnTile(velocity, bl, jumpTime, this.GetCancellationTokenOnDestroy()).Forget();
        } else {
            SetVelocity(velocity);
        }

        GameItems.instance.RainbowImpactStatus(bl.isRainbowTile && !isHumanPlayer);
        if (bl.isRainbowTile) {
            // Cancel any existing rainbow operation (equivalent to StopCoroutine)
            _rainbowCancellation?.Cancel();
            _rainbowCancellation?.Dispose();

            // Create new manual cancellation token
            _rainbowCancellation = new CancellationTokenSource();

            // Combine manual + auto-destroy tokens
            var linkedToken = CancellationTokenSource.CreateLinkedTokenSource(
                _rainbowCancellation.Token, this.GetCancellationTokenOnDestroy()).Token;

            IEPlayOnRainBow(bl.rainbow, bl.platformData.currNoteObj.duration, linkedToken).Forget();
        }

        Spawner.s.currentJumpNoteID = bl.noteID;

        //Fix the ball position to correct
        transCache.position = bl.FixPositionOnHit(transCache.position, _ballPositionInit.y);

        if (Spawner.s.mainBlockQueue.Count > 0) {
            _prevPlatformPosition = bl.transCache.position;
            Platform nextPlatform = Spawner.s.mainBlockQueue.Dequeue();
            if (nextPlatform.platformData.currentIndexNote == Spawner.s.currentJumpNoteID) {
                nextPlatform = Spawner.s.mainBlockQueue.Dequeue();
            }

            _destinationObj = nextPlatform;
            if (GameController.IsAutoPlay()) {
                _autoCurrentX = transCache.position.x;

                _autoTime = time;
                _autoCurTime = 0;
                if (bl is HitchhikePlatform hitch) {
                    _autoTime += hitch.timeSlide / timeScale;
                }
            }

            if (nextPlatform.sectionType == IngameSectionType.Normal && bl.sectionType == IngameSectionType.Mirror) {
                DestroyMirrorBall(true);
            }
        } else {
            _prevPlatformPosition = bl.transCache.position;
        }

        if (bl.isForceMove) {
            if (bl is PipePlatform pipePlatform) {
                forceMoveType = PlatformType.PIPE_TILE;
                isForceMove = true;
                OnChangeStageForceMove?.Invoke(true);
                _currentPipePlatform = pipePlatform;
                _currentPipePlatform.ForceMove(true);
            } else {
                Logger.EditorLogError("Chưa làm trường hợp này!!!!");
            }
        } else if (bl.platformData.stage == Stage.SKEW_SPIRAL &&
                   bl.platformData.platformType == PlatformType.SKEW_SPIRAL) {
            if (!isForceMove) {
                //Debug.LogError($"Go to force Move from {bl.platformData.currentIndexNote}");
                OnChangeStageForceMove?.Invoke(true);
            }

            isForceMove = true;
            forceMoveType = PlatformType.SKEW_SPIRAL;
            if (!_lockedGravity) {
                LockGravity();
                SetVelocity(Vector3.zero);
            }

            _centerPosition = MapManager.instance.CenterPosition();
            Vector3 middlePoint = (_prevPlatformPosition + _destinationObj.transCache.position) / 2;
            Vector2 offset = new Vector2(_centerPosition.x - middlePoint.x, _centerPosition.y - middlePoint.y);
            _centerPosition.x += offset.x * (time - 0.5f) * 2;
            _centerPosition.y += offset.y * (time - 0.5f) * 2;
            _centerPosition.z = middlePoint.z;

            // reset các biến giống autoMove để force-move
            _autoTime = time;
            _autoCurTime = 0;
            _prevAngle = bl.transCache.rotation.eulerAngles.z;
            // khi ra khỏi stage, thì _destination angle = 0. Để tránh nhân vật nghiêng khi nhảy sang ô skew
            _destinationAngle = bl.platformData.isEndStage ? 0 : _destinationObj.transCache.rotation.eulerAngles.z;
            if (_destinationAngle < _prevAngle - 5)
                _destinationAngle += 360;
        } else {
            if (isForceMove) {
                OnChangeStageForceMove?.Invoke(false);
            }

            isForceMove = false;
            transCacheRotation.rotation = Quaternion.identity; // reset nhân vật về góc thẳng.
            if (_lockedGravity && !isHumanEnd && !bl.isSlideTile && !bl.isHitchhikeTile && !isInHyperBoostSection &&
                !isInZicZacSection) {
                UnlockGravity();
                SetVelocity(velocity);
            }

            OnJump?.Invoke(bl.noteID);
        }
    }

    private void PlayJumpAnimation(Platform bl) {
        if (isHumanPlayer && !onLongTile && !onHitchTile) {
            //prepare end of road
            bool isHumanPrepareEnd = isHumanPlayer && !GameController.enableEndless &&
                                     bl.noteID == (NotesManager.instance.noteCount - 2);
            if ((EndlessIteration.IsActive || ChallengeMode.IsActive) &&
                Spawner.s.platformManager.GetTotalTileFront() == 1) {
                isHumanPrepareEnd = isHumanPlayer;
            }

            if (isHumanPrepareEnd) {
                ballInstance.SetAnimationCharacterLastJump(jumpTime);
            } else {
                if (isInHyperBoostSection || isInZicZacSection) {
                    ballInstance.SetStateStart();
                } else {
                    isJumpRight = bl.noteID % 2 == 0;
                    if (jumpTime > 0) {
                        ballInstance.SetAnimationCharacterJump(jumpTime, isJumpRight);
                        if (_mirrorBall) {
                            _mirrorBall.SetAnimationCharacterJump(jumpTime, isJumpRight);
                        }
                    }
                }
            }
        }
    }

    public void SetInvincible(byte amount) {
        _amountShield = amount;
        if (!_invincibleHitFx) {
            var asset = Resources.Load<ParticleSystem>("Booster/ShieldInvincibleVFX");
            if (asset) {
                _invincibleHitFx = Instantiate(asset);
            }
        }

        if (!_shieldFX) {
            var asset = Resources.Load<ParticleSystem>("Booster/ShieldVFX");
            if (asset) {
                _shieldFX = Instantiate(asset, transCache);
            }
        }

        if (!_shieldActiveFX) {
            var asset = Resources.Load<ParticleSystem>("Booster/ShieldActiveVFX");
            if (asset) {
                _shieldActiveFX = Instantiate(asset, transCache);
            }
        }

        if (!_shieldPreDeactiveFX) {
            var asset = Resources.Load<ParticleSystem>("Booster/ShieldWarningVFX");
            if (asset) {
                _shieldPreDeactiveFX = Instantiate(asset, transCache);
            }
        }

        if (!_shieldBrokenFX) {
            var asset = Resources.Load<ParticleSystem>("Booster/ShieldBrokenVFX");
            if (asset) {
                _shieldBrokenFX = Instantiate(asset, transCache);
            }
        }

        if (_shieldFX) {
            _shieldFX.gameObject.SetActive(true);
            _shieldFX.Play();
        }

        if (_shieldActiveFX) {
            _shieldActiveFX.gameObject.SetActive(false);
        }

        if (_amountShield > 0) {
            if (!_boosterAngle) {
                _boosterAngle = Instantiate(Resources.Load<BoosterAngle>("Booster/BoosterAngleStreak"), null);
            }

            _boosterAngle.Active(transCache);
        }
    }

    public void SetVincible() {
        _amountShield = 0;
        _isInActiveInvincible = false;
        if (_invincibleHitFx) {
            _invincibleHitFx.Stop();
        }

        if (_shieldFX) {
            _shieldFX.gameObject.SetActive(false);
        }

        if (_shieldActiveFX) {
            _shieldActiveFX.gameObject.SetActive(false);

        }

        if (_shieldPreDeactiveFX) {
            _shieldPreDeactiveFX.gameObject.SetActive(false);
        }

        if (_shieldBrokenFX) {
            _shieldBrokenFX.Stop();
        }

        if (_boosterAngle) {
            _boosterAngle.DeActive();
        }

        // if (_boosterAngleStreak) {
        //     _boosterAngleStreak.DeActive();
        // }
    }

    /// <summary>
    /// Active protection and then cooldown to deactive shield
    /// </summary>
    public void TriggerInvincible(bool isShowHitProtector = false) {
        if (!_isTriggeredInvincible && !_isInActiveInvincible) {
            IEDeactiveInvincible(this.GetCancellationTokenOnDestroy()).Forget();
            _isTriggeredInvincible = true;
            _isInActiveInvincible = true;
        }

        if (!isShowHitProtector) {
            PlayInvincibleHitFx();
        }
    }

    private async UniTaskVoid IEDeactiveInvincible(CancellationToken cancellationToken) {
        try {
            BoosterManager.UseItem(BoosterType.Shield, remoteConfig.Booster_Shield_InvincibleTime);

            if (_boosterAngle) {
                _boosterAngle.FlyToPlayer(_amountShield > 1);
            }

            if (_shieldFX) {
                if (_shieldActiveFX) {
                    _shieldActiveFX.gameObject.SetActive(true);
                    _shieldActiveFX.Play();
                }

                await UniTask.Delay(TimeSpan.FromSeconds(remoteConfig.Booster_Shield_InvincibleTime - 1),
                    cancellationToken: cancellationToken);

                if (_shieldActiveFX) {
                    _shieldActiveFX.gameObject.SetActive(false);
                }

                // warning before deactive shield
                if (_shieldPreDeactiveFX) {
                    _shieldPreDeactiveFX.gameObject.SetActive(true);
                    _shieldPreDeactiveFX.Play();
                }

                await UniTask.Delay(TimeSpan.FromSeconds(1f), cancellationToken: cancellationToken);
            } else {
                await UniTask.Delay(TimeSpan.FromSeconds(remoteConfig.Booster_Shield_InvincibleTime),
                    cancellationToken: cancellationToken);
            }

            TimeoutInvincible();
            if (_shieldBrokenFX) {
                _shieldBrokenFX.Play();
            }
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    private void TimeoutInvincible() {
        _isTriggeredInvincible = false;
        if (_shieldFX) {
            _shieldFX.gameObject.SetActive(false);
        }

        if (_shieldActiveFX) {
            _shieldActiveFX.gameObject.SetActive(false);
        }

        if (_shieldPreDeactiveFX) {
            _shieldPreDeactiveFX.gameObject.SetActive(false);
        }

        SoundManager.PlayBoosterTurnOff();
    }

    private void CompleteDeactiveInvincible() {
        _amountShield--;
        if (_amountShield <= 0 && _boosterAngle) {
            _boosterAngle.DeActive();
        }

        _isInActiveInvincible = false;
    }

    private void PlayInvincibleHitFx() {
        _invincibleHitFx.transform.position = transCache.position;
        _invincibleHitFx.Play();
        SoundManager.PlayBoosterShieldBaseActive();
    }
}