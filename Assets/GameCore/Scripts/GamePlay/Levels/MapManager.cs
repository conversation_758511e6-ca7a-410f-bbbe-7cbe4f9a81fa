using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GameCore.Scripts;
using GamePlay.Levels;
using Spine.Unity;
using UnityEngine;
using Debug = UnityEngine.Debug;
using Music.ACM;

public class MapManager : Singleton<MapManager> {
    [Serializable]
    public struct Environment {
        public ColorType            colorType;
        public NoteData.Mood        mood;
        public List<GameObject>     items;
        public List<ParticleSystem> Effects;
        public List<ParticleSystem> strongEffects;
    }

    #region Fields

    // ~~~~~~~~~~~~~~~~~~ static ~~~~~~~~~~~~~~~~~~
    public static event Action<NoteData.Mood, float> OnMoodChange;
    public static PlatformType                       MoodTile;

    // ~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~
    [SerializeField] private ParticleSystem[]  soundWave;
    [SerializeField] private List<Environment> _objEnvironments;

    [Tooltip("Hide gameobject when device is mid")] [SerializeField]
    private List<GameObject> objUnnecessaryEnvs;

    public bool[] disableRoadLine = new bool[4];

    [SerializeField] private List<ParticleSystem> allEffects;
    [SerializeField] private List<ParticleSystem> colorEffects;

    [SerializeField] private bool     fadeInEffect = true; // fade in/out when strongnote
    [SerializeField] private Material gridMaterial;

    /// <summary>
    /// Dùng để cached current mood -> dùng trong bài none musicalization -> sẽ tăng liên tục _currentMood và lặp lại
    /// </summary>
    [SerializeField, ReadOnly] private NoteData.Mood _currentMood;

    [Header("For Theme 33")] [SerializeField]
    private SkeletonGraphic rainbow;

    public SunController sun;

    [Space]

    // ~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~
    private List<PlatformData> _platformDatas;

    private GameLevels        _gameLevels;
    private Level             _currentLevel;
    private StageLevelManager _stageLevelManager = new StageLevelManager();

    private bool  _isGetLevelFromMidi;
    private float _deltaTime = 0.05f;

    private Coroutine _ieMoodchange;
    private float     _shortNoteDistance;

    private float _longNoteDuration;

    private float lastPositionZ = 0;

    /// <summary>
    /// dùng để cache index của note đầu trong stage.
    /// </summary>
    private int nearestStartStageIndex;

    /// <summary>
    /// Count số note trong 1 stage -> để sau kiểm tra lại, nếu quá ít- > hủy bỏ stage
    /// </summary>
    private int countNoteInStage;

    /// <summary>
    /// toggle kiểm tra có đang đếm số note trong stage hay không
    /// </summary>
    private bool isCountingNoteInStage;

    /// <summary>
    /// khoảng cách giữa 2 note -> dùng để check trong SKEW_STRAIGHT, SKEW_OPPOSITE, SKEW_SPIRAL
    /// </summary>
    private float diffTimeNote;

    /// <summary>
    /// dùng để check tính hợp lệ của chuỗi skew
    /// </summary>
    private bool acceptStageSkew;

    private Stage currentStage = Stage.NULL;

    private bool isDirty;
    
    #endregion

    #region Unity Methods

    protected override void Awake() {
        base.Awake();
        lastPositionZ = 0;
        Platform.OnHit += OnHitPlatForm;
    }

    private void Start() {
        if (DeviceHelper.IsMedium()) {
            foreach (GameObject obj in objUnnecessaryEnvs) {
                if (obj != null) {
                    obj.SetActive(false);
                }
            }
        }
#if UNITY_EDITOR
        CheckValidAllParicleSystemInGame();
#endif

        if (NotesManager.IsExistUpsideDown()) {
            SetupReverseTile();
        }
    }

    protected override void OnDestroy() {
        base.OnDestroy();
        _ieMoodchange = null;
        OnMoodChange = null;
        Platform.OnHit -= OnHitPlatForm;
    }

#if UNITY_EDITOR
    private void LateUpdate() {
        if (Input.GetKeyDown(KeyCode.F5)) {
            FadeMood();
        }
    }
#endif

    #endregion

    public void FadeMood() {
        _currentMood =
            (NoteData.Mood) (((int) _currentMood + 1) % Enum.GetNames(typeof(NoteData.Mood)).Length); //  tăng mood
        if (_ieMoodchange != null) {
            StopCoroutine(_ieMoodchange);
        }

        Spawner.s.StopAllCoroutines();
        float transitionDuration =  ImproveExperienceTheme.isInstanced? ImproveExperienceTheme.instance.GetTransitionDuration() : RemoteConfig.instance.Musicalization_MoodChange_TransitionTime;
        _ieMoodchange = StartCoroutine(MoodChange(_currentMood, 0f, transitionDuration));
    }

    public void ResetMood(float transitionDuration) {
        if (_currentMood == 0) {
            return;
        }

        _currentMood = 0;
        if (_ieMoodchange != null) {
            StopCoroutine(_ieMoodchange);
        }

        Spawner.s.StopAllCoroutines();
        _ieMoodchange = StartCoroutine(MoodChange(_currentMood, 0f, transitionDuration));
    }

    public void Init(float shortNoteDistance, float longNoteDuration) {
        _longNoteDuration = longNoteDuration;
        _shortNoteDistance = shortNoteDistance;

        if (_currentLevel == null) {
            _isGetLevelFromMidi = false;
            //~~~~~~~~~~ GameLevels from RemoteConfig ~~~~~~~~~~
            if (_gameLevels?.levels == null || _gameLevels.levels.Count == 0) {
                FetchGameLevels();
            }

            int currentIndexLevel = Configuration.GetGameLevel();
            _currentLevel = GetLevel(currentIndexLevel);
        }

        _stageLevelManager.Init(_currentLevel);
        CreatePlatformDatas(true);

        MoodTile = (PlatformType)RemoteConfig.instance.GetMusicalization_MoodChange_ColorTileId();
        SetFirstPositionZ(0);
    }

    private NoteData _lastNoteData;
    private int      trySkinNote;

    public void CreatePlatformDatas(bool isFirstTime) {
        if (Spawner.s.IsUseLongNoteV2) {
            Spawner.s.LongNoteConfig.PickSet(NotesManager.instance.usingCombinableLongTile);
        }

        if (_platformDatas != null && _platformDatas.Count > 0 && !isFirstTime) {
            _lastNoteData = _platformDatas[^1].currNoteObj;
        }

        _platformDatas = new List<PlatformData>();
        int tileTotalFront = RemoteConfig.instance.GetTileTotalFront();
        NotesManager.instance.RestoreDurationAfterChange();
        List<NoteData> noteDatas = NotesManager.instance.GetNoteDatas();

        trySkinNote = CheckTileTrySkin(noteDatas);
        TopBar.instance.HideTrySkinImage();

        for (int currNoteId = 0; currNoteId < noteDatas.Count; currNoteId++) {
            PlatformData platformData =
                GetPlatformData(currNoteId, _shortNoteDistance, tileTotalFront, noteDatas, isFirstTime);
            _platformDatas.Add(platformData);

            if (Spawner.s.platformManager.CanTrySkin(ref platformData)) {
                TopBar.instance.ShowTrySkinImage(GameItems.instance.tryBallId,
                    platformData.currentIndexNote * 1f / noteDatas.Count);
            }
        }

        if (!isFirstTime) {
            Platform platform = Spawner.s.platformManager.GetPlatform(_lastNoteData);
            if (platform != null) {
                platform.nextNoteDistance = _platformDatas[0].currNoteObj.distance;
            }
        }
    }

    private int CheckTileTrySkin(List<NoteData> noteDatas) {
        if (!TrySkinFeature.IsActive) return -1;
        float trySkinPercentAppear = RemoteConfigBase.instance.TrySkin_PercentAppear *
            noteDatas[NotesManager.instance.noteCount - 1].timeAppear / 100f;

        int tempIndex = -1;
        for (int noteId = 0; noteId < noteDatas.Count - 2; noteId++) {
            if (noteId > 0 && noteDatas[noteId].timeAppear > trySkinPercentAppear) {
                tempIndex = noteId - 1;
                break;
            }
        }

        for (int i = tempIndex; i >= 0; i--) {
            if (noteDatas[i].duration > _longNoteDuration) { // dont show in long note
                continue;
            }
            return i;
        }

        return -1;
    }

    public void PrepareMoodChange(PlatformData platformData, bool isChangeDifficultly = false) {
        if (!IsMoodChange(ref platformData) && !isChangeDifficultly) {
            return;
        }

        float transitionDuration =  ImproveExperienceTheme.isInstanced? ImproveExperienceTheme.instance.GetTransitionDuration() : RemoteConfigBase.instance.Musicalization_MoodChange_TransitionTime;
        float delay = platformData.nextNoteObj.distance;

        if (RemoteConfigBase.instance.GetMusicalization_MoodChange_ColorTileId() == 0) {
            delay -= RemoteConfigBase.instance.Musicalization_MoodChange_TimeModify;
            if (delay < 0) {
                delay = 0;
            }
        } else {
            delay = 0;
        }

        if (_ieMoodchange != null) {
            StopCoroutine(_ieMoodchange);
        }

        if (Spawner.s.isMusicalizationType) {
            _currentMood = platformData.nextNoteObj.mood;
        } else {
            _currentMood =
                (NoteData.Mood)(((int)_currentMood + 1) % Enum.GetNames(typeof(NoteData.Mood)).Length); //  tăng mood
        }

        _ieMoodchange = StartCoroutine(MoodChange(_currentMood, delay, transitionDuration));
    }

    public NoteData.Mood GetCurrentMode() {
        return _currentMood;
    }
    
    public static bool IsMoodChange(ref PlatformData platformData) {
        if (platformData.currentIndexNote < 5) { // too early to change smooth
            return false;
        }

        if (!Spawner.s.isMusicalizationType && !NotesManager.instance.HasNewElements()) {
            return CheckMoodChangeOfNonMusicalizationType(ref platformData);
        } else if (RemoteConfig.instance.Musicalization_MoodChange_IsEnable) {
            if (platformData.currNoteObj.mood != platformData.nextNoteObj.mood) {
                return true;
            }
        }

        return false;
    }

    private static bool CheckMoodChangeOfNonMusicalizationType(ref PlatformData platformData) {
        //TH-1069: các bài hát không phải musicalization cũng có mood change tại end stage
        if (platformData.isEndStage) {
            // Nếu note quá gần note trước ->> thì note sau sẽ trở thành note full mood để tránh xen vào giữa các note liên tiếp
            return platformData.distanceType != PlatformData.DistanceType.VeryShort;
        }

        if (platformData.IsShortDistance()) {
            return false;
        }

        // Kiểm tra có phải note thay thế note endstage không
        int tempIndex = platformData.currentIndexNote - 1;
        while (tempIndex > 0) {
            PlatformData tempPlatformData = instance._platformDatas[tempIndex % instance._platformDatas.Count];
            // Nếu note phía trước k short ->> nó đã là note full mood rồi
            if (!tempPlatformData.IsShortDistance()) {
                break;
            }

            // Nếu gặp note endstage mà chưa bị dừng ->> note này trở thành full mood
            //Debug.LogError($"FULL MOOD {platformData.currentIndexNote} thay {tempIndex}");
            if (tempPlatformData.isEndStage) {
                return true;
            }

            tempIndex--;
        }

        return false;
    }

    /// <summary>
    /// xử lý phần moodchange
    /// </summary>
    /// <param name="mood"></param>
    /// <param name="skinIndex"></param>
    /// <param name="delay"></param>
    /// <param name="transitionDuration"></param>
    public IEnumerator MoodChange(NoteData.Mood mood, float delay = 0, float transitionDuration = 0) {
        int forceSkinSet = Configuration.instance.Admin_ForceSkinSet;
        if (0 <= forceSkinSet && forceSkinSet <= 3) {
            mood = (NoteData.Mood)forceSkinSet;
        }

        _currentMood = mood;

        //Delay mood change to sync music
        if (delay > 0) {
            yield return new WaitForSeconds(delay);
        }
        
        //Hide effect
        int moodIndex = -1;
        foreach (var env in _objEnvironments) {
            moodIndex++;
            if (env.mood == mood)
                break;
        }

        foreach (var env in _objEnvironments) {
            foreach (GameObject obj in env.items) {
                if (obj.activeSelf) {
                    bool keepPlay = false;
                    foreach (GameObject _obj in _objEnvironments[moodIndex].items) {
                        if (obj == _obj) {
                            keepPlay = true;
                            break;
                        }
                    }

                    obj.SetActive(keepPlay);
                }
            }
        }

        //Appear effect
        if (!DeviceHelper.IsLowEnd()) {
            foreach (var env in _objEnvironments) {
                if (env.mood == mood) {
                    //Appear effect
                    foreach (GameObject obj in env.items) {
                        obj.SetActive(true);
                    }
                }
            }

            StartCoroutine(IEFadeEffect(transitionDuration, fadeInEffect, true));
        }

        OnMoodChange?.Invoke(mood, transitionDuration);
    }

    public int GetNextSkinId(NoteData.Mood mood) {
        //get next mood skin
        int indexSkin = GetSkinId(mood);
        //Check to make sure that content does not have duplicate skin mood
        int currenSkinId = Spawner.s.currentIndexSkin;
        if (currenSkinId == indexSkin) {
            indexSkin = indexSkin + 1 > Spawner.s.skinSet.Length ? 0 : indexSkin + 1;
        }

        return indexSkin;
    }

    public byte GetSkinId(NoteData.Mood mood) {
        var colorType = (from env in _objEnvironments where env.mood == mood select env.colorType).FirstOrDefault();
        byte index = 0;
        foreach (var item in Spawner.s.skinSet) {
            if (item.colorType == colorType) {
                return index;
            }

            index++;
        }

        if (Application.isEditor) {
            Logger.LogError("[GetSkinId] missing " + colorType);
        } else {
            Logger.LogWarning("[GetSkinId] missing " + colorType);
        }

        return 0;
    }

    private PlatformData GetPlatformData(int currNoteId, float shortNoteDistance, int tileTotalFront,
        List<NoteData> noteDatas, bool isFirstTime) {
        PlatformData platformData = new PlatformData {
            currentIndexNote = currNoteId
        };

        int nextNoteId = currNoteId + 1;
        if (nextNoteId >= noteDatas.Count) {
            nextNoteId = 0;
        }

        int beforeNoteId = currNoteId - 1;
        if (beforeNoteId < 0) {
            beforeNoteId = noteDatas.Count - 1;
        }

        platformData.beforeNoteObj = noteDatas[beforeNoteId];
        platformData.currNoteObj = noteDatas[currNoteId];
        platformData.nextNoteObj = noteDatas[nextNoteId];

        //~~~~~~~~~~~~~~~~~~~~ positionZ ~~~~~~~~~~~~~~~~~~~~
        if (currNoteId == 0) { //first note
            //lastPositionZ = 0;
            platformData.positionZ = lastPositionZ;
        } else {
            PlatformData data = _platformDatas[currNoteId - 1];
            lastPositionZ = data.positionZ;
            var distance = platformData.currNoteObj.distance;
            if (NotesManager.instance.isMidiGenerateTile &&
                !platformData.beforeNoteObj.cachedNextLongNote.IsNullOrEmpty()) {
                foreach (var cachedLongNoteData in platformData.beforeNoteObj.cachedNextLongNote) {
                    distance += cachedLongNoteData.Distance;
                }
            }

            platformData.positionZ = lastPositionZ + distance * Ball.b.GetBalLSpeed();
            //Debug.LogError($"Spawning {currNoteId} | Last pos {lastPositionZ}, dist {platformData.currNoteObj.distance}, pos {platformData.positionZ}");
        }

        //~~~~~~~~~~~~~~~~~~~~ minDuration ~~~~~~~~~~~~~~~~~~~~

        float minDistance = platformData.currNoteObj.distance;
        if (minDistance > platformData.nextNoteObj.distance) {
            minDistance = platformData.nextNoteObj.distance;
        }

        platformData.minDistance = minDistance;

        //~~~~~~~~~~~~~~~~~~~~ distance type ~~~~~~~~~~~~~~~~~~~~
        if (platformData.minDistance < shortNoteDistance) {
            platformData.distanceType = PlatformData.DistanceType.VeryShort;
        } else if (platformData.minDistance < NotesManager.instance.filterMaxTime && !NotesManager.instance.isBpm) {
            platformData.distanceType = PlatformData.DistanceType.Short;
        } else {
            platformData.distanceType = PlatformData.DistanceType.Long;
        }

        //~~~~~~~~~~~
        float duration = platformData.currNoteObj.duration;
        
        
        if (NotesManager.instance.isMidiGenerateTile || !Spawner.s.IsUseLongNoteV2) {//read by midi
            platformData.isLongDuration = duration >= _longNoteDuration;
            platformData.IsLongNote = platformData.isLongDuration;
        }
        else  {
            Spawner.s.LongNoteConfig.Process(ref platformData);
        }

        //~~~~~~~~~~~~~~~~~~~~~ fly time ~~~~~~~~~~~~~~~~~~~~~
        float flyTime = platformData.currNoteObj.distance * 2;
        if (tileTotalFront <= 0) {
            if (flyTime < 0.4f) {
                flyTime = 0;
            } else {
                float beforeFlyTime = platformData.beforeNoteObj.distance * 2;
                bool isNoFly = beforeFlyTime < 0.4f;
                if (isNoFly) {
                    flyTime = 0;
                }
            }
        }

        platformData.flyTime = flyTime;

        if (isFirstTime) {
            //~~~~~~~~~~~~~~~~~~~~~ stage ~~~~~~~~~~~~~~~~~~~~~
            platformData = UpdatePlatformData(platformData, false);
        }

        //~~~~~~~~~~~~~~~~~~~~~ strong note ~~~~~~~~~~~~~~~~~~~~~
        platformData.isStrongNote = platformData.currNoteObj.intensity == NoteData.Intensity.Strong;
        platformData.isInited = true;

        //~~~~~~~~~~~~~~~~~~~~~ environment ~~~~~~~~~~~~~~~~~~~~~
        //NoteData.Mood environmentType = GetEnvironmentTypeOfNote(platformData.currNoteObj.timeAppear);
        //platformData.environment = environmentType;

        //~~~~~~~~~~~~~~~~~~~~~ try skin ~~~~~~~~~~~~~~~~~~~~~
        if (currNoteId == trySkinNote) {
            platformData.canTrySkin = true;
        }

        //~~~~~~~~~~~~~~~~~~~~~ finished ~~~~~~~~~~~~~~~~~~~~~
        return platformData;
    }

    public void CancelSkewSpiral(int currentIndexNote) {
        int endNoteId = currentIndexNote + 100;
        int totalNote = _platformDatas.Count;
        // hủy bỏ toàn bộ các note trước nằm trong chuỗi
        for (int i = currentIndexNote; i < endNoteId; i++) {
            var platform = _platformDatas[i % totalNote];
            if (platform.stage == Stage.SKEW_SPIRAL) {
                platform.stage = Stage.NORMAL;
                platform.platformType = PlatformType.NORMAL;
                _platformDatas[i % totalNote] = platform;
            } else {
                break;
            }
        }
    }

    private Level GetLevel(int currentIndexLevel) {
        foreach (Level levels in _gameLevels.levels) {
            if (levels.index == currentIndexLevel) {
                return levels;
            }
        }

        //Level level = _gameLevels.levels[Random.Range(0, _gameLevels.levels.Count)];
        //nếu không có config cho level currentIndexLevel thì lấy level lớn nhất
        Level level = _gameLevels.levels[_gameLevels.levels.Count - 1];
        return level;
    }

    private void FetchGameLevels() {
        string gameLevels = RemoteConfigBase.instance.gameLevels;
        if (Configuration.instance.enableContentTool) {
            gameLevels = "{\"levels\":[{\"index\":0,\"stages\":[{\"name\":\"NORMAL\"}]}]}";
        }

        try {
            if (string.IsNullOrEmpty(gameLevels)) { //get default levels
                _gameLevels = GetDefaultGameLevels();
            } else {
                _gameLevels = JsonUtility.FromJson<GameLevels>(gameLevels);
            }
        }
        catch (Exception e) {
            _gameLevels = GetDefaultGameLevels();

            Debug.LogError(e.Message);
        }

        if (RemoteConfig.instance.gameLevelShuffleStage) {
            _gameLevels.ShuffleStage();
        }
    }

    private GameLevels GetDefaultGameLevels() {
        Level level0 = new Level {
            index = 0,
            stages = new List<StageLevel> {
                GetDefaultStageLevel(),
            }
        };

        Level level1 = new Level {
            index = 1,
            stages = new List<StageLevel> {
                GetDefaultStageLevel(),
                new StageLevel() { name = Stage.MOVING_TILE.ToString() }
            }
        };
        Level level2 = new Level {
            index = 2,
            stages = new List<StageLevel> {
                GetDefaultStageLevel(),
                new StageLevel() { name = Stage.MOVING_TILE.ToString() }
            }
        };
        Level level3 = new Level {
            index = 3,
            stages = new List<StageLevel> {
                GetDefaultStageLevel(),
                new StageLevel() { name = Stage.MOVING_TILE.ToString() }
            }
        };
        Level level4 = new Level {
            index = 4,
            stages = new List<StageLevel> {
                GetDefaultStageLevel(),
                new StageLevel() { name = Stage.FAKE_TILE.ToString() },
                new StageLevel() { name = Stage.MOVING_TILE.ToString() }
            }
        };

        Level level5 = new Level {
            index = 5,
            stages = new List<StageLevel> {
                new StageLevel() {
                    name = Stage.SKEW_STRAIGHT.ToString(),
                    minSpanTime = 0.3f,
                    minDeltaTime = 0.2f,
                    maxDeltaTime = 0.5f,
                    maxDiffTime = 0.15f,
                    skewAngle = 45,
                    minNote = 3,
                    rangeNote = new int[] { 5, 10 }
                },
                new StageLevel() {
                    name = Stage.SKEW_OPPOSITE.ToString(),
                    minSpanTime = 0.3f,
                    minDeltaTime = 0.3f,
                    maxDeltaTime = 0.6f,
                    maxDiffTime = 0.15f,
                    skewAngle = 45,
                    minNote = 3,
                    rangeNote = new int[] { 5, 10 }
                },
                new StageLevel() {
                    name = Stage.SKEW_SPIRAL.ToString(),
                    totalNote = 10,
                    minDeltaTime = 0,
                    maxDeltaTime = 0.4f,
                    maxDiffTime = 0.2f,
                    forceAutoJump = true,
                    spiralHeight = 15
                }
            }
        };

        GameLevels gameLevels = new GameLevels {
            levels = new List<Level> {
                level0,
                level1,
                level2,
                level3,
                level4,
                level5
            }
        };
        return gameLevels;
    }

    public StageLevel GetDefaultStageLevel() {
        return new StageLevel() { name = Stage.NORMAL.ToString() };
    }

    public PlatformData GetPlatformData(int idPlatform) {
        if (idPlatform.IsInRangeNumber(0, _platformDatas.Count - 1)) {
            return _platformDatas[idPlatform];
        }

        return new PlatformData();
    }

    public void SetPlatformData(int idPlatform, PlatformData data) {
        if (idPlatform.IsInRangeNumber(0, _platformDatas.Count - 1)) {
            _platformDatas[idPlatform] = data;
        }
    }

    public PlatformData GetPlatformDataByTime(float time) {
        foreach (PlatformData platformData in _platformDatas) {
            if (Math.Abs(platformData.currNoteObj.timeAppear - time) < 0.1) {
                return platformData;
            }

            if (platformData.currNoteObj.timeAppear - time > 1) {
                break;
            }
        }

        //return null;
        return new PlatformData();
    }

    public int GetTotalPlatform() {
        return _platformDatas.Count;
    }

    [HideInInspector] public bool               convertingData;
    [HideInInspector] public List<PlatformData> platformDataTemp;

    public void RevertData() {
        bool changeToMidi = NotesManager.instance.ChangeToMidi();
        if (changeToMidi) {
            _platformDatas = platformDataTemp;
            Spawner.s.ChangeNoteDatas();
        }
    }

    public void IncreaseEndlessCount(int nextEndlessModeCount) {
        PlatformData lastPlatformOfPreRound = _platformDatas[^1];
        if (RemoteConfigBase.instance.NotesDifficult_IsEnable &&
            nextEndlessModeCount == RemoteConfigBase.instance.NotesDifficult_Endless_CountChangeMidi) {
            bool changeToBpm = NotesManager.instance.ChangeToBpm();
            if (changeToBpm) {
                convertingData = true;
                platformDataTemp = _platformDatas;
                CreatePlatformDatas(false);
                Spawner.s.ChangeNoteDatas();
            }
        }

        for (int i = 0; i < _platformDatas.Count; i++) {
            PlatformData platformData = _platformDatas[i];

            //~~~~~~~~~~~~~~~~~~~~~ update Z ~~~~~~~~~~~~~~~~~~~~~
            PlatformData lastPlatform = i == 0 ? lastPlatformOfPreRound : _platformDatas[i - 1];
            float lastZ = lastPlatform.positionZ;
            
            var distance = platformData.currNoteObj.distance;
            if (NotesManager.instance.isMidiGenerateTile &&
                !platformData.beforeNoteObj.cachedNextLongNote.IsNullOrEmpty()) {
                foreach (var cachedLongNoteData in platformData.beforeNoteObj.cachedNextLongNote) {
                    distance += cachedLongNoteData.Distance;
                }
            }
            platformData.positionZ = lastZ + distance * Ball.b.GetBalLSpeed();
            int currNoteId = lastPlatform.currentIndexNote + 1;
            platformData.currentIndexNote = currNoteId;

            //~~~~~~~~~~~~~~~~~~~~~ stage ~~~~~~~~~~~~~~~~~~~~~
            if (!_isGetLevelFromMidi) {
                _platformDatas[i] = UpdatePlatformData(platformData, true);
            }
        }
    }

    private PlatformData UpdatePlatformData(PlatformData platformData, bool isEndless) {
        int indexNote = platformData.currentIndexNote;
        PlatformType platformType = PlatformType.NORMAL;
        if (NotesManager.instance.isMidiGenerateTile && !NotesManager.instance.HasElementType()) {
            NoteData noteData = NotesManager.instance.noteDatas[indexNote % NotesManager.instance.noteDatas.Count];
            if (noteData.fakeTile != NoteData.FakeTile.NONE) { // fake tile
                if (currentStage != Stage.FAKE_TILE) {
                    if (currentStage != Stage.NULL) {
                        var prevPlatformData = _platformDatas[(indexNote - 1) % _platformDatas.Count];
                        prevPlatformData.isEndStage = true;
                        _platformDatas[(indexNote - 1) % _platformDatas.Count] = prevPlatformData;
                    }

                    platformData.isStartStage = true;
                    currentStage = Stage.FAKE_TILE;
                }

                platformType = PlatformType.FAKE;
                platformData.stage = Stage.FAKE_TILE;

                platformData.fakeTile = noteData.ValidateFakeTile();
            } else if (noteData.moving != NoteData.Moving.None) { // fake moving
                platformType = PlatformType.MOVING;
                platformData.stage = Stage.MOVING_TILE;
                platformData.movingType = (int) noteData.moving;
                if (currentStage != Stage.MOVING_TILE) {
                    if (currentStage != Stage.NULL) {
                        var prevPlatformData = _platformDatas[(indexNote - 1) % _platformDatas.Count];
                        prevPlatformData.isEndStage = true;
                        _platformDatas[(indexNote - 1) % _platformDatas.Count] = prevPlatformData;
                    }

                    platformData.isStartStage = true;
                    currentStage = Stage.MOVING_TILE;
                }
            } else {
                if (currentStage != Stage.NORMAL) {
                    if (currentStage != Stage.NULL) {
                        var prevPlatformData = _platformDatas[(indexNote - 1) % _platformDatas.Count];
                        prevPlatformData.isEndStage = true;
                        _platformDatas[(indexNote - 1) % _platformDatas.Count] = prevPlatformData;
                    }

                    currentStage = Stage.NORMAL;
                }

                //normal
                platformType = PlatformType.NORMAL;
                platformData.stage = Stage.NORMAL;
            }
        } else if (NotesManager.instance.IsFollowSpecialTileRule()) {
            platformType = PlatformType.NORMAL; //don't remove this
            platformData.stage = Stage.NORMAL;
            platformData.isStartStage = false;
            platformData.isEndStage = false;

            platformData.stageNameType = String.Empty;
            platformData.movingType = 0;
        } else {
            if (_stageLevelManager != null) {
                platformType = _stageLevelManager.GetPlatformType(indexNote); //don't remove this
                var stage = _stageLevelManager.GetCurrentStage();
                if (stage == Stage.FAKE_TILE && !ApproveFakeTileStage()) {
                    stage = Stage.NORMAL;
                    platformType = PlatformType.NORMAL;
                }else if (stage == Stage.MOVING_TILE && !ApproveMovingTileStage()) {
                    stage = Stage.NORMAL;
                    platformType = PlatformType.NORMAL;
                }
                platformData.stage = stage;
                platformData.isStartStage = indexNote == _stageLevelManager.GetIndexNoteStageStart();
                platformData.isEndStage = indexNote == _stageLevelManager.GetIndexNoteStageEnd();
                platformData.stageNameType = _stageLevelManager.GetStageType();
            }

            int.TryParse(platformData.stageNameType, out int movingType);
            platformData.movingType = movingType;
        }

        platformData.platformType = platformType;
        if (platformData.isStartStage) {
            nearestStartStageIndex = indexNote;
            acceptStageSkew = true;
            countNoteInStage = 0;
            isCountingNoteInStage = false;
        }

        if (_stageLevelManager == null) {
            return platformData;
        }

        switch (platformData.stage) {
            case Stage.SKEW_STRAIGHT:
            case Stage.SKEW_OPPOSITE:
                bool isLastNote1 = (indexNote == NoteData.NoteIDLastNote ||
                                    indexNote == NoteData.NoteIDLastNoteForHuman);
                bool isFullMood1 = RemoteConfigBase.instance.GetMusicalization_MoodChange_ColorTileId() > 0 &&
                                   IsMoodChange(ref platformData);
                float minSpanTime = _stageLevelManager.MinSpanTime;
                float minTime1 = _stageLevelManager.MinDeltaTime;
                float maxTime1 = _stageLevelManager.MaxDeltaTime;
                float maxDiffTime1 = _stageLevelManager.MaxDiffTime;

                if (!isCountingNoteInStage) { // chưa bước vào stage
                    diffTimeNote =
                        platformData.nextNoteObj.distance; // lấy khoảng cách (ô đầu tới ô thứ 2) làm chuẩn thước đo
                    if (platformData.currNoteObj.distance < minSpanTime || isFullMood1 || isLastNote1 ||
                        platformData.isEndStage || diffTimeNote < minTime1 || diffTimeNote > maxTime1) {
                        // quá gần, khối FULL, khối Last, khối EndStage, hoặc ô tiếp theo quá gần/xa -> bỏ qua
                        platformData.platformType = PlatformType.NORMAL;
                    } else {
                        // thỏa mã điều kiện vào stage
                        platformData.platformType = platformType;
                        isCountingNoteInStage = true;
                        countNoteInStage = 1;
                    }
                } else { // trong stage
                    if (isFullMood1 || isLastNote1 ||
                        (platformData.stage == Stage.SKEW_STRAIGHT &&
                         (platformData.currNoteObj.distance - diffTimeNote) >
                         maxDiffTime1) // skew traight -> khối quá xa 
                        || (platformData.stage == Stage.SKEW_OPPOSITE &&
                            (Mathf.Abs(platformData.currNoteObj.distance - diffTimeNote) >
                             maxDiffTime1)) // skew opposite -> khối quá xa hoặc quá gần
                       ) {
                        // các khối đặc biệt hoặc khoảng cách quá xa so với note trước -> dừng stage
                        platformData.platformType = PlatformType.NORMAL;
                        isCountingNoteInStage = false;

                        int totalCount = _platformDatas.Count;

                        // giới hạn điều kiện kết thúc stage : không được quá sát note tiếp theo
                        for (int i = countNoteInStage; i >= 1; i--) {
                            var prevPlatform = _platformDatas[(indexNote - i) % totalCount];
                            if (prevPlatform.nextNoteObj.distance < minSpanTime) {
                                prevPlatform.platformType = PlatformType.NORMAL;
                                _platformDatas[(indexNote - i) % totalCount] = prevPlatform;
                                countNoteInStage--;
                            } else {
                                break;
                            }
                        }

                        if (countNoteInStage >= _stageLevelManager.MinNote) { // tối thiểu là min note đã đạt được
                            // giữ lại stage
                        } else {
                            // hủy bỏ stage
                            for (int i = 1; i <= countNoteInStage; i++) {
                                var prevPlatform = _platformDatas[(indexNote - i) % totalCount];
                                prevPlatform.platformType = PlatformType.NORMAL;
                                _platformDatas[(indexNote - i) % totalCount] = prevPlatform;
                            }
                        }
                    } else {
                        if (platformData.isEndStage && countNoteInStage < _stageLevelManager.MinNote - 1) {
                            platformData.platformType = PlatformType.NORMAL;
                            isCountingNoteInStage = false;
                            int totalCount = _platformDatas.Count;
                            for (int i = 1; i <= countNoteInStage; i++) {
                                var prevPlatform = _platformDatas[(indexNote - i) % totalCount];
                                prevPlatform.platformType = PlatformType.NORMAL;
                                _platformDatas[(indexNote - i) % totalCount] = prevPlatform;
                            }
                        } else {
                            platformData.platformType = platformType;
                            countNoteInStage++;
                        }
                    }
                }

                break;

            case Stage.SKEW_SPIRAL:

                bool isLastNote2 = (indexNote == NoteData.NoteIDLastNote ||
                                    indexNote == NoteData.NoteIDLastNoteForHuman);
                bool isFullMood2 = RemoteConfigBase.instance.GetMusicalization_MoodChange_ColorTileId() > 0 &&
                                   IsMoodChange(ref platformData);
                float minTime2 = _stageLevelManager.MinDeltaTime;
                float maxTime2 = _stageLevelManager.MaxDeltaTime;
                float maxDiffTime2 = _stageLevelManager.MaxDiffTime;
                bool isTooClose2 = platformData.currNoteObj.distance < minTime2;
                bool isTooFar2 = platformData.currNoteObj.distance > maxTime2;

                // Kiểm tra tính hợp lệ của chuỗi note
                if (platformData.isStartStage) {
                    diffTimeNote = platformData.nextNoteObj.distance;
                    if (diffTimeNote < minTime2 || diffTimeNote > maxTime2) {
                        acceptStageSkew = false;
                        platformData.platformType = PlatformType.NORMAL;
                    } else {
                        acceptStageSkew = true;
                        platformData.platformType = platformType;
                    }
                } else {
                    if (acceptStageSkew) {
                        if (isLastNote2 || isFullMood2 ||
                            Mathf.Abs(platformData.currNoteObj.distance - diffTimeNote) > maxDiffTime2) {
                            acceptStageSkew = false;
                            platformData.stage = Stage.NORMAL;
                            platformData.platformType = PlatformType.NORMAL;
                            //Debug.LogError($"Gặp khối {(isLastNote ? "LastNote" : "FullMood")} nên cancel cả chuỗi skew_spiral from {nearestStartStageIndex} to {indexNote}");

                            int totalNote = _platformDatas.Count;
                            // hủy bỏ toàn bộ các note trước nằm trong chuỗi
                            for (int i = nearestStartStageIndex; i < indexNote; i++) {
                                Debug.Log($"[{indexNote}] Hủy khối [{nearestStartStageIndex}] [{i}]");
                                var platform = _platformDatas[i % totalNote];
                                if (platform.stage == Stage.SKEW_SPIRAL) {
                                    platform.stage = Stage.NORMAL;
                                    platform.platformType = PlatformType.NORMAL;
                                    _platformDatas[i % totalNote] = platform;
                                    //Debug.LogError($"[{indexNote}] Hủy khối [{nearestStartStageIndex}] [{i}]");
                                }
                            }
                        } else {
                            // gán như thông thường
                            platformData.platformType = platformType;
                        }
                    } else {
                        platformData.stage = Stage.NORMAL;
                        platformData.platformType = PlatformType.NORMAL;
                    }
#if UNITY_EDITOR
                    if (acceptStageSkew && platformData.isEndStage) {
                        Debug.Log($"SKEW spiral {nearestStartStageIndex} -> {indexNote}");
                    }
#endif
                }

                break;

            case Stage.LATE_FAKE:
                //nếu là stage LATE_FAKE mà không đủ minDeltaTime thì chuyển type về NORMAL
                if (platformData.currNoteObj.distance <= _stageLevelManager.MinDeltaTime) {
                    platformData.platformType = PlatformType.NORMAL;
                } else {
                    platformData.platformType = platformType;
                }

                break;

            default:
                if (NotesManager.instance.isMidiGenerateTile) {
                    // Nếu là Midi Generate các tile ->> không thực hiện tối ưu hóa các tile
                    //break;
                } else {
                    //sau sẽ bỏ xử lý isShortDuration ở đây, sẽ xử lý ở bên ngoài cho từng stage cụ thể
                    var excludeWithFollowPitch = (platformData.stage != Stage.FAKE_TILE ||
                                                  !NotesManager.instance
                                                      .FakeTile_Follow_Pitch); //FakeTile_Follow_Pitch excluding
                    if (platformData.IsShortDistance() && platformData.stage != Stage.COLORING &&
                        platformData.stage != Stage.COLOR_CHOOSING && excludeWithFollowPitch) {
                        platformData.platformType = PlatformType.NORMAL;
                    } else if (!isEndless && RemoteConfig.instance.NotesDifficult_IsEnable &&
                               NotesManager.instance.Difficulty == NotesManager.Difficulties.Easy) {
                        platformData.platformType =
                            PlatformType.NORMAL; //Update 2022-10-07 remove fake/ moving tiles in easy mode
                    } else {
                        platformData.platformType = platformType;
                    }
                }

                break;
        }

        return platformData;
    }

    private bool ApproveMovingTileStage() {
        if (RemoteConfigBase.instance.NewElements_MovingTile_UsingGameStage)
            return true;

        return false;
    }

    private bool ApproveFakeTileStage() {
        if (RemoteConfigBase.instance.NewElements_FakeTile_UsingGameStage)
            return true;

        return false;
    }

    public float GetMinTimeStage() {
        return _stageLevelManager.MinDeltaTime;
    }

    public float GetMaxTimeStage() {
        return _stageLevelManager.MaxDeltaTime;
    }

    public float GetAngleOfTile() {
        return _stageLevelManager.SkewAngle;
    }

    private int cachedOffset;

    public (int offset, Vector3 position, Vector3 rotation) GetSkewSpiralPositionAndRotation(
        Platform realTile, bool isStartStage) {
        Vector3 newPosition = realTile.transCache.position;

        int totalTile = _stageLevelManager.TotalNoteInStage;
        float angle = 360 / (totalTile - 2); // -2 vì block free, block start và block cuối trùng nhau

        int offset;
        if (isStartStage) {
            offset = 0;
            cachedOffset = 0;
        } else {
            offset = cachedOffset + 1;
            cachedOffset = offset;
        }

        if (offset < 0 || offset > totalTile) {
            //Debug.LogError($"{offset} Out of range [0,{totalTile}]");
            offset = Mathf.Clamp(offset, 0, totalTile);
        }

        float curAngleDegree = 0;
        if (offset == 0) {
            // khối 0 là khối tự do, k ràng buộc gì
        } else if (offset == 1 || offset == totalTile) {
            // khối 1 và khối end -> đặt ở giữa để tạo vòng quay đẹp
            newPosition.x = 0;
        } else {
            curAngleDegree = angle * (offset - 1);
            float curAngleRadian = curAngleDegree * Mathf.Deg2Rad;
            float maxHeight = _stageLevelManager.SpiralHeight;
            newPosition.x = Mathf.Sin(curAngleRadian) * maxHeight / 2;
            newPosition.y = maxHeight / 2 * (1 - Mathf.Cos(curAngleRadian));
        }

        //Debug.LogError($"[{platformData.currentIndexNote}] [{offset}/{totalTile}] Angle {curAngleDegree} To Pos: {newPosition}");
        return (offset, newPosition, new Vector3(0, 0, curAngleDegree));
    }

    public bool IsForceMoveFollowStage() {
        return _stageLevelManager.ForceAutoJump;
    }

    public Vector3 CenterPosition() {
        return _stageLevelManager.CenterPosition;
    }

    public float GetTimeBetweenTile(int currentIndexNoteFrom, int currentIndexNoteTo) {
        int noteCount = _platformDatas.Count;
        int indexFrom = currentIndexNoteFrom % noteCount;
        int indexTo = currentIndexNoteTo % noteCount;

        int indexTarget = indexFrom > indexTo ? noteCount : indexTo;

        float totalTime = 0;
        for (int index = indexFrom; index < indexTarget; index++) {
            PlatformData p1 = GetPlatformData(index);
            PlatformData p2 = GetPlatformData(index + 1);

            float timeScale = GameController.enableEndless ? Ball.b.GetTimeScale(index) : 1;
            if (!p2.isInited) { //end of road
                totalTime += GetPlatformData(0).currNoteObj.distance / timeScale;
            } else {
                totalTime += (p2.currNoteObj.timeAppear - p1.currNoteObj.timeAppear) / timeScale;
            }

            if (indexTarget != indexTo && indexTo > 0 && !p2.isInited) { //calc first of road of new endless
                indexFrom = 0;
                indexTarget = indexTo;
                index = indexFrom - 1;
            }
        }

        return totalTime;
    }

    private void OnHitPlatForm(Platform block, int perfectCount) {
        if (!Spawner.s.onTransition && block.platformData.isStrongNote) {
            StartCoroutine(IEFadeEffect(0.5f, fadeInEffect));
            foreach (var env in _objEnvironments) {
                if (env.colorType == Spawner.s.skinSet[Spawner.s.currentIndexSkin].colorType) {
                    foreach (ParticleSystem eff in env.strongEffects) {
                        eff.Play();
                    }

                    break;
                }
            }
        }

        if (NotesManager.IsExistUpsideDown() && block.platformData.currNoteObj.elementType == NoteElementType.SpecialUpsideDown) {
            OnHitPlatForm_TileReverse();
        }
    }

    [HideInInspector] public bool isReversing;

    private IEnumerator IEFadeEffect(float duration = 0.5f, bool fadein = false, bool fadeAll = false) {
        float t = 0f;

        while (t < duration) {
            t += Time.deltaTime;

            if (!fadeAll)
                FadeAllEffect(t / duration, fadein);
            else
                FadeAllEffect(t / duration, fadein);

            yield return null;
        }
    }

    public void FadeEffect(float a, bool fadein = false) {
        foreach (var env in _objEnvironments) {
            if (env.colorType == Spawner.s.skinSet[Spawner.s.currentIndexSkin].colorType) {
                foreach (ParticleSystem eff in env.Effects) {
                    ChangeColor(eff, a, fadein);
                }

                break;
            }
        }
    }

    private void FadeAllEffect(float a, bool fadein = false) {
        foreach (var eff in allEffects) {
            if (!eff.gameObject.activeInHierarchy) continue;
            ChangeColor(eff, a, fadein, eff.startColor.a);
        }
    }

    ParticleSystem.Particle[] m_Particles;
    int                       numParticlesAlive;

    //Color startColor;
    private float _startAlpha;
    private float _endAlpha;
    private Color _tempColor;

    private void ChangeColor(ParticleSystem particles, float a, bool fadein = false, float opacity = 1f) {
        m_Particles = new ParticleSystem.Particle[particles.main.maxParticles];
        numParticlesAlive = particles.GetParticles(m_Particles);

        _startAlpha = fadein ? 0f : ((opacity == 0) ? 1 : opacity);
        _endAlpha = fadein ? ((opacity == 0) ? 1 : opacity) : 0f;

        // Change only the particles that are alive
        for (int j = 0; j < numParticlesAlive; j++) {
            //startColor = particles.startColor;
            _tempColor = m_Particles[j].startColor;
            _tempColor.a = Mathf.Lerp(_startAlpha, _endAlpha, a);
            m_Particles[j].startColor = _tempColor;
        }

        // Apply the particle changes to the Particle System
        particles.SetParticles(m_Particles, numParticlesAlive);
    }

    public IEnumerator ChangeStartColorEffect(Color start, Color end, float duration = 0.5f) {
        if (colorEffects == null || colorEffects.Count == 0) {
            yield break;
        }

        float t = 0;

        while (t < duration) {
            t += Time.deltaTime;
            Color c = Color.Lerp(start, end, t / duration);

            foreach (ParticleSystem eff in colorEffects) {
                //Set Opacity theo startcolor ban dau cua tung eff;
                c.a = Mathf.Lerp(0, eff.startColor.a, t / duration);

                m_Particles = new ParticleSystem.Particle[eff.main.maxParticles];
                int numParticlesAlive = eff.GetParticles(m_Particles);

                // Change only the particles that are alive
                for (int j = 0; j < numParticlesAlive; j++)
                    m_Particles[j].startColor = c;

                // Apply the particle changes to the Particle System
                eff.SetParticles(m_Particles, numParticlesAlive);
            }

            yield return null;
        }

        foreach (ParticleSystem eff in colorEffects) {
            float a = eff.startColor.a;
            end.a = a;
            eff.startColor = end;
        }
    }

    public IEnumerator ChangeGridColor(Color start, Color end, float duration = 0.5f) {
        if (gridMaterial != null) {
            float t = 0;
            float a = gridMaterial.GetColor("_TintColor").a;
            while (t < duration) {
                t += Time.deltaTime;
                Color c = Color.Lerp(start, end, t / duration);
                c.a = a;

                gridMaterial.SetColor("_TintColor", c);

                yield return null;
            }
        }
    }

    float                     high  = 10f;
    float                     plus  = 2f;
    int                       index = 0;
    ParticleSystem.MainModule main;

    public void UpdateSoundWave(float duration) {
        if (soundWave.Length == 0)
            return;

        index = (duration < RemoteConfig.instance.Musicalization_ShortNoteDistance) ? 1 : 0;

        for (int i = 0; i < soundWave.Length; i++)
            soundWave[i].gameObject.SetActive(i == index);

        high = Mathf.Clamp(2f / duration, 2f, 6f);
        plus = 3f * duration;
        if (high + plus > 7f) {
            plus = 7 - high;
        }

        plus = Mathf.Min(plus, 3f);

        main = soundWave[index].main;
        main.simulationSpeed = 0.5f / duration;
        main.startSizeY = new ParticleSystem.MinMaxCurve(high, high + plus);

        soundWave[index].Play();
    }

    public void OffSoundWave() {
        if (soundWave.Length == 0)
            return;

        for (int i = 0; i < soundWave.Length; i++)
            soundWave[i].gameObject.SetActive(false);
    }

    public void InitStageLevelManager() {
        _stageLevelManager.Init(_currentLevel);
        _lastNoteData = null;
    }

    public void SetFirstPositionZ(float positionZ) {
        lastPositionZ = positionZ;
    }

    string[] styleRainbow = new string[4] { "rainbow_4beat", "rainbow_2beat", "rainbow_2beat", "rainbow_1beat" };

    public void SetRainbowPosition(Transform parent, int skinIndex) {
        if (rainbow == null)
            return;

        rainbow.transform.SetParent(parent);
        rainbow.transform.SetAsFirstSibling();

        rainbow.AnimationState.SetAnimation(0, styleRainbow[skinIndex], true);
    }

    public ColorType GetColorTypeByMood(NoteData.Mood moodType) {
        foreach (var item in _objEnvironments) {
            if (item.mood == moodType) {
                return item.colorType;
            }
        }

        return _objEnvironments[0].colorType;
    }


    private void CheckValidAllParicleSystemInGame() {
        var particles = allEffects;
        //GameObject.FindObjectsByType<ParticleSystem>(FindObjectsInactive.Include, FindObjectsSortMode.None);
        foreach (var particle in particles) {
            if (particle.main.maxParticles >= 100) {
                Logger.EditorLogError($"[ {particle.gameObject.name} ] max particle: {particle.main.maxParticles}");
            }
        }
    }

    #region Reverse

    private BGReverseScript         _cgBGReverse;
    private TransitionReverseScript _transitionReverse;

    public void SetupReverseTile() {
        if (_cgBGReverse == null) {
            _cgBGReverse = ResourcesManager.LoadComponent<BGReverseScript>(ResourcesPath.SpecialTiles_BGReverse);
            _cgBGReverse.Init();
        }

        if (_transitionReverse == null) {
            _transitionReverse =
                ResourcesManager.LoadComponent<TransitionReverseScript>(ResourcesPath.SpecialTiles_TransitionReverse);
        }

        _transitionReverse.Init();
    }

    public void ShowTransitionReverse(float posZ) {
        if (_transitionReverse != null) {
            _transitionReverse.Show(posZ);
        }
    }

    public void ShowTransitionReverse(bool isShow) {
        if (_transitionReverse != null) {
            _transitionReverse.Show(isShow);
        }
    }

    private void OnHitPlatForm_TileReverse() {
        if (!isReversing) { //start
            TileReverseIn();

        } else { //end
            TileReverseOut();
        }
    }

    private void TileReverseIn() {
        float time = 1;
        isReversing = true;
        if (_cgBGReverse != null) {
            _cgBGReverse.SetAlpha(0);
            _cgBGReverse.DOFade(1, time);
        }
    }

    private void TileReverseOut() {
        float time = 1;
        isReversing = false;
        if (_cgBGReverse != null) {
            _cgBGReverse.DOFade(0, time);
        }
    }

    #endregion

    public void ForceSetReverse(bool inReverseMode) {
        if (inReverseMode) { //start
            TileReverseIn();
        } else { //end
            TileReverseOut();
        }

        if (_transitionReverse != null) {
            _transitionReverse.ForceSetReverse();
        }
    }
}