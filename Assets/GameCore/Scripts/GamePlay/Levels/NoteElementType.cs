public enum NoteElementType {
    None = 0,
    
    LongTile = 1,
    LongTileBreak = 2, // 1 đoạn tile dạng hộp dài, đi đến đâu vỡ đến đó để mở đường

    MovingTile = 3,    // tile moving ngang qua lại
    Teleport = 4,      // TH-2636: dạng moving tile, nhưng việc di chuyển là dứt kho<PERSON>t, và user cần đoán trước để nhảy cho chuẩn
    MovingCircle = 5,  // TH-2634: thiết kế dạng khác của moving tile, ví dụ như dạng tile quay vòng tròn

    Strong1 = 6,
    Strong2 = 7,
    Strong3 = 8,
    Strong4 = 9,

    FakeTile = 10,
    FakeThunder = 11,   // TH-2641: 1 tile trông bình thường nhưng có nắp đậy và hơi rung rung, đi vào sẽ bị dính đòn vfx --> thua luôn
    FakeConveyor = 12,  // TH-2640: tile dạng băng chuyền, có cả real tile và fake tile trên đó xen kẽ nhau di chuyển cắt ngang đường, cần chọn đúng vị trí để vượt qua
    FakeTransform = 13, // TH-2639: lúc đầu trông giống real tile, chuyển biến mờ dần, tới gần sẽ hiện rõ là fake tile
    FadeOut = 14,       // TH-2633: tile nhấp nháy thông báo vị trí từ xa, tới gần sẽ mờ dần r biến mất, user cần phải ghi nhớ
    FadeInOut = 15,     // TH-2635: tile nhấp nháy từ xa rồi biến mất, tới gần thì hiện ra
    Trap5 = 16,
    Trap6 = 17,

    MoodChange = 18,
    MoodChangeBroken = 19,  // TH-3081: Vào mood change sẽ có thêm hình ảnh, vfx xuất hiện kéo 1 khoảng tgian
    MoodChangeInstant = 20, // TH-3078: Mood change: tile cũ vỡ để tile mới xuất hiện, hoặc tile mới chạy từ xa về húc vỡ tile cũ ...
    MoodChangeOrder = 21,   // TH-3079: Mood change: tile cũ vỡ để tile mới xuất hiện, hoặc tile mới chạy từ xa về húc vỡ tile cũ ...
    MoodChangeTrain = 22,   // TH-3080: Mood change: tile cũ vỡ để tile mới xuất hiện, hoặc tile mới chạy từ xa về húc vỡ tile cũ ...

    SpecialUpsideDown = 23, // trạng thái ngược--> Đổi góc nhìn (đảo ngược)
    SpecialMirror = 24,     // nhảy vào strong note sẽ có mirror, vẫn chỉ là chơi 1 tay
    SpecialHyperBoost = 25, // item tên lửa, nếu ăn thì sẽ được bay 1 đoạn dài, đây là lúc user điều khiển hướng để ăn kim cương, khi hết tgian tồn tại của tên lửa, user cần chọn điểm rơi đúng tile để chơi tiếp
    SpecialZicZac = 26,     // ở các đoạn dài hoặc 1 khoảng mood, thiết kế khoảng nghỉ, user có thể điều khiến bóng để ăn item
    SpecialVoidWay = 27,    // Đi trong đường ống, ăn đúng nốt, né trap note
}

public static class NoteElementTypeExtensions {
    public static bool IsLongTileElement(this NoteElementType element) {
        switch (element) {
            case NoteElementType.LongTile:
            case NoteElementType.LongTileBreak:
                return true;
            default:
                return false;
        }
    }
    public static bool IsMoodChangeElement(this NoteElementType element) {
        switch (element) {
            case NoteElementType.MoodChange:
            case NoteElementType.MoodChangeBroken:
            case NoteElementType.MoodChangeInstant:
            case NoteElementType.MoodChangeOrder:
            case NoteElementType.MoodChangeTrain:
                return true;
            default:
                return false;
        }
    }
    
    public static bool IsSpecialSection(this NoteElementType element) {
        switch (element) {
            case NoteElementType.SpecialUpsideDown:
            case NoteElementType.SpecialMirror:
            case NoteElementType.SpecialHyperBoost:
            case NoteElementType.SpecialZicZac:
            case NoteElementType.SpecialVoidWay:
                return true;
            default:
                return false;
        }
    }
    public static bool IsFakeElement(this NoteElementType element) {
        switch (element) {
            case NoteElementType.FakeTile:
            case NoteElementType.FakeThunder:
            case NoteElementType.FakeConveyor:
            case NoteElementType.FakeTransform:
            case NoteElementType.FadeOut:
            case NoteElementType.FadeInOut:
            case NoteElementType.Trap5:
            case NoteElementType.Trap6:
                return true;
            default:
                return false;
        }
    }
    public static bool IsMovingElement(this NoteElementType element) {
        switch (element) {
            case NoteElementType.MovingTile:
            case NoteElementType.Teleport:
            case NoteElementType.MovingCircle:
                return true;
            default:
                return false;
        }
    }
    public static bool IsNeedResetAfterRevive(this NoteElementType element) {
        if (element.IsSpecialSection())
            return false;
        switch (element) {
            case NoteElementType.SpecialHyperBoost:
                return false;
            default:
                return true;
        }
    }
}