using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using Random = UnityEngine.Random;
using UnityEngine.Serialization;

namespace GamePlay.Levels {
    [Serializable]
    public class NoteData : Amanotes.ContentReader.NoteData {
        //static
        public static int NoteIDLastNote         = -1;
        public static int NoteIDLastNoteForHuman = -2;

        #region Enum

        public enum Timbre {
            None     = 0,
            Rhythmic = 1,
            Vocal    = 2,
            Melodic  = 3
        }

        public enum Pitch {
            None  = -1,
            Line1 = 0,
            Line2 = 1,
            Line3 = 2,
            Line4 = 3,
            Line5 = 4,
        }

        public enum Intensity {
            Weak,
            Strong,
        }

        public enum Phase {
            None                                         = 0,
            Intro                                        = 1, // C0
            Verse                                        = 2, // C_0
            [Tooltip("Pre-chorus / Build-up")] PreChorus = 3, // D0 == BuildUp
            [Tooltip("Chorus / Drop")]         Chorus    = 4, // D_0 == Drop

            Bridge = 5, // E0
            OutTro = 6, //F0
        }

        [Serializable]
        public enum Mood {
            Mild       = 0,
            Medium     = 1,
            Strong     = 2,
            VeryStrong = 3,
        }

        public enum Moving {
            None   = 0,
            Normal = 1,
            Snake  = 2,
            ZicZac = 3,
            Sync   = 4
        }

        [Flags]
        [Serializable]
        public enum FakeTile {
            NONE         = 0,
            LEFT         = 1, //line 1
            MIDDLE_LEFT  = 2, //line 2
            MIDDLE       = 4, //line 3
            MIDDLE_RIGHT = 8, //line 4
            RIGHT        = 16, //line 5
        }

        public enum NewAnimation {
            Normal  = 0,
            SpeedX2 = 1,
            SpeedX4 = 2
        }

        public enum OldAnimation {
            None = 0,
            C0   = 1,
            C_0  = 2,
            D0   = 3,
            D_0  = 4,
            E0   = 5,
            F0   = 6,
        }

      

        #endregion

        #region Fields

        public float              distance; //timeAppear current - timeAppear previous
        public Timbre             timbre;
        public Pitch              pitch;
        public Intensity          intensity;
        public Mood               mood;
        
        [NonSerialized] internal Moving   moving;
        [NonSerialized] internal FakeTile fakeTile;
        
        [NonSerialized] internal NewAnimation animation;
        [NonSerialized] internal float[]      haptics; //haptics
        
        [NonSerialized] internal LongNoteNodeData[] cachedNextLongNote;
        [NonSerialized] internal float              originalDuration;
        [NonSerialized] internal NoteElementType    elementType;

        [NonSerialized] internal Phase phase;

        #endregion

        #region Method
        public static List<NoteData> ConvertToInwaveNotes(List<Amanotes.ContentReader.NoteData> amaNotes) {
            List<NoteData> list = new List<NoteData>();
            foreach (Amanotes.ContentReader.NoteData note in amaNotes) {
                list.Add(ConvertToInwaveNote(note));
            }

            return list;
        }

        private static NoteData ConvertToInwaveNote(Amanotes.ContentReader.NoteData note) {
            NoteData inwaveNote = new NoteData();
            Type inwaveType = inwaveNote.GetType();
            FieldInfo[] fieldInfos = note.GetType().GetFields();
            
            foreach (FieldInfo fieldInfo in fieldInfos) {
                object value = fieldInfo.GetValue(note);
                FieldInfo fieldInwave = inwaveType.GetField(fieldInfo.Name);
                fieldInwave.SetValue(inwaveNote, value);
            }
            return inwaveNote;
        }

        public NoteData Clone() {
            return ConvertToInwaveNote(this);
        }

        public Pitch GetLineNumber() {
            float noPitchTime = RemoteConfig.instance.Musicalization_NoPitchTime;
            if (noPitchTime > 0 && timeAppear < noPitchTime) { //TH-386
                return Pitch.None;
            }

            switch (nodeID) {
                case (int)NoteID.C8:
                    return Pitch.Line1;

                case (int)NoteID.C_8:
                    return Pitch.Line2;

                case (int)NoteID.D8:
                    return Pitch.Line3;

                case (int)NoteID.D_8:
                    return Pitch.Line4;

                case (int)NoteID.E8:
                    return Pitch.Line5;

                default:
                    return Pitch.None;
            }
        }

        public static OldAnimation GetAnimation(int nodeID) {
            switch (nodeID) {
                case (int)NoteID.C0:
                    return OldAnimation.C0;
                case (int)NoteID.C_0:
                    return OldAnimation.C_0;
                case (int)NoteID.D0:
                    return OldAnimation.D0;
                case (int)NoteID.D_0:
                    return OldAnimation.D_0;
                case (int)NoteID.E0:
                    return OldAnimation.E0;
                case (int)NoteID.F0:
                    return OldAnimation.F0;

                default:
                    return OldAnimation.None;
            }
        }

        public static NewAnimation GetNewAnimation(int nodeID) {
            switch (nodeID) {
                case (int)NoteID.C0:
                case (int)NoteID.C_0:
                case (int)NoteID.D0:
                case (int)NoteID.D_0:
                case (int)NoteID.E0:
                case (int)NoteID.F0:
                    return NewAnimation.Normal;
                case (int)NoteID.C1:
                case (int)NoteID.C_1:
                case (int)NoteID.D1:
                case (int)NoteID.D_1:
                case (int)NoteID.E1:
                case (int)NoteID.F1:
                    return NewAnimation.SpeedX2;
                case (int)NoteID.C2:
                case (int)NoteID.C_2:
                case (int)NoteID.D2:
                case (int)NoteID.D_2:
                case (int)NoteID.E2:
                case (int)NoteID.F2:
                    return NewAnimation.SpeedX4;
                default:
                    return NewAnimation.Normal;
            }
        }

        public static Phase GetPharse(int nodeID) {
            switch (nodeID) {
                case (int)NoteID.C0:
                case (int)NoteID.C1:
                case (int)NoteID.C2:
                    return Phase.Intro;
                case (int)NoteID.C_0:
                case (int)NoteID.C_1:
                case (int)NoteID.C_2:
                    return Phase.Verse;
                case (int)NoteID.D0:
                case (int)NoteID.D1:
                case (int)NoteID.D2:
                    return Phase.PreChorus;
                case (int)NoteID.D_0:
                case (int)NoteID.D_1:
                case (int)NoteID.D_2:
                    return Phase.Chorus;
                case (int)NoteID.E0:
                case (int)NoteID.E1:
                case (int)NoteID.E2:
                    return Phase.Bridge;
                case (int)NoteID.F0:
                case (int)NoteID.F1:
                case (int)NoteID.F2:
                    return Phase.OutTro;
                default:
                    return Phase.None;
            }
        }

        public Mood GetMood() {
            if (velocity < 60) {
                return Mood.Mild;
            }

            if (60 <= velocity && velocity < 80) {
                return Mood.Medium;
            }

            if (80 <= velocity && velocity < 100) {
                return Mood.Strong;
            }

            if (100 <= velocity) {
                return Mood.VeryStrong;
            }

            return Mood.Mild;
        }

        public Mood GetOldMoodByAnimation(bool isSongType, Mood previousMood, bool isForceDifferent) {
            if (isSongType) {
                switch (phase) {
                    case Phase.Intro: return Mood.Mild;
                    case Phase.Verse: return Mood.Medium;
                    case Phase.PreChorus:
                        return Mood.Medium;
                    case Phase.Chorus:
                        //Strong & VeryStrong
                        if (isForceDifferent) {
                            if (previousMood == Mood.Strong) return Mood.VeryStrong;
                            else if (previousMood == Mood.VeryStrong) return Mood.Strong;
                        }

                        return Random.Range(0, 999) % 2 == 0 ? Mood.Strong : Mood.VeryStrong;
                    case Phase.Bridge:
                        return Mood.Mild;
                    case Phase.OutTro:
                        return Mood.Medium;
                }
            } else {
                switch (phase) {
                    case Phase.Intro:
                        //Mild & Medium
                        if (isForceDifferent) {
                            if (previousMood == Mood.Mild) return Mood.Medium;
                            else if (previousMood == Mood.Medium) return Mood.Mild;
                        }

                        return Random.Range(0, 999) % 2 == 0 ? Mood.Mild : Mood.Medium;
                    case Phase.Verse:
                        //Strong & VeryStrong
                        if (isForceDifferent) {
                            if (previousMood == Mood.Strong) return Mood.VeryStrong;
                            else if (previousMood == Mood.VeryStrong) return Mood.Strong;
                        }

                        return Random.Range(0, 999) % 2 == 0 ? Mood.Strong : Mood.VeryStrong;
                    case Phase.PreChorus: // Build-up
                        //Medium & Strong
                        if (isForceDifferent) {
                            if (previousMood == Mood.Medium) return Mood.Strong;
                            else if (previousMood == Mood.Strong) return Mood.Medium;
                        }

                        return Random.Range(0, 999) % 2 == 0 ? Mood.Medium : Mood.Strong;
                    case Phase.Chorus: // Drop
                        //Strong & VeryStrong
                        if (isForceDifferent) {
                            if (previousMood == Mood.Strong) return Mood.VeryStrong;
                            else if (previousMood == Mood.VeryStrong) return Mood.Strong;
                        }

                        return Random.Range(0, 999) % 2 == 0 ? Mood.Strong : Mood.VeryStrong;
                    case Phase.Bridge: // Breakdown
                        return Mood.Mild;
                    case Phase.OutTro:
                        //Medium & Strong
                        if (isForceDifferent) {
                            if (previousMood == Mood.Medium) return Mood.Strong;
                            else if (previousMood == Mood.Strong) return Mood.Medium;
                        }

                        return Random.Range(0, 999) % 2 == 0 ? Mood.Medium : Mood.Strong;
                }
            }

            Logger.EditorLogError($"Why get mood by velocity: {phase.ToString()}");
            return GetMood();
        }

        public Mood GetNewMoodByAnimation(bool isFirstChorus, Mood previousMood) {
            switch (phase) {
                case Phase.Intro:
                    return Mood.Mild;
                case Phase.Verse:
                    return Mood.Medium;
                case Phase.PreChorus:
                    if (previousMood == Mood.Mild) return Mood.Medium;
                    if (previousMood == Mood.Medium) return Mood.Mild;
                    return Random.value > 0.5f ? Mood.Medium : Mood.Mild;
                case Phase.Chorus:
                    //Strong & VeryStrong
                    if (isFirstChorus) {
                        return Mood.Strong;
                    } else {
                        return Mood.VeryStrong;
                    }
                case Phase.Bridge:
                    return Mood.Mild;
                case Phase.OutTro:
                    return Mood.Medium;
            }

            Logger.EditorLogError($"Why get mood by velocity: {phase.ToString()}");
            return GetMood();
        }

        #endregion

        #region LevelBot

        public static List<NoteData> ConvertToInwaveNotes(List<LevelBot.NoteData> genNotes) {
            List<NoteData> list = new List<NoteData>();
            foreach (LevelBot.NoteData note in genNotes) {
                list.Add(ConvertToInwaveNote(note));
            }

            return list;
        }

        private static NoteData ConvertToInwaveNote(LevelBot.NoteData noteLevelBot) {
            NoteData inwaveNote = new NoteData {
                timeAppear = noteLevelBot.Time,
                duration = noteLevelBot.Duration
            };

            return inwaveNote;
        }

        #endregion

        public static List<NoteData> CloneList(List<NoteData> genNotes) {
            List<NoteData> list = new List<NoteData>();
            foreach (NoteData note in genNotes) {
                list.Add(note.Clone());
            }

            return list;
        }

        public FakeTile ValidateFakeTile() {
            switch (pitch) {
                case Pitch.Line1:
                    RemoveFakeTileAtLine(FakeTile.LEFT);
                    RemoveFakeTileAtLine(FakeTile.MIDDLE_LEFT);
                    break;

                case Pitch.Line2:
                    RemoveFakeTileAtLine(FakeTile.LEFT);
                    RemoveFakeTileAtLine(FakeTile.MIDDLE_LEFT);
                    RemoveFakeTileAtLine(FakeTile.MIDDLE);
                    break;

                case Pitch.Line3:
                    RemoveFakeTileAtLine(FakeTile.MIDDLE_LEFT);
                    RemoveFakeTileAtLine(FakeTile.MIDDLE);
                    RemoveFakeTileAtLine(FakeTile.MIDDLE_RIGHT);
                    break;

                case Pitch.Line4:
                    RemoveFakeTileAtLine(FakeTile.MIDDLE);
                    RemoveFakeTileAtLine(FakeTile.MIDDLE_RIGHT);
                    RemoveFakeTileAtLine(FakeTile.RIGHT);
                    break;

                case Pitch.Line5:
                    RemoveFakeTileAtLine(FakeTile.MIDDLE_RIGHT);
                    RemoveFakeTileAtLine(FakeTile.RIGHT);
                    break;
            }

            void RemoveFakeTileAtLine(FakeTile line) {
                if ((fakeTile & line) == line) {
                    fakeTile &= ~line;
                }
            }

            return fakeTile;
        }
    }
}