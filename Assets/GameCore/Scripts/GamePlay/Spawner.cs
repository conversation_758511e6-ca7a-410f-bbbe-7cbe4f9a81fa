using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using GameCore.LiveEvent.MysteryDoor;
using GamePlay.Levels;
using Inwave;
using TilesHop.LiveEvent;
using TilesHop.LongNote;
using Debug = UnityEngine.Debug;
using Random = UnityEngine.Random;
using Music.ACM;
using TilesHop.Cores.Boosters;
using UnityEngine.U2D;
using Spline = Dreamteck.Splines.Spline;
using TilesHop.Cores.Pooling;
using TilesHop.LiveEvent.MilestoneEvent;
using UnityEngine.Pool;
using UnityEngine.UI;
using Utils = Music.ACM.Utils;

public class Spawner : MonoBehaviour, GameSubscriber {
    #region Fields

    public const float HYPERBOOST_DIAMOND_HEIGHT_BIAS = -0.5f;

    public delegate void BgChangeHandler(byte current, byte next, float duration);

    public static event BgChangeHandler onBgChange;

    public static event Action<int, SkinSet> OnChangeMood;
    public static event Action<float> OnStartMusic;
    public static Action<Platform> OnCompleteSpawnPlatform;

    public static Spawner s;
    public static int     jumpCount;

    //~~~~~~~~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~~~~~~~~
    public            GameObject trees;
    public            float      fadeOutTileDistance = 0;
    [ReadOnly] public byte       currentIndexSkin    = 0;
    public            SkinSet[]  skinSet;

    public Queue<Platform> mainBlockQueue = new();

    //bool _isLocalSong = false;
    public PlatformManager platformManager;

    public event Action<NoteData> OnLastNoteAppear;

    //~~~~~~~~~~~~~~~~~~~~~~~~~ HideInInspector ~~~~~~~~~~~~~~~~~~~~~~~~~
    [NonSerialized] public float   bpm;
    [NonSerialized] public int     indexSpawnNote = -1;
    [NonSerialized] public Vector3 blockPosition;
    [NonSerialized] public Vector3 blockScaleCurrent;
    [NonSerialized] public float   heightBlock;

    [NonSerialized] public SkinSet[] skinSetCustom;
    [NonSerialized] public bool      onTransition    = false;
    [NonSerialized] public int       themeId         = 0;
    [NonSerialized] public int       beforeIndexSkin = 0;
    [NonSerialized] public Platform  lastMovingObject;
    [NonSerialized] public float     treeMaxDistance;
    [NonSerialized] public bool      isMusicalizationType;
    [NonSerialized] public bool      isPitch;
    [NonSerialized] public int       currentJumpNoteID;
    [NonSerialized] public int       totalPerfectCount; //Only round 1
    [NonSerialized] public bool      isReloadDifficult;

    // ~~~~~~~~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~~~~~~~~
    private float   _prevPosX = 0;
    private Vector3 _originalBlockScale;
    private Vector3 _blockScale;

    private          List<TransitionImage>          _colorySprites        = ListPool<TransitionImage>.Get();
    private readonly Dictionary<Transform, Vector3> colorySpritesPosition = new Dictionary<Transform, Vector3>();

    private Coroutine _bgCoroutine;
    //private bool isNewChallenge = false;

    private int       _highScoreTrigger = 0;
    private int       _endlessTrigger   = 0;
    private List<int> _percentTrigger;
    private List<int> _starsTrigger;
    private List<int> _mileStoneTrigger;
    private List<int> _itemEventTrigger = new List<int>();

    private readonly string[] _percentStrings = {"20%", "40%", "60%", "80%"};
    private          float[]  _distanceMinMax1;
    private          float[]  _distanceMinMax2;
    private          float[]  _distanceMinMax3;

    [NonSerialized] public float[] distanceMinMax4;

    private float _tileSize;
    private float _tileMaxSize;
    private float _tileMinSize;

    private float[] _gridMap;
    private float[] _normalMap;
    private float[] _lineMap;

    private bool _canEarnDrill;
    private bool _canCollectMilestoneToken;

    private float     _localTreeLeftX;
    private float     _localTreeRightX;
    private Transform _tfGroupTreeLeft;
    private Transform _tfGroupTreeRight;

    private bool _isApplyLevelBotFilter = false; // Flag to reduce the difficulty of LevelBot Pitch Generation
    private List<Diamond> _listDiamond = ListPool<Diamond>.Get();
    [HideInInspector] public List<RhythmNote> _listRhythmNote = ListPool<RhythmNote>.Get();
    private float _tileMaxPositionX;

    private bool isLeftSkewTile;

    private const int MAXIMUM_COUNT_SKIN_TRY = 1;

    private int remainSkinTryInMap;

    private LongNoteConfig _longNoteConfig;

    private IngameSectionType CurrentSpecialSection     = IngameSectionType.Normal;
    private int               _indexStartSpecialSection = -1;
    private int               _indexEndSpecialSection   = -1;
    private HyperBoostType    _hyperBoostType           = HyperBoostType.music;

    public bool IsUseLongNoteV2 => remoteConfig.LongNote_v2_IsEnable && _longNoteConfig != null;
    public List<int> StarTriggers => _starsTrigger;

    public LongNoteConfig LongNoteConfig => _longNoteConfig;
    // ~~~~~~~~~~~~~~~~~~~~~~~~~ getter ~~~~~~~~~~~~~~~~~~~~~~~~~

    #region Getter

    private NotesManager notesManager => NotesManager.instance;
    private int noteCount => notesManager.noteCount;
    private float filterMiddleTime => notesManager.filterMiddleTime;
    private float filterMaxTime => notesManager.filterMaxTime;
    private List<NoteData> noteDatas => notesManager.noteDatas;
    private RemoteConfig remoteConfig => RemoteConfigBase.instance;

    private GameController _cacheGameController;

    private GameController gameController {
        get {
            if (!_cacheGameController) {
                _cacheGameController = SingletonManager.instance.GetSingleton<GameController>();
            }

            return _cacheGameController;
        }
    }

    public float[] LineMap => _lineMap;

    [NonSerialized] public bool isPauseSpawnTile;

    private MapManager _cacheMapManager;

    private MapManager mapManager {
        get {
            if (_cacheMapManager == null) {
                _cacheMapManager = MapManager.instance;
            }

            return _cacheMapManager;
        }
    }

    #endregion

    #endregion

    public event Action OnChangeJumpCount;

    #region Unity Methods

    private void Awake() {
        s = this;

        _distanceMinMax1 = RemoteConfig.GetFloatArray(remoteConfig.Distance_MinMax1, ':', new float[2] {1, 2});
        _distanceMinMax2 = RemoteConfig.GetFloatArray(remoteConfig.Distance_MinMax2, ':', new float[2] {1, 3});
        _distanceMinMax3 = RemoteConfig.GetFloatArray(remoteConfig.Distance_MinMax3, ':', new float[2] {1, 4});
        distanceMinMax4 = RemoteConfig.GetFloatArray(remoteConfig.Distance_MinMax4, ':', new float[2] {1, 5});

        //Normal Map
        _normalMap = new float[14] {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
        StartCoroutine(IEAwake());
    }

    private IEnumerator IEAwake() {
        WaitForEndOfFrame waitForEndOfFrame = YieldPool.GetWaitForEndOfFrame();
        while (NotesManager.instance == null) { // wait until NotesManager is initialized! -> to get current difficulty!
            yield return waitForEndOfFrame;
        }

        _tileSize = remoteConfig.GetTileSize() * remoteConfig.MapSize;
        _tileMaxSize = remoteConfig.GetTileMaxSize() * remoteConfig.MapSize;
        _tileMinSize = remoteConfig.GetTileMinSize() * remoteConfig.MapSize;

        _tileMaxPositionX = remoteConfig.GetTileMaxPositionX();

        // Grid Map
        _gridMap = new float[3] {-_tileMaxPositionX, 0, _tileMaxPositionX};
        //5 line map
        _lineMap = new[] {-_tileMaxPositionX, -_tileMaxPositionX / 2, 0, _tileMaxPositionX / 2, _tileMaxPositionX};

        while (Configuration.instance == null || SongManager.instance == null) {
            // wait until Configuration && SongManager is initialized! -> to get current song!
            yield return waitForEndOfFrame;
        }

        //get longnote config
        if (remoteConfig.LongNote_v2_IsEnable) {
            _longNoteConfig = Configuration.instance.GetLongNoteConfig();
        }

        // get current song
        Song song = SceneFader.instance.CurrentLoadingSong; //Configuration.instance.GetCurrentSong();
        isMusicalizationType = song.isMusicalizationType;

        int gameLevel = Configuration.GetGameLevel();
        if (remoteConfig.NewElements_OverLapOtherRule && gameLevel < remoteConfig.Musicalization_Pitch_FromLevel) {
            gameLevel = remoteConfig.Musicalization_Pitch_FromLevel;
        }

        isPitch = isMusicalizationType && remoteConfig.Musicalization_Pitch_IsEnable && !notesManager.usingRhythmTool &&
                  (notesManager.isMidiForceControl || gameLevel >= remoteConfig.Musicalization_Pitch_FromLevel ||
                   song.isTutorialSong || song.ForcePitchByDifficultyTag());

        //Get theme id
        themeId = ThemeManager.GetPlayingThemeId();

        bpm = song.bmp;

        if (Configuration.isAdmin) {
            bpm = PlayerPrefs.GetFloat(song.path + "bpm", bpm);
        }

        // isNewChallenge = CoreData.CheckNewChallenge() || remoteConfig.Unlock_NewChallenge <= 1;
        // if (notesManager.isDefaultSong && !_isLocalSong && remoteConfig.Unlock_NewChallenge > 0 &&
        //     !Configuration.instance.enableContentTool) {
        //     isNewChallenge = false;
        // }

        //Generate Platform
        platformManager.GeneratePlatform(true, song);

        //Update tile sprites
        if (skinSet[0].blockSprite == null) {
            for (byte i = 0; i < skinSet.Length; i++) {
                _tempSkinSet = skinSet[i];
                _tempSkinSet.blockSprite = _tempSkinSet.blockLarge;
                _tempSkinSet.psTexture = _tempSkinSet.strokeLarge;
                _tempSkinSet.ps2Texture = _tempSkinSet.effectLarge;

                skinSet[i] = _tempSkinSet;
            }
        }

        Platform firstItem = platformManager.GetDefaultPlatform();
        ThemeColor customColor = GetThemeColorFromScene();

        if (skinSetCustom == null || skinSetCustom.Length == 0) {
            int skinSetLength = skinSet.Length;
            skinSetCustom = new SkinSet[skinSetLength];
            for (int index = 0; index < skinSetLength; index++) {
                _tempSkinSet = skinSet[index];
                if (_tempSkinSet.bg != null) {
                    _tempSkinSet.bg.gameObject.SetActive(false);
                }

                if (_tempSkinSet.invironmentImages.Count > 0) {
                    for (int i = 0; i < _tempSkinSet.invironmentImages.Count; i++)
                        _tempSkinSet.invironmentImages[i].gameObject.SetActive(false);
                }

                _tempSkinSet.UpdateSkinSet(false, customColor, index);
                skinSet[index] = _tempSkinSet; //struct

                //skinSetCustom
                SkinSet skinCustom = _tempSkinSet;
                skinCustom.UpdateSkinSet(true, customColor, index);
                skinSetCustom[index] = skinCustom;
            }
        }

        _blockScale = firstItem.GetBlockSize();
        _originalBlockScale = _blockScale;
        if (remoteConfig.MapSize > 1) {
            _blockScale.y *= remoteConfig.MapSize;
        }

        float newZ = _blockScale.z * _tileSize / _blockScale.x;
        _blockScale = Util.SetPositionXZ(_blockScale, _tileSize, newZ);

        blockPosition = firstItem.transform.position;

        heightBlock = firstItem.GetHeightBlock(_blockScale.y);

        if (trees != null && (_colorySprites == null || _colorySprites.Count <= 0)) {
            Transform tfTrees = trees.transform;
            int treeCount = tfTrees.childCount;
            _colorySprites = ListPool<TransitionImage>.Get();
            colorySpritesPosition.Clear();

            for (int j = 0; j < treeCount; j++) {
                Transform child = tfTrees.GetChild(j);
                int childCount = child.childCount;
                for (int i = 0; i < childCount; i++) {
                    Transform item0 = child.GetChild(i);
                    if (item0 != null && item0.gameObject.activeInHierarchy) {
                        colorySpritesPosition.Add(item0, item0.position);

                        if (item0.TryGetComponent(out TransitionImage transitionImage)) {
                            _colorySprites.Add(transitionImage);
                        }

                        if (item0.position.z > treeMaxDistance) {
                            treeMaxDistance = item0.position.z;
                        }
                    }
                }
            }
        }

        if (_tfGroupTreeLeft != null && _tfGroupTreeRight != null) {
            _localTreeLeftX = _tfGroupTreeLeft.GetLocalX();
            _localTreeRightX = _tfGroupTreeRight.GetLocalX();
        }

        if (AnzuManager.isInstanced) {
            bool anzuOnBoarding = !notesManager.song.isTutorialSong || remoteConfig.OnboardingFlow_EnableAnzu;
            if (AnzuManager.instance.IsEnableAds(false) && !AnzuManager.instance.IsExcludeSongName(song) &&
                anzuOnBoarding) {
                AnzuManager.instance.InitAnzuAdv();
            } else {
                AnzuManager.instance.HideAnzuAdv();
            }
        }

        if (AdvertyManager.isInstanced) {
            bool anzuOnBoarding = !notesManager.song.isTutorialSong || remoteConfig.OnboardingFlow_EnableAnzu;
            if (AdvertyManager.instance.IsEnableAds() && !AdvertyManager.instance.IsExcludeSongName(song) &&
                anzuOnBoarding) {
                AdvertyManager.instance.InitAdv();
            } else {
                AdvertyManager.instance.DeactivateAdv();
            }
        }

        _isApplyLevelBotFilter = song.isMusicalizationLevelbot && remoteConfig.LevelBot_ApplyFilter;

        if (LiveEventManager.instance && LiveEventManager.instance.IsActiveEvent) {
            InitHatItem();
        }
    }

    private void Start() {
        gameController.addSubcriber(this);
        PosterManager.Init();
    }

    private void OnEnable() {
        MapManager.OnMoodChange += MapManager_OnMoodChange;
    }

    private void OnDisable() {
        MapManager.OnMoodChange -= MapManager_OnMoodChange;
    }

    private void OnDestroy() {
        onBgChange = null;
        ReleaseList(_percentTrigger);
        ReleaseList(_starsTrigger);
        ReleaseList(_mileStoneTrigger);
        ReleaseList(_listDiamond);
        ReleaseList(_listRhythmNote);
        ReleaseList(_colorySprites);

        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }

    private void ReleaseList<T>(List<T> list) {
        if (list != null) {
            ListPool<T>.Release(list);
        }
    }

    #endregion

    public Platform SpawnTile() {
        if (gameController.game == GameStatus.P_DIE) {
            return null;
        }

        if (isPauseSpawnTile) {
            return null;
        }

        int indexNote = ++indexSpawnNote;
        bool isValid = ValidateIndexNoteSpawn(ref indexNote);
        if (!isValid) {
            return null;
        }

        int currNoteId = indexNote % noteCount;

        PlatformData platformData = mapManager.GetPlatformData(currNoteId);
        PlatformData nextPlatformdata = mapManager.GetPlatformData(currNoteId + 1);

        bool isLastNote = platformData.currNoteObj.nodeID == NoteData.NoteIDLastNote ||
                          platformData.currNoteObj.nodeID == NoteData.NoteIDLastNoteForHuman;
        if (notesManager.IsForceNoteEnd && !isLastNote) {
            isLastNote |= platformData.currentIndexNote == notesManager.IdForceNoteEnd;
        }

        //TH-657 add full tile before mood change
        int moodTile = remoteConfig.GetMusicalization_MoodChange_ColorTileId();
        if (moodTile > 0 && !isLastNote && MapManager.IsMoodChange(ref platformData)) {
            platformData.platformType = MapManager.MoodTile;
        }

        // Platform luc bat dau / continue o dang Normal hoac full tiles
        PlatformType platformTypeOfNote = (gameController.game != GameStatus.LIVE &&
                                           platformData.platformType != PlatformType.MOOD_FULL)
            ? PlatformType.NORMAL
            : platformData.platformType;
        if (isLastNote) {
            platformTypeOfNote = PlatformType.NORMAL;
        }
        
        if (gameController.isUpdateSensitive) { //TH-807
            platformTypeOfNote = PlatformType.MOOD_FULL;
            platformData.platformType = platformTypeOfNote;
        }

        if (platformData.IsLongNote && !isLastNote && (isMusicalizationType || notesManager.increaseLongNoteByCode)) {
            int idTypeLongNote = (int) LongNoteType.None;
            bool isAdjusted = AdjustLongNoteDuration(currNoteId, ref platformData, ref nextPlatformdata);
            if (IsUseLongNoteV2) {
                if (isAdjusted) {
                    var breakType = _longNoteConfig.GetBreakType(platformData.currNoteObj.duration);
                    if (breakType != platformData.longNoteBreak) {
                        platformData.longNoteBreak = breakType;
                    }
                }

                idTypeLongNote =
                    _longNoteConfig.GetTypeLongNote(platformData.longNoteBreak, platformData.currNoteObj.velocity);
            } else {
                idTypeLongNote = remoteConfig.LongTile_IndexLine;
            }

            if (idTypeLongNote == (int) LongNoteType.None) {
                platformTypeOfNote = PlatformType.NORMAL;
                platformData.longNoteType = LongNoteType.None;
            } else {
                platformData.longNoteType = (LongNoteType) idTypeLongNote;
                switch (idTypeLongNote) {
                    case (int) LongNoteType.LongTile:
                        platformTypeOfNote = PlatformType.LONG_TILE;
                        platformData.isStrongNote = false;
                        break;

                    case (int) LongNoteType.Highway:
                        platformTypeOfNote = PlatformType.STRAIGHT_LONG_TILE;
                        platformData.isStrongNote = false;
                        break;

                    case (int) LongNoteType.Hitchhike:
                        platformTypeOfNote = PlatformType.HITCH_HIKE_TILE;
                        platformData.isStrongNote = false;
                        break;

                    case (int) LongNoteType.Pipe:
                        platformTypeOfNote = PlatformType.PIPE_TILE;
                        platformData.isStrongNote = false;
                        break;

                    case (int) LongNoteType.Brick:
                        platformTypeOfNote = PlatformType.BREAKABLE_LONG_TILE;
                        platformData.isStrongNote = false;
                        break;

                    case (int) LongNoteType.Combinable:
                        platformTypeOfNote = PlatformType.COMBINABLE_LONG_TILE;
                        platformData.isStrongNote = false;
                        break;
                }

                if (platformData.currNoteObj.cachedNextLongNote != null &&
                    platformData.currNoteObj.cachedNextLongNote.Length != 0) {
                    platformTypeOfNote = PlatformType.COMBINABLE_LONG_TILE;
                }
            }
        }

        ProcessElementLongNoteAndSpecialSection(currNoteId, ref platformData, ref platformTypeOfNote);

        Platform bl = platformManager.SpawnTile(platformTypeOfNote, ref platformData);

        bl.sectionType = CurrentSpecialSection;
        bl.noteID = currNoteId;
        bl.platformData = platformData /*.Clone()*/;
        bl.platformType = platformTypeOfNote;
#if UNITY_EDITOR
        bl.name = $"{bl.noteID.ToString()}_{bl.platformType.ToString()}";
#endif
        bl.nextNoteDistance = platformData.nextNoteObj.distance; //chỉ dùng cho real platform
        bl.transCache.rotation =
            Quaternion.identity; // reset rotation because skew_straight,skew_opposite,skew_spiral make tile rotate
        // Set position z
        Vector3 positionPlatform = bl.transCache.position;
        positionPlatform.z = platformData.positionZ;

        if (platformTypeOfNote.IsLongTile()) {
            bl.timeSlide = platformData.currNoteObj.duration;
            bl.nextNoteDistance = platformData.nextNoteObj.timeAppear - platformData.currNoteObj.timeAppear -
                                  platformData.currNoteObj.duration;
            if (platformTypeOfNote == PlatformType.LONG_TILE ||
                platformTypeOfNote == PlatformType.BREAKABLE_LONG_TILE) { //auto cut for old long tile
                float duration = platformData.currNoteObj.duration;
                float safeDuration = remoteConfig.LongTile_MinDistance;
                if (platformData.currNoteObj.timeAppear + duration >
                    platformData.nextNoteObj.timeAppear - safeDuration) {
                    duration = platformData.nextNoteObj.timeAppear - platformData.currNoteObj.timeAppear - safeDuration;
                }

                bl.timeSlide = duration;
                bl.nextNoteDistance = platformData.nextNoteObj.distance - duration;

                platformData.isStrongNote = false;
                bl.platformData = platformData;
            }
        }

        // Set random position x
        positionPlatform.x = GetPositionXOfPlatform(indexSpawnNote, ref platformData, _prevPosX);

        bool isStaticBlock = platformTypeOfNote == PlatformType.MOOD_FULL;
        //TH-1490: sẽ thực hiện kiểm tra nếu block này có chức năng rồi, thì không thực hiện chức năng khác
        bool isSpecialBlock = platformTypeOfNote == PlatformType.MOOD_FULL || platformTypeOfNote.IsLongTile() ||
                              platformData.currNoteObj.elementType != NoteElementType.None;
        bool isBlockText = false;

        //Show Endless Text22
        if (ShowEndlessText(indexNote, bl)) {
            isSpecialBlock = true; //endless text
            isBlockText = true;
        }

        // Show mileStones
        if (ShowMileStones(indexNote, isSpecialBlock, bl.GetContainer())) { }

        // Show stars
        if (ShowStars(indexNote, isSpecialBlock,bl, bl.GetContainer())) {
            isSpecialBlock = true; //stars
            //update scale stars
            if (platformTypeOfNote == PlatformType.MOOD_FULL) {
                //do scale của full bar đã *3 bên scale của star phải /3
                GameItems.instance.starManager.SetScaleStars(1f / 3);
            } else {
                GameItems.instance.starManager.SetScaleStars(1);
            }
        }

        // Show percent text
        //TH-1442: Bỏ mốc 20%, 40%, 60%, 80% hiển thị trong game đang bị rối, không cần thiết vì mình đã có các mốc sao rồi. -> chỉ áp dụng với economy system
        if (!remoteConfig.Economy_IsEnable) {
            if (ShowPercentText(indexNote, isSpecialBlock, bl)) {
                isBlockText = true;
                isSpecialBlock = true; // endless text
            }
        }

        //Show High Score
        if (ShowHighScore(indexNote, isSpecialBlock, bl)) {
            isSpecialBlock = true; //high score
            isBlockText = true;
        }

        if (isLastNote) {
            isSpecialBlock = true; //last note
            isStaticBlock = true; //last note
        }

        //Process block text
        bl.text.SetActive(isBlockText); // active

        if ((isBlockText || isStaticBlock) && !isMusicalizationType && platformTypeOfNote != PlatformType.MOOD_FULL) {
            positionPlatform.x = platformData.IsShortDistance() ? _prevPosX : 0;
        }

        if (CurrentSpecialSection == IngameSectionType.Mirror) {
            if (currNoteId == _indexStartSpecialSection || currNoteId == _indexEndSpecialSection) {
                positionPlatform.x = 0f;
            }
        }

        bl.transCache.position = positionPlatform;
        platformData.positionPlatform = positionPlatform;

        //Show token for LiveEvent
        if (ShowDrill(indexNote, isSpecialBlock, bl.GetContainer())) {
            isSpecialBlock = true; //stars
        }

        // show token for LE3 - Milestone
        if (MilestoneEvent.isAvailable && MilestoneEvent.instanceSafe.ShowToken(new TokenDisplayParam() {
                parent = bl.GetContainer(),
                indexNote = indexNote,
                endlessTriggerIndex = _endlessTrigger,
                isSpecial = isSpecialBlock,
                canCollect = _canCollectMilestoneToken,
            })) {
            isSpecialBlock = true;
        }

        if (IsShowItemEvent(indexNote, isSpecialBlock)) {
            isSpecialBlock = true;
            ShowItemEvent(bl);
        }

        //Show Diamond
        if (!isSpecialBlock && IsShowDiamond(indexNote)) {
            int value = 1;
            if (remoteConfig.Economy_IsEnable && RemoteConfigBase.instance.Economy_Diamond_InGame_YStartAmount != 0) {
                value = RemoteConfigBase.instance.Economy_Diamond_InGame_YStartAmount;
            }

            CreateDiamond(value, CurrencyEarnSource.COLLECT_DIAMOND, bl, positionPlatform.z);
        }

        //Show center
        bl.ShowPerfectAnim();

        float nextPositionX = _prevPosX;
        float distanceSlide = 0f;
        switch (platformTypeOfNote) {
            case PlatformType.NORMAL:
            case PlatformType.MOOD_SHORT:
                if (platformData.longNoteType == LongNoteType.Volley) {
                    AdjustLongNoteDuration(currNoteId, ref bl, ref platformData, ref nextPlatformdata,
                        isChangePlatformDistance: false);
                }

                break;

            case PlatformType.FAKE:
                if (bl.platformData.stage == Stage.FAKE_TILE) {
                    var element = bl.platformData.currNoteObj.elementType;
                    if (element != NoteElementType.None && element != NoteElementType.FakeTile) {
                        break;
                    }

                    bl.platformData.currNoteObj.elementType = NoteElementType.FakeTile;

                    if (NotesManager.instance.isMidiGenerateTile) {
                        positionPlatform.x = GetPositionXForceByPitch(ref platformData);
                        bl.transCache.position = positionPlatform;
                        platformData.positionPlatform = positionPlatform;
                    }

                    //Active clone
                    if (!isStaticBlock) {
                        ProcessTypeFakeForStageFakeTile(bl, platformData, positionPlatform, platformData.flyTime);
                    }
                }

                break;

            case PlatformType.MOVING:
                if (bl.platformData.stage == Stage.MOVING_TILE) {
                    var element = bl.platformData.currNoteObj.elementType;
                    if (element != NoteElementType.None && element != NoteElementType.MovingTile) {
                        break;
                    }

                    bl.platformData.currNoteObj.elementType = NoteElementType.MovingTile;
                    ProcessStageNameMovingTile(bl, isBlockText, isStaticBlock, platformData);
                }

                break;

            case PlatformType.MOOD_FULL:
                if (bl.platformData.stage == Stage.COLORING) {
                    ProcessPlatformTypeFull(bl, platformData);
                }

                break;

            case PlatformType.COLORING:
                if (bl.platformData.stage == Stage.COLORING) {
                    //set màu cho main tile và thêm 1 tile fake color bên cạnh
                    ProcessPlatformTypeColoringForStageColoring(bl, platformData);
                } else if (bl.platformData.stage == Stage.COLOR_CHOOSING) {
                    // chỉ có 1 tile màu trung lập, sẽ đổi khi player vào tile COLOR_CHOOSING
                    bl.SetSkinImmediately(_indexSkinDefaultForColorChoosing);
                }

                break;

            case PlatformType.COLOR_CHOOSING:
                if (bl.platformData.stage == Stage.COLOR_CHOOSING) { // bắt đầu vào stage color choosing
                    Platform colorChoosing2 = ProcessPlatformTypeColorChoosingForStageColorChoosing(bl, platformData);
                    if (colorChoosing2 != null) {
                        CompletedSpawnPlatform(colorChoosing2, false);

                        //update lại ví trí của tile color main 01 choosing căn ra giữa
                        positionPlatform.x = GetSlot01ColorChoosing();
                        bl.transCache.position = positionPlatform;

                        // Dùng (currNoteId+1) thay cho ++currNoteId vì currNoteId còn sử dụng ở các dòng code cuối func nữa
                        PlatformData nextPlatformData = mapManager.GetPlatformData((currNoteId + 1) % noteCount);
                        if (nextPlatformData.IsShortDistance()) {
                            _prevPosX = 0;
                        } else {
                            _prevPosX = positionPlatform.x;
                        }
                    }
                }

                break;

            case PlatformType.LATE_FAKE:
                if (bl.platformData.stage == Stage.LATE_FAKE) {
                    bool isFirstTile = platformManager.IsFirstTile();
                    if (!isFirstTile) {
                        bl.SetActiveEfxFocus(true);
                        ProcessTypeLateFakeForStageFakeTile(bl, platformData);
                    }
                }

                break;

            case PlatformType.SKEW:
                if (isLastNote) {
                    break; // không làm gì nếu là LastNote
                }

                if (bl.platformData.stage == Stage.SKEW_STRAIGHT || bl.platformData.stage == Stage.SKEW_OPPOSITE) {
                    ProcessTypeSkewForStageSkew(bl, ref platformData, ref isLeftSkewTile, ref positionPlatform);
                }

                break;

            case PlatformType.SKEW_SPIRAL:
                //Debug.LogError($"[{bl.noteID}] process skew spiral data");
                if (bl.platformData.stage == Stage.SKEW_SPIRAL) {
                    (int offset, Vector3 position, Vector3 rotation) tempData =
                        mapManager.GetSkewSpiralPositionAndRotation(bl, platformData.isStartStage);
                    positionPlatform = tempData.position;

                    platformData.positionPlatform = positionPlatform;
                    bl.transCache.position = positionPlatform;

                    bl.transCache.rotation = Quaternion.Euler(tempData.rotation);

                    bl.SetActiveEfxFocus(true);
#if UNITY_EDITOR
                    bl.name += $"_{tempData.offset.ToString()}";
#endif
                }

                break;

            case PlatformType.STRAIGHT_LONG_TILE:
                AdjustLongNoteDuration(currNoteId, ref bl, ref platformData, ref nextPlatformdata,
                    isChangePlatformDistance: true);
                PlatformStraightLongTile platformStraightLongTile = bl as PlatformStraightLongTile;

                distanceSlide = bl.timeSlide * Ball.b.GetBalLSpeed();
                // positionPlatform.x = (positionPlatform.x + nextPositionX) / 2;
                positionPlatform.x = _prevPosX;
                positionPlatform.z = platformData.positionZ - 1;

                float scaleZ2 = (distanceSlide + 2) / (12 * _blockScale.x / _originalBlockScale.x);

                platformStraightLongTile.SetScaleAndPosition(new Vector3(1, 1, scaleZ2), positionPlatform);

                platformData.positionPlatform = positionPlatform;
                platformStraightLongTile.UpdatePivot();
                break;

            case PlatformType.BREAKABLE_LONG_TILE:
            case PlatformType.LONG_TILE:
                if (NotesManager.instance.isMidiGenerateTile) {
                    //control x position
                    positionPlatform.x = GetPositionXForceByPitch(ref platformData);
                    nextPositionX = GetPositionXForceByPitch(ref nextPlatformdata);
                } else {
                    nextPositionX = GetPositionXOfPlatform(indexNote + 1, ref nextPlatformdata, _prevPosX);
                }

                if (MapManager.IsMoodChange(ref nextPlatformdata))
                    nextPositionX = 0;

                positionPlatform.x = (positionPlatform.x + nextPositionX) / 2;
                positionPlatform.z = platformData.positionZ;

                Vector3 startPoint = positionPlatform;
                distanceSlide = bl.timeSlide * Ball.b.GetBalLSpeed();

                Vector3 wantedPoint = new Vector3(nextPositionX, 0, nextPlatformdata.positionZ);

                float distanceToEndPoint = Vector3.Distance(startPoint, wantedPoint);

                Vector3 endPoint = startPoint + (wantedPoint - startPoint) * distanceSlide / distanceToEndPoint;

                float angel = Mathf.Atan((endPoint.x - startPoint.x) /
                                         (endPoint.z - startPoint.z)); // tính góc cho block
                if (gameController.IsTutorial) {
                    angel = 0;
                }

                if (float.IsNaN(angel)) {
                    angel = 0;
                }

                bl.transCache.localRotation = Quaternion.Euler(0, angel * 180f / Mathf.PI, 0).normalized;

                float scaleZ = (distanceSlide + 2) / (12 * _blockScale.x / _originalBlockScale.x);

                //offset value to ensure the availability of long tile when rotated
                var offset = distanceSlide - Mathf.Cos(Mathf.Abs(bl.transCache.localRotation.y)) * distanceSlide;
                float positionZ = startPoint.z - offset - 1;
                positionPlatform.z = positionZ;

                bl.SetScaleAndPosition(new Vector3(1, 1, scaleZ), positionPlatform);
                platformData.positionPlatform = positionPlatform;
                bl.AdjustBoxColliderLongType();
                bl.UpdatePivot();
                break;

            case PlatformType.PIPE_TILE:
                AdjustLongNoteDuration(currNoteId, ref bl, ref platformData, ref nextPlatformdata,
                    isChangePlatformDistance: true);
                positionPlatform.x = _prevPosX;
                var pipeBl = bl as PipePlatform;
                float lengthTile = bl.timeSlide * Ball.b.GetBalLSpeed();

                nextPositionX = GetPositionXOfPlatform(indexNote + 1, ref nextPlatformdata, _prevPosX);
                if (MapManager.IsMoodChange(ref nextPlatformdata))
                    nextPositionX = 0;

                pipeBl.CalculateMesh(Vector3.zero, new Vector3(nextPositionX - positionPlatform.x, 0, lengthTile),
                    positionPlatform.x);

                pipeBl.PositionPlatform = positionPlatform;
                bl.transCache.position = positionPlatform;
                break;

            case PlatformType.HITCH_HIKE_TILE:
                AdjustLongNoteDuration(currNoteId, ref bl, ref platformData, ref nextPlatformdata,
                    isChangePlatformDistance: true);
                var hitchBl = bl as HitchhikePlatform;
                float targetZ = bl.timeSlide * Ball.b.GetBalLSpeed() + 3f;
                positionPlatform.x = 0f;
                hitchBl.ShowLine(positionPlatform, targetZ);
                bl.transCache.position = positionPlatform;
                break;

            case PlatformType.COMBINABLE_LONG_TILE:
                if (NotesManager.instance.isMidiGenerateTile) {
                    //control x position
                    positionPlatform.x = GetPositionXForceByPitch(ref platformData);
                    nextPositionX = GetPositionXForceByPitch(ref nextPlatformdata);
                } else {
                    nextPositionX = GetPositionXOfPlatform(indexNote + 1, ref nextPlatformdata, _prevPosX);
                }

                if (MapManager.IsMoodChange(ref nextPlatformdata))
                    nextPositionX = 0;

                positionPlatform.x = (positionPlatform.x + nextPositionX) / 2;
                positionPlatform.z = platformData.positionZ;

                startPoint = positionPlatform;
                distanceSlide = platformData.currNoteObj.cachedNextLongNote.IsNullOrEmpty()
                    ? bl.timeSlide * Ball.b.GetBalLSpeed()
                    : platformData.currNoteObj.originalDuration * Ball.b.GetBalLSpeed();

                if (platformData.currNoteObj.cachedNextLongNote.IsNullOrEmpty()) {
                    wantedPoint = new Vector3(nextPositionX, 0, nextPlatformdata.positionZ);
                } else {
                    var next = positionPlatform.z + distanceSlide;
                    wantedPoint =
                        new Vector3(GetPositionXForceByPitch(platformData.currNoteObj.cachedNextLongNote[0].Pitch), 0,
                            next);
                }

                distanceToEndPoint = Vector3.Distance(startPoint, wantedPoint);

                endPoint = startPoint + (wantedPoint - startPoint) * distanceSlide / distanceToEndPoint;

                angel = Mathf.Atan((endPoint.x - startPoint.x) / (endPoint.z - startPoint.z)); // tính góc cho block

                var rotation = Quaternion.Euler(0, angel * 180f / Mathf.PI, 0);

                scaleZ = (distanceSlide + 2) / (12 * _blockScale.x / _originalBlockScale.x);

                offset = distanceSlide -
                         Mathf.Cos(Mathf.Abs(rotation.y)) *
                         distanceSlide; //offset value to ensure the availability of long tile when rotated
                positionZ = startPoint.z - offset - 1;
                positionPlatform.z = positionZ;

                //Add connecting segment
                var combinableLongTile = bl as PlatformCombinableLongTile;
                List<Vector3> nodes = new List<Vector3>();
                nodes.Add(new Vector3(positionPlatform.x, 0, positionPlatform.z));
                nodes.Add(new Vector3(endPoint.x, 0, endPoint.z));

                //Validate and add segments for removed long tile
                if (!platformData.currNoteObj.cachedNextLongNote.IsNullOrEmpty()) {
                    float totalDuration = 0f;
                    for (int i = 0; i < platformData.currNoteObj.cachedNextLongNote.Length; i++) {
                        var data = platformData.currNoteObj.cachedNextLongNote[i];
                        _tempNextX = i < platformData.currNoteObj.cachedNextLongNote.Length - 1
                            ? GetPositionXForceByPitch(data.Pitch) +
                              GetPositionXForceByPitch(platformData.currNoteObj.cachedNextLongNote[i + 1].Pitch)
                            : GetPositionXForceByPitch(data.Pitch) + nextPositionX;
                        totalDuration += data.SlideDuration;
                        _tempNextEnd.x = _tempNextX / 2f;
                        _tempNextEnd.y = 0;
                        _tempNextEnd.z = endPoint.z + totalDuration * Ball.b.GetBalLSpeed();
                        nodes.Add(_tempNextEnd);
                    }
                }

                combinableLongTile.SetScaleAndPosition(new Vector3(1, 1, scaleZ), positionPlatform);
                combinableLongTile.UpdateNodePositions(nodes, ShapeTangentMode.Linear, Spline.Type.Linear);
                combinableLongTile.AdjustBoxColliderLongType();
                combinableLongTile.UpdatePivot();
                combinableLongTile.Init();
                break;

            case PlatformType.S_LONG_TILE:
                break;

            default:
                Debug.LogError("[Spawn] need process stageOfNote " + platformTypeOfNote);
                break;
        }

        if (platformTypeOfNote == PlatformType.PIPE_TILE) {
            _prevPosX = (bl as PipePlatform).GetEndPosX() + positionPlatform.x;
        } else {
            _prevPosX = positionPlatform.x;
        }

        mapManager.SetPlatformData(currNoteId, platformData);
        bl.platformData = platformData;
        bl.isHitchhikeTile = platformTypeOfNote == PlatformType.HITCH_HIKE_TILE;
        bl.isForceMove = platformTypeOfNote.IsForceMove();

        if (indexNote != 0 && mainBlockQueue.Count == 0) {
            bl.FlyEffect(0);
        } else {
            bl.FlyEffect(platformData.flyTime);
        }

        MidiProcessSpecialTile(bl, currNoteId);
        ProcessBoosterHyperBoost(bl, currNoteId, platformData);

        CompletedSpawnPlatform(bl, true);
        if (CanMakeDiamondLine(currNoteId)) {
            CheckAndMakeDiamondLine(currNoteId);
        }

        ProcessSpecialSection(bl, currNoteId, platformTypeOfNote, platformData, nextPlatformdata, positionPlatform);

        bl.MakeTileTidy();

        if (isLastNote) {
            OnLastNoteAppear?.Invoke(platformData.currNoteObj);
        }

        return bl;
    }

    private bool ValidateIndexNoteSpawn(ref int indexNote) {
        if (indexNote < noteCount) {
            return true;
        }

        if (!GameController.enableEndless) { //finish road
            return false;
        }

        int endlessModeCount = Ball.b.endlessModeCount;
        if (indexNote % noteCount == 0) {
            MapManager.instance.IncreaseEndlessCount(endlessModeCount + 2);
        }

        if (ChallengeMode.IsActive) {
            int maxRound = 2; // tối đa 3 round to collect 3 crown
            if (indexNote >= noteCount * maxRound) {
                return false;
            }
        } else if (EndlessIteration.IsActive) {
            int round = endlessModeCount + 1;
            if (indexNote > noteCount * round) {
                return false;
            }
        }

        return true;
    }

    private void ProcessElementLongNoteAndSpecialSection(int currNoteId, ref PlatformData platformData,
                                                         ref PlatformType platformTypeOfNote) {
        if (!notesManager.HasElementType()) {
            return;
        }

        if (platformData.IsLongNote) {
            switch (platformData.currNoteObj.elementType) {
                case NoteElementType.LongTileBreak:
                    platformTypeOfNote = PlatformType.BREAKABLE_LONG_TILE;
                    break;
            }
        }

        if (notesManager.SpecialSection_Type == IngameSectionType.Normal)
            return;

        if (currNoteId >= _indexStartSpecialSection && currNoteId <= _indexEndSpecialSection) {
            CurrentSpecialSection = notesManager.SpecialSection_Type;
            if (CurrentSpecialSection == IngameSectionType.HyperBoost ||
                CurrentSpecialSection == IngameSectionType.ZicZac) {
                platformTypeOfNote = PlatformType.NORMAL;
                platformData.isStrongNote = false;
            }
        } else {
            CurrentSpecialSection = IngameSectionType.Normal;
        }
    }

    private void MidiProcessSpecialTile(Platform bl, int currNoteId) {
        bl.elementType = notesManager.noteDatas[currNoteId].elementType;
    }

    private void ProcessSpecialSection(Platform bl, int currNoteId, PlatformType platformTypeOfNote,
                                       PlatformData platformData, PlatformData nextPlatformData,
                                       Vector3 positionPlatform) {
        switch (CurrentSpecialSection) {
            case IngameSectionType.Mirror when _indexStartSpecialSection == currNoteId:
                bl.CreateMirrorBox();
                break;

            case IngameSectionType.Mirror: {
                if (platformTypeOfNote != PlatformType.MOOD_FULL) {
                    bl.SetMirror(CreateMirrorTile(bl));
                }

                break;
            }

            case IngameSectionType.HyperBoost when currNoteId == _indexStartSpecialSection:
                bl.CreatePointHyperBoost();
                Enum.TryParse(remoteConfig.NewElements_HyperBoost_ObjectType, out _hyperBoostType);
                break;

            case IngameSectionType.HyperBoost: {
                bl.SetHyperBoost();
                Vector3 positionBonus = platformData.positionPlatform +
                                        (remoteConfig.NewElements_HyperBoost_FlyHeight +
                                         HYPERBOOST_DIAMOND_HEIGHT_BIAS) * Vector3.up;
                switch (_hyperBoostType) {
                    case HyperBoostType.diamond:
                        CreateDiamond(1, CurrencyEarnSource.COLLECT_DIAMOND, positionBonus);
                        break;

                    case HyperBoostType.music:
                        CreateMusicNote(positionBonus, false);
                        break;

                    default:
                        CreateMusicNote(positionBonus, false);
                        break;
                }

                break;
            }

            case IngameSectionType.ZicZac:
                if (_ziczacTile == null || !_ziczacTile.gameObject.activeSelf) {
                    SpawnZicZacTile(platformData, positionPlatform);
                }

                bl.AddPointSpecialSectionZicZac(currNoteId == _indexStartSpecialSection,
                    currNoteId == _indexEndSpecialSection);
                break;

            case IngameSectionType.UpsideDown: {
                if (currNoteId == _indexStartSpecialSection || currNoteId == _indexEndSpecialSection) {
                    float posZOfTransferPort = platformData.positionZ +
                                               (nextPlatformData.positionZ - platformData.positionZ) / 3;
                    MapManager.instance.ShowTransitionReverse(posZOfTransferPort);
                    isPauseSpawnTile = true;
                }

                break;
            }
        }
    }

    private void RecheckBoosterHyperStar(int currentJump) {
        if (!BoosterManager.isInstanced)
            return;
        if (!BoosterManager.instanceSafe.usingHyperStar)
            return;

        BoosterManager.instanceSafe.GetHyperBoostData(out int start, out int end);
        if (start == 0 && end == 0)
            return;

        if (currentJump == end - 1) {
            BoosterManager.instanceSafe.ResetHyperBoostData();
        }
    }

    private void ProcessBoosterHyperBoost(Platform bl, int currNoteId, PlatformData platformData) {
        if (!BoosterManager.isInstanced)
            return;
        if (!BoosterManager.instanceSafe.usingHyperStar)
            return;

        if (GameController.enableEndless && Ball.b.isPassRound1)
            return;

        BoosterManager.instanceSafe.GetHyperBoostData(out int start, out int end);
        if (start == 0 && end == 0)
            return;

        if (currNoteId == start) {
            bl.CreatePointHyperBoost();
            bl.sectionType = IngameSectionType.HyperBoost;
            return;
        }

        if (currNoteId > start && currNoteId < end) {
            bl.SetHyperBoost();
            Vector3 positionBonus = platformData.positionPlatform +
                                    (remoteConfig.NewElements_HyperBoost_FlyHeight + HYPERBOOST_DIAMOND_HEIGHT_BIAS) *
                                    Vector3.up;
            CreateDiamond(BoosterManager.instanceSafe.GetHyperBoostValue(), CurrencyEarnSource.booster, positionBonus);
            bl.sectionType = IngameSectionType.HyperBoost;
        }
    }

    private PlatformCombinableLongTile _ziczacTile;

    private void SpawnZicZacTile(PlatformData platformData, Vector3 positionPlatform) {
        _ziczacTile =
            platformManager.SpawnTile(PlatformType.S_LONG_TILE, ref platformData) as PlatformCombinableLongTile;
        if (_ziczacTile == null)
            return;

        _ziczacTile.platformData.currentIndexNote = platformData.currentIndexNote;

        var endPlatform = mapManager.GetPlatformData(_indexEndSpecialSection);
        Vector3 startPoint = positionPlatform;
        float timeSlide = endPlatform.currNoteObj.timeAppear - platformData.currNoteObj.timeAppear;
        float distanceSlide = timeSlide * Ball.b.GetBalLSpeed();
        float scaleZ = (distanceSlide + 2) / (12 * _blockScale.x / _originalBlockScale.x);

        positionPlatform.z -= 1;
        //Add connecting segment
        List<Vector3> nodeS = new List<Vector3>();
        nodeS.Add(new Vector3(positionPlatform.x, 0, positionPlatform.z));
        //nodeS.Add(new Vector3(endPoint.x, 0, endPoint.z));
        for (int i = platformData.currentIndexNote + 1; i <= _indexEndSpecialSection; i++) {
            var noteData = mapManager.GetPlatformData(i).currNoteObj;
            _tempMidPoint.x = GetPositionXForceByPitch(noteData.pitch);
            _tempMidPoint.y = 0;
            _tempMidPoint.z = startPoint.z +
                              (noteData.timeAppear - platformData.currNoteObj.timeAppear) * Ball.b.GetBalLSpeed();
            nodeS.Add(_tempMidPoint);
        }

        _ziczacTile.timeSlide = timeSlide;
        _ziczacTile.SetScaleAndPosition(new Vector3(1, 1, scaleZ), positionPlatform);
        _ziczacTile.SetSPositions(nodeS);
        _ziczacTile.AdjustBoxColliderLongType();
        _ziczacTile.ForceControlScale();
        _ziczacTile.UpdatePivot();
        _ziczacTile.Init(false);
        CompletedSpawnPlatform(_ziczacTile, true);
    }

    public void OnSlideZicZac(bool isSlide, Vector3 position) {
        if (_ziczacTile == null)
            return;

        _ziczacTile.OnSlide(isSlide, position);
    }

    public void RollinZicZacTile(float time, Vector3 position) {
        if (_ziczacTile == null)
            return;

        _ziczacTile.Slide(time, position);
    }

    public float GetZicZacAutoPosition(Vector3 cachePosition) {
        if (_ziczacTile == null) {
            return cachePosition.x;
        }

        return _ziczacTile.GetAutoPosition(cachePosition).x;
    }
    // private NoteElementType GetFullMoodType() {
    //     int indexMoodChange = Configuration.GetSpecialMoodChangeIndex();
    //     switch (indexMoodChange) {
    //         case (int)SpecialMoodChangeIndex.MoodBreak:
    //             return NoteElementType.MoodChangeBroken;
    //         case (int)SpecialMoodChangeIndex.TileBreakInstant:
    //             return NoteElementType.MoodChangeInstant;
    //         case (int)SpecialMoodChangeIndex.TileBreakOrder:
    //             return NoteElementType.MoodChangeOrder;
    //         case (int)SpecialMoodChangeIndex.TileBreakTrain:
    //             return NoteElementType.MoodChangeTrain;
    //     }
    //     return NoteElementType.MoodChange;
    // }
    //
    // private NoteElementType GetRandomElementType() {
    //     bool isSpecial = UnityEngine.Random.Range(0, 999999) % 3 == 0;
    //     if (!isSpecial) {
    //         return NoteElementType.None;
    //     }
    //
    //     var specialTile = Configuration.GetSpecialTileIndex();
    //     switch (specialTile) {
    //         case <0:
    //             return NoteElementType.None;
    //         case (int)SpecialTileIndex.FadeOut:
    //             return NoteElementType.FadeOut;
    //         case (int)SpecialTileIndex.FadeInOut:
    //             return NoteElementType.FadeInOut;
    //         case (int)SpecialTileIndex.FakeThunder:
    //             return NoteElementType.FakeThunder;
    //         case (int)SpecialTileIndex.FakeConveyor:
    //             return NoteElementType.FakeConveyor;
    //         case (int)SpecialTileIndex.MovingTeleport:
    //             return NoteElementType.Teleport;
    //         case (int)SpecialTileIndex.MovingCircle:
    //             return NoteElementType.MovingCircle;
    //         case (int)SpecialTileIndex.FakeTransform:
    //             return NoteElementType.FakeTransform;
    //     }
    //
    //     return NoteElementType.None;
    // }

    private bool AdjustLongNoteDuration(int idCurrentNote, ref PlatformData platformData,
                                        ref PlatformData nextPlatformData) {
        if (MapManager.instance == null)
            return false;
        if (platformData.nextNoteObj == null)
            return false;

        //Logger.EditorLogError("LongNote", $" {idCurrentNote} {platformData.longNoteType.ToString()}");
        if (!platformData.isLongDuration || //too far
            (platformData.currNoteObj.timeAppear + platformData.currNoteObj.duration +
                LongNoteConfig.SafeLongNoteDistance > platformData.nextNoteObj.timeAppear) //too close
           ) {
            notesManager.StorageDurationBeforeChange(idCurrentNote);

            float timeSlide = platformData.nextNoteObj.timeAppear - platformData.currNoteObj.timeAppear -
                              LongNoteConfig.SafeLongNoteDistance;

            var prevPlatformData = mapManager.GetPlatformData(idCurrentNote - 1);
            if (prevPlatformData.nextNoteObj != null) {
                prevPlatformData.nextNoteObj.duration = timeSlide;
                mapManager.SetPlatformData(idCurrentNote - 1, prevPlatformData);
            }

            if (platformData.currNoteObj != null) {
                platformData.currNoteObj.duration = timeSlide;
                mapManager.SetPlatformData(idCurrentNote, platformData);
            }

            if (nextPlatformData.beforeNoteObj != null) {
                nextPlatformData.beforeNoteObj.duration = timeSlide;
                MapManager.instance.SetPlatformData(idCurrentNote + 1, nextPlatformData);
            }

            return true;
        }

        if (platformData.isLongDuration && NotesManager.instance.isMidiGenerateTile) {
            if (!platformData.currNoteObj.cachedNextLongNote.IsNullOrEmpty()) {
                var totalDuration = platformData.currNoteObj.originalDuration;
                for (int i = 0; i < platformData.currNoteObj.cachedNextLongNote.Length; i++) {
                    var currentData = platformData.currNoteObj.cachedNextLongNote[i];
                    if (currentData == null)
                        continue;

                    if (currentData.TimeAppear + currentData.SlideDuration + LongNoteConfig.SafeLongNoteDistance >
                        platformData.nextNoteObj.timeAppear) {
                        currentData.SlideDuration -= LongNoteConfig.SafeLongNoteDistance;
                    }

                    totalDuration += currentData.SlideDuration;
                }

                var prevPlatformData = MapManager.instance.GetPlatformData(idCurrentNote - 1);
                if (prevPlatformData.nextNoteObj != null) {
                    prevPlatformData.nextNoteObj.duration = totalDuration;
                    MapManager.instance.SetPlatformData(idCurrentNote - 1, prevPlatformData);
                }

                if (platformData.currNoteObj != null) {
                    platformData.currNoteObj.duration = totalDuration;
                    MapManager.instance.SetPlatformData(idCurrentNote, platformData);
                }

                if (nextPlatformData.beforeNoteObj != null) {
                    nextPlatformData.beforeNoteObj.duration = totalDuration;
                    MapManager.instance.SetPlatformData(idCurrentNote + 1, nextPlatformData);
                }

                return true;
            }
        }

        return false;
    }

    private void AdjustLongNoteDuration(int idCurrentNote, ref Platform bl, ref PlatformData platformData,
                                        ref PlatformData nextPlatformdata, bool isChangePlatformDistance = true) {
        // if (MapManager.instance == null) return;
        // //Logger.EditorLogError("LongNote", $" {idCurrentNote} {platformData.longNoteType.ToString()}");
        // if (!platformData.isLongDuration || //too far
        //     (platformData.currNoteObj.timeAppear + bl.timeSlide + LongNoteConfig.SafeLongNoteDistance >
        //      platformData.nextNoteObj.timeAppear) //too close
        //    ) {
        //     notesManager.StorageDurationBeforeChange(idCurrentNote);
        //
        //     float timeSlide = platformData.nextNoteObj.timeAppear - platformData.currNoteObj.timeAppear -
        //                       LongNoteConfig.SafeLongNoteDistance;
        //
        //     var prevPlatformData = MapManager.instance.GetPlatformData(idCurrentNote - 1);
        //     if (prevPlatformData.nextNoteObj != null) {
        //         prevPlatformData.nextNoteObj.duration = timeSlide;
        //         MapManager.instance.SetPlatformData(idCurrentNote - 1, prevPlatformData);
        //     }
        //
        //     platformData.currNoteObj.duration = timeSlide;
        //     MapManager.instance.SetPlatformData(idCurrentNote, platformData);
        //
        //     nextPlatformdata.beforeNoteObj.duration = timeSlide;
        //     MapManager.instance.SetPlatformData(idCurrentNote + 1, nextPlatformdata);
        //
        //     Logger.EditorLogError("LongNote", $"Change longnote {idCurrentNote} {bl.timeSlide} -> {timeSlide}");
        //     bl.timeSlide = timeSlide;
        //     if (isChangePlatformDistance) {
        //         float nextDistance = nextPlatformdata.currNoteObj.timeAppear -
        //                              (platformData.currNoteObj.timeAppear + timeSlide);
        //         bl.nextNoteDistance = nextDistance;
        //     }
        // }
    }

    private bool CanMakeDiamondLine(int currNoteId) {
        if (!isMusicalizationType)
            return false;
        if (currNoteId == 0)
            return true;

        PlatformData prevTile = mapManager.GetPlatformData((currNoteId - 1) % noteCount);
        if (!prevTile.isInited)
            return false;
        if (prevTile.platformType == PlatformType.SKEW_SPIRAL)
            return false;

        return true;
    }

    public bool DisableTrySkinOnPlatForm(bool canTry) {
        //validate try skin
        if (canTry && Ball.b.endlessModeCount == 0) {
            if (remainSkinTryInMap > 0) {
                remainSkinTryInMap--;
            } else {
                return true;
            }
        }

        return false;
    }

    private void CompletedSpawnPlatform(Platform bl, bool isMainTile) {
        //Set skin
        bool isMoodTile = (bl.platformData.platformType == PlatformType.MOOD_FULL &&
                           !RemoteConfigBase.instance.Musicalization_MoodChange_FullTileSameColor) ||
                          bl.platformData.platformType == PlatformType.MOOD_SHORT;

        if (isMoodTile) {
            //get next mood skin
            NoteData.Mood nextMood;
            if (isMusicalizationType) {
                nextMood = bl.platformData.nextNoteObj.mood;
            } else {
                var currentMood = mapManager.GetCurrentMode();
                nextMood = (NoteData.Mood) (((int) currentMood + 1) % Enum.GetNames(typeof(NoteData.Mood)).Length);
            }

            int indexSkin = mapManager.GetNextSkinId(nextMood);
            bl.SetSkinImmediately(indexSkin);
        } else if (bl.platformData.stage != Stage.COLORING && bl.platformData.stage != Stage.COLOR_CHOOSING) {
            if (!onTransition) {
                //nếu không phải đang đổi màu thì áp dụng skin mới luôn (cần viết gọn lại 3 hàm dưới)
                bl.SetSkin(currentIndexSkin, true); //set sr[1]
                bl.FinishedSwitchColor(currentIndexSkin); //set sr[0] = sr[1]
                bl.SetSkin(currentIndexSkin); //set sr[0] if no onTransition
            } else { //nếu đang đổi màu thì set skin cũ trước
                //set sr[0] = skin cũ
                bl.SetSkin(beforeIndexSkin, false, true);

                //set sr[1] = skin mới rồi đợi quá trình chuyển skin hoàn tất
                bl.SetSkin(currentIndexSkin, true);
            }

            bl.SetEffectTexture(currentIndexSkin);
            bl.indexSkin = currentIndexSkin; // khởi tạo index skin cho tile có thể chuyển màu
        }

        bl.ShowTopFire(false); //disable topFire

        //Clear effect
        if (bl.brustEffect != null) {
            bl.brustEffect.Clear();
        }

        bl.SetActiveHitEffect(false);
        bl.SetOrdering(); //Update ordering

        bl.CompleteSpawn();
        OnCompleteSpawnPlatform?.Invoke(bl);
        if (isMainTile) {
            mainBlockQueue.Enqueue(bl); //for auto play
        }
    }

    private bool ShowStars(int indexNote, bool isStaticBlock, Platform bl, Transform parent) {
        if (remoteConfig.ProgressBar_StyleIndex == 3) {
            if (remoteConfig.ProgressByScore_IsEnable) {
                return false;
            }

            if (GameController.enableEndless) {
                return false;
            }
        }

        if (ChallengeMode.IsActive && GameController.enableEndless) {
            byte idCrown = 0;
            if (Ball.b.endlessModeCount == 0) {
                //2 star
                if (indexNote == noteCount / 2) {
                    idCrown = 1;
                } else if (indexNote == noteCount) {
                    idCrown = 2;
                }
            } else if (indexNote == noteCount * 2 - 1) {
                idCrown = 3;
            }

            if (idCrown != 0 && idCrown > notesManager.song.bestCrown) {
                //Logger.EditorLogError($"Spawn crown :{indexNote} / {noteCount}");
                GameItems.instance.starManager.SetCrown(1, bl);
                return true;
            }

            return false;
        }

        if (remoteConfig.Economy_IsEnable) {
            if (notesManager.song.isTutorialSong) {
                // TH-1194: Hide the progress bar and diamond in tutorial game play
                return false;
            } else {
                //TH-1442: Khi chơi endless mode, thanh progress bar chỉ hiển sao ở cuối loop (hiện tại vẫn hiện 3 mốc sao)
                // Khi endlessmode, các star sẽ hiển thị theo milestone endless
                if (GameController.enableEndless && remoteConfig.Economy_MileStone_Enable) {
                    return ShowStarsEconomyEndless(indexNote, isStaticBlock, parent);
                } else {
                    // Nothing -> không thay đổi gì so với normal mode
                }
            }
        }

        if (_starsTrigger.Contains(indexNote) && indexNote < _endlessTrigger) {

            //TY-4405: Lỗi chơi hết bài nhưng không được 3 sao
            //=> Khi chơi đến note của thì luôn hiện 3 sao
            if (indexNote == noteCount - 1) {
                GameItems.instance.starManager.SetStars(3, parent);
                return true;
            }

            if (!isStaticBlock) {
                int indexStar = _starsTrigger.IndexOf(indexNote) + 1;
                GameItems.instance.starManager.SetStars(indexStar, parent);
                return true;
            } else {
                _starsTrigger[_starsTrigger.IndexOf(indexNote)]++;
            }
        }

        return false;
    }

    private bool ShowDrill(int indexNote, bool isStaticBlock, Transform parent) {
        if (!_canEarnDrill)
            return false;

        if (indexNote > _endlessTrigger) {
            indexNote %= _endlessTrigger;
        }

        if (MysteryDoorManager.instanceSafe.ContainDrillAt(indexNote)) {
            if (isStaticBlock) {
                MysteryDoorManager.instanceSafe.IncreaseDrillAt(indexNote);
                return false;
            }

            MysteryDoorManager.instanceSafe.SpawnDrill(parent, indexNote);
            return true;
        }

        return false;
    }

    private bool ShowMilestoneToken(int indexNote, bool isStaticBlock, Transform parent) {
        if (!_canCollectMilestoneToken) {
            return false;
        }

        if (indexNote > _endlessTrigger) {
            indexNote %= _endlessTrigger;
        }

        if (MilestoneEvent.instanceSafe.ContainTokenAt(indexNote)) {
            if (isStaticBlock) {
                MilestoneEvent.instanceSafe.IncreaseTokenAt(indexNote);
                return false;
            }

            MilestoneEvent.instanceSafe.SpawnToken(parent);
            return true;
        }

        return false;
    }

    //TH-1442: Khi chơi endless mode, thanh progress bar chỉ hiển sao ở cuối loop (hiện tại vẫn hiện 3 mốc sao)
    // Khi endlessmode, các star sẽ hiển thị theo milestone endless
    private bool ShowStarsEconomyEndless(int indexNote, bool isStaticBlock, Transform parent) {
        int round = indexNote / _endlessTrigger;
        if (remoteConfig.Economy_MileStone_Endless_Detailed) {
            // ăn theo từng mốc
            int realNote = indexNote % _endlessTrigger;
            if (_starsTrigger.Contains(indexNote) && indexNote < _endlessTrigger) {
                if (!isStaticBlock) {
                    int offsetMileStone = _mileStoneTrigger.IndexOf(realNote) + 1;
                    int indexMileStone = round * 4 + offsetMileStone;
                    if (gameController.HaveRewardAtMileStone(indexMileStone)) {
                        GameItems.instance.starManager.SetStars(offsetMileStone, parent);
                        return true;
                    } else {
                        // không có reward cho mốc milestone này
                        return false;
                    }
                } else {
                    _starsTrigger[_starsTrigger.IndexOf(indexNote)]++;
                    return false;
                }
            }

            return false;
        } else {
            // ăn theo 1 round
            if (indexNote != 0 && indexNote % _endlessTrigger == 0) {
                int indexMileStone = round * 4;
                if (gameController.HaveRewardAtMileStone(indexMileStone)) {
                    GameItems.instance.starManager.SetStars(3, parent); // dùng star 2 để làm mốc
                    return true;
                } else {
                    // không có reward cho mốc milestone này
                    return false;
                }
            } else {
                return false;
            }
        }
    }

    private bool ShowMileStones(int indexNote, bool isStaticBlock, Transform parent) {
        if (!remoteConfig.Economy_MileStone_Enable) {
            // không có config cho diamond on road
            return false;
        }

        if (notesManager.song.isTutorialSong) {
            // TH-1194: Hide the progress bar and diamond in tutorial game play
            return false;
        }

        if (Configuration.instance.isGamePlayTutorial) { // không show diamond cho bài hát tutorial
            return false;
        }

        if (GameController.enableEndless) { // endless mode
            if (RemoteConfig.instance.Economy_MileStone_Endless_Detailed) {
                // tặng milestone tại các mốc star 1,2,3
                int round = indexNote / _endlessTrigger;
                int realNote = indexNote % _endlessTrigger;
                if (_mileStoneTrigger.Contains(realNote)) {
                    if (!isStaticBlock) {
                        int offsetMileStone = _mileStoneTrigger.IndexOf(realNote) + 1;
                        int valueMileStone = 0;
                        switch (offsetMileStone) {
                            case 1:
                                valueMileStone = remoteConfig.Economy_MileStone_1st_Value;
                                break;

                            case 2:
                                valueMileStone = remoteConfig.Economy_MileStone_2nd_Value;
                                break;

                            case 3:
                                valueMileStone = remoteConfig.Economy_MileStone_3rd_Value;
                                break;
                        }

                        int indexMileStone = round * 4 + offsetMileStone;
                        if (gameController.HaveRewardAtMileStone(indexMileStone)) {
                            GameItems.instance.mileStoneManager.SetMileStone(realNote, indexMileStone, valueMileStone,
                                parent);
                            return true;
                        } else {
                            // không có reward cho mốc milestone này
                            return false;
                        }
                    } else {
                        _mileStoneTrigger[_mileStoneTrigger.IndexOf(realNote)]++;
                        return false;
                    }
                }
            } else {
                // tặng milestone tại đầu endlessmode
                if (indexNote != 0 && indexNote % _endlessTrigger == 0) {
                    int round = (indexNote / _endlessTrigger);
                    int indexMileStone = round * 4;
                    if (gameController.HaveRewardAtMileStone(indexMileStone)) {
                        GameItems.instance.mileStoneManager.SetMileStone(0, indexMileStone,
                            remoteConfig.Economy_MileStone_Endless_Value, parent); // note đầu tiên
                        return true;
                    } else {
                        // không có reward cho mốc milestone này
                        return false;
                    }
                }
            }
        } else { // normal mode
            if (_mileStoneTrigger.Contains(indexNote) && indexNote < _endlessTrigger) {
                if (!isStaticBlock) {
                    int indexMileStone = _mileStoneTrigger.IndexOf(indexNote) + 1;
                    int valueMileStone = 0;
                    switch (indexMileStone) {
                        case 1:
                            valueMileStone = remoteConfig.Economy_MileStone_1st_Value;
                            break;

                        case 2:
                            valueMileStone = remoteConfig.Economy_MileStone_2nd_Value;
                            break;

                        case 3:
                            valueMileStone = remoteConfig.Economy_MileStone_3rd_Value;
                            break;
                    }

                    if (gameController.HaveRewardAtMileStone(indexMileStone)) {
                        GameItems.instance.mileStoneManager.SetMileStone(indexNote, indexMileStone, valueMileStone,
                            parent);
                        return true;
                    } else {
                        // không có reward cho mốc milestone này
                        return false;
                    }
                } else {
                    _mileStoneTrigger[_mileStoneTrigger.IndexOf(indexNote)]++;
                }
            }
        }

        return false;
    }

    private void GenerateFirstPlatforms(bool isContinue) {
        // Reset scale & spawn
        platformManager.SpawnFirstPlatforms();

        if (isContinue && Ball.b.endlessModeCount < remoteConfig.Reborn_StraightTile) {
            //move all tile to center road if it is continue
            platformManager.UpdateTileWhenContinue();

            //cho diamond line vào giữa
            foreach (Diamond diamond in _listDiamond) {
                if (diamond) {
                    diamond.transform.SetPositionX(0);
                }
            }

            foreach (RhythmNote rhythm in _listRhythmNote) {
                if (rhythm) {
                    rhythm.transform.SetPositionX(0);
                }
            }
        }

        if (isContinue) {
            Platform firstPlatform = platformManager.GetFirstPlatform();
            if (!firstPlatform) {
                Util.GoHome();
                return;
            }

            firstPlatform.HideTextInTile();
            firstPlatform.ResetHatItem();
        }
    }

    private void RewindPlatforms() {
        // Reset scale & spawn
        Dictionary<int, Vector3> caches = new Dictionary<int, Vector3>();
        for (int i = 0; i < platformManager.blockTotal; i++) {
            int index = indexSpawnNote + i + 1;
            caches.Add(index, mapManager.GetPlatformData(index).positionPlatform);
        }

        platformManager.SpawnFirstPlatforms(caches);

        Platform firstPlatform = platformManager.GetFirstPlatform();
        if (firstPlatform == null) {
            Util.GoHome();
            return;
        }

        firstPlatform.HideTextInTile();
        firstPlatform.ResetHatItem();
        firstPlatform.Hit4OnlyEffect();
    }

    public void gameStart() {
        if (remoteConfig.ImproveSensitive_IsEnable && remoteConfig.ImproveSensitive_UseNewNotes &&
            gameController.isUpdateSensitive) {
            SuperpoweredSDK.instance.PauseMusic();
        } else {
            PlayMusic(gameController.continueCount > 0);
        }

        if (mainBlockQueue.Count > 0) {
            var block = mainBlockQueue.Dequeue();
            if (gameController.IsNewTutorialGamePlay) {
                gameController.passedTile = block;
            }
        }
    }

    public void PlayMusic(bool isContinue = false) {
        SuperpoweredSDK.instance.SetVolume(0);
        List<NoteData> notes;
        //Chon notedata luc
        if (mapManager.convertingData) {
            notes = NotesManager.instance.noteMidiDatas;
            mapManager.convertingData = false;
        } else
            notes = noteDatas;

        float t = 0;
        if (isContinue) {
            if (currentJumpNoteID == notes.Count - 1) {
                t = notes[0].timeAppear;
            } else {
                t = notes[currentJumpNoteID + 1].timeAppear; //+1 to get current tile
            }
        } else {
            t = notes[0].timeAppear;
            if (!GameController.enableEndless || (!ChallengeMode.IsActive && !EndlessIteration.IsActive)) {
                SuperpoweredSDK.instance.SetSpeed(1f);
            }
        }

        t += remoteConfig.FixLatency;

        OnStartMusic?.Invoke(t);
        SuperpoweredSDK.instance.SetPosition(t);
        if (t < 0) {
            StartCoroutine(PlayMusicDelay(t));
        } else {
            SuperpoweredSDK.instance.SmoothPlay(1f);
        }
    }

    IEnumerator PlayMusicDelay(float delayTime) {
        yield return new WaitForSeconds(delayTime);

        SuperpoweredSDK.instance.SmoothPlay(1f);
    }

    public void gamePrepare() {
        CurrentSpecialSection = IngameSectionType.Normal;
        _indexStartSpecialSection = notesManager.SpecialSection_Start;
        _indexEndSpecialSection = notesManager.SpecialSection_End;
        remainSkinTryInMap = MAXIMUM_COUNT_SKIN_TRY;
        //Debug.LogError("reset remain try skin count");

        notesManager.UpdateLastNoteData(GameController.enableEndless); //TH-395

        StopAllCoroutines();

        // set trigger to display text
        _highScoreTrigger = Configuration.GetBestScore(notesManager.song.path);
        UpdateTrigger(noteCount);

        GameItems.instance.starManager.Deactive();
        GameItems.instance.mileStoneManager.DeActive();
        //GameItems.instance.crown.Deactivate();

        currentJumpNoteID = 0;
        totalPerfectCount = 0;
        SetJumpCount(0);
        if (EndlessIteration.IsActive && gameController.isContinueEndless) {
            indexSpawnNote = currentJumpNoteID + Ball.b.endlessModeCount * noteCount - 1;
        } else {
            indexSpawnNote = -1;
        }

        if (!isReloadDifficult) {
            ResetSkin(false);
        }

        foreach (TransitionImage a in _colorySprites) {
            a.SwitchIndex(currentIndexSkin, 0);
        }

        float shortNoteDistance = notesManager.GetShortNoteDistance();
        float longNoteDuration = notesManager.GetLongNoteDuration();

        if (mapManager != null) {
            mapManager.Init(shortNoteDistance, longNoteDuration);
        }

        ResetGame(false);

        UpdatePositionTrees();

        if (remoteConfig.isEnableAnzu && AnzuManager.isInstanced) {
            AnzuManager.instance.UpdatePosWhenGamePrepare();
        }

        if (NotesManager.IsExistUpsideDown()) {
            if (MapManager.instance != null) {
                MapManager.instance.ForceSetReverse(false);
            }
        }

        if (remoteConfig.Adverty_IsEnable && AdvertyManager.instance != null) {
            AdvertyManager.instance.UpdatePosWhenGamePrepare();
        }
    }

    internal void SetJumpCount(int value) {
        jumpCount = value;
        OnChangeJumpCount?.Invoke();
    }

    private void UpdateTrigger(int totalNote) {
        // _percentTrigger = new List<int> {
        //     (int)(totalNote * 0.2f),
        //     (int)(totalNote * 0.4f),
        //     (int)(totalNote * 0.6f),
        //     (int)(totalNote * 0.8f)
        // };

        if (_percentTrigger != null) {
            _percentTrigger.Clear();
        } else {
            _percentTrigger = ListPool<int>.Get();
        }

        _percentTrigger.Add((int) (totalNote * 0.2f));
        _percentTrigger.Add((int) (totalNote * 0.4f));
        _percentTrigger.Add((int) (totalNote * 0.6f));
        _percentTrigger.Add((int) (totalNote * 0.8f));

        if (LiveEventManager.instance && LiveEventManager.instance.IsActiveEvent) {
            _itemEventTrigger = new List<int>();
            Dictionary<int, int> songEarn = LiveEventManager.instance.GetCurrencySongEarn();
            if (songEarn != null) {
                foreach (var pair in songEarn) {
                    _itemEventTrigger.Add((int) (totalNote * pair.Key * 0.01f));
                }
            }
        }

        if (_starsTrigger != null) {
            _starsTrigger.Clear();
        } else {
            _starsTrigger = ListPool<int>.Get();
        }

        if (_mileStoneTrigger != null) {
            _mileStoneTrigger.Clear();
        } else {
            _mileStoneTrigger = ListPool<int>.Get();
        }

        if (remoteConfig.Economy_MileStone_Enable) {
            _starsTrigger.Add((int) (totalNote * (RemoteConfig.instance.Economy_MileStone_1st_Percent / 100f)));
            _starsTrigger.Add((int) (totalNote * (RemoteConfig.instance.Economy_MileStone_2nd_Percent / 100f)));
            _starsTrigger.Add((int) (totalNote * (RemoteConfig.instance.Economy_MileStone_3rd_Percent / 100f)));

            if (_starsTrigger[2] > totalNote - 1) {
                _starsTrigger[2] = totalNote - 1;
            }

            _mileStoneTrigger.Add((int) (totalNote * (RemoteConfig.instance.Economy_MileStone_1st_Percent / 100f)));
            _mileStoneTrigger.Add((int) (totalNote * (RemoteConfig.instance.Economy_MileStone_2nd_Percent / 100f)));
            _mileStoneTrigger.Add((int) (totalNote * (RemoteConfig.instance.Economy_MileStone_3rd_Percent / 100f)));

            if (_mileStoneTrigger[2] > totalNote - 1) {
                _mileStoneTrigger[2] = totalNote - 1;
            }
        } else {
            _starsTrigger.Add((int) (totalNote * 0.33f));
            _starsTrigger.Add((int) (totalNote * 0.66f));
            _starsTrigger.Add(totalNote - 1);

            _mileStoneTrigger.Add((int) (totalNote * 0.33f));
            _mileStoneTrigger.Add((int) (totalNote * 0.66f));
            _mileStoneTrigger.Add(totalNote - 1);
        }

        if (!GameController.enableEndless) {
            _endlessTrigger = 999999;
        } else { //enable Endless
            if (_starsTrigger[2] > totalNote - 2) {
                // vì xóa note đầu của round sau sẽ trùng với note cuối của round trước
                _starsTrigger[2] = totalNote - 2;
            }

            if (_mileStoneTrigger[2] > totalNote - 2) {
                _mileStoneTrigger[2] = totalNote - 2;
            }

            _endlessTrigger = totalNote;
        }

        if (MysteryDoorManager.CanEarnDrill(GameController.enableEndless)) {
            MysteryDoorManager.instanceSafe.ProcessKey(totalNote);
            _canEarnDrill = true;
        } else {
            _canEarnDrill = false;
        }

        _canCollectMilestoneToken = MilestoneEvent.PrepareTokensIngame(totalNote, GameController.enableEndless);
    }

    public void gameContinue() {
        RecheckBoosterHyperStar(currentJumpNoteID);
        CurrentSpecialSection = IngameSectionType.Normal;
        if (currentJumpNoteID >= _indexStartSpecialSection) {
            switch (notesManager.SpecialSection_Type) {
                case IngameSectionType.Mirror:
                    gameController.sectionType = IngameSectionType.Normal;
                    if (currentJumpNoteID >= _indexEndSpecialSection) {
                        _indexEndSpecialSection = -1; //end
                    } else {
                        _indexStartSpecialSection = currentJumpNoteID;
                    }

                    break;

                case IngameSectionType.HyperBoost:
                case IngameSectionType.ZicZac:
                    if (currentJumpNoteID >= _indexEndSpecialSection) {
                        _indexEndSpecialSection = -1; //end
                    } else {
                        _indexStartSpecialSection = currentJumpNoteID;
                    }

                    break;

                case IngameSectionType.UpsideDown:
                //nothing
                case IngameSectionType.Normal:
                    break;
            }
        }

        _ziczacTile = null;

        if (mapManager.convertingData)
            mapManager.RevertData();

        indexSpawnNote = currentJumpNoteID + Ball.b.endlessModeCount * noteCount - 1;
        ResetSkin(true);

        if (gameController.IsNewTutorialGamePlay) {
            SoftResetGame(true);
        } else {
            ResetGame(true);
        }

        if (GameItems.instance.starManager.IsActive() && GameItems.instance.starManager.transform.parent != null &&
            GameItems.instance.starManager.transform.parent.parent.gameObject ==
            platformManager.GetFirstPlatform().gameObject) {
            GameItems.instance.starManager.Deactive();
        }
        //GameItems.instance.crown.Deactivate();

        UpdatePositionTrees();

        if (remoteConfig.isEnableAnzu && AnzuManager.instance != null) {
            AnzuManager.instance.UpdatePosWhenGameContinue();
        }

        if (remoteConfig.Adverty_IsEnable && AdvertyManager.instance != null) {
            AdvertyManager.instance.UpdatePosWhenGameContinue();
        }

        if (NotesManager.IsExistUpsideDown()) {
            bool inReverseMode = currentJumpNoteID >= notesManager.SpecialSection_Start &&
                                 currentJumpNoteID < notesManager.SpecialSection_End;
            if (MapManager.instance != null) {
                MapManager.instance.ForceSetReverse(inReverseMode);
            }
        }
    }

    public void UpdatePositionTrees() {
        foreach (KeyValuePair<Transform, Vector3> tree in colorySpritesPosition) {
            Transform transform1 = tree.Key.transform;
            Vector3 pos = transform1.position;
            pos.z = tree.Value.z + Follow.instance.transform.position.z;
            transform1.position = pos;
        }
    }

    private bool isFirstPlay;

    private void ResetGame(bool isContinue) {
        _prevPosX = 0;

        Platform.updatedTextures = false;
        Platform.lastSwitchTime = 0;

        blockScaleCurrent = _blockScale;
        Ball.b.UpdateMinDistanceWithBlock();
        Ball.b.ResetInteractableWithPlatform();

        mainBlockQueue.Clear();
        ClearDiamondLine();
        isFirstPlay = !NotesManager.instance.song.IsPlayed();
        GenerateFirstPlatforms(isContinue);

        Platform firstPlatform = platformManager.GetFirstPlatform();
        if (firstPlatform == null) {
            Util.GoHome();
            return;
        }

        Vector3 position = firstPlatform.transCache.position;
        if (firstPlatform.isSlideTile)
            position.z = firstPlatform.platformData.positionZ;
        Ball.b.EnableBall(position.z, position.x);

        if (isContinue) {
            if (firstPlatform.noteID > 0) {
                currentJumpNoteID = firstPlatform.noteID - 1;
            } else { //noteID = 0
                if (Ball.b.isPassRound1) { //nếu đang là endless mode thì quay trở lại cuối vòng trước
                    currentJumpNoteID = noteCount - 1;
                } else {
                    currentJumpNoteID = 0;
                }
            }
        } else { // not continue
        }

        // Update Camera position
        if (!gameController.isSensitiveClosing) {
            Follow.instance.UpdatePosition();
        }

        platformManager.MoveFirstPlatform();
    }

    private void SoftResetGame(bool isContinue) {
        _prevPosX = 0;

        Platform.updatedTextures = false;
        Platform.lastSwitchTime = 0;

        blockScaleCurrent = _blockScale;
        Ball.b.UpdateMinDistanceWithBlock();

        mainBlockQueue.Clear();
        ClearDiamondLine();
        isFirstPlay = !NotesManager.instance.song.IsPlayed();
        // GenerateFirstPlatforms(isContinue);
        RewindPlatforms();

        Platform firstPlatform = platformManager.GetFirstPlatform();
        if (firstPlatform == null) {
            Util.GoHome();
            return;
        }

        Vector3 position = firstPlatform.transCache.position;
        if (firstPlatform.isSlideTile)
            position.z = firstPlatform.platformData.positionZ;
        Ball.b.EnableBall(position.z, position.x);

        if (isContinue) {
            if (firstPlatform.noteID > 0) {
                currentJumpNoteID = firstPlatform.noteID - 1;
            } else { //noteID = 0
                if (Ball.b.isPassRound1) { //nếu đang là endless mode thì quay trở lại cuối vòng trước
                    currentJumpNoteID = noteCount - 1;
                } else {
                    currentJumpNoteID = 0;
                }
            }
        }

        Follow.instance.UpdatePositionAfterRewind();
    }

    private bool IsUseFirstMoodFromNoteData() {
        if (!remoteConfig.Musicalization_MoodChange_IsEnable) {
            return false;
        }

        if (isMusicalizationType) {
            return true;
        }

        if (!remoteConfig.NotesDifficult_IsEnable) {
            return false;
        }

        if (notesManager.song.IsLocalSong()) {
            return false; //local song
        }

        return true;
    }

    private void ResetSkin(bool isContinue) {
        platformManager.AwakeBlock();
        if (!isContinue) {
            //set mood cho lần chơi đầu
            if (IsUseFirstMoodFromNoteData()) {
                NoteData.Mood firstMood = noteDatas[currentJumpNoteID].mood;
                StartCoroutine(mapManager.MoodChange(firstMood));
            } else {
                NoteData.Mood firstMood = NoteData.Mood.Mild; // default is Mild
                StartCoroutine(mapManager.MoodChange(firstMood));
            }

            if (remoteConfig.Theme_Default_SkinSet >= 0) {
                byte forceColorId = byte.MaxValue;
                NoteData.Mood moodType = (NoteData.Mood) remoteConfig.Theme_Default_SkinSet;
                var colorType = mapManager.GetColorTypeByMood(moodType);
                for (byte i = 0; i < skinSet.Length; i++) {
                    if (skinSet[i].colorType == colorType) {
                        forceColorId = i;
                        break;
                    }
                }

                if (forceColorId >= 0) {
                    SwitchBackGround(0f, forceColorId);
                }
            }
        }
    }

    public void gameOver() {
        platformManager.GameOver();
        GameItems.instance.gameOver();
        Ball.b.DeactivateBall();
        SuperpoweredSDK.instance.SmoothPause(0.3f, true, 0.6f);

        ClearDiamondLine();
    }

    public void UpdateBg() {
        if (!onTransition && skinSet.Length > 1) {
            SwitchBackGround(remoteConfig.SwitchColorDuration);
        }
    }

    // Collect the block and spawner again
    public void HitByCollector(Platform bl) {
        if (bl == null)
            return;

        platformManager.RecycleTile(bl); //recycle tile
        //use old logic to spawn tile
        if (bl.hitted && remoteConfig.GetTileTotalFront() <= 0) {
            SpawnTile(); //spawn tile
        }
    }

    public void ChangeBlockSize(int perfectCount) {
        if (remoteConfig.ImproveSensitive_IsEnable && gameController.isUpdateSensitive) {
            return;
        }

        platformManager.ChangeBlockSize(perfectCount, blockScaleCurrent, _tileMinSize, _tileMaxSize);
    }

    public void SwitchBackGround(float duration, byte indexNextSkin = byte.MaxValue) {
        byte currentIndex = currentIndexSkin;
        onTransition = true;
        if (indexNextSkin >= skinSet.Length) {
            indexNextSkin = (byte) ((currentIndex + 1) % skinSet.Length);
        }

        if (_bgCoroutine != null) {
            StopCoroutine(_bgCoroutine);
        }

        if (PlayerImageController.instance != null) {
            if (currentIndex != indexNextSkin) {
                PlayerImageController.instance.SlideIn(indexNextSkin);
                PlayerImageController.instance.SlideOut(currentIndex);
            } else {
                PlayerImageController.instance.UpdatePosition(indexNextSkin);
            }
        }

        _bgCoroutine = StartCoroutine(BackgroundTransition(currentIndex, indexNextSkin, duration));
        onBgChange?.Invoke(currentIndex, indexNextSkin, duration);

        //Logger.Log("[SwitchBackGround] " + currentIndex + " => " + indexNextSkin);
    }

    private IEnumerator BackgroundTransition(byte current, byte next, float duration) {
        beforeIndexSkin = currentIndexSkin;
        currentIndexSkin = next;

        SkinSet prevSkin = GetSkinSet(current);
        SkinSet currentSkin = GetSkinSet(currentIndexSkin);

        Image currentSkinBG = currentSkin.bg;
        if (currentSkinBG != null) {
            currentSkinBG.transform.gameObject.SetActive(true);
            mapManager.SetRainbowPosition(currentSkinBG.transform, next);
            if (mapManager.sun != null) {
                mapManager.sun.ChangeSun(next, currentSkinBG.transform);
            }
        }

        if (GameItems.instance.wednesdayController != null) {
            GameItems.instance.wednesdayController.UpdateSpeedCharacter(currentIndexSkin);
        }

        if (GameItems.instance.edmController != null)
            GameItems.instance.edmController.SetFaceSkin(next);

        if (GameItems.instance.hiphopRetroController != null)
            GameItems.instance.hiphopRetroController.SetThemeEnvironment(next);

        if (GameItems.instance.hiphopModernController != null)
            StartCoroutine(GameItems.instance.hiphopModernController.PlayMoodChange());

        if (GameItems.instance.vfxController != null) {
            GameItems.instance.vfxController.ChangeMood(currentIndexSkin);
        }

        OnChangeMood?.Invoke(currentIndexSkin, currentSkin);

        if (currentSkin.invironmentImages.Count > 0) {
            for (byte i = 0; i < currentSkin.invironmentImages.Count; i++)
                currentSkin.invironmentImages[i].gameObject.SetActive(true);
        }

        if (skinSet[current].bg != null) {
            skinSet[current].bg.CrossFadeAlpha(0, duration, false);
        }

        if (skinSet[current].invironmentImages.Count > 0) {
            for (byte i = 0; i < skinSet[current].invironmentImages.Count; i++)
                skinSet[current].invironmentImages[i].CrossFadeAlpha(0, duration, false);
        }

        if (currentSkinBG != null) {
            currentSkinBG.canvasRenderer.SetAlpha(0);
            currentSkinBG.CrossFadeAlpha(1, duration, false);
        }

        if (currentSkin.invironmentImages.Count > 0) {
            for (byte i = 0; i < currentSkin.invironmentImages.Count; i++)
                currentSkin.invironmentImages[i].CrossFadeAlpha(1, duration, false);
        }

        platformManager.SetBlockSkin(currentIndexSkin, true);
        platformManager.SetCloneSkin(currentIndexSkin, true);
        foreach (var a in _colorySprites) {
            a.SwitchIndex(currentIndexSkin, duration);
        }

        StartCoroutine(mapManager.ChangeStartColorEffect(skinSet[current].tileColor, skinSet[next].tileColor));
        StartCoroutine(mapManager.ChangeGridColor(skinSet[current].gridColor, skinSet[next].gridColor));

        float countTime = 0;
        bool updatedEffect = false;
        while (countTime < duration) {
            countTime += Time.deltaTime;

            if (countTime > duration / 2) {
                if (!updatedEffect) {
                    updatedEffect = true;
                    platformManager.SetEffectTexture(currentIndexSkin);
                }
            }

            platformManager.SwitchColor(countTime / duration, current, prevSkin, next, currentSkin);

            platformManager.UpdatePlatformMaterial(countTime / duration, current, next);
            yield return null;
        }

        platformManager.FinishedSwitchColor(currentIndexSkin);
        platformManager.FinishUpdatePlatformMaterial(currentIndexSkin);
        if (currentIndexSkin != current) {
            Image image = skinSet[current].bg;
            if (image != null) {
                image.transform.gameObject.SetActive(false);
            }

            if (skinSet[current].invironmentImages.Count > 0) {
                for (byte i = 0; i < skinSet[current].invironmentImages.Count; i++)
                    skinSet[current].invironmentImages[i].gameObject.SetActive(false);
            }
        }

        onTransition = false;
    }

    public static int GetPerfectMax(int level = 0) {
        switch (level) {
            case 1:
                return RemoteConfig.instance.Perfect_VisualEffectMax / 2;

            case 2:
                return RemoteConfig.instance.Perfect_VisualEffectMax;

            case 3:
                return RemoteConfig.instance.Perfect_VisualEffectMax;

            default:
                return RemoteConfig.instance.Perfect_VisualEffectMax;
        }
    }

    public void MoveTreeOutSide() {
        if (_tfGroupTreeLeft != null) {
            _tfGroupTreeLeft.DOKill();
            _tfGroupTreeLeft.DOLocalMoveX(-120, 0.5f);
        }

        if (_tfGroupTreeRight != null) {
            _tfGroupTreeRight.DOKill();
            _tfGroupTreeRight.DOLocalMoveX(120, 0.5f);
        }
    }

    public void MoveTreeInSide() {
        if (_tfGroupTreeLeft != null) {
            _tfGroupTreeLeft.DOKill();
            _tfGroupTreeLeft.DOLocalMoveX(_localTreeLeftX, 0.5f);
        }

        if (_tfGroupTreeRight != null) {
            _tfGroupTreeRight.DOKill();
            _tfGroupTreeRight.DOLocalMoveX(_localTreeRightX, 0.5f);
        }
    }

    /// <summary>
    /// IsActiveDiamond
    /// </summary>
    /// <returns></returns>
    private bool IsActiveDiamond() {
        return platformManager.IsActiveDiamond();
    }

    public SkinSet GetSkinSet(int index) {
        if (0 <= index && index < skinSet.Length) {
            return skinSet[index];
        } else {
            return skinSet[0];
        }
    }

    public int GetIndexSkinSetFromStageType(string stageNameType) {
        if (!int.TryParse(stageNameType, out int currentColor)) {
            currentColor = 0;
        }

        return currentColor;
    }

    public float GetPositionXOfPlatform(int indexNoteSpawn, ref PlatformData platformData, float prevPosX) {
        if (platformData.currNoteObj == null) {
            return 0;
        }

        if (indexNoteSpawn <= 3 || platformData.platformType == PlatformType.MOOD_FULL ||
            platformData.currNoteObj.nodeID == NoteData.NoteIDLastNoteForHuman ||
            gameController.game != GameStatus.LIVE) {
            // ~~~~~~~~~~~~ 4 note đầu hoặc type = FULL thì đặt ở giữa ~~~~~~~~~~~~
            return 0;
        }

        PlatformData prev = mapManager.GetPlatformData((indexNoteSpawn - 1) % noteCount);
        if (prev.longNoteType is LongNoteType.Highway or LongNoteType.Pipe) {
            return prevPosX;
        }

        if (gameController.IsNewTutorialGamePlay) {
            return GetPositionXByTutorial(indexNoteSpawn, ref platformData, prevPosX);
        }

        if (remoteConfig.NotesDifficult_IsEnable && notesManager.isBpm) {
            NotesManager.Difficulties difficulties = notesManager.Difficulty;
            if (difficulties == NotesManager.Difficulties.Easy) {
                return GetPositionXByLevelEasyBpm(indexNoteSpawn, ref platformData, prevPosX);
            }

            if (difficulties == NotesManager.Difficulties.Endless) {
                // ~~~~~~~~~~~~ dùng logic cũ của game ~~~~~~~~~~~~+
                return GetPositionXByOldLogic(indexNoteSpawn, ref platformData, prevPosX);
            }
        }

        if (isPitch && (!_isApplyLevelBotFilter ||
                        platformData.currNoteObj.timeAppear > remoteConfig.LevelBot_PitchFromSecond)) {
            // ~~~~~~~~~~~~ nếu là logic của musicalization ~~~~~~~~~~~~
            return GetPositionXByPitch(indexNoteSpawn, ref platformData, prevPosX);
        }

        // ~~~~~~~~~~~~ nếu là logic cũ của game ~~~~~~~~~~~~+
        return GetPositionXByOldLogic(indexNoteSpawn, ref platformData, prevPosX);
    }

    #region Get PositionX By Level Easy

    private float GetPositionXByTutorial(int indexNoteSpawn, ref PlatformData platformData, float prevPosX) {
        float positionX;
        var config = gameController._tutorialGamePlayConfig;

        if (config.FollowMidi) {
            positionX = GetPositionXByPitch(indexNoteSpawn, ref platformData, prevPosX);
        } else {
            if (platformData.IsShortDistance()) {
                positionX = prevPosX; //thẳng hàng
            } else {
                bool isChangeLine = Random.Range(0, 999) % 100 < config.ChanceToNotStraight;
                if (isChangeLine) {
                    //lệch hàng
                    int[] lines = config.OnlyOnLane;
                    positionX = GetXOfOtherLine(prevPosX, lines, _lineMap);
                } else {
                    positionX = prevPosX; //thẳng hàng
                }
            }
        }

        return positionX;
    }

    private float GetXOfOtherLine(float prevPosX, int[] validLine, float[] lines) {
        List<float> validPos = new List<float>();
        for (int i = 0; i < validLine.Length; i++) {
            int index = validLine[i] - 1;
            if (index < 0)
                continue;
            if (index >= lines.Length)
                continue;
            if (Mathf.Approximately(prevPosX, lines[index]))
                continue;

            validPos.Add(lines[index]);
        }

        if (validPos.Count == 0) {
            Logger.EditorLogError("error");
            return 0f;
        } else {
            return validPos[UnityEngine.Random.Range(0, 999) % validPos.Count];
        }
    }

    private float GetPositionXByLevelEasyBpm(int indexNoteSpawn, ref PlatformData platformData, float prevPosX) {
        float positionX;

        if (platformData.distanceType == PlatformData.DistanceType.VeryShort) {
            positionX = prevPosX; //thẳng hàng
        } else if (Ball.b.endlessModeCount > 0) {
            positionX = GetPositionXByLevelEasy_after30s(indexNoteSpawn, ref platformData, prevPosX);
        } else if (platformData.currNoteObj.timeAppear <= 15) {
            positionX = GetPositionXByLevelEasy_first15s(prevPosX);
        } else if (platformData.currNoteObj.timeAppear <= 30) {
            positionX = GetPositionXByLevelEasy_next15s(prevPosX);
        } else {
            positionX = GetPositionXByLevelEasy_after30s(indexNoteSpawn, ref platformData, prevPosX);
        }

        Debug.Log($"[GetPositionXByLevelEasy] indexNoteSpawn {indexNoteSpawn} => positionX {positionX}");
        return positionX;
    }

    private float GetPositionXByLevelEasy_first15s(float prevPosX) {
        float[] mapPositions = {_lineMap[1], _lineMap[2], _lineMap[3]}; //line 2 3 4
        int preIndex = CalcIndex(prevPosX, mapPositions);
        if (preIndex == 0 || preIndex == 2) { //line 2 4
            return Random.Range(0, 2) == 0 ? prevPosX : mapPositions[1]; //50%
        } else { // line 3
            int ranIndex = Random.Range(0, 3); //0 1 2
            return mapPositions[ranIndex];
        }
    }

    private float GetPositionXByLevelEasy_next15s(float prevPosX) {
        float[] mapPositions = _lineMap; //line 1 2 3 4 5
        int preIndex = CalcIndex(prevPosX, mapPositions);
        int index = 0;

        switch (preIndex) {
            case 0:
                index = CalcIndexRandom(0, 1, 2);
                break;

            case 1:
                index = CalcIndexRandom(0, 1, 2, 3);
                break;

            case 2:
                index = CalcIndexRandom(0, 1, 2, 3, 4);
                break;

            case 3:
                index = CalcIndexRandom(1, 2, 3, 4);
                break;

            case 4:
                index = CalcIndexRandom(2, 3, 4);
                break;
        }

        index = Mathf.Clamp(index, 0, mapPositions.Length);
        return mapPositions[index];
    }

    private float GetPositionXByLevelEasy_after30s(int indexNoteSpawn, ref PlatformData platformData, float prevPosX) {
        PlatformType platformType = platformData.platformType;
        float[] mapPositions = GetMapPositions(indexNoteSpawn, ref platformData, true); //line 1 2 3 4 5

        int randomX = Random.Range(0, mapPositions.Length);
        float tempPosition = mapPositions[randomX];

        float positionX;
        if (Random.Range(0, 4) != 0) { //0 1 2 3 => 75%
            if (prevPosX * tempPosition > 0) {
                positionX = -tempPosition;
            } else {
                if (Mathf.Approximately(prevPosX, 0) && Mathf.Approximately(tempPosition, 0)) {
                    if (platformType == PlatformType.NORMAL || platformType == PlatformType.MOVING ||
                        Random.Range(0, 2) == 0) {
                        randomX += 1;
                    } else {
                        randomX -= 1;
                    }
                }

                randomX = Mathf.Clamp(randomX, 0, mapPositions.Length - 1);
                positionX = mapPositions[randomX];
            }
        } else {
            positionX = tempPosition;
        }

        return positionX;
    }

    private int CalcIndexRandom(params int[] range) {
        int index = Random.Range(0, range.Length);
        return range[index];
    }

    private int CalcIndex(float prevPosX, float[] mapPositions) {
        for (int index = 0; index < mapPositions.Length; index++) {
            if (Mathf.Approximately(prevPosX, mapPositions[index])) {
                return index;
            }
        }

        return mapPositions.Length / 2;
    }

    #endregion

    #region Get PositionX By Pitch

    private float GetPositionXByPitch(int indexNoteSpawn, ref PlatformData platformData, float prevPosX) {
        float positionX;
        //Bổ sung 2022-05-12: Apply LevelBot Filter
        NoteData.Pitch pitch = platformData.currNoteObj.pitch; //xác định 1 trong 5 line
        if (platformData.distanceType == PlatformData.DistanceType.VeryShort) { //distance = shortNoteDistance
            positionX = prevPosX; //thẳng hàng
        } else if (pitch != NoteData.Pitch.None) { //nếu note có xác định line
            if (!NotesManager.instance.FakeTile_Follow_Pitch && platformData.platformType == PlatformType.FAKE &&
                (pitch == NoteData.Pitch.Line2 || pitch == NoteData.Pitch.Line4)) {
                //Chỉ hiện thị với line 1 3 5 => theo logic cũ
                positionX = GetPositionXByOldLogic(indexNoteSpawn, ref platformData, prevPosX);
            } else { // theo logic line map của musicalization
                positionX = _isApplyLevelBotFilter
                    ? GetLevelBotPositionX(ref platformData, prevPosX, _lineMap[(int) pitch])
                    : _lineMap[(int) pitch];
            }
        } else { //nếu note không xác định line (motif = true) thì theo logic cũ
            positionX = GetPositionXByOldLogic(indexNoteSpawn, ref platformData, prevPosX);
        }

        //Debug.Log($"[GetPositionXByPitch] indexNoteSpawn {indexNoteSpawn} => positionX {positionX}");
        return positionX;
    }

    private float GetPositionXForceByPitch(ref PlatformData platformData) {
        NoteData.Pitch pitch = platformData.currNoteObj.pitch; //xác định 1 trong 5 line
        return _lineMap[(int) pitch];
    }

    private float GetPositionXForceByPitch(NoteData.Pitch pitch) {
        return _lineMap[(int) pitch];
    }

    private float GetLevelBotPositionX(ref PlatformData platformData, float prevPosX, float currentPositionX) {
        float positionX;
        float lvbFilterMaxTime = filterMaxTime;
        if (platformData.minDistance < lvbFilterMaxTime) {
            float shift = (currentPositionX - prevPosX) * remoteConfig.GetLevelBotHardPercent();
            positionX = prevPosX + shift;
        } else {
            positionX = currentPositionX;
        }

        //Debug.Log($"[GetLevelBotPositionX] indexNoteSpawn {platformData.currentIndexNote} => positionX {positionX}");
        return positionX;
    }

    #endregion

    #region Get PositionX By Old Logic

    private float GetPositionXByOldLogic(int indexNoteSpawn, ref PlatformData platformData, float prevPosX) {
        float positionX = platformData.IsShortDistance()
            ? GetNearPositionX(ref platformData, prevPosX)
            : GetStandardPositionX(indexNoteSpawn, ref platformData, prevPosX);
        //Debug.Log($"[GetPositionXByOldLogic] indexNoteSpawn {indexNoteSpawn} => positionX {positionX}");
        return positionX;
    }

    private float GetNearPositionX(ref PlatformData platformData, float prevPosX) {
        float shift = Mathf.Lerp(0, 4f,
            (platformData.minDistance - filterMiddleTime - 0.06f) / (filterMaxTime - filterMiddleTime));
        float positionX = prevPosX + (Random.Range(0, 2) == 0 ? 1 : -1) * shift;
        if (Mathf.Abs(positionX) > distanceMinMax4[1]) {
            positionX = prevPosX - Mathf.Sign(prevPosX) * shift;
        }

        //Debug.Log($"[GetNearPositionX] indexNoteSpawn {platformData.currentIndexNote} => positionX {positionX}");
        return positionX;
    }

    private float GetStandardPositionX(int indexNoteSpawn, ref PlatformData platformData, float prevPosX) {
        float positionX;
        PlatformType platformType = platformData.platformType;
        float[] mapPositions = GetMapPositions(indexNoteSpawn, ref platformData, false);

        int randomX = Random.Range(0, mapPositions.Length);
        if (Random.Range(0, 4) != 0) { //0 1 2 3 => 75%
            if (prevPosX * mapPositions[randomX] > 0) {
                positionX = -mapPositions[randomX];
            } else {
                if (Mathf.Approximately(prevPosX, 0) && Mathf.Approximately(mapPositions[randomX], 0)) {
                    if (platformType == PlatformType.NORMAL || platformType == PlatformType.MOVING ||
                        Random.Range(0, 2) == 0) {
                        randomX += 1;
                    } else {
                        randomX -= 1;
                    }
                }

                randomX = Mathf.Clamp(randomX, 0, mapPositions.Length - 1);
                positionX = mapPositions[randomX];
            }
        } else {
            positionX = mapPositions[randomX];
        }

        if (!notesManager.isBpm) {
            positionX = PopulatePositionX(positionX, platformData.minDistance, ref platformType);
        }

        return positionX;
    }

    private float[] GetMapPositions(int indexNoteSpawn, ref PlatformData platformData, bool isForceLineMap) {
        PlatformType platformType = platformData.platformType;

        float[] mapPositions = _gridMap; //default

        switch (platformType) {
            case PlatformType.NORMAL:
            case PlatformType.MOVING:
                mapPositions = isForceLineMap ? _lineMap : GetNormalMapPositions(indexNoteSpawn);
                break;

            case PlatformType.FAKE:
            case PlatformType.LATE_FAKE:
                if (NotesManager.instance.FakeTile_Follow_Pitch) { //support 5 lines
                    mapPositions = _lineMap;
                } else { // support 3 main lines
                    mapPositions = _gridMap;
                }

                break;

            case PlatformType.SKEW:
            case PlatformType.SKEW_SPIRAL:
                mapPositions = _gridMap;
                break;

            case PlatformType.COLORING:
                if (platformData.stage == Stage.COLORING) {
                    //do stage này mỗi note có 2 tile màu thật + màu giả
                    mapPositions = _gridMap;
                } else if (platformData.stage == Stage.COLOR_CHOOSING) {
                    //stage này mỗi note chỉ có 1 tile
                    mapPositions = isForceLineMap ? _lineMap : GetNormalMapPositions(indexNoteSpawn);
                }

                break;

            default:
                //default
                //type COLOR_CHOOSING sẽ được update vị trí sau
                mapPositions = _gridMap;
                break;
        }

        return mapPositions;
    }

    private float PopulatePositionX(float positionX, float distance, ref PlatformType platformType) {
        if (distance < filterMaxTime && !notesManager.isBpm) {
            if (platformType != PlatformType.NORMAL && platformType != PlatformType.MOVING) {
                //bởi vì moving tile được tách từ mormal tile ra
                return 0;
            } else {
                return Mathf.Lerp(0, 3f, (distance - filterMiddleTime) / (filterMaxTime - filterMiddleTime)) *
                       Mathf.Sign(positionX);
            }
        } else {
            return positionX;
        }
    }

    private float[] GetNormalMapPositions(int spawnCount) {
        int mapPositionIndex = spawnCount / remoteConfig.Distance_Interval;
        float maxDistance = 0;
        float minDistance = 0;
        if (mapPositionIndex >= 3 || Ball.b.endlessModeCount > 0) {
            maxDistance = distanceMinMax4[1];
            minDistance = distanceMinMax4[0];
        } else if (mapPositionIndex == 2) {
            maxDistance = _distanceMinMax3[1];
            minDistance = _distanceMinMax3[0];
        } else if (mapPositionIndex == 1) {
            maxDistance = _distanceMinMax2[1];
            minDistance = _distanceMinMax2[0];
        } else if (mapPositionIndex == 0) {
            maxDistance = _distanceMinMax1[1];
            minDistance = _distanceMinMax1[0];
        }

        if (maxDistance > remoteConfig.GetTileMaxPositionX()) {
            maxDistance = _tileMaxPositionX;
        }

        _normalMap[1] = minDistance;
        _normalMap[2] = minDistance + 0.2f * (maxDistance - minDistance);
        _normalMap[3] = minDistance + 0.4f * (maxDistance - minDistance);
        _normalMap[4] = minDistance + 0.6f * (maxDistance - minDistance);
        _normalMap[5] = minDistance + 0.8f * (maxDistance - minDistance);
        _normalMap[6] = maxDistance;
        _normalMap[7] = maxDistance;
        _normalMap[8] = maxDistance;
        _normalMap[9] = maxDistance;
        _normalMap[10] = maxDistance;
        _normalMap[11] = maxDistance;
        _normalMap[12] = maxDistance;
        _normalMap[13] = maxDistance;

        return _normalMap;
    }

    #endregion

    private bool ShowEndlessText(int indexNote, Platform bl) {
        if (indexSpawnNote <= platformManager.blockTotal) { //những tile đầu tiên ko có text endless
            return false;
        }

        if (indexNote % _endlessTrigger == 0 && GameController.enableEndless) {
            bl.SetText(string.Format(LocalizationManager.instance.GetLocalizedValue(CONFIG_STRING.Round_x),
                indexNote / _endlessTrigger + 1));
            return true;
        }

        return false;
    }

    private bool ShowHighScore(int indexNote, bool isStaticBlock, Platform bl) {
        // Active text block if going to highscore or endless mode (increase the block square size and active text)
        if (indexNote == _highScoreTrigger && indexNote > platformManager.blockTotal) { //is first tiles
            if (!isStaticBlock) {
                if (bl.platformType == PlatformType.LONG_TILE || bl.platformType == PlatformType.BREAKABLE_LONG_TILE) {
                    //not show percent text on long tile
                    _highScoreTrigger++;
                    return false;
                }

                bl.SetText(LocalizationManager.instance.GetLocalizedValue(CONFIG_STRING.NewHighScore));

                return true;
            } else {
                _highScoreTrigger++;
            }
        }

        return false;
    }

    private bool ShowPercentText(int indexNote, bool isStaticBlock, Platform bl) {
        if (indexSpawnNote <= platformManager.blockTotal) { //is first tiles
            return false;
        }

        if (_percentTrigger.Contains(indexNote) && indexNote < _endlessTrigger) {
            if (!isStaticBlock) {
                if (bl.platformType == PlatformType.LONG_TILE || bl.platformType == PlatformType.BREAKABLE_LONG_TILE) {
                    //not show percent text on long tile
                    _percentTrigger[_percentTrigger.IndexOf(indexNote)]++;
                    return false;
                }

                bl.SetText(_percentStrings[_percentTrigger.IndexOf(indexNote)]);
                return true;
            } else {
                _percentTrigger[_percentTrigger.IndexOf(indexNote)]++;
            }
        }

        return false;
    }

    private bool IsShowItemEvent(int indexNote, bool isStaticBlock) {
        if (!remoteConfig.LiveEvent_Enable) {
            return false;
        }

        if (GameController.enableEndless) {
            // Nếu config false -> thì chỉ ăn round 1, các round khác sẽ false
            if (!LiveEventManager.instance.IsEarnTokenInEndless()) {
                if (Ball.b.endlessModeCount != 0) {
                    return false;
                }
            }
        }

        int realNote = indexNote % _endlessTrigger;
        if (_itemEventTrigger.Contains(realNote)) {
            if (!isStaticBlock) {
                return true;
            } else {
                _itemEventTrigger[_itemEventTrigger.IndexOf(realNote)]++;
            }
        }

        return false;
    }

    public bool IsShowDiamond(int indexNote) {
        //Dont show diamond if noteLength enable
        if (isMusicalizationType && remoteConfig.Musicalization_NoteLength_IsEnable &&
            !remoteConfig.Economy_Diamond_Ingame_StartDiamond) {
            return false;
        }

        if (remoteConfig.Economy_IsEnable) {
            if (NotesManager.instance.song.isTutorialSong) {
                // TH-1194: Hide the progress bar and diamond in tutorial game play
                return false;
            }

            if (remoteConfig.Economy_Diamond_Ingame_StartDiamond) {
                if (!Utils.IsInternetReachable && NotesManager.instance.song.IsPlayed()) {
                    return false;
                }

                // Hiển thị X tiles đầu tiên với mỗi tiles chứa Y Diamonds (nếu user ăn hết thì tổng diamond nhận được là X * Y)
                if (indexNote != 0 && indexNote <= remoteConfig.Economy_Diamond_Ingame_XStartTiles) {
                    return true;
                }

                if (remoteConfig.Economy_Diamond_Ingame_XStartTilesRepeat && GameController.enableEndless) {
                    return (indexNote % _endlessTrigger) != 0 && (indexNote % _endlessTrigger) <=
                        remoteConfig.Economy_Diamond_Ingame_XStartTiles;
                }
            }
        }

        // Hiển thị diamond kiểu cũ  theo remoteConfig.Diamond_SpawnRate
        if (indexSpawnNote <= platformManager.blockTotal)
            return false; //Những tile đầu tiên sẽ ko có diamond
        if (IsActiveDiamond())
            return false; // Trên tile đã có diamond rồi
        if (Random.Range(0, remoteConfig.Diamond_SpawnRate) == 1)
            return true;

        return false;
    }

    #region Process Stage Name

    public void ProcessTypeFakeForStageFakeTile(Platform platform, PlatformData platformData, Vector3 positionPlatform,
                                                float flyTime) {
        if (platformData.fakeTile != NoteData.FakeTile.NONE) {
            if ((platformData.fakeTile & NoteData.FakeTile.LEFT) == NoteData.FakeTile.LEFT) {
                platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[0], flyTime);
            }

            if ((platformData.fakeTile & NoteData.FakeTile.MIDDLE_LEFT) == NoteData.FakeTile.MIDDLE_LEFT) {
                platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[1], flyTime);
            }

            if ((platformData.fakeTile & NoteData.FakeTile.MIDDLE) == NoteData.FakeTile.MIDDLE) {
                platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[2], flyTime);
            }

            if ((platformData.fakeTile & NoteData.FakeTile.MIDDLE_RIGHT) == NoteData.FakeTile.MIDDLE_RIGHT) {
                platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[3], flyTime);
            }

            if ((platformData.fakeTile & NoteData.FakeTile.RIGHT) == NoteData.FakeTile.RIGHT) {
                platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[4], flyTime);
            }
        } else {
            string gamePlayType = platformData.stageNameType;
            if (!string.IsNullOrEmpty(gamePlayType) &&
                string.Equals(gamePlayType, "FAKE_3", StringComparison.Ordinal)) {
                if (NotesManager.instance.FakeTile_Follow_Pitch) { // support full 5 lines (1,2,3,4,5)
                    int line = GetLine(positionPlatform.x, _lineMap);
                    switch (line) {
                        case 0:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[2], flyTime);
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[4], flyTime);
                            break;

                        case 1:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[3], flyTime);
                            break;

                        case 2:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[0], flyTime);
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[4], flyTime);
                            break;

                        case 3:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[1], flyTime);
                            break;

                        case 4:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[0], flyTime);
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _lineMap[2], flyTime);
                            break;
                    }
                } else { // support 3 main lines (1,3,5)
                    int line = GetLine(positionPlatform.x, _gridMap);
                    switch (line) {
                        case 0:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _gridMap[1], flyTime);
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _gridMap[2], flyTime);
                            break;

                        case 1:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _gridMap[0], flyTime);
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _gridMap[2], flyTime);
                            break;

                        case 2:
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _gridMap[0], flyTime);
                            platformManager.AddFakeTile(FakeTileStyle.Fake_Normal, platform, _gridMap[1], flyTime);
                            break;
                    }
                }
            } else { //FAKE_2
                platformManager.AttachCloneGrid2(platform, _gridMap, flyTime);
            }
        }
    }

    public Platform ProcessCustomFakeTile(Platform platform, FakeTileStyle fakeTileStyle, PlatformData platformData,
                                          Vector3 positionPlatform, float flyTime) {
        float originalPosX = positionPlatform.x;
        float fakePosX;
        if (originalPosX <= 0) {
            fakePosX = originalPosX + (_lineMap[2] - _lineMap[0]);
        } else {
            fakePosX = originalPosX - (_lineMap[2] - _lineMap[0]);
        }

        var fakeTile = platformManager.AddFakeTile(fakeTileStyle, platform, fakePosX, flyTime);
        return fakeTile;
    }

    public void ProcessConveyorFakeTile(Platform platform, PlatformData platformData, Vector3 positionPlatform,
                                        float flyTime) {
        var dataLine = GetConveyorRange();
        float originalPosX = positionPlatform.x;
        float offset = (dataLine.maxEnd - dataLine.minStart) / 3f;
        float fakePosLX = ValidPosX(originalPosX + offset);
        platformManager.AddFakeTile(FakeTileStyle.Fake_Conveyor, platform, fakePosLX, flyTime);

        var fakePosRX = ValidPosX(originalPosX - offset);
        platformManager.AddFakeTile(FakeTileStyle.Fake_Conveyor, platform, fakePosRX, flyTime);

        float ValidPosX(float posX) {
            if (posX > dataLine.maxEnd) {
                posX -= (dataLine.maxEnd - dataLine.minStart);
            }

            if (posX < dataLine.minStart) {
                posX += (dataLine.maxEnd - dataLine.minStart);
            }

            return posX;
        }
    }

    private int GetLine(float posX, float[] lines) {
        for (int i = 0; i < lines.Length; i++) {
            if (Mathf.Approximately(posX, lines[i])) {
                return i;
            }
        }

        return -1;
    }

    public void ProcessStageNameMovingTile(Platform platform, bool isBlockText, bool isStaticBlock,
                                           PlatformData platformData) {
        //bool isMoving = !notesManager.isDefaultSong || Configuration.instance.enableContentTool || _isLocalSong;
        //Dont move with LongDuration (Draw diamond)

        int indexLastPlatform = platform.noteID == 0 ? mapManager.GetTotalPlatform() - 1 : platform.noteID - 1;
        PlatformData prevPlatformData = mapManager.GetPlatformData(indexLastPlatform);
        bool isLongDuration = platformData.isLongDuration ||
                              (prevPlatformData.isInited && prevPlatformData.isLongDuration);

        //TH-1490: khối block text vẫn được di chuyển bình thường
        bool isMoving = !( /*isBlockText ||*/ isStaticBlock) && !isLongDuration;
        bool forward = Random.Range(0, 2) == 0;

        if (isMusicalizationType) {
            //moving to extract position
            float timeAppear = platformData.currNoteObj.timeAppear;

            //if (currentJumpNoteID == mapManager.GetTotalPlatform() - 1) { //End of road
            //    gameController.timePlay = notesManager.noteDatas[0].timeAppear;
            //}

            float timePlay = gameController.timePlay;
            float appear = 0;

            PlatformData platformDataFrom = mapManager.GetPlatformDataByTime(timePlay);
            platformDataFrom = platformDataFrom.isInited
                ? platformDataFrom
                : mapManager.GetPlatformData(currentJumpNoteID + 1);
            if (!platformDataFrom.isInited) {
                appear = (timeAppear - timePlay) / Ball.b.timeScale;
            } else {
                float timeBetweenTile = mapManager.GetTimeBetweenTile(
                    platformDataFrom.currentIndexNote, platformData.currentIndexNote);
                appear = timeBetweenTile;
            }

            if (appear > 0) {
                platform.Moving(isMoving, platformData.movingType, forward, appear);
            } else {
                platform.Moving(isMoving, platformData.movingType, forward);
            }
        } else {
            platform.Moving(isMoving, platformData.movingType, forward);
        }

        if (isMoving) {
            lastMovingObject = platform;
        }
    }

    private void ProcessPlatformTypeFull(Platform bl, PlatformData platformData) {
        int indexSkinSet = GetIndexSkinSetFromStageType(platformData.stageNameType);
        bl.SetSkinImmediately(indexSkinSet);
    }

    private void ProcessPlatformTypeColoringForStageColoring(Platform platform, PlatformData platformData) {
        //update main color
        int indexSkinSet = GetIndexSkinSetFromStageType(platformData.stageNameType);
        platform.SetSkinImmediately(indexSkinSet);

        if (!platformData.IsShortDistance()) { //add fake color nếu không phải là note ngắn
            int indexFakeSkin = indexSkinSet + 1;
            if (indexFakeSkin >= Spawner.s.skinSet.Length) {
                indexFakeSkin = 0;
            }

            platformManager.AttachColorTileGrid2(platform, _gridMap, platformData, indexFakeSkin, PlatformType.FAKE);
        }
    }

    #endregion

    public Vector3 GetBlockScale() {
        return _blockScale;
    }

    public SkinSet GetCurrentSkin() {
        return skinSet[currentIndexSkin];
    }

    #region Color Choosing

    private int _indexSkinDefaultForColorChoosing;

    Platform ProcessPlatformTypeColorChoosingForStageColorChoosing(Platform platform, PlatformData platformData) {
        _indexSkinDefaultForColorChoosing = currentIndexSkin; //màu default của dãy tile đằng sau

        //lấy 2 skin tiếp sau đó cho vào 2 tile đầu để player chọn
        int indexSkinChoose1 = (_indexSkinDefaultForColorChoosing + 1) % skinSet.Length;
        int indexSkinChoose2 = (_indexSkinDefaultForColorChoosing + 2) % skinSet.Length;

        //main tile color 1
        platform.SetSkinImmediately(indexSkinChoose1);

        //if (!platformData.isShortDuration) { //nếu không phải là note ngắn
        //main tile color 2
        Platform colorTile = platformManager.AttachColorTileGrid2(platform, _gridMap, platformData, indexSkinChoose2,
            PlatformType.COLOR_CHOOSING);

        colorTile.noteID = platform.noteID;
        colorTile.nextNoteDistance = platformData.nextNoteObj.distance;

        return colorTile;

        // } else { //nếu là note ngắn thì thôi, ko thêm tile bên cạnh nữa
        //     return null;
        // }
    }

    public float GetSlot01ColorChoosing() {
        float positionX02 = (_gridMap[0] + _gridMap[1]) / 2;
        return positionX02;
    }

    public float GetSlot02ColorChoosing() {
        float positionX02 = (_gridMap[1] + _gridMap[2]) / 2;
        return positionX02;
    }

    public void ChangeColorForStageColorChoosing(byte indexSkin, int indexNote) {
        //lưu lại index skin player đã chọn để dùng lúc spawn tiếp theo cho stage color choosing
        _indexSkinDefaultForColorChoosing = indexSkin;

        //Đổi màu toàn bộ tile trong stage color choosing
        platformManager.ChangeColorForStageColorChoosing(indexSkin, indexNote);

        //đổi backgroud sang màu mà player đã chọn
        SwitchBackGround(1f, indexSkin);
    }

    #endregion

    private void ProcessTypeLateFakeForStageFakeTile(Platform realTile, PlatformData platformData) {
        Platform fakeLate = platformManager.SpawnTile(platformData.platformType, ref platformData);

        fakeLate.noteID = realTile.noteID;
        fakeLate.platformData = platformData /*.Clone()*/;
        fakeLate.platformType = PlatformType.FAKE;
#if UNITY_EDITOR
        fakeLate.name = fakeLate.noteID + "_" + fakeLate.platformType;
#endif

        // Set position
        Vector3 newPosition = PlatformManager.GetNearPositionGrid(realTile, _gridMap);
        fakeLate.transCache.position = newPosition;

        fakeLate.FlyEffect(platformData.flyTime);
        CompletedSpawnPlatform(fakeLate, false);

        fakeLate.SetActiveEfxFocus(true);

        realTile.AddFakeTiles(fakeLate);
    }

    private Vector3 GetSkewPosition(Platform realTile, bool isLeft) {
        Vector3 newPosition = PlatformManager.GetNearPositionGrid(realTile, _gridMap);
        newPosition.x = isLeft ? GetSlot01ColorChoosing() : GetSlot02ColorChoosing();
        return newPosition;
    }

    private void ProcessTypeSkewForStageSkew(Platform realTile, ref PlatformData platformData, ref bool isLeftSkewTile,
                                             ref Vector3 positionPlatform) {
        float time = platformData.currNoteObj.distance /*/ Ball.b.timeScale*/; // thời gian cách note trước
        float minTime = MapManager.instance.GetMinTimeStage();
        bool isTooClose = time < minTime;
        var prevPlatformData = MapManager.instance.GetPlatformData((realTile.noteID - 1) % noteCount);
        bool isSkew = true;
        if (isTooClose) {
            switch (prevPlatformData.platformType) {
                case PlatformType.SKEW:
                    // tile trước là skew ->> là skew và cùng bên với tile trước
                    isLeftSkewTile = prevPlatformData.positionPlatform.x < 0;
                    isSkew = true;
                    break;

                default:
                    // tile trước không phải là skew, mà tile này lại quá gần -> vẫn giữ là khối normal
                    isSkew = false;
                    break;
            }
        } else {
            if (platformData.stage == Stage.SKEW_OPPOSITE) {
                isLeftSkewTile = !isLeftSkewTile; // ngược bên với tile trước
            } else {
                if (prevPlatformData.platformType != PlatformType.SKEW) {
                    // đây là tile đầu của chuỗi SKEW_STRAIGHT -> xác định dãy bên trái hay bên phải dựa vào tile trước x>0
                    isLeftSkewTile = prevPlatformData.positionPlatform.x < 0;
                }
            }
        }

        if (!isSkew) {
            // không phải khối skew thì thôi, k care nữa
            realTile.name += "_SKEW_PASSED";
            return;
        }

        realTile.name += (isLeftSkewTile ? "_LEFT" : "_RIGHT");
        positionPlatform = GetSkewPosition(realTile, isLeftSkewTile);

        platformData.positionPlatform = positionPlatform;
        realTile.transCache.position = positionPlatform;

        float angle = MapManager.instance.GetAngleOfTile();

        realTile.SetActiveEfxFocus(true);
        //rotate tile
        if (isLeftSkewTile) {
            realTile.transCache.rotation = Quaternion.Euler(0, 0, -angle);
        } else {
            realTile.transCache.rotation = Quaternion.Euler(0, 0, angle);
        }
    }

    public SkinSet GetSkinSet(int index, bool isCustom, bool isMoodTile = false) {
        if (isCustom && !isMoodTile) {
            return 0 <= index && index < skinSetCustom.Length ? skinSetCustom[index] : skinSetCustom[0];
        } else {
            return GetSkinSet(index);
        }
    }

    private ThemeColor GetThemeColorFromScene() {
        ThemeColor themeColor = new ThemeColor {tileColors = new TileColor [skinSet.Length]};

        for (int i = 0; i < skinSet.Length; i++) {
            SkinSet skin = skinSet[i];
            TileColor tileColor = themeColor.tileColors[i];
            tileColor.main = skin.tileColor;
            tileColor.sub = skin.fireColor;
            tileColor.colorType = skin.colorType;
            themeColor.tileColors[i] = tileColor; //because TileColor is struct
        }

        return themeColor;
    }

    private void CheckAndMakeDiamondLine(int currNoteId) {
        if (!remoteConfig.Musicalization_NoteLength_IsEnable) {
            return;
        }

        if (remoteConfig.Economy_IsEnable && notesManager.song.isTutorialSong) {
            // TH-1194: Hide the progress bar and diamond in tutorial game play
            return;
        }

        if (currNoteId < 1) {
            return;
        }

        if (gameController.isSensitiveClosing) {
            return;
        }

        PlatformData platformData = MapManager.instance.GetPlatformData(currNoteId);
        PlatformData prevPlatformData = MapManager.instance.GetPlatformData(currNoteId - 1);

        if (!platformData.isInited || !prevPlatformData.isInited || !prevPlatformData.IsLongNote ||
            platformManager.GetNumberBlock() == 1) {
            return;
        }

        List<Vector3> lineDiamonds;
        if (IsUseLongNoteV2 && prevPlatformData.longNoteType == LongNoteType.Volley) {
            lineDiamonds = GetLineOrbs(ref prevPlatformData, ref platformData);
        } else {
            lineDiamonds = GetLineDiamonds(ref prevPlatformData, ref platformData);
        }

        if (lineDiamonds.Count <= 0) {
            ListPool<Vector3>.Release(lineDiamonds);
            return;
        }

        Platform preBlock = platformManager.GetBlockByNoteID(currNoteId - 1);
        if (preBlock != null && preBlock.isTileTrySkin && lineDiamonds.Count > 0) {
            lineDiamonds.RemoveAt(0); //remove first diamond when this tile has TrySkin
        }

        switch (prevPlatformData.longNoteType) {
            case LongNoteType.LineDiamond:
                foreach (Vector3 position in lineDiamonds) {
                    //Spawn diamond
                    CreateDiamond(1, CurrencyEarnSource.COLLECT_DIAMOND, position);
                }

                break;

            case LongNoteType.LineMusicNote:
                foreach (Vector3 position in lineDiamonds) {
                    //Spawn rhythmNote
                    CreateMusicNote(position, true);
                }

                break;

            case LongNoteType.Hitchhike:
                if (!IsUseLongNoteV2)
                    break;

                if (preBlock is HitchhikePlatform hitchBl) {
                    hitchBl.CreateLineOfOrbs(_longNoteConfig.HitchHike_Orbs_time,
                        _longNoteConfig.HitchHike_Orbs_Variant, platformData.positionPlatform);
                } else {
                    Logger.EditorLogError("Long note", "Not type of HitchhikePlatform");
                }

                break;

            case LongNoteType.LineDiamondFirstTime:
                if (!isFirstPlay)
                    break;

                foreach (Vector3 position in lineDiamonds) {
                    //Spawn diamond
                    CreateDiamond(1, CurrencyEarnSource.COLLECT_DIAMOND, position);
                }

                break;

            case LongNoteType.LineRainbow:
                if (preBlock != null) {
                    preBlock.isRainbowTile = true;
                    GameItems.instance.MakeRainbow(prevPlatformData.positionPlatform, platformData.positionPlatform);
                }

                break;

            case LongNoteType.Volley: // TH - 1866: dạng nốt nhạc
                foreach (Vector3 position in lineDiamonds) {
                    //Spawn rhythmNote
                    CreateMusicNote(position, true);
                }

                break;
        }

        ListPool<Vector3>.Release(lineDiamonds);
    }

    public void CreateMusicNote(Vector3 position, bool usingPerfect) {
        RhythmNote spawnRhythm = platformManager.PrefRhythmNote.Spawn(transform, position);
        spawnRhythm.SetUsingPerfect(usingPerfect);
        _listRhythmNote.Add(spawnRhythm);
    }

    public Diamond CreateDiamond(int value, CurrencyEarnSource source, Vector3 position) {
        Diamond spawnDiamond = platformManager.prefDiamond.Spawn(transform, position);
        spawnDiamond.Init(value, source, -1);
        _listDiamond.Add(spawnDiamond);
        return spawnDiamond;
    }

    public Diamond CreateDiamond(int value, CurrencyEarnSource source, Platform platform, float positionZ) {
        Diamond spawnDiamond = platformManager.prefDiamond.Spawn(transform, Vector3.zero);
        spawnDiamond.Init(value, source, platform.platformData.currentIndexNote);
        spawnDiamond.transform.SetParent(platform.sq.transform);
        platform.ActiveDiamond(spawnDiamond, positionZ);
        return spawnDiamond;
    }

    public Vector3 CalculateDiamondOnRoad(float positionZ) {
        return platformManager.GetDiamondRoadAt(positionZ);
    }

    public Platform CheckSpawnInTile(float zPosition) {
        return platformManager.CheckSpawnInTile(zPosition);
    }

    private List<Vector3> GetLineDiamonds(ref PlatformData prevPlatformData, ref PlatformData platformData) {
        float x1 = prevPlatformData.positionPlatform.x;
        float x2 = platformData.positionPlatform.x;

        float h = Ball.b.GetJumpHeight(prevPlatformData.nextNoteObj.distance);
        float z1 = prevPlatformData.positionPlatform.z;
        float z2 = platformData.positionPlatform.z;
        float z3 = (z1 + z2) / 2;

        float a = h / (z3 - z1) / (z3 - z2);
        float b = -(z1 + z2) * a;
        float c = -a * z1 * z1 - b * z1;
        // y = az2 + bz + c

        float posZEndDiamond =
            prevPlatformData.positionZ + prevPlatformData.currNoteObj.duration * Ball.b.GetBalLSpeed();
        float stepDiamond = 3; //distance
        int totalDiamond = Mathf.FloorToInt((posZEndDiamond - z1) / stepDiamond);
        List<Vector3> positions = ListPool<Vector3>.Get();
        for (int index = 0; index < totalDiamond; index++) {
            float posZ = z1 + index * stepDiamond; //xếp kim cương bắt đầu từ điểm đầu -> cuối
            //0.5f => kim cương đầu tiên nâng cao lên để tránh cảm giám bị chìm vào tile
            float y = index == 0 ? 0.5f : a * posZ * posZ + b * posZ + c;

            Vector3 position = new(x1 + (x2 - x1) * (posZ - z1) / (z2 - z1), //tính độ lệch x dựa vào z
                y, posZ);

            if (y > 0) { //valid
                positions.Add(position);
            }
        }

        return positions;
    }

    private List<Vector3> GetLineOrbs(ref PlatformData prevPlatformData, ref PlatformData platformData) {
        List<Vector3> positions = ListPool<Vector3>.Get();
        float x1 = prevPlatformData.positionPlatform.x;
        float x2 = platformData.positionPlatform.x;

        float h = Ball.b.GetJumpHeight(prevPlatformData.nextNoteObj.distance);
        float z1 = prevPlatformData.positionPlatform.z;
        float z2 = platformData.positionPlatform.z;
        float z3 = (z1 + z2) / 2;

        float a = h / (z3 - z1) / (z3 - z2);
        float b = -(z1 + z2) * a;
        float c = -a * z1 * z1 - b * z1;
        // y = az2 + bz + c
        int totalDiamond = (int) ((z2 - z1) / Ball.b.GetBalLSpeed() / _longNoteConfig.Volley_Orbs_time);
        if (totalDiamond <= 0) {
            return positions;
        }

        float stepDiamond = (z2 - z1) / totalDiamond;
        Vector3 tempPosition = Vector3.zero;
        for (int index = 0; index < totalDiamond; index++) {
            float posZ = z1 + (index + 0.5f) * stepDiamond; //xếp kim cương bắt đầu từ điểm đầu -> cuối
            float y = a * posZ * posZ + b * posZ + c;

            tempPosition.x = x1 + (x2 - x1) * (posZ - z1) / (z2 - z1); //tính độ lệch x dựa vào z
            tempPosition.y = y;
            tempPosition.z = posZ;

            if (y > 0) { //valid
                positions.Add(tempPosition);
            }
        }

        return positions;
    }

    public void ClearDiamondLine() {
        foreach (Diamond diamond in _listDiamond) {
            if (diamond != null) {
                diamond.Recycle();
            }
        }

        _listDiamond.Clear();

        foreach (var rhythmNote in _listRhythmNote) {
            if (rhythmNote != null) {
                rhythmNote.MakeRecycle();
            }
        }

        _listRhythmNote.Clear();
    }

    public void ReloadDifficult(bool isReplay, bool isBack = true) {
        StartCoroutine(IEReloadDifficult(isReplay, isBack));
    }

    private IEnumerator IEReloadDifficult(bool isReplay, bool isBack = true) {
        if (currentJumpNoteID >= noteDatas.Count) {
            Logger.LogError("ReloadDifficult: currentJumpNoteID is out of range");
            yield break;
        }

        var mood = noteDatas[currentJumpNoteID].mood;
        isReloadDifficult = true;
        yield return notesManager.LoadNoteData(notesManager.song);

        GameItems.instance.starManager.SetParent(null);
        GameItems.instance.mileStoneManager.Reset();

        yield return this.StartCoroutine(platformManager.RecycleAllTile(isBack));

        Awake();
        yield return null;

        gameController.ResetCountEvent();
        gameController.GamePrepare(isReplay);
        //Smooth Change
        if (mood != noteDatas[currentJumpNoteID].mood)
            MapManager.instance.PrepareMoodChange(MapManager.instance.GetPlatformData(0), true);
        isReloadDifficult = false;
    }

    public void ChangeNoteDatas() {
        int totalNote = notesManager.noteCount;
        indexSpawnNote = totalNote;
        UpdateTrigger(totalNote);
    }

    private void MapManager_OnMoodChange(NoteData.Mood mood, float transitionDuration) {
        byte nextSkinIndex = MapManager.instance.GetSkinId(mood);
        SwitchBackGround(transitionDuration, nextSkinIndex);
    }

    public int GetDiamondCount() {
        return _listDiamond.Count + _listRhythmNote.Count;
    }

    #region Live Event

    private HatItemScript _hatItemScript;

    // Performance optimization: Temp fields to avoid allocations in loops
    private float   _tempNextX;
    private Vector3 _tempNextEnd;
    private Vector3 _tempMidPoint;
    private SkinSet _tempSkinSet;

    private void InitHatItem() {
        if (_hatItemScript == null) {
            GameObject loadPrefab = Resources.Load<GameObject>(ResourcesPath.Items_HatItemEvent);
            if (loadPrefab != null) {
                GameObject character = Instantiate(loadPrefab, transform, false);
                character.SetActive(false);
                character.TryGetComponent(out _hatItemScript);
                _hatItemScript.CreatePool(1);
            } else {
                Logger.LogError("[InitHatItem] cannot load resource: " + ResourcesPath.Items_HatItemEvent);
            }
        }
    }

    public HatItemScript GetHatItem() {
        HatItemScript hatItemScript = _hatItemScript.Spawn();
        return hatItemScript;
    }

    private void ShowItemEvent(Platform bl) {
        Dictionary<int, int> songEarn = LiveEventManager.instance.GetCurrencySongEarn();
        List<int> trigger = songEarn.Keys.ToList();

        int indexOf = _itemEventTrigger.IndexOf(bl.noteID);
        int i = trigger[indexOf];
        bool tryGetValue = songEarn.TryGetValue(i, out int value);
        if (tryGetValue) {
            HatItemScript hatItemScript = _hatItemScript.Spawn(bl.GetContainer());
            hatItemScript.Init();
            hatItemScript.Active(value);
            bl.SetHatItemScript(hatItemScript);
        }
    }

    #endregion

    public void CreateOrbs(Platform platform, List<Vector3> positions) {
        //Spawn diamond
        int index = 0;
        foreach (var position in positions) {
            RhythmNote spawnRhythm = platformManager.PrefRhythmNote.Spawn(transform, position);
#if UNITY_EDITOR
            spawnRhythm.name = $"Rhythm [{platform.noteID}] {index}";
            index++;
#endif
            _listRhythmNote.Add(spawnRhythm);
            platform.AddOrb(spawnRhythm);
        }
    }

    public void SetPrevPosX(float x) {
        _prevPosX = x;
    }

    public (float minStart, float minEnd, float maxStart, float maxEnd) GetConveyorRange() {
        float range = (_lineMap[1] - _lineMap[0]);
        float min = _lineMap[0] - range * 2;
        float max = _lineMap[4] + range * 2;
        return (min, min + range, max - range, max);
    }

    public Vector3 CalculateStartConveyorPosition(PlatformData platformData, Vector3 targetPosition) {
        Vector3 startPosition = targetPosition;
        float currentNotJumpZ = Ball.b.transCache.position.z;
        float speed = remoteConfig.NewElements_Conveyor_Speed;
        float remainTime = (targetPosition.z - currentNotJumpZ) / Ball.b.GetBalLSpeed(); // thời gian cách ball
        float distanceMove = speed * remainTime;

        float range = (_lineMap[1] - _lineMap[0]);
        float min = _lineMap[0] - range * 2;
        float max = _lineMap[4] + range * 2;
        float totalLength = max - min;
        distanceMove -= totalLength * ((int) (distanceMove / totalLength));

        startPosition.x -= distanceMove;
        if (startPosition.x < min) {
            startPosition.x += totalLength;
        }

        return startPosition;
    }

    private Platform CreateMirrorTile(Platform platform) {
        var mirrorData = platform.platformData;
        if (Mathf.Approximately(mirrorData.positionPlatform.x, 0f)) {
            return null;
        }

        mirrorData.positionPlatform.x = -mirrorData.positionPlatform.x;

        Platform mirrorTile = platformManager.SpawnTile(platform.platformType, ref mirrorData);
#if UNITY_EDITOR
        mirrorTile.name = $"[{platform.noteID}] Mirror";
#endif
        mirrorTile.SetMirror(true);
        mirrorTile.SetMirror(platform);

        mirrorTile.noteID = platform.noteID;
        mirrorTile.platformData = mirrorData;
        mirrorTile.platformType = platform.platformType;
        mirrorTile.transCache.position = mirrorData.positionPlatform;
        mirrorTile.sectionType = platform.sectionType;

        Vector3 rotation = platform.transCache.localRotation.eulerAngles;
        rotation.y = -rotation.y;
        mirrorTile.transCache.rotation = Quaternion.Euler(rotation);

        if (platform.platformData.IsLongNote) {
            // mirrorTile.transCache.localScale = platform.transCache.localScale;
            mirrorTile.SetScaleAndPosition(platform.transCache.localScale, mirrorData.positionPlatform);
            mirrorTile.UpdatePivot(platform.GetPivotPosition());
        }

        mirrorTile.timeSlide = platform.timeSlide;
        mirrorTile.nextNoteDistance = platform.nextNoteDistance;
        //mirrorTile.text.SetActive(isBlockText); // active
        mirrorTile.ShowPerfectAnim();

        mirrorTile.isHitchhikeTile = platform.isHitchhikeTile;
        mirrorTile.isForceMove = platform.isForceMove;
        mirrorTile.FlyEffect(mirrorData.flyTime);
        CompletedSpawnPlatform(mirrorTile, false);

        mirrorTile.gameObject.SetActive(true);
        mirrorTile.ProcessMirrorTile();
        return mirrorTile;
    }

    public float GetTimeAppearFirstStar() {
        if (_starsTrigger.IsNullOrEmpty())
            return 0;
        if (noteDatas.IsNullOrEmpty())
            return 0;

        if (noteDatas.Count <= _starsTrigger[0])
            return 0;

        return noteDatas[_starsTrigger[0]].timeAppear;
    }

    public float GetIntervalTime(int start, int end) {
        if (start >= end) {
            return 0;
        }

        if (noteDatas.IsNullOrEmpty())
            return 0;
        if (noteDatas.Count <= start)
            return 0;
        if (noteDatas.Count <= end)
            return 0;

        return noteDatas[end].timeAppear - noteDatas[start].timeAppear;
    }
}