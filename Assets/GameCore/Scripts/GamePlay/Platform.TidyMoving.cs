using System.Collections.Generic;
using TilesHop.Cores.Boosters;
using UnityEngine;

public partial class Platform {
    private static readonly HashSet<PlatformType> _canTidyPlatformTypes = new() {
        PlatformType.NORMAL,
        PlatformType.FAKE,
        PlatformType.LONG_TILE,
        PlatformType.BREAKABLE_LONG_TILE,
        PlatformType.S_LONG_TILE,
        PlatformType.STRAIGHT_LONG_TILE,
    };
    
    private GameObject _tidyFx;
    private GameObject _tidyFxPrefab;
    private float      _tidyDelta;
    
    protected virtual void OnCompletedMoving() {
        if (BoosterManager.isInstanced && BoosterManager.instanceSafe.usingTidyTile) {
            BoosterTidyTile();
        }
    }

    private void BoosterTidyTile() {
        if (sectionType == IngameSectionType.HyperBoost) {
            return;
        }
        
        float tidyRatio = RemoteConfigBase.instance.Booster_TidyTile_Ratio;
        if (tidyRatio <= 0) {
            return;
        }

        if (platformData.currentIndexNote >= Spawner.s.StarTriggers[0]) {
            //only active note before star 1
            ShowTidyFx(false);
            return;
        }

        if(elementType != NoteElementType.None && elementType != NoteElementType.FakeTile) { return; }
        if (_fakeTileStyle != FakeTileStyle.None) { return; }
        if (GameControllerInstance.game != GameStatus.LIVE) { return; }
        
        if (platformType == PlatformType.FAKE) {
            ShowTidyFx(true);
            return;
        }
        
        if (_canTidyPlatformTypes.Contains(platformType)) {
            ShowTidyFx(true);
        }
    }

    private bool _hasDoneTidy;
    private bool CheckCanTidyTile() {
        if (_hasDoneTidy) {
            return false; 
        }

        if (!BoosterManager.isInstanced || !BoosterManager.instanceSafe.usingTidyTile) {
            return false;
        }
        
        if (sectionType == IngameSectionType.HyperBoost) {
            return false;
        }

        if (elementType != NoteElementType.None && elementType != NoteElementType.FakeTile) {
            return false;
        }
        
        float tidyRatio = RemoteConfigBase.instance.Booster_TidyTile_Ratio;
        if (tidyRatio <= 0) {
            return false;
        }
        
        // only active the Tile Tidy before the user reaches the first star milestone
        if (platformData.currentIndexNote >= Spawner.s.StarTriggers[0]) {
            return false;
        }

        
        if (_fakeTileStyle != FakeTileStyle.None) { return false; }
        if (GameControllerInstance.game != GameStatus.LIVE) { return false; }
        
        
        if (platformType == PlatformType.FAKE) {
            return true;
        }
        
        if (_canTidyPlatformTypes.Contains(platformType)) {
            return true;
        }

        return false;
    }
    
    public void MakeTileTidy() {
        if (CheckCanTidyTile()) {
            var  tidyRatio = RemoteConfigBase.instance.Booster_TidyTile_Ratio;
            var delta = flyEndPosition.x * tidyRatio;
                
            SetMove(delta);
            if (elementType is NoteElementType.FakeTile || platformType is PlatformType.FAKE) {
                foreach (var fakeTile in _fakeTiles) {
                    fakeTile.SetMove(delta);
                }
            }
        }
    }
    
    private void SetMove(float delta) {
        var newEndPos = flyEndPosition;
        newEndPos.x -= delta;
        _tidyDelta = delta;
        flyEndPosition = newEndPos;
        _hasDoneTidy = true;
    }

    public void ShowTidyFx(bool isShow) {
        if (!isShow) {
            if (_tidyFx != null) {
                _tidyFx.SetActive(false);
            }
            return;
        }

        if (_tidyFx == null) {
            if (_tidyFxPrefab == null) {
                _tidyFxPrefab = Resources.Load<GameObject>("Booster/TidySign");
            }

            if (_tidyFxPrefab == null) {
                return;
            }

            _tidyFx = Instantiate(_tidyFxPrefab, Spawner.s.platformManager.transform);
        }
        _tidyFx.SetActive(true);

        if (platformType == PlatformType.LONG_TILE) {
            _tidyFx.transform.position = pivot.position;
        } else {
            _tidyFx.transform.position = transCache.position;
        }
        _tidyFx.transform.SetLocalX(0f);
    }
}
