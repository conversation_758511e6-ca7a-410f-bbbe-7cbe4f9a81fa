using System;
using GamePlay.Levels;
using UnityEngine;
using System.Collections.Generic;
using DG.Tweening;
using Random = UnityEngine.Random;
using PathCreation;
using PathCreation.Examples;
using TilesHop.GamePlay.SpecialSection;

public class GameItems : MonoBehaviour {
    public                   StarManager      starManager;
    public                   MileStoneManager mileStoneManager;
    public                   ParticleSystem   brokenEffect;
    public                   ParticleSystem   brustEffect;
    public                   ParticleSystem   burnedEffect;
    [SerializeField] private GameObject       shadowBall;
    [SerializeField] private GameObject       shadowScaler;
    [SerializeField] private GameObject       objBestEffect;
    [SerializeField] private GameObject       ballMirror;

    // Use this for initialization
    public static GameItems        instance;
    public        BallTrailer      ballTrailer;
    public        CharacterTrailer charTrailer;

    public                   FlyNumber       flyNumber;
    [HideInInspector] public List<FlyNumber> flyNumbers = new List<FlyNumber>();

    [Header("TrySkin")] public int                    tryBallId = -1;
    public                     Transform              tfTrySkin;
    public                     CharacterManagerScript tryCharacterManagerScript;
    public                     BallManagerScript      tryBallManagerScript;

    public CharacterManagerScript currentCharacterManagerScript;
    public BallManagerScript      currentBallManagerScript;

    [Space] [SerializeField] private ParticleSystem[] confettiEffect;

    private GameObject _idleBouncingFX;
    private Vector3?   _originalScaleShadow;

    public WednesdayController    wednesdayController;
    public EDMController          edmController;
    public HipHopRetroController  hiphopRetroController;
    public HipHopModernController hiphopModernController;

    public VFXController vfxController;

    private PathCreator _pathCreatorPrefab;
    private GameObject  _rainbowImpact;

    private Transform _longNoteTrail;

    private ParticleSystem _vfxOpening;
    private ParticleSystem _vfxDead;
    private Tween          _tweenVFXOpening;
    private Tween          _tweenVFXDead;

    public  Action            OnSwapCharacterAndBall;
    public  Action<bool, int> OnTrailUpdate;
    private RemoteConfig      remoteConfig => RemoteConfigBase.instance;

    public GameObject ballShadow => shadowScaler;

    private void Awake() {
        instance = this;
        if (remoteConfig.MapSize > 1) {
            brustEffect.transform.localScale *= remoteConfig.MapSize;
            shadowBall.transform.localScale *= remoteConfig.MapSize;
            starManager.transform.localScale *= remoteConfig.MapSize;
        }
        starManager.gameObject.SetActive(true);

        InitFlyNumber();
        InitTrySkin();
        Platform.OnHit += OnHitPlatForm;
        Platform.OnReachPlatform += ProcessReachPlatform;
        LoadVFXFromResouces();
    }

    private void OnDestroy() {
        Platform.OnHit -= OnHitPlatForm;
        Platform.OnReachPlatform -= ProcessReachPlatform;
        
        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }

    private void LoadVFXFromResouces() {
        if (remoteConfig.VFX_Opening_IsEnable) {
            _vfxOpening = Instantiate(Resources.Load<ParticleSystem>("VFX/[TH]_Opening Effect2"), transform);
            _vfxOpening.gameObject.SetActive(false);
        }

        if (remoteConfig.VFX_Dead_IsEnable) {
            _vfxDead = Instantiate(Resources.Load<ParticleSystem>("VFX/[TH]_Dead Effect"), transform);
            _vfxDead.gameObject.SetActive(false);
        }
    }

    public void InitTrySkin() {
        if (!TrySkinFeature.IsActive) {
            currentBallManagerScript.transform.parent.gameObject.SetActive(false);
            tryCharacterManagerScript.transform.parent.gameObject.SetActive(false);
            return;
        }

        //current Skin
        int ballId = Ball.b.BallId;

        bool isCharacter = BallManager.instance.IsHumanBehaviour(ballId);

        currentCharacterManagerScript.SetActive(isCharacter);
        if (isCharacter) {
            currentCharacterManagerScript.Init(ballId);
            currentCharacterManagerScript.Rotate(180, 0.1f);
        }

        currentBallManagerScript.gameObject.SetActive(!isCharacter);
        if (!isCharacter) {
            currentBallManagerScript.Init(ballId);
        }

        currentBallManagerScript.transform.parent.gameObject.SetActive(false);

        //trySkin


        tryBallId = TrySkinFeature.GetIdTryBall();
        if (tryBallId < 0) {
            tryCharacterManagerScript.transform.parent.gameObject.SetActive(false);
        }

        if (tryBallId > 0 && TopBar.instance != null) {
            TopBar.instance.UpdateTrySkinImage(tryBallId);
        }

        isCharacter = BallManager.instance.IsHumanBehaviour(tryBallId);

        tryCharacterManagerScript.SetActive(isCharacter);
        if (isCharacter) {
            tryCharacterManagerScript.Init(tryBallId);
            tryCharacterManagerScript.Rotate(180, 0.1f);
        }

        tryBallManagerScript.gameObject.SetActive(!isCharacter);
        if (!isCharacter) {
            tryBallManagerScript.Init(tryBallId);
        }

        tryCharacterManagerScript.transform.parent.gameObject.SetActive(false);
    }

    private void InitFlyNumber(int num = 10) {
        if (!remoteConfig.EnablePerfectScoreFly)
            return;

        for (int i = 0; i < num; i++)
            flyNumbers.Add(Instantiate(flyNumber, ObjectPool.instance.CachedTransform));
    }

    public void ShowFlyNumber(int perfectCount) {
        for (int i = 0; i < flyNumbers.Count; i++) {
            if (!flyNumbers[i].gameObject.activeSelf) {
                flyNumbers[i].transform.position = Ball.b.transform.position;
                flyNumbers[i].SetNumber(perfectCount);
                break;
            }
        }
    }

    private void OnHitPlatForm(Platform block, int perfectCount) {
        bool isPlayConfettiEffect = block.PlayHitVFX(perfectCount);
        if (isPlayConfettiEffect) {
            PlayConfettiEffect();
        }

        ProcessReachPlatform(block, perfectCount);
    }

    private void ProcessReachPlatform(Platform block, int perfectCount) {
        // star effect
        if (starManager.IsActive() && starManager.transform.parent != null &&
            starManager.transform.parent.parent.gameObject == block.gameObject) {
            starManager.Eat();

            if (!RemoteConfig.instance.PlayNewSFX_Enable)
                SoundManager.PlayCoins(0.02f);
            
            if (RemoteConfig.instance.OnboardingFlow_EnableProgressBar) {
                Vector3 positionStar = starManager.GetPositionStar();
                TopBar.instance.MoveStar(positionStar, GameController.instance.stars);
            }
        }

        //milestone effect
        if (remoteConfig.Economy_MileStone_Enable &&
            !Configuration.instance.isGamePlayTutorial) {
            if (mileStoneManager.CanEat(block.noteID)) {
                var mileStone = mileStoneManager.GetMileStone(block.noteID);
                if (mileStone != null) {
                    mileStoneManager.Eat(block.noteID, mileStone);
                    GameController.instance.EatMileStone(mileStone.id, mileStone.amount);
                }
            }
        }

        // brust effect
        if (Spawner.s.themeId != ThemeManager.ThemeIdNoel && Spawner.s.themeId != ThemeManager.ThemeRadioStroke &&
            !(perfectCount >= Spawner.GetPerfectMax()) && !Ball.b.isInHyperBoostSection && !Ball.b.isInZicZacSection) {
            burnedEffect.gameObject.SetActive(false);
            brustEffect.gameObject.SetActive(true);
            brustEffect.transform.position =
                Util.SetPositionY(Ball.b.transCache.position, brustEffect.transform.position.y);
            brustEffect.Play();
        }

        if (remoteConfig.OnboardingFlow_EnableProgressBar) {
            if (remoteConfig.ProgressBar_StyleIndex == 3 || ChallengeMode.IsActive) {
                if (GameController.enableEndless) {
                    TopBar.instance.SetProcessByTileEndless(Spawner.s.currentJumpNoteID);
                } else { //Normal
                    if (!remoteConfig.ProgressByScore_IsEnable) {
                        SetProcessByTile(block.noteID);
                    }
                }
            } else {
                SetProcessByTile(block.noteID);
            }
        }
    }
    
    private void SetProcessByTile(int noteID) {
        float percent = 0;
        if (noteID == 0) {
            percent = Ball.b.isPassRound1 ? 1 : 0; //0 = start game
        } else {
            percent = Mathf.Clamp01((noteID + 1) / (float) NotesManager.instance.noteCount);
        }

        TopBar.instance.SetProcess(percent);
    }

    public void BrokenEffect(Vector3 pos, Color color) {
        brokenEffect.transform.position = pos;
        brokenEffect.gameObject.SetActive(true);
        ParticleSystem.MainModule main = brokenEffect.main;
        main.startColor = color;
        brokenEffect.Play();
    }

    public void SetActiveFeetEffect(bool isActive, bool isCharacter = false, Vector3 positionBall = default) {
        ChangeIdleBouncing(isActive && isCharacter);
        shadowBall.SetActive(isActive && !isCharacter);

        if (!isActive) return;
        if (isCharacter) {
            if (_idleBouncingFX != null && positionBall != default) {
                _idleBouncingFX.transform.SetPositionZ(positionBall.z);
            }
        } else {
            if (positionBall != default) {
                shadowScaler.transform.SetPositionZ(positionBall.z);
                var shadowBallTrans = shadowBall.transform.localPosition;
                shadowBallTrans.x = positionBall.x - 0.4f;
                shadowBall.transform.localPosition = shadowBallTrans;
            }
        }
    }

    public void HideShadowBall() {
        if (_originalScaleShadow == null) {
            _originalScaleShadow = shadowBall.transform.localScale;
        }

        shadowBall.transform.localScale = Vector3.zero;
        SetActiveFeetEffect(false);
    }

    public void SetAnimShadowBall(float animTime) {
        if (_originalScaleShadow == null) {
            _originalScaleShadow = shadowBall.transform.localScale;
        }

        shadowBall.transform.localScale = Vector3.zero;
        shadowBall.transform.DOScale((Vector3)_originalScaleShadow, animTime).SetEase(Ease.Linear);
    }

    public void ShowFxWin(bool isShow) {
        if (isShow) {
            float positionZ = Ball.b.transCache.GetPositionZ();
            objBestEffect.transform.SetPositionZ(positionZ);
        }

        objBestEffect.SetActive(isShow);
    }

    /// <summary>
    /// TH-1338: [DEV] Tích hợp hiệu ứng Opening theo vfx/concept
    /// Chỉ hiển thị tại đầu game + revive. các trường hợp khác sẽ không hiện
    /// </summary>
    /// <param name="position"> Vị trí ball</param>
    /// <param name="time">Thời gian show effect</param>
    public void ShowOpeningVFX(Vector3 position, float time = 4f) {
        if (_vfxOpening == null) return;
        _tweenVFXOpening?.Kill();

        position.y -= 0.5f;
        _vfxOpening.transform.position = position;
        _vfxOpening.gameObject.SetActive(true);
        _vfxOpening.Play();
        _tweenVFXOpening = DOVirtual.DelayedCall(time, () => { _vfxOpening.gameObject.SetActive(false); });
    }

    /// TH-1305: Tích hợp hiệu ứng Dead theo vfx/concept
    /// </summary>
    /// <param name="position">Vị trí show vfx</param>
    /// <param name="showTime">Thời gian show vfx</param>
    public void ShowDeadVFX(Vector3 position, float showTime = 1.5f) {
        if (_vfxDead == null) return;
        _tweenVFXDead?.Complete();
        position.y = Mathf.Max(position.y, -7f);
        _vfxDead.transform.position = position;
        _vfxDead.gameObject.SetActive(true);
        _vfxDead.Play();
        _tweenVFXDead = DOVirtual.DelayedCall(showTime, () => { _vfxDead.gameObject.SetActive(false); });
    }


    public static readonly string            _Alpha       = "_Alpha";
    private                List<PathCreator> _listRainbow = new List<PathCreator>();

    public PathCreator MakeRainbow(Vector3 position, Vector3 endPosition) {
        PathCreator pathCreator = CreateRainbow();

        pathCreator.StartPoint = position;
        pathCreator.EndPoint = endPosition;
        float startX = position.x;
        float endX = endPosition.x;

        float distance = Vector3.Distance(endPosition, position);
        float rate = 1f;

        float height = (Ball.b.isHumanPlayer) ? 8f : 10f;
        pathCreator.bezierPath.SetPoint(0, endPosition - position);
        pathCreator.bezierPath.SetPoint(1,
            Vector3.right * (endX - startX) * 2f / 3f + Vector3.forward * distance * rate * 2f / 3f +
            Vector3.up * height * rate);
        pathCreator.bezierPath.SetPoint(2,
            Vector3.right * (endX - startX) * 1f / 3f + Vector3.forward * distance * rate * 1f / 3f +
            Vector3.up * height * rate);
        pathCreator.bezierPath.SetPoint(3, Vector3.zero);

        pathCreator.transform.position = position;
        pathCreator.gameObject.SetActive(true);

        var _roadMeshCreator = pathCreator._roadMesh;
        _roadMeshCreator.AssignMeshComponents();
        _roadMeshCreator.AssignMaterials();
        _roadMeshCreator.CreateRoadMesh();
        // rainbowMat.SetFloat(_Alpha, 1f);
        _roadMeshCreator.meshRenderer.materials[0].SetFloat(_Alpha, 1f);

        pathCreator.InitializeEditorData(false);

        return pathCreator;
    }

    private PathCreator CreateRainbow() {
        foreach (var rainbow in _listRainbow) {
            if (!rainbow.gameObject.activeSelf) {
                return rainbow;
            }
        }

        if (_pathCreatorPrefab == null) {
            _pathCreatorPrefab = Resources.Load<PathCreator>("Rainbow");

            _rainbowImpact = Instantiate(Resources.Load<GameObject>("Rainbow_Impact_fx"), Ball.b.transform);
            _rainbowImpact.transform.localScale = Vector3.one * 0.666f;
            _rainbowImpact.transform.localPosition = Vector3.down * 0.333f;
        }

        var newRainbow = Instantiate(_pathCreatorPrefab, this.transform);
        newRainbow._roadMesh = newRainbow.GetComponent<RoadMeshCreator>();
        _listRainbow.Add(newRainbow);
        newRainbow.gameObject.SetActive(false);
        return newRainbow;
    }

    public void DisableRainbow(PathCreator rainbow) {
        rainbow.gameObject.SetActive(false);
    }

    public void RainbowImpactStatus(bool isEnable) {
        if (_rainbowImpact == null) return;
        _rainbowImpact.SetActive(isEnable);
    }

    public void SetRainbowAlpha(PathCreator rainbow, float value) {
        // rainbowMat.SetFloat(_Alpha, value);
        rainbow._roadMesh.meshRenderer.materials[0].SetFloat(_Alpha, value);
    }

    public void gameOver() {
        foreach (var rainbow in _listRainbow) {
            rainbow.gameObject.SetActive(false);
        }

        if (_moodChangeBrokenVFX != null) {
            _moodChangeBrokenVFX.Hide();
        }
    }

    public void PlayConfettiEffect() {
        if (confettiEffect.Length == 0)
            return;

        foreach (var eff in confettiEffect) {
            if (eff != null) {
                var main = eff.main;
                main.gravityModifier = Random.Range(5f, 6f) * Platform.GravityEarth / Mathf.Abs(Physics.gravity.y) * Time.timeScale;
                main.startLifetime = Random.Range(2f, 3f) / Time.timeScale;
            }
        }

        if (confettiEffect[0] != null)
            confettiEffect[0].Play();
    }

    private void ChangeIdleBouncing(bool isShow) {
        if (isShow && _idleBouncingFX == null) {
            GameObject idleBouncingResource = Resources.Load<GameObject>(ResourcesPath.Platforms_Idle_bouncing_fx);
            _idleBouncingFX = Instantiate(idleBouncingResource, transform);
            _idleBouncingFX.transform.localPosition = Vector3.zero;
        }

        if (_idleBouncingFX != null) {
            _idleBouncingFX.SetActive(isShow);
        }
    }

    public void UpdatePositionVFXSlide(Vector3 position) {
        if (_longNoteTrail == null) return;
        _longNoteTrail.position = position;
    }

    public void ActiveVFXSlide(bool active, Vector3 position) {
        if (active) {
            if (_longNoteTrail == null) {
                var prefab = Resources.Load<GameObject>("VFX/LongNote_TrailBall");
                if (prefab == null) return;
                _longNoteTrail = Instantiate(prefab, this.transform).transform;
                if (_longNoteTrail.TryGetComponent(out TrailRenderer trail)) {
                    trail.sortingOrder = Platform.MaxSortingOrder + 3;
                }
            }

            UpdatePositionVFXSlide(position);
        }

        if (_longNoteTrail == null) return;
        _longNoteTrail.gameObject.SetActive(active);
    }

    private ParticleSystem _vfxThunderFakeTile;

    public void PlayVFXThunderFakeTile(Vector3 transCachePosition) {
        if (_vfxThunderFakeTile == null) {
            _vfxThunderFakeTile = Instantiate(Resources.Load<ParticleSystem>("VFX/VFX_Thunder_Impact"), this.transform);
        }

        _vfxThunderFakeTile.transform.position = transCachePosition;
        _vfxThunderFakeTile.Play();
    }

    #region Mirror Section VFX

    private MirrorBoxVFX    _objMirrorBox;
    private HyperBoostVFX _objHyperBoost;

    public ISpecialPointer CreateMirrorBox(Transform transCache) {
        if (_objMirrorBox == null) {
            _objMirrorBox = Instantiate(Resources.Load<MirrorBoxVFX>(ResourcesPath.Mirror_VFX_Sign), this.transform);
        }
        _objMirrorBox.ShowVFX(transCache);
        return _objMirrorBox;
    }
    public void CreateMirrorBoxExplosion() {
        ShowVFXSpecialSectionActive(_objMirrorBox);
    }
    private void ShowVFXSpecialSectionActive(ISpecialPointer pointer) {
        pointer.OnHit(this.transform);
    }
    public void DisableVFXSpecialSection(ISpecialPointer pointer) {
        pointer?.HideVFX(this.transform);
    }
    
    public ISpecialPointer CreatePointHyperBoost(Transform transCache) {
        if (_objHyperBoost == null) {
            _objHyperBoost = Instantiate(Resources.Load<HyperBoostVFX>(ResourcesPath.HyperBoost_VFX_Sign), this.transform);
        }
        
        _objHyperBoost.ShowVFX(transCache);
        return _objHyperBoost;
    }

    public void CreateHyperBoostExplosion() {
        ShowVFXSpecialSectionActive(_objHyperBoost);
    }
    #endregion

    private VFXFakeIndefiniteTile vfxIndefiniteTile;
    public VFXFakeIndefiniteTile GetVFX(FakeTileStyle fakeIndefinite, Transform transformParent) {
        if (vfxIndefiniteTile == null) {
            vfxIndefiniteTile = Resources.Load<VFXFakeIndefiniteTile>("VFX/VFX_TrapTile_08_2");
            if (vfxIndefiniteTile != null) {
                vfxIndefiniteTile.CreatePool(1);
            }
        }
        return vfxIndefiniteTile != null ? vfxIndefiniteTile.Spawn(transformParent) : null;
    }

    private MoodChangeBrokenVFX _moodChangeBrokenVFX;
    public void ShowMoodChangeBroken(byte offsetId) {
        if (_moodChangeBrokenVFX == null) {
            _moodChangeBrokenVFX = Instantiate( Resources.Load<MoodChangeBrokenVFX>("Ingame/MoodChange-BrokenVFX"));
        }
        _moodChangeBrokenVFX.ShowCrash(offsetId);
    }
}