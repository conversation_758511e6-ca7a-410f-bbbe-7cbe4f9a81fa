using UnityEditor;
using UnityEngine;

public class ScreenshotCapture : MonoBehaviour {
    private const string  ScreenshotName = "Screenshot";
    private const int     SuperSize      = 1; // Set to 1 for normal size, 2 for double size, etc.
    private const KeyCode ScreenshotKey  = KeyCode.F12; // or any other key you prefer

    void Update() {
        if (Input.GetKeyDown(ScreenshotKey)) {
            TakeScreenShot();
        }
    }

    [MenuItem("InWave/Tools/TakeScreenShot")]
    public static void ShowWindow() {
        TakeScreenShot();
    }

    private static void TakeScreenShot() {
        string timestamp = System.DateTime.Now.ToString("yyyyMMddHHmmss");
        string filename = $"EditorCache/{ScreenshotName}_{timestamp}.png";
        ScreenCapture.CaptureScreenshot(filename, SuperSize);
        Debug.Log("Screenshot saved as: " + filename);
    }
}