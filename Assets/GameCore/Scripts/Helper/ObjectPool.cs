using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;

public sealed class ObjectPool : MonoBehaviour
{
    private static List<GameObject> tempList = new List<GameObject>();
    private Dictionary<GameObject, List<GameObject>> pooledObjects = DictionaryPool<GameObject, List<GameObject>>.Get();
    private Dictionary<GameObject, GameObject> spawnedObjects = DictionaryPool<GameObject, GameObject>.Get();
    private static ObjectPool _instance;
    public StartupPoolMode startupPoolMode;
    public StartupPool[] startupPools;
    
    private bool startupPoolsCreated;
    private Transform _cachedTransform;

    public Transform CachedTransform {
        get {
            if (_cachedTransform == null) {
                _cachedTransform = transform;
            }

            return _cachedTransform;
        }
    }
    
    private void Awake()
    {
        _instance = this;
        if (this.startupPoolMode != ObjectPool.StartupPoolMode.Awake)
            return;
        CreateStartupPools();
        //DontDestroyOnLoad(this.gameObject);
    }

    private void Start()
    {
        if (this.startupPoolMode != ObjectPool.StartupPoolMode.Start)
            return;
        CreateStartupPools();
    }

    private void OnDestroy() {
        foreach (KeyValuePair<GameObject, List<GameObject>> pooledObject in pooledObjects) {
            ListPool<GameObject>.Release(pooledObject.Value);
        }
        
        DictionaryPool<GameObject, List<GameObject>>.Release(pooledObjects);
        DictionaryPool<GameObject, GameObject>.Release(spawnedObjects);
    }

    public static void CreateStartupPools()
    {
        if (instance.startupPoolsCreated)
            return;
        instance.startupPoolsCreated = true;
        StartupPool[] startupPools = instance.startupPools;
        if (startupPools == null || startupPools.Length <= 0)
            return;
        for (int index = 0; index < startupPools.Length; ++index)
            CreatePool(startupPools[index].prefab, startupPools[index].size);
    }

    public static void CreatePool<T>(T prefab, int initialPoolSize) where T : Component
    {
        CreatePool(prefab.gameObject, initialPoolSize);
    }

    public static void CreatePool(GameObject prefab, int initialPoolSize)
    {
        if (!(prefab != null) || instance.pooledObjects.ContainsKey(prefab))
            return;
        List<GameObject> gameObjectList = ListPool<GameObject>.Get();
        instance.pooledObjects.Add(prefab, gameObjectList);
        if (initialPoolSize <= 0)
            return;
        bool activeSelf = prefab.activeSelf;
        prefab.SetActive(false);
        Transform transform = instance.CachedTransform;
        while (gameObjectList.Count < initialPoolSize) {
            GameObject gameObject = Instantiate(prefab, transform);
            gameObjectList.Add(gameObject);
        }
        prefab.SetActive(activeSelf);
    }

    public static T Spawn<T>(T prefab, Transform parent, Vector3 position, Quaternion rotation) where T : Component
    {
        return Spawn(prefab.gameObject, parent, position, rotation).GetComponent<T>();
    }

    public static T Spawn<T>(T prefab, Vector3 position, Quaternion rotation) where T : Component
    {
        return Spawn(prefab.gameObject, null, position, rotation).GetComponent<T>();
    }

    public static T Spawn<T>(T prefab, Transform parent, Vector3 position) where T : Component
    {
        return Spawn(prefab.gameObject, parent, position, Quaternion.identity).GetComponent<T>();
    }

    public static T Spawn<T>(T prefab, Vector3 position) where T : Component
    {
        return Spawn(prefab.gameObject, null, position, Quaternion.identity).GetComponent<T>();
    }

    public static T Spawn<T>(T prefab, Transform parent) where T : Component
    {
        return Spawn(prefab.gameObject, parent, Vector3.zero, Quaternion.identity).GetComponent<T>();
    }

    public static T Spawn<T>(T prefab) where T : Component
    {
        return ObjectPool.Spawn(prefab.gameObject, null, Vector3.zero, Quaternion.identity).GetComponent<T>();
    }

    public static GameObject Spawn(GameObject prefab, Transform parent, Vector3 localPosition, Quaternion localRotation)
    {
        if (instance.pooledObjects.TryGetValue(prefab, out List<GameObject> gameObjectList)) {
            GameObject key1 = null;
            if (gameObjectList.Count > 0) {
                while (key1 == null && gameObjectList.Count > 0) {
                    key1 = gameObjectList[0];
                    gameObjectList.RemoveAt(0);
                }
                
                if (key1 != null) {
                    Transform transform = key1.transform;
                    transform.SetParent(parent);
                    transform.localPosition = localPosition;
                    transform.localRotation = localRotation;
                    transform.localScale = Vector3.one;
                    key1.SetActive(true);
                    instance.spawnedObjects.Add(key1, prefab);
                    return key1;
                }
            }
            
            GameObject key2 = Instantiate(prefab, parent);
            Transform transform1 = key2.transform;
            transform1.localPosition = localPosition;
            transform1.localRotation = localRotation;
            transform1.localScale = Vector3.one;
            key2.SetActive(true);
            instance.spawnedObjects.Add(key2, prefab);
            return key2;
        }
        
        GameObject gameObject = Instantiate(prefab, parent);
        // Transform component = gameObject.GetComponent<Transform>();
        Transform component = gameObject.transform;
        component.localPosition = localPosition;
        component.localRotation = localRotation;
        component.localScale = Vector3.one;
        gameObject.SetActive(true);
        return gameObject;
    }

    public static GameObject Spawn(GameObject prefab, Transform parent, Vector3 position)
    {
        return Spawn(prefab, parent, position, Quaternion.identity);
    }

    public static GameObject Spawn(GameObject prefab, Vector3 position, Quaternion rotation)
    {
        return Spawn(prefab, (Transform)null, position, rotation);
    }

    public static GameObject Spawn(GameObject prefab, Transform parent)
    {
        return Spawn(prefab, parent, Vector3.zero, Quaternion.identity);
    }

    public static GameObject Spawn(GameObject prefab, Vector3 position)
    {
        return Spawn(prefab, (Transform)null, position, Quaternion.identity);
    }

    public static GameObject Spawn(GameObject prefab)
    {
        return Spawn(prefab, (Transform)null, Vector3.zero, Quaternion.identity);
    }

    public static void Recycle<T>(T obj) where T : Component
    {
        Recycle(obj.gameObject);
    }

    public static void Recycle(GameObject obj)
    {
        GameObject prefab;
        if (instance.spawnedObjects.TryGetValue(obj, out prefab))
        {
            Recycle(obj, prefab);
        }
        else
        {
            obj.transform.SetParent(instance.transform);
            Destroy(obj);
        }
    }

    public static void Recycle(GameObject obj, GameObject prefab)
    {
        instance.pooledObjects[prefab].Add(obj);
        instance.spawnedObjects.Remove(obj);
        if (obj) {
            obj.transform.SetParent(instance.transform);
            obj.SetActive(false);
        }
    }

    public static void RecycleAll<T>(T prefab) where T : Component
    {
        RecycleAll(prefab.gameObject);
    }

    public static void RecycleAll(GameObject prefab)
    {
        foreach (KeyValuePair<GameObject, GameObject> spawnedObject in instance.spawnedObjects)
        {
            if (spawnedObject.Value == prefab)
                tempList.Add(spawnedObject.Key);
        }
        for (int index = 0; index < tempList.Count; ++index)
            Recycle(tempList[index]);
        tempList.Clear();
    }

    public static void RecycleAll()
    {
        tempList.AddRange((IEnumerable<GameObject>)instance.spawnedObjects.Keys);
        for (int index = 0; index < tempList.Count; ++index)
            Recycle(tempList[index]);
        tempList.Clear();
    }

    public static bool IsSpawned(GameObject obj)
    {
        return instance.spawnedObjects.ContainsKey(obj);
    }

    public static int CountPooled<T>(T prefab) where T : Component
    {
        return CountPooled(prefab.gameObject);
    }

    public static int CountPooled(GameObject prefab)
    {
        List<GameObject> gameObjectList;
        if (instance.pooledObjects.TryGetValue(prefab, out gameObjectList))
            return gameObjectList.Count;
        return 0;
    }

    public static int CountSpawned<T>(T prefab) where T : Component
    {
        return CountSpawned(prefab.gameObject);
    }

    public static int CountSpawned(GameObject prefab)
    {
        int num = 0;
        foreach (GameObject gameObject in instance.spawnedObjects.Values)
        {
            if (prefab == gameObject)
                ++num;
        }
        return num;
    }

    public static int CountAllPooled()
    {
        int num = 0;
        foreach (List<GameObject> gameObjectList in instance.pooledObjects.Values)
            num += gameObjectList.Count;
        return num;
    }
    public static List<GameObject> GetAllPooled()
    {
        List<GameObject> list = new List<GameObject>();
        foreach (List<GameObject> gameObjectList in instance.pooledObjects.Values)
            list.AddRange((IEnumerable<GameObject>)gameObjectList);
        return list;
    }
    public static List<GameObject> GetPooled(GameObject prefab, List<GameObject> list, bool appendList)
    {
        if (list == null)
            list = new List<GameObject>();
        if (!appendList)
            list.Clear();
        List<GameObject> gameObjectList;
        if (instance.pooledObjects.TryGetValue(prefab, out gameObjectList))
            list.AddRange((IEnumerable<GameObject>)gameObjectList);
        return list;
    }

    public static List<T> GetPooled<T>(T prefab, List<T> list, bool appendList) where T : Component
    {
        if (list == null)
            list = new List<T>();
        if (!appendList)
            list.Clear();
        List<GameObject> gameObjectList;
        if (instance.pooledObjects.TryGetValue(prefab.gameObject, out gameObjectList))
        {
            for (int index = 0; index < gameObjectList.Count; ++index)
                list.Add(gameObjectList[index].GetComponent<T>());
        }
        return list;
    }

    public static void RemovePooled<T>(T prefab, List<T> list, bool appendList) where T : Component
    {
        List<GameObject> gameObjectList;
        if (instance.pooledObjects.TryGetValue(prefab.gameObject, out gameObjectList))
        {
            for (int index = 0; index < gameObjectList.Count; ++index)
            {
                instance.pooledObjects.Remove(gameObjectList[index]);
                Destroy(gameObjectList[index]);
            }
        }
    }

    public static List<GameObject> GetSpawned(GameObject prefab, List<GameObject> list, bool appendList)
    {
        if (list == null)
            list = new List<GameObject>();
        if (!appendList)
            list.Clear();
        foreach (KeyValuePair<GameObject, GameObject> spawnedObject in instance.spawnedObjects)
        {
            if (spawnedObject.Value == prefab)
                list.Add(spawnedObject.Key);
        }
        return list;
    }

    public static List<T> GetSpawned<T>(T prefab, List<T> list, bool appendList) where T : Component
    {
        if (list == null)
            list = new List<T>();
        if (!appendList)
            list.Clear();
        GameObject gameObject = prefab.gameObject;
        foreach (KeyValuePair<GameObject, GameObject> spawnedObject in instance.spawnedObjects)
        {
            if (spawnedObject.Value == gameObject)
                list.Add(spawnedObject.Key.GetComponent<T>());
        }
        return list;
    }

    public static void RemoveSpawned<T>(T prefab) where T : Component
    {
        GameObject prefabObject = prefab.gameObject;
        foreach (KeyValuePair<GameObject, GameObject> spawnedObject in instance.spawnedObjects) {
            if (spawnedObject.Value == prefabObject) {
                instance.spawnedObjects.Remove(prefabObject);
                Destroy(prefabObject);
            }
        }
    }

    public static ObjectPool instance
    {
        get
        {
            if (_instance != null)
                return _instance;
            
            _instance = new GameObject("ObjectPool")
            {
                transform = {
                    localPosition = Vector3.zero,
                    localRotation = Quaternion.identity,
                    localScale = Vector3.one
                }
            }.AddComponent<ObjectPool>();
            return _instance;
        }
    }

    public enum StartupPoolMode
    {
        Awake,
        Start,
        CallManually,
    }

    [Serializable]
    public class StartupPool
    {
        public int size;
        public GameObject prefab;
    }
}
