using UnityEngine;
using System.Collections.Generic;
using System;

[Serializable]
public struct JsonContainer<T>
{
    public List<T> _data;
    const string Key = "_data";
    private JsonContainer(List<T> dataList)
    {
        _data = dataList;
    }
    public static string JsonEncode(List<T> dataList)
    {
        JsonContainer<T> container = new JsonContainer<T>(dataList);
        return JsonUtility.ToJson(container);
    }
    public static List<T> JsonDecode(string json)
    {
        if (!json.Contains(Key)) {
            json = "{\"" + Key + "\":" + json + "}";
        }
        JsonContainer<T> container = JsonUtility.FromJson<JsonContainer<T>>(json);
        return container._data;
    }
}