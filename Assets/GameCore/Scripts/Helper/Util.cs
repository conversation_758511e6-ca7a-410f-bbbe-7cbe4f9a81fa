using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;
using System.Linq;
using System.Text;
using System.Security.Cryptography;
using System.Collections;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using Firebase.Database;
using Inwave;
using Music.ACM;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Hybrid;
using TilesHop.Cores.Pooling;
using TilesHop.EconomySystem;
using TilesHop.Cores.UserProgression;
using UnityEngine.SceneManagement;
using Object = UnityEngine.Object;
using Utils = Inwave.Utils;

public class Util : UnityEngine.Object {
    private const          string        StrHome                      = "Home";
    private const          string        KEY_INAPPREVIEW_SHOWED_COUNT = "inapp_review_showed_count";
    public static readonly System.Random randomGenerator              = new();

    public static int SongStartInSession { get; set; }

    public static void GoToGamePlay(Song song, string location, bool isSongClick, string song_play_type_detail = null,
                                    bool isOpenSensitiveFromHome = false) {
        if (GameController.instance != null) {
            GameController.instance.CheckSaveData();
        }

        if (GroundMusic.instance != null) {
            GroundMusic.instance.StopMusic();
        }

        if (song == null) {
            song = Configuration.instance.GetCurrentSong();
        }

        song.song_play_type_detail = song_play_type_detail;

        if (isSongClick) {
            AnalyticHelper.LogSong(SONG_STATUS.song_click, song, location: location);
        }

        Configuration.instance.isOpenSensitiveFromHome = isOpenSensitiveFromHome;
        SceneFader.instance.FadeTo(null, song, location);
    }

    public static void GoHome() {
        SceneFader.instance.FadeTo(GetHomeScene());
    }

    private static BoardManager leaderBoard;

    public static bool ShowLeaderBoard(Song song = null, SONG_MODE mode = SONG_MODE.normal) {
        if (Configuration.instance.isAllowFacebook && RemoteConfig.instance.enableLogin) {
            if (leaderBoard != null) {
                leaderBoard.SetupBoard(song, mode);
            } else {
                if (RemoteConfigBase.instance.UserProfile_UseNewProfile) {
                    GameObject boardManagerV2 = ShowPopUp(PopupName.LeaderboardV2);
                    if (boardManagerV2 != null) {
                        leaderBoard = boardManagerV2.GetComponent<BoardManagerV2>();
                        leaderBoard.SetupBoard(song, mode);
                    }
                } else {
                    GameObject bm = ShowPopUp(PopupName.LeaderBoard);
                    if (bm != null) {
                        leaderBoard = bm.GetComponent<BoardManager>();
                        leaderBoard.SetupBoard(song, mode);
                    }
                }
            }

            return true;
        }

        return false;
    }

    public static bool ShowProfilePage(bool isMe, UserEntry otherEntry = null) {
        if (RemoteConfigBase.instance.UserProfile_UseNewProfile) {
            GameObject userProfilePage = ShowPopUp(PopupName.UserProfile);
            //Init profile page
            if (userProfilePage != null) {
                userProfilePage.GetComponent<UserProfilePage>().Init(isMe, otherEntry);
                TrackingUserProfile.Track_UserButton(isMe);
                return true;
            }
        }

        return false;
    }

    public static string GetHomeScene() {
        return StrHome;
    }

    public static MessagePopup ShowMessage(string msg, int setFunc = 0, bool showCloseButton = true) {
        GameObject popUp = Util.ShowTopPopUp(PopupName.MessagePopup);
        MessagePopup mPopup = null;
        if (popUp != null) {
            mPopup = popUp.GetComponent<MessagePopup>();
            mPopup.Set(msg, setFunc, showCloseButton);
        } else {
            Logger.LogWarning("[ShowMessage] Cannot show message popup => " + msg);
        }

        return mPopup;
    }

    public static void ShowSmallMessage(string msg) {
        GameObject popUp = Util.ShowTopPopUp(PopupName.SmallMessagePopup);
        if (popUp != null) {
            popUp.GetComponent<MessagePopup>().Set(msg);
        }
    }

    public static int RandomExclude(int start, int end, int exclude) {
        int[] list = new int[end - start - 1];
        int count = 0;
        for (int i = start; i < end; i++) {
            if (i != exclude) {
                list[count] = i;
                count++;
            }
        }

        return list[UnityEngine.Random.Range(0, list.Length)];
    }

    /// <summary>
    /// Generate an interger between min and max value that better than UnityEngine.Random
    /// + System.Random Faster and less predictable than UnityEngine.Random
    /// </summary>
    /// <param name="min"></param>
    /// <param name="max"></param>
    /// <returns></returns>
    public static int GetBetterRandomInt(int min, int max) {
        return randomGenerator.Next(min, max);
    }

    public static string ReplaceMp3ToBin(string path) {
        return path.Replace(FILE_EXT.MP3, FILE_EXT.BIN).Replace(FILE_EXT.M4A, FILE_EXT.BIN);
    }

    public static string ReplaceMp3ToAmabp4(string path) {
        return path.Replace(FILE_EXT.MP3, FILE_EXT.AMABP4).Replace(FILE_EXT.M4A, FILE_EXT.BIN);
    }

    public static string SongToBoardId(string songId) {
        return songId.Replace(FILE_EXT.MP3, "");
    }

    public static string BoardToSongId(string boardId) {
        if (boardId != CONFIG_STRING.DBKey_Total) {
            return boardId + FILE_EXT.MP3;
        } else {
            return boardId;
        }
    }

    public static string GetAppUrl() {
        string url = "";
#if UNITY_IOS
        string iOSRatingURI = "itms-apps://itunes.apple.com/app/id{0}";
        string iOSAppID = "1344702806";
        if (!string.IsNullOrEmpty(iOSAppID)) {
            url = iOSRatingURI.Replace("{0}", iOSAppID);
        } else {
            //Debug.LogWarning ("Please set iOSAppID variable");
        }

#elif UNITY_ANDROID
        string AndroidRatingURI = "http://play.google.com/store/apps/details?id={0}";
        url = AndroidRatingURI.Replace("{0}", Application.identifier);
#endif
        return url;
    }

    public static Texture2D RoundTexture(Texture2D sourceTex) {
        int h = sourceTex.height;
        int w = sourceTex.width;
        float r = -1 + sourceTex.height / 2;
        float cx = -1 + sourceTex.height / 2;
        float cy = -1 + sourceTex.width / 2;

        Color[] c = sourceTex.GetPixels(0, 0, sourceTex.width, sourceTex.height);
        Texture2D b = new Texture2D(h, w);
        for (int i = 0; i < (h * w); i++) {
            int y = Mathf.FloorToInt(((float) i) / ((float) w));
            int x = Mathf.FloorToInt(((float) i - ((float) (y * w))));
            if (r * r >= (x - cx) * (x - cx) + (y - cy) * (y - cy)) {
                b.SetPixel(x, y, c[i]);
            } else {
                b.SetPixel(x, y, Color.clear);
            }
        }

        b.Apply();
        return b;
    }

    public static GameObject ShowPopUp(string name, Transform parent = null) {
        if (parent == null) {
            parent = GetPopupParent();
        }

        if (parent != null) {
            return GetPopup(name, parent);
        }
        
        CustomException.Fire("[ShowPopUp]", $"parent == null => Cannot show popup {name}");
        return null;
    }

    public static IEnumerator ShowPopupAsync<T>(string name, Transform parent = null, Action<T> callback = null) where T : Object{
        if (parent == null) {
            parent = GetPopupParent();
        }

        if (parent != null) {
            string path = BuildString('/',"Popups", name); // build string by using StringBuilder
            var assetRequest = Resources.LoadAsync<T>(path);
            while (!assetRequest.isDone) {
                yield return null;
            }
            
            // Check if the parent is still alive to avoid spawning when switching scenes, for example 
            if (assetRequest.asset != null && parent != null) {
                var popup = Instantiate(assetRequest.asset as T, parent);
                callback?.Invoke(popup);
            } else {
                CustomException.Fire("[ShowPopupAsync]", $"Cannot find {name} in Resources!");
            }
            yield break;
        }
        
        CustomException.Fire("[ShowPopUpAsync]", $"parent == null => Cannot show popup {name}");
    }

    public static IEnumerator ShowPopupWithOwnCanvasAsync<T>(string name, Action<T> callback = null) where T : Object {
        string path = BuildString('/', "Popups", name); // build string by using StringBuilder
        var assetRequest = Resources.LoadAsync<T>(path);
        while (!assetRequest.isDone) {
            yield return null;
        }

        // Check if the parent is still alive to avoid spawning when switching scenes, for example 
        if (assetRequest.asset != null) {
            var popup = Instantiate(assetRequest.asset as T);

            Camera mainCam;
            Canvas popupOwnCanvas = null;
            
            if (GameController.isInstanced) {
                mainCam = GameController.instance.mainCamera;
            } else {
                mainCam = Camera.main;
            }


            if (popup is GameObject gameObj) {
                gameObj.TryGetComponent(out popupOwnCanvas);
            } else if (popup is Component component) {
                component.TryGetComponent(out popupOwnCanvas);
            }

            if (popupOwnCanvas != null) {
                popupOwnCanvas.worldCamera = mainCam;
            }
            
            callback?.Invoke(popup);
            
        } else {
            CustomException.Fire("[ShowPopupWithOwnCanvasAsync]", $"Cannot find {name} in Resources!");
        }
    }
    
    public static IEnumerator ShowPopupAsyncAndWaitClose(string name, Transform parent = null, Action<GameObject> onLoadDone = null) {
        if (parent == null) {
            parent = GetPopupParent();
        }

        if (parent != null) {
            string path = BuildString('/',"Popups", name); // build string by using StringBuilder
            var assetRequest = Resources.LoadAsync<GameObject>(path);
            while (!assetRequest.isDone) {
                yield return null;
            }
            
            if (assetRequest.asset != null) {
                var popup = Instantiate(assetRequest.asset as GameObject, parent);
                onLoadDone?.Invoke(popup);
                
                while (popup != null && popup.activeSelf) {
                    yield return null;
                }
            } else {
                CustomException.Fire("[ShowPopupAsyncAndWaitClose]", $"Cannot find {name} in Resources!");
            }
            yield break;
        }
        
        CustomException.Fire("[ShowPopupAsyncAndWaitClose]", $"parent == null => Cannot show popup {name}");
    }

    public static Transform GetPopupParent() {
        Transform parent = null;
        if (IsHomeScene()) { //UI1 Home
            parent = HomeManager.instance.transform;
        } else if (UIController.ui != null) { //Game Play
            parent = UIController.ui.transform;
        }

        if (parent == null) {
            GameObject uiCanvas = GameObject.Find("UICanvas");
            if (uiCanvas != null) {
                parent = uiCanvas.transform;
            }
        }

        return parent;
    }

    public static GameObject ShowPopUpCanvasHeight(string name) {
        var popup = GetPopup(name, null);
        if (popup == null) {
            return null;
        }

        popup.GetComponent<Canvas>().worldCamera = Camera.main;
        return popup;
    }

    public static bool IsHomeScene() {
        return HomeManager.instance != null;
    }

    public static bool IsGameScene() {
        return GameController.instance != null;
    }

    public static bool IsGamePlaying() {
        return GameController.instance != null && GameController.instance.game == GameStatus.LIVE;
    }

    public static bool IsActionPhase() {
        return UIController.ui != null && UIController.ui.gameui;
    }

    public static bool IsGameResultScene() {
        return UIController.ui != null && UIController.ui.gameover && UIController.ui.gameover.gameObject.activeSelf;
    }

    public static bool IsReviveScene() {
        return UIController.ui != null && UIController.ui.continueUI != null &&
               UIController.ui.continueUI.gameObject.activeSelf;
    }

    public static bool IsShowIapShow() {
        return Shop.instance != null && Shop.instance.gameObject.activeInHierarchy;
    }

    /// <summary>
    /// Show the popup on the top of all UI Elements, There is no particle, fx effect in this popup
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public static GameObject ShowTopPopUp(string name) {
        Transform parent = UIOverlay.isInstanced ? UIOverlay.instance.transform : null;
        if (parent != null) {
            return GetPopup(name, parent);
        } else {
            return null;
        }
    }

    /// <summary>
    /// GetParentPopUp
    /// </summary>
    /// <returns></returns>
    public static Transform GetParentPopUp() {
        Transform parent = null;
        if (IsHomeScene()) {
            parent = HomeManager.instance.transform;
        } else if (UIController.ui != null) {
            parent = UIController.ui.transform;
        } else if (GameObject.Find("UICanvas") != null) {
            parent = GameObject.Find("UICanvas").transform;
        }

        return parent;
    }

    /// <summary>
    /// GetPopup
    /// </summary>
    /// <param name="popupName"></param>
    /// <param name="parent"></param>
    /// <returns></returns>
    public static GameObject GetPopup(string popupName, Transform parent = null) {
        string path = $"Popups/{popupName}";
        GameObject res = Resources.Load<GameObject>(path);
        if (res != null) {
            return Instantiate(res, parent);
        } else {
            if (Utils.isInternetReachable && !RemoteConfigBase.instance.isError) {
                CustomException.Fire("[GetPopup]", $"Cannot find {popupName} in Resources!");
            }

            return null;
        }
    }

    public static void RateUs() {
        SoundManager.PlayGameButton();
        if (RemoteConfig.instance.NativeReview_IsEnable) {
            ShowReviewNative((successed) => {
                if (!successed) {
                    OpenGameInStore(); //when not successed -> goto store
                }
            });
        } else { //NativeReview disable
            OpenGameInStore();
        }

        PlayerPrefs.SetInt(CONFIG_STRING.RateLater, 99);
    }

    public static void ShowReviewNative(Action<bool> callback) {
        if (Utils.IsAndroid() && Configuration.instance.androidSdk < 21) {
            // Android API smaller than 21 is not support native review
            callback?.Invoke(false);
            return;
        }

        int showedCount = PlayerPrefs.GetInt(KEY_INAPPREVIEW_SHOWED_COUNT);
        if (showedCount != 0) {
            callback?.Invoke(false);
            return;
        }

        ReviewNativeScript review;
        if (ReviewNativeScript.instance == null) {
            GameObject obj = new GameObject {name = "ReviewNative"};
            review = obj.AddComponent<ReviewNativeScript>();
        } else {
            review = ReviewNativeScript.instance;
        }

        review.ShowReviewPopup((onsuccessed) => {
            if (onsuccessed) {
                showedCount++;
                PlayerPrefs.SetInt(KEY_INAPPREVIEW_SHOWED_COUNT, showedCount);
            }

            callback?.Invoke(onsuccessed);
        });
    }

    public static void OpenGameInStore() {
        string url = GetAppUrl();
        if (!string.IsNullOrEmpty(url)) {
            OpenURL(url);
        }
    }

    public static string GetAndroidExternalStoragePath() {
#if UNITY_EDITOR
        return Application.persistentDataPath;
#else
        return SuperpoweredSDK.instance.AndroidSdCardPath();
#endif
    }

    public static string Base64Encode(string plainText) {
        var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
        return System.Convert.ToBase64String(plainTextBytes);
    }

    public static string Base64Decode(string base64EncodedData) {
        var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
        return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
    }

    public static string ComputeHash(string s) {
        // Form hash
        System.Security.Cryptography.MD5 h = System.Security.Cryptography.MD5.Create();
        byte[] data = h.ComputeHash(System.Text.Encoding.Default.GetBytes(s));
        // Create string representation
        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        for (int i = 0; i < data.Length; ++i) {
            sb.Append(data[i].ToString("x2"));
        }

        return sb.ToString();
    }

    public static string FileNameToTitle(string name, int length = 50) {
        string title = name.Replace(FILE_EXT.MP3, "").Replace(".MP3", "").Replace("-", " ").Replace("_", " ")
            .Replace(",", " ");
        return TruncateString(title, length);
    }

    public static string TruncateString(string str, int length = 50) {
        if (str.Length > length) {
            str = str.Remove(length) + "...";
        }

        return str;
    }

    public static string GetParentFolder(string folder) {
        var parent = new DirectoryInfo(folder);
        if (parent != null && parent.Parent != null) {
            return parent.Parent.FullName;
        }

        return null;
    }

    public static void GoToPolicyLink() {
        OpenURL(RemoteConfigBase.instance.PolicyLink);
    }

    public static void GoToTOSLink() {
        OpenURL(RemoteConfigBase.instance.TOSLink);
    }

#if UNITY_IPHONE
    [DllImport("__Internal")]
    private static extern void _openURL(string link);
#endif

    public static void OpenURL(string link) {
#if UNITY_IPHONE && !UNITY_EDITOR
        _openURL(link);
#else
        Application.OpenURL(link);
#endif
    }

    public static bool CanReadDirectory(DirectoryInfo dir) {
        if (dir.Exists) {
            try {
                dir.GetDirectories();
                return true;
            } catch {
                return false;
            }
        } else {
            return false;
        }
    }

    public static bool CanReadDirectory(string path) {
        if (Directory.Exists(path)) {
            //#if UNITY_EDITOR
            try {
                DirectoryInfo di = new DirectoryInfo(path);
                di.GetDirectories();
                //FileInfo[] fiArrRaw = di.GetFiles ();
                //if(diArr.Length + fiArrRaw.Length == 0){
                //	return false;
                //}
                return true;
            } catch {
                return false;
            }
        } else {
            return false;
        }

        /*
        #else
        var readAllow = false;
        var readDeny = false;
        var accessControlList = Directory.GetAccessControl (path);
        if (accessControlList == null)
            return false;
        var accessRules = accessControlList.GetAccessRules (true, true, typeof(System.Security.Principal.SecurityIdentifier));
        if (accessRules == null)
            return false;

        foreach (FileSystemAccessRule rule in accessRules) {
            if ((FileSystemRights.Read & rule.FileSystemRights) != FileSystemRights.Read)
                continue;

            if (rule.AccessControlType == AccessControlType.Allow)
                readAllow = true;
            else if (rule.AccessControlType == AccessControlType.Deny)
                readDeny = true;
        }

        return readAllow && !readDeny;
        #endif
        */
    }

    public static string KiloFormat(int num) {
        if (num >= 100000000) {
            return (num / 1000000D).ToString("0.#M");
        }

        if (num >= 1000000) {
            return (num / 1000000D).ToString("0.##M");
        }

        if (num >= 100000) {
            return (num / 1000D).ToString("0.#K");
        }

        if (num >= 10000) {
            return (num / 1000D).ToString("0.##K");
        }

        return num.ToString("#,0");
    }

    public static Vector3 SetPositionY(Vector3 position, float y) {
        position.y = y;
        return position;
    }

    public static Vector3 SetPositionX(Vector3 position, float x) {
        position.x = x;
        return position;
    }

    public static Vector3 SetPositionZ(Vector3 position, float z) {
        position.z = z;
        return position;
    }

    public static Vector3 SetPositionXY(Vector3 position, float x, float y) {
        position.x = x;
        position.y = y;
        return position;
    }

    public static Vector3 SetPositionYZ(Vector3 position, float y, float z) {
        position.z = z;
        position.y = y;
        return position;
    }

    public static Vector3 SetPositionXZ(Vector3 position, float x, float z) {
        position.x = x;
        position.z = z;
        return position;
    }

    public static DisableCallbackUI ShowNeedMore(LOCATION_NAME lastLocation, int price, VIDEOREWARD rewardType,
                                                 Song extra = null, bool isFromShop = false) {
        int x = price - Configuration.instance.GetDiamonds();
        var shop = Util.ShowIAPShop(lastLocation);
        shop.SetNeedMore(x);
        AnalyticHelper.NeedMore(TRACK_NAME.popup_shop);
        return shop;
    }

    /// <summary>
    /// Check xem có cần show popup watch Ads /  Sale IAP hay không
    /// </summary>
    /// <param name="location"></param>
    /// <returns></returns>
    public static DisableCallbackUI CheckShowEconomyBoost(string location) {
        if (SubscriptionController.IsSubscriptionVip()) {
            return null; // khi vip rồi k show nữa
        }

        bool canShowSaleIAP = PopupSaleIAP.CanShowPopup(location, SongStartInSession);
        if (canShowSaleIAP) {
            PopupSaleIAP saleIAP = Util.ShowPopUp(PopupName.SaleIAP).GetComponent<PopupSaleIAP>();
            saleIAP.Show(location);
            SongStartInSession = 0; // reset songstart để tránh show iap xong lại show ads offer ngay lập tức
            return saleIAP;
        }

        bool canShowWatchAds = PopupWatchAds.CanShowPopup(location, SongStartInSession);
        if (canShowWatchAds) {
            PopupWatchAds watchAds = Util.ShowPopUp(PopupWatchAds.WatchAdsName).GetComponent<PopupWatchAds>();
            watchAds.Show(location);
            SongStartInSession = 0; // reset songstart để tránh show iap xong lại show ads offer ngay lập tức

            return watchAds;
        }

        return null;
    }

    /// <summary>
    /// Check xem có cần show popup reward for oldUser
    /// </summary>
    /// <returns></returns>
    public static IEnumerator CheckShowOldUserRewardBLE() {
        if (!RemoteConfigBase.instance.OldUserBLE_Enable) {
            yield break;
        }

        var data = OldUserBLE.CheckBalanceOldUser();
        if (data.showPopup) {
            UIBoosterOldUser.isShowing = true;
            UIBoosterOldUser popupHandler;
            
            yield return ShowPopupAsync<UIBoosterOldUser>(PopupName.BoosterOldUser, callback: popup => {
                if (popup == null) {
                    UIBoosterOldUser.isShowing = false;
                    return;
                }
                popupHandler = popup;
                
                if (data.rewardBall) {
                    int idBall = OldUserBLE.GetBallIdReward();
                    popupHandler.Show(idBall);
                } else {
                    popupHandler.Show(-1);
                }
            });
        }
    }

    private static string GetShop() {
        if (RemoteConfigBase.instance.ShopUI_Revamp_Enable) {
            return PopupName.ShopUI_Revamp;
        }

        if (RemoteConfig.instance.Economy_IsEnable || RemoteConfig.instance.ShopUI3_Enable) {
            return PopupName.ShopUI3;
        }

        if (RemoteConfig.instance.ShopUI2_Enable) {
            return PopupName.ShopUI2;
        }

        return PopupName.Shop;
    }

    public static string FirstCharToUpper(string input) {
        return string.IsNullOrEmpty(input) ? input : input.First().ToString().ToUpper() + input.ToLower().Substring(1);
    }

    public static string[] SplitWords(string input) {
        List<string> result = new List<string>();
        if (!string.IsNullOrEmpty(input)) {
            string[] words = Regex.Split(input, @"\W+");
            for (int i = 0; i < words.Length; i++) {
                string newWord = Base64Encode(words[i].ToLower().Trim());
                if (!string.IsNullOrEmpty(newWord)) {
                    result.Add(newWord);
                }
            }
        }

        return result.ToArray();
    }

    public static float GetWithOfText(Text txt) {
        TextGenerator textGen = new TextGenerator();
        TextGenerationSettings generationSettings = txt.GetGenerationSettings(txt.rectTransform.rect.size);
        return (textGen.GetPreferredWidth(txt.text, generationSettings) * 500f / Screen.width) + 30;
    }

    /// <summary>
    /// GetExactlyWithOfText
    /// </summary>
    /// <param name="txt"></param>
    /// <returns></returns>
    public static float GetExactlyWithOfText(Text txt) {
        TextGenerator textGen = new TextGenerator();
        TextGenerationSettings generationSettings = txt.GetGenerationSettings(txt.rectTransform.rect.size);
        return textGen.GetPreferredWidth(txt.text, generationSettings) * 480f / Screen.width;
    }

    public static int GetTextWidth(Text text) {
        Font font = text.font;
        CharacterInfo characterInfo = new CharacterInfo();
        int totalLength = 0;
        string message = text.text;
        font.RequestCharactersInTexture(message, text.fontSize, FontStyle.Normal);
        char[] arr = message.ToCharArray();

        foreach (char c in arr) {
            font.GetCharacterInfo(c, out characterInfo, text.fontSize);
            totalLength += characterInfo.advance;
        }

        return totalLength;
    }

    public static DateTime GetDateTimeByString(string str, DateTime defaultValue) {
        if (!string.IsNullOrEmpty(str) && DateTime.TryParse(str, out DateTime temp)) {
            return temp;
        } else {
            return defaultValue;
        }
    }

    public static string ValidAttributeName(string input) {
        if (Configuration.isAdmin || Application.platform == RuntimePlatform.WindowsEditor ||
            Application.platform == RuntimePlatform.OSXEditor) {
            try {
                if (string.IsNullOrEmpty(input)) {
                    CustomException.Fire("[ValidAttributeName]", $"string.IsNullOrEmpty({input})");
                    return input;
                }

                StringBuilder s = new StringBuilder(input.Length); // (input.Length);
                using (var reader = new StringReader(input)) {
                    char c;
                    for (int i = 0; i < input.Length; i++) {
                        c = (char) reader.Read();
                        if (char.IsLetterOrDigit(c) || c == '_') {
                            s.Append(c);
                        } else if (c == '-' || c == '.' || c == '#' || c == '$' || c == '[' || c == ']') {
                            s.Append('_');
                        }
                    }
                }

                // Incase key is empty after validate ->> default '_'
                if (s.Length < 1) {
                    Debug.LogError(
                        $"Firebase key attribute is empty after validate. Key: \"{input}\" . =>  So set default key to \"_\"");
                    s.Append('_');
                }

                string validAttributeName = s.ToString();
                if (!string.Equals(input, validAttributeName, StringComparison.Ordinal) && !string.Equals(input.Trim(),
                        validAttributeName.Trim(), StringComparison.Ordinal)) {
                    CustomException.Fire("[ValidAttributeName]",
                        "Change ValidAttributeName from " + input + " => " + validAttributeName);
                }

                return validAttributeName;
            } catch (Exception e) {
                CustomException.Fire("[ValidAttributeName]", $" Error when valid {(input ?? "NULL")} {e.Message}");
                return input;
            }
        } else {
            return input;
        }
    }

    public static string MD5Hash(string str) {
        MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();

        byte[] bHash = md5.ComputeHash(Encoding.UTF8.GetBytes(str));

        StringBuilder sbHash = new StringBuilder();

        foreach (byte b in bHash) {
            sbHash.Append(String.Format("{0:x2}", b));
        }

        return sbHash.ToString();
    }

    public static void Shuffle<T>(IList<T> list) {
        System.Random rng = new System.Random();
        int n = list.Count;
        while (n > 1) {
            n--;
            int k = rng.Next(n + 1);
            (list[k], list[n]) = (list[n], list[k]);
        }
    }

    public static void ToggleExitPopup() {
        if (ExitPopUp.instance != null) {
            ExitPopUp.instance.Close();
        } else {
            ShowPopUp(PopupName.ExitPopUp);
        }
    }

    public static string ReplaceMultiSpace(string s) {
        return Regex.Replace(s, @"\s+", " ");
    }

    public static int GetCurrentWeek() {
        return GetWeek(DateTime.Today);
    }

    public static int GetWeek(DateTime dateTime) {
        //Ngày đầu tiên của tuần là Chủ Nhật
        DateTimeFormatInfo dateTimeFormatInfo = DateTimeFormatInfo.InvariantInfo;
        Calendar calendar = dateTimeFormatInfo.Calendar;
        int weekOfYear = calendar.GetWeekOfYear(dateTime, dateTimeFormatInfo.CalendarWeekRule,
            dateTimeFormatInfo.FirstDayOfWeek);
        return weekOfYear;
    }

    public static int GetCurrentDay() {
        return DateTime.Today.DayOfYear;
    }

    public static string GetUrlDBDaily() {
        int currentDay = Util.GetCurrentDay();
        string dbDay = "Daily/" + CONFIG_STRING.DBKey_LeaderBoards + CONFIG_STRING.DBKey_day + currentDay;
        return dbDay;
    }

    public static string GetUrlDBWeekly() {
        int currentWeek = Util.GetCurrentWeek();
        string dbWeekly = "Weekly/" + CONFIG_STRING.DBKey_LeaderBoards + CONFIG_STRING.DBKey_week + currentWeek;
        return dbWeekly;
    }

    public static int GetTotalRemainingDayInWeek() {
        DateTime now = DateTime.Now;
        DayOfWeek nowDayOfWeek = now.DayOfWeek;
        switch (nowDayOfWeek) {
            case DayOfWeek.Sunday:
                return 6;

            case DayOfWeek.Monday:
                return 5;

            case DayOfWeek.Tuesday:
                return 4;

            case DayOfWeek.Wednesday:
                return 3;

            case DayOfWeek.Thursday:
                return 2;

            case DayOfWeek.Friday:
                return 1;

            case DayOfWeek.Saturday:
                return 0;

            default:
                return 0;
        }
    }

    public static GameObject InstantiateShopIAP() {
        return ShowPopUp(GetShop());
    }

    public static Shop ShowIAPShop(LOCATION_NAME lastLocation) {
        Shop.lastLocation = lastLocation;
        bool isHomeScene = IsHomeScene();
        bool isTabFormEnable = BottomMenuScript.isShopIapTabForm && isHomeScene &&
                               !HomeManager.instance.IsShowingFullScreenPopup();

        if (isTabFormEnable) {
            GameObject shop = HomeManager.instance.InstantiateShopTab();
            if (shop == null) {
                shop = InstantiateShopIAP();
            }

            shop.transform.parent = HomeManager.instance.tabContainerShopIap.transform;

            // put popup to tab
            var rectTransform = (RectTransform) shop.transform;
            rectTransform.offsetMax = Vector2.zero;
            rectTransform.offsetMin = Vector2.down * 20f;

            // show tab transitions
            HomeManager.instance.ShowShopIapTab();

            // remove overrided sorting layer canvas
            if (shop.TryGetComponent(out GraphicRaycaster raycaster)) {
                Destroy(raycaster);
                if (shop.TryGetComponent(out Canvas canvas)) {
                    Destroy(canvas);
                }
            }

            return shop.GetComponent<Shop>();
        }

        return ShowPopupShopIAP();
    }

    public static Shop ShowPopupShopIAP() {
        return ShowPopUp(GetShop()).GetComponent<Shop>();
    }

    private static UIStarJourney _popupStarJourney;

    public static void ShowStarJourney(RectTransform parent = null) {
        if (_popupStarJourney == null) {
            var starJourney = ShowPopUp(PopupName.StarJourney);
            if (starJourney == null) {
                return;
            }

            _popupStarJourney = starJourney.GetComponent<UIStarJourney>();
        }

        if (parent != null) {
            var rectTransform = (RectTransform) _popupStarJourney.transform;
            rectTransform.parent = parent;
            rectTransform.offsetMax = Vector2.zero;
            rectTransform.offsetMin = Vector2.zero;
        }

        if (_popupStarJourney) {
            _popupStarJourney.Show();
        }
    }

    public static UIHybridCenter ShowPopup_UPAchievement() {
        var center = Util.ShowPopUp(PopupName.HybridCenter).GetComponent<UIHybridCenter>();
        center.Show(GroupUPType.ACHIEVEMENT);
        return center;
    }

    public static UIHybridCenter ShowPopup_UPMission(bool isFromHomeMission = false) {
        var center = Util.ShowPopUp(PopupName.HybridCenter).GetComponent<UIHybridCenter>();
        center.Show(GroupUPType.MISSION, isFromHomeMission);
        return center;
    }

    public static string PriceToString(double price, string currencyCode) {
        CultureInfo[] cultures = CultureInfo.GetCultures(CultureTypes.SpecificCultures);
        foreach (CultureInfo ci in cultures) {
            RegionInfo ri = new RegionInfo(ci.LCID);
            if (ri.ISOCurrencySymbol == currencyCode) {
                return $"{ri.CurrencySymbol}{DoubleToCurrencyString(price)}";
            }
        }

        return price.ToString("C");
    }

    public static string DoubleToCurrencyString(double price) {
        if (price < 1000) {
            return price.ToString("F");
        } else {
            string result = (Mathf.RoundToInt((float) price)).ToString();
            int index = result.IndexOf('.');
            if (index < 0) {
                index = result.Length;
            }

            while (true) {
                if (index <= 3)
                    break;

                result = result.Insert(index - 3, ",");
                index -= 3;
            }

            return result;
        }
    }

    public static List<string> GetListString(string strValue) {
        if (string.IsNullOrEmpty(strValue)) {
            return null;
        }

        try {
            strValue = $"{{\"listString\":{strValue}}}";
            List<string> urlList = JsonUtility.FromJson<ListWrapper>(strValue).listString;
            return urlList;
        } catch (Exception e) {
            CustomException.Fire("[GetListString]", "Cannot parse => " + strValue + " => ERROR => " + e.Message);
            return null;
        }
    }

    public static void WaitRemoteConfigDone(Action action, bool isRunNextFrame = false) {
        if (action == null) {
            return;
        }

        if (RemoteConfigBase.isLoaded) {
            if (isRunNextFrame) {
                QueueRunNextFrame.instanceSafe.Enqueue(action);
            } else {
                action.Invoke();
            }
        } else {
            RemoteConfigBase.onLoaded += () => {
                if (isRunNextFrame) {
                    QueueRunNextFrame.instanceSafe.Enqueue(action);
                } else {
                    action.Invoke();
                }
            };
        }
    }

    public static void WaitSongListDone(Action action) {
        if (action == null) {
            return;
        }

        if (SongManager.isInitedSongList) {
            action.Invoke();
        } else {
            SongManager.OnInitedSongList += OnDone;
        }

        void OnDone() {
            action?.Invoke();
            SongManager.OnInitedSongList -= OnDone;
        }
    }

    public static void WaitBallManagerInitDone(Action action) {
        if (action == null) {
            return;
        }

        if (BallManager.Inited) {
            action.Invoke();
        } else {
            BallManager.OnInitedDone += OnDone;
        }

        void OnDone() {
            action?.Invoke();
            BallManager.OnInitedDone -= OnDone;
        }
    }

    public static void WaitThemeManagerInitDone(Action action) {
        if (action == null) {
            return;
        }

        if (ThemeManager.Inited) {
            action.Invoke();
        } else {
            ThemeManager.OnInitedDone += OnDone;
        }

        void OnDone() {
            action?.Invoke();
            ThemeManager.OnInitedDone -= OnDone;
        }
    }

    public static void WaitACMInitDone(Action action) {
        if (action == null) {
            return;
        }

        if (ACMSDKv4.IsInitialized()) {
            action.Invoke();
        } else {
            ACMSDK.Instance.OnInitializationSuccess.AddListener(OnDone);
        }

        void OnDone() {
            action?.Invoke();
            ACMSDK.Instance.OnInitializationSuccess.RemoveListener(OnDone);
        }
    }

    public static void WaitFirebaseSDKInitDone(Action action) {
        if (action == null) {
            return;
        }

        if (FirebaseSDK.firebaseStatus == FirebaseSDK.FirebaseStatus.READY) {
            action.Invoke();
        } else {
            FirebaseSDK.onInitialized += OnDone;
        }

        return;

        void OnDone(bool b) {
            action?.Invoke();
            FirebaseSDK.onInitialized -= OnDone;
        }
    }

    public static bool IsCanShowPopup() {
        if (LocalizationManager.instance == null || !LocalizationManager.instance.IsInited()) {
            return false;
        }

        if (GroundMusic.instance == null) {
            return false;
        }

        return true;
    }

    public static Vector3 DoPathBezier(Vector3 p0, Vector3 p1, Vector3 p2, float t) {
        return (1 - t) * (1 - t) * p0 + 2 * (1 - t) * t * p1 + t * t * p2;
    }

    public static Vector3 DoPathLine(Vector3 p0, Vector3 p1, Vector3 p2, float t) {
        if (t < 0.5f) {
            return (1 - t * 2) * p0 + t * 2 * p1;
        } else {
            return (1 - t) * 2 * p1 + (t - 0.5f) * 2 * p2;
        }
    }

    public static Tips ShowPopUpTip(Tips.ShowType showType, LOCATION_NAME lastLocation, Action onStartClose = null) {
        string namePopup = PopupName.Tips;
        if (showType == Tips.ShowType.Downloading) {
            if (RemoteConfig.instance.DownloadSong_v2_IsEnable) {
                namePopup = PopupName.TipsV2;
            }
        }

        Tips.lastLocation = lastLocation;
        GameObject popup = ShowPopUp(namePopup);
        var tips = popup.GetComponent<Tips>();
        if (tips == null) {
            Logger.EditorLogError("Null component in Popup");
            return null;
        }

        tips.SetStyle(showType);
        if (onStartClose != null) {
            tips.onStartClose = onStartClose;
        }

        return tips;
    }

    public static string GetFormattedRank(long rank) {
        if (rank >= 1000000) {
            return (rank / 1000000) + "M";
        } else if (rank >= 1000) {
            return (rank / 1000) + "K";
        } else if (rank > 0) {
            return rank.ToString();
        } else {
            return "--";
        }
    }

    #region String Utils

    // Removed static StringBuilder to prevent thread-safety issues (race conditions).
    // A local StringBuilder will be used in each call instead.

    public static string BuildString(char seperator, params object[] contents) {
        StringBuilder sb = new StringBuilder();
        sb.AppendJoin(seperator, contents);
        return sb.ToString();
    }

    public static string BuildString(string seperator, params object[] contents) {
        StringBuilder sb = new StringBuilder();
        sb.AppendJoin(seperator, contents);
        return sb.ToString();
    }

    #endregion

    public static GameObject ShowUI(string nameUI, Transform parent = null) {
        if (parent == null) {
            if (IsHomeScene()) //UI1 Home
            {
                parent = HomeManager.instance.transform;
            } else if (UIController.ui != null) //Game Play
            {
                parent = UIController.ui.transform;
            }
        }

        if (parent == null) {
            GameObject uiCanvas = GameObject.Find("UICanvas");
            if (uiCanvas != null) {
                parent = uiCanvas.transform;
            }
        }

        if (parent != null) {
            string popups = $"UI/{nameUI}";
            GameObject res = Resources.Load<GameObject>(popups);
            return res != null ? Instantiate(res, parent) : null;
        } else {
            return null;
        }
    }

    public static void ShowPopup_AdminSpecialTile(Song song) {
        GameObject prefab = Util.ShowPopUp(CONFIG_STRING.Admin_SpecialTileControl);
        var popup = prefab.GetComponent<UIAdminPopup_SpecialTile>();
        popup.Show(song);
    }

    public static void ShowPopup_AdminSpecialTileV2(Song song) {
        GameObject prefab = Util.ShowPopUp(CONFIG_STRING.Admin_SpecialTileV2);
        var popup = prefab.GetComponent<UIAdminPopup_SpecialTileV2>();
        popup.Show(song);
    }

    public static void ShowPopup_AdminLongNote(Song song) {
        GameObject prefab = Util.ShowPopUp(CONFIG_STRING.Admin_LongNotePrototype);
        var popup = prefab.GetComponent<AdminLongNotePopup>();
        popup.Show(song);
    }

    public static float Bezier(float p0, float p1, float p2, float t) {
        return (1 - t) * (1 - t) * p0 + 2 * t * (1 - t) * p1 + t * t * p2;
    }

    public static bool IsLongScreen() {
        return Utils.GetHeight() * 1f / Utils.GetWidth() > 1.8F;
    }

    public static string FormatToMillion(int number) {
        return number >= 1_000_000 ? BuildString(string.Empty, (number / 1_000_000), "M") : number.ToString();
    }

    public static bool IsRectOverlapping(RectTransform rectA, RectTransform rectB) {
        // Lấy các góc của từng RectTransform trong World Space
        Vector3[] cornersA = new Vector3[4];
        Vector3[] cornersB = new Vector3[4];

        rectA.GetWorldCorners(cornersA);
        rectB.GetWorldCorners(cornersB);

        // Chuyển thành Rect (minX, minY, width, height)
        Rect rectWorldA = new(cornersA[0].x, cornersA[0].y, cornersA[2].x - cornersA[0].x,
            cornersA[2].y - cornersA[0].y);

        Rect rectWorldB = new(cornersB[0].x, cornersB[0].y, cornersB[2].x - cornersB[0].x,
            cornersB[2].y - cornersB[0].y);

        // Kiểm tra xem hai hình chữ nhật có giao nhau không
        return rectWorldA.Overlaps(rectWorldB);
    }

    public static bool IsPointerOver(RectTransform rect, Camera cam) {
        return RectTransformUtility.RectangleContainsScreenPoint(rect, Input.mousePosition, cam);
    }

    public static RewardType ConvertToRewardType(MissionReward currentMissionMissionReward) {

        switch (currentMissionMissionReward) {
            case MissionReward.Diamond:
                return RewardType.Gem;

            case MissionReward.Song:
                return RewardType.Song;

            case MissionReward.Ball:
            case MissionReward.Character:
                return RewardType.Skin;

            case MissionReward.Theme:
                return RewardType.Theme;

            default:
                Logger.EditorLogError("Not handle this type!!!");
                break;
        }

        return RewardType.None;
    }

    public const   string KEY_TOOLTIP_DUALUNLOCK   = "dual_unlock_tooltip";
    private static bool   _isShowDualUnlockTooltip = false;

    public static bool EnableCheckDualUnlock = true;

    public static UITooltipDualUnlock CheckShowTooltipDualUnlock(Transform parent) {
        if (_isShowDualUnlockTooltip)
            return null;
        if (PlayerPrefs.HasKey(KEY_TOOLTIP_DUALUNLOCK))
            return null;

        _isShowDualUnlockTooltip = true;

        var prefab = Resources.Load<UITooltipDualUnlock>("UI/Tooltip-DualUnlock");
        if (prefab == null) {
            return null;
        }

        var item = Instantiate(prefab, parent);
        return item;
    }

    public static string EncodePath(string path) {

        string dir = Path.GetDirectoryName(path);
        string fileName = Path.GetFileName(path);

        fileName = Uri.EscapeDataString(fileName);

        if (dir != null && !dir.Contains("file:")) {
            return "file://" + dir + "/" + fileName;
        } else {
            if (Utils.IsIPhone() && dir != null && !dir.Contains("file://")) {
                dir = dir.Replace("file:", "file://");
            }

            return dir + "/" + fileName;
        }
    }

    public static Vector3 GetMiddlePosition(Vector3 start, Vector3 end, float height, float posZ) {
        float x1 = start.x;
        float x2 = end.x;

        float h = height;
        float z1 = start.z;
        float z2 = end.z;
        float z3 = (z1 + z2) / 2;

        float a = h / (z3 - z1) / (z3 - z2);
        float b = -(z1 + z2) * a;
        float c = -a * z1 * z1 - b * z1;

        float y = a * posZ * posZ + b * posZ + c;

        Vector3 position = new(x1 + (x2 - x1) * (posZ - z1) / (z2 - z1), y, posZ);

        if (y > 0) { //valid
            return position;
        } else {
            Logger.EditorLogError($"invalid!!! {start} - {end} - {posZ}");
            return end;
        }
    }

    public static List<int> ShuffleArray(List<int> arr) {
        // Knuth shuffle algorithm :: courtesy of Wikipedia
        for (int t = 0; t < arr.Count; t++) {
            int tmp = arr[t];
            int r = UnityEngine.Random.Range(t, arr.Count);
            arr[t] = arr[r];
            arr[r] = tmp;
        }

        return arr;
    }

    public static void LoadResourcesAsync<T>(string path, Action<T> callback) where T : UnityEngine.Object {
        Configuration.instance.StartCoroutine(IELoadResourcesAsync(path, callback));
    }

    private static IEnumerator IELoadResourcesAsync<T>(string path, Action<T> callback) where T : UnityEngine.Object {
        ResourceRequest request = Resources.LoadAsync<T>(path);
        yield return request;

        if (request.asset != null) {
            callback?.Invoke(request.asset as T);
        } else {
            callback?.Invoke(null);
        }
    }
}