public struct FileNameData {
    public const string PlayerData                = "PlayerData";
    public const string RecentSearchSongData      = "RecentSearchSongData";
    public const string RecentSearchSongDiscovery = "RecentSearchSongDiscovery";
}

public struct FILE_EXT {
    public const string MP3    = ".mp3";
    public const string OGG    = ".ogg";
    public const string BIN    = ".bin";
    public const string AMABP4 = ".amabp4";
    public const string M4A    = ".m4a";
    public const string CSV    = ".csv";
}

public struct GAMETAG {
    public const string Ball        = "Ball";
    public const string ScoreEffect = "ScoreEffect";
    public const string BlockClone  = "BlockClone";
    public const string Block       = "Block";
    public const string Perfect     = "Perfect";
    public const string Tree        = "Tree";
    public const string Ground      = "Ground";
    public const string Road        = "Road";
    public const string Collector   = "Collector";
    public const string Crown       = "Crown";
    public const string Anzu        = "Anzu"; //TH-84
    public const string Adverty     = "Adverty"; //TH-3122
    public const string Diamond     = "Diamond";//TH-4572
    public const string Drill       = "Drill";//TH-4716 - Live event Mystery
}

public enum SONGTYPE {
    VIDEO,
    OPEN,
    LOCK,
    PAY,
    ALBUM,
    BUTTON,
    CHALLENGE,
    VIP,
    FAVORITE_TIP,
    EVENT, //TH-1528: Live event
    USER_PROGRESSION, //TH-2234: User progression
    COUNTDOWN, //TH-2234: User progression
    CHALLENGE_OLD_USER, //TH-3057: Free Challenge 
}

public struct SONGTYPE_STRING {
    public const string VIDEO              = "VIDEO";
    public const string OPEN               = "OPEN";
    public const string LOCK               = "LOCK";
    public const string PAY                = "PAY";
    public const string ALBUM              = "ALBUM";
    public const string CHALLENGE          = "CHALLENGE";
    public const string VIP                = "VIP";
    public const string EVENT              = "EVENT"; //TH-1528: Live event
    public const string USER_PROGRESSION   = "USER_PROGRESSION"; //TH-2234: User Progression
    public const string COUNTDOWN          = "COUNTDOWN"; //TH-2234: User Progression
    public const string CHALLENGE_OLD_USER = "CHALLENGE_OLD_USER"; //TH-3057: Free Challenge 
}

public enum SongUnlockType {
    @default                           = -1,
    ads                                = 10,
    currency                           = 18,
    vip                                = 2,
    seven_day_mission                  = 0,
    song_of_day                        = 1,
    ad_fs_reward                       = 3,
    token                              = 4,
    mysterybox_popup                   = 5,
    discover_your_challenge_golden_box = 6,
    userprogression_timer              = 7,
    starjourney                        = 8,
    free_challenge_unlocked            = 9, //TH-3057: Free Challenge
    seasonal_pack                      = 11,
    userprogression_reward             = 12,
    mission                            = 13,
    reward                             = 14,
    notification_box                   = 15,
    pay_single                         = 16,
    pay_album                          = 17,
    triple_offer                       = 19,
    endless_offer                      = 30,
    mystery_door                         = 20,
    beat_sahur                    = 19,
}

public enum SONG_MODE {
    normal, //NORMAL
    endless_mode, //ENDLESS
    local_song
}

public enum VIDEOREWARD {
    None,
    SKIPABLE,
    item, //TH-124 UNLOCKBALL + UNLOCKTHEME
    song_unlock, //UNLOCKSONG
    revive, //TH-124 CONTINUE
    currency, // GETDIAMOND
    currency_x2, //	X2DIAMOND
    currency_multiply, // Multiply diamond
    local_song, //UPLOADSONG
    SKIPABLE_ENDGAME,
    LUCKY_SPIN,
    CHALLENGE,
    RANDOMSONG,
    UNLOCK_ENDLESS,
    UNLOCK_ZEND,
    UNLOCK_HARD,
    TRIAL,
    skin_offer,
    more_diamond, // TH1471: ads diamond
    REVIVE_QUEST,
    endless_offer_reward,
}

public enum GAME_STEP {
    Step_RemoteConfigLoadedFail,
    Step_SongConfigLoaded,
    Step_SongConfigLoadedFail,
    Step_SongConfigDownload,
    Step_SongConfigDownloadFail,

    User_Game_Start, //=> FN_Game_Start
    User_Loading_Start, //=> FN_Loading_Start
    User_RemoteConfigLoaded, //=> FN_RemoteConfigLoaded
    User_Loading_End //=> FN_Loading_End
}

public enum BUTTON_NAME {
    Start,
    Play,
    Finish,
    PlayMySong,
    Menu,
    FreeVideo,
    Share,
    Tips,
    Replay,
    Next,
    Shop,
    Settings,
    Remove_Ads,
    Update,
    Update_Skip,
    Favorite_Remove,
    Favorite_Add,
    Preview,
    Continue_Gems,
    Continue_Video,
    Rate_Later,
    Rate_DontAsk,
    Rate,
    Rate_Feedback,
    Submit_Feedback,
    Social_Song,
    Balls,
    Achievement,
    LeaderBoard_Total,
    Publish_Song,
    Song_Details,
    LeaderBoard_Song,
    Favorite_Tip,
    Get_Diamond_Home,
    Quit,
    Continue_Click_Video,
    Continue_Click_Vip,
    Continue_Cancel,
    Search,
    VipGameClick,
    VipPlayNơw,
    AdFsReward_Close,
    AdFsReward_AutoClose,
    AdFsReward_Play,
    AdFsReward_Claim,
    Continue_Free,
    PlaytimeReward,
    FreeGemHome,
    MysteryBox,
    HomeNoAds,
    HomeStarterPack,
    SpecialOffer,
    Discover
}

public enum FIRE_EVENT {
    Change_Language,
    Change_Theme,
    Change_Ball,
    Facebook_Share,
    Twitter_Share,
    Friend_Rank_View,
    Global_Rank_View,
    Daily_Rank_View,
    Weekly_Rank_View,
    Notifications_Click,
    LocalSong_Click,
    LocalSong_AskPermission,
    LocalSong_RequestPermission,
    LocalSong_PermissionDenied,
    LocalSong_FileSelector,
    LocalSong_SelectFile,
    LocalSong_ConfirmFile,
    LocalSong_AddedFile,
    LocalSong_SongClick,
    LocalSong_SongPlay,
    LocalSong_Remove,

    Change_Adn,
    FreeVideo_AutoOpen,
    FreeVideo_Click_Normal,
    FreeVideo_Click_Force,
    BannerAds_Show,
    BannerAds_Loaded,
    BannerAds_LoadFailed,
    fad_game_request,
    fad_game_cached,
    link_open,
    song_info,

    Challenge_Start,
    Challenge_Failed,
    Challenge_Replay,
    Challenge_Pass,

    Add_Favorite,
    Remove_Favorite,
    NextSong_Play,
    AB_test_join,
    shop_purchase,
    ball_purchase,
    impression_data_from_ironsource_arm, //TH-102
    ad_impression, //TH-102
    ad_impression_ama,
    freeskin_is_impression,
    freeskin_is_action,
    control_config_impression,
    control_config_confirm,

    //try new skin
    skin_offer_impression,
    skin_offer_action,
    skin_gameplay_impression,
    skin_gameplay_equip,
    skin_unlocked_popup,

    app_open,
    af_app_open,
    deep_link_navigate,
}

public struct SONG_TAG {
    public const string ALL           = "ALL";
    public const string NEW           = "NEW";
    public const string LOCK          = "LOCK";
    public const string MORE          = "MORE";
    public const string UNLOCKED      = "UNLOCKED";
    public const string OTHER         = "Other";
    public const string MY_SONGS      = "MY_SONGS";
    public const string SORT_BY_NAME  = "SORT_BY_NAME";
    public const string SORT_BY_SPEED = "SORT_BY_SPEED";
    public const string FAVORITES     = "FAVORITES";
    public const string UNFAVORITES   = "UNFAVORITES";
    public const string VIP           = "VIP";
    public const string SEARCH        = "SEARCH";
    public const string Monstercat    = "Monstercat";
    public const string Halloween     = "Halloween";
    public const string Xmas          = "XMAS";
    public const string EXPLORE       = "EXPLORE";
    public const string POPULAR       = "POPULAR";
    public const string WORLDCUP      = "WorldCup";
}

public struct CONFIG_STRING {
    public const string Sound       = "sound";
    public const string Music       = "music";
    public const string NoAds       = "noads";
    public const string Intro       = "intro";
    public const string NoFSAds     = "user_NoFSAds";
    public const string StarterPack = "user_starterpack";

    public const string HourlyGiftTime          = "hourlygift_time";
    public const string DailyGiftDate           = "dailygift_date"; //backup
    public const string PREF_ADMIN_PASSWORD_KEY = "admin_password_key";

    public const string DailyGiftNumber    = "dailygift_number"; //backup
    public const string WrongPlay          = "WrongPlay";
    public const string Bonus_Login        = "bonus_fb_login"; //backup
    public const string Bonus_Sharing      = "bonus_sharing"; //backup
    public const string CurrentSong        = "current_song";
    public const string ExtensionSong      = "extension_song"; // các bài hát user đã chơi, mà nằm ngoài list mặc định
    public const string SongVersion        = "songversion";
    public const string BestScore          = "bestscore"; //backup
    public const string BestProgression    = "BestProgression";
    public const string BestStars          = "beststars"; //backup
    public const string BestCrowns         = "BestCrown";
    public const string BestMileStone      = "bestmilestone";
    public const string TotalTileCollected = "TileCollected_";

    public const string Diamonds        = "diamonds"; //backup
    public const string ItemRevive      = "currency_revive"; //backup
    public const string RateLater       = "RateLater"; //backup
    public const string PlayCount       = "PlayCount";
    public const string Balls           = "balls"; //backup
    public const string SelectedBall    = "selected_ball"; //backup
    public const string ThemesUnlocked  = "ThemesUnlocked";
    public const string SelectedTheme   = "selected_theme"; //backup
    public const string OpenedSongTotal = "opened_song_total";
    public const string Retry           = "retry"; //backup
    public const string Revive          = "revive"; //backup
    public const string IsTop1          = "istop1"; //backup
    public const string PerfectCount    = "perfect_count"; //backup
    public const string TutorialPerfect = "perfect_count_tutorial"; //to exclude tutorial perfect count
    public const string NormalPerfect   = "perfect_count_normal"; //to exclude tutorial perfect count
    public const string ABTest          = "ABTest_op";
    public const string FreeAddSong     = "FreeAddSong";

    public const string DBKey_LeaderBoards = "LeaderBoardsPerfect";
    public const string DBKey_day          = "_day_";
    public const string DBKey_week         = "_week_";
    public const string DBKey_Total        = "Total";
    public const string DBKey_total_user   = "total_user";
    public const string DBKey_Users        = "Users";
    public const string DBKey_UserSongs    = "UserSongs";
    public const string DBKey_SongKeywords = "SongKeywords";
    public const string DBKey_Links        = "Links";
    public const string CurrentFolder      = "CurrentFolder";
    public const string ThemeArray         = "ThemeArray";
    public const string EndLessMode        = "ENDLESS_MODE";
    public const string Round_x            = "ROUND_X";
    public const string NewHighScore       = "NEW_HIGHSCORE";

    public const string SongUnlockCount = "SongUnlockCount";
    public const string SongPlayCount   = "SongPlayCount";
    public const string SongReviveCount = "SongReviveCount";
    public const string TutorialAd      = "TutorialAd";

    public const string first_open_time = "first_open_time";
    public const string last_open_time  = "last_open_time";
    public const string last_close_time = "last_close_time";

    public const string isDebug              = "IsDebug";
    public const string IsContentTool        = "IsContentTool";
    public const string IsSuperpowered       = "IsSuperpowered";
    public const string IsAutoPlay           = "isAutoPlay";
    public const string IsApprovalProcess    = "IsApprovalProcess";
    public const string IsSelectMIDI         = "IsSelectMIDI";
    public const string IsLongNotePrototype  = "IsLongNotePrototype";
    public const string IsSpecialTileControl = "IsSpecialTileControl";
    public const string IsSpecialTileV2      = "IsSpecialTileV2";
    public const string IsAdmin              = "IsAdmin";
    public const string ForceTheme           = "ForceTheme";
    public const string IsRVUser             = "IsRVUser";
    public const string FirstVisitedGame     = "FirstVisitedGame";

    public const string RandomCount       = "RandomCount";
    public const string FREE              = "FREE";
    public const string IsEnableLogViewer = "IsEnableLogViewer";

    public const string DateTimeFormat = "yyyy-MM-dd HH:mm:ss";
    public const string DateFormat     = "yyyy-MM-dd";
    public const string facebook       = "facebook";
    public const string PlayVibration  = "PlayVibration";

    public const string AuthTypeLogined = "AuthTypeLogined";

    public const string IsTrial        = "isTrial";
    public const string NotificationID = "NotificationID_";
    public const string count_event    = "count_event_";

    //Survey
    public const string CountShowSurvey = "CountShowSurvey_";
    public const string FinishSurvey    = "FinishSurvey_";
    public const string PlayAfterSubs   = "PlayAfterSubs";

    public const string EndlessModeByUser = "EndlessModeByUser";

    public const string BpmLocal           = "BmpLocal";
    public const string SongBpm            = "SongBpm";
    public const string SongStartTimeLocal = "SongStartTimeLocal";

    public const string ShowTutorialShop               = "ShowTutorialShop";
    public const string ShowVIPTooltip                 = "ShowVIPTooltip";
    public const string ShowShopTooltip                = "ShowShopTooltip";
    public const string FistClickVIPButton             = "FistClickVIPButton";
    public const string FistShowSubscriptionOnboarding = "FistShowSubscriptionOnboarding";

    public const string OnboardingFeature_PreviewSong_Appear = "OnboardingFeature_PreviewSong_Appear";
    public const string OnboardingFeature_TimeRedDotAppear   = "OnboardingFeature_TimeRedDotAppear";
    public const string OnboardingFeature_ExploreAppear      = "OnboardingFeature_ExploreAppear";
    public const string OnboardingFeature_ShopballAppear     = "OnboardingFeature_ShopballAppear";
    public const string OnboardingFeature_ProgressFirstStar  = "OnboardingFeature_ProgressFirstStar";
    public const string OnboardingFeature_ResultDiamond      = "OnboardingFeature_ResultDiamond";
    public const string OnboardingFeature_UnlockSongByGem    = "OnboardingFeature_UnlockSongByGem";

    //TrySkin
    public const string Key_TrySkin_CountSongToTrySkin = "TrySkin_CountSongToTrySkin";
    public const string Key_TrySkin_Date               = "TrySkin_Date";
    public const string Key_TrySkin_CountTrySkinInDay  = "TrySkin_NumberSkinTryInDay";
    public const string Key_CountTrySkinAppear         = "Key_CountTrySkinAppear";
    public const string Key_TrySkin_TriedSkins         = "TrySkin_TriedSkins";

    //Result Screen
    public const string Key_MultiplierCapping_Value = "ResultScreen_MultiplierAds_CappingValue";
    public const string Key_MultiplierCapping_Date  = "ResultScreen_MultiplierAds_CappingDate";

    public const string TutorialReward            = "TutorialReward";
    public const string TutorialRewardPopUpAppear = "TutorialRewardPopUpAppear";
    public const string NewUserGetTutorialReward  = "TutorialRewardNewUser";
    public const string CanGetTutorialReward      = "CanGetTutorialReward";

    //Approval Process
    public const string ACM_ApprovalProcess = "ACM_ApprovalProcess";

    // Freegem
    public const string FreeGem_Date  = "FreeGem_Date"; //TH-1556: Economy free gem in shop
    public const string FreeGem_Index = "FreeGem_Index"; //TH-1556: Economy free gem in shop

    //Long note prototype
    public static string Admin_LongNotePrototype  = "Admin_LongNotePrototype";
    public static string Admin_SpecialTileControl = "Admin_SpecialTileControl";
    public static string Admin_SpecialTileV2      = "Admin_SpecialTileV2";
    public static string SelectMIDIVersion        = "MIDI_MultipleVersion";

    public const string Currency_Total_Earn  = "Currency_Total_Earn";
    public const string Currency_Total_Spend = "Currency_Total_Spend";

    //Mystery Box
    public const string Mysterybox_Free = "Mysterybox_Free";
    public const string Mysterybox_Ads  = "Mysterybox_Ads";

    //TH-2917: Old user gem popup
    public const string OldUserRewardCount = "rewarded_count";
    public const string ECONOMY_TUTORIAL   = "EconomyTutorial";

    public const string KEY_FEATURE_ELEMENT       = "Element Note";
    public const string Key_EarnedElement         = "ElementTile_Earned";
    public const string Key_ProirityEarnedElement = "ElementTile_Earned_Priority";
    public const string KEY_FEATURE_CHALLENGEMODE = "Challenge Mode";
}