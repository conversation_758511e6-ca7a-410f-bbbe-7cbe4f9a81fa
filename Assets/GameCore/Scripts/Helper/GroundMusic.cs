using System;
using System.Collections;
using UnityEngine;
using Music.ACM;
using Sirenix.OdinInspector;
using TilesHop.Cores.Pooling;

public class GroundMusic : Singleton<GroundMusic> {
    public static  string CurrentPreviewSong;
    public static  bool   IsDownloadContent;
    public static  float  PercentDownloadContent;
    private static string songIDLocal    = "";
    private static string songPathRemote = "";

    [SerializeField] private AudioSource audioSource;

    private                                                  float time      = 0;
    private                                                  bool  isLoading = false;
    public                                                   WWW   www;
    [ShowInInspector,Sirenix.OdinInspector.ReadOnly] private int   _loopCount;
    
    #region BGM Survey
    private const string    audioBackGroundPath   = "Demographic/BGM/BGM1_128";
    private const string    audioBackGroundV2Path = "Demographic/BGM/survey_bgm";
    #endregion

    public static event Action<string> OnLoadPreviewSongError;
    public static event Action OnStopPreviewSong;
    public static event Action OnUseMusic;

    private Coroutine _iePlayMusic;
    private Coroutine _iePauseMusic;

    private void Start() {
        SetMute(!Configuration.instance.MusicIsOn());
    }

    public void SetMute(bool isMute) {
        audioSource.mute = isMute;
    }

    private IEnumerator LoadMp3Data(string location, bool canplayPreviewAfterFailed) {
        Song song = null;
        bool isLocalSong = false;
        if (SongManager.instance.IsOfficial(songIDLocal)) {
            song = SongManager.instance.GetSongByPath(songIDLocal);
        } else if (SongManager.instance.IsLocalSong(songIDLocal)) {
            isLocalSong = true;
            song = SongManager.instance.userLocalSongs[songIDLocal];
        } else if (SongManager.instance.IsLocalTutorialSong(songIDLocal)) {
            song = SongManager.instance.tutorialLocalSong[songIDLocal];
        }

        if (song != null && !DownloadManager.instanceSafe.CheckDataAndDownload(song, false)) {
            isLoading = true;
            if (!isLocalSong) {
                while (DownloadManager.instanceSafe.IsNotReady()) {
                    yield return null;
                }
            }

            SuperpoweredSDK.instance.LoadMusic(song, true);

            while (!SuperpoweredSDK.instance.IsReady()) {
                yield return null;
            }

            SuperpoweredSDK.instance.SetVolume(0.4f);
            SuperpoweredSDK.instance.SetPosition(time);
            SuperpoweredSDK.instance.SetSpeed(1f);
            SuperpoweredSDK.instance.SmoothPlay(0.5f);

            isLoading = false;
        } else {
            if (canplayPreviewAfterFailed && song != null) {
                PlayPreviewSong(_loopCount, song, location, false);
            } else {
                Debug.LogWarning("Can't load the song to play music!!!!");
            }
        }
    }

    public void PlayMusic(string location, string songPathInput = null, bool canPlayPreviewIfFailed = false) {
        if (audioSource.isPlaying) {
            return;
        }

        if (string.IsNullOrEmpty(songPathInput)) {
            songPathInput = Configuration.instance.GetCurrentSong().path;
        } else if (songPathInput == songPathRemote) {
            _iePlayMusic = StartCoroutine(SmoothPlayAudioSource());
            return;
        }

        if (songPathInput != null) {
            bool needLoadNew = true;
            if (SuperpoweredSDK.instance.IsReady()) {
                if (songIDLocal.Equals(songPathInput)) {
                    // already loaded this song before
                    needLoadNew = false;
                    SuperpoweredSDK.instance.SetVolume(0.4f);
                    SuperpoweredSDK.instance.SetPosition(time);
                    SuperpoweredSDK.instance.SetSpeed(1f);
                    SuperpoweredSDK.instance.SmoothPlay(0.5f);
                } else {
                    //loaded another song -> need to reset
                    SuperpoweredSDK.instance.SetReady(false);
                }
            }

            if (needLoadNew && !isLoading) {
                if (songIDLocal != songPathInput) {
                    time = 0;
                    songIDLocal = songPathInput;
                }

                StartCoroutine(LoadMp3Data(location, canPlayPreviewIfFailed));
            }
        }
    }

    public void StopMusic(float smoothTime = 0.3f, bool isForceStop = false) {
        songPathRemote = "";
        StopAllCoroutines();
        if (SuperpoweredSDK.instance != null && SuperpoweredSDK.instance.IsPlaying()) {
            time = SuperpoweredSDK.instance.GetPosition();
            SuperpoweredSDK.instance.SmoothPause(smoothTime);
        }

        if (audioSource.isPlaying) {
            if (isForceStop) {
                audioSource.Stop();
            } else {
                _iePauseMusic = StartCoroutine(SmoothPauseAudioSource(smoothTime));
            }
        }
    }

    public void StopMusicInstantly() {
        songPathRemote = "";
        StopAllCoroutines();
        if (SuperpoweredSDK.instance != null && SuperpoweredSDK.instance.IsPlaying()) {
            SuperpoweredSDK.instance.PauseMusic();
        }

        audioSource.Stop();
    }

    public void PlayPreviewSong(int loopCount, Song song, string location, bool isTracking = true, string location_detail = null) {
        if ((Util.IsActionPhase() && UIController.ui.gameui.IsReadyToPlayPhase) || Util.IsGamePlaying()) {
            return;
        }

        if (isTracking) {
            AnalyticHelper.Button_Click(BUTTON_NAME.Preview);
        }
        _loopCount = loopCount;
        StopMusic(isForceStop: true);
        StopAllCoroutines();
        songPathRemote = song.path;

        DownloadManager.retryTimes = 0;
        bool isCheckLocal = song.isTutorialSong && !song.isTutorialOnlineSong;
        PreviewSong(song, location: location, location_detail: location_detail, isCheckLocal: isCheckLocal,
            isTracking: isTracking);
    }

    /// <summary>
    /// Download and play preview song!
    /// </summary>
    /// <param name="song">selected song</param>
    /// <param name="isCheckLocal"> true if tutorial song</param>
    /// <param name="isTracking"> Có thực thiện log analytics không? (false khi auto previews)</param>
    private void PreviewSong(Song song, string location, string location_detail, bool isCheckLocal,
                             bool isTracking = true) {
        if (song == null) return;

        if (isCheckLocal) {
            HandleLocalPreview(song, location, location_detail, isTracking);
            return;
        }

        if (TryPlayCachedPreview(song, location, location_detail, isTracking)) return;
        
        PlayRemotePreview(song, location, location_detail, isTracking);
    }

    private void HandleLocalPreview(Song song, string location, string location_detail, bool isTracking) {
        song.HasLocalPreview(clip => {
            if (song.path != songPathRemote) return;

            if (clip == null) {
                PreviewSong(song, location, location_detail: location_detail, isCheckLocal: false);
            } else {
                clip.name = song.path;
                audioSource.SetClip(clip);
                string path = string.IsNullOrEmpty(song.pathPreview) ? song.path : song.pathPreview;
                PlayPreviewSong(path, location, location_detail: location_detail, isTracking);
            }
        });
    }

    private bool TryPlayCachedPreview(Song song, string location, string location_detail, bool isTracking) {
        if (string.IsNullOrEmpty(song.acm_id_v3)) return false;

        string urlPreview = DownloadManager.instanceSafe.GetLocalPathAcmCachedData(song.acm_id_v3, ACMSDKv4.GetSlotIDPreview());
        if (string.IsNullOrEmpty(urlPreview)) return false;

        StartCoroutine(ReadyToPlay($"file://{urlPreview}", song.path, location,
            location_detail: location_detail, isTracking: isTracking, isCheckPreview: true));
        return true;
    }

    private void PlayRemotePreview(Song song, string location, string location_detail, bool isTracking) {
        if (!string.IsNullOrEmpty(song.pathPreview)) {
            StartPlayOnline(song.pathPreview, isTracking, location: location, location_detail: location_detail);
        } else if (IsLocalUserSong(song)) {
            PlayLocalUserSong(song, location, location_detail, isTracking);
        } else if (ACMSDK.IsInitialized) {
            DownloadACMPreview(song, location, location_detail, isTracking);
        }
    }

    private bool IsLocalUserSong(Song song) {
        return SongManager.instance.IsLocalSong(song.path) && 
               SongManager.instance.userLocalSongs.ContainsKey(song.path);
    }

    private void PlayLocalUserSong(Song song, string location, string location_detail, bool isTracking) {
        StartCoroutine(ReadyToPlay($"file://{SongManager.instance.userLocalSongs[song.path].GetLocalMp3Path()}",
            song.path, location, location_detail, isTracking: isTracking, isCheckPreview: true));
    }

    private void DownloadACMPreview(Song song, string location, string location_detail, bool isTracking) {
        IsDownloadContent = true;
        PercentDownloadContent = 0f;
        
        ACMSDK.Instance.DownloadContent(ACMSDKv4.GetSlotIDPreview(), song.acm_id_v3, string.Empty, 
            mp3Error => HandleACMError(song),
            mp3Info => HandleACMSuccess(song, mp3Info, location, location_detail, isTracking),
            OnChangeProgress);
    }

    private void HandleACMError(Song song) {
        if (song.path == songPathRemote) {
            OnLoadPreviewSongError?.Invoke(song.acm_id_v3);
        }
    }

    private void HandleACMSuccess(Song song, DownloadInfo mp3Info, string location, string location_detail, bool isTracking) {
        if (song.path == songPathRemote) {
            StartCoroutine(ReadyToPlay($"file://{mp3Info.LocalPath}", song.path, location: location,
                location_detail: location_detail, isTracking: isTracking, isCheckPreview: true));
        }
        
        DownloadManager.instanceSafe.UpdateSongIdSlotIdLocalName(song.acm_id_v3, 
            mp3Info.content.slot_id, 
            System.IO.Path.GetFileName(mp3Info.localPath), 
            mp3Info.content.id);
        
        IsDownloadContent = false;
        PercentDownloadContent = 1f;
    }

    private void OnChangeProgress(float percent) {
        PercentDownloadContent = percent;
    }

    private void StartPlayOnline(string songPath, bool isTracking, string location, string location_detail) {
        if (!DownloadManager.IsMaxRetry()) {
            if (SongManager.instance.IsLocalSong(songPath) && SongManager.instance.userLocalSongs.ContainsKey(songPath)) {
                if (SongManager.instance.userLocalSongs[songPath].diamonds < 0) {
                    StartCoroutine(ReadyToPlay(
                        $"file://{SongManager.instance.userLocalSongs[songPath].GetLocalMp3Path()}", songPath, location,
                        location_detail: location_detail, isTracking: isTracking));
                } else {
                    string path = songPath;
#if UNITY_IOS
                    /*
                    if (!songPath.Contains("/"))
                    {
                        path = Item.GetIOSLocalPath(songPath);
                    }*/
                    PlayMusic(songPath);
#else
                    StartCoroutine(ReadyToPlay("file://" + path, songPath, location, isTracking: isTracking,
                        location_detail: location_detail));
#endif
                }
            } else if (SongManager.instance.IsOfficial(songPath) ||
                       SongManager.instance.IsLocalTutorialSong(songPath)) {
                string url = DownloadManager.RemotePath(songPath, true);
                if (url != null) {
                    url = url.Replace("MIDI/", "Preview/");
                    if (!url.Contains(FILE_EXT.MP3)) {
                        url += FILE_EXT.MP3;
                    }

                    StartCoroutine(ReadyToPlay(url, songPath, location, isTracking: isTracking,
                        location_detail: location_detail));
                }
            }
        }
    }

    public IEnumerator ReadyToPlay(string url, string songPath, string location, string location_detail,
                                   bool isTracking = true, bool isCheckPreview = false) {
        // Get remote music
        if (www != null) {
            www.Dispose();
        }

        www = new WWW(url);
        yield return www;

        if (isCheckPreview && songPathRemote != songPath) {
            yield break;
        }

        if (www.error == null) {
            AudioClip clip = www.GetAudioClip(false, false);
            if (clip != null && clip.loadState == AudioDataLoadState.Loaded) {
                clip.name = songPath;
                audioSource.SetClip(clip);
                PlayPreviewSong(songPath, location, location_detail, isTracking);
            } else {
                StartPlayOnline(songPath, isTracking, location, location_detail);
            }
        } else {
            Debug.LogWarning("[ReadyToPlay] cannot download " + www.url);
            StartPlayOnline(songPath, isTracking, location, location_detail);
        }
    }

    private void PlayPreviewSong(string songPath, string location, string location_detail = null,
                                 bool isTracking = true) {
        if ((Util.IsActionPhase() && UIController.ui.gameui.IsReadyToPlayPhase) || Util.IsGamePlaying()) {
            return;
        }

        CurrentPreviewSong = songPath;

        _iePlayMusic = StartCoroutine(SmoothPlayAudioSource());
        if (SongManager.instance.IsOfficial(songPath)) {
            if (isTracking) {
                Song song = null;
                if (SongManager.instance.TryGetSong(songPath, out Song instanceSong)) {
                    song = instanceSong;
                }
                
                if (song != null) {
                    AnalyticHelper.LogSong(SONG_STATUS.song_preview, song, location, songPlayTypeDetail: location_detail);
                }
            }

            SongList.previewSongID = songPath;
            SongList.startPreviewTime = Time.time;
        }
    }

    private IEnumerator SmoothPlayAudioSource(float timer = 1f) {
        if (audioSource.clip == null) {
            yield break;
        }

        if (RemoteConfig.instance.SilentMode_IsEnable) {
            AmanoteNativeBinding.PlaySoundInSilentMode();
        }

        if (_iePauseMusic != null) {
            StopCoroutine(_iePauseMusic);
        }

        audioSource.Play();
        OnUseMusic?.Invoke();

        float orignial = 0;
        float destination = 1;
        float t = 0;
        float delta = Time.deltaTime / timer;
        float length = audioSource.clip.length;
        while (t < 1) {
            t += delta;
            audioSource.volume = Mathf.Lerp(orignial, destination, t);
            yield return null;
        }

        yield return new WaitForSeconds(length - timer * 2f);

        _iePauseMusic = StartCoroutine(SmoothPauseAudioSource());

        yield return new WaitForSeconds(timer * 1.1f);

        if (_loopCount > 0) {
            _loopCount--;
            if (_loopCount <= 0) {
                PlayBGSurveyMusic(true);
                OnStopPreviewSong?.Invoke();
                yield break;
            }
        }
        
        _iePlayMusic = StartCoroutine(SmoothPlayAudioSource()); //repeat
        
    }

    private IEnumerator SmoothPauseAudioSource(float timer = 1f) {
        float orignial = audioSource.volume;
        float destination = 0;

        float t = 0;
        float delta = Time.deltaTime / timer;
        while (t < 1) {
            t += delta;
            audioSource.volume = Mathf.Lerp(orignial, destination, t);
            yield return null;
        }

        audioSource.Stop();
    }

    public bool IsCanResume() {
        return time > 0 && !audioSource.isPlaying &&
               (!SuperpoweredSDK.instance.IsPlaying() || SuperpoweredSDK.instance.GetVolume() == 0);
    }

    public void Resume() {
        if (audioSource.clip != null) {
            if (_iePlayMusic != null) {
                StopCoroutine(_iePlayMusic);
            }

            PlayMusic(audioSource.clip);
        }
        // else {
        //     SuperpoweredSDK.instance.Resume();
        // }
    }

    public bool IsPlaying() {
        bool isSPPlaying = SuperpoweredSDK.instance != null && SuperpoweredSDK.instance.IsPlaying();
        bool isAudioSourcePlaying = audioSource != null && audioSource.isPlaying;

        return isSPPlaying || isAudioSourcePlaying;
    }

    public void PlayMusic(AudioClip audioClip) {
        audioSource.clip = audioClip;
        _iePlayMusic = StartCoroutine(SmoothPlayAudioSource());
    }

    public void PlayBGSurveyMusic(bool newVersion) {
        string path = newVersion ? audioBackGroundV2Path : audioBackGroundPath;
        var clipBG = Resources.Load<AudioClip>(path);
        PlayMusic(clipBG);
        CurrentPreviewSong = path;
    }

    private IEnumerator IESmoothChangeAudioSourceVolume(float destination, float timer = 1f) {
        float orignial = audioSource.volume;
        float t = 0;
        float delta = Time.deltaTime / timer;
        while (t < 1) {
            t += delta;
            audioSource.volume = Mathf.Lerp(orignial, destination, t);
            yield return null;
        }

        if (destination < 0.1f) {
            audioSource.Stop();
        } else if (destination > 0.9f) {
            _iePlayMusic = StartCoroutine(SmoothPlayAudioSource());
        }
    }

    public  bool      _isForceControlSound;
    private int       _idOfController;
    private Coroutine _fadeMusic;

    public void ForceFadeMusic(float volume, float time) {
        if (_fadeMusic != null) {
            StopCoroutine(_fadeMusic);
        }

        _fadeMusic = StartCoroutine(IESmoothChangeAudioSourceVolume(volume, time));
    }

    public void SetForceControlSound(bool isForce, int lord) {
        _isForceControlSound = isForce;
        _idOfController = isForce ? lord : int.MinValue;
        if (isForce) {
            StopAllCoroutines();
        }
    }

    public bool IsControlByObject(int idObject) {
        if (!_isForceControlSound)
            return false;

        return idObject == _idOfController;
    }

    public AudioSource GetAudioSource() {
        return audioSource;
    }
}