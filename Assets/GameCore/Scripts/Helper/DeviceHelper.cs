using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Inwave;
using UnityEngine;

public static class DeviceHelper {
    public enum DeviceType {
        Low    = 0,
        Middle = 1,
        High   = 2
    }

    /// <summary>
    /// Check running device is low end or not
    /// </summary>
    /// <returns>true/false</returns>
    private static bool _isChecked = false;

    private static DeviceType _deviceType;

    public static bool IsLowEnd() {
        return GetDeviceType() == DeviceType.Low;
    }

    public static bool IsMedium() {
        return GetDeviceType() == DeviceType.Middle;
    }

    public static bool IsHigh() {
        return GetDeviceType() == DeviceType.High;
    }

    /// <summary>
    /// Run unique one time
    /// </summary>
    public static void FireDeviceDetails() {
        var param = AnalyticHelper.GetDefaultParam(location: string.Empty, includeLocation: false);
        param.Add("type", GetDeviceType().ToString());
        param.Add("DeviceModel", SystemInfo.deviceModel);
        param.Add("Memory", SystemInfo.systemMemorySize.ToString());
        param.Add("Processor", SystemInfo.processorCount.ToString());
        param.Add("Graphics", SystemInfo.graphicsMemorySize.ToString());
        param.Add("Shader", SystemInfo.graphicsShaderLevel.ToString());
        AnalyticHelper.FireString("DeviceInfo", param);
    }

    public static DeviceType GetDeviceType() {
        if (_isChecked) {
            return _deviceType;
        }

        _isChecked = true;

        int systemMemorySize = SystemInfo.systemMemorySize;
        int processorCount = SystemInfo.processorCount;
        int graphicsMemorySize = SystemInfo.graphicsMemorySize;

        if (systemMemorySize <= RemoteConfigBase.instance.LowEndDevice_Memory ||
            processorCount <= RemoteConfigBase.instance.LowEndDevice_Processor ||
            graphicsMemorySize <= RemoteConfigBase.instance.LowEndDevice_GraphicMemory) {
            _deviceType = DeviceType.Low;

        } else if (systemMemorySize <= RemoteConfigBase.instance.MiddleDevice_Memory ||
                   processorCount <= RemoteConfigBase.instance.MiddleDevice_Processor ||
                   graphicsMemorySize <= RemoteConfigBase.instance.MiddleDevice_GraphicMemory) {
            _deviceType = DeviceType.Middle;
        } else {
            _deviceType = DeviceType.High;
        }

        if (Configuration.isAdmin) {
            Logger.Log("[DeviceType]: " + "systemMemorySize: " + systemMemorySize + " processorCount: " +
                       processorCount + " graphicsMemorySize: " + graphicsMemorySize + " => DeviceType: " +
                       _deviceType);
        }

        if (_deviceType != DeviceType.Low) {
            List<string> listLowDevice = RemoteConfigBase.instance.LowEndDevice_Model.StringToList( new[] { ';' });
            bool isLowEndDevice = listLowDevice.Contains(SystemInfo.deviceModel);
            if (isLowEndDevice) {
                if (Configuration.instance.isTutorial) {
                    string log = $"Device is low end => {SystemInfo.deviceModel} old type => {_deviceType}";
                    CustomException.Fire("ForceLowEndDevice", log);
                }

                _deviceType = DeviceType.Low;
            }
        }

        return _deviceType;
    }
}