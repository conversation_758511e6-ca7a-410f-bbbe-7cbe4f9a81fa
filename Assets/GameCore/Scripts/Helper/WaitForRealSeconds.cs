using System.Collections.Generic;
using UnityEngine;

namespace TilesHop.Cores.Pooling{

    /// <summary>
    /// Custom yield instruction thay thế WaitForSecondsRealtime,
    /// có thể reset để tái sử dụng trong pool.
    /// </summary>
    public class WaitForRealSeconds : CustomYieldInstruction {
        private float _targetTime;

        public void Reset(float seconds) {
            _targetTime = Time.realtimeSinceStartup + seconds;
        }

        public override bool keepWaiting => Time.realtimeSinceStartup < _targetTime;
    }
}