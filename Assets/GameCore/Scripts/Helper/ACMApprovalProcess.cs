using System;
using Music.ACM;
using System.Collections;
using System.Collections.Generic;
using Music.ACM.Interfaces;
using UnityEngine;
using UnityEngine.UI;

public class ACMApprovalProcess : PopupUI {
    [SerializeField] private Text     _txtSongName;
    [SerializeField] private Text     _txtArtist;
    [SerializeField] private Text     _txtAcmId;
    [SerializeField] private Text     _txtLoading;
    [SerializeField] private Text     _txtError;
    [SerializeField] private Dropdown _drdSelectMp3;
    [SerializeField] private Dropdown _drdSelectLevelDesign;
    [SerializeField] private Dropdown _drdSelectPreview;
    [SerializeField] private Dropdown _drdSelectCoverImage;
    [SerializeField] private Button   _btnPreview;
    [SerializeField] private Button   _btnPlay;
    [SerializeField] private Button   _btnCancel;
    [SerializeField] private Image    _imgCover;
    [SerializeField] private Image    _imgLoading;

    private string _slotIdMp3;
    private string _slotIdLevelDesign;
    private string _slotIdPreview;
    private string _slotIdCoverImage;
    private bool   _isLoading;

    private Dictionary<string, List<Content>> allContents = new Dictionary<string, List<Content>>();
    private Action<string, string>            _onBtnPlayClicked;
    private string                            _location = LOCATION_NAME.approval_process.ToString();

    private void Awake() {
        _btnPreview.onClick.AddListener(OnBtnPreviewClicked);
        _btnPlay.onClick.AddListener(OnBtnPlayClicked);
        _btnCancel.onClick.AddListener(OnBtnCancelClicked);
    }

    public void ClosePopUp() {
        Close();
    }

    private void Start() {
        _imgLoading.gameObject.SetActive(false);
    }

    /// <summary>
    /// Load information of the current song.
    /// Formerly Show(), renamed to avoid confusion with base class' Show()
    /// </summary>
    public void FetchData(string songId, string mp3SlotId, string levelDesignSlotId,
                          Action<string, string> onBtnPlayClicked, string previewSlotId = "",
                          string coverImageSlotId = "") {
        _slotIdMp3 = mp3SlotId;
        _slotIdLevelDesign = levelDesignSlotId;
        _slotIdPreview = previewSlotId;
        _slotIdCoverImage = coverImageSlotId;

        _onBtnPlayClicked = onBtnPlayClicked;

        _drdSelectMp3.ClearOptions();
        _drdSelectLevelDesign.ClearOptions();
        _drdSelectPreview.ClearOptions();
        _drdSelectCoverImage.ClearOptions();
        _txtError.text = string.Empty;
        allContents.Clear();

        ShowLoading(true);
        if (string.IsNullOrEmpty(songId) == false) {
            ACMSDK.Instance.GetSong(songId, error => {
                if (error != null) {
                    ACMDebug.Log(error);
                    _txtError.text = error;
                    ShowLoading(false);
                }
            }, response => {
                _txtSongName.text = response.Name;
                _txtArtist.text = GetArtistName(response.Artists);
                _txtAcmId.text = songId;

                IContent[] contents = response.Contents;
                for (int i = 0; i < contents.Length; i++) {
                    Content content = (Content) contents[i];

                    if (allContents.ContainsKey(content.slot_id) == false) {
                        List<Content> slotContents = new List<Content>();
                        allContents[content.slot_id] = slotContents;
                    }

                    allContents[content.slot_id].Add(content);

                    Dropdown.OptionData optionData = new Dropdown.OptionData {
                        text = content.name
                    };
                    if (content.slot_id.Equals(mp3SlotId)) {
                        _drdSelectMp3.options.Add(optionData);
                        _drdSelectMp3.value = 0;
                        _drdSelectMp3.RefreshShownValue();
                    } else if (content.slot_id.Equals(levelDesignSlotId)) {
                        _drdSelectLevelDesign.options.Add(optionData);
                        _drdSelectLevelDesign.value = 0;
                        _drdSelectLevelDesign.RefreshShownValue();
                    } else {
                        if (!string.IsNullOrEmpty(previewSlotId) && (content.slot_id.Equals(previewSlotId))) {
                            _drdSelectPreview.options.Add(optionData);
                            _drdSelectPreview.value = 0;
                            _drdSelectPreview.RefreshShownValue();
                        }

                        if (!string.IsNullOrEmpty(coverImageSlotId) && (content.slot_id.Equals(coverImageSlotId))) {
                            _drdSelectCoverImage.options.Add(optionData);
                            _drdSelectCoverImage.value = 0;
                            _drdSelectCoverImage.RefreshShownValue();
                        }
                    }
                }

                ShowLoading(false);
            });
        }
    }

    private void OnBtnCancelClicked() {
        ClosePopUp();
    }

    private void OnBtnPreviewClicked() {
        string previewContentID = string.Empty;
        if (_drdSelectPreview.options.Count > 0) {
            previewContentID = GetContentId(_slotIdPreview, _drdSelectPreview.value);
            GroundMusic.instance.StopMusicInstantly();
            ACMSDK.Instance.DownloadContent(ACMSDKv4.GetSlotIDPreview(), _txtAcmId.text, previewContentID,
                (mp3Error) => { },
                (mp3Info) => {
                    StartCoroutine(GroundMusic.instance.ReadyToPlay($"file://{mp3Info.LocalPath}", _txtAcmId.text,
                        location: _location, location_detail: null, isTracking: false, isCheckPreview: false));
                }, null);
        }
    }

    private void OnBtnPlayClicked() {
        string mp3ContentID = string.Empty;
        string levelDesignContentId = string.Empty;
        if (_drdSelectMp3.options.Count > 0) {
            mp3ContentID = GetContentId(_slotIdMp3, _drdSelectMp3.value);
        }

        if (_drdSelectLevelDesign.options.Count > 0) {
            levelDesignContentId = GetContentId(_slotIdLevelDesign, _drdSelectLevelDesign.value);
        }

        _onBtnPlayClicked?.Invoke(mp3ContentID, levelDesignContentId);
    }

    private void ShowLoading(bool active) {
        if (active) {
            _imgLoading.gameObject.SetActive(true);
            _isLoading = true;
            StartCoroutine(ShowLoadingIenumerator());
        } else {
            _isLoading = false;
            _imgLoading.gameObject.SetActive(false);
        }
    }

    private IEnumerator ShowLoadingIenumerator() {
        while (_isLoading) {
            float alpha = Mathf.PingPong(Time.time, 1f);
            Color color = Color.black;
            color.a = alpha;
            _txtLoading.color = color;
            yield return null;
        }
    }

    private string GetArtistName(IArtist[] artists) {
        string artistName = string.Empty;
        for (int i = 0; i < artists.Length; i++) {
            Music.ACM.Artist artist = (Music.ACM.Artist) artists[i];
            artistName += artist.name;
            if (i < artists.Length - 1) {
                artistName += ", ";
            }
        }

        return artistName;
    }

    public string GetContentId(string slotId, int index) {
        if (allContents.ContainsKey(slotId)) {
            if (index < allContents[slotId].Count) {
                return allContents[slotId][index].id;
            }
        }

        return string.Empty;
    }
}