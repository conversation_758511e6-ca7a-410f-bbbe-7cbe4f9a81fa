using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DG.Tweening;
using Music.ACM;
using UnityEngine;

/// <summary>
/// trungvt
/// </summary>
public class UpdateSongContent : Singleton<UpdateSongContent> {
    #region Fields

    private bool _isProcessing;

    #endregion

    #region Methods

    public IEnumerator Run() {
        yield return null;

        List<Song> oldSongs = SongManager.instance.GetSongsAsList();

        for (int index = 0; index < oldSongs.Count; index++) {
            //for (int index = 0; index < 30; index++) {
            yield return Inwave.Utils.WaitUntil(() => _isProcessing == false);

            Song oldSong = oldSongs[index];
            string _songId = oldSong.acm_id_v3;
            Debug.Log($"[ProcessSong] {index} ID {_songId}");

            if (string.IsNullOrEmpty(_songId)) {
                continue;
            }

            _isProcessing = true;
            ACMSDK.Instance.SearchSong_ByIds(new List<string> { _songId }).ExtraFields("songParams,speed").OnError(error => {
                if (Utils.IsInternetReachable) {
                    CustomException.Fire("[SearchSong_ByIds] ", $"AcmID Error: {error} IDs: {_songId}");
                }
                DOVirtual.DelayedCall(0.1f, () => { _isProcessing = false; });

            }).OnSuccess(response => {
                SongData[] songDatas = response.data;
                if (songDatas != null && songDatas.Length > 0) {
                    Debug.Log($"[ProcessSong] {songDatas[0].song_name.name}");
                    Song newSong = ACMSDKv4.ParseSong(songDatas[0]);
                    UpdateData(oldSong, newSong);

                } else {
                    if (Utils.IsInternetReachable && !string.IsNullOrEmpty(response.ErrorMessage)) {
                        CustomException.Fire($"[SearchSong_ByIds]",
                            $"AcmID Error {_songId} Error Message {response.ErrorMessage}");
                    }
                }

                DOVirtual.DelayedCall(0.1f, () => { _isProcessing = false; });
            }).Run();
        }

        string data = GetData(oldSongs);
        FileHelper.SaveFile(data, "D:\\data.csv");

        Debug.Log($"[ProcessSong] END");
    }

    private string GetData(List<Song> songs) {
        StringBuilder sb = new StringBuilder();

        string header =
            "path,msc_path,acm_id_v3,name,artist,genre,bmp,diamonds,type,tags,theme,card,acm_id,label,filter,flag,info,urlArtistCover,linkYoutube,feat,midi_grid,midi_line,midi_tab";
        sb.AppendLine(header);

        for (int index = 0; index < songs.Count; index++) {
            Song song = songs[index];
            string datasSong = GetDataSong(song);
            sb.AppendLine(datasSong);
        }

        return sb.ToString();
    }

    private string GetDataSong(Song song) {
        StringBuilder sb = new StringBuilder();
        sb.Append(string.IsNullOrEmpty(song.path) ? string.Empty : song.path).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.msc_path) ? string.Empty : song.msc_path).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.acm_id_v3) ? string.Empty : song.acm_id_v3).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.name) ? string.Empty : song.name).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.artist) ? string.Empty : song.artist).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.genre) ? string.Empty : song.genre).Append(FileHelper.Split);
        sb.Append(song.bmp).Append(FileHelper.Split);
        sb.Append(song.diamonds).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.type) ? string.Empty : song.type).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.tags) ? string.Empty : song.tags).Append(FileHelper.Split);
        //sb.Append(string.IsNullOrEmpty(song.acm_id) ? string.Empty : song.acm_id).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.label) ? string.Empty : song.label).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.filter) ? string.Empty : song.filter).Append(FileHelper.Split);
        //sb.Append(string.IsNullOrEmpty(song.flag) ? string.Empty : song.flag).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.urlArtistCover) ? string.Empty : song.urlArtistCover).Append(FileHelper.Split);
        sb.Append(song.midi_grid > 0 ? song.midi_grid.ToString() : string.Empty).Append(FileHelper.Split);
        sb.Append(song.midi_line > 0 ? song.midi_line.ToString() : string.Empty).Append(FileHelper.Split);
        sb.Append(string.IsNullOrEmpty(song.midi_tab) ? string.Empty : song.midi_tab);

        return sb.ToString();
    }

    private void UpdateData(Song oldSong, Song newSong) {
        oldSong.name = string.IsNullOrEmpty(newSong.name) ? oldSong.name : newSong.name.Replace(FileHelper.Split, " -");
        oldSong.artist = string.IsNullOrEmpty(newSong.artist) ? oldSong.artist : newSong.artist.Replace(FileHelper.Split, " -");
        oldSong.genre = string.IsNullOrEmpty(newSong.genre) ? oldSong.genre : newSong.genre.Replace(FileHelper.Split, " -");
        oldSong.bmp = newSong.bmp > 0 ? oldSong.bmp : newSong.bmp;
    }

    #endregion
}