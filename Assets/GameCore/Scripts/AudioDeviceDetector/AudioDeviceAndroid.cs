using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if !UNITY_EDITOR && UNITY_ANDROID

public class AudioDeviceAndroid : IAudioDevice
{
    readonly AndroidJavaClass detectorClass = new AndroidJavaClass("com.youmusic.magictiles.AudioDeviceDetector");

    public void RegisterEvents()
    {
        Debug.Log("[AudioDeviceInfo] unity register event ");
        detectorClass.CallStatic("registerEvents");
    }

    public AudioDeviceInfo GetInfo()
    {
        string param = detectorClass.CallStatic<string>("getAudioInfo");
        Debug.Log("[AudioDeviceInfo] unity getinfo: " + param);
        return ParseStringInfo(param);
    }

    public AudioDeviceInfo ParseStringInfo(string param)
    {
        if (!string.IsNullOrEmpty(param))
        {
            string[] parameters = param.Split(';');
            if (parameters.Length == 2)
            {
                string name = AudioDeviceDetector.RemoveSurrogatePairs(parameters[0]);
                string rawType = parameters[1];
                AudioDeviceType type = MapDeviceType(rawType);
                if (type == AudioDeviceType.Internal)
                {
                    name = SystemInfo.deviceModel;
                }

                AudioDeviceInfo info = new AudioDeviceInfo()
                {
                    deviceType = type,
                    deviceName = name,
                    deviceRawType = rawType
                };
                return info;
            }
            Debug.LogError("[AudioDeviceInfo] cant parse param: " + param);
        }

        return null;
    }


    private AudioDeviceType MapDeviceType(string rawType)
    {
        if (string.IsNullOrEmpty(rawType)) return AudioDeviceType.Unknown;
        switch (rawType)
        {
            case "TYPE_BLUETOOTH_SCO":
            case "TYPE_BLUETOOTH_A2DP":
            case "TYPE_BLE_HEADSET":
            case "TYPE_BLE_SPEAKER":
            case "TYPE_BLE_BROADCAST":
                return AudioDeviceType.Bluetooth;

            case "TYPE_BUILTIN_SPEAKER_SAFE":
            case "TYPE_BUILTIN_MIC":
            case "TYPE_BUILTIN_EARPIECE":
            case "TYPE_BUILTIN_SPEAKER":
                return AudioDeviceType.Internal;

            case "TYPE_USB_HEADSET":
            case "TYPE_USB_DEVICE":
            case "TYPE_USB_ACCESSORY":
            case "TYPE_TELEPHONY":
            case "TYPE_LINE_ANALOG":
            case "TYPE_LINE_DIGITAL":
            case "TYPE_HDMI_ARC":
            case "TYPE_HDMI_EARC":
            case "TYPE_HDMI":
            case "TYPE_DOCK":
            case "TYPE_AUX_LINE":
            case "TYPE_WIRED_HEADSET":
            case "TYPE_WIRED_HEADPHONES":
                return AudioDeviceType.Wire;

            case "TYPE_FM":
            case "TYPE_FM_TUNER":
            case "TYPE_IP":
                return AudioDeviceType.Wireless;

            case "TYPE_REMOTE_SUBMIX":
            case "TYPE_BUS":
            case "TYPE_HEARING_AID":
            case "TYPE_UNKNOWN":
            case "TYPE_TV_TUNER":
            default:
                return AudioDeviceType.Unknown;
        }
    }
}
#endif