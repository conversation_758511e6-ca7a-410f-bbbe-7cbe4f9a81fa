using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;

public enum FREEVIDEO_TYPE {
    DIAMOND,
    SONG,
    BALL,
    THEME,
    HOURLY,
}

public class FreeVideoItem : OptimizedCellView {
    //pubic
    [Header("FreeVideo Item")] [SerializeField]
    private Text title;

    [SerializeField] private Text           text;
    [SerializeField] private Image          bg;
    [SerializeField] private Image          ballPreview;
    [SerializeField] private FREEVIDEO_TYPE type = FREEVIDEO_TYPE.HOURLY;
    [SerializeField] private Color          giftColorInactive;
    [SerializeField] private GameObject     btnWatch;
    [SerializeField] private GameObject     btnPlay;
    [SerializeField] private Image          giftImg;

    //private
    private int _indexInList = 0;

    //reward
    private Song   _songReward;
    private int    _idReward;
    private int    _amountHourlyReward;
    private string _location;

    private       Color  _giftColor;
    private       string _freeText  = string.Empty;
    private const string TimeFormat = "{0:00} : {1:00}";

    private bool  _isCountingtime;
    private float _countingtime;

    private void Awake() {
        _freeText = LocalizationManager.instance.GetLocalizedValue("FREE");
        LocalizationManager.instance.UpdateFont(title);
        LocalizationManager.instance.UpdateFont(text);
    }

    private void Update() {
        if (_isCountingtime) {
            _countingtime += Time.deltaTime;
            if (_countingtime >= 1) {
                _countingtime -= 1f;
                double remaining = ItweenShakeEffect.GetGiftTimeRemain();
                if (remaining > 0) {
                    text.text = String.Format(TimeFormat, (int) (remaining / 60), (int) (remaining % 60));
                    giftImg.color = giftColorInactive;
                } else {
                    text.text = _freeText;
                    giftImg.color = _giftColor;
                }
            }
        }
    }

    // Use this for initialization
    public void SetData(int index, FreeVideoItemData data, string location) {
        SetData(index, data.type, data.title, data.sprite, data.song, data.id);
        this._location = location;
    }

    private void SetData(int indexInList, FREEVIDEO_TYPE rewardType, string strTitle, Sprite bg, Song songReward,
                         int idReward) {
        this.type = rewardType;
        this.bg.sprite = bg;
        this._songReward = songReward;
        this._idReward = idReward;
        this._indexInList = indexInList;
        this._isCountingtime = false;
        //text.gameObject.SetActive (true);
        text.text = LocalizationManager.instance.GetLocalizedValue("WATCH_A_VIDEO");
        giftImg.gameObject.SetActive(false);
        ballPreview.gameObject.SetActive(false);
        btnWatch.SetActive(true);
        switch (rewardType) {
            case FREEVIDEO_TYPE.HOURLY:
                int remoteAmount = RemoteConfig.instance.GetFreeGiftValue();
                _amountHourlyReward = remoteAmount < 0 ? 5 : remoteAmount;
                title.text = LocalizationManager.instance.GetLocalizedValue("X_DIAMONDS")
                    .Replace("10", _amountHourlyReward.ToString());
                giftImg.gameObject.SetActive(true);
                _giftColor = giftImg.color;
                btnWatch.SetActive(false);
                _isCountingtime = true;
                _countingtime = 1f;
                break;

            case FREEVIDEO_TYPE.BALL:
                var ballConfig = BallManager.instance.GetBallConfig(idReward);
                title.text = ballConfig.name; //Set name follow ball id
                Sprite sprite = BallManager.instance.GetIcon(idReward);
                if (sprite is null) {
                    sprite = BallManager.instance.GetIcon(0);
                    Logger.LogError("[GetSprite] Cannot get sprite of " + idReward);
                }

                ballPreview.sprite = sprite;
                ballPreview.gameObject.SetActive(true);
                break;

            case FREEVIDEO_TYPE.THEME:
                title.text =
                    $"{LocalizationManager.instance.GetLocalizedValue("THEME2_NAME")} {LocalizationManager.instance.GetLocalizedValue("THEME")}";
                break;

            case FREEVIDEO_TYPE.SONG:
                title.text = _songReward.name;
                break;

            case FREEVIDEO_TYPE.DIAMOND:
                title.text = $"+{Configuration.instance.diamondsVideoBonus}";
                break;
        }
    }

    public void Button_Click() {
        if (FreeVideo.instance != null) {
            FreeVideo.instance.currentIndex = _indexInList;
        }

        SoundManager.PlayGameButton();
        bool activated = btnPlay.activeSelf;

        AnalyticHelper.FireEvent(FIRE_EVENT.FreeVideo_Click_Normal, new Dictionary<string, object> {
            {"Type", type.ToString()}
        });

        switch (this.type) {
            case FREEVIDEO_TYPE.BALL:
                if (!activated) {
                    AdsManager.instance.ShowRewardAds(VIDEOREWARD.item, null, _location, true, OnRewardVideoCompleted);
                } else {
                    Configuration.SetSelectedBall(_idReward, isForce: true, location: this._location);
                    GoToGamePlay(SONG_PLAY_TYPE.free_video_ball, null, false);
                }

                break;

            case FREEVIDEO_TYPE.THEME:
                if (!activated) {
                    AdsManager.instance.ShowRewardAds(VIDEOREWARD.item, null, _location, true, OnRewardVideoCompleted);
                } else {
                    GoToGamePlay(SONG_PLAY_TYPE.free_video_theme, null, false);
                }

                break;

            case FREEVIDEO_TYPE.SONG:
                if (!activated) {
                    SongLocationTracker.SetSongPlayType(SONG_PLAY_TYPE.free_video_song, true);
                    AdsManager.instance.ShowRewardAds(VIDEOREWARD.song_unlock, _songReward, _location, true,
                        OnRewardVideoCompleted);
                } else {
                    GoToGamePlay(SONG_PLAY_TYPE.free_video_song, Song.GetByPath(_songReward.path), true);
                }

                break;

            case FREEVIDEO_TYPE.HOURLY:
                ItweenShakeEffect.ReceiveGift(_amountHourlyReward);
				SoundManager.PlayFreeGiftCollect();
				break;

            case FREEVIDEO_TYPE.DIAMOND:
                if (Shop.instance != null && Shop.instance.IsNeedMore()) {
                    AnalyticHelper.NeedMore(TRACK_NAME.popup_shop_watch);
                }

                SoundManager.PlayGameButton();
                AdsManager.instance.ShowRewardAds(VIDEOREWARD.currency, null, _location, true, OnRewardVideoCompleted);
                break;
        }
    }

    private void OnRewardVideoCompleted(bool isCompleted = true) {
        if (isCompleted && this != null) {
            switch (this.type) {
                case FREEVIDEO_TYPE.BALL:
                    btnPlay.SetActive(true);
                    Configuration.SetOpenBall(_idReward, 0, location: this._location);
                    GoToGamePlay(SONG_PLAY_TYPE.free_video_ball, null, false);
                    AirfluxTracker.TrackRewardAdsImpression();
                    break;

                case FREEVIDEO_TYPE.THEME:
                    btnPlay.SetActive(true);
                    ThemeManager.instance.SetOpenTheme(_idReward);
                    AirfluxTracker.TrackRewardAdsImpression();
                    break;

                case FREEVIDEO_TYPE.SONG:
                    btnPlay.SetActive(true);
                    var song = Song.GetByPath(_songReward.path);
                    SongList.OpenSong(song, 0, SongUnlockType.@default);
                    GoToGamePlay(SONG_PLAY_TYPE.free_video_song, song, false);
                    AirfluxTracker.TrackRewardAdsImpression();
                    break;

                case FREEVIDEO_TYPE.DIAMOND:
                    if (FreeVideo.instance != null) {
                        FreeVideo.instance.ShowDiamondVfx(btnWatch.transform,
                            Configuration.instance.diamondsVideoBonus);
                    }
                    
                    Configuration.UpdateDiamond(Configuration.instance.diamondsVideoBonus, CurrencyEarnSource.VIDEO.ToString(),
                        TRACK_LOCATION.ad_shop);

                    IncreaseDiamondsVideoBonus();
                    AirfluxTracker.TrackRewardAdsImpression(Configuration.instance.diamondsVideoBonus);
                    break;
            }
        }
    }

    private void GoToGamePlay(SONG_PLAY_TYPE songPlayType, Song song = null, bool isSongClick = true) {
        SongLocationTracker.SetSongPlayType(songPlayType, true);
        if (TransitionInOut.isInstanced) {
            TransitionInOut.instance.TransitionOut(btnWatch.transform.position, () => {
                Util.GoToGamePlay(song, location: this._location, isSongClick);
            });
        } else {
            Util.GoToGamePlay(song, location: this._location, isSongClick);
        }
    }

    private void IncreaseDiamondsVideoBonus() {
        if (Configuration.instance.diamondsVideoBonus == 0) {
            Configuration.instance.diamondsVideoBonus = RemoteConfig.instance.Diamond_Video_1st;
        }

        int nextValue = Configuration.instance.diamondsVideoBonus + RemoteConfig.instance.Diamond_Video_Step;
        if (nextValue > RemoteConfig.instance.Diamond_Video_Max) {
            nextValue = RemoteConfig.instance.Diamond_Video_1st;
        }

        StartCoroutine(UpdateText(Configuration.instance.diamondsVideoBonus, nextValue));
        Configuration.instance.diamondsVideoBonus = nextValue;
    }

    private IEnumerator UpdateText(int from, int to) {
        float t = 0;
        float time = 1f;
        while (t < time) {
            t += Time.deltaTime;
            title.text = $"+{(int) Mathf.Lerp(from, to, t / time)}";
            yield return null;
        }
    }

    public override void SetData(IData _data) { }
}