using TilesHop.EconomySystem;
using UnityEngine;
using UnityEngine.UI;

public class SubscriptionUIv5 : SubscriptionUI {
    [Header("Subscription UI v5")][SerializeField]
    private Text txtGemBenefit; [SerializeField]
    private Text txtReviveBenefit;

    private EconomyIAPRemoteData _economyIAPRemoteData;

    protected override void Start() {
        base.Start();
        _economyIAPRemoteData = Configuration.instance.GetEconomyRemoteConfig();
        if (_economyIAPRemoteData != null) {
            txtReviveBenefit.text =
                string.Format(LocalizationManager.instance.GetLocalizedValue("SUB_PAYWALL_ECONOMY_DESC_REVIVE"),
                    _economyIAPRemoteData.Subscription_Revive_Free);

            txtGemBenefit.text =
                string.Format(LocalizationManager.instance.GetLocalizedValue("SUB_PAYWALL_ECONOMY_DESC_GEM"),
                    RemoteConfig.instance.Diamond_DailyVipReward);
        }
    }
}