using UnityEngine;
using UnityEngine.UI;

public class SubscriptionUIv4 : SubscriptionUI {
    [Header("Ver 4")] [SerializeField] private GameObject groupOfferContainer;
    [SerializeField]                   private Text       txtDiscount;
    [SerializeField]                   private Text       txtOldPrice;
    [SerializeField]                   private Text       txtNewPrice;
    [SerializeField]                   private GameObject iconLoadingPrice;

    public const int PercentOff = 30;

    protected override void OnEnable() {
        base.OnEnable();
        desc3.text =
            $"{string.Format(LocalizationManager.instance.GetLocalizedValue("DAY_TRIAL"), remoteConfig.Subscription_TrialPeriod)}. {LocalizationManager.instance.GetLocalizedValue("BENEFIT_VIP_CANCEL")}";
        SubPackageManager.OnPriceUpdate += UpdateSpecialOffer;
        bool loadedPrice = SubPackageManager.IsLoadedPrices;
        if (loadedPrice) {
            UpdateSpecialOffer();
        } else {
            ShowLoadingOffer();
        }
    }

    protected override void OnDisable() {
        base.OnDisable();
        SubPackageManager.OnPriceUpdate += UpdateSpecialOffer;
    }

    private void UpdateSpecialOffer() {
        iconLoadingPrice.SetActive(false);
        groupOfferContainer.SetActive(true);

        var idPack = IAPDefinitionId.subscription_month;
        txtDiscount.text = $"{PercentOff}% {LocalizationManager.instance.GetLocalizedValue("PRICE_OFF")}";
        float currentPrice = SubPackageManager.GetPackagePriceValue(idPack);
        float oldPrice = PercentOff >= 100 ? currentPrice : Mathf.RoundToInt(currentPrice / (1 - PercentOff / 100f));
        if (oldPrice > 10000) {
            oldPrice = Mathf.RoundToInt(oldPrice / 1000) * 1000;
        } else if (oldPrice > 1000) {
            oldPrice = Mathf.RoundToInt(oldPrice / 100) * 100;
        } else if (oldPrice > 100) {
            oldPrice = Mathf.RoundToInt(oldPrice / 10) * 10;
        }

        txtOldPrice.text = Util.PriceToString(oldPrice, SubPackageManager.GetPackagePriceCode(idPack));
        txtNewPrice.text = Util.PriceToString(currentPrice, SubPackageManager.GetPackagePriceCode(idPack));
    }

    private void ShowLoadingOffer() {
        iconLoadingPrice.gameObject.SetActive(true);
        groupOfferContainer.SetActive(false);
    }

    protected override void OnSelectSub(IAPDefinitionId id) {
        base.OnSelectSub(id);
        desc3.text =
            $"{string.Format(LocalizationManager.instance.GetLocalizedValue("DAY_TRIAL"), remoteConfig.Subscription_TrialPeriod)}. {LocalizationManager.instance.GetLocalizedValue("BENEFIT_VIP_CANCEL")}";
    }

    protected override void UpdatePackageDetail() {
        base.UpdatePackageDetail();
        desc3.text =
            $"{string.Format(LocalizationManager.instance.GetLocalizedValue("DAY_TRIAL"), remoteConfig.Subscription_TrialPeriod)}. {LocalizationManager.instance.GetLocalizedValue("BENEFIT_VIP_CANCEL")}";
    }
}