using System;
using TilesHop.EconomySystem;
using UnityEngine;
using UnityEngine.UI;

public class ShopPackage : MonoBehaviour {
    #region SerializeField Fields

    [SerializeField] protected IAPDefinitionId id;
    [SerializeField] protected Text            priceText;
    [SerializeField] private   Text            packageNameText;
    [SerializeField] private   Text            descText;
    [SerializeField] private   Transform       label;
    [SerializeField] private   Text            bargainPercentText;
    [SerializeField] private   int             _amount;

    #endregion

    #region Fields

    private string _originalPrice;
    private string _originalPackageName;
    private string _productPrice;

    private Shop.ShopRemoteData remoteData;

    #endregion

    #region Unity Methods

    // Use this for initialization
    protected virtual void OnEnable() {
        if (!RemoteConfig.instance.Enable_IAP) {
            gameObject.SetActive(id == IAPDefinitionId.watchvideo);
            return;
        }

        bool noSubOffer = !SubscriptionController.IsEnableSubscription() || SubscriptionController.IsSubscription();

        switch (id) {
            case IAPDefinitionId.subscription_week when SubPackageManager.isEnableMultiple || noSubOffer:
            case IAPDefinitionId.removeads
                when !Configuration.CanShowOfferNoFSAds():
            case IAPDefinitionId.watchvideo when SubscriptionController.IsSubscriptionVip():
                gameObject.SetActive(false);
                break;
            default:
                if (id != IAPDefinitionId.watchvideo) {
                    _originalPrice = priceText.text;
                    _originalPackageName = packageNameText.text;
                    string productName = IapBase.GetProductName(id);
                    _productPrice = IapBase.GetPriceString(id);

                    if (id == IAPDefinitionId.subscription_week) {
                        string price = SubPackageManager.GetPackagePrice(id);
                        priceText.text = Util.BuildString("/", price, LocalizationManager.instance.GetLocalizedValue("WEEK"));
                    } else if (!string.IsNullOrEmpty(_productPrice)) {
                        priceText.text = _productPrice;

                        if (id != IAPDefinitionId.diamond1) {
                            
                        }
                    }

                    if (!string.IsNullOrEmpty(productName)) {
                        string tit = System.Text.RegularExpressions.Regex.Replace(productName, @"\((.*)\)", "");
                        string[] titSplit = tit.Split('-');
                        if (titSplit.Length == 2) {
                            if (label != null) {
                                label.gameObject.SetActive(true);
                                label.GetChild(0).GetComponent<Text>().text = titSplit[1].Trim();
                            }

                            packageNameText.text = titSplit[0].Trim();
                        } else {
                            packageNameText.GetComponent<Text>().text = tit;
                        }
                    }

                    remoteData = RemoteConfigBase.instance.GetShopRemoteData(id.ToString());
                    if (remoteData != null) {
                        _amount = remoteData.value;

                        if (bargainPercentText != null) {
                            if (remoteData.bargainPercent > 0) {
                                label.gameObject.SetActive(true);
                                bargainPercentText.text = $"<size=18>{remoteData.bargainPercent}%</size>\nMore";
                            } else {
                                label.gameObject.SetActive(false);
                            }
                        }
                    } else {
                        // hide pack item when no related config is found
                        gameObject.SetActive(false);
                    }
                }

                if (descText != null && _amount != 0) {
                    if (RemoteConfigBase.instance.ShopUI_Revamp_Enable) {
                        descText.text =
                            $"<b>{GameConstant.IntToCurrencyFormatString(_amount)}</b> {LocalizationManager.instance.GetLocalizedValue("DIAMONDS").ToLower()}";
                    } else if (RemoteConfigBase.instance.ShopUI2_Enable) {
                        descText.text =
                            $"+<b>{GameConstant.IntToCurrencyFormatString(_amount)}</b> {LocalizationManager.instance.GetLocalizedValue("DIAMONDS").ToLower()}";
                    } else {
                        descText.text = $"+{GameConstant.IntToCurrencyFormatString(_amount)}";
                    }
                }

                break;
        }

        LocalizationManager.instance.UpdateFont(descText);
        LocalizationManager.instance.UpdateFont(priceText);
        LocalizationManager.instance.UpdateFont(packageNameText);
    }

    #endregion

    public virtual void Purchase_Click() {
        if (IapBase.IsProcessingPurchase) {
            Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("YOUR_PAYMENT_BEING_PROCESSED"));
            return;
        }

        SoundManager.PlayGameButton();
        //if (id != IAPDefinitionId.subscription_week) {

        //Shop.amount = _amount;

        if (id == IAPDefinitionId.subscription_year || id == IAPDefinitionId.subscription_month ||
            id == IAPDefinitionId.subscription_week || id == IAPDefinitionId.subs_no_ads) {
            SubscriptionController.SetScreen(SUBSCRIPTION_SCREEN.shop_diamond.ToString());
            SubscriptionController.ShowSubscription(SUBSCRIPTION_SCREEN.shop_diamond.ToString(), false, userOpen: true);
        } else {
            string productId;
            if (remoteData != null) {
                string remotePack = Inwave.Utils.IsAndroid()
                    ? remoteData.androidId
                    : remoteData.iosId;
                productId = IapBase.IsExistPackage(remotePack) ? remotePack : id.ToString();
            } else {
                productId = IapBase.GetProductID(id);
            }
            BuyProduct(productId);

            AnalyticHelper.IAP_Click(IapBase.GetProductID(id), _originalPackageName, _originalPrice);
        }
    }

    private void BuyProduct(string idPack) {
        EconomyTrackingEvents.TrackIAPShopPurchase(EconomyTrackingEvents.IAPShopPurchaseState.Click, Shop.lastLocation,
            idPack, IapBase.GetPriceCode(id)
            , _amount, _productPrice);
        Configuration.instance.BuyProduct(id, idPack, (isSuccess, message) => {
            if (isSuccess) {
                OnBuyIAPSuccess();
            }

            EconomyTrackingEvents.TrackIAPShopPurchase(
                isSuccess
                    ? EconomyTrackingEvents.IAPShopPurchaseState.Success
                    : EconomyTrackingEvents.IAPShopPurchaseState.Cancel,
                Shop.lastLocation,
                idPack,
                IapBase.GetPriceCode(id)
                , _amount,
                _productPrice);
        });
    }

    protected virtual void OnBuyIAPSuccess() {
        if (_amount != 0) {
            Configuration.UpdateDiamond(_amount, CurrencyEarnSource.IAP.ToString(), CurrencyEarnSource.iap_shop.ToString());
			SoundManager.PlayGemPurchased();
		} else {
            Logger.EditorLogError($"Can't handle this package??? {id.ToString()}");
        }
    }

    public void Refresh() {
        OnEnable();
    }
}