using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Amanotes
{
    [CreateAssetMenu(fileName = "SDKVersions", menuName = "ScriptableObjects/GM Tool/sdk verions")]
    public class SDKVersionsSO : ScriptableObject
    {
        public List<VersionData> ISAdapterVersions = new List<VersionData>();

        public List<VersionData> FirebaseVersions = new List<VersionData>();

        [System.Serializable]
        public class VersionData
        {
            public string name;
            public string version;
        }
    }
}
