using System;
using System.Collections;
using UnityEngine;

/// <summary>
/// trungvt
/// </summary>
public class ImageColor : MonoBehaviour {
    #region Fields

    [SerializeField] private SpriteRenderer[] spriteRenderers;
    [SerializeField] private Color            color;

    private       Color _tmpColor;
    private const float TOLERANCE = 0.01f;

    #endregion

    #region Unity Methods

    private void FixedUpdate() {
        if (Math.Abs(_tmpColor.r - color.r) > TOLERANCE || Math.Abs(_tmpColor.g - color.g) > TOLERANCE ||
            Math.Abs(_tmpColor.b - color.b) > TOLERANCE) {
            _tmpColor = color;
            foreach (SpriteRenderer spriteRenderer in spriteRenderers) {
                if (spriteRenderer == null) continue;
                float oldColorA = spriteRenderer.color.a;
                spriteRenderer.color = new Color(color.r, color.g, color.b, oldColorA);
            }
        }
    }

# if UNITY_EDITOR

    Unity.EditorCoroutines.Editor.EditorCoroutine _editorCoroutine;

    private void OnValidate() {
        if (_editorCoroutine == null && !Application.isPlaying) {
            _editorCoroutine = Unity.EditorCoroutines.Editor.EditorCoroutineUtility.StartCoroutine(CheckUpdate(), this);
        }
    }

    private IEnumerator CheckUpdate() {
        while (true) {
            yield return null;

            FixedUpdate();
        }
    }
#endif

    #endregion
}