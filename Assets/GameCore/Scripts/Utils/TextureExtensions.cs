using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class TextureExtensions {
    public static Color GetMainColor(this Texture2D texture2D) {
        float r = 0f;
        float g = 0f;
        float b = 0f;
        int total = 0;
        for (int i = 0; i < texture2D.height; i++) {
            for (int j = 0; j < texture2D.width; j++) {
                Color pixelColor = texture2D.GetPixel(j, i);
                r += pixelColor.r;
                g += pixelColor.g;
                b += pixelColor.b;
                total++;
            }
        }

        r /= total;
        g /= total;
        b /= total;

        return new Color(r, g, b);
    }
}