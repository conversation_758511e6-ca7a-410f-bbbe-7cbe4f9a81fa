using UnityEngine;
using Spine.Unity;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class SpineGraphicAnimationController : MonoBehaviour {
#if UNITY_EDITOR
	public string animToPlay;
#endif
	private string currentAnim;
	private float animDuration, animTimeStamp, originTimeScale = 1;
	public void SetMachineSkin(string skinName) {
		GetComponent<SkeletonGraphic>().Skeleton.SetSkin(skinName);
	}
	public void PlayAnimation(string anim, bool isLoop = false) {
		if (currentAnim == anim) return;
		currentAnim = anim;
		var playAnim = GetComponent<SkeletonGraphic>().AnimationState.SetAnimation(0, anim, isLoop);
		animTimeStamp = Time.time;
		animDuration = playAnim.TrackTime;
	}
	public void PlayAnimationDuplicate(string anim, bool isLoop = false) {
		currentAnim = anim;
		var playAnim = GetComponent<SkeletonGraphic>().AnimationState.SetAnimation(0, anim, isLoop);
		animTimeStamp = Time.time;
		animDuration = playAnim.TrackTime;
	}
	public void AddAnimation(string anim, bool isLoop = false, float delay = 0)//call this function after PlayAnimation to queue a animation after the one in the Play function
	{
		GetComponent<SkeletonGraphic>().AnimationState.AddAnimation(0, anim, isLoop, delay);
	}
	public void Pause() {
		originTimeScale = GetComponent<SkeletonGraphic>().AnimationState.TimeScale;
		GetComponent<SkeletonGraphic>().AnimationState.TimeScale = 0;
	}
	public void Resume() {
		GetComponent<SkeletonGraphic>().AnimationState.TimeScale = originTimeScale;
	}
	public void PlayAnimation(string anim, bool isLoop, float timeScale) {
		currentAnim = anim;
		var playAnim = GetComponent<SkeletonGraphic>().AnimationState.SetAnimation(0, anim, isLoop);
		animTimeStamp = Time.time;
		playAnim.TimeScale = timeScale;
	}
	public void UpdateMesh() {
		GetComponent<SkeletonGraphic>().UpdateMesh();
	}

	/// <summary>
	/// Reload spine is like the Reload button on the inspector
	/// </summary>
	public void Reload(bool overwrite) {
		GetComponent<SkeletonGraphic>().Initialize(overwrite);
	}
}
#if UNITY_EDITOR
[CustomEditor(typeof(SpineGraphicAnimationController))]
public class SpineGraphicAnimationControllerEditor : Editor {
	public override void OnInspectorGUI() {
		base.OnInspectorGUI();
		if (GUILayout.Button("Play Animation")) {
			var source = (SpineGraphicAnimationController)target;
			source.PlayAnimationDuplicate(source.animToPlay);
		}
	}
}
#endif
