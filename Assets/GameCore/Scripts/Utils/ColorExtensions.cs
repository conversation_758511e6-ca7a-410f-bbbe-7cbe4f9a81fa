using UnityEngine;

public static partial class ColorExtensions {
    public static Color AddHueColor(this Color color, int deltaHue) {
        Color.RGBToHSV(color, out var hue, out var saturation, out var value);
        //Debug.LogError($"{hue} {saturation} {value}");
        hue += (deltaHue / 255f);
        if (hue > 1f) {
            hue -= 1f;
        }

        //Debug.LogError($"{hue} {saturation} {value}");
        return Color.HSVToRGB(hue, saturation, value);
    }
}