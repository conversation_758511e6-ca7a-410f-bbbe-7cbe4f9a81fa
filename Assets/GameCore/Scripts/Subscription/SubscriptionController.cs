using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using Inwave.CommunicationSystem.LocalSegmentation;
using TilesHop.Cores.IAASegmentation;
using UnityEngine;
using UnityEngine.Networking;
using Object = UnityEngine.Object;
#if UNITY_PURCHASING
using System.Collections.Generic;
using Facebook.MiniJSON;
using UnityEngine.Purchasing;
#endif

public enum SUBSCRIPTION_SOURCE {
    Clicked,
    Song,
    Trial,
    TrialRevive,
    TrialResult,
}

internal enum SUBSCRIPTION_SCREEN {
    intro                    = 0, //Intro
    session                  = 1, //Session
    song_result              = 2, //SongResult
    home                     = 3, //HomeButton
    shop_diamond             = 4, //Diamond
    shop_ball_inhome         = 5,
    shop_ball_ingame         = 6,
    shop_ball_inhome_mission = 7,
    shop_ball_ingame_mission = 8,
    revive                   = 9,
    DiscountOffer            = 10,
    shop_theme_seeAll        = 11,
    shop_theme_actionPhase   = 12,
    iaa_segmentation         = 13,
}

public enum SUBSCRIPTION_EVENT {
    sub_cata_show,
    sub_offer_click, //+ User_Subscription_Intro_Offer_Trial 
    sub_offer_close, //+ User_Subscription_Intro_Offer_Close
    sub_offer_cancel,
    Subscription_Trial,
    Subscription_Charge,
    Subscription_Cancel,
    sub_offer_show, //+ User_Subscription_Intro_Offer_Show
    sub_offer_show_first,
    Subscription_Expire,
    Subscription_Unknown,

    user_subscription,
    user_subscription_trial2pay,

    user_subscription_unsubs,
    user_subscription_resubs,
    sub_view_all_plans_show, //+ User_Subscription_Intro_Offer_Explore 
    FN_Subscription_Intro_Offer_Explore,
    sub_package_click,

    User_Subscription_Intro_Offer_Show,
    User_Subscription_Intro_Offer_Trial,
    User_Subscription_Intro_Offer_Close,
    User_Subscription_Intro_Offer_Explore,
}

public enum SUBSCRIPTION_ONBOARDING_EVENT {
    sub_onboarding_popup,
    sub_onboarding_page_click,
    sub_onboarding_page_open,
    sub_onboarding_page_try,
    sub_onboarding_tips,
    sub_onboarding_tips_tap,
    sub_onboarding_upgrade_click,
    sub_onboarding_close
}

public enum SUBSCRIPTION_USERTYPE {
    NONE   = 0,
    ACTIVE = 1,
    CANCEL = 2
}

public enum SUBSTYPE {
    VIP   = 0,
    NoAds = 1, //TH-676
}

public struct SubscriptionDataContainer {
    public SubscriptionData data;

    public string ToJSon(string ads_id, string receipt, string meta_info, string appsflyer_id, string pseudo_id) {
        data = new SubscriptionData {
            ads_id = ads_id,
            receipt = receipt,
            meta_info = meta_info,
            appsflyer_id = appsflyer_id,
            pseudo_id = pseudo_id
        };

        return JsonUtility.ToJson(this);
    }
}

[Serializable]
public struct SubscriptionData {
    public string ads_id;
    public string receipt;
    public string meta_info;
    public string appsflyer_id;
    public string pseudo_id;
}

public enum OfferType {
    Main,
    Ads,
    Trial
}

public class SubscriptionController : MonoBehaviour {
    public const string Key_UserType   = "SC_UserType";
    public const string Key_TrialDate  = "SC_TrialDate";
    public const string Key_ExpireDate = "SC_ExpireDate";

    private const string Key_FistTimeOffer     = "SC_FistTime";
    private const string Key_OfferCount        = "SC_OfferCount";
    private const string Key_Canceled          = "SC_Canceled";
    private const string Key_BonusDiamondDate  = "SC_BonusDiamondDate";
    private const string Key_SubmitedInfo      = "SC_SubmitedInfo";
    private const string Key_SubmitedTrial2Pay = "Key_SubmitedTrial2Pay";
    private const string Key_SubsType          = "SC_SubsType";

    public static bool   isShowed;
    static        string _screen;

    public delegate void BoughtHandler(bool isBuySuccess);

    public static event BoughtHandler OnChange;

    private static RemoteConfig remoteConfig => RemoteConfig.instance;
    public static bool fisrtSession;

    public static bool isJustBoughtSub; //TH-457

    //Subscription Receipt Submit 
    public        string subscriptionDbLink = "https://us-central1-beat-hopper.cloudfunctions.net/saveReceipt";
    public static bool   isUserOpen         = false;

    public static GameObject subPopupObject { get; private set; }

    public static DisableCallbackUI ShowSubscription(string screen, bool firstTimeOnly = false,
                                                     OfferType type = OfferType.Main, bool userOpen = false) {
        if (firstTimeOnly && isShowed) {
            return null;
        }
        if (!remoteConfig.Subscription_SessionPopup && screen == SUBSCRIPTION_SCREEN.session.ToString()) {
            return null;
        }

        if (!remoteConfig.Subscription_DiscountOffer && screen == SUBSCRIPTION_SCREEN.DiscountOffer.ToString()) {
            return null;
        }

        if (!IsEnableSubscription() || IsSubscriptionVip()) {
            return null;
        }

        isUserOpen = userOpen;
        SetScreen(screen);
        /*
        if (IsFistTimeOffer()) {
            _source = SUBSCRIPTION_SOURCE.intro.ToString();
            FistTimeOfferOff();
        } else {
            _source = source;
        }
        */

        int showCount = Count();

        AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.sub_offer_show, null, 0, showCount);

        string popupName;

        bool isUseEconomySubscription = false;
        if (remoteConfig.Economy_IsEnable) {
            var config = Configuration.instance.GetEconomyRemoteConfig();
            if (config != null && config.ModifiedSubscription_Enable) {
                isUseEconomySubscription = true;
            }
        }

        if (isUseEconomySubscription) {
            popupName = PopupName.SubscriptionUIMultipleV5;
        } else if (remoteConfig.SubscriptionUI_v4_IsEnable) {
            popupName = PopupName.SubscriptionUIMultipleV4;
        } else if (remoteConfig.SubscriptionFollowLocation_Enable) {
            popupName = PopupName.SubscriptionUIMultipleV2;
        } else {
            popupName = PopupName.SubscriptionUIMultipleV2;
        }

        GameObject showPopUp;
        if (!remoteConfig.SubscriptionFollowLocation_Enable) {
            showPopUp = Util.ShowPopUp(popupName);
        } else {
            showPopUp = Util.ShowPopUpCanvasHeight(popupName);
        }

        if (showPopUp != null) {
            PopupUI popupUI = showPopUp.GetComponent<PopupUI>();
            subPopupObject = showPopUp;
            
            if (popupUI != null) {
                OnPopupClose = null;

                popupUI.onStartClose = () => {
                    if (!isShowSubscriptionAllPlans) {
                        OnPopupClose?.Invoke();
                    }
                };
            }

            isShowed = true;
            return popupUI;

        } else {
            Debug.LogError("[ShowSubscription] cannot load " + popupName);
            return null;
        }
    }

    public static Action OnPopupClose;
    public static bool   isShowSubscriptionAllPlans;

    public static bool ShowSubscriptionAllPlans(int viewTime) {
        string popupName;
        bool isUseEconomySubscription = false;
        if (remoteConfig.Economy_IsEnable) {
            var config = Configuration.instance.GetEconomyRemoteConfig();
            if (config != null && config.ModifiedSubscription_Enable) {
                isUseEconomySubscription = true;
            }
        }

        if (isUseEconomySubscription) {
            popupName = PopupName.SubscriptionUIMultiple_detail_V5;
        } else if (remoteConfig.SubscriptionUI_v4_IsEnable) {
            popupName = PopupName.SubscriptionUIMultiple_detail_V4;
        } else if (remoteConfig.SubscriptionFollowLocation_Enable) {
            popupName = PopupName.SubscriptionUIMultiple_detail_V2;
        } else {
            popupName = PopupName.SubscriptionUIMultiple_detail_V2;
        }

        AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.sub_view_all_plans_show, null, viewTime);

        GameObject showPopUp;
        if (!remoteConfig.SubscriptionFollowLocation_Enable) {
            showPopUp = Util.ShowPopUp(popupName);
        } else {
            showPopUp = Util.ShowPopUpCanvasHeight(popupName);
        }

        if (showPopUp != null) {
            PopupUI popupUI = showPopUp.GetComponent<PopupUI>();
            if (popupUI != null) {
                popupUI.onStartClose = () => {
                    isShowSubscriptionAllPlans = false;
                    OnPopupClose?.Invoke();
                };
            }

            isShowed = true;
            isShowSubscriptionAllPlans = true;
            return true;

        } else {
            Debug.LogError("[ShowSubscription] cannot load " + popupName);
            return false;
        }
    }

    public static bool ShowSubscriptionOfferMonthly() {
        string popupName = PopupName.SubsOfferMonthly;
        GameObject showPopUp = Util.ShowPopUp(popupName);
        if (showPopUp != null) {
            PopupUI popupUI = showPopUp.GetComponent<PopupUI>();
            return popupUI != null;

        } else {
            Debug.LogError("[ShowSubscription] cannot load " + popupName);
            return false;
        }
    }

    public static bool ShowSubscriptionOfferMonthly(out GameObject popup) {
        string popupName = PopupName.SubsOfferMonthly;
        GameObject showPopUp = Util.ShowPopUp(popupName);
        if (showPopUp != null) {
            if (showPopUp.TryGetComponent(out PopupUI popupUI)) {
                popup = showPopUp;
                return true;
            }

            popup = null;
            return false;
        }

        Debug.LogError("[ShowSubscription] cannot load " + popupName);
        popup = null;
        return false;
    }

    public static bool ShowSubscriptionOfferNoAds() {
        if (!SubsOfferNoAdsScript.IsShowSubsOfferNoAds()) {
            return false;
        }

        if (IsSubscription()) {
            return false;
        }

        string popupName = PopupName.SubsOfferNoAds;
        GameObject showPopUp = Util.ShowPopUp(popupName);
        if (showPopUp != null) {
            PopupUI popupUI = showPopUp.GetComponent<PopupUI>();
            return popupUI != null;

        } else {
            Debug.LogError("[ShowSubscription] cannot load " + popupName);
            return false;
        }
    }

    public static void SetScreen(string screen) {
        _screen = screen;
    }

    public static string GetScreen() {
        return _screen;
    }

    public static int Count() {
        int count = PlayerPrefsCache.GetInt(Key_OfferCount);
        count++;
        PlayerPrefs.SetInt(Key_OfferCount, count);
        return count;
    }

    public static bool IsFistTimeOffer() {
        return PlayerPrefsCache.GetInt(Key_FistTimeOffer, 0) == 0;
    }

    public static void FistTimeOfferOff() {
        PlayerPrefs.SetInt(Key_FistTimeOffer, 1);
    }

    public static bool IsEnableSubscription() {
        return remoteConfig.Subscription_Enable || IsSubscription();
    }

    public static bool IsSubscription() {
        return PlayerPrefsCache.GetInt(Key_UserType, 0) == (int) SUBSCRIPTION_USERTYPE.ACTIVE;
    }

    public static bool IsSubscriptionVip() {
        return IsSubscription() && GetSubsType() == SUBSTYPE.VIP;
    }

    public static bool IsSubscriptionNoAds() {
        return IsSubscription() && GetSubsType() == SUBSTYPE.NoAds;
    }

    public static void SetUserType(SUBSCRIPTION_USERTYPE userType) {
        PlayerPrefsCache.SetInt(Key_UserType, (int) userType);
        GameCore.BHBalancy.CustomUserProperties.VIPStatus = (userType == SUBSCRIPTION_USERTYPE.ACTIVE);
        CustomException.SetKey(FirebaseKey.UserType, userType.ToString());
    }

    public static SUBSTYPE GetSubsType() {
        int i = PlayerPrefsCache.GetInt(Key_SubsType, 0);
        return Enum.TryParse(i.ToString(), out SUBSTYPE subsType) ? subsType : SUBSTYPE.VIP;
    }

    public static void SetSubsType(SUBSTYPE subsType) {
        PlayerPrefsCache.SetInt(Key_SubsType, (int) subsType);
        
    }

#if UNITY_PURCHASING
    public static void UpdateSubscription(Product p, IAPDefinitionId iapId = IAPDefinitionId.none) {
        string oldExpired = PlayerPrefs.GetString(Key_ExpireDate);
        if (p == null || p.receipt == null || !InAppPurchaser.checkIfProductIsAvailableForSubscriptionManager(p.receipt)) {
            long oldExpiredLong = 0;
            long.TryParse(oldExpired, out oldExpiredLong);
            long todayLong = DateTime.Now.ToUniversalTime().Ticks;
            if (IsSubscription() && oldExpiredLong <= todayLong) {
                SetExpired();
            }

            Debug.Log("This product is not available for SubscriptionManager class");
            return;
        }

        //purchase subscription
        Debug.Log("SubscriptionController StartSubscription!");

        string intro_json = "";
        SubscriptionManager subscription = new SubscriptionManager(p, intro_json);
        SubscriptionInfo info = subscription.getSubscriptionInfo();

        string newExpired = info.getExpireDate().ToUniversalTime().Ticks.ToString();
        bool isNew = newExpired != oldExpired;

        if (IsSubscription() && info.isExpired() == Result.True) {
            SetExpired();
        }

        if (IsSubscription() && info.isCancelled() == Result.True) {
            SetCancel();
        }

        if (info.isSubscribed() == Result.True) {
            //Save Subscription current package name
            //SubPackageManager.SaveCurrentConfig();
            StartSubscription(info.isFreeTrial() == Result.True, isNew, p);
            //BonusDiamond();
        }

        PlayerPrefs.SetString(Key_ExpireDate, newExpired);

    }
#endif
    /// <summary>
    /// Kiểm tra xem có đủ điều kiện show reward và show nếu đạt
    /// </summary>
    /// <returns></returns>
    public static bool CheckAndShowBonus() {
        if (!IsSubscriptionVip()) {
            return false;
        }

        if (!Configuration.CanEarnDiamond()) {
            return false;
        }

        if (IsBonus()) {
            SpawnDailyRewardPopup();
            return true;
        }

        return false;
    }

    private static bool IsBonus() {
        bool bonus = false;
        DateTime lastDate =
            Util.GetDateTimeByString(PlayerPrefs.GetString(Key_BonusDiamondDate), DateTime.Now.AddDays(-1));
        int dayDiff = (int) (DateTime.Now - lastDate.Date).TotalDays;
        if (dayDiff >= 1) {
            bonus = true;
        }

        return bonus;
    }

    private static GameObject SpawnDailyRewardPopup() {
        PlayerPrefs.SetString(Key_BonusDiamondDate, DateTime.Now.ToString(CONFIG_STRING.DateTimeFormat));
        int valueBonus = remoteConfig.Diamond_DailyVipReward;
        if (valueBonus > 0) {
            var popup = Util.ShowPopUp(PopupName.VipGift);
            popup.GetComponent<VipGift>().SetValue(valueBonus,
                LocalizationManager.instance.GetLocalizedValue("VIP_REWARD"), "Subscription");

            return popup;
        }

        return null;
    }
    
    public static IEnumerator IEShowVipDailyReward() {
        if (!IsSubscriptionVip()) {
            yield break;
        }

        if (!Configuration.CanEarnDiamond()) {
            yield break;
        }

        if (IsBonus()) {
            var popup = SpawnDailyRewardPopup();
            if (popup) {
                yield return new WaitWhile(() => popup && popup.activeSelf);
            }
        }
    }

    public static void ShowVIPNotice() {
        Util.ShowPopUp(PopupName.VipNotice);
    }
#if UNITY_PURCHASING
    public static void StartSubscription(bool isFreeTrial, bool isNew = false, Product p = null,
                                         long expiredDayVip = 0) {
        Debug.Log("StartSubscription");

        //Fire Trial to Pay Event
        SubmitTrialToPay();

        if (!IsSubscription()) {
            SubmitTransactionInfo();

            string currentPackageId = SubPackageManager.GetCurrentPackageId(p);
            SubPackageManager.SetCurrentPurchasedId(currentPackageId);

            if (Configuration.instanceSafe.isDebug) {
                long ticksExpired = DateTime.Now.ToUniversalTime().Ticks + 2000000000;
                DateTime exp = new DateTime(ticksExpired);
                Debug.Log("Time Expired:" + exp.ToShortTimeString());
                PlayerPrefs.SetString(Key_ExpireDate, ticksExpired.ToString());
            }

            PlayerPrefs.DeleteKey(Key_Canceled);
            SetUserType(SUBSCRIPTION_USERTYPE.ACTIVE);

            if (SubscriptionUI.instance != null) {
                SubscriptionUI.instance.Close();
                CheckAndShowBonus();
            }

            if (UIController.ui != null) {
                UIController.ui.gameover.vipButton.SetActive(false);
            }

            if (isNew) {
                Debug.Log("StartSubscription isNew");
                if (isFreeTrial) {
                    Debug.Log("StartSubscription isFreeTrial");
                    AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Trial,
                        SubPackageManager.GetCurrentPackageId(p));
                    PlayerPrefs.SetString(Key_TrialDate, (DateTime.Now.ToUniversalTime().Ticks).ToString());
                } else {
                    AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Charge,
                        SubPackageManager.GetCurrentPackageId(p));
                }
            }

            Configuration.instance.AddNoAds(true);
            isJustBoughtSub = true;
            OnChange?.Invoke(true);
        } else if (isNew) {
            //Already Subscription
            AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Charge,
                SubPackageManager.GetCurrentPackageId(p));
        } else {
            //Unknown status
            AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Unknown,
                SubPackageManager.GetCurrentPackageId(p));
        }

    }
#endif
    static void SubmitTransactionInfo() {
        if (!PlayerPrefs.HasKey(Key_SubmitedInfo)) {
            try {
                FireEventSubscriptionUser(SUBSCRIPTION_EVENT.user_subscription);
                PlayerPrefs.SetInt(Key_SubmitedInfo, 1);

                if (IsSubscription()) {
                    AnalyticHelper.FireString("Subscription_User_Resubmit");
                }
            } catch (Exception e) {
                CustomException.Fire("AnalyticHelper.SubmitTransactionInfo", e.ToString());
            }
        }
    }

    static void SubmitTrialToPay() {
        if (PlayerPrefs.HasKey(Key_TrialDate)) {
            if (!PlayerPrefs.HasKey(Key_SubmitedTrial2Pay)) {
                FireEventSubscriptionUser(SUBSCRIPTION_EVENT.user_subscription_trial2pay);
                PlayerPrefs.SetInt(Key_SubmitedTrial2Pay, 1);
            }
        }
    }

    public static void SetCancel() {

        if (!PlayerPrefs.HasKey(Key_Canceled)) {
            Debug.Log("SubscriptionController SetCancel!");
            AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Cancel,
                SubPackageManager.GetCurrentPackageId());
            PlayerPrefs.SetInt(Key_Canceled, 1);
        }

        OnChange?.Invoke(false);
    }

    public static void SetExpired() {
        TimeManager.timeEndSubscriptionVIP = TimeManager.TimeIngameIgnorePause;
        
        Debug.Log("SubscriptionController SetExpired!");
        SetUserType(SUBSCRIPTION_USERTYPE.CANCEL);
        PlayerPrefs.DeleteKey(Key_SubmitedInfo);
        AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Expire, SubPackageManager.GetCurrentPackageId());
        LocalDB.StoreIAPInformationToDB(TRACK_NAME.vip_status, VipStatus.none.ToString());
        OnChange?.Invoke(false);
        Configuration.instance.RestoreAds();
    }

    #region RevenueCat function

    public static void UpdateSubscription_RevenueCat(string productIdentifier, Purchases.CustomerInfo purchaserInfo) {
        if (string.IsNullOrEmpty(productIdentifier)) {
            Debug.Log("[IAP] UpdateSubscription_RevenueCat: Emptypackage! ");
            return;
        }

        Debug.Log("[IAP] UpdateSubscription_RevenueCat: " + productIdentifier);
        string oldExpired = PlayerPrefs.GetString(Key_ExpireDate);
        //purchase subscription
        string newExpired = string.Empty;
        if (purchaserInfo.LatestExpirationDate != null)
            newExpired = purchaserInfo.LatestExpirationDate.Value.Ticks.ToString();
        Purchases.EntitlementInfo purchasedProduct = purchaserInfo.Entitlements.All["VIP"];

        bool isTrial = !purchasedProduct.WillRenew;
        bool isNew = !newExpired.Equals(oldExpired);

        if (IsSubscription() && !purchasedProduct.IsActive) {
            SetExpired();
        }

        if (!IsSubscription() || isNew) {
            if (purchasedProduct.IsActive) {
                StartSubscription_RevenueCat(isTrial, productIdentifier);
                if (!isTrial) {
                    LocalDB.StoreIAPHistoryToDB(productIdentifier, 1, DateTime.Now); // buy subscription
                }
            }

            PlayerPrefs.SetString(Key_ExpireDate, newExpired);
        }
    }

    public static void StartSubscription_RevenueCat(bool isFreeTrial, string productIdentifier) {
        Debug.Log("[IAP] StartSubscription_RevenueCat: " + productIdentifier);
        //Fire Trial to Pay Event
        SubmitTrialToPay();

        if (RemoteConfigBase.instance.IaaSegmentation_IsEnablePopup) {
            IaaSegmentation.data.isHighValueFromTrial = true;
            IaaSegmentation.SaveSegmentationData();
        }

        if (!IsSubscription() || !IsSubscriptionVip() || SubsciptionOnboarding.instance != null) {
            SubmitTransactionInfo();
            SubPackageManager.SetCurrentPurchasedId(productIdentifier);

            if (Configuration.instance.isDebug) {
                long ticksExpired = DateTime.Now.ToUniversalTime().Ticks + 2000000000;
                DateTime exp = new DateTime(ticksExpired);
                Debug.Log("Time Expired:" + exp.ToShortTimeString());
                PlayerPrefs.SetString(Key_ExpireDate, ticksExpired.ToString());
            }

            TimeManager.timeStartSubscriptionVIP = TimeManager.TimeIngameIgnorePause;

            PlayerPrefs.DeleteKey(Key_Canceled);
            SetUserType(SUBSCRIPTION_USERTYPE.ACTIVE);
            LocalDB.StoreIAPInformationToDB(TRACK_NAME.vip_status,
                isFreeTrial ? VipStatus.trial.ToString() : VipStatus.purchase.ToString());

            IAPDefinitionId iapDefinitionId = IapBase.GetDefinitionId(productIdentifier);
            if (iapDefinitionId == IAPDefinitionId.none) {
                //maybe this is old!!!!
                var oldDefinition = SubPackageManager.GetCachedDefinitionId(productIdentifier);
                if (oldDefinition != IAPDefinitionId.none) {
                    iapDefinitionId = oldDefinition;
                }
            } else {
                SubPackageManager.SetCachedDefinitionId(productIdentifier,  iapDefinitionId);
            }

            SUBSTYPE subsType = iapDefinitionId == IAPDefinitionId.subs_no_ads ? SUBSTYPE.NoAds : SUBSTYPE.VIP;
            SetSubsType(subsType);

            if (Shop.instance != null) {
                Shop.instance.Close();
                if (remoteConfig.SubscriptionOnboarding_Enable) {
                    ShowVIPNotice();
                } else {
                    CheckAndShowBonus();
                }
            }

            if (SubscriptionUI.instance != null) {
                SubscriptionUI.instance.Close();
                if (remoteConfig.SubscriptionOnboarding_Enable)
                    ShowVIPNotice();
                else
                    CheckAndShowBonus();
            }

            if (SeeAll.s != null) {
                SeeAll.s.Close();
                if (remoteConfig.SubscriptionOnboarding_Enable)
                    ShowVIPNotice();
                else
                    CheckAndShowBonus();
            }

            if (SubsciptionOnboarding.instance != null) {
                SubsciptionOnboarding.instance.Close();
            }

            if (UIController.ui != null && UIController.ui.gameover.vipButton) {
                UIController.ui.gameover.vipButton.SetActive(false);
            }

            if (isFreeTrial) {
                AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Trial, productIdentifier);
                PlayerPrefs.SetString(Key_TrialDate, (DateTime.Now.ToUniversalTime().Ticks).ToString());
            } else {
                AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Charge, productIdentifier);
            }

            Configuration.instance.AddNoAds(true, subsType);
            isJustBoughtSub = true;
            OnChange?.Invoke(true);
        } else {
            //Already Subscription
            AnalyticHelper.Subscription(SUBSCRIPTION_EVENT.Subscription_Charge, productIdentifier);
        }
    }

    #endregion

    public static bool IsUserSubsWeek() {
        if (IsSubscription()) {
            IAPDefinitionId idSubsPurchased = RevenueCatPurchases.instance.GetIdSubsPurchased();
            if (idSubsPurchased == IAPDefinitionId.subscription_week) {
                return true;
            }
        }

        return false;
    }

    private static void FireEventSubscriptionUser(SUBSCRIPTION_EVENT eventName) {
        Dictionary<string, object> param = AnalyticHelper.GetDefaultParam(string.Empty,false);

        string screen = GetScreen();
        if (screen != null) {
            param[TRACK_NAME.screen] = screen;
            ShopBallTracking.VipPurchase(screen);
        }

        AnalyticHelper.LogEvent(eventName.ToString(), param);
    }
}