using System;
using UnityEngine;
using UnityEngine.UI;

public class PackageItem_v4 : PackageItem {
    [SerializeField] private Text oldPriceText;

    protected override void Awake() {
        base.Awake();
        if (saveText != null)
            saveText.text =
                $"{SubscriptionUIv4.PercentOff}% {LocalizationManager.instance.GetLocalizedValue("PRICE_OFF")}";
        float currentPrice = SubPackageManager.GetPackagePriceValue(id);
        //priceText.text = Util.PriceToString(currentPrice, SubPackageManager.GetPackagePriceCode(id));
        if (oldPriceText != null) {
            float oldPrice = SubscriptionUIv4.PercentOff >= 100
                ? currentPrice
                : Mathf.RoundToInt(currentPrice / (1 - SubscriptionUIv4.PercentOff / 100f));

            if (oldPrice > 10000) {
                oldPrice = Mathf.RoundToInt(oldPrice / 1000) * 1000;
            } else if (oldPrice > 1000) {
                oldPrice = Mathf.RoundToInt(oldPrice / 100) * 100;
            } else if (oldPrice > 100) {
                oldPrice = Mathf.RoundToInt(oldPrice / 10) * 10;
            }

            oldPriceText.text = Util.PriceToString(oldPrice, SubPackageManager.GetPackagePriceCode(id));
        }
    }
}