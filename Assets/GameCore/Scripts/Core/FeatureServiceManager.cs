using GameCore.EndlessOffer;
using System.Collections;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Hybrid.TripleOffer;
using TilesHop.Cores.Pooling;
using TilesHop.EconomySystem;
using TilesHop.LiveEvent;
using TilesHop.MysteryBox;
using TilesHop.PlaytimeReward;
using GameCore.LiveEvent.MysteryDoor;
using UnityEngine;

public class FeatureServiceManager : FastSingleton<FeatureServiceManager> {
    private Queue _services = new Queue();

    protected override void Awake() {
        base.Awake();
        if (instance == this) {
            DontDestroyOnLoad(this.gameObject);
        }
    }

    private void EnqueueService(FeatureService service) {
        _services.Enqueue(service);
    }

    public void Init() {
        RegisterServices();
        StartCoroutine(IEStartServices());
    }

    private void RegisterServices() {
        if (RemoteConfigBase.instance.Booster_IsEnable) {
            EnqueueService(gameObject.AddComponent<BoosterManager>());
        } else {
            FeatureUnlockManager.instanceSafe.TurnOffFeature(FeatureKey.BOOSTER_LIFE_SAVER);
            FeatureUnlockManager.instanceSafe.TurnOffFeature(FeatureKey.BOOSTER_HYPER_STAR);
            FeatureUnlockManager.instanceSafe.TurnOffFeature(FeatureKey.BOOSTER_TILE_TIDY);
        }

        if (RemoteConfig.instance.MidiMultipleVersion_IsEnable) {
            EnqueueService(gameObject.AddComponent<MidiMultipleVersion>());
        }

        if (RemoteConfigBase.instance.mysterybox_enable) {
            EnqueueService(gameObject.AddComponent<MysteryBox>());
        }

        if (!string.IsNullOrEmpty(RemoteConfigBase.instance.knob_enable_condition) &&
            !string.IsNullOrEmpty(RemoteConfigBase.instance.knob_data)) {
            EnqueueService(gameObject.AddComponent<PlaytimeReward>());
        }

        if (RemoteConfigBase.instance.PowerCube_IsEnable) {
            EnqueueService(gameObject.AddComponent<PowerCubeManager>());
        } else {
            FeatureUnlockManager.instanceSafe.TurnOffFeature(FeatureKey.POWER_CUBE);
        }
    }

    private IEnumerator IEStartServices() {
        if (RemoteConfigBase.instance.MysteryDoor_IsEnable) {
            EnqueueService(gameObject.AddComponent<MysteryDoorManager>());
            yield return null;
        }

        while (_services.Count != 0) {
            FeatureService service = (FeatureService) _services.Dequeue();
            Logger.EditorLog("Feature Services", $"Start service: {service.GetType()}");
            service.Init();
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        yield return null;
    }
}