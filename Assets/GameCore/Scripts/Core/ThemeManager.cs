using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Facebook.MiniJSON;
using UnityEngine.SceneManagement;
using CielaSpike;
using Music.ACM;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using TilesHop.Cores.UserProgression;
using TilesHop.GameCore.StarsJourney;
using TilesHop.LiveEvent.MilestoneEvent;

public class ThemeManager : Singleton<ThemeManager> {
    #region Fields

    public const int ThemeIdEmpty       = 0;
    public const int ThemeIdTutorial    = 1;
    public const int ThemeIdNoel        = 15;
    public const int ThemeIdHalloween   = 16;
    public const int ThemeIdGLU2        = 23;
    public const int ThemeIdMonstercat  = 27;
    public const int ThemeXmas2021      = 28;
    public const int ThemeWorldCup      = 30;
    public const int ThemePrideMonth    = 33;
    public const int ThemeEDM           = 34;
    public const int ThemeHipHopRetro   = 35;
    public const int ThemeHipHopModern  = 36;
    public const int ThemeEDM2          = 37;
    public const int ThemeHLW2023       = 38;
    public const int ThemeXmas2023      = 39;
    public const int ThemeLowDevice     = 99;
    public const int ThemePoolParty     = 40;
    public const int ThemeRadioStroke   = 41;
    public const int ThemeIdSpectrum    = 42;
    public const int ThemeCat           = 43;
    public const int ThemeBeliever      = 44;
    public const int ThemeBabyShark     = 45;
    public const int ThemeHalloween2024 = 47;
    public const int ThemePassoBemSolto = 49;
    public const int ThemeEiffel        = 50;

    public static readonly Dictionary<int, string> supportThemeList = new() { //in apk
        { -1, "--- Auto --" },
        { 0, "Theme0 - Empty" },
        { 1, "Theme1 - Mountain" },
        { 2, "Theme2 - Forest" }, //A_B => BG
        { 3, "Theme3 - Sea" }, //A_B => BG
        //{ 4, "Theme4 - Xmas 2018 - Removed" },
        { 5, "Theme5 - Easter" }, //A_B => BG
        { 6, "Theme6 - Neon 1" }, //A_B => BG
        { 7, "Theme7 - Neon 2" }, //A_B => BG
        //{ 8, "Theme8 - Xmas 2019" },
        //{ 9, "Theme9 - Son Tung - Removed" },
        { 10, "Theme10 - Carnival" }, //A_B => BG
        { 11, "Theme11 - OldTownRoad" }, //A_B => BG
        { 12, "Theme12 - Valley" }, //A_B => BG
        { 13, "Theme13 - Alan" }, //A_B => BG
        { 14, "Theme14 - Lake" }, //A_B => BG
        //{ 15, "Theme15 - Xmas 2020" },
        { 16, "Theme16 - Halloween 2019" }, //A_B => BG
        // { 17, "Theme17 - OldTownRoad New - Removed" },
        { 18, "Theme18 - Japan1" },
        { 19, "Theme19 - Japan2" },
        //{ 20, "Theme20 - China1" },
        { 21, "Theme21 - China2" }, //A_B => BG
        { 22, "Theme22 - GLU1" }, //A_B => BG
        { 23, "Theme23 - GLU2" }, //A_B => BG
        { 24, "Theme24 - DragonBoat" }, //A_B => BG
        //{ 25, "Theme25 - DragonRed" },
        { 26, "Theme26 - Believer" }, //A_B => BG
        { 27, "Theme27 - MonsterCat" }, //A_B => BG
        //{ 28, "Theme28 - Xmas 2021" },
        { 29, "Theme29 - After Midnight" }, //A_B => BG
        //{ 30, "Theme30 - WorldCup2022" },
        { 31, "Theme31 - Xmas2022" }, //A_B => BG
        { 32, "Theme32 - Wednesday" }, //A_B => BG
        //{ 33, "Theme33 - PrideMonth" },
        { 34, "Theme34 - EDM" }, //A_B => BG
        { 35, "Theme35 - HipHopClassic" }, //A_B => BG
        { 36, "Theme36 - HipHopModern" }, //A_B => BG
        { 37, "Theme37 - EDM 2" },
        { 38, "Theme38 - HLW 2023" }, //A_B => BG
        //{ 39, "Theme39 - Xmas 2023" },
        //{ 40, "Theme40 - Pool Party" },
        { 41, "Theme41 - Radio Stroke" }, //A_B => BG
        { 42, "Theme42 - Spectrum" }, //A_B => BG
        { 43, "Theme43 - CAT" },
        { ThemeBeliever, "Theme 44 - Theme Believer" },
        { ThemeBabyShark, "Theme 45 - Theme BabyShark" },
        { 46, "Theme 46 - Theme Crazy Fog" },
        { ThemeHalloween2024, "Theme 47 - Halloween 2024" },
        { ThemeLowDevice, "Theme99 - Low Device" },
        { 48, "Theme 48 - Xmas 2024" },
        { ThemePassoBemSolto, "Theme 49 - Passo Bem Solto" },
        { ThemeEiffel, "Theme 50 - Eiffel" },
        { 51, "Theme 51 - Tungtung sahu" },
    };

    public static readonly Dictionary<string, Color> labelThemeByColor = new() {
        { "Hot", new Color(1f, 0.09f, 0.22f) },
        { "New", new Color(0f, 0.61f, 1f) },
    };

    public const string THEME_PREFIX = "Theme";

    private static readonly Dictionary<string, List<int>> _themeTags     = new();
    private static readonly Dictionary<string, List<int>> _characterTags = new();

    [SerializeField] private TextAsset  defaultThemeConfig;
    [SerializeField] private ThemesData themesData;

    [ShowInInspector] private Dictionary<int, ThemeConfig> themeConfigs = new Dictionary<int, ThemeConfig>();

    private static List<int> _themeIDs; //list theme id get from _themeConfigs, maybe random or default
    private static List<int> _themeAutoIDs;
    private RemoteConfig remoteConfig => RemoteConfigBase.instance;

    //private
    private bool isInited;

    private string _cachedSceneName;
    private int    _themeId;

    private int _defaultThemeId;

    public int CurrentThemeId => _themeId;
    public int DefaultThemeId => _defaultThemeId;

    public static bool Inited = false;
    public static event Action OnInitedDone;
    public static event Action OnUnlockNewTheme;

    #endregion

    public IEnumerator Init() {
        if (isInited) {
            yield break;
        }

        LoadThemeConfig();

        while (!isInited) {
            yield return null;
        }

        //Assign default theme array
        List<int> defaultThemeArray = GetThemeIDDefault();
        _themeIDs = remoteConfig.Theme_Random ? GetThemeIDRandom(defaultThemeArray) : defaultThemeArray;
        _themeAutoIDs = GetAutoThemes();
        UpdateThemeTags();
        UpdateCharacterTags();

        Inited = true;
        OnInitedDone?.Invoke();
    }

    private List<int> GetAutoThemes() {
        if (string.IsNullOrEmpty(remoteConfig.Theme_IDs)) {
            return _themeIDs;
        }

        List<int> stringToListInt = remoteConfig.Theme_IDs.StringToListInt();
        return stringToListInt;
    }

    private async void LoadThemeConfig() {
        try {
            string dataCsv = null;
            string urlThemeConfig = remoteConfig.Theme_ConfigUrl;
            try {
                if (!string.IsNullOrEmpty(urlThemeConfig)) {
                    dataCsv = await DownloadManager.DownloadCsv(urlThemeConfig);
                }

            } catch (Exception e) {
                CustomException.Fire("[LoadThemeConfig]", " ERR: " + e.ToString());
            }

            if (string.IsNullOrEmpty(dataCsv)) {
                dataCsv = defaultThemeConfig.text;
            }

            if (!string.IsNullOrEmpty(dataCsv)) {
                this.StartCoroutineAsync(IEProcessDataThemeConfig(dataCsv, urlThemeConfig));
            }
        } catch (Exception e) {
            CustomException.Fire("[LoadThemeConfig]", " ERR: " + e.ToString());
        }
    }

    private IEnumerator IEProcessDataThemeConfig(string dataCsv, string urlThemeConfig) {
        yield return Ninja.JumpBack;

        List<ThemeConfig> configs = CSVReader.ProcessDataCSV<ThemeConfig>(dataCsv, urlThemeConfig);

        yield return Ninja.JumpToUnity;

        if (configs is { Count: > 0 }) {
            UpdateUnlockedThemes();
            configs.Sort((a, b) => a.order.CompareTo(b.order));

            foreach (ThemeConfig tmp in configs) { //Add default open theme
                themeConfigs.TryAdd(tmp.id, tmp);
                if (tmp.unlockType == UnlockType.Open) {
                    _unlockedTheme.Add(tmp.id);
                }
            }

            if (StarsJourneyManager.isEnable) {
                yield return IEProcessStarJourneyThemes();
            }

            if (RemoteConfigBase.instance.MileStoneEvent_IsEnable) {
                yield return IEProcessMilestoneEventRewardThemes();
            }
        }

        isInited = true;
    }

    private IEnumerator IEProcessStarJourneyThemes() {
        while (!StarsJourneyManager.instanceSafe.isReady) {
            yield return null;
        }

        var starJourneyThemes = StarsJourneyManager.instanceSafe.rewardThemes;
        foreach (int themeId in starJourneyThemes.Keys) {
            if (themeConfigs.TryGetValue(themeId, out var themeConfig)) {
                if (themeConfig.unlockType != UnlockType.Open) {
                    themeConfig.unlockType = UnlockType.StarJourney;
                }
            }
        }
    }
    
    private IEnumerator IEProcessMilestoneEventRewardThemes() {
        while (!MilestoneEvent.instanceSafe.isValidConfig) {
            yield return null;
        }

        string[] themeRewardIdStringPool = MilestoneEvent.instanceSafe.config.rewardPoolThemes.Split(';');
        foreach (string idString in themeRewardIdStringPool) {
            if (int.TryParse(idString, out int themeId) && themeConfigs.TryGetValue(themeId, out var themeConfig)) {
                if (themeConfig.unlockType != UnlockType.Open) {
                    themeConfig.unlockType = UnlockType.MilestoneEvent;
                }
            }
        }
    }

    private void UpdateThemeTags() {
        //Assign theme tags
        if (_themeTags.Count != 0) {
            return;
        }

        var rootData = (Dictionary<string, System.Object>) Json.Deserialize(remoteConfig.Theme_Tags);
        foreach (var item in rootData) {
            List<int> validThemes = new List<int>();
            if (item.Value != null) {
                var rawThemes = RemoteConfig.GetIntArray(item.Value.ToString());
                foreach (var themeId in rawThemes) {
                    if (supportThemeList.ContainsKey(themeId)) {
                        validThemes.Add(themeId);
                    }
                }
            }

            _themeTags.Add(item.Key, validThemes);
        }
    }

    private void UpdateCharacterTags() {
        //Assign character tags
        if (_characterTags.Count == 0) {
            var rootData = (Dictionary<string, System.Object>) Json.Deserialize(remoteConfig.Character_Tags);
            foreach (var item in rootData) {
                _characterTags.Add(item.Key, RemoteConfig.GetIntArray(item.Value.ToString())?.ToList());
            }
        }
    }

    public static int GetPlayingThemeId() {
        if (instance == null) {
            return -1;
        }

        string sceneName = SceneManager.GetActiveScene().name;
        if (!sceneName.Equals(instance._cachedSceneName)) {
            if (!int.TryParse(sceneName.Replace(ThemeManager.THEME_PREFIX, string.Empty), out instance._themeId)) {
                instance._themeId = -1;
            }

            instance._cachedSceneName = sceneName;
        }

        return instance._themeId;
    }

    public static string GetThemeSceneName(Song song) {
        bool canGetNew = !RemoteConfigBase.instance.Theme_IsEnableSelectTheme;
        string sceneName = THEME_PREFIX + GetSelectedTheme(song, false, canGetNew);

        // if scene is not exits, force random again
        if (!Application.CanStreamedLevelBeLoaded(sceneName)) {
            Logger.LogError("[GetThemeSceneName] Theme scene is not exits: " + sceneName);
            sceneName = THEME_PREFIX + GetSelectedTheme(song, true, true);

            // if scene is not exits, return theme default 
            if (!Application.CanStreamedLevelBeLoaded(sceneName)) {
                Logger.LogError("[GetThemeSceneName] Theme scene is not exits: " + sceneName);
                sceneName = THEME_PREFIX + ThemeIdTutorial;

                if (!Application.CanStreamedLevelBeLoaded(sceneName)) {
                    Logger.LogError("[GetThemeSceneName] Theme scene is not exits: " + sceneName);
                    sceneName = THEME_PREFIX + 99;
                }
            }
        }

        return sceneName;
    }

    /// <summary>
    /// Return musicalization themeId
    /// if isSceneChange is true, return saved themeId before
    /// if isSceneChange is false, return new random themeId
    /// </summary>
    /// <param name="song"></param>
    /// <param name="canGetNew"></param>
    /// <returns></returns>
    private static int GetMusicalizationTheme(Song song, bool canGetNew) {
        if (_themeIDs == null || _themeIDs.Count == 0) {
            instance.StartCoroutine(instance.Init());
            return ThemeIdTutorial;
        }

        int themeId = song.GetSelectedTheme();
        if (themeId < 0 || !_themeIDs.Contains(themeId) || canGetNew) {
            //Get new index
            int index = PlayerPrefs.GetInt(THEME_PREFIX, -1) + 1;
            while (!IsValidIndexTheme(index)) {
                index++;
            }

            index %= _themeAutoIDs.Count;
            themeId = _themeAutoIDs[index];

            //Save data
            PlayerPrefs.SetInt(THEME_PREFIX, index);
            song.SetSelectedTheme(themeId);
        }

        return themeId;
    }

    private static bool IsValidIndexTheme(int index) {
        int themeID = _themeAutoIDs[index % _themeAutoIDs.Count];
        return _themeIDs.Contains(themeID);
    }

    private List<int> GetThemeIDDefault() {
        List<int> result = new List<int>();

        foreach (var key in themeConfigs.Keys.ToList()) {
            if (IsSupportTheme(key)) {
                result.Add(key);
            } else {
                themeConfigs.Remove(key);
            }
        }

        if (result.Count > 0) {
            return result;
        }

        List<int> defaultList = supportThemeList.Keys.ToList();
        defaultList.Remove(-1);
        return defaultList;
    }

    /// <summary>
    /// Get Theme By ThemeIds song.themeIds = "16;17;18"
    /// </summary>
    /// <param name="song"></param>
    /// <returns></returns>
    static int GetThemeBySongThemeIds(Song song) {
        try {
            List<int> songThemeIds = song.themeIds;
            songThemeIds ??= new List<int>();
            if (songThemeIds.Count == 0 && !string.IsNullOrEmpty(song.themes) && _themeIDs != null) {
                int[] rawThemeIds = RemoteConfigBase.GetIntArray(song.themes, ';');
                for (int i = 0; i < rawThemeIds.Length; i++) {
                    if (_themeIDs.Contains(rawThemeIds[i]))
                        songThemeIds.Add(rawThemeIds[i]);
                }
            }

            if (songThemeIds.Count > 0) {
                return songThemeIds[UnityEngine.Random.Range(0, songThemeIds.Count)];
            }

            return -1;
        } catch (Exception ex) {
            CustomException.Fire("[ThemeBySong]", $"song {song.name} => {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// Get Theme By Tags Remote Config Example: song.tags = "Xmas" Theme_Tags{"Xmas":"16,17,18"}
    /// </summary>
    /// <param name="song"></param>
    /// <returns></returns>
    static int GetThemeByTags(Song song) {

        List<int> result = new List<int>();
        foreach (var item in _themeTags) {
            if (item.Value != null &&
                ((song.tags != null && song.tags.IndexOf(item.Key, StringComparison.OrdinalIgnoreCase) >= 0) ||
                 song.path == item.Key)) {
                result.AddRange(item.Value);
            }
        }

        if (result.Count > 0) {
            return result[UnityEngine.Random.Range(0, result.Count)];
        }

        return -1;
    }

    /// <summary>
    /// Get Character By theme Remote Config Example: song.tags = "Xmas" Character_Tags{"Xmas":"4"}
    /// </summary>
    /// <param name="song"></param>
    /// <returns></returns>
    public static int GetCharacterByCurrentTheme() {
        List<int> result = new List<int>();
        int theme = GetPlayingThemeId();
        foreach (var item in _characterTags) {
            if (item.Value != null && theme.ToString() == item.Key) {
                result.AddRange(item.Value);
            }
        }

        if (result.Count > 0) {
            return result[UnityEngine.Random.Range(0, result.Count)];
        }

        return -1;
    }

    /// <summary>
    /// Return current theme array setting
    /// </summary>
    /// <returns></returns>
    static List<int> GetThemeIDRandom(List<int> supportThemes) {
        List<int> themeArray;
        string cachedThemeIDs = PlayerPrefs.GetString(CONFIG_STRING.ThemeArray);
        if (string.IsNullOrEmpty(cachedThemeIDs)) {
            // Init array
            themeArray = Util.ShuffleArray(supportThemes);

            // Save to Prefs for later
            SaveToPrefs(themeArray);
            return themeArray;
        } else {
            string[] stringArray = cachedThemeIDs.Split(',');
            themeArray = new List<int>();
            foreach (string t1 in stringArray) {
                if (int.TryParse(t1, out var themeId)) {
                    themeArray.Add(themeId);
                }
            }

            bool isChanged = false;
            //Add new themes
            foreach (int t in supportThemes) {
                if (!themeArray.Contains(t)) {
                    themeArray.Add(t);
                    isChanged = true;
                }
            }

            //Remove invalid
            for (int i = themeArray.Count - 1; i > 0; i--) {
                if (!supportThemes.Contains(themeArray[i])) {
                    themeArray.Remove(themeArray[i]);
                    isChanged = true;
                }
            }

            if (isChanged) {
                SaveToPrefs(themeArray);
            }

            return themeArray;
        }
    }

    static void SaveToPrefs(List<int> arr) {
        string strSave = "";
        for (int t = 0; t < arr.Count; t++) {
            strSave += arr[t].ToString();
            if (t != arr.Count - 1) {
                strSave += ",";
            }
        }

        PlayerPrefs.SetString(CONFIG_STRING.ThemeArray, strSave);
    }

    /// <summary>
    /// Get selected theme by: Random, Force, Remote Config
    /// </summary>
    /// <param name="song">Song</param>
    /// <param name="forceRandom">is force random</param>
    /// <param name="forceLocal"></param>
    /// <param name="canGetNew"></param>
    /// <returns></returns>
    public static int GetSelectedTheme(Song song, bool forceRandom = false, bool canGetNew = false) {
        //Check case song is null
        if (song == null) {
            CustomException.Fire("GetSelectedTheme", "Song is null");
            return ThemeIdTutorial;
        }

        if (UserProgressionController.EnableFeature) {
            if (DeviceHelper.IsLowEnd()) {
                return ThemeLowDevice;
            } else {
                int idTheme = UserProgressionController.instanceSafe.GetIdTheme(song);
                if (idTheme >= 0) {
                    return idTheme;
                }
            }
        }

        //Return force theme configuration
        if (!forceRandom) {
            int forceID = RemoteConfigBase.instance.Theme_ForceID;
            if (forceID >= 0) {
                ThemeConfig themeConfig = instance.GetThemeConfig(forceID);
                if (themeConfig != null && instance.IsSupportTheme(forceID)) {
                    return forceID;
                } else if (themeConfig == null && forceID == ThemeLowDevice) {
                    return forceID;
                }
            } else if (DeviceHelper.IsLowEnd()) {
                return ThemeLowDevice;
            }
        }

        //Get random theme by themeIds
        int themeId = GetThemeBySongThemeIds(song);

        //Get random theme by theme_tags
        if (themeId < 0) {
            themeId = GetThemeByTags(song);
        }

        //Return tutorial theme default
        if (themeId < 0 && RemoteConfigBase.instance.Theme_TutorialID >= 0 && Configuration.instance.isTutorial) {
            return RemoteConfigBase.instance.Theme_TutorialID;
        }

        //Get theme musicalization
        if (themeId < 0) {
            themeId = GetMusicalizationTheme(song, canGetNew);
        }

        if (instance != null) {
            instance._defaultThemeId = themeId;
        }

        return themeId;
    }

    /// <summary>
    /// Clear when press ResetButton
    /// </summary>
    public static void ClearThemeArray() {
        _themeIDs = null;
    }

    public static bool IsEnableShopInGame() {
        if (!RemoteConfigBase.instance.ShopBallInGame_IsEnable) {
            return false;
        }

        if (Ball.b == null) {
            return true;
        }

        return !Ball.b.UsingForceBallTheme();
    }

    public static bool IsEnableSelectThemes() {
        if (DeviceHelper.IsLowEnd())
            return false;

        if (!RemoteConfigBase.instance.Theme_IsEnableSelectTheme) {
            return false;
        }

        if (RemoteConfigBase.instance.Theme_DisableSelectionFixTheme) {
            Song song = NotesManager.instance.song;
            List<int> songThemeIds = song.themeIds;
            if (songThemeIds != null && songThemeIds.Count > 0 && songThemeIds.Contains(GetPlayingThemeId())) {
                return false;
            }
        }

        if (RemoteConfigBase.instance.Theme_IsEnableSelectThemeAfterReward && !HaveReceivedThemeAsReward()) {
            return false;
        }

        return true;
    }

    public static bool IsThemePrideMonth() {
        return GetPlayingThemeId() == ThemePrideMonth;
    }

    public static bool IsThemeEDM() {
        return GetPlayingThemeId() == ThemeEDM || GetPlayingThemeId() == ThemeEDM2;
    }

    public static bool IsThemeHipHop() {
        return GetPlayingThemeId() == ThemeHipHopRetro || GetPlayingThemeId() == ThemeHipHopModern;
    }

    public static bool IsXmas2021() {
        return GetPlayingThemeId() == ThemeXmas2021;
    }

    public Sprite GetIcon(int id) {
        foreach (ThemeData themeData in themesData.listCharacter) {
            if (themeData.id == id) {
                return themeData.sprIcon;
            }
        }

        Debug.LogError("[GetIconTheme] cannot find icon of: " + id);
        return null;
    }

    
    /// <summary>
    /// Instead of using the old method that could return null, where comparing to 'null' is expensive
    /// </summary>
    /// <param name="id"></param>
    /// <param name="icon">output</param>
    /// <returns>Return true if the theme icon is successfully retrieved; otherwise, return false</returns>
    public bool TryGetIcon(int id, out Sprite icon) {
        icon = null;
        foreach (var themeData in themesData.listCharacter) {
            if (themeData.id != id) continue;
            icon = themeData.sprIcon;
            return true;
        }

        return false;
    }

    public CodeColorItem GetCodeColor(int id) {
        foreach (ThemeData themeData in themesData.listCharacter) {
            if (themeData.id == id) {
                return themeData.colorCode;
            }
        }

        Debug.LogError("[GetIconTheme] cannot find code of: " + id);
        return CodeColorItem.Blue;
    }

    public List<ThemeData> GetAllThemesData() {
        List<ThemeData> a = themesData.Clone();
        return a;
    }

    private bool IsSupportTheme(int id) {
        bool containsKey = supportThemeList.ContainsKey(id);
        if (!containsKey) {
            Logger.LogWarning("[IsSupportTheme] not support theme: " + id + " in apk!");
            return false;
        }

        foreach (ThemeData themeData in themesData.listCharacter) {
            if (themeData.id == id) {
                if (themeData.sprIcon == null) {
                    Logger.LogError("[IsSupportTheme] miss icon for theme: " + id);
                }

                return themeData.sprIcon != null;
            }
        }

        Logger.LogError("[IsSupportTheme] miss themeData for theme: " + id);
        return false;
    }

    public ThemeData GetThemeData(int id) {
        foreach (ThemeData data in themesData.listCharacter) {
            if (data.id == id) {
                return data;
            }
        }

        return null;
    }

    public List<int> GetThemeIDs() {
        return _themeIDs;
    }

    public List<int> GetThemeIds() {
        return themeConfigs.Keys.ToList();
    }

    public ThemeConfig GetThemeConfig(int id, bool fireException = false) {
        if (themeConfigs == null || themeConfigs.Count == 0) {
            Logger.LogError($"[GetThemeConfig] {(themeConfigs == null ? "null" : "themeConfigs.Count == 0")}");
            return null;
        }

        if (themeConfigs.ContainsKey(id)) {
            return themeConfigs[id];
        }

        if (fireException) {
            CustomException.Fire("[GetThemeConfig]", $"Cannot get themeConfigs of {id}");
        }

        return null;
    }

    public bool IsExistTheme(int id) {
        if (themeConfigs == null || themeConfigs.Count == 0) {
            return false;
        }

        if (themeConfigs.ContainsKey(id)) {
            return true;
        }

        return false;
    }

    public static int GetShowAdOfTheme(int id) {
        int i = PlayerPrefs.GetInt("GetShowAdOfTheme_" + id, 0);
        return i;
    }

    public static void SetShowAdOfTheme(int id, int count) {
        PlayerPrefs.SetInt("GetShowAdOfTheme_" + id, count);
    }

    public UnlockType GetUnlockType(int idTheme) {
        ThemeConfig themeConfig = GetThemeConfig(idTheme);
        if (themeConfig == null) {
            return UnlockType.Open;
        } else {
            return themeConfig.unlockType;
        }
    }

    #region Unlock Themes

    private readonly List<int> _unlockedTheme = new();

    private void UpdateUnlockedThemes() {
        try {
            string s = PlayerPrefs.GetString(CONFIG_STRING.ThemesUnlocked, string.Empty);
            if (!string.IsNullOrEmpty(s)) {
                JsonConvert.PopulateObject(s, _unlockedTheme);
            }
        } catch (Exception e) {
            CustomException.Fire("[UpdateUnlockedThemes]", $"error on parse ThemeUnlocked {e.Message}");
        }
    }

    public bool IsOpenTheme(int id) {
        if (id < 0) {
            return false;
        }

        if (id == 0) {
            return true;
        }

        if (_unlockedTheme.Count == 0) {
            UpdateUnlockedThemes();
        }

        if (_unlockedTheme.Contains(id)) {
            return true;
        }

        var themeConfig = GetThemeConfig(id);
        if (Configuration.IsNoAllAds()) { // VIP user
            if (themeConfig.unlockType is UnlockType.Video or UnlockType.Vip or UnlockType.VipMission) {
                return true;
            }

            if (themeConfig.unlockType == UnlockType.Lock) {
                if (themeConfig.ads != 0) { //unlock by ads
                    return true;
                } else { //unlock by diamond
                    if (!Configuration.CanEarnDiamond()) {
                        return true;
                    }
                }
            }

            if (themeConfig.unlockType == UnlockType.Diamond) {
                if (!Configuration.CanEarnDiamond()) {
                    return true;
                }
            }
        }

        return false;
    }

    public void SetOpenTheme(int id, int price = 0) {
        if (IsOpenTheme(id)) {
            return;
        }

        if (price > 0) {
            Configuration.UpdateDiamond(-price, CurrencySpendSource.unlock_theme.ToString());
            AirfluxTracker.TrackSpendCredits(price);
        }

        _unlockedTheme.Add(id);
        PlayerPrefs.SetString(CONFIG_STRING.ThemesUnlocked, JsonConvert.SerializeObject(_unlockedTheme));
        Configuration.instance.userDataCached.UnlockTheme(id);
        OnUnlockNewTheme?.Invoke();
    }

    #endregion

    public static string GetScoreIngameType() {
        switch (GetPlayingThemeId()) {
            case ThemeEDM:
            case ThemeEDM2:
                return "ScoreIngame-EDM";

            case ThemeIdSpectrum:
                return "ScoreIngame-Spectrum";

            case ThemeHipHopRetro:
            case ThemeHipHopModern:
                return "ScoreIngame-HipHop";

            case ThemeCat:

                return "ScoreIngame-Cat";

            case 46:
                return "ScoreIngame-CrazyFrog";

        }

        return "ScoreIngame-Standard";
    }

    public static bool IsUseNewTile() {
        // Newtile là tile kiểu trắng -> đổ màu lên tile
        int currentTheme = GetPlayingThemeId();
        switch (currentTheme) {
            case ThemeEDM:
            case ThemeEDM2:
            case ThemeHipHopRetro:
            case ThemeHipHopModern:
            case ThemePoolParty:
            case ThemeRadioStroke:
            case ThemeHLW2023:
                return true;
        }

        return false;
    }

    static readonly HashSet<int> _cachedRewardTheme = new();

    public int GetRewardTheme() {
        if (!isInited) {
            return -1;
        }

        if (themeConfigs != null) {
            foreach (var valuePair in themeConfigs) {
                bool isIgnoreTheme = valuePair.Value.unlockType is UnlockType.Event or UnlockType.Vip
                    or UnlockType.VipMission or UnlockType.StarterPack;

                if (isIgnoreTheme || Configuration.IsEarnTheme(valuePair.Key)) {
                    continue;
                }

                if (_cachedRewardTheme.Contains(valuePair.Key)) {
                    continue;
                }

                return valuePair.Key;
            }
        }

        return -1;
    }

    public static bool HaveReceivedThemeAsReward() {
        return PlayerPrefs.HasKey(CONFIG_STRING.ThemesUnlocked);
    }

    public static bool IsIgnoreTheme(ThemeData themeData) {
        var unlockType = themeData.GetUnlockType();
        return !themeData.IsOpenTheme() && (unlockType == UnlockType.Event || unlockType == UnlockType.StarterPack);
    }

    public (int, int) CountCurrentOpenAndMaxNumberThemes() {
        int count = 0;
        int maxCount = 0;
        foreach (ThemeData themeData in themesData.listCharacter) {
            if (!IsExistTheme(themeData.id)) {
                continue;
            }

            if (IsIgnoreTheme(themeData)) {
                continue;
            }

            maxCount++;
            if (IsOpenTheme(themeData.id)) {
                count++;
            }
        }

        return (count, maxCount);
    }

    public int GetLockedTheme() {
        foreach (ThemeData themeData in themesData.listCharacter) {
            if (!IsExistTheme(themeData.id)) {
                continue;
            }

            if (IsIgnoreTheme(themeData)) {
                continue;
            }
            if (!IsOpenTheme(themeData.id)) {
                return themeData.id;
            }
        }

        return -1;
    }
}