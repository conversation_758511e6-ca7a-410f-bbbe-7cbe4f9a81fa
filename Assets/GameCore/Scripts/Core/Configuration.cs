using System.Collections.Generic;
using UnityEngine;
using System;
using System.Collections;
using System.Globalization;
using System.Linq;
using System.Text;
using Amanotes;
using CielaSpike;
using Inwave;
using Inwave.CommunicationSystem.LocalSegmentation;
using Music.ACM;
using UnityEngine.Serialization;
using Newtonsoft.Json;
using TilesHop.Cores;
using TilesHop.Cores.UserProgression;
using TilesHop.ChallengeOldUser;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Hybrid;
using TilesHop.LongNote;
using TilesHop.EconomySystem;
using TilesHop.GameCore.ShopBallRevamp;
using TilesHop.LiveEvent.GalaxyQuest;
using TilesHop.Cores.IAASegmentation;
using TilesHop.LiveEvent.MilestoneEvent;
using tule.SecuredTypes;
using UnityEngine.Networking;
using Random = UnityEngine.Random;
using Task = System.Threading.Tasks.Task;
using Utils = Inwave.Utils;

public partial class Configuration : MonoBehaviour {
    #region Fields

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ static ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public static Configuration instance => PreSDKs.isInstanced ? PreSDKs.instance.configuration : null;
    public static bool isInstanced => instance != null;
    public static event Action<int> OnChangeDiamond;
    public static event Action OnUnlockNewBall;

    public static  bool isFirstOpen = false;
    public static  bool isAdmin;
    private static bool _isNoAds = false;

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public AnimationCurve curve;
    public AnimationCurve curve_moving;
    public bool           enableContentTool = false;
    public bool           testNotchDevices  = false;
    public bool           forceChinaUser    = false;
    public bool           isPartnerBuild    = false;
    public AnimationCurve defaultCurve;
    public Color          likeActiveColor;
    public Color          likeInactiveColor;
    public Color          favoriteColor;
    public Color          favoriteInactiveColor;
    public bool           isEndlessModeByUser = true;
    public bool           isOpenSensitiveFromHome;
    public bool           isOnApplicationQuit;

    public event Action<bool> onApplicationPause;
    public event Action<bool> onApplicationFocus;

    public bool isEnableLogViewer = false;

    public TextAsset defaultChallengeSongPoolConfig;
    public TextAsset defaultHardcoreSongPoolConfig;
    public TextAsset defaultUserProgressSongPackData;
    public TextAsset defaultUserProgressAchievementData;

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ private ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    [SerializeField]  private bool enableAdminFromEditor = false;
    [HideInInspector] public  bool gameloaded            = false;
    [HideInInspector] public  bool isTutorial            = false;
    [HideInInspector] public  bool isApprovalProcess     = false;
    [HideInInspector] public  bool isSelectMIDI          = false;
    [HideInInspector] public  bool isGamePlayTutorial    = false;
    [HideInInspector] public  int  diamondsVideoBonus    = 0;
    [HideInInspector] public  bool isLongNotePrototype   = false;
    [HideInInspector] public  bool isSpecialTileControl  = false;
    [HideInInspector] public  bool isSpecialTileV2       = false;

    [HideInInspector] public int             androidSdk          = 0;
    [HideInInspector] public bool            isShowedForceUpdate = false;
    [HideInInspector] public bool            isDebug             = false;
    [HideInInspector] public bool            isAutoPlay          = false;
    [HideInInspector] public bool            isAllowFacebook     = false;
    [HideInInspector] public EndlessModeType EndlessMode_Type    = EndlessModeType.SpeedUpFromRound1;

    public string    CountryName;
    public TextAsset Economy_LocalEconomySettings;

    private bool  musicIsOn     = true;
    private bool  vibrationIsOn = false;
    private bool  soundIsOn     = true;
    private int[] themeArray;
    private bool  _isShownStarterPack = false;

    private List<Song> _extensionSongs = null;

    public static readonly TimeSpan OneSecond = TimeSpan.FromSeconds(1);

    [HideInInspector] public bool isHighlightCurrentSong;

    private RemoteSecureInt _diamond = new();

    private int Diamond {
        get => _diamond.GetValue();
        set => _diamond.SetValue(value);
    }

    //private ItemRevive _itemRevive;

    private static int _totalEarn;
    private static int _totalSpend;

    public  Material       grayMaterial;
    private UserDataCached _userDataCached;

    public UserDataCached userDataCached {
        get {
            if (_userDataCached == null) {
                _userDataCached = new UserDataCached();
                _userDataCached.Load();
            }

            return _userDataCached;
        }
    }

    public static int LastPlayedSongOldHighestStars;

    public static bool isUniqueStarsEachSongPlay =>
        UserProgressionController.EnableFeature || StarsJourneyManager.isEnable ||
        RemoteConfigBase.instance.Hybrid_UniqueStarIngame;

    #endregion

    #region Admin

    [HideInInspector] public bool Admin_IsShowedPause = false;
    [HideInInspector] public int  Admin_ForceSkinSet  = -1;

    #endregion

    //public string ACMKey = "HWimS3k78By5mSAZewvT";

    /// <summary>
    /// Load/Save counted multiplier ads at result screen 
    /// </summary>
    public static int CountedMultiplierAds {
        get {
            if (PlayerPrefs.HasKey(CONFIG_STRING.Key_MultiplierCapping_Date)) {
                DateTime lastSaveDate =
                    new DateTime(long.Parse(PlayerPrefs.GetString(CONFIG_STRING.Key_MultiplierCapping_Date)));
                DateTime now = DateTime.Today;
                if (now.DayOfYear == lastSaveDate.DayOfYear) {
                    // same day
                    return PlayerPrefs.GetInt(CONFIG_STRING.Key_MultiplierCapping_Value);
                } else {
                    //another day -> reset value
                    return 0;
                }
            } else {
                // chưa có lưu ngày nào ->> default
                return 0;
            }
        }
        set {
            PlayerPrefs.SetInt(CONFIG_STRING.Key_MultiplierCapping_Value, value);
            PlayerPrefs.SetString(CONFIG_STRING.Key_MultiplierCapping_Date, DateTime.Today.Ticks.ToString());
        }
    }

    #region Unity method

    private void Start() {
        Input.multiTouchEnabled = false;
        isFirstOpen = IntroIsOn();

        isAdmin = IsCheckAdmin();
        if (enableAdminFromEditor) {
            EnableAdmin(true);
        }

        //Variables need to set first
        UpdateIsTutorial();
        isDebug = PlayerPrefs.GetInt(CONFIG_STRING.isDebug, enableAdminFromEditor && !isPartnerBuild ? 1 : 0) != 0 ||
                  enableContentTool;
        isAutoPlay = isPartnerBuild || PlayerPrefs.GetInt(CONFIG_STRING.IsAutoPlay, 0) != 0 || enableContentTool;
        isApprovalProcess = PlayerPrefs.GetInt(CONFIG_STRING.IsApprovalProcess, 0) != 0;
        isSelectMIDI = PlayerPrefs.GetInt(CONFIG_STRING.IsSelectMIDI, 0) != 0;
        isLongNotePrototype = PlayerPrefs.GetInt(CONFIG_STRING.IsLongNotePrototype, 0) != 0;
        isSpecialTileControl = PlayerPrefs.GetInt(CONFIG_STRING.IsSpecialTileControl, 0) != 0;
        isSpecialTileV2 = PlayerPrefs.GetInt(CONFIG_STRING.IsSpecialTileV2, 0) != 0;
        _totalEarn = PlayerPrefs.GetInt(CONFIG_STRING.Currency_Total_Earn, 0);
        _totalSpend = PlayerPrefs.GetInt(CONFIG_STRING.Currency_Total_Spend, 0);

        UpdateACM_Info(); //Reload ACM info if enabling approval process

        musicIsOn = PlayerPrefs.GetInt(CONFIG_STRING.Music, 1) != 0;
        soundIsOn = PlayerPrefs.GetInt(CONFIG_STRING.Sound, 1) != 0;

        SetGamePlayTutorial();
        androidSdk = SuperpoweredSDK.GetAndroiSDKInt();
        isAllowFacebook = androidSdk > 23 || androidSdk == 0; // DISABLE FACEBOOK ON ANDROID <= 6

        isEnableLogViewer = PlayerPrefs.GetInt(CONFIG_STRING.IsEnableLogViewer, isEnableLogViewer ? 1 : 0) == 1;
        _isNoAds = PlayerPrefs.GetInt(CONFIG_STRING.NoAds, 0) == 1 ||
                   (SubscriptionController.IsSubscription() && SubscriptionController.GetSubsType() == SUBSTYPE.VIP) ||
                   isPartnerBuild;
#if ENABLE_LOGS
            ShowLogViewer(isEnableLogViewer);
#endif
        SetFirstOpenTime();

        CountryName = Application.systemLanguage.ToCountryCode();
        WaitRemoteConfigDone();
    }

    private void OnApplicationQuit() {
        isOnApplicationQuit = true;
    }

    private void OnApplicationPause(bool pauseStatus) {
        onApplicationPause?.Invoke(pauseStatus);
    }

    private void OnApplicationFocus(bool hasFocus) {
        onApplicationFocus?.Invoke(hasFocus);
    }

    #endregion

    private void WaitRemoteConfigDone() {
        Util.WaitRemoteConfigDone(() => {
            RemoteSecureInt.Encrypt = RemoteConfigBase.instance.Secure_Data_Enable;
            // nếu bật Secured config và user đã có data mã hóa
            if (RemoteSecureInt.Encrypt && SecuredPlayerPrefs.HasEncryptionKey(CONFIG_STRING.Diamonds)) {
                Diamond = SecuredPlayerPrefs.GetInt(CONFIG_STRING.Diamonds, 0);
            }
            // nếu không, sử dụng data bình thường và ghi data mã hóa
            else {
                Diamond = PlayerPrefs.GetInt(CONFIG_STRING.Diamonds, 0);
                SecuredPlayerPrefs.SetInt(CONFIG_STRING.Diamonds, Diamond);
            }

            //_itemRevive = new ItemRevive(isUsingSecuredPref, RemoteConfigBase.instance.ItemRevive_Start);
            StartCoroutine(IEUpdateQualitySettings());

            UpdateACMDomainFromFirebase(RemoteConfigBase.instance.bcsDomain);
        });
    }

    private void UpdateACM_Info() {
        ACMSDKv4.UpdateSlotID(isApprovalProcess);
        string accessKey = isApprovalProcess ? "qec67195hj5d" : "ublwkg7zyyib";
        StartCoroutine(UpdateAccessKey(accessKey));
    }

    private IEnumerator UpdateAccessKey(string accessKey) {
        yield return Inwave.Utils.WaitUntil(() => ACMSDK.IsInitialized);

        ACMSDK.Instance.UpdateAccessKey(accessKey);
    }

    private void UpdateACMDomainFromFirebase(string bcsDomain) {
        if (string.IsNullOrEmpty(bcsDomain)) {
            return;
        }

        ACMConstant.API_BASE_URL = bcsDomain;
    }

    public void UpdateIsTutorial() {
        enableContentTool = PlayerPrefsCache.GetInt(CONFIG_STRING.IsContentTool, enableContentTool ? 1 : 0) == 1;
        isTutorial = IntroIsOn();
    }

    /// <summary>
    /// Set first open time
    /// </summary>
    private static void SetFirstOpenTime() {
        string firstOpenTime = PlayerPrefs.GetString(CONFIG_STRING.first_open_time);
        if (string.IsNullOrEmpty(firstOpenTime)) {
            PlayerPrefs.SetString(CONFIG_STRING.first_open_time, DateTime.Now.ToString(CONFIG_STRING.DateTimeFormat));
        }
    }

    /// <summary>
    /// Return first open time
    /// </summary>
    public static DateTime GetFirstOpenTime() {
        string firstOpenTime = PlayerPrefs.GetString(CONFIG_STRING.first_open_time);
        return Util.GetDateTimeByString(firstOpenTime, DateTime.Now);
    }

    private void InitSettings() {
        try {
            diamondsVideoBonus = RemoteConfig.instance.Diamond_Video_1st;
            Application.targetFrameRate = 60;

            VerifyBuild();
            StartCoroutine(InitThemeManager());

            isEndlessModeByUser = PlayerPrefs.GetInt(CONFIG_STRING.EndlessModeByUser, 1) == 1;
            vibrationIsOn =
                PlayerPrefs.GetInt(CONFIG_STRING.PlayVibration, RemoteConfig.instance.Vibration_DefaultOn ? 1 : 0) == 1;
        } catch (Exception e) {
            Debug.LogError(e.Message + " => " + e.StackTrace);
        }
    }

    private IEnumerator InitThemeManager() {
        while (!ThemeManager.isInstanced) {
            yield return null;
        }

        StartCoroutine(ThemeManager.instance.Init());
    }

    void VerifyBuild() {
        if (!isPartnerBuild) { } else {
            if (!RemoteConfig.instance.AllowPartnerBuild) {
                Debug.Log("Quit!");
                Application.Quit();
            }
        }
    }

    public void SetGamePlayTutorial() {
        isGamePlayTutorial = isTutorial;
    }

    //Function for game loaded event
    public void GameLoaded() {
        gameloaded = true;
        LoadCurrency();
        InitSettings();

        if (RemoteConfigBase.instance != null && RemoteConfigBase.instance.IaaSegmentation_IsEnablePopup) {
            IaaSegmentation.TrySegmentation();
        }

        // init lần lượt các singleton của các feature
        // Cần lưu ý khi add product trong IapBase.InitializePurchasing() nếu phụ thuộc vào instance của feature
        StartCoroutine(IEInitFeatures());

        CheckInitAppOpenAds();

        Util.WaitRemoteConfigDone(InitAfterRemoteConfigDone);

        if (isTutorial) {
            if (RemoteConfig.instance != null) {
                SetDiamonds(DIAMOND_INIT);
            }

            DeviceHelper.FireDeviceDetails();
        }

        if (AnalyticHelper.instance != null) {
            AnalyticHelper.instance.GameLoaded(isTutorial);
        }

        SetEconomyEarning();
        if (RemoteConfigBase.instance != null && RemoteConfigBase.instance.UITransition_IsTransitionHomeAndGameplay) {
            TransitionInOut.Active();
        }

        // setup thời gian in-app không tính thời gian pause game
        if (TimeManager.Instance != null) {
            TimeManager.Instance.HandleOnGameLoaded();
            onApplicationPause += TimeManager.Instance.HandleOnApplicationPause;
            onApplicationFocus += TimeManager.Instance.HandleOnApplicationFocus;
        }
    }

    public int DIAMOND_INIT => RemoteConfig.instance.Diamond_Init + 20;

    private void InitAfterRemoteConfigDone() {
        // init IAP products - Delay để đảm bảo Activity ready
        StartCoroutine(DelayedInitializePurchasing());
    }

    private IEnumerator DelayedInitializePurchasing() {
        // Đợi ít nhất 1 frame để đảm bảo Activity ready
        yield return null;

        // Đợi thêm 0.5s để đảm bảo UI components ready
        //yield return new WaitForSeconds(0.5f);

        // Kiểm tra xem Activity có ready không
        //if (Application.isPlaying) {
        StartCoroutine(IapBase.InitializePurchasing());
        //}
    }

    private ABAppOpenAds _configOpenAds;

    private void CheckInitAppOpenAds() {
        if (RemoteConfigBase.instance == null) {
            return;
        }

        string config = RemoteConfigBase.instance.AdOpenApp_Config;
        AppOpenAds.UpdateConfig(config);
    }

    private bool IsCheckAdmin() {
        return PlayerPrefs.GetInt(CONFIG_STRING.IsAdmin, 0) == 1 || instance.enableContentTool;
    }

    public void EnableAdmin(bool isEnable) {
        PlayerPrefs.SetInt(CONFIG_STRING.IsAdmin, isEnable ? 1 : 0);
        isAdmin = isEnable;
    }

    public static bool IsNoAllAds() {
        return _isNoAds;
    }

    public static bool IsNoSomeAds() { //banner + inter + native TH-676
        return SubscriptionController.IsSubscription() && SubscriptionController.GetSubsType() == SUBSTYPE.NoAds;
    }

    public void AddNoAds(bool isSubscription = false, SUBSTYPE subsType = SUBSTYPE.VIP) {
        _isNoAds = true;
        if (!isSubscription || subsType == SUBSTYPE.VIP) {
            if (SongManager.instance != null) {
                List<Song> valueCollection = SongManager.instance.GetSongsAsList();
                if (valueCollection != null) {
                    for (int i = 0, n = valueCollection.Count; i < n; i++) {
                        Song song = valueCollection[i];
                        if (song != null) {
                            if (song.savedType == SONGTYPE.VIDEO || song.savedType == SONGTYPE.VIP) {
                                song.savedType = SONGTYPE.OPEN;
                            }

                            if (song.savedType == SONGTYPE.LOCK && UnlockAllSongByVIP()) {
                                song.savedType = SONGTYPE.OPEN;
                            }
                        }
                    }
                }
            }
        }

        if (!isSubscription) {
            PlayerPrefs.SetInt(CONFIG_STRING.NoAds, 1);
        }

        if (Configuration.instance != null && Configuration.instance.userDataCached != null) {
            Configuration.instance.userDataCached.ReCalculateSongs();
        }

        AchievementCenter.UpdateAchievement();

        if (Util.IsHomeScene()) {
            if (HomeManager.instance != null) {
                HomeManager.instance.UpdateVIPUI();

                if (SongList.instance != null) {
                    SongList.instance.BuildList();
                    HomeManager.instance.ForcedUpdateSongList();
                }
            }
        }

        RemoveBanner();
        RemoveIngameAds();
        SetEconomyEarning();
    }

    public static void AddNoFSAds() {
        PlayerPrefs.SetInt(CONFIG_STRING.NoFSAds, 1);
        if (Util.IsHomeScene()) {
            HomeManager.instance.UpdateVIPUI();
        }

        RemoveBanner();
        RemoveIngameAds();
    }

    public static bool IsNoFSAds() {
        return PlayerPrefs.GetInt(CONFIG_STRING.NoFSAds, 0) == 1;
    }

    private static void RemoveBanner() {
        AdsManager.instance.allowBanner = false;
        AdsManager.instance.DestroyBanner();
    }

    private static void RemoveIngameAds() {
        if (AnzuManager.instance != null) {
            AnzuManager.instance.DestroyBanner();
            Destroy(AnzuManager.instance.gameObject);
        }

        if (AdvertyManager.instance != null) {
            AdvertyManager.instance.DestroyBanner();
            Destroy(AdvertyManager.instance.gameObject);
        }
    }

    /// <summary>
    /// Call when subscription is expired
    /// </summary>
    public void RestoreAds() {
        _isNoAds = false;
        if (SongManager.instance != null) {
            List<Song> valueCollection = SongManager.instance.GetSongsAsList();
            if (valueCollection != null) {
                for (int i = 0, n = valueCollection.Count; i < n; i++) {
                    Song it = valueCollection[i];
                    if (it != null) {
                        it.UpdateData();
                    }
                }
            }
        }

        if (AdsManager.instance != null) {
            AdsManager.instance.allowBanner = AdsManager.instance.IsEnableBanner;
            AdsManager.instance.LoadBanner();
        }

        if (Util.IsHomeScene()) {
            if (HomeManager.instance != null) {
                HomeManager.instance.UpdateVIPUI();
            }

            if (SongList.instance != null) {
                SongList.instance.BuildList();
            }
        }

        SetEconomyEarning();
    }

    private void SetEconomyEarning() {
        if (!RemoteConfig.instance.Economy_IsEnable) {
            return;
        }

        var remoteConfig = RemoteConfigBase.instance;
        if (!CanEarnDiamond()) {
            Logger.Log("[Earning Config] - Modify earning config");
            remoteConfig.Economy_MileStone_Enable = false; // không kiếm gem bởi milestone
            remoteConfig.Economy_Diamond_Ingame_StartDiamond = false; // không kiếm gem bởi các mốc đầu milestone
            if (remoteConfig.LongTile_IndexLine == (int) LongNoteType.LineDiamond ||
                remoteConfig.LongTile_IndexLine == (int) LongNoteType.LineDiamondFirstTime) {
                remoteConfig.LongTile_IndexLine = (int) LongNoteType.LineMusicNote; // không kiếm gem trên longnote
            }

            remoteConfig.SevenDayMission_IsEnable = false; // không kiếm gem trên sevenday mission
        } else {
            remoteConfig.RestoreEconomyRemoteConfig();
        }
    }

    public void SetMusic(bool ON) {
        if (ON)
            SetMusicOn();
        else
            SetMusicOff();
    }

    public void SetMusicOn() {
        PlayerPrefs.SetInt(CONFIG_STRING.Music, 1);
        musicIsOn = true;
    }

    public void SetMusicOff() {
        PlayerPrefs.SetInt(CONFIG_STRING.Music, 0);
        musicIsOn = false;
    }

    public bool MusicIsOn() {
        return musicIsOn;
    }

    public void SetSound(bool ON) {
        if (ON)
            SetSoundOn();
        else
            SetSoundOff();
    }

    public void SetSoundOn() {
        PlayerPrefs.SetInt(CONFIG_STRING.Sound, 1);
        soundIsOn = true;
    }

    public void SetSoundOff() {
        PlayerPrefs.SetInt(CONFIG_STRING.Sound, 0);
        soundIsOn = false;
    }

    public bool SoundIsOn() {
        return soundIsOn;
    }

    public bool VibrationIsOn() {
        return vibrationIsOn;
    }

    public void SetVibrationOn() {
        PlayerPrefs.SetInt(CONFIG_STRING.PlayVibration, 1);
        vibrationIsOn = true;
    }

    public void SetVibrationOff() {
        PlayerPrefs.SetInt(CONFIG_STRING.PlayVibration, 0);
        vibrationIsOn = false;
    }

    public static bool IntroIsOn() {
        return PlayerPrefs.GetInt(CONFIG_STRING.Intro, 1) == 1 && !instance.enableContentTool;
    }

    public static void SetIntroOff() {
        PlayerPrefs.SetInt(CONFIG_STRING.Intro, 0);
    }

    public static string GetDailyGiftDate() {
        return PlayerPrefs.GetString(CONFIG_STRING.DailyGiftDate);
    }

    public static string GetGiftTime(string key) {
        return PlayerPrefs.GetString(key);
    }

    public static void SetGiftTime(string key, string date) {
        PlayerPrefs.SetString(key, date);
    }

    public static int GetDailyGiftNumber() {
        return PlayerPrefs.GetInt(CONFIG_STRING.DailyGiftNumber, -1);
    }

    public static void SetDailyGift(int number, string date) {
        PlayerPrefs.SetInt(CONFIG_STRING.DailyGiftNumber, number);
        PlayerPrefs.SetString(CONFIG_STRING.DailyGiftDate, date);
    }

    public static void SetWrongPlay(string type) {
        PlayerPrefs.SetString(CONFIG_STRING.WrongPlay, type);
    }

    public static string GetWrongPlay() {
        return PlayerPrefs.GetString(CONFIG_STRING.WrongPlay, "");
    }

    public static bool FreeAddSongIsOn() {
        return PlayerPrefs.GetInt(CONFIG_STRING.FreeAddSong, 1) == 1;
    }

    public static void SetFreeAddSongOff() {
        PlayerPrefs.SetInt(CONFIG_STRING.FreeAddSong, 0);
    }

    public static void SetTutorialAdOn() {
        PlayerPrefs.SetInt(CONFIG_STRING.TutorialAd, 1);
    }

    public Song GetCurrentSong() {
        //Debug.LogError("Get current song " + SongManager.instance.songs.Count);
        string songpath = PlayerPrefsCache.GetString(CONFIG_STRING.CurrentSong, null);

        if (!string.IsNullOrEmpty(songpath)) {
            if (SongManager.instance.userLocalSongs != null &&
                SongManager.instance.userLocalSongs.TryGetValue(songpath, out Song currentSong)) {
                return currentSong;
            } else if (SongManager.instance.TryGetSong(songpath, out currentSong)) {
                return currentSong;
            } else if (isTutorial && SongManager.instance.tutorialLocalSong.TryGetValue(songpath, out currentSong)) {
                return currentSong;
            }
        }

        if (SongManager.instance.GetSongsCount() != 0) {
            List<Song> valueCollection = SongManager.instance.GetSongsAsList();
            foreach (var song in valueCollection) {
                if (song.type.Equals(SONGTYPE_STRING.OPEN)) {
                    return song;
                }
            }

            //Or Return fist item in song list
            return valueCollection.First();
        } else if (SongManager.instance.userLocalSongs != null) {
            //Or Return fist item in localSongList
            foreach (Song song in SongManager.instance.userLocalSongs.Values) {
                return song;
            }
        }

        return null;
    }

    /// <summary>
    /// TH-1507: [Funtion][5.5.3][IOS][Recent song] Không hiển thị bài hát vừa chơi tại disk ở home, và recent song
    /// Hàm này sẽ thực hiện kiểm tra song, nếu không có trong danh sách mặc định, sẽ add thêm vào cuối danh sách
    /// </summary>
    /// <param name="song"></param>
    public void AddLastestPlayedSong(Song song) {
        string songPath = song.path;

        if (song.IsLocalSong()) {
            return; // bài hát đã embed
        }

        if (SongManager.instance.IsOfficial(songPath)) {
            return; // bài hát có trong defaults list
        }

        if (isTutorial && SongManager.instance.tutorialLocalSong.ContainsKey(songPath)) {
            return; // bài hát là tutorial
        }

        Logger.Log($"Add song {songPath} to defaults song list");
        SongManager.instance.AddSong(songPath, song); // add thêm vào cuối danh sách mặc định

        AddExtensionSong(song);
    }

    /// <summary>
    /// Load Các bài hát online (k thuộc songlist)
    /// </summary>
    /// <returns></returns>
    public List<Song> LoadExtensionSong() {
        SaveData.Load($"{CONFIG_STRING.ExtensionSong}.dat", ref _extensionSongs);
        return _extensionSongs;
    }

    /// <summary>
    /// Save Các bài hát online (k thuộc songlist)
    /// </summary>
    /// <returns></returns>
    private void AddExtensionSong(Song song) {
        _extensionSongs ??= new List<Song>();
        if (!_extensionSongs.Contains(song)) {
            _extensionSongs.Add(song);
            SaveData.Save($"{CONFIG_STRING.ExtensionSong}.dat", _extensionSongs);
        }
    }

    public static void SetCurrentSong(Song song) {
        PlayerPrefsCache.SetString(CONFIG_STRING.CurrentSong, song.path ?? String.Empty);
    }

    public static int GetBestScore(string id) {
        string key = Util.BuildString(string.Empty, CONFIG_STRING.BestScore, id);

        // nếu bật config
        if (RemoteConfig.instance.Secure_Data_Enable) {
            // trường hợp đã có data mã hóa
            if (SecuredPlayerPrefs.HasEncryptionKey(key)) {
                return SecuredPlayerPrefs.GetInt(key, 0);
            }
            // nếu chưa thì lấy data cũ
            // int bestScore = PlayerPrefs.GetInt(key, 0);
            // SecuredPlayerPrefs.SetInt(key, bestScore);
            // return bestScore;
        }

        return PlayerPrefs.GetInt(key, 0);
    }

    public static void SetTotalTile(int totalTile, string path) {
        PlayerPrefs.SetInt(CONFIG_STRING.TotalTileCollected + path, totalTile);
    }

    public static int GetTotalTile(string path) {
        return PlayerPrefs.GetInt(CONFIG_STRING.TotalTileCollected + path, 0);
    }

    public static int GetSongVersion(string id) {
        return PlayerPrefs.GetInt(CONFIG_STRING.SongVersion + id, 0);
    }

    public static void SetSongVersion(string id, int version) {
        PlayerPrefs.SetInt(CONFIG_STRING.SongVersion + id, version);
    }

    public static void SetBestStars(int stars, string id) {
        PlayerPrefs.SetInt(CONFIG_STRING.BestStars + id, stars);
    }

    public static int GetBestStars(string id) {
        return PlayerPrefs.GetInt(CONFIG_STRING.BestStars + id, 0);
    }

    public static void SetBestCrowns(int stars, string id) {
        PlayerPrefs.SetInt(CONFIG_STRING.BestCrowns + id, stars);
    }

    public static int GetBestCrowns(string id) {
        return PlayerPrefs.GetInt(CONFIG_STRING.BestCrowns + id, 0);
    }

    public static bool ExistBestCrowns(string id) {
        return PlayerPrefs.HasKey(CONFIG_STRING.BestCrowns + id);
    }

    public static void SetBestMileStone(int milestones, string id) {
        PlayerPrefs.SetInt(CONFIG_STRING.BestMileStone + id, milestones);
    }

    public static int GetBestMileStone(string id) {
        return PlayerPrefs.GetInt(CONFIG_STRING.BestMileStone + id, 0);
    }

    public static void SetBestScore(int score, string songPath) {
        int oldScore = GetBestScore(songPath);
        if (oldScore > score) {
            return;
        }

        SecuredPlayerPrefs.SetInt(CONFIG_STRING.BestScore + songPath, score);

        if (SongManager.instance.IsOfficial(songPath)) {
            int totalScore = GetBestScore(CONFIG_STRING.DBKey_Total);
            if (totalScore == 0) {
                foreach (string key in SongManager.instance.GetSongsKeys()) {
                    totalScore += GetBestScore(key);
                }
            } else {
                totalScore += score - oldScore;
            }

            SecuredPlayerPrefs.SetInt(CONFIG_STRING.BestScore + CONFIG_STRING.DBKey_Total, totalScore);
            AnalyticHelper.FB_Standard_Post_Score(SongManager.instance.GetSongByPath(songPath).name, score);
        }
    }

    public int GetDiamonds() {
        return Diamond;
    }

    private void IncreaseDiamonds(int change) {
        int diamond = Diamond;
        diamond += change;
        Diamond = diamond;
        SecuredPlayerPrefs.SetInt(CONFIG_STRING.Diamonds, diamond);
        OnChangeDiamond?.Invoke(diamond);

        if (AirfluxTracker.IsEnableAirFlux()) {
            Airflux.SetSoftCurrency("diamond", diamond);
        }
    }

    public void SetDiamonds(int amount) {
        Diamond = amount;
        SecuredPlayerPrefs.SetInt(CONFIG_STRING.Diamonds, amount);
        OnChangeDiamond?.Invoke(amount);

        if (AirfluxTracker.IsEnableAirFlux()) {
            Airflux.SetSoftCurrency("diamond", amount);
        }
    }

    public static bool CanEarnDiamond() {
        if (IsNoAllAds()) { // is VIP users
            if (RemoteConfigBase.instance.Economy_MileStone_Enable) {
                return true;
            }

            if (RemoteConfigBase.instance.Economy_IsEnable) { // bật economy
                return RemoteConfigBase.instance.Economy_VIP_BenefitAds_IsEnable; // tùy thuộc bật cờ này hay k
            } else {
                return true;
            }
        }

        return true;
    }

    public static void SetShowedOldUserGemPopup() {
        PlayerPrefs.SetInt(CONFIG_STRING.OldUserRewardCount, 1);
    }

    /// <summary>
    /// điều kiện hiện Popup đền bù gems: đang chơi bản Economy, Non-vip user, chưa hiện popup lần nào và chưa nhận tuto reward của bản Economy
    /// </summary>
    /// <returns></returns>
    public static bool CheckToShowOldUserGemPopup() {
        return RemoteConfig.instance.Economy_IsEnable && !SubscriptionController.IsSubscriptionVip() &&
               PlayerPrefs.GetInt(CONFIG_STRING.OldUserRewardCount, 0) == 0 &&
               PlayerPrefs.GetInt(CONFIG_STRING.NewUserGetTutorialReward, 0) == 0;
    }

    public static bool IsOpenBall(int id) {
        if (BallManager.isInstanced) {
            BallConfig ballConfig = BallManager.instance.GetBallConfig(id);
            if (ballConfig != null) {
                if (ballConfig.unlockType == UnlockType.Open) {
                    return true;
                }

                if (IsNoAllAds()) { // VIP user
                    if (ballConfig.unlockType is UnlockType.Video or UnlockType.Vip or UnlockType.SeasonalPack) {
                        return true;
                    }

                    if (ballConfig.unlockType == UnlockType.Lock) {
                        if (ballConfig.AdsPrice != 0) { //unlock by ads
                            return true;
                        } else { //unlock by diamond
                            if (!CanEarnDiamond()) {
                                return true;
                            }
                        }
                    }

                    if (ballConfig.unlockType == UnlockType.Diamond) {
                        if (!CanEarnDiamond()) {
                            return true;
                        }
                    }
                }
            }
        }

        return id == 0 || IsEarnBall(id);
    }

    public static bool IsEarnBall(int id) {
        return PlayerPrefs.GetString(CONFIG_STRING.Balls, string.Empty)
            .Contains(Util.BuildString(string.Empty, ",", id, ","));
    }

    //Price = -1 unlock by bonus (free gift, spin)
    //Price = 0 unlock video
    //Price > 0 purchase diamond
    public static bool SetOpenBall(int id, int price, string location, bool achievement = true,
                                   bool isSetSelected = true) {
        if (IsEarnBall(id)) {
            return false;
        }

        if (price > 0) {
            bool isHumanPlayer = BallManager.itemsHuman.Contains(id);
            bool valid = UpdateDiamond(-price,
                isHumanPlayer
                    ? CurrencySpendSource.unlock_char.ToString()
                    : CurrencySpendSource.unlock_ball.ToString());
            if (!valid) {
                return false;
            }

            AirfluxTracker.TrackSpendCredits(price);
        }

        string currentBalls = PlayerPrefs.GetString(CONFIG_STRING.Balls, string.Empty);
        PlayerPrefs.SetString(CONFIG_STRING.Balls, $"{currentBalls},{id},");

        instance.userDataCached.UnlockBall(id);

        if (isSetSelected) {
            SetSelectedBall(id, false, location);
        }

        MissionCenter.DoMission(MissionType.unlock_x_skins);
        OnUnlockNewBall?.Invoke();
        return true;
    }

    public static int GetSelectedBall() {
        int selectedBall = PlayerPrefs.GetInt(CONFIG_STRING.SelectedBall, BallManager.DefaultBall);

        if (selectedBall != BallManager.DefaultBall) {
            if (BallManager.isInstanced) {
                if (!BallManager.instance.ExistBallConfig(selectedBall)) {
                    selectedBall = BallManager.DefaultBall;
                    PlayerPrefs.SetInt(CONFIG_STRING.SelectedBall, BallManager.DefaultBall);
                }
            } else {
                Logger.LogWarning("[GetSelectedBall] BallManager is not instanced");
            }
        }

        return selectedBall;
    }

    public static void SetSelectedBall(int id, bool isForce, string location, string unlocktype = null) {
        if (isForce || PlayerPrefs.GetInt(CONFIG_STRING.SelectedBall, BallManager.DefaultBall) != id) {
            BallConfig ballConfig = BallManager.instance.GetBallConfig(id);
            bool isHumanPlayer = BallManager.itemsHuman.Contains(id);
            AnalyticHelper.Item_Usage(isHumanPlayer ? CurrencySpendSource.unlock_char : CurrencySpendSource.unlock_ball,
                ballConfig != null ? ballConfig.name : id.ToString());
            PlayerPrefs.SetInt(CONFIG_STRING.SelectedBall, id);

            Dictionary<string, object> param = new Dictionary<string, object> {
                {"item_id", id}, {
                    "unlock_type",
                    unlocktype ?? (ballConfig == null ? string.Empty : ShopScript.GetUnlockType(ballConfig))
                },
                {"class_type", ballConfig?.rarity.ToString()},
                {"location", location ?? ((Util.IsHomeScene()) ? "home_skinshop" : "gameplay_skinshop")}
            };
            if (RemoteConfigBase.instance.ShopBall_UseCategoryLayout) {
                param.Add("category", BallCategoryScroller.CurrentCategory);
            }

            AnalyticHelper.LogEvent(TRACK_NAME.shop_ball_item_equipped, param);

            MissionCenter.DoMission(MissionType.equip_other_ball_x_times);
        }
    }

    //Price = -1 unlock by bonus (free gift, spin)
    //Price = 0 unlock video
    //Price > 0 purchase diamond
    public bool SetOpenSong(Song song, int price, bool achievement, SongUnlockType songUnlockType) {
        if (IsOpenSong(song.path)) {
            return true;
        }

        if (price > 0) {
            bool valid = UpdateDiamond(-price, CurrencySpendSource.unlock_song.ToString());
            if (!valid) {
                return false;
            }

            AirfluxTracker.TrackSpendCredits(price);
        }

        song.savedType = SONGTYPE.OPEN;
        PlayerPrefs.SetString(song.path, SONGTYPE.OPEN.ToString());
        if (songUnlockType != SongUnlockType.@default) {
            PlayerPrefs.SetString(PlayerPrefsKey.SongUnlockType + song.GetKeyOfSong(), songUnlockType.ToString());
        }

        if (achievement) {
            userDataCached.UnlockSong(song.path, song.GetUnlockTypeString());
        }

        UserEntry.SaveUnlockSong(song); //TH-517
        return true;
    }

    public bool IsOpenSong(string songPath) {
        return PlayerPrefs.GetString(songPath) == SONGTYPE_STRING.OPEN;
    }

    private static bool UnlockAllSongByVIP() {
        if (!RemoteConfigBase.instance.Economy_IsEnable) {
            return !CanEarnDiamond();
        }

        if (!CanEarnDiamond())
            return true; // không kiếm đc thêm gem, thì phải unlock song

        var economyRemoteConfig = instance.GetEconomyRemoteConfig();
        if (economyRemoteConfig != null && economyRemoteConfig.ModifiedSubscription_Enable) {
            //var 3 not unlock all song
            return false;
        }

        return !CanEarnDiamond();
    }

    public static SONGTYPE GetSaveTypeOfSong(string path, string defaultType) {
        if (IsNoAllAds()) { //VIP user
            if (defaultType == SONGTYPE_STRING.VIDEO) {
                return SONGTYPE.OPEN;
            }

            if (defaultType == SONGTYPE_STRING.LOCK) {
                if (UnlockAllSongByVIP()) {
                    return SONGTYPE.OPEN;
                }
            }
        }

        string saved = PlayerPrefs.GetString(path, defaultType);
        if (string.IsNullOrEmpty(saved)) {
            saved = defaultType;
        } else if (string.Equals(saved, "VDEO", StringComparison.Ordinal)) {
            saved = SONGTYPE_STRING.VIDEO;
            PlayerPrefs.SetString(path, saved);
        }

        var songType = GetSongTypeByString(saved);

        if (!RemoteConfigBase.instance.Subscription_Enable && songType == SONGTYPE.VIP) {
            songType = SONGTYPE.VIDEO;
        }

        return songType;
    }

    /// <summary>
    /// Convert songtype string to enum - fast performance
    /// </summary>
    /// <param name="songType"></param>
    /// <returns></returns>
    public static SONGTYPE GetSongTypeByString(string songType) {
        switch (songType) {
            case SONGTYPE_STRING.VIDEO:
                return SONGTYPE.VIDEO;

            case SONGTYPE_STRING.OPEN:
                return SONGTYPE.OPEN;

            case SONGTYPE_STRING.LOCK:
                return SONGTYPE.LOCK;

            case SONGTYPE_STRING.PAY:
                return SONGTYPE.PAY;

            case SONGTYPE_STRING.ALBUM:
                return SONGTYPE.ALBUM;

            case SONGTYPE_STRING.CHALLENGE:
                return SONGTYPE.CHALLENGE;

            case SONGTYPE_STRING.VIP:
                return SONGTYPE.VIP;

            case SONGTYPE_STRING.EVENT:
                return SONGTYPE.EVENT;

            case SONGTYPE_STRING.USER_PROGRESSION:
                return SONGTYPE.USER_PROGRESSION;

            case SONGTYPE_STRING.COUNTDOWN:
                return SONGTYPE.COUNTDOWN;

            case SONGTYPE_STRING.CHALLENGE_OLD_USER:
                return SONGTYPE.CHALLENGE_OLD_USER;

            default:
                if (Enum.TryParse(songType, true, out SONGTYPE result)) {
                    return result;
                } else {
                    result = SONGTYPE.VIDEO;
                    if (!RemoteConfigBase.instance.isError && !SongManager.instance.isSongConfigDownloadFail) {
                        CustomException.Fire("SongList Config",
                            $"Wrong unlocktype [{songType}] in songlist {RemoteConfigBase.instance.SongList_Path} version {RemoteConfigBase.instance.SongList_Version}");
                    }

                    return result;
                }
        }
    }

    public static void UpdateUniquePlayCount(Song song) {
        int current = PlayerPrefs.GetInt(CONFIG_STRING.SongPlayCount, 0);
        current++;
        PlayerPrefs.SetInt(CONFIG_STRING.SongPlayCount, current);
        AnalyticHelper.Level(current, song.path);
    }

    public static int GetGameLevel() {
        int songPlayCount = PlayerPrefs.GetInt(CONFIG_STRING.SongPlayCount, 0);
        return songPlayCount;
    }

    public static bool IsVideoSongOpened(Song song) {
        return song.type.Equals(SONGTYPE_STRING.VIDEO) && song.savedType == SONGTYPE.OPEN;
    }

    public void ChangeEnableLogViewer() {
        SetLogViewer(!isEnableLogViewer);
    }

#if ENABLE_LOGS
        /// <summary>
        /// Instantiate or Destroy Lunaconsole logviewer
        /// </summary>
        /// <param name="isShow"></param>
        public void ShowLogViewer(bool isShow) {
            if (isShow) {
                //Enable
                if (!LunarConsolePlugin.LunarConsole.isConsoleEnabled) {
                    GameObject objLunarConsole = new GameObject("LunarConsole");
                    objLunarConsole.AddComponent<LunarConsolePlugin.LunarConsole>();
                }
                LunarConsolePlugin.LunarConsole.Show();
            } else {
                //Disable
                if (LunarConsolePlugin.LunarConsole.isConsoleEnabled) {
                    Destroy(LunarConsolePlugin.LunarConsole.instance.gameObject);
                }
            }
        }
#endif

    private Dictionary<float, float> _curveTimeValue = new Dictionary<float, float>();

    public void InitTimeCurveMoving() {
        if (_curveTimeValue == null) {
            _curveTimeValue = new Dictionary<float, float>();
        }

        if (_curveTimeValue.Count == 0) {
            Keyframe[] keyframes = curve_moving.keys;
            float timeStart = keyframes[0].time;
            float timeEnd = keyframes[keyframes.Length - 1].time;
            int totalStep = 20;
            float timeStep = (timeEnd - timeStart) / totalStep;

            for (int index = 0; index < totalStep; index++) {
                float time = timeStart + index * timeStep;
                float value = curve_moving.Evaluate(time);
                _curveTimeValue.Add(time, value);
            }

            _curveTimeValue.Add(timeEnd, keyframes[keyframes.Length - 1].value);
        }
    }

    public float GetTimeByValue(float value) {
        if (_curveTimeValue == null || _curveTimeValue.Count == 0) {
            InitTimeCurveMoving();
        }

        float time01 = 0;
        float time02 = 0;
        if (_curveTimeValue != null) {
            foreach (float time in _curveTimeValue.Keys) {
                float valueOfTime = _curveTimeValue[time];
                if (Math.Abs(valueOfTime - value) < 0.01) {
                    return time;
                }

                if (value > valueOfTime) {
                    time01 = time;
                    continue;
                }

                if (value < valueOfTime) {
                    time02 = time;
                    break;
                }
            }
        }

        return (time01 + time02) / 2;
    }

    public static bool IsSceneInit() {
        return LoadingScript.instance != null;
    }

    public void SetEndlessModeByUser(bool isEnable) {
        isEndlessModeByUser = isEnable;
        PlayerPrefs.SetInt(CONFIG_STRING.EndlessModeByUser, isEnable ? 1 : 0);
    }

    public static void SetSongBpmLocal(string path, float bpm) {
        PlayerPrefs.SetFloat(CONFIG_STRING.BpmLocal + path, bpm);
    }

    public static float GetSongBpmLocal(string path) {
        return PlayerPrefs.GetFloat(CONFIG_STRING.BpmLocal + path, 0);
    }

    public static void SetSongStartTimeLocal(string path, float startTime) {
        PlayerPrefs.SetFloat(CONFIG_STRING.SongStartTimeLocal + path, startTime);
    }

    public static float GetSongStartTimeLocal(string path) {
        return PlayerPrefs.GetFloat(CONFIG_STRING.SongStartTimeLocal + path, 0);
    }

    public static void SetSongBpmACM(string path, float bpm) {
        PlayerPrefs.SetFloat(CONFIG_STRING.SongBpm + path, bpm);
    }

    public static float GetSongBpmACM(string path) {
        return PlayerPrefs.GetFloat(CONFIG_STRING.SongBpm + path, 0);
    }

    #region Update Diamond

    /// <summary>
    /// onUpdateDiamond(int changeAmount)
    /// </summary>
    public static Action<int> onUpdateDiamond;

    public static bool UpdateDiamond(int change, string type, string newLocation = "") {
        if (!instance) {
            return false;
        }

        int valueBefore = instance.Diamond;
        if (change >= 5) {
            if (RemoteConfigBase.instance && !RemoteConfigBase.instance.PlayNewSFX_Enable) {
                SoundManager.PlayCoins();
            }
        } else if (change < 0) {
            SoundManager.PlayBuy();
            if (valueBefore < -change) {
                return false;
            }
        }

        instance.IncreaseDiamonds(change);
        int valueAfter = instance.Diamond;
        if (valueAfter < 0) {
            instance.Diamond = valueBefore;
            CustomException.Fire("Currency Spend",
                $"Spend higher then current: {change} vs {valueBefore}. location {(string.IsNullOrEmpty(newLocation) ? type : newLocation)}");
            return false;
        }

        if (TopBar.instance) {
            TopBar.instance.OnChangeDiamond(change);
        } else {
            var topBarHome = HomeManager.instance?.topBarHome;
            if (topBarHome) {
                topBarHome.UpdateUI(change);
            }
        }

        if (change >= 0) {
            _totalEarn += change;
            PlayerPrefs.SetInt(CONFIG_STRING.Currency_Total_Earn, _totalEarn);
            AnalyticHelper.FB_Standard_Earn(type, change);
            EconomyTrackingEvents.TrackCurrencyEarn(string.IsNullOrEmpty(newLocation) ? type : newLocation, change,
                _totalEarn, valueAfter);
        } else {
            transactionIDSpend = DateTime.Now.ToString("yyMMddHHmmss");
            _totalSpend -= change;
            PlayerPrefs.SetInt(CONFIG_STRING.Currency_Total_Spend, _totalSpend);
            AnalyticHelper.FB_Standard_Spend(type, Mathf.Abs(change));
            EconomyTrackingEvents.TrackCurrencySpend(string.IsNullOrEmpty(newLocation) ? type : newLocation, change,
                _totalSpend, valueAfter, transactionIDSpend);
        }

        onUpdateDiamond?.Invoke(change);
        return true;
    }

    public static string transactionIDSpend; //Transaction ID when users spend gem.

    #endregion

    /// <summary>
    /// Kiểm tra 1 bài hát có đủ điều kiện bật endless mode không?
    /// </summary>
    /// <param name="songPath"> Đường dẫn của bài hát</param>
    /// <returns></returns>
    public static bool IsEndlessModeEnable(string songPath) {
        if (!EnableEndlessModeByUser()) {
            return false;
        }

        return GetBestStars(songPath) >= RemoteConfigBase.instance.EndlessMode_UnlockByStar;
    }

    public static bool IsPrepareQuit() {
        if (instance == null) {
            return true;
        }

        if (instance.isOnApplicationQuit) {
            return true;
        }

        return false;
    }

    #region Free Diamond In Shop

    public bool IsShowFreeGemPack =>
        RemoteConfig.instance.Economy_IsEnable && RemoteConfig.instance.Economy_shop_freegem_Enable &&
        RemoteConfig.instance.Economy_shop_freegem_1stValue != 0 &&
        RemoteConfig.instance.Economy_shop_freegem_nextValue != 0 &&
        RemoteConfig.instance.Economy_shop_freegem_intervalTime != 0;

    public bool isOpenedHome { get; set; }
    public bool isAutoOpenSelectTheme { get; set; }

    public (bool isFreeGem, int amount, bool runTime, float remainTime) GetFreeGemData() {
        int freeGem1StValue = RemoteConfigBase.instance.Economy_shop_freegem_1stValue;
        int freeGemNextValue = RemoteConfigBase.instance.Economy_shop_freegem_nextValue;
        int intervalTime = RemoteConfigBase.instance.Economy_shop_freegem_intervalTime;

        if (PlayerPrefs.HasKey(CONFIG_STRING.FreeGem_Date)) {
            DateTime lastDate = Util.GetDateTimeByString(PlayerPrefs.GetString(CONFIG_STRING.FreeGem_Date),
                DateTime.Today.AddDays(-1));
            DateTime now = DateTime.Now;

            if (lastDate.DayOfYear == now.DayOfYear && (now - lastDate).TotalHours < 24) { // trùng 1 ngày
                int index = PlayerPrefs.GetInt(CONFIG_STRING.FreeGem_Index, 0);
                switch (index % 3) {
                    case 0: //count down
                        double totalSeconds = (lastDate.AddMinutes(intervalTime) - now).TotalSeconds;
                        if (totalSeconds <= 0) { // đủ thời gian interval -> reset vòng lặp
                            return (true, freeGem1StValue, false, 0);
                        } else {
                            return (false, freeGem1StValue, true, (float) totalSeconds);
                        }

                        break;

                    default: // không tính countDown
                        return (false, freeGemNextValue, false, 0);
                }
            } else { // sang ngày mới -> reset
                PlayerPrefs.DeleteKey(CONFIG_STRING.FreeGem_Index); // xóa index thưởng đi
                return (true, freeGem1StValue, false, 0f);
            }
        } else { // chưa từng nhận gì
            PlayerPrefs.DeleteKey(CONFIG_STRING.FreeGem_Index); // xóa index thưởng đi
            return (true, freeGem1StValue, false, 0f);
        }
    }

    public bool IsAvailableFreeGemsInShop() {
        if (DailyDealShop.isInstanced) {
            return DailyDealShop.HasNotice();
        }

        int intervalTime = RemoteConfigBase.instance.Economy_shop_freegem_intervalTime;

        if (PlayerPrefs.HasKey(CONFIG_STRING.FreeGem_Date)) {
            DateTime lastDate = Util.GetDateTimeByString(PlayerPrefs.GetString(CONFIG_STRING.FreeGem_Date),
                DateTime.Today.AddDays(-1));
            DateTime now = DateTime.Now;

            if (lastDate.DayOfYear == now.DayOfYear && (now - lastDate).TotalHours < 24) { // trùng 1 ngày
                int index = PlayerPrefs.GetInt(CONFIG_STRING.FreeGem_Index, 0);
                switch (index % 3) {
                    case 0: //count down
                        double totalSeconds = (lastDate.AddMinutes(intervalTime) - now).TotalSeconds;
                        return totalSeconds <= 0; // đủ thời gian interval -> reset vòng lặp

                    default: // không tính countDown
                        return true;
                }
            }

            // sang ngày mới -> reset
            return true;
        }

        // chưa từng nhận gì
        return true;
    }

    public void ReceiveFreeGem(int amount) {
        UpdateDiamond(amount, CurrencyEarnSource.FREE_DIAMOND_SHOP.ToString(),
            CurrencyEarnSource.free_diamond_shop.ToString());
        int index = PlayerPrefs.GetInt(CONFIG_STRING.FreeGem_Index, 0) + 1;
        PlayerPrefs.SetInt(CONFIG_STRING.FreeGem_Index, index);
        PlayerPrefs.SetString(CONFIG_STRING.FreeGem_Date, DateTime.Now.ToString(CONFIG_STRING.DateTimeFormat));

        MissionCenter.DoMission(MissionType.get_free_gem_in_shop_x_times);
    }

    public static string ToStringDateTimeHMS(int value) {
        int hours = value / 3600;
        int minute = (value / 60) % 60;
        int second = value % 60;
        return String.Format("{0:00} : {1:00} : {2:00}", hours, minute, second);
    }

    #endregion

    #region Validate Admin

    public void ValidateAdmin() {
        if (Application.isEditor || enableAdminFromEditor || !isAdmin) {
            return;
        }

        string prevPassword = PlayerPrefs.GetString(CONFIG_STRING.PREF_ADMIN_PASSWORD_KEY, string.Empty);
        string remoteHash = RemoteConfig.instance.Admin_Password;
        if (string.IsNullOrEmpty(prevPassword) || string.IsNullOrEmpty(remoteHash) ||
            !Util.MD5Hash(prevPassword).Equals(remoteHash)) {
            //old version || not loaded remote password || wrong passord
            Logger.EditorLogError("Wrong password ->> reset");

            EnableAdmin(false);

            if (isAutoPlay) {
                SetAutoPlay(false);
            }

            if (isDebug) {
                SetDebugMode(false);
            }

            if (isApprovalProcess) {
                SetApprovalProcess(false);
            }

            if (isSelectMIDI) {
                SetSelectMIDI(false);
            }

            if (isEnableLogViewer) {
                SetLogViewer(false);
            }

            if (enableContentTool) {
                SetContentTool(false);
            }

            if (isLongNotePrototype) {
                SetLongNotePrototype(false);
            }

            if (isSpecialTileControl) {
                SetSpecialTileControl(false);
            }

            SetForceTheme(-1);
        }
    }

    public void SetAutoPlay(bool isActive) {
        isAutoPlay = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.IsAutoPlay, isAutoPlay ? 1 : 0);
    }

    public void SetDebugMode(bool isActive) {
        isDebug = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.isDebug, isActive ? 1 : 0);
    }

    public void SetContentTool(bool isActive) {
        enableContentTool = isActive;
        PlayerPrefsCache.SetInt(CONFIG_STRING.IsContentTool, isActive ? 1 : 0);
    }

    public void SetApprovalProcess(bool isActive) {
        isApprovalProcess = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.IsApprovalProcess, isActive ? 1 : 0);
        UpdateACM_Info();
    }

    public void SetSelectMIDI(bool isActive) {
        isSelectMIDI = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.IsSelectMIDI, isActive ? 1 : 0);
    }

    public void SetLongNotePrototype(bool isActive) {
        isLongNotePrototype = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.IsLongNotePrototype, isActive ? 1 : 0);
    }

    public void SetSpecialTileControl(bool isActive) {
        isSpecialTileControl = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.IsSpecialTileControl, isActive ? 1 : 0);
    }

    public void SetSpecialTileV2(bool isActive) {
        isSpecialTileV2 = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.IsSpecialTileV2, isActive ? 1 : 0);
    }

    public void SetLogViewer(bool isActive) {
        isEnableLogViewer = isActive;
        PlayerPrefs.SetInt(CONFIG_STRING.IsEnableLogViewer, isActive ? 1 : 0);
    }

    public void SetForceTheme(int index) {
        RemoteConfig.instance.Theme_ForceID = index;
        PlayerPrefs.SetInt(CONFIG_STRING.ForceTheme, index);
    }

    #endregion

    #region LongNote Ver 2

    private LongNoteConfig _longNoteConfig;
    private bool           isParsedLongNote;

    public LongNoteConfig GetLongNoteConfig() {
        if (_longNoteConfig == null && !isParsedLongNote && RemoteConfigBase.instance != null) {
            try {
                isParsedLongNote = true;
                _longNoteConfig = JsonUtility.FromJson<LongNoteConfig>(RemoteConfigBase.instance.LongNote_Config);
                if (_longNoteConfig != null) {
                    _longNoteConfig.Init();
                }
            } catch (Exception e) {
                Logger.EditorLogError("LongNote", "Wrong config!!!!");
                // _longNoteConfig = new LongNoteConfig();
            }
        }

        return _longNoteConfig;
    }

    public void ResetLongNoteConfig() {
        _longNoteConfig = null;
        isParsedLongNote = false;
    }

    #endregion

    public static float GetBestProgression(Song song, bool isEndless) {
        string key = CONFIG_STRING.BestProgression + (isEndless ? "E" : "N") + song.GetKeyOfSong();
        return PlayerPrefs.GetFloat(key, 0);
    }

    public static void SetBestProgression(Song song, bool isEndless, float pctProgression) {
        string key = CONFIG_STRING.BestProgression + (isEndless ? "E" : "N") + song.GetKeyOfSong();
        PlayerPrefs.SetFloat(key, pctProgression);
    }

    #region Economy IAP

    private EconomyIAPRemoteData _economyIAPRemoteData;

    public EconomyIAPRemoteData GetEconomyRemoteConfig() {
        if (!RemoteConfig.instance.Economy_IsEnable)
            return null;

        if (_economyIAPRemoteData == null) {
            string economyIAPData = RemoteConfig.instance.Economy_IAP_data;
            if (!string.IsNullOrEmpty(economyIAPData)) {
                try {
                    _economyIAPRemoteData = JsonUtility.FromJson<EconomyIAPRemoteData>(economyIAPData);
                } catch (Exception e) {
                    CustomException.Fire("[GetEconomyRemoteConfig]", e.Message + " => " + economyIAPData);
                }
            }
        }

        return _economyIAPRemoteData;
    }

    public void ResetCached() {
        _economyIAPRemoteData = null;

        _isParseRatePopupConfig = false;
        _ratePopupConfig = null;
    }

    public static bool CanShowOfferNoFSAds() {
        if (IsNoAllAds())
            return false;
        if (IsNoSomeAds())
            return false;
        if (IsNoFSAds())
            return false;

        return true;
    }

    public static event Action OnBoughtStarterPack;

    public static void BuyStarterPack() {
        PlayerPrefs.SetInt(CONFIG_STRING.StarterPack, 1);
        OnBoughtStarterPack?.Invoke();
        EconomyIAPTracker.Track_StarterPackDisable();
    }

    private static bool IsBoughtStarterPack() {
        return PlayerPrefs.GetInt(CONFIG_STRING.StarterPack, 0) == 1;
    }

    #endregion

    #region IAP

    public void BuyProduct(IAPDefinitionId id, string namePack = "", Action<bool, string> callback = null) {
        if (string.IsNullOrEmpty(namePack)) {
            IapBase.BuyProduct(id, false, (isSuccess, message) => {
                if (isSuccess) {
                    OnIAPSuccessed(id);
                }

                callback?.Invoke(isSuccess, message);
                if (isSuccess) {
                    callback = null;
                }
            });
        } else {
            IapBase.BuyProduct(id, namePack, false, (isSuccess, message) => {
                if (isSuccess) {
                    OnIAPSuccessed(id);
                }

                callback?.Invoke(isSuccess, message);
                if (isSuccess) {
                    callback = null;
                }
            });
        }
    }

    public void CompletedPurchase(IAPDefinitionId definitionId, string productIdentifier, bool isSubscriptionPackage) {
        if (isSubscriptionPackage) {
            SubscriptionController.StartSubscription_RevenueCat(definitionId != IAPDefinitionId.subs_no_ads,
                productIdentifier);
        }
    }

    private void OnIAPSuccessed(IAPDefinitionId id) {
        AnalyticHelper.FireEvent(FIRE_EVENT.shop_purchase,
            new Dictionary<string, object>() {{TRACK_NAME.pack_name, id.ToString()}});
        LocalDB.StoreIAPHistoryToDB(id.ToString(), 1, DateTime.Now);
        if (Shop.isNeedMore) {
            AnalyticHelper.NeedMore(TRACK_NAME.popup_shop_purchase);
        }

        UserProperties.UpdateUserType(USER_PROPERTY_TYPE.IAP);
        GameCore.BHBalancy.CustomUserProperties.IncreasePackagePurchased();
        GameCore.BHBalancy.CustomUserProperties.IncreaseMoneySpent((int) IapBase.GetPriceValue(id));
        if (!GameController.CheckInstanced() || GameController.instance.game != GameStatus.LIVE) {
            if (!MessagePopup.instance)
                Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("PURCHASE_SUCCESS"));
            else
                StartCoroutine(ShowMessage());
        }
    }

    private IEnumerator ShowMessage() {
        MessagePopup.instance.Close();

        while (MessagePopup.instance != null) {
            yield return null;
        }

        Util.ShowMessage(LocalizationManager.instance.GetLocalizedValue("PURCHASE_SUCCESS"));
    }

    #endregion

    private StarterPackConfig _starterPackConfig;

    public StarterPackConfig GetStarterPackConfig() {
        if (!RemoteConfigBase.instance.StarterPack_IsEnable)
            return null;

        if (_starterPackConfig == null) {
            string config = RemoteConfigBase.instance.StarterPack_Config;
            if (!string.IsNullOrEmpty(config)) {
                try {
                    _starterPackConfig = JsonUtility.FromJson<StarterPackConfig>(config);
                } catch (Exception e) {
                    CustomException.Fire("[GetStarterPackConfig]", e.Message + " => " + config);
                }
            }
        }

        return _starterPackConfig;
    }

    public bool CanShowStarterPack() {
        if (IsBoughtStarterPack()) {
            return false; //already bought package
        }

        var config = GetStarterPackConfig();
        if (config == null) {
            return false;
        }

        if (config.condition == null) {
            return false;
        }

        // điều kiện về day-diff
        int userCurrentDay = UserProperties.GetDayDiff();
        if (config.condition.After_daydiff > userCurrentDay) {
            return false;
        }

        // điều kiện về session: session = n <=> session id = n-1
        if (config.condition.After_session >= UserProperties.GetSessionCount()) {
            return false;
        }

        if (SubscriptionController.IsSubscriptionVip() && !config.condition.Active_Sub) {
            return false;
        }

        if (config.Diamond == 0 && config.Skins.IsNullOrEmpty() && config.Booster.IsNullOrEmpty()) {
            return false; //no benefit
        }

        // Bỏ qua điều kiện về thời gian đối với ver 2+3 vì không có Deal Expiration Time
        if (RemoteConfigBase.instance.StarterPack_Version >= 2) {
            return true;
        }

        string timeBeginStarterPack = PlayerPrefs.GetString(EconomyOfferStarterPack.TIME_SHOW_PREF, "");
        if (string.IsNullOrEmpty(timeBeginStarterPack)) {
            return true;
        }

        DateTime beginTime = DateTime.Parse(timeBeginStarterPack);
        DateTime now = DateTime.Now;

        if (now < beginTime) {
            EconomyIAPTracker.Track_StarterPackDisable();
            return false; //invalid time
        }

        if ((now - beginTime).TotalHours >= config.condition.Starter_pack_Time_limit) {
            EconomyIAPTracker.Track_StarterPackDisable();
            return false; //run out of valid time
        }

        return true; //valid time
    }

    public TimeSpan GetRemainTimeStarterPack() {
        var config = GetStarterPackConfig();
        if (config == null)
            return TimeSpan.Zero;

        string timeBeginStarterPack = PlayerPrefs.GetString(EconomyOfferStarterPack.TIME_SHOW_PREF, "");
        TimeSpan timeShow = TimeSpan.FromHours(config.condition.Starter_pack_Time_limit);
        if (string.IsNullOrEmpty(timeBeginStarterPack)) {
            PlayerPrefs.SetString(EconomyOfferStarterPack.TIME_SHOW_PREF,
                DateTime.Now.ToString(CultureInfo.InvariantCulture));
            return timeShow;
        } else {
            DateTime beginTime = DateTime.Parse(timeBeginStarterPack);
            DateTime now = DateTime.Now;
            return timeShow - (now - beginTime);
        }
    }

    public bool CanShowStarterPackAutomatic() {
        if (!CanShowStarterPack()) {
            return false;
        }

        if (RemoteConfigBase.instance.StarterPack_Version == 1) {
            //check capping per day : 1
            string value = PlayerPrefs.GetString(EconomyOfferStarterPack.AUTO_SHOW_TIME_PREF, string.Empty);
            if (string.IsNullOrEmpty(value)) {
                return true;
            }

            if (DateTime.TryParse(value, out DateTime lastShow)) {
                DateTime today = DateTime.Today;
                if (today.DayOfYear != lastShow.DayOfYear) {
                    PlayerPrefs.DeleteKey(EconomyOfferStarterPack.AUTO_SHOW_COUNT_PREF);
                    return true;
                }

                int cappingPerDay = _starterPackConfig.condition.Capping_per_day;
                int showCount = PlayerPrefs.GetInt(EconomyOfferStarterPack.AUTO_SHOW_COUNT_PREF, 0);
                return showCount < cappingPerDay;
            }

            CustomException.Fire("StarterPack", "Wrong datetime automatic show format!!!");
            return true;
        }

        // [Ver>=2] only show 1 automatic starterpack per 1 session
        if (_isShownStarterPack) {
            return false;
        }

        // [Ver>=2] turn off auto show if count >= 5
        if (PlayerPrefs.GetInt(EconomyOfferStarterPack.AUTO_SHOW_SESSION_COUNT_PREF, 0) >=
            _starterPackConfig.condition.MaxAutoPopupSession) {
            return false;
        }

        // [Ver2>=] auto-show only once each session
        return !EconomyOfferStarterPack.isAutoShowed;
    }

    public void AutomaticShowStarterPack() {
        _isShownStarterPack = true;
        PlayerPrefs.SetString(EconomyOfferStarterPack.AUTO_SHOW_TIME_PREF,
            DateTime.Today.ToString(CultureInfo.InvariantCulture));

        string autoShowPrefKey = RemoteConfigBase.instance.StarterPack_Version != 1
            ? EconomyOfferStarterPack.AUTO_SHOW_SESSION_COUNT_PREF // ver 2, 3
            : EconomyOfferStarterPack.AUTO_SHOW_COUNT_PREF; // ver 1

        int showCount = PlayerPrefs.GetInt(autoShowPrefKey, 0);
        showCount++;
        PlayerPrefs.SetInt(autoShowPrefKey, showCount);
    }

    public static bool CanInterruptResultScreen() {
        if (!RemoteConfigBase.instance.ResultScreen_CanInterrupt) {
            return false;
        }

        if (ChallengeOldUserController.IsPlayingChallenge)
            return false;
        if (DiscoveryChallengeManager.isInstanced && DiscoveryChallengeManager.instanceSafe.IsInprogress)
            return false;

        if (UserProgressionController.EnableFeature) {
            if (GameController.instance != null) {
                int exp = GameController.instance.GetCurrentExp();
                if (UserProgressionController.instanceSafe.CanLevelUpByExp(exp))
                    return false;
            }
        }

        switch (RemoteConfigBase.instance.ResultScreen_StyleIndex) {
            case 2:
            case 3:
            case 4:
                return false;
        }

        return true;
    }

    private IEnumerator IEUpdateQualitySettings() {
        yield return new WaitForSeconds(0.5f);

        // DeviceHelper.DeviceType deviceType = DeviceHelper.GetDeviceType();
        // int targetLevel = deviceType switch {
        //     DeviceHelper.DeviceType.Low => 0,
        //     DeviceHelper.DeviceType.Middle => 1,
        //     DeviceHelper.DeviceType.High => 2,
        //     _ => 1 // Default to middle
        // };
        //
        // // Chỉ apply expensive changes nếu thực sự cần
        // bool applyExpensive = QualitySettings.GetQualityLevel() != targetLevel;
        // if (applyExpensive) {
        //     QualitySettings.SetQualityLevel(targetLevel);
        // }
        //
        // Logger.LogWarning($"[UpdateQualitySettings] Quality level set to: {targetLevel}");

        float memoryGB = SystemInfo.systemMemorySize / 1024f;
        int roundedMemoryGB = Mathf.RoundToInt(memoryGB);
        CustomException.SetKey(FirebaseKey.systemMemorySize, roundedMemoryGB + "G");
    }

    #region Rate Popup

    private static bool            _isParseRatePopupConfig;
    private static RatePopupConfig _ratePopupConfig;

    public static RatePopupConfig GetRateConfig() {
        if (_ratePopupConfig != null) {
            return _ratePopupConfig;
        }

        if (_isParseRatePopupConfig) {
            return null;
        }

        _isParseRatePopupConfig = true;
        var config = RemoteConfig.instance.RatePopup_Config;
        if (string.IsNullOrEmpty(config)) { //null
            return null;
        }

        _ratePopupConfig = JsonUtility.FromJson<RatePopupConfig>(config);
        return _ratePopupConfig;
    }

    #endregion

    #region Notification Box

    [Space] [SerializeField] private TextAsset   localDataNotificationBox;
    [HideInInspector]        public  bool        isLoadDoneNotificationData;
    public                           List<IData> dataNotificationBox;

    public async void LoadNotificationData() {
        try {
            //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

            if (isLoadDoneNotificationData || (dataNotificationBox != null && dataNotificationBox.Count > 0)) {
                return;
            }

            string dataCsv = null;
            string urlData = RemoteConfigBase.instance.NotificationBox_UrlData;
            try {
                if (!string.IsNullOrEmpty(urlData)) {
                    dataCsv = await DownloadManager.DownloadCsv(urlData);
                }
            } catch (Exception e) {
                CustomException.Fire("[LoadData]", " ERR: " + e.Message);
            }

            if (string.IsNullOrEmpty(dataCsv)) {
                dataCsv = localDataNotificationBox.text;
            }

            if (!string.IsNullOrEmpty(dataCsv)) {
                this.StartCoroutineAsync(IENProcessDataConfig(dataCsv, urlData));
            }
        } catch (Exception e) {
            CustomException.Fire("[LoadNotificationData]", " ERR: " + e.Message);
        }
    }

    private IEnumerator IENProcessDataConfig(string dataCsv, string urlData) {
        yield return Ninja.JumpBack;

        List<NotificationItemData> configs = CSVReader.ProcessDataCSV<NotificationItemData>(dataCsv, urlData);

        yield return Ninja.JumpToUnity;

        if (configs != null && configs.Count > 0) {
            for (int index = 0; index < configs.Count; index++) {
                NotificationItemData itemData = configs[index];
                bool init = itemData.Init();
                if (!init) {
                    configs.RemoveAt(index);
                    index--;
                }
            }

            dataNotificationBox = new List<IData>(configs);

            if (additionalDataNotificationBox != null) {
                for (int index = 0; index < additionalDataNotificationBox.Count; index++) {
                    NotificationItemData itemData = (NotificationItemData) additionalDataNotificationBox[index];
                    bool init = itemData.Init();
                    if (!init) {
                        additionalDataNotificationBox.RemoveAt(index);
                        index--;
                    }
                }

                dataNotificationBox.AddRange(additionalDataNotificationBox);
            }
        }

        isLoadDoneNotificationData = true;
    }

    public List<IData> additionalDataNotificationBox;

    NotificationItemData GenItemData(int id, string title, string description) {
        NotificationItemData itemData = new NotificationItemData();
        itemData.id = id;
        itemData.title = title;
        itemData.description = description;
        return itemData;
    }

    public void PushNotification(int id, string title, string description) {
        NotificationItemData itemData = GenItemData(id, title, description);
        itemData.reward_type = RewardType.None;

        additionalDataNotificationBox ??= new List<IData>();
        additionalDataNotificationBox.Add(itemData);
    }

    public void PushNotification(int id, string title, string description, int totalGems) {
        NotificationItemData itemData = GenItemData(id, title, description);
        itemData.reward_type = RewardType.Gem;
        itemData.total = totalGems;

        additionalDataNotificationBox ??= new List<IData>();
        additionalDataNotificationBox.Add(itemData);
    }

    public bool HasUnreadNotification() {
        if (dataNotificationBox == null || dataNotificationBox.Count == 0) {
            return false;
        }

        return true;
    }

    #endregion

    public static bool IsEarnTheme(int id) {
        return PlayerPrefs.GetString(CONFIG_STRING.ThemesUnlocked, string.Empty).Contains("," + id.ToString() + ",");
    }

    #region Special Section

    public static List<int> GetSpecialTiles() {
        List<int> options = RemoteConfig.instance.NewElements_Tile_index.ToList();
        if (options.IsNullOrEmpty())
            return null;

        return options;
    }

    public static int GetSpecialTileIndex() {
        List<int> options = RemoteConfig.instance.NewElements_Tile_index.ToList();
        if (options.IsNullOrEmpty())
            return -1;

        return options[Random.Range(0, 9999) % options.Count];
    }

    public static int GetSpecialSectionIndex() {
        List<int> options = RemoteConfig.instance.NewElements_Section_Index.ToList();
        if (options.IsNullOrEmpty())
            return (int) IngameSectionType.Normal;

        return options[Random.Range(0, 9999) % options.Count];
    }

    public static List<NoteElementType> GetSpecialMoodChange() {
        List<int> options = RemoteConfig.instance.NewElements_MoodChange_Index.ToList();

        if (options.IsNullOrEmpty()) {
            return null;
        }

        List<NoteElementType> moodChange = new List<NoteElementType>();
        foreach (var option in options) {
            moodChange.Add(IntToMoodChangeType(option));
        }

        return moodChange;
    }

    private static NoteElementType IntToMoodChangeType(int id) {
        switch (id) {
            case 0:
                return NoteElementType.MoodChange;

            case 1:
                return NoteElementType.MoodChangeBroken;

            case 2:
                return NoteElementType.MoodChangeInstant;

            case 3:
                return NoteElementType.MoodChangeOrder;

            case 4:
                return NoteElementType.MoodChangeTrain;
        }

        return NoteElementType.MoodChange;
    }

    #endregion

    private static HashSet<NoteElementType> _listAutoElementTypes;

    public static HashSet<NoteElementType> GetListAutoGenElements() {
        if (_listAutoElementTypes != null)
            return _listAutoElementTypes;

        _listAutoElementTypes = new HashSet<NoteElementType>();
        if (string.IsNullOrWhiteSpace(RemoteConfigBase.instance.NewElements_AutoGen_List)) {
            return _listAutoElementTypes;
        }

        var dataElement =
            RemoteConfigBase.instance.NewElements_AutoGen_List.Split(';', StringSplitOptions.RemoveEmptyEntries);
        foreach (var data in dataElement) {
            if (Enum.TryParse(data, out NoteElementType value)) {
                if (!_listAutoElementTypes.Contains(value)) {
                    _listAutoElementTypes.Add(value);
                }
            }
        }

        return _listAutoElementTypes;
    }

    #region Unlock Elements

    private static HashSet<NoteElementType> _listEarnedElement;

    public static void EarnElement(NoteElementType elementType) {
        string earnData = PlayerPrefs.GetString(CONFIG_STRING.Key_EarnedElement, string.Empty);
        var earnElement = GetEarnedElements();
        if (earnElement.Contains(elementType))
            return;

        StringBuilder builder = new StringBuilder(earnData);
        builder.Append(FileHelper.Split);
        builder.Append(elementType.ToString());
        PlayerPrefs.SetString(CONFIG_STRING.Key_EarnedElement, builder.ToString());
        _listEarnedElement?.Add(elementType);
        PlayerPrefs.SetString(CONFIG_STRING.Key_ProirityEarnedElement, elementType.ToString());
    }

    public static void EarnElement(List<NoteElementType> elementTypes) {
        if (elementTypes.IsNullOrEmpty())
            return;

        string earnData = PlayerPrefs.GetString(CONFIG_STRING.Key_EarnedElement, string.Empty);
        var earnElement = GetEarnedElements();
        StringBuilder builder = new StringBuilder(earnData);
        foreach (var elementType in elementTypes) {
            if (earnElement.Contains(elementType))
                continue;

            builder.Append(FileHelper.Split);
            builder.Append(elementType.ToString());
        }

        PlayerPrefs.SetString(CONFIG_STRING.Key_EarnedElement, builder.ToString());
        _listEarnedElement?.AddRange(elementTypes);
        PlayerPrefs.SetString(CONFIG_STRING.Key_ProirityEarnedElement, elementTypes[0].ToString());
    }

    public static HashSet<NoteElementType> GetEarnedElements() {
        if (_listEarnedElement != null) {
            return _listEarnedElement;
        }

        _listEarnedElement = new HashSet<NoteElementType>();

        string earnData = PlayerPrefs.GetString(CONFIG_STRING.Key_EarnedElement, string.Empty);
        if (string.IsNullOrEmpty(earnData)) {
            return _listEarnedElement;
        }

        var split = earnData.Split(FileHelper.Split, StringSplitOptions.RemoveEmptyEntries);
        foreach (var part in split) {
            if (Enum.TryParse(part, out NoteElementType element)) {
                _listEarnedElement.Add(element);
            }
        }

        return _listEarnedElement;
    }

    public static void SetListAutoGenElements(HashSet<NoteElementType> newList) {
        _listAutoElementTypes = newList;
    }

    // #region Currency
    //
    // public int GetAmountItemRevive() {
    //     if (_itemRevive == null)
    //         return 0;
    //
    //     return _itemRevive.Current;
    // }
    //
    // public void UseItemRevive(int amount) {
    //     if (_itemRevive == null)
    //         return;
    //
    //     _itemRevive.Current -= amount;
    // }
    //
    //
    // #endregion
    public static NoteElementType GetPriorityEarnedElements() {
        if (PlayerPrefs.HasKey(CONFIG_STRING.Key_ProirityEarnedElement)) {
            if (Enum.TryParse(PlayerPrefs.GetString(CONFIG_STRING.Key_ProirityEarnedElement),
                    out NoteElementType elementType)) {
                return elementType;
            }
        }

        return NoteElementType.None;
    }

    public static void ResetPriorityEarnedElements() {
        PlayerPrefs.DeleteKey(CONFIG_STRING.Key_ProirityEarnedElement);
    }

    #endregion

    public static int GetCurrentLevel() {
        if (UserProgressionController.EnableFeature) {
            return UserProgressionController.instanceSafe.Level;
        }

        if (StarsJourneyManager.isEnable) {
            return StarsJourneyManager.instanceSafe.level;
        }

        return 0;
    }

    private Material _feverMaterial;

    public Material GetFeverMaterial() {
        if (_feverMaterial == null) {
            _feverMaterial = Resources.Load<Material>("Materials/BoxMaterial_fever");
        }

        return _feverMaterial;
    }

    private IEnumerator IEInitFeatures() {
        while (!RemoteConfigBase.isLoaded) {
            yield return null;
        }

        FeatureUnlockManager.instanceSafe.Init();

        // init galaxy quest manager
        if (RemoteConfigBase.instance.GalaxyQuest_IsEnable) {
            GalaxyQuest.Init();
            yield return null;
        } else {
            FeatureUnlockManager.instanceSafe.TurnOffFeature(FeatureKey.GALAXY_QUEST);
        }

        FeatureServiceManager.instanceSafe.Init();
        yield return null;

        if (SpecialOfferManager.IsEnable()) {
            SpecialOfferManager.instanceSafe.InitSettings();
            yield return null;
        }

        // Khởi tạo hardcore collection manager
        HardcoreSongCollection.instanceSafe.Init();
        yield return null;

        // khởi tạo singleton cho Discovery Challenge
        if (DiscoveryChallengeManager.IsEnable()) {
            DiscoveryChallengeManager.instanceSafe.InitSettings();
            yield return null;
        }

        if (RemoteConfigBase.instance.UserProgression_IsEnable) {
            UserProgressionController.instanceSafe.Init();
            yield return null;
        }

        if (RemoteConfigBase.instance.Hybrid_Mission_IsEnable) {
            MissionManager.instanceSafe.Init();
            yield return null;
        }

        if (RemoteConfigBase.instance.Hybrid_Achievement_IsEnable) {
            AchievementManager.instanceSafe.Init();
            yield return null;
        }

        if (RemoteConfigBase.instance.StarsJourney_IsEnable) {
            StarsJourneyManager.instanceSafe.Init();
            yield return null;
        }

        if (RemoteConfigBase.instance.InstantMission_Enable) {
            TilesHop.InstantMission.InstantMissionManager.instanceSafe.Init();
            yield return null;
        }

        if (RemoteConfigBase.instance.MileStoneEvent_IsEnable) {
            MilestoneEvent.instanceSafe.Initialize(
                new MilestoneEventAdapter(),
                new IngameResourceAdapter(),
                new UserExperienceAdapter());
        }
    }

    public void GetReward(RewardData rewardData, string location, CurrencyEarnSource earnSource,
                          SongUnlockType unlockType, string boosterSource) {
        if (rewardData == null)
            return;
        if (rewardData.rewards.IsNullOrEmpty())
            return;

        foreach (var reward in rewardData.rewards) {
            switch (reward.type) {
                case RewardType.None:
                    break;

                case RewardType.Gem:
                    UpdateDiamond(reward.valueInt, earnSource.ToString());
                    break;

                case RewardType.Skin:
                    SetOpenBall(reward.valueInt, -1, location);
                    break;

                case RewardType.Song:
                    var song = SongManager.instance.GetSongByAcmId(reward.valueStr);
                    if (song != null) {
                        instance.SetOpenSong(song, 0, true, unlockType);
                    } else {
                        Debug.LogError($"Song not found: {reward.valueStr}");
                    }

                    break;

                case RewardType.Theme:
                    ThemeManager.instance?.SetOpenTheme(reward.valueInt, -1);
                    break;

                case RewardType.Booster:
                    if (Enum.TryParse(reward.valueStr, out BoosterType type)) {
                        BoosterManager.AddItemAmount(type, boosterSource, reward.valueInt);
                    } else {
                        Debug.LogError($"Booster type not recognized. {reward.valueStr}");
                    }

                    break;

                case RewardType.UnlimitedRevive:
                    UnlimitedReviveManager.IncreaseUnlimitedReviveTime(reward.valueInt);
                    break;

                default:
                    Debug.LogError($"Not handle this reward type {reward.type}");
                    break;
            }
        }
    }

    public static bool EnableEndlessMode() {
        if (RemoteConfigBase.instance.ChallengeMode_Enable) {
            return false;
        }

        return RemoteConfigBase.instance.EndlessMode_Enable;
    }

    public static bool EnableEndlessModeByUser() {
        return EnableEndlessMode() && instance.isEndlessModeByUser;
    }
}