using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GameCore.Scripts.Core {
    public class AchievementBase : FastSingleton<AchievementBase> {
        public static            bool                 EnableAchievement;
        [SerializeField] private List<AchievementObj> achievementList;

        private bool _isUpdateAchievement;

        public void Init(List<AchievementObj> achievementObjs) {
            achievementList = achievementObjs;
            EnableAchievement = true;
        }

        public void WaitAchievementInitDone(Action onDone) {
            if (_isUpdateAchievement) {
                onDone?.Invoke();
            } else {
                Util.WaitBallManagerInitDone(() => {
                    Util.WaitSongListDone(() => {
                        Util.WaitThemeManagerInitDone(() => {
                            StartCoroutine(IEUpdateAchievement(onDone));
                        });
                    });
                });
            }
        }

        private IEnumerator IEUpdateAchievement(Action onDone) {
            if (!_isUpdateAchievement) {
                yield return UpdateAchievement();

                if (Util.IsHomeScene()) {
                    HomeManager.instance.UpdateAchievementBadgeCount();
                }
            }

            onDone?.Invoke();
        }

        public List<AchievementObj> GetAchievementList() {
            // When the application is quitting or pausing, this instance might already be destroyed.
            // Accessing it would lead to a NullReferenceException. This check prevents that.
            if (this == null) {
                return null;
            }

            if (!_isUpdateAchievement) {
                StartCoroutine(UpdateAchievement());
            }

            return achievementList;
        }

        public void CheckAchievements() {
            StartCoroutine(AchievementObj.CheckTypes(new[] {
                // score achievements
                AchievementType.SCORE_100,
                AchievementType.SCORE_250,
                AchievementType.SCORE_500,
                AchievementType.SCORE_1000,

                // star achievements
                AchievementType.STARS_1,
                AchievementType.STARS_10,
                AchievementType.STARS_ALL,

                // retry achievements
                AchievementType.RETRY_50,
                AchievementType.RETRY_100,

                // revive achievements
                AchievementType.REVIVE_20,
                AchievementType.REVIVE_50,

                // others
                AchievementType.PERFECT_10
            }));
        }

        public IEnumerator UpdateAchievement() {
            yield return AchievementObj.PrepareAchievementStatusesSong();
            yield return AchievementObj.PrepareAchievementStatusesBall();

            foreach (AchievementObj achievement in achievementList) {
                achievement.UpdateStatus(true);
            }
            _isUpdateAchievement = true;
        }

        public static bool CheckNewChallenge() {
            foreach (AchievementObj achievement in instance.achievementList) {
                if (achievement.isNewChallenge()) {
                    return achievement.status == AchievementStatus.RECEIVED ||
                           achievement.status == AchievementStatus.UNLOCK;
                }
            }

            return false;
        }

        public void IncreaseCountUnlockTheme(int unlockThemeCount) {
            StartCoroutine(AchievementObj.CheckTypes(new[] {
                AchievementType.UNLOCK_THEME_2,
                AchievementType.UNLOCK_THEME_ALL
            }));
        }

        public void IncreaseCountUnlockBall(int unlockBallCount) {
            StartCoroutine(IEIncreaseCountUnlockBall());

            IEnumerator IEIncreaseCountUnlockBall() {
                yield return AchievementObj.PrepareAchievementStatusesBall();
                yield return StartCoroutine(AchievementObj.CheckTypes(new[] {
                    AchievementType.UNLOCK_BALL_5,
                    AchievementType.UNLOCK_BALL_10,
                    AchievementType.UNLOCK_BALL_ALL
                }));
            }
        }

        public void IncreaseCountUnlockSong(int count) {
            StartCoroutine(IEIncreaseCountUnlockSong());

            IEnumerator IEIncreaseCountUnlockSong() {
                yield return AchievementObj.PrepareAchievementStatusesSong();
                yield return StartCoroutine(AchievementObj.CheckTypes(new[] {
                    AchievementType.UNLOCK_SONG_5,
                    AchievementType.UNLOCK_SONG_10,
                    AchievementType.UNLOCK_SONG_ALL
                }));
            }
        }

        public void IncreaseCountPlaySong(int count) { }

        public void IncreaseCountReplay(int count) {
            StartCoroutine(AchievementObj.CheckTypes(new[] {
                AchievementType.RETRY_50,
                AchievementType.RETRY_100,
            }));
        }
    }
}