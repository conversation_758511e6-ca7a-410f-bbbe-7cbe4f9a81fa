using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class FadeToast : MonoBehaviour {
    [SerializeField] private float fadeDuration = 0.3f;
    [SerializeField] private float toastLiveTime = 1.5f;
    [SerializeField] private CanvasGroup canvasGroup;
    [SerializeField] private Text txtMessageLine1;
    [SerializeField] private Text txtMessageLine2;

    private static string messageLine1;
    private static string messageLine2;
    private Sequence _fadeSequence;
    
    private void Start() {
        txtMessageLine1.text = messageLine1;
        txtMessageLine2.text = messageLine2;
        
        if (canvasGroup) {
            _fadeSequence = DOTween.Sequence();
            _fadeSequence.Append(canvasGroup.DOFade(1f, fadeDuration).SetEase(Ease.Linear));
            _fadeSequence.AppendInterval(toastLiveTime);
            _fadeSequence.Append(canvasGroup.DOFade(0f, fadeDuration).SetEase(Ease.Linear));
            _fadeSequence.OnComplete(CloseToast);
        }
        else {
            DOVirtual.DelayedCall(toastLiveTime, CloseToast);
        }
    }

    private void OnDestroy() {
        if (_fadeSequence != null && _fadeSequence.IsActive()) {
            _fadeSequence.Kill();
        }
    }

    private void CloseToast() {
        Destroy(gameObject);
    }

    public static void ShowFadeLocalizedToast(string line1LocalizedKey, string line2LocalizedKey) {
        messageLine1 = LocalizationManager.instance.GetLocalizedValue(line1LocalizedKey);
        messageLine2 = LocalizationManager.instance.GetLocalizedValue(line2LocalizedKey);
        Util.ShowPopUp(PopupName.FadeToast);
    }
}
