using System.Collections;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using System;
using System.Linq;
using DG.Tweening;
using Facebook.MiniJSON;
using Music.ACM;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.UserProgression;
using Random = UnityEngine.Random;

public class SongManager : MonoBehaviour {
    public static SongManager instance;

    private Dictionary<string, Song> _songs; //path, Song

    [ShowInInspector] public Dictionary<string, Song> userLocalSongs; // các bài hát import từ device
    public                   Dictionary<string, Song> tutorialLocalSong; // các bài hát tutorial local
    public                   Dictionary<string, Song> tutorialRemoteSong;

    public                   List<string> levelBotSongs = new List<string>();
    [SerializeField] private TextAsset    defaultSongConfig;

    [SerializeField] private TextAsset defaultTutorialLocalConfig; // các bài hát tutorial local

    private const string Key = "Songs_ACM_V3";

    private const string KeyHash = "Songs_ACM_V3Hash";

    //private const string KeySongsResource     = "SongsResource";
    private const int AcmApiLimitSongCount   = 200;
    private       int _stepFrameCheckLicense = 10;

    private RemoteConfig remoteConfig => RemoteConfig.instance;
    [ShowInInspector] private string UsedSongListPath;

    public static bool isInitedSongList;

    private static int _userProgressCurrentSelectedPack = 1;

    public static int UserProgress_CurrentSelectedPackLevel {
        get => _userProgressCurrentSelectedPack;
        set => _userProgressCurrentSelectedPack = value;
    }

    public static event Action OnInitedSongList;

    [HideInInspector] public string idSongOfDay;

    #region Unity Method

    private void Awake() {
        if (instance == null) {
            instance = this;
        } else if (instance != null) {
            Destroy(gameObject);
        }
    }
    
    private void Start() {
        _ = UniStart();
    }

    private async UniTaskVoid UniStart() {
        if (instance != this) {
            return;
        }

        await UniTask.Yield(this.GetCancellationTokenOnDestroy());

        _songs = new Dictionary<string, Song>();

        userLocalSongs = new Dictionary<string, Song>();
        tutorialLocalSong = LoadTutorialLocalSongs();

        LoadSongLocal();
        SubscriptionController.OnChange += SubscriptionBtnOnChange;

        Util.WaitRemoteConfigDone(() => LoadSongList(this.GetCancellationTokenOnDestroy()).Forget());
    }

    private void Update() {
        if (remoteConfig.ACMv4_EnableCheckLicense && Time.frameCount % _stepFrameCheckLicense == 0) {
            if (CanCheckLicense()) {
                CheckingLicense();
            }
        }
    }

    private void OnDestroy() {
        SubscriptionController.OnChange -= SubscriptionBtnOnChange;

        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
        
        // Note: No manual CancellationTokenSource needed - SongManager only uses auto-destroy tokens
        // GetCancellationTokenOnDestroy() automatically handles cleanup
    }

    #endregion

    public static int UserProgress_NextSelectedLevel() {
        int packCount = UserProgressionController.instanceSafe.SongPacksHolder.dictSongPacks.Count;
        return UserProgress_CurrentSelectedPackLevel % packCount + 1;
    }

    public static int UserProgress_PrevSelectedLevel() {
        int packCount = UserProgressionController.instanceSafe.SongPacksHolder.dictSongPacks.Count;
        return (UserProgress_CurrentSelectedPackLevel + packCount - 2) % packCount + 1;
    }

    /// <summary>
    /// Load Song List when Remote Config loaded
    /// </summary>
    private async UniTaskVoid LoadSongList(CancellationToken cancellationToken = default) {
        try {
            //Load songlist from resource first if available
            if (!string.IsNullOrEmpty(RemoteConfigBase.instance.SongListTargetSegment) &&
                PlayerPrefs.HasKey(PlayerPrefsKey.songsListResource)) {
                string resourcePath = PlayerPrefs.GetString(PlayerPrefsKey.songsListResource);
                LoadSongListResource(resourcePath);
                UsedSongListPath = resourcePath;
                return;
            }

            UsedSongListPath = GetSavedSongConfigVersion();
            //Check new version for download Remote SongConfig
            if (IsNewVersion()) {
                if (!remoteConfig.SongList_ForceWait) { //Allow going to game, do not need to wait
                    //Get Saved Song Config
                    await IEProcessSongList(cancellationToken: cancellationToken);
                }

                //S3 file csv songconfig
                DownloadRemoteSongConfig(GetSongConfigUrl(), cancellationToken).Forget();
            } else {
                //Get Saved Song Config
                await IEProcessSongList(cancellationToken: cancellationToken);
            }
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    public void LoadSongListResource(bool isAgeUnder15) {
        string filename = GetFilenameSongList(isAgeUnder15);
        PlayerPrefs.SetString(PlayerPrefsKey.songsListResource, filename);

        LoadSongListResource(filename);
    }

    private void LoadSongListResource(string filename) {
        string configData = Resources.Load<TextAsset>(filename).text;
        IEProcessSongList(configData, this.GetCancellationTokenOnDestroy()).Forget();
    }

    private string GetFilenameSongList(bool isAgeUnder15) {
        try {
            string filename = isAgeUnder15 ? "songconfig_0_15_Logic_" : "songconfig_15-34_logic_";

            Dictionary<string, object> rootData =
                (Dictionary<string, object>) Json.Deserialize(remoteConfig.SongListTargetSegment);
            object logicIndex;
            if (isAgeUnder15) {
                if (!rootData.TryGetValue("0-15", out logicIndex)) {
                    logicIndex = "1"; //default
                }
            } else {
                if (!rootData.TryGetValue("15-34", out logicIndex)) {
                    logicIndex = "1"; //default
                }
            }

            filename += logicIndex;
            return filename;
        } catch (Exception e) {
            CustomException.Fire("[GetFilenameSongList]", e.Message + "=> get songconfig_0_15_Logic_1 song list");
            return "songconfig_0_15_Logic_1";
        }
    }

    /// <summary>
    /// Get Song Config csv file to download
    /// </summary>
    /// <returns></returns>
    string GetSongConfigUrl() {
        if (AnalyticHelper.instance.IsChina()) {
            return remoteConfig.RootPath + "Songs/SongConfig/CN.csv";
        } else {
            return remoteConfig.SongList_Path;
        }
    }

    /// <summary>
    /// Get Saved Song SongConfig Version
    /// </summary>
    /// <returns>Saved Version</returns>
    private string GetSavedSongConfigVersion() {
        string defaultValue =
            $"{RemoteConfig.DefaultSongConfigPath_Local}{RemoteConfig.DefaultSongConfigVersion_Local}";
        return PlayerPrefs.GetString(CONFIG_STRING.SongVersion, defaultValue);
    }

    /// <summary>
    /// Get Current Song SongConfig Version
    /// </summary>
    /// <returns>Current Version</returns>
    private string GetCurrentSongConfigVersion() {
        return $"{remoteConfig.SongList_Path}{remoteConfig.SongList_Version}";
    }

    /// <summary>
    /// Set Current Song SongConfig Version
    /// </summary>
    private void SetSongConfigVersion() {
        string path = GetCurrentSongConfigVersion();
        UsedSongListPath = path;
        PlayerPrefs.SetString(CONFIG_STRING.SongVersion, path);
    }

    /// <summary>
    /// Get Saved SongConfig Text
    /// </summary>
    /// <returns></returns>
    private string GetSavedSongConfig() {
        string data = string.Empty;
        SaveData.Load($"{RemoteConfigBase.prefix}{Key}.dat", ref data);
        return data;
    }

    /// <summary>
    /// Cache SongConfig Text & Version to Playerprefs
    /// </summary>
    /// <param name="data"></param>
    private void CacheSongData(string data) {
        // if (AnalyticHelper.instance.IsChina()) {
        //     PlayerPrefs.SetString($"{RemoteConfigBase.prefix}{KeyHash}", Util.MD5Hash(data));
        // }
        // PlayerPrefs.SetString($"{RemoteConfigBase.prefix}{Key}", data);

        PlayerPrefs.DeleteKey(RemoteConfigBase.prefix + Key);
        SaveData.Save($"{RemoteConfigBase.prefix}{Key}.dat", data);
        SetSongConfigVersion();
    }

    /// <summary>
    /// Check Have a new version of Songconfig: EnableRemote Sync + difference version
    /// China users always download
    /// </summary>
    /// <returns></returns>
    private bool IsNewVersion() {
        bool isEnableSync = remoteConfig.enableRemoteSync && !string.IsNullOrEmpty(remoteConfig.SongList_Path) &&
                            GetCurrentSongConfigVersion() != GetSavedSongConfigVersion();
        return isEnableSync || AnalyticHelper.instance.IsChina() || remoteConfig.enableLocalSync;
    }

    /// <summary>
    /// Check data is new or old by Md5, China users only
    /// </summary>
    /// <param name="data">SongConfig CSV string Data</param>
    /// <returns></returns>
    private bool IsNewData(string data) {
        if (AnalyticHelper.instance.IsChina()) {
            return PlayerPrefs.GetString(RemoteConfigBase.prefix + KeyHash) != Util.MD5Hash(data);
        } else {
            return true;
        }
    }

    /// <summary>
    /// Download Remote SongConfig by url
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    private async UniTaskVoid DownloadRemoteSongConfig(string url, CancellationToken cancellationToken = default) {
        try {
            AnalyticHelper.Game_Step(GAME_STEP.Step_SongConfigDownload);
            float startWaitTime = Time.time;
            WWW wwwTemp = new WWW(url);
            while (!wwwTemp.isDone && Time.time - startWaitTime < 5) {
                await UniTask.Yield(cancellationToken);
            }

            //Prevent loadsong at game play
            while (GameController.CheckInstanced() && GameController.instance.game == GameStatus.LIVE) {
                await UniTask.Yield(cancellationToken);
            }

            if (wwwTemp.isDone && wwwTemp.error == null) {
                isSongConfigDownloadFail = false;
                if (IsNewData(wwwTemp.text)) {
                    await IEProcessSongList(wwwTemp.text, cancellationToken);

                    if (!UserProgressionController.EnableFeature) {
                        RefreshSongList();
                    }
                }
            } else {
                isSongConfigDownloadFail = true;
                AnalyticHelper.Game_Step(GAME_STEP.Step_SongConfigDownloadFail, errorMsg: wwwTemp.error);
                if (GetSongsCount() == 0) {
                    await IEProcessSongList(cancellationToken: cancellationToken);

                    if (!UserProgressionController.EnableFeature) {
                        RefreshSongList();
                    }
                }
            }
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    public bool isSongConfigDownloadFail;

    /// <summary>
    /// Refresh Song List
    /// </summary>
    private void RefreshSongList() {
        if (GetSongsCount() != 0 && SongList.instance != null) {
            SongList.instance.BuildList();
        }
    }

    /// <summary>
    /// Parse SongConfig Text To SongList Dictionary
    /// </summary>
    /// <param name="data">SongConfig Text</param>
    public async UniTask IEProcessSongList(string data = null, CancellationToken cancellationToken = default) {
        try {
            if (string.IsNullOrEmpty(data)) {
                data = GetSavedSongConfig();
            }

            await UniTask.Yield(cancellationToken);

            var newSongList = ItemContainer.GetSongs(data);

            bool isDefault = false;
            await UniTask.Yield(cancellationToken);

            if (newSongList.Count != 0) {
                //Apply new downloaded songList
                if (IsNewVersion()) {
                    CacheSongData(data);
                }

                _songs = newSongList;
            } else {
                //Apply default songList in build
                string defaultSongConfigText = defaultSongConfig.text;
                _songs = ItemContainer.GetSongs(defaultSongConfigText);
                isDefault = true;
                await UniTask.Yield(cancellationToken);

                Logger.CanLog(() => Logger.Log("[IEProcessSongList] using default song config"));
            }

            List<Song> extensionSong = Configuration.instance.LoadExtensionSong();
            if (extensionSong != null && extensionSong.Count != 0) {
                // Load thêm các bài ngoài danh sách mặc định
                foreach (var item in extensionSong) {
                    _songs.TryAdd(item.path, item);
                }
            }

            if (GetSongsCount() != 0) {
                await IEProcessSongType(cancellationToken);

                AnalyticHelper.Game_Step(GAME_STEP.Step_SongConfigLoaded);
            } else {
                AnalyticHelper.Game_Step(GAME_STEP.Step_SongConfigLoadedFail);
                if (!Configuration.instance.enableContentTool) {
                    Util.ShowMessage("Can't load the song list!");
                }
            }

            //remove song license cached before
            List<string> unlicensedSongs = GetUnlicensedSongs();
            if (unlicensedSongs != null && unlicensedSongs.Count != 0) {
                int count = unlicensedSongs.Count;
                List<string> foundSong = new List<string>(count);
                foreach (string songPath in _songs.Keys) {
                    if (unlicensedSongs.Contains(_songs[songPath].acm_id_v3)) {
                        foundSong.Add(songPath);
                        count--;
                        if (count == 0) {
                            break;
                        }
                    }
                }

                foreach (var found in foundSong) {
                    RemoveSong(found);
                }
            }

            //Init & Check first 400 songs
            _songsCheckLicense = GetSongsAsList().Take(AcmApiLimitSongCount * 2).ToList();

            await UniTask.Yield(cancellationToken);

            HideSongsInList((RemoteConfigBase.instance.ListHiddenSongs).StringToList());

            isInitedSongList = true;
            OnInitedSongList?.Invoke();

            await UniTask.Yield(cancellationToken);

            CheckCacheSongForceClose();
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    private void CheckCacheSongForceClose() {
        string songForceClose = PlayerPrefsKey.SongForceClose;
        if (!PlayerPrefs.HasKey(songForceClose)) {
            return;
        }

        string eventSongForceClose = PlayerPrefs.GetString(songForceClose);
        if (string.IsNullOrWhiteSpace(eventSongForceClose)) {
            return;
        }

        Dictionary<string, object> param =
            JsonConvert.DeserializeObject<Dictionary<string, object>>(eventSongForceClose);
        string eventName = SONG_STATUS.song_force_close.ToString();
        AnalyticHelper.UpdateParamsAccumulatedCount(param, eventName); //accumulated_count
        AnalyticHelper.LogEvent(eventName, param);
        PlayerPrefs.DeleteKey(songForceClose);
    }

    /// <summary>
    /// Update Song Type & Serial after parse song list
    /// </summary>
    private async UniTask IEProcessSongType(CancellationToken cancellationToken = default) {
        try {
            string songTypeVideo = SONGTYPE_STRING.VIDEO;
            string songTypeChallenge = SONGTYPE_STRING.CHALLENGE;
            string songTypeVip = SONGTYPE_STRING.VIP;
            bool isSubscription = SubscriptionController.IsEnableSubscription();

            bool isInitUnlockTheme = !PlayerPrefs.HasKey(CONFIG_STRING.ThemesUnlocked);
            List<int> unlockedTheme = new();

            List<Song> listSong = GetSongsAsList();
            int numberInFrame = Mathf.RoundToInt(listSong.Count * 1f / 10);

            int indexSong = 0;
            for (int i = 0, count = listSong.Count; i < count; i++) {
                Song song = listSong[i];
                if (song == null) {
                    continue;
                }

                if (song.type.Equals(songTypeChallenge)) {
                    song.type = songTypeVideo;
                } else if (song.type.Equals(songTypeVip) && !isSubscription) {
                    song.type = songTypeVideo;
                }

                indexSong++;
                song.indexInConfig = indexSong;
                song.UpdateData();

                if (isInitUnlockTheme && !string.IsNullOrEmpty(song.path)) {
                    //convert unlock theme from old version
                    int themeId = song.GetSelectedTheme();
                    if (themeId > 0 && !unlockedTheme.Contains(themeId)) {
                        unlockedTheme.Add(themeId);
                    }
                }

                if (indexSong % numberInFrame == 0) {
                    await UniTask.NextFrame(cancellationToken);
                }
            }

            if (isInitUnlockTheme && unlockedTheme.Count != 0) {
                string value = JsonConvert.SerializeObject(unlockedTheme);
                PlayerPrefs.SetString(CONFIG_STRING.ThemesUnlocked, value);
            }
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    public void MergeLocalSongList(List<FirebaseItem> ownerSongs) {
        foreach (var s in ownerSongs) {
            if (!IsExistLocalSong(s.key)) {
                s.uploadedBy = null;
                s.storage = SONG_STORAGE.LOCAL;
                userLocalSongs.Add(s.path, s);
            }
        }

        if (SongList.instance != null) {
            SongList.instance.BuildList();
        }
    }

    bool IsExistLocalSong(string key) {
        foreach (Song s in SongManager.instance.userLocalSongs.Values) {
            if (s.key == key) {
                return true;
            }
        }

        return false;
    }

    public static bool IsReady() {
        return isInitedSongList && RemoteConfigBase.isLoaded;
    }

    public Dictionary<string, Song> GetLock() {
        Dictionary<string, Song> list = new Dictionary<string, Song>();
        List<Song> values = GetSongsAsList();
        foreach (Song s in values) {
            if (s.savedType != SONGTYPE.OPEN) {
                list.Add(s.path, s);
            }
        }

        return list;
    }

    public Dictionary<string, Song> GetByTag(string tagName) {
        if (!string.IsNullOrEmpty(tagName)) {
            tagName = tagName.ToUpper();
        }

        if (tagName == null || tagName == SONG_TAG.ALL) {
            return GetAllSongs();
        }

        Dictionary<string, Song> result = new(); //path,Song
        switch (tagName) {
            case SONG_TAG.SORT_BY_NAME:
                result = _songs.OrderBy(x => x.Value.name).ToDictionary(pair => pair.Key, pair => pair.Value);
                break;

            case SONG_TAG.SORT_BY_SPEED:
                result = _songs.OrderBy(x => x.Value.bmp).ToDictionary(pair => pair.Key, pair => pair.Value);
                break;

            default:
                List<string> acmIDFavorites = UserEntry.GetFavoriteList();
                List<Song> values = GetSongsAsList();
                foreach (Song song in values) {
                    SONGTYPE type = song.savedType;
                    switch (tagName) {
                        case SONG_TAG.LOCK:
                            if ((type != SONGTYPE.OPEN && type != SONGTYPE.VIP) ||
                                (type == SONGTYPE.VIP && !SubscriptionController.IsSubscriptionVip())) {
                                if (!result.ContainsKey(song.path)) {
                                    result.Add(song.path, song);
                                }
                            }

                            break;

                        case SONG_TAG.UNLOCKED:
                            if ((type == SONGTYPE.VIP && SubscriptionController.IsSubscriptionVip()) ||
                                type == SONGTYPE.OPEN) {
                                if (!result.ContainsKey(song.path)) {
                                    result.Add(song.path, song);
                                }
                            }

                            break;

                        case SONG_TAG.FAVORITES:
                            //if ((type == SONGTYPE.VIP && SubscriptionController.IsSubscriptionVip()) ||
                            //    type == SONGTYPE.OPEN) {
                            if (acmIDFavorites.Contains(song.acm_id_v3)) {
                                if (!result.ContainsKey(song.path)) {
                                    result.Add(song.path, song);
                                }
                            }
                            //}

                            break;

                        case SONG_TAG.UNFAVORITES:
                            if ((type == SONGTYPE.VIP && SubscriptionController.IsSubscriptionVip()) ||
                                type == SONGTYPE.OPEN) {
                                if (!acmIDFavorites.Contains(song.acm_id_v3)) {
                                    if (!result.ContainsKey(song.path)) {
                                        result.Add(song.path, song);
                                    }
                                }
                            }

                            break;

                        case SONG_TAG.NEW:
                            if (song.tags != null && song.tags.ToUpper().Contains(SONG_TAG.NEW)) {
                                if (!result.ContainsKey(song.path)) {
                                    result.Add(song.path, song);
                                }
                            }

                            break;

                        case GenreType.POPULAR:
                            if (song.tags != null && song.tags.ToUpper().Contains(tagName)) {
                                if (!result.ContainsKey(song.path)) {
                                    result.Add(song.path, song);
                                }
                            }

                            break;

                        default:
                            if (!result.ContainsKey(song.path)) {
                                if (!string.IsNullOrEmpty(song.genre) && song.genre.ToUpper().Contains(tagName)) {
                                    if (!result.ContainsKey(song.path)) {
                                        result.Add(song.path, song);
                                    }
                                } else if (song.tags != null && song.tags.ToUpper().Contains(tagName)) {
                                    if (!result.ContainsKey(song.path)) {
                                        result.Add(song.path, song);
                                    }
                                }
                            }

                            break;
                    }
                }

                break;
        }

        if (_hideSongIds != null && _hideSongIds.Count > 0) {
            Dictionary<string, Song> result2 = new Dictionary<string, Song>();

            foreach (var song in result) {
                if (song.Value.IsHideSong()) {
                    continue;
                }

                if (!result2.ContainsKey(song.Key)) {
                    result2.Add(song.Key, song.Value);
                }
            }

            return result2;
        } else {
            return result;
        }
    }

    private Dictionary<string, Song> GetAllSongs() {
        Dictionary<string, Song> result = new Dictionary<string, Song>(); //path,Song
        List<string> excludeTags = (remoteConfig.Song_Tags_Exclude).StringToList();

        if (excludeTags.Count > 0 || (_hideSongIds != null && _hideSongIds.Count > 0)) {
            List<Song> values = GetSongsAsList();
            foreach (Song song in values) {
                if (excludeTags.Count > 0 && song.IsContainTags(excludeTags)) {
                    continue;
                }

                if (song.IsHideSong()) {
                    continue;
                }

                if (!result.ContainsKey(song.path)) {
                    result.Add(song.path, song);
                }
            }

            return result;
        } else {
            return _songs;
        }
    }

    public void LoadSongLocal() {
        if (userLocalSongs.Count != 0) {
            return;
        }

        var rawList = CSVReader.GetConfig(FileHelper.LoadFile(FileHelper.LocalPath("songlocal.csv")));
        foreach (Song it in rawList.Values) {
            if (!it.path.Contains(FILE_EXT.MP3)) {
                try {
                    it.path = Util.Base64Decode(it.path);
                    it.name = Util.Base64Decode(it.name);
                } catch {
                    //Debug.Log (ex.ToString ());
                }

                it.type = SONGTYPE.OPEN.ToString();
                it.storage = SONG_STORAGE.LOCAL;
                if (it.bmp < 50) {
                    it.bmp = 120;
                }

                if (!IsOfficial(it.path)) {
                    userLocalSongs.Add(it.path, it);
                }
            }
        }
    }

    public void AddSongLocal(Song song) {
        Dictionary<string, Song> newList = new Dictionary<string, Song>();
        song.storage = SONG_STORAGE.LOCAL;
        newList.Add(song.path, song);
        foreach (Song it in userLocalSongs.Values) {
            newList.Add(it.path, it);
        }

        userLocalSongs = newList;
        FileHelper.SaveFileLocalSong();
    }

    public void RemoveSongLocal(string key) {
        if (userLocalSongs.ContainsKey(key)) {
            userLocalSongs[key].DeleteCacheData();
            userLocalSongs.Remove(key);
            FileHelper.SaveFileLocalSong();
        }
    }

    public bool IsLocalSong(string songpath) {
        return userLocalSongs.ContainsKey(songpath) || songpath.Contains("localMP3");
    }

    public bool IsOfficial(string songpath) {
        return _songs.ContainsKey(songpath);
    }

    public void ParseContentToolSongData(Dictionary<string, Song> contentSongs) {
        _songs.Clear();

        foreach (string path in contentSongs.Keys) {
            Song contentSong = contentSongs[path];
            contentSong.type = "OPEN";
            AddSong(contentSong.path, contentSong);
        }

        Debug.Log($"[ParseContentToolSongData] songs.Count {GetSongsCount()}");
    }

    public List<string> GetAllArtistFromSongConfig(bool isOrderByAlphabet) {
        List<string> listArtists = new List<string>();

        foreach (Song s in GetSongsAsList()) {
            if (!string.IsNullOrEmpty(s.artist)) {
                string artist = s.artist.Trim();

                if (!listArtists.Contains(artist)) {
                    listArtists.Add(artist);
                }
            }
        }

        if (isOrderByAlphabet) {
            listArtists.Sort((x, y) => String.Compare(x, y, StringComparison.Ordinal));
        }

        return listArtists;
    }

    public List<string> GetAllArtistFromRemote() {
        List<string> data = new List<string>();

        if (SongCards.instance == null) {
            return data;
        }

        List<Inwave.Artist> dataArtist = SongCards.instance.GetDataArtists();

        if (dataArtist != null) {
            foreach (Inwave.Artist artist in dataArtist) {
                if (!data.Contains(artist.name)) {
                    data.Add(artist.name);
                }
            }
        }

        return data;
    }

    public List<Song> GetSongsOfArtist(string artistName) {
        List<Song> songsOfArtist = new List<Song>();

        Dictionary<string, Song> songList = GetByTag("ALL");
        if (songList != null) {
            int ordering = 0;

            foreach (Song song in songList.Values) {
                if (song.artist != null && song.artist.Contains(artistName)) {
                    song.ordering = ++ordering;
                    songsOfArtist.Add(song);
                }
            }
        }

        return songsOfArtist;
    }

    /// <summary>
    /// GetSongsByIDs
    /// </summary>
    /// <param name="songIds"></param>
    /// <param name="isLockOnly"></param>
    /// <returns></returns>
    public List<Song> GetSongsByIDs(List<string> songIds, bool isLockOnly = false) {
        List<Song> list = new List<Song>();
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (string.IsNullOrEmpty(song.acm_id_v3) || !songIds.Contains(song.acm_id_v3)) {
                continue;
            }

            if (!isLockOnly) {
                list.Add(song);
            } else if (song.savedType != SONGTYPE.OPEN && !song.isSongOfDay) {
                list.Add(song);
            }
        }

        return list;
    }

    /// <summary>
    /// GetSongsByIDs
    /// </summary>
    /// <param name="songIds"></param>
    /// <param name="isLockOnly"></param>
    /// <returns></returns>
    public List<Song> GetSongsByIDsKeepOrder(List<string> songIds, bool isLockOnly = false) {
        List<Song> list = new();
        Dictionary<string, Song> dicts = new();
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (songIds.Contains(song.acm_id_v3) && (!isLockOnly || song.savedType != SONGTYPE.OPEN)) {
                // list.Add(song);
                dicts.Add(song.acm_id_v3, song);
            }
        }

        for (var index = 0; index < songIds.Count; index++) {
            var acmId = songIds[index];
            if (dicts.ContainsKey(acmId)) {
                list.Add(dicts[acmId]);
            }
        }

        return list;
    }

    /// <summary>
    /// GetSong By ACM ID
    /// </summary>
    /// <param name="songAcmID"></param>
    /// <returns></returns>
    public Song GetSongByAcmId(string songAcmID) {
        if (string.IsNullOrEmpty(songAcmID)) {
            return null;
        }

        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (songAcmID.Equals(song.acm_id_v3)) {
                return song;
            }
        }

        return null;
    }

    public void SearchSongByAcmId(string songAcmID, Action<Song> callback) {
        var song = GetSongByAcmId(songAcmID);
        if (song != null) {
            callback?.Invoke(song);
        } else {
            ACMSDK.Instance.SearchSong_ByIds(new List<string>() { songAcmID }).ExtraFields("songParams,speed")
                .OnError(error => { callback?.Invoke(null); }).OnSuccess(response => {
                    SongData[] songDatas = response.data;
                    if (songDatas != null && songDatas.Length > 0) {
                        Song newSong = ACMSDKv4.ParseSong(songDatas[0]);
                        callback?.Invoke(newSong);
                    } else {
                        callback?.Invoke(null);
                    }
                }).Run();
        }
    }

    public List<string> GetSongAcmIDsByPaths(List<string> songPaths) {
        List<string> list = new();
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (songPaths.Contains(song.path)) {
                list.Add(song.acm_id_v3);
            }
        }

        return list;
    }

    private void SubscriptionBtnOnChange(bool isBuySuccess) {
        if (!isBuySuccess) {
            IEProcessSongTypeWhenChangeSubs(this.GetCancellationTokenOnDestroy()).Forget();
        }
    }

    private async UniTaskVoid IEProcessSongTypeWhenChangeSubs(CancellationToken cancellationToken = default) {
        try {
            while (!isInitedSongList) {
                await UniTask.Yield(cancellationToken);
            }

            await IEProcessSongType(cancellationToken);
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    /// <summary>
    /// GetFirstLockSong
    /// </summary>
    /// <param name="ignoreSongPath"></param>
    /// <returns></returns>
    public Song GetFirstLockSong(HashSet<string> ignoreSongPath = null) {
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (song.savedType == SONGTYPE.OPEN || song.savedType == SONGTYPE.EVENT) {
                continue;
            }

            if (song.IsHideSong()) {
                continue;
            }

            if (ignoreSongPath == null || !ignoreSongPath.Contains(song.path)) {
                return song;
            }
        }

        return null;
    }

    public Song GetFirstLockSongIgnoreACMID(List<string> acmIds) {
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (song.savedType != SONGTYPE.OPEN) {
                if (acmIds == null || !acmIds.Contains(song.acm_id_v3)) {
                    return song;
                }
            }
        }

        return null;
    }

    #region Song of the day

    public Song GetSongOfDay() {
        if (remoteConfig == null || !remoteConfig.SongOfDay_IsShow ||
            UserProperties.GetLoginDay() >= remoteConfig.SongOfDay_LimitDay) {
            return null;
        }

        string keySongOfDay = PlayerPrefsKey.SongOfDay + Util.GetCurrentDay();
        string pathSongOfDay = PlayerPrefs.GetString(keySongOfDay, "");
        if (string.IsNullOrEmpty(pathSongOfDay)) { //first time
            string songTag = remoteConfig.SongOfDay_SongTag;
            string songDifficulty = remoteConfig.SongOfDay_SongDifficulty;
            int indexFrom = remoteConfig.SongOfDay_IndexSongFrom;
            int indexTo = remoteConfig.SongOfDay_IndexSongTo;
            Song firstSongLock = GetRandomSongLock(songTag, songDifficulty, indexFrom, indexTo);
            if (firstSongLock == null) {
                return null;
            } else {
                PlayerPrefs.SetString(keySongOfDay, firstSongLock.path);
                return firstSongLock;
            }
        } else { //second time
            Song songByPath = GetSongOfDayByPath(pathSongOfDay);
            if (songByPath == null || songByPath.savedType == SONGTYPE.EVENT || songByPath.IsBelongToLiveEvent ||
                IsHideSong(songByPath.acm_id_v3)) {
                if (songByPath != null) {
                    songByPath.savedType = SONGTYPE.LOCK;
                    songByPath.isSongOfDay = false;
                }

                PlayerPrefs.DeleteKey(keySongOfDay);
                return GetSongOfDay();
            } else {
                return songByPath;
            }
        }
    }

    private Song GetSongOfDayByPath(string pathSongOfDay) {
        if (remoteConfig == null)
            return null;

        Dictionary<string, Song> listSong = GetByTag(remoteConfig.SongOfDay_SongTag);
        foreach (KeyValuePair<string, Song> song in listSong) {
            if (song.Key == pathSongOfDay) {
                return song.Value;
            }
        }

        return null;
    }

    private Song GetRandomSongLock(string songTag, string songDifficulty, int indexFrom, int indexTo) {
        List<string> ignoreSongPath = GetIgnoreSongPath();

        Dictionary<string, Song> listSongByTag = GetByTag(songTag);
        List<Song> qualifiedSong = FilterByDifficulty(listSongByTag, songDifficulty, ignoreSongPath, indexTo);

        indexTo = Mathf.Min(indexTo, qualifiedSong.Count - 1);
        if (indexFrom < 0 || indexFrom > indexTo || indexFrom >= qualifiedSong.Count) {
            return null;
        }

        return qualifiedSong[Random.Range(indexFrom, indexTo)];
    }

    private List<Song> FilterByDifficulty(Dictionary<string, Song> listSong, string songDifficulty,
                                          List<String> ignoreSongPath, int maxCount) {
        if (string.IsNullOrEmpty(songDifficulty)) {
            return listSong.Values.ToList();
        }

        var splitData = songDifficulty.Split(';', StringSplitOptions.RemoveEmptyEntries);
        List<DifficultyTag> difficultyTags = new List<DifficultyTag>();
        foreach (string strTag in splitData) {
            if (Enum.TryParse(strTag, true, out DifficultyTag difficultyTag)) {
                difficultyTags.Add(difficultyTag);
            }
        }

        if (difficultyTags.Count == 0) {
            //wrong config???
            return listSong.Values.ToList();
        }

        var list = new List<Song>();
        int count = 0;
        foreach (var song in listSong.Values) {
            if (song.savedType != SONGTYPE.OPEN && song.savedType != SONGTYPE.EVENT && !song.IsBelongToLiveEvent &&
                (ignoreSongPath == null || !ignoreSongPath.Contains(song.path))) {
                if (difficultyTags.Contains(song.GetDifficultyTag())) {
                    list.Add(song);
                    count++;
                    if (count > maxCount)
                        break;
                }
            }
        }

        return list;
    }

    private List<string> GetIgnoreSongPath() {
        List<string> songs = new List<string>();
        if (remoteConfig != null && remoteConfig.AdBreak_InterstitialReward && UIOverlay.instance != null) {
            Song songReward = UIOverlay.instance.GetSongReward();
            if (songReward != null) {
                songs.Add(songReward.path);
            }
        }

        return songs;
    }

    public static Dictionary<string, Song> AddSongOfDay(Dictionary<string, Song> songList, Song songOfDay, int index) {
        List<string> listKey = songList.Keys.ToList();
        if (index >= listKey.Count) {
            index = listKey.Count - 1;
        }

        if (index < 0) {
            index = 0;
        }

        listKey.Remove(songOfDay.path);
        listKey.Insert(index, songOfDay.path);

        if (!songList.ContainsKey(songOfDay.path)) {
            songList.Add(songOfDay.path, songOfDay);
        }

        Dictionary<string, Song> newSongList = new Dictionary<string, Song>();
        foreach (string pathSong in listKey) {
            newSongList.Add(pathSong, songList[pathSong]);
        }

        return newSongList;
    }

    public static void AddSongOfDay(ref List<Song> songList, Song songOfDay, int index) {
        songList.Remove(songOfDay);
        songList.Insert(index, songOfDay);
    }

    #endregion //Song of the day

    public List<string> ExcludeMainSong(List<string> favoriteList) {
        List<string> data = new List<string>();

        //Exclude song in main song list
        List<string> dataExclude = new List<string>();
        List<Song> songsAsList = GetSongsAsList();
        foreach (var song in songsAsList) {
            if (favoriteList.Contains(song.acm_id_v3)) {
                dataExclude.Add(song.acm_id_v3);
            }
        }

        foreach (string songAcmID in favoriteList) {
            if (!dataExclude.Contains(songAcmID)) {
                data.Add(songAcmID);
            }
        }

        return data;
    }

    #region Check license

    public static Action<bool, HashSet<string>> OnSongListChanged;

    private List<Song> _songsCheckLicense = new List<Song>();
    private bool       _isCheckingLicense;

    // Flag to prevent concurrent modifications during achievement processing
    public static bool IsProcessingAchievements { get; set; } = false;

    public void AddSongCheckLicense(Song song) {
        if (!_songsCheckLicense.Contains(song)) {
            _songsCheckLicense.Add(song);
        }
    }

    private bool CanCheckLicense() {
        if (_songsCheckLicense == null || _songsCheckLicense.Count == 0 || _isCheckingLicense) {
            return false;
        }

        // Prevent license checking during achievement processing to avoid concurrent modification
        if (IsProcessingAchievements) {
            return false;
        }

        if (GameController.CheckInstanced() && GameController.instance.game == GameStatus.LIVE) {
            return false;
        }

        if (!ACMSDKv4.IsInitialized()) {
            return false;
        }

        return true;
    }

    private void CheckingLicense() {
        _isCheckingLicense = true;
        List<Song> checkSongs = GetSong(_songsCheckLicense, AcmApiLimitSongCount);
        List<string> songIDs = GetSongIDs(checkSongs);
        if (ACMSDKv4.IsInitialized()) {
            ACMSDK.Instance.CheckLicenseByIds(songIDs,
                error => { DOVirtual.DelayedCall(0.1f, SetDoneCheckingLicense); }, response => {
                    List<string> validSongIds = response?.Data?.ToList();
                    ProcessValidSongs(validSongIds, checkSongs, this.GetCancellationTokenOnDestroy()).Forget();
                });
        }
    }

    private void SetDoneCheckingLicense() {
        _isCheckingLicense = false;
    }

    private async UniTaskVoid ProcessValidSongs(List<string> validSongIds, List<Song> checkSongs, CancellationToken cancellationToken = default) {
        try {
            while (IsProcessingAchievements) {
                await UniTask.Yield(cancellationToken);
            }

            validSongIds ??= new List<string>();
            HashSet<string> removedSongIds = new();

            foreach (Song checkSong in checkSongs) {
                checkSong.isCheckedLicense = true;
                if (!validSongIds.Contains(checkSong.acm_id_v3)) { // UNLICENSED
                    RemoveSong(checkSong.path);

                    removedSongIds.Add(checkSong.acm_id_v3);
                    if (remoteConfig.ACMv4_FireLicenseException) {
                        AnalyticHelper.LogEvent("SongsUnlicensed", new Dictionary<string, object> {
                            { "ID", checkSong.acm_id_v3 },
                            { "NAME", checkSong.name }
                        });
                    }
                }
            }

            if (removedSongIds.Count != 0) {
                SaveUnlicensedSong(removedSongIds);
                OnSongListChanged?.Invoke(true, removedSongIds);
            }

            await UniTask.Delay(TimeSpan.FromSeconds(0.1f), cancellationToken: cancellationToken);

            SetDoneCheckingLicense();
        } catch (OperationCanceledException) {
            // Operation was cancelled - this is expected behavior
        }
    }

    /// <summary>
    /// Remove songs ra khỏi hidden list
    /// </summary>
    /// <param name="addSongs"></param>
    public void ClearHidedSongs(List<Song> addSongs) {
        Logger.CanLog(() => Logger.Log("ClearHidedSongs", "addSongs.Count => " + addSongs.Count));

        foreach (Song song in addSongs) {
            _hideSongIds.Remove(song.acm_id_v3);
        }
    }

    public void ClearHidedSong(string acmId) {
        _hideSongIds.Remove(acmId);
    }

    /// <param name="hideList"></param>
    /// <returns></returns>
    public List<Song> HideSongsInList(List<string> hideList) {
        Logger.CanLog(() => Logger.Log("RemoveSongs", "acmIdSongs.Count => " + hideList.Count));

        // remove các bài hát từ songs list được unlock bởi user
        List<Song> songsByIDs = GetSongsByIDs(hideList);
        for (int index = songsByIDs.Count - 1; index >= 0; index--) {
            Song song = songsByIDs[index];
            if (song.IsUnlockByUser()) {
                hideList.Remove(song.acm_id_v3);
                songsByIDs.RemoveAt(index);
            }
        }

        // remove các bài hát lấy từ ACM được unlock bởi user
        hideList.RemoveAll(UserEntry.IsUnlockedSong);

        if (hideList.Count != 0) {
            // thêm list acmID vào _hideSongIds
            AddHideSongIds(hideList);
            OnSongListChanged?.Invoke(true, _hideSongIds);
        }

        return songsByIDs;
    }

    public void RemoveSongInHiddenList(Song song) {
        if (_hideSongIds.Contains(song.acm_id_v3)) {
            _hideSongIds.Remove(song.acm_id_v3);

            if (Util.IsHomeScene() && !UserProgressionController.EnableFeature) {
                SongList.instance.SongScroller.AddSongToTop(song);
            }
        }
    }

    [ShowInInspector] private readonly HashSet<string> _hideSongIds = new();

    private void AddHideSongIds(List<string> removedSongIds) {
        _hideSongIds.AddRange(removedSongIds);
    }

    public bool IsHideSong(string songId) {
        if (string.IsNullOrEmpty(songId)) {
            return false;
        }

        return _hideSongIds != null && _hideSongIds.Contains(songId);
    }

    private List<string> GetSongIDs(List<Song> listSongs) {
        List<string> songIDs = new List<string>();

        foreach (Song song in listSongs) {
            if (string.IsNullOrEmpty(song.acm_id_v3)) {
                Logger.CanLog(() => Logger.LogWarning(
                    $"[GetSongIDs] song.name => {song.name} => acm_id_v3 is NULL! => song.ToString() => {song}"));
            } else {
                songIDs.Add(song.acm_id_v3);
            }
        }

        return songIDs;
    }

    public Song GetSongByPath(string path) {
        if (string.IsNullOrEmpty(path)) {
            return null;
        }

        return _songs.TryGetValue(path, out Song songByPath) ? songByPath : null;
    }

    private List<Song> GetSong(List<Song> mainSong, int count) {
        List<Song> checkSongs = new();

        int number = Mathf.Min(count, mainSong.Count);
        for (int i = 0; i < number; i++) {
            Song song = mainSong[0];
            checkSongs.Add(song);
            mainSong.Remove(song);
        }

        return checkSongs;
    }

    #region Unlicensed Songs

    private static List<string> _unlicensedSongs = new List<string>();

    private static void SaveUnlicensedSong(ICollection<string> songs) {
        List<string> unlicensedSongs = GetUnlicensedSongs();
        foreach (var item in songs) {
            if (!unlicensedSongs.Contains(item)) {
                unlicensedSongs.Add(item);
            }
        }

        string update = string.Join(";", unlicensedSongs);
        PlayerPrefs.SetString($"SongUnlicensed_{Util.GetCurrentDay()}", update);
    }

    private static List<string> GetUnlicensedSongs() {
        if (_unlicensedSongs.Count != 0) {
            return _unlicensedSongs;
        }

        _unlicensedSongs =
            (PlayerPrefs.GetString($"SongUnlicensed_{Util.GetCurrentDay()}", string.Empty)).StringToList();
        return _unlicensedSongs;
    }

    #endregion

    #endregion

    public List<Song> GetSongs(string listAcmID, bool isLock = true) {
        if (string.IsNullOrEmpty(listAcmID)) {
            return null;
        }

        List<Song> lsSongs = new List<Song>();
        var songIDs = listAcmID.Split(';', ',');
        int count = songIDs.Length;
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            string acmID = song.acm_id_v3;
            if (!string.IsNullOrEmpty(acmID) && songIDs.Contains(acmID)) {
                if (!isLock || (isLock && song.savedType != SONGTYPE.OPEN)) {
                    lsSongs.Add(song);
                    count--;
                    if (count <= 0) {
                        return lsSongs;
                    }
                }
            }
        }

        return lsSongs;
    }

    public static List<string> GetLimitSongs(List<string> songsID, int limit) {
        while (songsID.Count > limit) {
            songsID.RemoveAt(songsID.Count - 1);
        }

        return songsID;
    }

    public List<Song> GetSongsByGenre(string genreType) {
        genreType = genreType == null ? "" : genreType.ToUpper();
        Dictionary<string, Song> songList = GetByTag("ALL");
        if (songList == null) {
            return null;
        }

        int ordering = 0;
        List<Song> data = new List<Song>();
        foreach (Song song in songList.Values) {
            bool isCheckGenre = IsCheckGenre(genreType, song);
            if (isCheckGenre) {
                ordering++;
                song.ordering = ordering;
                data.Add(song);
            }
        }

        return data;
    }

    private bool IsCheckGenre(string genreType, Song song) {
        string[] genres = song.GetGenres();
        if (genres == null) {
            return false;
        }

        List<string> genresOfSong = genres.ToList();
        return genresOfSong.Contains(genreType);
    }

    public Song GetSongUnlockedNotPlay() {
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (song.IsOpened() && !song.IsPlayed()) {
                return song;
            }
        }

        return songsAsList.FirstOrDefault(song => song.IsOpened());
    }

    public Song GetSongToDoMission() {
        Song temp = null;
        int priority = 0;
        byte star;
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (song.IsOpened()) {
                if (!song.IsPlayed()) {
                    temp = song;
                    priority = 4;
                    break;
                }

                star = song.Star;
                if (star == 0) {
                    temp = song;
                    priority = 3;
                } else if (star < remoteConfig.EndlessMode_UnlockByStar) {
                    temp = song;
                    priority = 2;
                } else {
                    temp = song;
                    priority = 1;
                }
            }
        }

        if (priority != 0 && temp != null) {
            return temp;
        }

        return songsAsList.FirstOrDefault(song => song.IsOpened());
    }

    public Song GetEndlessSong() {
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (song.IsOpened() && Configuration.IsEndlessModeEnable(song.path)) {
                return song;
            }
        }

        return songsAsList.FirstOrDefault(song => song.IsOpened());
    }

    public Song GetFirstOpenSong() {
        foreach (var song in _songs) {
            if (song.Value.IsOpened())
                return song.Value;
        }

        return _songs.First().Value;
    }

    public Song GetFirstSong() {
        return _songs.First().Value;
    }

    public Dictionary<string, Song> GetSongsByCategory(string category) {
        category = category == null ? "ALL" : category.ToUpper();
        Dictionary<string, Song> songList = GetByTag("ALL");
        if (songList == null) {
            return null;
        }

        if (category == "ALL") {
            return songList;
        }

        int ordering = 0;

        Dictionary<string, Song> data = new Dictionary<string, Song>();
        foreach (Song song in songList.Values) {
            bool isCheckGenre = IsCheckCategory(category, song);
            if (isCheckGenre) {
                ordering++;
                song.ordering = ordering;
                data.TryAdd(song.path, song);
            }
        }

        return data;
    }

    private bool IsCheckCategory(string category, Song song) {
        string[] genres = song.GetCategories();
        if (genres == null) {
            return false;
        }

        List<string> genresOfSong = genres.ToList();
        return genresOfSong.Contains(category);
    }

    /// <summary>
    /// Kiểm tra đã unlock all song hay chưa
    /// </summary>
    /// <returns>true nếu đã unlock all song. False nếu còn bài hát đang lock</returns>
    public bool IsUnlockAllSong() {
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (song.savedType != SONGTYPE.OPEN) {
                return false;
            }
        }

        return true;
    }

    public Song GetTutorialSongByACMID(string name, string artist) {
        foreach (var pair in tutorialLocalSong) {
            if (pair.Value.name.Equals(name) && pair.Value.artist.Equals(artist))
                return pair.Value;
        }

        return null;
    }

    private Dictionary<string, Song> LoadTutorialLocalSongs() {
        string data = defaultTutorialLocalConfig.text;
        Dictionary<string, Song> tmpSongs = ItemContainer.GetSongs(data);
        foreach (Song localTutorialSong in tmpSongs.Select(song => song.Value)) {
            localTutorialSong.isTutorialSong = true;
            localTutorialSong.midiContentType = MidiContentType.MUSIC_EXPERIENCE_DESIGN;
        }

        return tmpSongs;
    }

    public void AddTutorialLocalSong(Dictionary<string, Song> songDict) {
        foreach (KeyValuePair<string, Song> item in songDict) {
            tutorialLocalSong[item.Key] = item.Value; // gán bằng giá trị mới
        }
    }

    public bool IsLocalTutorialSong(string path) {
        return tutorialLocalSong.ContainsKey(path) &&
               (tutorialRemoteSong == null || !tutorialRemoteSong.ContainsKey(path));
    }

    public bool CheckSongGenre(string itemPath, string genre) {
        if (IsOfficial(itemPath)) {
            return _songs[itemPath].IsContainGenre(genre);
        }

        return false;
    }

    #region RewardSong

    readonly HashSet<string> _cachedRewardSongPath = new HashSet<string>();

    public Song GetRewardSong() {
        //ignore SongPath
        Song songReward = UIOverlay.instance.GetSongReward();
        if (songReward != null && !_cachedRewardSongPath.Contains(songReward.path)) {
            _cachedRewardSongPath.Add(songReward.path);
        }

        Song songOfDay = GetSongOfDay();
        if (songOfDay != null && !_cachedRewardSongPath.Contains(songOfDay.path)) {
            _cachedRewardSongPath.Add(songOfDay.path);
        }

        Song firstLockSong = GetFirstLockSong(_cachedRewardSongPath);
        _cachedRewardSongPath.Add(firstLockSong.path);
        return firstLockSong;
    }

    #endregion

    #region User Progression

    public List<Song> GetSongsBySongPackData(List<SongPackData> songPackDatas) {
        List<Song> validSong = new List<Song>();
        foreach (var songPackData in songPackDatas) {
            var song = GetSongByPath(songPackData.SongPath);
            if (song != null) {
                validSong.Add(song);
            }
        }

        return validSong;
    }

    public List<ArtistDataWrapper> GetArtistsBySongPackData(List<SongPackData> songPackDatas) {
        HashSet<ArtistDataWrapper> artists = new();
        foreach (var songPackData in songPackDatas) {
            var song = GetSongByPath(songPackData.SongPath);
            if (song != null && !string.IsNullOrEmpty(song.artist)) {
                artists.Add(new ArtistDataWrapper(song.artist));
            }
        }

        return artists.ToList();
    }

    public void UserProgression_UnlockSongs(List<SongPackData> unlockSongs, bool isDefaultList) {
        List<string> unlockSongStr = new List<string>();
        foreach (var songPackData in unlockSongs) {
            var song = GetSongByPath(songPackData.SongPath);
            if (song != null) {
                song.UnlockByUserProgression(songPackData);
                if (isDefaultList) {
                    unlockSongStr.Add(song.path);
                }
            }
        }

        if (isDefaultList) {
            foreach (var song in _songs) {
                if (!unlockSongStr.Contains(song.Key)) {
                    song.Value.LockByUserProgression();
                }
            }

        }

        if (Util.IsHomeScene()) {
            OnSongListChanged?.Invoke(true, null);
        }
    }

    public void UserProgression_UnlockSongs(List<SongPackData> unlockSongs, List<SongPackData> allSongs,
                                            UserProgressionController.UnlockAllConfig _config, bool isDefaultList) {
        List<string> unlockSongStr = new List<string>();
        List<string> lockSongStr = new List<string>();
        foreach (var songPackData in allSongs) {
            var song = GetSongByPath(songPackData.SongPath);
            if (song != null) {
                if (unlockSongs.Contains(songPackData)) {
                    song.UnlockByUserProgression(songPackData);
                    if (isDefaultList) {
                        unlockSongStr.Add(song.path);
                    }
                } else {
                    lockSongStr.Add(song.path);
                }
            }
        }

        if (isDefaultList) {

            foreach (var song in _songs) {
                if (!unlockSongStr.Contains(song.Key)) {
                    if (remoteConfig.UserProgression_ShowDiscoverBeforeUnlockAll && !lockSongStr.Contains(song.Key)) {
                        song.Value.UnLockByUserProgression(_config.type, _config.value);
                    } else {
                        song.Value.LockByUserProgression();
                    }
                }
            }

        }

        OnSongListChanged?.Invoke(true, null);
    }

    public void UserProgression_UnlockAllSongs(List<SongPackData> allSongInPack,
                                               UserProgressionController.UnlockAllConfig _config, bool isDefault) {
        // lọc ra các song có trong list songpack
        HashSet<string> validSongInPacks = new HashSet<string>();
        foreach (var songPackData in allSongInPack) {
            var song = GetSongByPath(songPackData.SongPath);
            if (song != null) {
                if (isDefault) {
                    song.UnlockByUserProgression(songPackData);
                }

                validSongInPacks.Add(songPackData.SongPath);
            }
        }

        // xử lý với các song còn lại ko có trong list songpack
        foreach (var song in _songs) {
            // với hashset độ phức tạp của Contains là O(1), thay vì O(n) khi dùng List
            if (!validSongInPacks.Contains(song.Key)) {
                song.Value.UnLockByUserProgression(_config.type, _config.value);
            }
        }

        OnSongListChanged?.Invoke(true, null);
    }

    #endregion

    public int GetSongsCount() {
        return _songs?.Count ?? 0;
    }

    private readonly List<Song> _cachedSongList = new List<Song>();
    private          bool       _isDirty        = true;

    public List<Song> GetSongsAsList() {
        if (_isDirty && _songs != null && _songs.Count > 0) {
            _cachedSongList.Clear();
            _cachedSongList.AddRange(_songs.Values);
            _isDirty = false;
        }

        return _cachedSongList;
    }

    public void AddSong(string songPath, Song song) {
        if (string.IsNullOrEmpty(songPath) || _songs.ContainsKey(songPath)) {
            return;

        }

        _songs.Add(songPath, song);
        _isDirty = true; // Mark cache as dirty
    }

    public void RemoveSong(string path) {
        _songs.Remove(path);
        _isDirty = true;
    }

    public bool TryGetSong(string path, out Song currentSong) {
        if (_songs != null) {
            return _songs.TryGetValue(path, out currentSong);
        }

        currentSong = null;
        return false;
    }

    public IEnumerable<string> GetSongsKeys() {
        return _songs.Keys;
    }

    public List<string> GetLockSongs(int max) {
        List<string> songs = new List<string>(capacity: max);
        int count = 0;
        List<Song> songsAsList = GetSongsAsList();
        foreach (Song song in songsAsList) {
            if (song.savedType is SONGTYPE.OPEN or SONGTYPE.EVENT) {
                continue;
            }

            if (song.IsHideSong()) {
                continue;
            }

            songs.Add(song.acm_id_v3);
            count++;
            if (count >= max) {
                break;
            }
        }

        return songs;
    }
}