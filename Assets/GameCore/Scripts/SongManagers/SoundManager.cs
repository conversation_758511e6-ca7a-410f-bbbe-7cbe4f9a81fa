using System;
using System.Collections.Generic;
using UnityEngine;
using TilesHop.Sound;
using System.Collections;
public partial class SoundManager : Singleton<SoundManager> {
    // Use this for initialization
    private Dictionary<SFXType, string> dictSFXSounds;
    private int                         channel    = 0;
    private int                         maxChannel = 4;

    private RemoteConfig remoteConfig => RemoteConfigBase.instance;
    
    private void Start() {
        dictSFXSounds = new Dictionary<SFXType, string>() {
            {SFXType.gameButton, "click"},
            {SFXType.gameCompleted, "gameCompleted"},
            {SFXType.star, "star"},
            {SFXType.die, "die"},
            {SFXType.select, "select"},
            {SFXType.coins, "coins"},
            {SFXType.impactExtend, "impactExtend"},
            {SFXType.buy, "buy"},
            {SFXType.checkPoint, "checkPoint"},
            {SFXType.diamond, "diamond"},

            //TH-4187
            {SFXType.gameplay_strong_note, "GamPlay_StrongNote" },
            {SFXType.gameplay_moodchange, "GamePlay_MoodChange" },
            {SFXType.scroll_tick, "Slide_tick"},
            {SFXType.list_menu_open, "ListMenu_open"},
            {SFXType.list_menu_close, "ListMenu_close"},
            {SFXType.gem_purchased, "Shop_BuyDiamon_success" },
            {SFXType.shop_ball_tap, "Shop_tap" },
            {SFXType.shop_ball_purchased, "Shop_Button_BuyGem"},
            {SFXType.free_gift_collect, "FreeGift_collect"},
            {SFXType.shop_select_char, "Shop_Select_char"},
            {SFXType.song_card_preview, "PlayPreview_stop"},

            //TH-4577
            {SFXType.booster_turnoff, "SFX_Boot_TurnOff"},
            {SFXType.booster_shield_base_active, "SFX_Boot_Shield_Base_Active" },
            {SFXType.booster_tiletidy_active, "SFX_Boot_TileTidy_Active" },
            {SFXType.booster_magnet_active, "SFX_Boot_Magnet_Active" },

            {SFXType.thunder_quest_tutorial_pop, "SFX_Tutorial_Pop" },
            {SFXType.popup_slide, "Page_slide"},
            {SFXType.tooltip_reward, "SFX_Pop_InfoReward" }
        };

        Util.WaitRemoteConfigDone(UpdateAssets, true);

        AudioDeviceDetector.Instance.RegisterAudioDeviceEvents();
    }

    private void UpdateAssets() {
        if (remoteConfig.PlayNewSFX_Enable) {
            dictSFXSounds.Add(SFXType.start_song, "start_song");
            dictSFXSounds.Add(SFXType.ingame_star, "ingame_star");
            dictSFXSounds.Add(SFXType.revive_bgm, "revive_bgm");
            dictSFXSounds.Add(SFXType.revive_countdown, "revive_countdown");
            dictSFXSounds.Add(SFXType.revived, "revived");
            dictSFXSounds.Add(SFXType.result_star, "result_star");
            dictSFXSounds.Add(SFXType.result_gem, "result_gem");
            dictSFXSounds.Add(SFXType.result_bgm, "result_bgm");
            dictSFXSounds.Add(SFXType.popup_open, "popup_open");
            dictSFXSounds.Add(SFXType.popup_close, "popup_close");
            dictSFXSounds.Add(SFXType.button_tap, "button_tap");
            dictSFXSounds.Add(SFXType.gem_count, "gem_count");
            dictSFXSounds.Add(SFXType.open_shop, "open_shop");
            dictSFXSounds.Add(SFXType.character_select, "character_select");
        }

        if (remoteConfig.PlayNewSFX_UP_Enable) { //TH-3985
            dictSFXSounds.Add(SFXType.result_tile_collect, "anim_resultscrn_collect_2024SP");
            dictSFXSounds.Add(SFXType.result_reward_popup, "btn_resultscrn_rewardpopup_2024SP");
            dictSFXSounds.Add(SFXType.home_tile_collect, "anim_mainscrn_tilecollect_2024SP");
            dictSFXSounds.Add(SFXType.home_pack_unlock, "anim_mainscrn_songpackunlocked_2024SP");
            dictSFXSounds.Add(SFXType.home_onboarding, "anim_mainscrn_onboardingpopup_2024SP");
            dictSFXSounds.Add(SFXType.ingame_diamond_collect, "anim_actionphrase_diamondcollect_2024SP");
        }

        if (remoteConfig.StarsJourney_IsEnable) { //TH-4486
            dictSFXSounds.Add(SFXType.result_disk_popin, "Result_Disk_PopIn");
            dictSFXSounds.Add(SFXType.result_small_star_popin, "Result_SmallStar_PopIn");
            dictSFXSounds.Add(SFXType.home_sj_star_flying_to, "Home_SJ_StarFlyingTo");
            dictSFXSounds.Add(SFXType.home_sj_new_level, "Home_SJ_NewLevel");
            dictSFXSounds.Add(SFXType.sj_feature_progress_line_slide, "SJFeature_ProgressLine_Slide");
            dictSFXSounds.Add(SFXType.sj_feature_unlock_reward_extra, "SJFeature_UnlockRewadExtra");
            dictSFXSounds.Add(SFXType.home_small_star, "Home_SmallStar_Pop");
            dictSFXSounds.Add(SFXType.sj_gift_drop, "GiftBox_InDrop");
            dictSFXSounds.Add(SFXType.sj_gift_item_pop, "GiftBox_ItemPop");
            dictSFXSounds.Add(SFXType.sj_gift_open, "GiftBox_InOpen");
        }

        if (remoteConfig.GalaxyQuest_IsEnable) { //TH-4796
            dictSFXSounds.Add(SFXType.thunder_quest_counting_member, "SFX_CountingMember");
            dictSFXSounds.Add(SFXType.thunder_quest_counting_finish, "SFX_CountingMemberFinish");
            dictSFXSounds.Add(SFXType.thunder_quest_player_jump, "SFX_Jump");
            dictSFXSounds.Add(SFXType.thunder_quest_bots_jump, "SFX_GroupJump");
            dictSFXSounds.Add(SFXType.thunder_quest_thunder_impact, "SFX_Thunder_Impact");
            dictSFXSounds.Add(SFXType.thunder_quest_die, "SFX_Die");
            dictSFXSounds.Add(SFXType.thunder_quest_popup_revive, "SaveArlarm");
            dictSFXSounds.Add(SFXType.thunder_quest_popup_step_reward, "SFX_Gift_Popup");
            dictSFXSounds.Add(SFXType.thunder_quest_popup_final_reward, "SFX_Congratulations");
            dictSFXSounds.Add(SFXType.thunder_quest_revive, "SFX_Return_Button");
            dictSFXSounds.Add(SFXType.thunder_quest_revive_action, "SFX_Return");
        }

        if (remoteConfig.MysteryDoor_IsEnable) { //TH-4928
            dictSFXSounds.Add(SFXType.mystery_door_drill, "SFX_Drill");
            dictSFXSounds.Add(SFXType.mystery_door_no_drill, "SFX_Drill_error");
            dictSFXSounds.Add(SFXType.mystery_door_collect_key, "SFX_Key_FinishDiging");
            dictSFXSounds.Add(SFXType.mystery_door_active_key, "SFX_Key_ToTheHole");
            dictSFXSounds.Add(SFXType.mystery_door_open_door, "SFX_KeyFull_OpenDoor");
            dictSFXSounds.Add(SFXType.mystery_door_reward, "SFX_Congratulations_In");
            dictSFXSounds.Add(SFXType.mystery_door_close_door, "SFX_CloseDoor");
            dictSFXSounds.Add(SFXType.mystery_door_drill_ingame, "SFX_Item_Drill_Ingame");
            dictSFXSounds.Add(SFXType.mystery_door_drill_counting, "SFX_Item_Drill_Counting");
        }

        if (remoteConfig.MileStoneEvent_IsEnable) {
            dictSFXSounds.Add(SFXType.beat_sahur_eat_item, "InGame_LE3_ItemCollect_Shahur");
            dictSFXSounds.Add(SFXType.beat_sahur_popup_bg, "UI_Popup_Bg_Sahur");
            dictSFXSounds.Add(SFXType.beat_sahur_tooltip_open, "UI_LE3_PopupBox_Open");
            dictSFXSounds.Add(SFXType.beat_sahur_popup_open, "UI_beat_sahur_Popup_In");
            dictSFXSounds.Add(SFXType.beat_sahur_popup_close, "UI_beat_sahur_Popup_Close");
            dictSFXSounds.Add(SFXType.beat_sahur_popup_onboard_open, "UI_beat_sahur_Popup_onboard_in");
            dictSFXSounds.Add(SFXType.beat_sahur_btn_home, "UI_beat_sahur_btn_home");
		}
    }

    /// <summary>
    /// Only support ios for silent mode
    /// </summary>
    public void ReloadAsset() {
        if (Application.platform == RuntimePlatform.IPhonePlayer) {
            AudioPlayer.ClearAudio();
        }
    }

    private void PlaySingleSound(SFXType soundName, float soundVolume) {
        if (dictSFXSounds != null && dictSFXSounds.ContainsKey(soundName)) {
            PlaySingleSound(dictSFXSounds[soundName], soundVolume);
        } else {
            Logger.EditorLogError("dictSFXSounds don't ContainsKey " + soundName);
        }
    }

    private void PlaySingleSound(string soundName, float soundVolume = 0.2f, bool loop = false) {
        if (Configuration.instance == null || !Configuration.instance.SoundIsOn()) {
            return;
        }

        StartCoroutine(AudioPlayer.Play(channel, soundName, soundVolume, loop));
        channel++;
        if (channel == maxChannel) {
            channel = 0;
        }
    }

    private static void StopSfx(SFXType key) {
        if (!isInstanced)
            return;

        if (instance.dictSFXSounds.ContainsKey(key)) {
            AudioPlayer.Stop(instance.dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayGameButton(float volume = 0.5f) {
        try {
            if (!isInstanced) {
                return;
            }

            SFXType key = instance.remoteConfig.PlayNewSFX_Enable ? SFXType.button_tap : SFXType.gameButton;
            instance.PlaySingleSound(key, volume);

        } catch (Exception e) {
            CustomException.Fire("[PlayGameButton]", e.Message);
        }
    }

    public void PlaySelect() {
        PlaySingleSound(dictSFXSounds[SFXType.select]);
    }

    public void PlayGameCompleted() {
        PlaySingleSound(dictSFXSounds[SFXType.gameCompleted], 0.3f);
    }

    public void PlayDie() {
        PlaySingleSound(dictSFXSounds[SFXType.die]);
    }

    public void PlayStar(float volume = 0.5f) {
        var key = remoteConfig.PlayNewSFX_Enable ? SFXType.result_star : SFXType.star;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayCheckPoint(float volume = 0.05f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (instance.remoteConfig.PlayNewSFX_Enable)
            return;

        instance.PlaySingleSound(instance.dictSFXSounds[SFXType.checkPoint], volume);
    }

    public void PlayCollect() {
        PlaySingleSound(dictSFXSounds[SFXType.coins]);
    }

    public static void PlayCoins(float volume = 0.35f) {
        if (!isInstanced) {
            return;
        }

        instance.PlaySingleSound(instance.dictSFXSounds[SFXType.coins], volume);
    }

    public void PlayImpactExtend() {
        PlaySingleSound(dictSFXSounds[SFXType.impactExtend], 0.02f);
    }

    public static void PlayBuy() {
        if (!isInstanced) {
            return;
        }

        instance.PlaySingleSound(instance.dictSFXSounds[SFXType.buy]);
    }

    public static void PlayDiamond() {
        if (!isInstanced)
            return;

        var key = SFXType.diamond;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    private void OnApplicationPause(bool pauseStatus) {
        if (pauseStatus) {
            AudioPlayer.Pause();
        } else {
            AudioPlayer.Resume();
        }
    }

    //Play New SFX
    public static void PlayStartSong(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.start_song;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayIngame_Star(float volume = 1f) {
        if (instance == null) {
            return;
        }
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.ingame_star;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayRevive_BG(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revive_bgm;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
            StopSfx(SFXType.start_song);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void StopRevive_BG() {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revive_bgm;
        StopSfx(key);
    }

    public void PlayRevive_CountDown(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revive_countdown;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlayRevived(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.revived;
        TryPlaySFX(key, volume);
    }

    public static void PlayResultBG(float volume = 0.7f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.result_bgm;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void StopResultBG() {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null)
            return;
        if (!instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.result_bgm;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            AudioPlayer.Stop(instance.dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void PlayResultGEM(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.result_gem;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlaySFX_PopupOpen(float volume = 1f) {
        try {
            if (!isInstanced || !instance.remoteConfig.PlayNewSFX_Enable) {
                return;
            }

            SFXType key = SFXType.popup_open;
            instance.PlaySingleSound(key, volume);

        } catch (Exception e) {
            CustomException.Fire("[PlaySFX_PopupOpen]", e.Message);
        }
    }

    public static void PlaySFX_PopupClose(float volume = 1f) {
        if (!isInstanced || !instance.remoteConfig.PlayNewSFX_Enable) {
            return;
        }

        SFXType key = SFXType.popup_close;
        instance.PlaySingleSound(key, volume);
    }

    public void PlaySFX_GemCount(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.gem_count;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume, true);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void StopSFX_GemCount() {
        if (remoteConfig == null)
            return;
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.gem_count;
        if (dictSFXSounds.ContainsKey(key)) {
            AudioPlayer.Stop(dictSFXSounds[key]);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void PlaySFX_OpenShop(float volume = 0.5f) {
        if (!remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.open_shop;
        if (dictSFXSounds.ContainsKey(key)) {
            PlaySingleSound(dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public static void PlaySFX_SelectCharacter(float volume = 0.5f) {
        if (!isInstanced)
            return;
        if (instance.remoteConfig == null || !instance.remoteConfig.PlayNewSFX_Enable)
            return;

        var key = SFXType.character_select;
        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        } else {
            Logger.LogError("dictSFXSounds don't ContainsKey " + key);
        }
    }

    public void Pause() {
        AudioPlayer.Pause();
    }

    public static void StopAll() {
        if (instance == null) {
            return;
        }

        for (int chanel = 0; chanel < instance.maxChannel; chanel++) {
            if (AudioPlayer.IsPlaying(chanel)) {
                AudioPlayer.Stop(chanel);
            }
        }
    }

    private static void TryPlaySFX(SFXType key, float volume = 0.5f) {
        if (!isInstanced) {
            return;
        }

        if (instance.dictSFXSounds.ContainsKey(key)) {
            instance.PlaySingleSound(instance.dictSFXSounds[key], volume);
        }
    }

    public static void PlayResultDiamondCollect(float volume = 0.5f) {
        TryPlaySFX(SFXType.result_tile_collect, volume);
    }

    public static void PlayResultRewardPopup(float volume = 0.5f) {
        TryPlaySFX(SFXType.result_reward_popup, volume);
    }

    public static void PlayHomeTileCollect(float volume = 0.5f) {
        TryPlaySFX(SFXType.home_tile_collect, volume);
    }

    public static void PlayHomeSongPackUnlock(float volume = 0.5f) {
        TryPlaySFX(SFXType.home_pack_unlock, volume);
    }

    public static void PlayHomeOnboarding(float volume = 0.5f) {
        TryPlaySFX(SFXType.home_onboarding, volume);
    }

    public static void PlayEatSingleDiamond() {
        if (RemoteConfigBase.instance.PlayNewSFX_UP_Enable) {
            TryPlaySFX(SFXType.ingame_diamond_collect);
            return;
        }

        if (RemoteConfig.instance.PlayNewSFX_Enable) {
            return;
        }

        PlayCoins(0.02f);
    }

    public static void PlayOpenTooltipReward(float volume = 1f) {
        TryPlaySFX(SFXType.tooltip_reward, volume);
    }

    #region SJ

    public static void PlayResultDiskPopin(float volume = 0.5f) {
        TryPlaySFX(SFXType.result_disk_popin, volume);
    }

    public static void PlayResultSmallStarPopin(float volume = 0.5f) {
        TryPlaySFX(SFXType.result_small_star_popin, volume);
    }

    public static void PlayHomeSJStarFlyingTo(float volume = 0.5f) {
        TryPlaySFX(SFXType.home_sj_star_flying_to, volume);
    }

    public static void PlayHomeSmallStar(float volume = 0.5f) {
        TryPlaySFX(SFXType.home_small_star, volume);
    }

    public static void PlayHomeSJNewLevel(float volume = 0.5f) {
        TryPlaySFX(SFXType.home_sj_new_level, volume);
    }

    public static void PlaySJFeatureProgressLineSlide(float volume = 0.5f) {
        TryPlaySFX(SFXType.sj_feature_progress_line_slide, volume);
    }

    public static void PlaySJFeatureUnlockRewardExtra(float volume = 0.5f) {
        TryPlaySFX(SFXType.sj_feature_unlock_reward_extra, volume);
    }

    public static void PlayPopupSlide(float volume = 1) {
        TryPlaySFX(SFXType.popup_slide, volume);
    }

    public static void PlayGiftDrop(float volume = 1) {
        TryPlaySFX(SFXType.sj_gift_drop, volume);
    }

    public static void PlayGiftItemPop(float volume = 1) {
        TryPlaySFX(SFXType.sj_gift_item_pop, volume);
    }

    public static void PlayGiftOpen(float volume = 1) {
        TryPlaySFX(SFXType.sj_gift_open, volume);
    }

    #endregion

    #region New update TH-4515

    public static void PlayGameplayStrongNote(float volume = 0.5f) {
        TryPlaySFX(SFXType.gameplay_strong_note, volume);
    }

    public static void PlayGameplayMoodChange(float volume = 0.5f) {
        TryPlaySFX(SFXType.gameplay_moodchange, volume);
    }

    public static void PlayScrollTick(float volume = 1) {
        TryPlaySFX(SFXType.scroll_tick, volume);
    }

    public static void PlayOpenMission(float volume = 1) {
        TryPlaySFX(SFXType.list_menu_open, volume);
    }

    public static void PlayCloseMission(float volume = 1) {
        TryPlaySFX(SFXType.list_menu_close, volume);
    }

    public static void PlayGemPurchased(float volume = 1) {
        TryPlaySFX(SFXType.gem_purchased, volume);
    }

    public static void PlayShopBallTap(float volume = 1) {
        TryPlaySFX(SFXType.shop_ball_tap, volume);
    }

    public static void PlayBallPurchased(float volume = 1) {
        TryPlaySFX(SFXType.shop_ball_purchased, volume);
    }

    public static void PlayFreeGiftCollect(float volume = 1) {
        TryPlaySFX(SFXType.free_gift_collect, volume);
    }

    public static void PlayShopSelectChar(float volume = 1) {
        TryPlaySFX(SFXType.shop_select_char, volume);
    }

    public static void PlaySongCardPreview(float volume = 1) {
        TryPlaySFX(SFXType.song_card_preview, volume);
    }

    #endregion

    #region Booster

    public static void PlayBoosterShieldBaseActive(float volume = 1f) {
        TryPlaySFX(SFXType.booster_shield_base_active, volume);
    }

    public static void PlayBoosterTurnOff(float volume = 1f) {
        TryPlaySFX(SFXType.booster_turnoff, volume);
    }

    public static void PlayBoosterTileTidyActive(float volume = 1f) {
        TryPlaySFX(SFXType.booster_tiletidy_active, volume);
    }

    public static void PlayBoosterMagnetActive(float volume = 1f) {
        TryPlaySFX(SFXType.booster_magnet_active, volume);
    }
    #endregion

    #region Thunder Quest

    public static void PlayThunderQuestTutorialPop(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_tutorial_pop, volume);
    }

    public static void PlayThunderQuestFindCounting(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_counting_member, volume);
    }

    public static void PlayThunderQuestFindCountingFinish(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_counting_finish, volume);
    }

    public static void PlayThunderQuestPlayJump(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_player_jump, volume);
    }

    public static void PlayThunderQuestBotsJump(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_bots_jump, volume);
    }

    public static void PlayThunderQuestThunderImpact(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_thunder_impact, volume);
    }

    public static void PlayThunderQuestPlayerDie(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_die, volume);
    }

    public static void PlayThunderQuestShowPopupRevive(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_popup_revive, volume);
    }

    public static void PlayThunderQuestShowPopupStepReward(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_popup_step_reward, volume);
    }

    public static void PlayThunderQuestShowPopupFinalReward(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_popup_final_reward, volume);
    }

    public static void PlayThunderQuestRevive(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_revive, volume);
    }

    public static void PlayThunderQuestReviveAction(float volume = 1f) {
        TryPlaySFX(SFXType.thunder_quest_revive_action, volume);
    }

    #endregion

    #region Mystery Door

    public static void PlayMysteryDoorDrill(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_drill, volume);
    }

    public static void PlayMysteryDoorNoDrill(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_no_drill, volume);
    }

    public static void PlayMysteryDoorCollectKey(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_collect_key, volume);
    }

    public static void PlayMysteryDoorActiveKey(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_active_key, volume);
    }

    public static void PlayMysteryDoorOpenDoor(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_open_door, volume);
    }

    public static void PlayMysteryShopPopupReward(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_reward, volume);
    }

    public static void PlayMysteryDoorCloseDoor(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_close_door, volume);
    }

    public static void PlayMysteryDoorEatDrill(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_drill_ingame, volume);
    }

    public static void PlayMysteryDoorDrillCounting(float volume = 1f) {
        TryPlaySFX(SFXType.mystery_door_drill_counting, volume);
    }

    #endregion

    #region Endless Offer

    private static bool _isProcessingEOSound;
    public static IEnumerator IEAddEndlessOfferSound() {
        if (_isProcessingEOSound) {
            yield break;
        }

        _isProcessingEOSound = true;

        while (!isInstanced || instance.dictSFXSounds.IsNullOrEmpty())
        {
            yield return null;
        }

        instance.dictSFXSounds.Add(SFXType.endless_offer_lock_click, "SFX_Error_Unlock");
        instance.dictSFXSounds.Add(SFXType.endless_offer_cards_move, "SFX_SildeCard_Roundtine");
        instance.dictSFXSounds.Add(SFXType.endless_offer_collect_milestone, "SFX_CollectToken");
        instance.dictSFXSounds.Add(SFXType.endless_offer_progress_up, "SFX_Progress_Up");
        instance.dictSFXSounds.Add(SFXType.endless_offer_milestone_reward, "SFX_FullProgressBar_reward");
    }

    public static void PlayEndlessOfferLockClick(float volume = 1f) {
        TryPlaySFX(SFXType.endless_offer_lock_click, volume);
    }

    public static void PlayEndlessOfferCardsMove(float volume = 1f) {
        TryPlaySFX(SFXType.endless_offer_cards_move, volume);
    }

    public static void PlayEndlessOfferCollectToken(float volume = 1f) {
        TryPlaySFX(SFXType.endless_offer_collect_milestone, volume);
    }

    public static void PlayEndlessOfferProgressUp(float volume = 1f) {
        TryPlaySFX(SFXType.endless_offer_progress_up, volume);
    }

    public static void PlayEndlessOfferGetMilestoneReward(float volume = 1f) {
        TryPlaySFX(SFXType.endless_offer_milestone_reward, volume);
	}
	#endregion

	#region LE3
    public static void PlayBeatSahurEatItem(float volume = 1f) {
		TryPlaySFX(SFXType.beat_sahur_eat_item, volume);
	}

	public static void PlayBeatSahurPopupBG(float volume = 1f) {
		TryPlaySFX(SFXType.beat_sahur_popup_bg, volume);
	}

	public static void PlayBeatSahurTooltipOpen(float volume = 1f) {
		TryPlaySFX(SFXType.beat_sahur_tooltip_open, volume);
	}

    public static void PlayBeatSahurPopupOpen(float volume = 1f) {
		TryPlaySFX(SFXType.beat_sahur_popup_open, volume);
	}

    public static void PlayBeatSahurPopupClose(float volume = 1f) {
		TryPlaySFX(SFXType.beat_sahur_popup_close, volume);
	}

    public static void PlayBeatSahurPopupOnboardOpen(float volume = 1f) {
		TryPlaySFX(SFXType.beat_sahur_popup_onboard_open, volume);
	}

    public static void PlayBeatSahurBtnHome(float volume = 1f) {
		TryPlaySFX(SFXType.beat_sahur_btn_home, volume);
	}
    #endregion
}