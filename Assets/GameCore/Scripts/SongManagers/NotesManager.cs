using System.Collections.Generic;
using UnityEngine;
using System;
using System.Collections;
using System.IO;
using System.Linq;
using System.Text;
using Amanotes.ContentReader;
using GamePlay.Levels;
using Inwave;
using LevelBot;
using Sirenix.OdinInspector;
using TilesHop.GamePlay.NewElement;
using UnityEngine.Pool;
using NoteData = GamePlay.Levels.NoteData;
using Random = UnityEngine.Random;

public class NotesManager : MonoBehaviour {
    enum TrackName {
        main        = 0,
        motif_main1 = 1,
        relation    = 2,
        animation   = 3,
        haptic      = 4,
        trap        = 5,
        element     = 6,
    }

    public enum Difficulties { //TH-709
        None    = 0,
        Easy    = 1,
        Normal  = 2,
        Hard    = 3,
        Endless = 4,
    }

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ const ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public static NotesManager instance;

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ public ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    [Tooltip("Set 0 to disable")] [SerializeField]
    private int forceNoteStart = -1;

    [Tooltip("Set 0 to disable")] [SerializeField]
    private int forceNoteEnd = -1;

    public IngameSectionType SpecialSection_Type;
    public int               SpecialSection_Start;
    public int               SpecialSection_End;

    [ShowInInspector, ReadOnly] private bool                     _existSpecialTileInMidi;

    private                             HashSet<NoteElementType> _listSpecialTiles = new HashSet<NoteElementType>();

    // ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ HideInInspector ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    public                   List<NoteData> noteDatas; //string index = 0
    public                   List<NoteData> animationNotesDatas;
    private                  List<NoteData> _noteBpmDatas;
    [HideInInspector] public List<NoteData> noteMidiDatas;

    [HideInInspector] public float delayTime     = 0;
    [HideInInspector] public float minDistance   = 0;
    [HideInInspector] public Song  song;
    [HideInInspector] public bool  isBpm = false;
    [HideInInspector] public bool  usingRhythmTool;
    [HideInInspector] public float filterMaxTime;
    [HideInInspector] public float filterMiddleTime;
    [HideInInspector] public float filterMinTime;
    [HideInInspector] public int   numberNoteInRound;

    public   bool isMidiGenerateTile = false;
    public   bool usingCombinableLongTile;
    public   bool useHaptic;
    public   bool isMidiForceControl;
    public   bool FakeTile_Follow_Pitch;
    private  bool _isExistNewElements = false;
    internal bool increaseLongNoteByCode;
    private  bool _increaseMoodChange;
    

    [NonSerialized, ShowInInspector] private byte _countMoodChange;

    public bool isLoadingNoteData;

    [ShowInInspector] private string _lastMidiContentId;

    public int noteCount => noteDatas.Count; //string index = 0
    public bool IsForceNoteEnd => forceNoteEnd > 0;

    public int IdForceNoteEnd {
        get => forceNoteEnd;
        set {
            forceNoteEnd = value;
            PlayerPrefs.SetInt("devsetting_forceend", value);
        }
    }

    private RemoteConfig remoteConfig => RemoteConfig.instance;

    //private

    public Difficulties Difficulty { get; private set; } = Difficulties.None;
    public string SongName { get; private set; }

    public const float LongDurationValue  = 0.5f;
    public const float ShortDistanceValue = 0.3f;

    private void Awake() {
        instance = this;
    }

    private void Start() {
        forceNoteEnd = PlayerPrefs.GetInt("devsetting_forceend", 0);
        
        Util.WaitRemoteConfigDone(Init, true);

        Platform.OnHit += PlatformOnOnHit;
    }

    private void Init() {
        //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

#if UNITY_ANDROID
        ContentNoteGenerator.InitKey(remoteConfig.APIKey_ContentReader_Android);
#else
        ContentNoteGenerator.InitKey(remoteConfig.APIKey_ContentReader_IOS);
#endif
    }

    public IEnumerator LoadNoteData(Song song, byte[] rawData = null) {
        isLoadingNoteData = true;
        this.song = song;
        this._lastMidiContentId = song.binContentID;
        this.SongName = song.GetSongName();
        this.Difficulty = GetDifficultiesOfCurrentSong();
        if (Difficulty == Difficulties.Easy) {
            UpdateMidiFilter(remoteConfig.NotesDifficult_Easy_GameLevel);
        } else {
            UpdateMidiFilter();
        }

        if (song.bmp < 10) {
            song.bmp = 120;
        }

        minDistance = 0;
        isBpm = false;
        usingRhythmTool = false;
        isMidiGenerateTile = false;
        _countMoodChange = 0;
        _existSpecialTileInMidi = false;
        _listSpecialTiles.Clear();
        usingCombinableLongTile = false;
        useHaptic = false;
        increaseLongNoteByCode = false;
        _increaseMoodChange = false;
        isMidiForceControl = CheckMidiForceControl(song.acm_id_v3);
        FakeTile_Follow_Pitch = CheckFakeTileFollowPitch(song.acm_id_v3);
        _isExistNewElements = CheckExistNewElements();
        // Get note
        noteDatas ??= new List<NoteData>();
        noteDatas.Clear();
        _noteBpmDatas ??= new List<NoteData>();
        _noteBpmDatas.Clear();
        noteMidiDatas ??= new List<NoteData>();
        noteMidiDatas.Clear();
        animationNotesDatas ??= new List<NoteData>();
        animationNotesDatas.Clear();

        if (!(remoteConfig.isUseRhythmToolForACM && !song.path.Contains(FILE_EXT.MP3))) {
            string path = song.GetLocalMidiPath();
            if (rawData == null) {
                yield return FileHelper.IELoadFileBinary(path, bytes => {
                    rawData = bytes; //
                });
            }

            if (rawData != null && rawData.Length > 10) {
                AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_midi_success, song);

                if (song.isMusicalizationLevelbot) { //isMusicalizationLevelbot
                    if (_isExistNewElements && !remoteConfig.NewElements_NormalAutoGenElement_Enable) {
                        //not auto gen for normal content
                        _isExistNewElements = false;
                    }

                    noteDatas = GetNotesByLevelBot(rawData);
                } else if (song.isMusicalizationOld || song.isMusicalizationACM) { //TH-281 Musicalization
                    LoadMusicalizationNote(rawData);
                } else { //old normal midi
                    if (_isExistNewElements && !remoteConfig.NewElements_NormalAutoGenElement_Enable) {
                        //not auto gen for normal content
                        _isExistNewElements = false;
                    }

                    Logger.LogWarning("[LoadNoteData] old normal midi => " + song.name + " => " + song.acm_id_v3);
                    if (!string.IsNullOrEmpty(song.midi_tab) && Enum.TryParse(song.midi_tab, out NoteTab tab)) {
                        LoadRawNote(rawData, tab);
                    } else {
                        LoadRawNote(rawData, NoteTab.Easy);
                        LoadRawNote(rawData, NoteTab.Medium);
                        LoadRawNote(rawData, NoteTab.Hard);
                        LoadRawNote(rawData, NoteTab.Expert);
                        LoadRawNote(rawData, NoteTab.SupperEasy);
                    }
                }

                FilterNote(noteDatas);
                //CountSpecial();
                noteDatas = AddNecessaryNotes(noteDatas);
                UpdateDistance(noteDatas);
                UpdateDuration(noteDatas);

                if (isMidiGenerateTile) {
                    noteDatas = GetCalculatedCombinableLongNotes(noteDatas);
                }

                if (remoteConfig.LongTile_Increase_ByCode) {
                    if (!this.song.isTutorialSong) {
                        int countCurrentLongNote = CalculatedLongNotes(noteDatas);
                        if (countCurrentLongNote != 0) {
                            //no need!
                            Logger.EditorLog("LongNote", $"Exist long note {countCurrentLongNote}=> not increase");
                        } else if (remoteConfig.LongTile_Increase_MinTile < 1 ||
                                   remoteConfig.LongTile_Increase_MaxTile < remoteConfig.LongTile_Increase_MinTile) {
                            //no need!
                            Logger.EditorLog("LongNote",
                                $"Config is invalid. min: {remoteConfig.LongTile_Increase_MinTile} - max: {remoteConfig.LongTile_Increase_MaxTile}");
                        } else if (string.IsNullOrEmpty(remoteConfig.LongTile_Increase_Apply)) {
                            (noteDatas, increaseLongNoteByCode) = CalculatedIncreaseLongNotes(noteDatas);
                        } else {
                            if (!string.IsNullOrEmpty(song.acm_id_v3) &&
                                remoteConfig.LongTile_Increase_Apply.Contains(song.acm_id_v3)) {
                                (noteDatas, increaseLongNoteByCode) = CalculatedIncreaseLongNotes(noteDatas);
                            } else {
                                Logger.EditorLog("LongNote", $"Not required acmidv3");
                            }
                        }

#if UNITY_EDITOR
                        if (increaseLongNoteByCode) {
                            Logger.EditorLog("LongNote", $"Increase Long Note by Code!");
                        }
#endif
                    }
                }

                //CountSpecial();
            } else {
                AnalyticHelper.NO_ACM_Event(TRACK_NAME.get_midi_fail, song, "Can't read file");
            }
        }

        // ~~~~~~~~~~~~~~~~~~~~ use RhythmTool || BMP to make notes ~~~~~~~~~~~~~~~~~~~~
        if (noteCount == 0) {
            usingRhythmTool = true;
            if (BuidMidiNoteByRhythmTool(song)) {
                isBpm = false;
                FilterNote(noteDatas);

                noteDatas = AddNecessaryNotes(noteDatas);
                UpdateDistance(noteDatas);
                UpdateDuration(noteDatas);

                UpdateNoteForRhythmTool();
            } else {
                isBpm = true;
                noteDatas = BuildMidiNoteByBmp(song);
            }
        }

        if (isMidiGenerateTile) {
            if (SpecialSection_Type != IngameSectionType.Normal) {
                ReCalculateInGameSpecialSectionByMiDiAfterFilterNote();
            }

            if (!_existSpecialTileInMidi && MidiGenAutoElements() && HasNewElements()) {
                CustomGenerateSpecialTile();
            }
        } else {
            if (HasNewElements()) {
                CustomGenerateSpecialTile();
            } else {
                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, "No auto gen !!!");
            }
        }

        if (IsExistUpsideDown() && MapManager.instance != null) {
            MapManager.instance.SetupReverseTile();
        }

        if (SpecialSection_Type == IngameSectionType.ZicZac) {
            noteDatas = GetCalculatedZicZacLongNotes(noteDatas);
        }

        noteMidiDatas = NoteData.CloneList(noteDatas);

        if (remoteConfig.NotesDifficult_IsEnable &&
            ((Difficulty == Difficulties.Easy && remoteConfig.NotesDifficult_Easy_UseBpm) ||
             Difficulty == Difficulties.Endless)) {
            List<Vector2> longTime = GetTimeLongNote(noteDatas, remoteConfig.NotesDifficult_Easy_TimeLongNote);
            float timeAppearSecondNote = noteDatas[1].timeAppear;

            _noteBpmDatas = new List<NoteData>();
            _noteBpmDatas = BuildMidiNoteByBmp(song, timeAppearSecondNote);
            _noteBpmDatas = AddNecessaryNotes(_noteBpmDatas);
            if (!remoteConfig.NotesDifficult_Easy_UseBpmLocalSong)
                _noteBpmDatas = FilterNoteByLongTime(_noteBpmDatas, longTime);
            UpdateDistance(_noteBpmDatas);
            UpdateDuration(_noteBpmDatas);
            _noteBpmDatas = UpdateMoodFromMidi(_noteBpmDatas, noteDatas);

            if (Difficulty == Difficulties.Easy || (Difficulty == Difficulties.Endless &&
                                                    remoteConfig.NotesDifficult_Endless_CountChangeMidi <= 1)) {
                isBpm = true;
                noteDatas = _noteBpmDatas;
            }
        }

        noteDatas = ApplyForceNote(noteDatas);
        _noteBpmDatas = ApplyForceNote(_noteBpmDatas);
        noteMidiDatas = ApplyForceNote(noteMidiDatas);

        isLoadingNoteData = false;
    }

    private bool CheckMidiForceControl(string songAcmIDV3) {
        if (string.IsNullOrEmpty(songAcmIDV3))
            return false;

        return remoteConfig.Midi_FullControl_Force.Contains(songAcmIDV3);
    }

    private bool CheckFakeTileFollowPitch(string songAcmIDV3) {
        if (string.IsNullOrEmpty(songAcmIDV3))
            return false;
        if (!remoteConfig.FakeTile_Follow_Pitch)
            return false;
        if (remoteConfig.FakeTile_Follow_Pitch_Exclude.Contains(songAcmIDV3))
            return false;
        if (string.IsNullOrEmpty(remoteConfig.FakeTile_Follow_Pitch_Include))
            return true;

        return remoteConfig.FakeTile_Follow_Pitch_Include.Contains(songAcmIDV3);
    }

    private string                 _cachedSongName;
    private Dictionary<int, float> _cachedDuration = new Dictionary<int, float>();

    public void StorageDurationBeforeChange(int idNote) {
        if (_cachedDuration.ContainsKey(idNote))
            return;
        if (idNote >= noteDatas.Count)
            return;

        _cachedDuration.Add(idNote, noteDatas[idNote].duration);
        if (string.IsNullOrEmpty(_cachedSongName)) {
            _cachedSongName = this.SongName;
        }
        //Logger.EditorLogError($"Cached id {idNote} time {noteDatas[idNote].duration}");
    }

    public void RestoreDurationAfterChange() {
        if (_cachedDuration.Count != 0) {
            if (_cachedSongName.Equals(this.SongName)) {
                Logger.EditorLog("LongNote", $"Restore duration after change!!! {_cachedDuration.Count}");
                foreach (var item in _cachedDuration) {
                    if (noteDatas.Count > item.Key) {
                        noteDatas[item.Key].duration = item.Value;
                    } else {
                        Logger.EditorLogError("null");
                    }
                }
            } else {
                Logger.EditorLog("LongNote", $"Another song => no need restore changed duration!");
            }

            _cachedSongName = null;
            _cachedDuration.Clear();
        }
    }

    public List<NoteData> GetNoteDatas() {
        return noteDatas;
    }

    private void LoadMusicalizationNote(byte[] rawData) {
        try {
            FullMidiContent fullMidiContent = NoteGeneration.GetAllTrackNotes(rawData);
#if UNITY_EDITOR
            DebugFullMidiContent(fullMidiContent);
#endif
            MidiTrackData mainTrack = GetTrackName(fullMidiContent, TrackName.main);

            if (mainTrack != null) {
                List<NoteData> notesMainTrack = NoteData.ConvertToInwaveNotes(mainTrack.Notes);

                noteDatas = GetMainNotes(notesMainTrack);

                bool isNewMidiLogic = false;
                if (Configuration.instance.enableContentTool) {
                    isNewMidiLogic = CheckMusicalizationV2ByTrack(fullMidiContent);
                } else {
                    isNewMidiLogic = song.IsMusicalizationV2();
                }

                isMidiGenerateTile = isMidiForceControl || (remoteConfig.IsMIDI_FullControl && isNewMidiLogic);
                if (!isMidiGenerateTile) {
                    if (!remoteConfig.NewElements_NormalAutoGenElement_Enable) {
                        //not auto gen for normal content
                        _isExistNewElements = false;
                    }
                }

                if (isMidiGenerateTile) {
                    Logger.EditorLog("MIDI new generation");
                    song.longNoteDuration = LongDurationValue;
                    //MIDIFilterNote(noteDatas);

                    //~~~~~~~~~~~~~~ Element Type ~~~~~~~~~~~~
                    MidiTrackData elementTrack = GetTrackName(fullMidiContent, TrackName.element);

                    int songStartCount = PlayerPrefs.GetInt(CONFIG_STRING.count_event + SONG_STATUS.song_start);

                    //TH-3028: song_start lớn hơn _realtile_element_song_start thì mới đọc element track
                    bool isUsingForRealTile = isMidiForceControl ||
                                              (HasNewElements() && remoteConfig.NewElements_OverLapOtherRule) ||
                                              songStartCount >= remoteConfig.MidiFilter_realtile_element_song_start;

                    //TH-3028: song_start lớn hơn _faketile_element_song_start thì mới đọc element track của fake tile
                    bool isUsingForFakeTile = isMidiForceControl ||
                                              (HasNewElements() && remoteConfig.NewElements_OverLapOtherRule) ||
                                              songStartCount >= remoteConfig.MidiFilter_faketile_element_song_start;

                    noteDatas = AddElement(elementTrack, noteDatas, isUsingForRealTile, isUsingForFakeTile);

                    noteDatas = AddMovingTile(noteDatas);

                    MidiTrackData trapTrack = GetTrackName(fullMidiContent, TrackName.trap);
                    noteDatas = AddTrap(trapTrack, noteDatas);

                    MidiTrackData hapticTrack = GetTrackName(fullMidiContent, TrackName.haptic);
                    noteDatas = AddHaptic(hapticTrack, noteDatas);
                }

                //~~~~~~~~~~~~~~ timbre ~~~~~~~~~~~~~~
                List<NoteData> timbreNotes = GetTimbreNotes(notesMainTrack);
                if (timbreNotes.Count > 0) {
                    noteDatas = AddTimbre(timbreNotes, noteDatas);
                }

                //~~~~~~~~~~~~~~ pitch [line] ~~~~~~~~~~~~~~
                MidiTrackData motifTrack = GetTrackName(fullMidiContent, TrackName.motif_main1);
                noteDatas = AddPitch(motifTrack, noteDatas);

                //~~~~~~~~~~~~~~ intensity [strong note] ~~~~~~~~~~~~~~
                noteDatas = AddIntensity(noteDatas);

                //~~~~~~~~~~~~~~ mood change ~~~~~~~~~~~~~~
                MidiTrackData relationTrack = GetTrackName(fullMidiContent, TrackName.relation);
                noteDatas = AddMoodChange(relationTrack, noteDatas, (isMidiGenerateTile && HasNewElements()));

                //~~~~~~~~~~~~~~ animation ~~~~~~~~~~~~
                MidiTrackData animationTrack = GetTrackName(fullMidiContent, TrackName.animation);
                if (animationTrack != null) {
                    GetAnimationNotes(animationTrack, noteDatas);
                    noteDatas = AddMoodChangeByAnimation(animationNotesDatas, noteDatas);
                }

                ListPool<NoteData>.Release(timbreNotes);
            }
        } catch (Exception e) {
            string msg =
                $"Cannot process MIDI of song name => {song.name}; song acm_id => {song.acm_id_v3}; error midi => {e.Message}";
            CustomException.Fire("[Load Musicalization Note]", msg);
        }
    }

    private bool CheckMusicalizationV2ByTrack(FullMidiContent fullMidiContent) {
        foreach (MidiTrackData track in fullMidiContent.Tracks) {
            if (track.TrackName.Equals(TrackName.haptic.ToString())) {
                return true;
            }

            if (track.TrackName.Equals(TrackName.element.ToString())) {
                return true;
            }

            if (track.TrackName.Equals(TrackName.trap.ToString())) {
                return true;
            }
        }

        return false;
    }

    private List<NoteData> AddElement(MidiTrackData elementTrack, List<NoteData> notes, bool isUsingForRealTile,
                                      bool isUsingForFakeTile) {
        List<NoteData> elementNotes = elementTrack == null ? null : NoteData.ConvertToInwaveNotes(elementTrack.Notes);
        if (elementNotes == null || elementNotes.Count == 0) {
            return notes;
        }

        int indexElement = 0;
        NoteElementType elementType;
        bool isFakeElement;
        int totalElementNote = elementNotes.Count;

        for (int i = 0; i < notes.Count; i++) {
            for (int j = indexElement; j < totalElementNote; j++) {
                if (Math.Abs(notes[i].timeAppear - elementNotes[j].timeAppear) < Mathf.Epsilon) {
                    elementType = GetElementType(elementNotes[j].nodeID, elementNotes[j].duration,
                        elementNotes[j].velocity, notes[i], HasNewElements());
                    isFakeElement = elementType.IsFakeElement();
                    if ((isUsingForFakeTile && isFakeElement) || (isUsingForRealTile && !isFakeElement) ||
                        elementType.IsLongTileElement()) {
                        if (elementType != NoteElementType.None && elementType != NoteElementType.FakeTile &&
                            elementType != NoteElementType.MovingTile && elementType != NoteElementType.LongTile) {
                            if (remoteConfig.NewElements_OverLapMidiManualElement) {
                                elementType = NoteElementType.None;
                            } else {
                                _existSpecialTileInMidi = true; //exist element
                                _listSpecialTiles.Add(elementType); //midi specialTile
                                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"[{i}] {elementType}");
                            }
                        }

                        notes[i].elementType = elementType;
                    }
                } else if (elementNotes[j].timeAppear - notes[i].timeAppear > Mathf.Epsilon) {
                    indexElement = j;
                    break;
                }
            }
        }

        return notes;
    }

    private NoteElementType GetElementType(int nodeId, float duration, float velocity, NoteData note,
                                           bool usingNewElement) {
        switch (nodeId) {
            case (int) NoteID.C8:
            case (int) NoteID.C_8:
            case (int) NoteID.D8:
            case (int) NoteID.D_8:
            case (int) NoteID.E8:
                if (duration >= LongDurationValue) { // long tile
                    if (!usingNewElement) {
                        return NoteElementType.LongTile;
                    }

                    return velocity switch {
                        < 20 => NoteElementType.LongTileBreak,
                        _ => NoteElementType.LongTile
                    };
                } else if (note.intensity == NoteData.Intensity.Weak) { //single tile
                    if (!usingNewElement) {
                        return NoteElementType.None;
                    }

                    return velocity switch {
                        < 20 => NoteElementType.MovingTile,
                        < 30 => NoteElementType.FadeOut,
                        < 40 => NoteElementType.MovingCircle,
                        < 50 => NoteElementType.FadeInOut,
                        < 60 => NoteElementType.Teleport,
                        _ => NoteElementType.None
                    };
                } else { // strong tile
                    if (!usingNewElement) {
                        return NoteElementType.Strong1;
                    }

                    return velocity switch {
                        < 60 => NoteElementType.Strong1,
                        < 80 => NoteElementType.Strong2,
                        < 100 => NoteElementType.Strong3,
                        _ => NoteElementType.Strong4
                    };
                }

            case (int) NoteID.C9:
            case (int) NoteID.C_9:
            case (int) NoteID.D9:
            case (int) NoteID.D_9:
            case (int) NoteID.E9:
                if (!usingNewElement) {
                    return NoteElementType.FakeTile;
                }

                //trap tile
                return velocity switch {
                    < 20 => NoteElementType.FakeTile,
                    < 30 => NoteElementType.FakeTransform,
                    < 40 => NoteElementType.FakeConveyor,
                    < 50 => NoteElementType.FakeThunder,
                    _ => NoteElementType.FakeTile
                };
        }

        return NoteElementType.None;
    }

    private List<NoteData> AddHaptic(MidiTrackData hapticTrack, List<NoteData> notes) {
        List<NoteData> hapticNotes = hapticTrack == null ? null : NoteData.ConvertToInwaveNotes(hapticTrack.Notes);
        if (hapticNotes == null || hapticNotes.Count == 0) {
            return notes;
        }

        int indexHaptic = 0;
        float startTime;
        float endTime;
        List<float> haptics = new List<float>();
        int totalTrapNote = hapticNotes.Count;
        for (int i = 0; i < noteDatas.Count; i++) {
            startTime = noteDatas[i].timeAppear;
            endTime = startTime + noteDatas[i].duration;
            haptics.Clear();
            for (int j = indexHaptic; j < totalTrapNote; j++) {
                if (hapticNotes[j].timeAppear >= startTime) {
                    if (hapticNotes[j].timeAppear <= endTime) {
                        //add
                        haptics.Add(hapticNotes[j].timeAppear);
                    } else {
                        if (haptics.Count != 0) {
                            noteDatas[i].haptics = haptics.ToArray();
                        }

                        indexHaptic = j;
                        break;
                    }
                }
            }
        }

        return notes;
    }

    private List<NoteData> AddTrap(MidiTrackData trapTrack, List<NoteData> notes) {
        List<NoteData> trapNotes = trapTrack == null ? null : NoteData.ConvertToInwaveNotes(trapTrack.Notes);
        if (trapNotes == null || trapNotes.Count == 0) {
            return notes;
        }

        NoteData.FakeTile fakeTile = NoteData.FakeTile.NONE;
        int indexTrap = 0;
        bool hasFakeTile = false;
        int totalTrapNote = trapNotes.Count;
        for (int i = 0; i < noteDatas.Count; i++) {
            fakeTile = NoteData.FakeTile.NONE;
            hasFakeTile = false;
            for (int j = indexTrap; j < totalTrapNote; j++) {
                if (Math.Abs(notes[i].timeAppear - trapNotes[j].timeAppear) < Mathf.Epsilon) {
                    //same
                    fakeTile |= GetFakeTileLine(trapNotes[j].nodeID);
                    hasFakeTile = true;
                } else if (trapNotes[j].timeAppear - notes[i].timeAppear > Mathf.Epsilon) {
                    indexTrap = j;
                    if (hasFakeTile) {
                        SetFakeData(i, fakeTile);
                    }

                    break;
                }
            }
        }

        void SetFakeData(int index, NoteData.FakeTile fake) {
            notes[index].fakeTile |= fake;
        }

        return notes;
    }

    private NoteData.FakeTile GetFakeTileLine(int id) {
        switch (id) {
            case (int) NoteID.C9:
                return NoteData.FakeTile.LEFT;

            case (int) NoteID.C_9:
                return NoteData.FakeTile.MIDDLE_LEFT;

            case (int) NoteID.D9:
                return NoteData.FakeTile.MIDDLE;

            case (int) NoteID.D_9:
                return NoteData.FakeTile.MIDDLE_RIGHT;

            case (int) NoteID.E9:
                return NoteData.FakeTile.RIGHT;

            default:
                return NoteData.FakeTile.NONE;
        }
    }

    private List<NoteData> AddMovingTile(List<NoteData> noteDatas) {
        float epsilon = 0.001f;
        float lastTime = -1f;
        int lastTile = 0;
        NoteData.Moving movingType = NoteData.Moving.None;
        bool inMovingStage = false;
        for (int i = 0; i < noteDatas.Count; i++) {
            if (noteDatas[i].elementType == NoteElementType.MovingTile) {
                if (!inMovingStage) {
                    inMovingStage = true;
                    movingType = GetRandomMovingTile();
                }

                noteDatas[i].moving = movingType;
            } else {
                inMovingStage = false;
            }
        }

        return noteDatas;
    }

    private NoteData.Moving GetRandomMovingTile() {
        int random = UnityEngine.Random.Range(0, 99999) % 3;
        if (random == 0)
            return NoteData.Moving.Normal;
        else if (random == 1)
            return NoteData.Moving.Snake;
        else
            return NoteData.Moving.ZicZac;
    }

    private List<NoteData> GetCalculatedCombinableLongNotes(List<NoteData> noteDatas) {
        List<LongNoteNodeData> tempCachedLongNotes = new List<LongNoteNodeData>();
        //remove unused long note(s) and store their information
        for (int i = 0; i < noteDatas.Count; i++) {
            noteDatas[i].originalDuration = noteDatas[i].duration;
            if (noteDatas[i].duration >= remoteConfig.Musicalization_LongNoteDuration && i <= noteDatas.Count - 2) {
                int tempIndex = i + 1;
                while (tempIndex < noteDatas.Count - 1 &&
                       noteDatas[tempIndex].duration >= remoteConfig.Musicalization_LongNoteDuration) {
                    var nodeData = new LongNoteNodeData {
                        TimeAppear = noteDatas[tempIndex].timeAppear,
                        SlideDuration = noteDatas[tempIndex].duration,
                        Pitch = noteDatas[tempIndex].pitch,
                        Distance = noteDatas[tempIndex].distance,
                        Haptics = noteDatas[tempIndex].haptics
                    };
                    tempCachedLongNotes.Add(nodeData);
                    noteDatas.RemoveAt(tempIndex);
                    if (!nodeData.Haptics.IsNullOrEmpty()) {
                        var temp = new List<float>();
                        if (!noteDatas[i].haptics.IsNullOrEmpty()) {
                            temp.AddRange(noteDatas[i].haptics.ToList());
                        }

                        temp.AddRange(nodeData.Haptics);
                        noteDatas[i].haptics = temp.ToArray();
                    }
                }

                if (!usingCombinableLongTile && tempCachedLongNotes.Count != 0) {
                    usingCombinableLongTile = true;
                }

                noteDatas[i].cachedNextLongNote = tempCachedLongNotes.ToArray();
                tempCachedLongNotes.Clear();
            }
        }

        return noteDatas;
    }

    private int CalculatedLongNotes(List<NoteData> noteDatas) {
        int count = 0;
        float longDuration = song.GetLongDuration();
        for (int i = 0; i < noteDatas.Count; i++) {
            if (noteDatas[i].duration >= longDuration) {
                count++;
            }
        }

        return count;
    }

    private (List<NoteData>, bool) CalculatedIncreaseLongNotes(List<NoteData> noteDatas) {
        List<LongNoteNodeData> tempCachedLongNotes = new List<LongNoteNodeData>();
        float longDuration = song.GetLongDuration();

        bool hasIncrease = false;
        byte currentMood = 0;
        byte amountInMood = 0;
        //remove unused long note(s) and store their information
        for (int i = 0; i < noteDatas.Count; i++) {
            if (IsMoodChange(i)) {
                currentMood++;
                amountInMood = 0;
                continue;
            }

            if (amountInMood >= remoteConfig.LongTile_Increase_AmountPerMoodChange) {
                continue;
            }

            int result = CanMakeLongNote(i);
            if (result > 0) {
                float duration = noteDatas[result + i + 1].timeAppear - noteDatas[i].timeAppear -
                                 remoteConfig.LongTile_MinDistance;
                if (duration < longDuration) {
                    Logger.EditorLog("LongNote",
                        $"Not required target longDuration: {longDuration} vs this: {duration}");
                    continue;
                } else {
                    Logger.EditorLog("LongNote", $"Merger long note to [{i}] with result: {result}", "#dd0000ff");
                }

                noteDatas[i].haptics = new float[result + 1];
                noteDatas[i].originalDuration = noteDatas[i + 1].timeAppear - noteDatas[i].timeAppear;
                noteDatas[i].duration = duration;
                noteDatas[i].haptics[0] = noteDatas[i].timeAppear;
                int tempIndex = i + 1;
                byte offset = 1;
                while (tempIndex < noteDatas.Count - 1 && result > 0) {
                    var nodeData = new LongNoteNodeData {
                        TimeAppear = noteDatas[tempIndex].timeAppear,
                        SlideDuration = noteDatas[tempIndex + 1].timeAppear - noteDatas[tempIndex].timeAppear,
                        Pitch = noteDatas[tempIndex].pitch,
                        Distance = 0,
                        Haptics = null
                    };
                    noteDatas[i].haptics[offset++] = nodeData.TimeAppear;
                    if (result == 1) {
                        nodeData.SlideDuration -= remoteConfig.LongTile_MinDistance;
                        duration += nodeData.SlideDuration;
                    }

                    tempCachedLongNotes.Add(nodeData);
                    noteDatas.RemoveAt(tempIndex);
                    result--;
                }

                noteDatas[i + 1].distance = noteDatas[i + 1].timeAppear - noteDatas[i].timeAppear;
                if (remoteConfig.LongTile_Increase_Separate) {
                    usingCombinableLongTile = true;
                    noteDatas[i].cachedNextLongNote = tempCachedLongNotes.ToArray();
                } else {
                    useHaptic = true;
                }

                tempCachedLongNotes.Clear();
                amountInMood++;
                if (!hasIncrease) {
                    hasIncrease = true;
                }
            }
        }

        return (noteDatas, hasIncrease);

        int CanMakeLongNote(int i) {
            if (i < 5 || i > noteDatas.Count - remoteConfig.LongTile_Increase_MaxTile - 2)
                return 0;

            if (IsLongNote(i) || IsMoodChange(i) || IsClose(i)) {
                return 0;
            }

            byte numberTile = 1;
            byte acceptTile = 0;
            while (numberTile < remoteConfig.LongTile_Increase_MaxTile) {
                if (IsLongNote(i + numberTile) || IsMoodChange(i + numberTile))
                    break;

                float deltaTime = noteDatas[i + numberTile].timeAppear - noteDatas[i].timeAppear -
                                  remoteConfig.LongTile_MinDistance;
                if (deltaTime > remoteConfig.LongTile_Increase_MaxTime) {
                    break;
                }

                if (!IsClose(i + numberTile + 1)) {
                    acceptTile = numberTile;
                }

                numberTile++;
            }

            if (acceptTile >= remoteConfig.LongTile_Increase_MinTile - 1) {
                float deltaTime = noteDatas[i + acceptTile].timeAppear - noteDatas[i].timeAppear -
                                  remoteConfig.LongTile_MinDistance;
                if (deltaTime < longDuration) {
                    return 0;
                }

                return acceptTile;
            }

            return 0;
        }

        bool IsLongNote(int i) {
            return noteDatas[i].duration >= longDuration;
        }

        bool IsMoodChange(int i) {
            return i >= 6 && noteDatas[i].mood != noteDatas[i - 1].mood;
        }

        bool IsClose(int i) {
            return noteDatas[i].distance < remoteConfig.LongTile_MinDistance;
        }
    }

    private List<NoteData> GetCalculatedZicZacLongNotes(List<NoteData> noteDatas) {
        List<LongNoteNodeData> tempCachedLongNotes = new List<LongNoteNodeData>();
        //remove unused long note(s) and store their information

        noteDatas[SpecialSection_Start].originalDuration = noteDatas[SpecialSection_Start + 1].timeAppear -
                                                           noteDatas[SpecialSection_Start].timeAppear;

        int tempIndex = SpecialSection_Start + 1;
        while (tempIndex <= SpecialSection_End) {
            var nodeData = new LongNoteNodeData {
                TimeAppear = noteDatas[tempIndex].timeAppear,
                SlideDuration = noteDatas[tempIndex + 1].timeAppear - noteDatas[tempIndex].timeAppear,
                Pitch = noteDatas[tempIndex].pitch,
                Distance = 0,
                Haptics = noteDatas[tempIndex].haptics
            };
            tempCachedLongNotes.Add(nodeData);
            if (!nodeData.Haptics.IsNullOrEmpty()) {
                var temp = new List<float>();
                if (!noteDatas[SpecialSection_Start].haptics.IsNullOrEmpty()) {
                    temp.AddRange(noteDatas[SpecialSection_Start].haptics.ToList());
                }

                temp.AddRange(nodeData.Haptics);
                noteDatas[SpecialSection_Start].haptics = temp.ToArray();
            }

            if (!noteDatas[tempIndex].cachedNextLongNote.IsNullOrEmpty()) {
                Logger.EditorLog("long note combine!!");
                tempCachedLongNotes.AddRange(noteDatas[tempIndex].cachedNextLongNote);
            }

            //noteDatas.RemoveAt(tempIndex);
            tempIndex++;
        }

        noteDatas[SpecialSection_Start].cachedNextLongNote = tempCachedLongNotes.ToArray();
        noteDatas[SpecialSection_Start].duration =
            noteDatas[SpecialSection_End].timeAppear - noteDatas[SpecialSection_Start].timeAppear;
        tempCachedLongNotes.Clear();

        return noteDatas;
    }

    private static void DebugFullMidiContent(FullMidiContent fullMidiContent) {
        StringBuilder output =
            new StringBuilder(
                $"[DebugFullMidiContent] song: {instance.song.name} {instance.song.GetLocalMidiPath()} \n");
        output.AppendLine("Track,Line,NoteID,timeAppear,duration,velocity");
        foreach (var track in fullMidiContent.Tracks) {
            foreach (var item in track.Notes) {
                output.Append(item.trackName);
                output.Append(FileHelper.Split);
                output.Append(GetNoteName(item.nodeID));
                output.Append(FileHelper.Split);
                output.Append(item.nodeID);
                output.Append(FileHelper.Split);
                output.Append(item.timeAppear);
                output.Append(FileHelper.Split);
                output.Append(item.duration);
                output.Append(FileHelper.Split);
                output.Append(item.velocity);
                output.Append("\n");
            }
        }

        Debug.Log(output);
    }

    private static void DebugFullMidiContent(List<NoteData> noteDatas) {
        StringBuilder output =
            new StringBuilder(
                $"[DebugFullMidiContent] song: {instance.song.name} {instance.song.GetLocalMidiPath()} \n");
        output.AppendLine("Track,Line,NoteID,timeAppear,duration,velocity,mood");
        foreach (var item in noteDatas) {
            output.Append(item.trackName);
            output.Append(FileHelper.Split);
            output.Append(item.GetLineNumber());
            output.Append(FileHelper.Split);
            output.Append(item.nodeID);
            output.Append(FileHelper.Split);
            output.Append(item.timeAppear);
            output.Append(FileHelper.Split);
            output.Append(item.duration);
            output.Append(FileHelper.Split);
            output.Append(item.velocity);
            output.Append(FileHelper.Split);
            output.Append(item.mood);
            output.Append("\n");
        }

        Debug.Log(output);
    }

    public static String GetNoteName(int noteNumber) {
        int octave = noteNumber / 12;
        noteNumber -= 21; // see the explanation below.
        var notes = new string[] { "A", "A#", "B", "C", "C#", "D", "D#", "E", "F", "F#", "G", "G#" };
        if (noteNumber > 0 && notes.Length > noteNumber % 12) {
            var name = notes[noteNumber % 12];
            return name + octave;
        } else {
            return "";
        }
    }

    public bool ChangeToBpm() {
        if (_noteBpmDatas == null || _noteBpmDatas.Count == 0) {
            return false;
        }

        isBpm = true;
        noteDatas = _noteBpmDatas;
        return true;
    }

    public bool ChangeToMidi() {
        if (noteMidiDatas == null || noteMidiDatas.Count == 0) {
            return false;
        }

        isBpm = false;
        noteDatas = noteMidiDatas;
        return true;
    }

    public bool ChangeToSensitiveNotes() {
        isBpm = false;

        List<NoteData> sensitiveNotes = new List<NoteData>();
        for (int index = 0; index < 100; index++) {
            NoteData sensitiveNote = new NoteData {
                timeAppear = 60f / 114 * index
            };
            sensitiveNotes.Add(sensitiveNote);
        }

        UpdateDistance(sensitiveNotes);
        UpdateDuration(sensitiveNotes);

        noteDatas = sensitiveNotes;
        return true;
    }

    private List<NoteData> ApplyForceNote(List<NoteData> notes) {
        if (notes == null || notes.Count == 0) {
            return notes;
        }

        if (forceNoteStart >= 0 && forceNoteEnd >= 0 && forceNoteStart < forceNoteEnd) {
            List<NoteData> newNotes = new List<NoteData>();
            for (int i = forceNoteStart; i <= forceNoteEnd; i++) {
                if (i >= 0 && i < notes.Count) {
                    newNotes.Add(notes[i]);
                }
            }

            notes = newNotes;
        }

        if (notes.Count > 1) {
            notes[0].distance = notes[1].distance;
        }

        return notes;
    }

    private List<NoteData> AddMoodChange(MidiTrackData relationTrack, List<NoteData> notes, bool usingElement) {
        List<NoteData> relationNotes =
            relationTrack == null ? null : NoteData.ConvertToInwaveNotes(relationTrack.Notes);
        if (relationNotes == null) {
            return notes;
        }

        List<NoteData> moodNotes = new List<NoteData>();
        List<NoteData> specialNotes = new List<NoteData>();

        foreach (NoteData noteData in relationNotes) {
            switch (noteData.nodeID) {
                case (byte) NoteID.C0:
                case (byte) NoteID.C_0:
                case (byte) NoteID.D0:
                case (byte) NoteID.D_0:
                case (byte) NoteID.E0:
                    moodNotes.Add(noteData);
                    break;
            }
        }

        foreach (NoteData noteData in relationNotes) {
            switch (noteData.nodeID) {
                case (byte) NoteID.F0:
                    if (!usingElement) {
                        noteData.elementType = NoteElementType.MoodChange;
                    } else {
                        noteData.elementType = noteData.velocity switch {
                            < 20 => NoteElementType.MoodChangeInstant,
                            < 30 => NoteElementType.MoodChangeOrder,
                            < 40 => NoteElementType.MoodChangeTrain,
                            < 50 => NoteElementType.MoodChangeBroken,
                            _ => NoteElementType.MoodChange
                        };
                    }

                    bool foundTile = false;
                    foreach (var moodNote in moodNotes) {
                        if (Mathf.Approximately(moodNote.timeAppear, noteData.timeAppear)) {
                            foundTile = true;
                            moodNote.elementType = noteData.elementType;
                            break;
                        }
                    }

                    if (!foundTile) {
                        moodNotes.Add(noteData);
                    }

                    break;

                case (byte) NoteID.C1:
                    noteData.elementType = NoteElementType.None; // normal section
                    specialNotes.Add(noteData);
                    break;

                case (byte) NoteID.C_1:
                    if (!usingElement) {
                        noteData.elementType = NoteElementType.None;
                    } else {
                        noteData.elementType = noteData.velocity switch {
                            < 20 => NoteElementType.SpecialUpsideDown,
                            < 30 => NoteElementType.SpecialMirror,
                            < 40 => NoteElementType.SpecialHyperBoost,
                            < 50 => NoteElementType.SpecialVoidWay,
                            _ => NoteElementType.None
                        };
                        specialNotes.Add(noteData);
                    }

                    break;
            }
        }

        if (!moodNotes.IsNullOrEmpty()) {
            byte indexPartMood = 0;
            bool isMoodChange;
            NoteData moodNote;
            NoteData.Mood mood;
            for (int index = 0; index < notes.Count; index++) {
                isMoodChange = false;
                NoteData noteData = notes[index];
                if (indexPartMood < moodNotes.Count - 1 &&
                    noteData.timeAppear > moodNotes[indexPartMood + 1].timeAppear) {
                    indexPartMood += 1;
                    isMoodChange = true;
                }

                moodNote = moodNotes[indexPartMood];
                mood = moodNote.GetMood();
                noteData.mood = mood;
                if (isMoodChange && usingElement && index != 0) {
                    if (moodNote.elementType != NoteElementType.MoodChange &&
                        moodNote.elementType != NoteElementType.None) {
                        if (remoteConfig.NewElements_OverLapMidiManualElement) {
                            moodNote.elementType = NoteElementType.None;
                        } else {
                            _existSpecialTileInMidi = true; //exist moodchange
                            _listSpecialTiles.Add(moodNote.elementType); //midi specialMoodchange
                            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                                $"MoodChange [{index - 1}] {moodNote.elementType} {notes[index - 1].timeAppear}");
                        }
                    }

                    notes[index - 1].elementType = moodNote.elementType;
                }
            }
        }

        ResetSpecialSection();

        if (usingElement && !remoteConfig.NewElements_OverLapMidiManualElement && !specialNotes.IsNullOrEmpty()) {
            int indexSpecial = 0;
            bool isSpecialNote;
            NoteElementType elementType = NoteElementType.None;
            NoteData noteData;
            for (int index = 0; index < notes.Count; index++) {
                isSpecialNote = false;
                noteData = notes[index];
                if (indexSpecial < specialNotes.Count - 1 &&
                    noteData.timeAppear > specialNotes[indexSpecial + 1].timeAppear) {
                    indexSpecial += 1;
                    isSpecialNote = true;
                }

                NoteData specialNote = specialNotes[indexSpecial];
                if (isSpecialNote && index > 0) {
                    if (SpecialSection_Start < 0 && SpecialSection_Type == IngameSectionType.Normal) { //start
                        SpecialSection_Start = index - 1;
                        elementType = specialNote.elementType;
                    } else if (SpecialSection_End < 0) { //end
                        SpecialSection_End = index - 1;
                    }

                    Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                        $"Special Section {index - 1}  {specialNote.elementType} {notes[index - 1].timeAppear}");
                }
            }

            if (SpecialSection_Start > 0 && SpecialSection_End > 0) {
                SpecialSection_Type = ElementTypeToSectionType(elementType);
                Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                    $"Special Section [{SpecialSection_Start} - {SpecialSection_End}] {SpecialSection_Type}");
                if (SpecialSection_Type == IngameSectionType.UpsideDown) {
                    //process note!!!
                    var data = UpdateTileReverse(noteDatas, SpecialSection_Start, SpecialSection_End); //TH-2637
                    UpdateDistance(noteDatas);
                    UpdateDuration(noteDatas);
                    SpecialSection_Start = data.start;
                    SpecialSection_End = data.end;
                } else {
                    notes[SpecialSection_Start].elementType = elementType;
                    notes[SpecialSection_End].elementType = elementType;
                }
            } else {
                ResetSpecialSection();
                Logger.EditorLogError(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Special Section FAILED");
            }
        }

        return notes;
    }

    private List<NoteData> AddMoodChangeByAnimation(List<NoteData> animationNotes, List<NoteData> notes) {
        int indexPartMood = 0;
        foreach (NoteData noteData in notes) {
            //Chuyển sang part mood tiếp theo nếu time appear vượt qua
            if (indexPartMood < animationNotes.Count - 1 &&
                noteData.timeAppear > animationNotes[indexPartMood + 1].timeAppear) {
                indexPartMood += 1;
            }

            noteData.mood = animationNotes[indexPartMood].mood;
        }

        return notes;
    }

    private void GetAnimationNotes(MidiTrackData animationTrack, List<NoteData> notes) {
        if (animationTrack == null) {
            return;
        }

        List<NoteData> animationTrackNotes = NoteData.ConvertToInwaveNotes(animationTrack.Notes);
        if (remoteConfig.SongStructure_Enable) {
            animationNotesDatas = GetNewAnimationNotes(animationTrackNotes);
        } else {
            animationNotesDatas = GetOldAnimationNotes(animationTrackNotes);
        }
    }

    private List<NoteData> AddIntensity(List<NoteData> notes) {
        foreach (NoteData noteData in notes) {
            noteData.intensity = noteData.velocity >= 100 ? NoteData.Intensity.Strong : NoteData.Intensity.Weak;
        }

        return notes;
    }

    private List<NoteData> AddPitch(MidiTrackData motifTrack, List<NoteData> notes) {
        List<NoteData> motifNotes = motifTrack == null ? null : NoteData.ConvertToInwaveNotes(motifTrack.Notes);

        bool motifOrNot = remoteConfig.Musicalization_Pitch_Motif_or_not;
        if (motifOrNot && motifNotes != null) { //use motif track
            NoteData noteData;
            float timeAppear;
            NoteData noteByTime;
            NoteData.Pitch pitch;
            for (int index = 0; index < notes.Count; index++) {
                noteData = notes[index];
                timeAppear = noteData.timeAppear;
                noteByTime = GetNoteByTime(timeAppear, motifNotes);
                pitch = noteByTime?.GetLineNumber() ?? NoteData.Pitch.None;

                noteData.pitch = pitch;
            }
        } else { //use main track
            foreach (NoteData noteData in notes) {
                noteData.pitch = noteData.GetLineNumber();
            }
        }

        return notes;
    }

    private NoteData GetNoteByTime(float timeAppear, List<NoteData> notes) {
        for (int index = 0; index < notes.Count; index++) {
            NoteData noteData = notes[index];
            if (noteData.timeAppear < timeAppear) {
                continue;
            }

            if (noteData.timeAppear > timeAppear) {
                break;
            }

            return noteData;
        }

        return null;
    }

    private List<NoteData> AddTimbre(List<NoteData> noteTimbre, List<NoteData> notes) {
        noteTimbre = FilterNote(noteTimbre);
        notes = FilterNote(notes);

        int indexTimbre = 0;
        int notesCount = notes.Count;
        for (int index = 0; index < notesCount; index++) {
            NoteData currentNote = notes[index];
            NoteData nextTimbre = noteTimbre[Mathf.Min(indexTimbre + 1, noteTimbre.Count - 1)];

            //tăng part timbre lên nếu timeAppear của note đã vượt qua
            if (currentNote.timeAppear >= nextTimbre.timeAppear) {
                indexTimbre++;
            }

            //set timbre cho note dựa trên timbre hiện tại
            NoteData currentTimbre = noteTimbre[Mathf.Min(indexTimbre, noteTimbre.Count - 1)];
            NoteData.Timbre timbre = GetTimbre(currentTimbre);
            currentNote.timbre = timbre;
        }

        return notes;
    }

    private NoteData.Timbre GetTimbre(NoteData timbre) {
        if (timbre.velocity < 60) {
            return NoteData.Timbre.Rhythmic;
        } else if (60 <= timbre.velocity && timbre.velocity <= 80) {
            return NoteData.Timbre.Vocal;
        } else if (80 <= timbre.velocity) {
            return NoteData.Timbre.Melodic;
        }

        return NoteData.Timbre.Rhythmic;
    }

    private List<NoteData> GetTimbreNotes(List<NoteData> notesMainTrack) {
        List<NoteData> notes = ListPool<NoteData>.Get();

        foreach (NoteData noteData in notesMainTrack) {
            if (noteData.nodeID == (int) NoteID.C0) {
                notes.Add(noteData);
            }
        }

        return notes;
    }

    private List<NoteData> GetTimbreNotesV2(List<NoteData> notesMainTrack) { //mood change
        List<NoteData> notes = new List<NoteData>();

        foreach (NoteData noteData in notesMainTrack) {
            switch (noteData.nodeID) {
                case (int) NoteID.C0:
                case (int) NoteID.C_0:
                case (int) NoteID.D0:
                case (int) NoteID.D_0:
                case (int) NoteID.E0:
                    notes.Add(noteData);
                    break;
            }
        }

        return notes;
    }

    private List<NoteData> GetMainNotes(List<NoteData> notesMainTrack) {
        List<NoteData> notes = new List<NoteData>();

        foreach (NoteData noteData in notesMainTrack) {
            if (noteData.nodeID != (int) NoteID.C0) {
                notes.Add(noteData);
            }
        }

        return notes;
    }

    private List<NoteData> GetOldAnimationNotes(List<NoteData> notesAnimation) {
        List<NoteData> notes = new List<NoteData>();
        bool isBuildUpPart = false;
        bool isDropPart = false;
        bool isOuttroPart = false;
        foreach (NoteData noteData in notesAnimation) {
            if (IsCheckedNoteID(noteData.nodeID)) {
                if ((isBuildUpPart && !IsBuildUpPart(noteData.nodeID)) || isDropPart || isOuttroPart) {
                    // Chèn thêm anim Fill cuối đoạn BuildUp / Drop / Outtro nodeID = 6;

                    NoteData node = new NoteData();
                    node.timeAppear = noteData.timeAppear - 2f * 120f / song.bmp;
                    node.nodeID = 6;
                    notes.Add(node);
                    isBuildUpPart = false;
                    isDropPart = false;
                    isOuttroPart = false;
                }

                if (IsBuildUpPart(noteData.nodeID)) {
                    isBuildUpPart = true;
                } else if (IsDropPart(noteData.nodeID)) {
                    isDropPart = true;
                } else if (IsOuttroPart(noteData.nodeID)) {
                    isOuttroPart = true;
                }

                notes.Add(noteData);
            }
        }

        if (isOuttroPart) {
            NoteData node = new NoteData();
            node.timeAppear = noteDatas[noteCount - 1].timeAppear - 2f * 120f / song.bmp;
            node.nodeID = 6;
            notes.Add(node);
        }

        return notes;
    }

    private bool IsBuildUpPart(int id) {
        return id is (int) NoteID.D0 or (int) NoteID.D1 or (int) NoteID.D2;
    }

    private bool IsDropPart(int id) {
        return id is (int) NoteID.D_0 or (int) NoteID.D_1 or (int) NoteID.D_2;
    }

    private bool IsOuttroPart(int id) {
        return id is (int) NoteID.F0 or (int) NoteID.F1 or (int) NoteID.F2;
    }

    private bool IsCheckedNoteID(int nodeID) {
        return nodeID == (int) NoteID.C0 || nodeID == (int) NoteID.C_0 || nodeID == (int) NoteID.D0 ||
               nodeID == (int) NoteID.D_0 || nodeID == (int) NoteID.E0 || nodeID == (int) NoteID.F0 ||
               nodeID == (int) NoteID.D1 || nodeID == (int) NoteID.D2;
    }

    private List<NoteData> GetNewAnimationNotes(List<NoteData> notesAnimation) {
        List<NoteData> notes = new List<NoteData>();
        bool isFirstChorus = true;
        NoteData.Mood previousMood = NoteData.Mood.Mild;
        foreach (NoteData noteData in notesAnimation) {
            var phrase = NoteData.GetPharse(noteData.nodeID);
            if (phrase != NoteData.Phase.None) {
                noteData.phase = phrase;
                noteData.animation = NoteData.GetNewAnimation(noteData.nodeID);
                noteData.mood = noteData.GetNewMoodByAnimation(isFirstChorus, previousMood);
                previousMood = noteData.mood;
                if (isFirstChorus && noteData.phase == NoteData.Phase.Chorus) {
                    isFirstChorus = false;
                }

                notes.Add(noteData);
            }
        }

        return notes;
    }

    private List<NoteData> BuildMidiNoteByBmp(Song item, float startTime = -1) {
        List<NoteData> notesByBmp = new List<NoteData>();

        //Uu tien lay bpm tu ACM
        float bpm = remoteConfig.NotesDifficult_Easy_UseBpmLocalSong
            ? (Configuration.GetSongBpmACM(item.path) != 0
                ? Configuration.GetSongBpmACM(item.path)
                : Configuration.GetSongBpmLocal(item.path))
            : item.bmp;
        startTime = Configuration.GetSongBpmLocal(item.path) != 0
            ? Configuration.GetSongStartTimeLocal(item.path)
            : startTime;
        if (bpm < 50) {
            bpm = 120;
        }

        float distance = 60f / bpm;
        int totalNote = (int) (SuperpoweredSDK.instance.musicLength / distance);
        float timeAppear = startTime >= 0 ? startTime : item.startTime;
        for (int i = 0; i < totalNote; i++) {
            NoteData note = new NoteData {
                timeAppear = timeAppear
            };
            if (i == 0 || i > totalNote - 2) {
                note.nodeID = -1;
            }

            note.distance = distance;
            notesByBmp.Add(note);
            timeAppear += distance;
        }

        return notesByBmp;
    }

    private bool BuidMidiNoteByRhythmTool(Song localSong) {
        Logger.LogWarning("[BuidMidiNoteByRhythmTool] build note data by RhythmTool");

        PlayerData playerData = CoreData.GetPlayerData();
        if (playerData.IsExitsLocalNotes(song.path)) { //if it exits?
            List<NoteData> notes = playerData.LoadLocalNote(song.path);
            if (notes != null && notes.Count > 0) {
                noteDatas = notes;

                Logger.Log("[BuidMidiNoteByRhythmTool] Notes of song: " + localSong.path + " is " + noteCount);
                return true;
            } else {
                Logger.Log("[BuidMidiNoteByRhythmTool] Notes of song: " + localSong.path + " is null");
            }
        } else {
            Logger.Log("[BuidMidiNoteByRhythmTool] no notes of song: " + localSong.path);
        }

        return false;
    }

    private void LoadRawNote(byte[] data, NoteTab tab) {
        if (noteCount == 0) {
            try {
                NoteGeneration.GetNotes(data, tab, (res, bpm) => {
                    noteDatas = NoteData.ConvertToInwaveNotes(res);
                    // translate
                    if (song.midi_grid > 0) {
                        GridSnap gridSnap = GetGridSnapByID(song.midi_grid);
                        Logger.Log("gridSnap:" + gridSnap.ToString() + "-" + noteCount);
                        noteDatas = NoteData.ConvertToInwaveNotes(NoteGeneration.Translate(bpm, res, gridSnap));
                    }
#if UNITY_EDITOR
                    DebugFullMidiContent(noteDatas);
#endif
                    Logger.Log("song.midi_grid: " + song.midi_grid + "- bpm: " + bpm);
                    Logger.Log("resSucess:" + tab.ToString() + "-" + noteCount);
                }, (err) => { Debug.LogError(err); });
            } catch (Exception ex) {
                Debug.LogError(ex);
            }
        }
    }

    private void UpdateMidiFilter(int gameLevel = -1) {
        float[] filterArrayMin = { 0.32f, 0.17f, 0.1f };
        float[] filterArrayMax = { 0.5f, 0.25f, 0.1f };
        //song.filter = "0;0;0";
        if (!string.IsNullOrEmpty(song.filter)) {
            filterArrayMin = RemoteConfig.GetFloatArray(song.filter, ';', filterArrayMin);
        } else {
            filterArrayMin = RemoteConfig.GetFloatArray(remoteConfig.GetMidiFilter_Min(), ';', filterArrayMin);
        }

        filterArrayMax = RemoteConfig.GetFloatArray(remoteConfig.GetMidiFilter_Max(), ';', filterArrayMax);

        filterMaxTime = filterArrayMin[0];
        filterMiddleTime = filterArrayMin[1];
        filterMinTime = filterArrayMin[2];

        if (string.IsNullOrEmpty(song.filter) && !Configuration.instance.enableContentTool) {
            float percent = gameLevel >= 0 ? gameLevel : Configuration.GetGameLevel() / remoteConfig.MaxDifficultyLvl;
            filterMaxTime = Mathf.Lerp(filterArrayMax[0], filterMaxTime, percent);
            filterMiddleTime = Mathf.Lerp(filterArrayMax[1], filterMiddleTime, percent);
            filterMinTime = Mathf.Lerp(filterArrayMax[2], filterMinTime, percent);
        }
    }

    private List<NoteData> FilterNote(List<NoteData> notes) {
        if (notes.Count <= 1) {
            return notes;
        }

        // Filter the notes
        float lastTime = 0;
        float minTime = filterMinTime;

        for (int i = 0; i < notes.Count; i++) {
            bool isValidNote = !(song.midi_line > 0 && song.midi_line != notes[i].stringIndex + 1);

            if (i > 0 && notes[i].timeAppear - lastTime < minTime) {
                isValidNote = false;
            }

            if (isValidNote) {
                lastTime = notes[i].timeAppear;
            } else {
                notes.RemoveAt(i);
                i--;
            }
        }

        return notes;
    }

    /// <summary>
    /// 22/Oct/2021 Temporary remove from FilterNote function
    /// </summary>
    /// <param name="notes"></param>
    private void ModifyTimeAppear(List<NoteData> notes) {
        float mf = remoteConfig.Modify_MidiTime;
        float abs_mf = Mathf.Abs(mf);
        if (abs_mf > 0) {
            //Increase note time appear
            for (int i = 0; i < notes.Count; i++) {
                if (notes[i].timeAppear > abs_mf) {
                    notes[i].timeAppear += mf;
                }
            }
        }
    }

    List<NoteData> AddNecessaryNotes(List<NoteData> notes) {
        if (notes.Count <= 1) {
            return notes;
        }

        //Add first note
        NoteData firstNote = notes[0];

        float firstJumpTime = notes[1].timeAppear - firstNote.timeAppear;
        float timeAppear = firstNote.timeAppear - firstJumpTime;

        if (timeAppear > 0) {
            NoteData note = new NoteData();
            note.timeAppear = timeAppear;
            note.nodeID = -1;

            if (song.isMusicalizationType) {
                note.timbre = firstNote.timbre;
                note.mood = firstNote.mood;
            }

            notes.Insert(0, note);
        }

        //Add last note to static
        notes[notes.Count - 1].nodeID = -1;
        bool endlessMode = GameController.GetEndlessModeOfSong();
        if (endlessMode) {
            NoteData lastNote = notes[notes.Count - 1];

            // add 3 last note
            int count = 2; // Change 3 to 1
            while (count > 0) {
                NoteData note = new NoteData();
                note.timeAppear = notes[notes.Count - 1].timeAppear + firstJumpTime;
                note.nodeID = -1;

                if (song.isMusicalizationType) {
                    note.timbre = lastNote.timbre;
                    note.mood = lastNote.mood;
                }

                notes.Add(note);
                count--;
            }
        }

        return notes;
    }

    private void UpdateDistance(List<NoteData> datas) {
        float lastTime = 0;
        minDistance = 10;

        NoteData note;
        NoteData beforeNote;
        for (int i = 0; i < datas.Count; i++) {
            note = datas[i];
            float distance = note.timeAppear - lastTime;
            if (i > 0) {
                beforeNote = datas[i - 1];
                if (beforeNote.cachedNextLongNote != null && beforeNote.cachedNextLongNote.Length != 0) {
                    foreach (LongNoteNodeData cachedLongNoteData in beforeNote.cachedNextLongNote) {
                        distance -= cachedLongNoteData.Distance;
                    }
                }
            }

            note.distance = distance;
            lastTime = note.timeAppear;
            if (distance < minDistance && distance >= filterMinTime && i > 1) {
                minDistance = distance;
            }
        }
    }

    private void UpdateDuration(List<NoteData> datas) {
        for (int i = 0; i < datas.Count - 1; i++) {
            NoteData noteCurrent = datas[i];
            NoteData noteBehind = datas[i + 1];
            float distance = noteBehind.timeAppear - noteCurrent.timeAppear;

            if (distance < noteCurrent.duration) {
                noteCurrent.duration = distance;
            }
        }
    }

    void UpdateNoteForRhythmTool() {
        for (int i = 0; i < noteCount - 1; i++) {
            NoteData note = noteDatas[i];
            NoteData noteNext = noteDatas[i + 1];
            //If duration > rhythmToolMinDuration, insert some notes to middle
            if (noteNext.distance > remoteConfig.rhythmToolMaxDuration) {
                int total = (int) Mathf.Ceil(noteNext.distance / remoteConfig.rhythmToolMaxDuration);
                float distance = noteNext.distance / total;
                float timeAppear = note.timeAppear;
                for (int j = 0; j < total - 1; j++) {
                    timeAppear += distance;
                    NoteData newNote = new NoteData { timeAppear = timeAppear, distance = distance };
                    noteDatas.Insert(i + 1, newNote);
                    i++;
                }

                noteNext.distance = distance;
            }
        }
    }

    public string NotesToString() {
        string noteEditText = "";
        for (int i = 0; i < noteCount; i++) {
            noteEditText += System.Math.Round(noteDatas[i].timeAppear, 2) + " ";
        }

        Logger.Log(noteCount);
        return noteEditText;
    }

    public void StringToNotes(string data) {
        data = data.Replace("\n", " ");
        string[] strNotes = data.Split(' ');
        int strCount = strNotes.Length;
        noteDatas.Clear();
        float lastTime = 0;
        for (int i = 0; i < strCount; i++) {
            if (!string.IsNullOrEmpty(strNotes[i])) {
                NoteData note = new NoteData();
                float timeAppear = 0;
                float.TryParse(strNotes[i].Trim(), out timeAppear);
                note.timeAppear = timeAppear;
                if (note.timeAppear - lastTime > 15) {
                    break;
                }

                if (note.timeAppear > 0) //&& note.timeAppear < song.length)
                {
                    noteDatas.Add(note);
                    lastTime = note.timeAppear;
                }
            }
        }

        noteDatas.Sort((x, y) => x.timeAppear.CompareTo(y.timeAppear));
    }

    GridSnap GetGridSnapByID(int id) {
        switch (id) {
            case 4:
                return GridSnap.OneByFour;

            case 8:
                return GridSnap.OneByEight;

            case 12:
                return GridSnap.OneByTwelve;

            case 16:
                return GridSnap.OneBySixTeen;

            case 24:
                return GridSnap.OneByTwentyFour;

            case 32:
                return GridSnap.OneByThirtyTwo;

            case 48:
                return GridSnap.OneByFortyEight;

            case 64:
                return GridSnap.OneBySixtyFour;

            default:
                return GridSnap.OneByEight;
        }
    }

    private MidiTrackData GetTrackName(FullMidiContent fullMidiContent, TrackName trackName) {
        string strTrackName = trackName.ToString();
        foreach (MidiTrackData track in fullMidiContent.Tracks) {
            if (track.TrackName.Equals(strTrackName)) {
                return track;
            }
        }

        return null;
    }

    public bool IsLoadedNoteData(Song s) {
        if (song.path != s.path) {
            return false;
        }

        if (noteCount == 0) {
            return false;
        }

        //Nếu đã thay đổi từ tutorial -> none tutorial thì k nên cached lại nữa
        if (song.isTutorialSong != s.isTutorialSong) {
            return false;
        }

        // Nếu đã thay đổi độ khó so với ban đầu ->> cần load lại bài hát. Đoạn này k nên cached data
        if (remoteConfig.NotesDifficult_IsEnable && GetDifficultiesOfCurrentSong() == Difficulties.Endless && isBpm) {
            return false;
        }

        // TH-3028: load lại note data khi chơi lại cùng một bài hát Midi v2 hoặc force midi control
        if (isMidiGenerateTile || HasNewElements()) {
            return false;
        }

        return true;
    }

    public bool NeedReloadNoteData(Song s) {
        if (!IsLoadedNoteData(s))
            return true;
        if (string.IsNullOrEmpty(_lastMidiContentId) || !_lastMidiContentId.Equals(s.binContentID))
            return true;

        if (CheckExistNewElements()) {
            return true;
        }

        return false;
    }

    public void UpdateLastNoteData(bool enableEndless) { //TH-395
        if (noteDatas == null || noteDatas.Count == 0) {
            return;
        }

        int ballId = Ball.b.BallId;
        bool isHumanPlayer = BallManager.itemsHuman.Contains(ballId);
        if (isHumanPlayer && !enableEndless) {
            NoteData lastNoteData = noteDatas[noteCount - 1];
            if (lastNoteData.nodeID != NoteData.NoteIDLastNoteForHuman) {
                NoteData note = new NoteData {
                    distance = 1,
                    timeAppear = lastNoteData.timeAppear + lastNoteData.distance,
                    nodeID = NoteData.NoteIDLastNoteForHuman
                };

                if (song.isMusicalizationType) {
                    note.timbre = lastNoteData.timbre;
                    note.mood = lastNoteData.mood;
                }

                noteDatas.Add(note);
            }
        } else {
            NoteData lastNoteData = noteDatas[noteCount - 1];
            if (lastNoteData.nodeID == NoteData.NoteIDLastNoteForHuman) {
                noteDatas.Remove(lastNoteData);
            }
        }
    }

    public void UpdateLastNoteData(int ballId) {
        if (noteDatas == null || noteDatas.Count == 0 || GameController.enableEndless) {
            return;
        }

        bool isHumanPlayer = BallManager.itemsHuman.Contains(ballId);
        NoteData lastNoteData = noteDatas[noteCount - 1];
        lastNoteData.nodeID = isHumanPlayer ? NoteData.NoteIDLastNoteForHuman : NoteData.NoteIDLastNote;
    }

    public void UpdateLastNoteDataAfterTryNewSkin(bool isHumanPlayer) {
        if (noteDatas == null || noteDatas.Count == 0 || GameController.enableEndless) {
            return;
        }

        NoteData lastNoteData = noteDatas[noteCount - 1];
        lastNoteData.nodeID = isHumanPlayer ? NoteData.NoteIDLastNoteForHuman : NoteData.NoteIDLastNote;
    }

    #region LevelBot

    private List<NoteData> GetNotesByLevelBot(byte[] rawBytes) {
        LevelBotGenerator gen = new LevelBotGenerator(rawBytes);
        SongMeta songMeta = gen.GetSongMeta();
        song.bmp = songMeta.BPM;
        TPMSettings settings = TPMSettings.GetNormalSettings();
        TPMControl tpmControl = new TPMControl();
        List<LevelBot.NoteData> genNotes = tpmControl.GetTPMNotes(gen, settings);
        settings.UpdateCalculatedTPMAndAVGTPM(genNotes);
        settings.UpdateMaxMinEffectiveTPM();

        List<NoteData> inwaveNotes = NoteData.ConvertToInwaveNotes(genNotes);

        //~~~~~~~~~~~~~~ timbre ~~~~~~~~~~~~~~
        inwaveNotes = AddTimbre(inwaveNotes, genNotes);

        //~~~~~~~~~~~~~~ pitch ~~~~~~~~~~~~~~
        PitchLaneArranger arranger = new PitchLaneArranger(5, 1);
        int[] lanes = arranger.GetLaneOfNoteByPitch(genNotes);
        inwaveNotes = AddPitch(inwaveNotes, lanes);

        //~~~~~~~~~~~~~~ intensity ~~~~~~~~~~~~~~
        inwaveNotes = AddIntensity(inwaveNotes, gen, genNotes);

        //~~~~~~~~~~~~~~ mood change ~~~~~~~~~~~~~~
        inwaveNotes = AddMoodChange(inwaveNotes, gen);

        if (inwaveNotes.Count <= 10) {
            CustomException.Fire("[GetNotesByLevelBot] Notes.Count <= 10",
                "name => " + song.name + " acmID => " + song.acm_id_v3 ?? "");
        }

        return inwaveNotes;
    }

    private List<NoteData> AddMoodChange(List<NoteData> inwaveNotes, LevelBotGenerator gen) {
        NovelSegmentData[] novelSegmentData = gen.NovelSegmentData;

        List<float> listArousal = ListPool<float>.Get();
        foreach (NovelSegmentData segmentData in novelSegmentData) {
            listArousal.Add(segmentData.Arousal);
        }

        int[] groupingAscend = LevelBotUtil.GetGoupingAscend(4, listArousal);
        ListPool<float>.Release(listArousal);

        int indexPartMood = 0;
        for (int index = 0; index < inwaveNotes.Count; index++) {
            NoteData noteData = inwaveNotes[index];
            //Chuyển sang part mood tiếp theo nếu time appear vượt qua
            while (indexPartMood < novelSegmentData.Length - 1 &&
                   noteData.timeAppear >= novelSegmentData[indexPartMood + 1].Time) {
                indexPartMood += 1;
            }

            NoteData.Mood mood = (NoteData.Mood) groupingAscend[indexPartMood];
            noteData.mood = mood;
        }

        return inwaveNotes;
    }

    private List<NoteData> AddIntensity(List<NoteData> inwaveNotes, LevelBotGenerator gen,
                                        List<LevelBot.NoteData> genNotes) {
        BeatData[] beat = gen.BeatOnset;
        List<BeatData> downbeats = new List<BeatData>(beat.Length);
        foreach (BeatData b in beat) {
            if (b.Label == 1) {
                downbeats.Add(b);
            }
        }

        List<BeatData> strongestbeat30Per = LevelBot.NoteData.FilterToNotePercentageByConfidence(downbeats, 0.3f);

        for (int index = 0; index < genNotes.Count; index++) {
            LevelBot.NoteData note = genNotes[index];
            float validTime = note.Time - 0.1f; // Allow 100ms delay
            int idx = LevelBot.NoteData.BinarySearchIndexAfterNoteTime(strongestbeat30Per, validTime);
            if (idx == -1) {
                continue;
            }

            float distance = Mathf.Abs(strongestbeat30Per[idx].Time - note.Time);
            if (distance <= 0.1f) {
                inwaveNotes[index].intensity = NoteData.Intensity.Strong;
            }
        }

        return inwaveNotes;
    }

    private List<NoteData> AddTimbre(List<NoteData> inwaveNotes, List<LevelBot.NoteData> genNotes) {
        for (int index = 0; index < inwaveNotes.Count; index++) {
            NoteData inwaveNote = inwaveNotes[index];
            NoteChannel noteChannel = genNotes[index].Channel;

            switch (noteChannel) {
                case NoteChannel.Vocal:
                    inwaveNote.timbre = NoteData.Timbre.Vocal;
                    break;

                case NoteChannel.Melody:
                    inwaveNote.timbre = NoteData.Timbre.Melodic;
                    break;

                default:
                    inwaveNote.timbre = NoteData.Timbre.Rhythmic;
                    break;
            }
        }

        return inwaveNotes;
    }

    private List<NoteData> AddPitch(List<NoteData> notes, int[] lanes) {
        int maxIndex = Mathf.Min(notes.Count, lanes.Length);

        for (int index = 0; index < maxIndex; index++) {
            NoteData note = notes[index];
            int lane = lanes[index]; //0 1 2 3 4
            if (0 <= lane && lane <= 4) {
                note.pitch = (NoteData.Pitch) lane;
            } else {
                note.pitch = NoteData.Pitch.None;
            }
        }

        return notes;
    }

    #endregion

    private List<Vector2> GetTimeLongNote(List<NoteData> notes, float longTime) {
        List<Vector2> longNotes = new List<Vector2>();

        for (int index = 1; index < notes.Count; index++) {
            NoteData noteData = notes[index];
            if (noteData.distance >= longTime) {
                NoteData preNote = notes[index - 1];
                Vector2 v = new Vector2(preNote.timeAppear, noteData.timeAppear);
                longNotes.Add(v);
            }
        }

        return longNotes;
    }

    private List<NoteData> FilterNoteByLongTime(List<NoteData> datas, List<Vector2> longTime) {
        for (int index = 1; index < datas.Count; index++) {
            NoteData noteData = datas[index];
            bool isValidNote = IsValidNote(noteData, longTime);
            if (!isValidNote) {
                datas.Remove(noteData);
                index--;
            }
        }

        return datas;
    }

    private bool IsValidNote(NoteData noteData, List<Vector2> longTime) {
        float timeAppear = noteData.timeAppear;
        for (int index = 0; index < longTime.Count; index++) {
            Vector2 time = longTime[index];
            if (time.x < timeAppear && timeAppear < time.y) {
                return false;
            }

            if (timeAppear < time.x) {
                break;
            }
        }

        return true;
    }

    #region Level Difficulties

    public Difficulties GetDifficultiesOfCurrentSong(bool isCheckSelectOfUser = true) {
        if (!remoteConfig.NotesDifficult_IsEnable) {
            return Difficulties.None;
        }

        var difficulty = GetSavedSongDifficult();
        if (difficulty == Difficulties.None) {
            difficulty = GetCurrentDifficultiesPlayer();
            if (difficulty == Difficulties.None) {
                return GameController.GetEndlessModeOfSong(isCheckSelectOfUser)
                    ? Difficulties.Endless
                    : Difficulties.Normal;
            }
        }

        return difficulty;
    }

    public Difficulties GetCurrentDifficultiesPlayer() {
        string s = PlayerPrefs.GetString(PlayerPrefsKey.CurrentDifficulties, Difficulties.None.ToString());
        if (!Enum.TryParse(s, out Difficulties currentDifficulties)) {
            currentDifficulties = Difficulties.None;
        }

        return currentDifficulties;
    }

    public void SetCurrentDifficultiesPlayer(Difficulties difficult) {
        Difficulty = difficult;
        PlayerPrefs.SetString(PlayerPrefsKey.CurrentDifficulties, difficult.ToString());
    }

    public void SavedSongDifficult(Difficulties difficult) {
        PlayerPrefs.SetString(PlayerPrefsKey.CurrentDifficulties + song.path, difficult.ToString());
    }

    private Difficulties GetSavedSongDifficult() {
        string s = PlayerPrefs.GetString(PlayerPrefsKey.CurrentDifficulties + song.path, Difficulties.None.ToString());
        if (!Enum.TryParse(s, out Difficulties dif)) {
            dif = Difficulties.None;
        }

        return dif;
    }

    public bool IncreaseDifficulties() {
        Difficulties currentDifficult = GetCurrentDifficultiesPlayer();
        ;
        switch (currentDifficult) {
            case Difficulties.Easy:
                SetCurrentDifficultiesPlayer(Difficulties.Normal);
                return true;

            case Difficulties.Normal:
                SetCurrentDifficultiesPlayer(Difficulties.Hard);
                return true;

            case Difficulties.Hard:
                //nothing
                return false;

            case Difficulties.Endless:
                SetCurrentDifficultiesPlayer(Difficulties.Hard);
                return false;

            default:
                return false;
        }
    }

    public bool DecreaseDifficulties() {
        Difficulties currentDifficult = GetCurrentDifficultiesPlayer();
        switch (currentDifficult) {
            case Difficulties.Easy:
                //nothing
                return false;

            case Difficulties.Normal:
                SetCurrentDifficultiesPlayer(Difficulties.Easy);
                return true;

            case Difficulties.Hard:
            case Difficulties.Endless:
                SetCurrentDifficultiesPlayer(Difficulties.Normal);
                return true;

            default:
                return false;
        }
    }

    #endregion

    private List<NoteData> UpdateMoodFromMidi(List<NoteData> bpmNotes, List<NoteData> midiNotes) {
        List<NoteData> moodChange = GetMoodChange(midiNotes);

        int indexMood = 0;
        foreach (NoteData noteData in bpmNotes) {
            if (indexMood < moodChange.Count - 1 && noteData.timeAppear > moodChange[indexMood + 1].timeAppear) {
                indexMood++;
            }

            noteData.mood = moodChange[indexMood].mood;
        }

        return bpmNotes;
    }

    private List<NoteData> UpdateMoodFromBpm(List<NoteData> bpmNotes) {
        float timeOfPart = bpmNotes[bpmNotes.Count - 1].timeAppear / 4f;
        int moodIndex = 0;
        int i = 0;
        NoteData.Mood mood = NoteData.Mood.Mild;
        while (moodIndex < 4) {
            if (moodIndex == 0)
                mood = NoteData.Mood.Mild;
            else if (moodIndex == 1)
                mood = NoteData.Mood.Medium;
            else if (moodIndex == 2)
                mood = NoteData.Mood.Strong;
            else if (moodIndex == 3)
                mood = NoteData.Mood.VeryStrong;

            while (bpmNotes[i].timeAppear < (moodIndex + 1) * timeOfPart) {
                bpmNotes[i].mood = mood;
                i++;
            }

            moodIndex++;
        }

        return bpmNotes;
    }

    private List<NoteData> GetMoodChange(List<NoteData> midiNotes) {
        List<NoteData> mood = new List<NoteData>();

        for (int index = 0; index < midiNotes.Count; index++) {
            NoteData midiNote = midiNotes[index];
            if (index == 0) {
                mood.Add(midiNote);
            } else {
                NoteData lastMood = mood[mood.Count - 1];
                if (lastMood.mood != midiNote.mood) {
                    mood.Add(midiNote);
                }
            }
        }

        return mood;
    }

    public static bool IsMusicalizationContent(byte[] data, bool isBinContent = true) {
        FullMidiContent content = null;
        try {
            if (isBinContent) {
                // Read bin
                content = NoteGeneration.GetAllTrackNotes(data);
            } else {
                // Read midi
                content = ContentNoteGenerator.GetAllTrackNotes(data);
            }
        } catch {
            // Load Musicalization failed => normal content
        }

        if (content != null && content.Tracks != null && content.Tracks.Count != 0) {
            foreach (var item in content.Tracks) {
                if (item.TrackName.Equals("main")) {
                    return true;
                }
            }

            return false;
        } else {
            return false;
        }
    }

    public (string midiType, int totalNote, int longNote, int shortNote, int strongNote, int moodchange, bool
        is_new_element, float longNoteDuration) AnalyzeData() {
        string midiType = song.GetMidiType().ToString();
        int totalNote = noteCount;
        int longNote = 0;
        int shortNote = 0;
        int strongNote = 0;
        byte moodchange = 0;

        float shortNoteDistance = 0;
        if (remoteConfig.Musicalization_EnableShortNote) {
            shortNoteDistance = song.shortNoteDistance;
            if (shortNoteDistance <= 0) {
                shortNoteDistance = remoteConfig.Musicalization_ShortNoteDistance;
            }

            if (shortNoteDistance > 20) { //means shortNoteDistance is millisecond
                shortNoteDistance /= 1000;
            }
        }

        float longNoteDuration = song.GetLongDuration();

        for (int i = 0; i < totalNote; i++) {
            var note = noteDatas[i];
            if (note.distance < shortNoteDistance) {
                shortNote++;
            }

            if (note.duration >= longNoteDuration) {
                longNote++;
            }

            if (note.intensity == NoteData.Intensity.Strong) {
                strongNote++;
            }

            if (i >= 6 && i != totalNote - 1) {
                if (note.mood != noteDatas[i + 1].mood) {
                    moodchange++;
                }
            }
        }

        return (midiType, noteCount, longNote, shortNote, strongNote, moodchange, HasElementType(), longNoteDuration);
    }

    public float GetShortNoteDistance() {
        float shortNoteDistance = 0;
        if (RemoteConfigBase.instance.Musicalization_EnableShortNote) {
            shortNoteDistance = song.shortNoteDistance;
            if (shortNoteDistance <= 0) {
                shortNoteDistance = RemoteConfigBase.instance.Musicalization_ShortNoteDistance;
            }

            if (shortNoteDistance > 20) { //means shortNoteDistance is millisecond
                shortNoteDistance /= 1000;
            }
        }

        return shortNoteDistance;
    }

    public float GetLongNoteDuration() {
        if (isMidiGenerateTile || remoteConfig.LongNote_v2_IsEnable) { //read by midi
            return LongDurationValue;
        }

        float longNoteDuration = song.longNoteDuration;
        if (longNoteDuration <= 0) {
            longNoteDuration = RemoteConfigBase.instance.Musicalization_LongNoteDuration;
        }

        if (longNoteDuration > 20) { //means longNoteDuration is millisecond
            longNoteDuration /= 1000;
        }

        return longNoteDuration;
    }

    #region Special Tile & Section

    private (int start, int end) UpdateTileReverse(List<NoteData> notes, int start = -1, int end = -1, int range = -1) {
        float timeEnd = end > 0 ? notes[end].timeAppear : -1;
        int indexNote = start > 0 ? start : 10;
        int count = range > 0 ? range : 20;
        float time = 3;

        notes[indexNote].elementType = NoteElementType.SpecialUpsideDown;
        for (int index = 1; index < notes.Count - 1; index++) {
            float appear = notes[indexNote + index].timeAppear - notes[indexNote].timeAppear;
            if (appear < time) {
                notes.RemoveAt(indexNote + index);
                index--;
            } else {
                break;
            }
        }

        int indexReverse2 = indexNote + count;
        if (timeEnd > 0) {
            for (int index = 1; index < notes.Count - 1; index++) {
                if (notes[index].timeAppear >= timeEnd && notes[index - 1].timeAppear < timeEnd) {
                    indexReverse2 = index;
                    break;
                }
            }
        }

        notes[indexReverse2].elementType = NoteElementType.SpecialUpsideDown;
        for (int index = 1; index < notes.Count - 1; index++) {
            float appear = notes[indexReverse2 + index].timeAppear - notes[indexReverse2].timeAppear;
            if (appear < time) {
                notes.RemoveAt(indexReverse2 + index);
                index--;
            } else {
                break;
            }
        }

        return (indexNote, indexReverse2);
    }

    private bool CheckExistNewElements() {
        if (song.isTutorialSong) {
            return false;
        }

        if (song.isChallengeSong) {
            return remoteConfig.NewElements_Enable_HC_CS;
        }

        if (song.isHardcoreSong) {
            return remoteConfig.NewElements_Enable_HC_HS;
        }

        if (StarsJourneyManager.isEnable) {
            return true;
        }

        if (!remoteConfig.NewElements_Enable) {
            return false;
        }

        if (Configuration.instance.isSpecialTileControl || Configuration.instance.isSpecialTileV2) {
            return true;
        }

        if (remoteConfig.NewElements_AfterSongStart > 0) {
            int songStart = AnalyticHelper.CountEvent(SONG_STATUS.song_start.ToString());
            if (songStart < remoteConfig.NewElements_AfterSongStart) {
                return false;
            }
        }

        return true;
    }

    private bool MidiGenAutoElements() {
        if (song.isChallengeSong) {
            return remoteConfig.NewElements_Enable_HC_CS;
        }

        if (song.isHardcoreSong) {
            return remoteConfig.NewElements_Enable_HC_HS;
        }

        return remoteConfig.NewElements_MidiAutoGenElement_Enable;
    }

    public bool HasNewElements() {
        return _isExistNewElements;
    }

    public bool IsFollowSpecialTileRule() {
        if (!HasNewElements()) {
            return false;
        }

        if (remoteConfig.NewElements_OverLapOtherRule) {
            return true;
        }

        if (isMidiGenerateTile)
            return true;

        if (Configuration.instance.isSpecialTileControl || Configuration.instance.isSpecialTileV2) {
            return true;
        }
        
        if (_autoGenByUnlockElement != null || _autoGenBySingleRule!=null) {
            if (remoteConfig.NewElements_FakeTile_UsingGameStage || remoteConfig.NewElements_MovingTile_UsingGameStage) {
                return false;
            } else {
                return true;
            }
        }
        
        if (_autogenByLearningCurve != null && _autogenByLearningCurve.IsExist())
            return true;

        if (_listSpecialTiles.Count != 0) {
            return true;
        }

        return false;
    }

    public bool HasElementType() {
        if (!HasNewElements()) {
            return false;
        }

        if (!_listSpecialTiles.IsNullOrEmpty()) {
            return true;
        }

        return false;
    }

    private void CustomGenerateSpecialTile() {
        PrepareMoodChange();
        if (Configuration.instance.isSpecialTileControl) {
            GenerateSpecialTileByAdmin();
        } else if (!Configuration.GetListAutoGenElements().IsNullOrEmpty()) {
            GenerateSpecialTileBySingleRule();
        } else if (StarsJourneyManager.isEnable) {
            GenerateSpecialTileByUnlockElement();
        } else if (remoteConfig.NewElements_RuleShow_Enable) {
            GenerateSpecialTileByLearningCurve();
        } else {
            GenerateSpecialTileByDifficulty();
        }
    }

    private void IncreaseMoodChange() {
        int intervalMoodChange = 30;
        int idMood = 0;
        int temp = 0;
        float shortDistance = GetShortNoteDistance();
        float longDuration = GetLongNoteDuration();
        int totalNote = noteDatas.Count;

#if UNITY_EDITOR
        List<int> listMoodChange = new List<int>();
#endif
        for (int i = 0; i < totalNote; i++) {
            temp++;
            noteDatas[i].mood = (NoteData.Mood) (idMood);
            if (temp >= intervalMoodChange) {
                if (i > totalNote - 10)
                    continue;
                if (noteDatas[i].distance < shortDistance)
                    continue;
                if (noteDatas[i].duration >= longDuration)
                    continue;

                idMood++;
                idMood %= Enum.GetValues(typeof(NoteData.Mood)).Length;
                temp = 0;

#if UNITY_EDITOR
                listMoodChange.Add(i);
#endif
            }
        }

#if UNITY_EDITOR
        Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
            $"Auto add mood change : {string.Join(";", listMoodChange)}");
#endif
    }

    [ShowInInspector, ReadOnly] private NewTilesByDifficulty _autoGenByDifficulty;

    private void GenerateSpecialTileByDifficulty() {
        var difficulty = song.GetDifficultyTag();
        Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
            $"Auto gen by <color=red>Difficulty</color> : {difficulty}");

        _autoGenByDifficulty ??= new NewTilesByDifficulty();
        _autoGenByDifficulty.ResetData(difficulty, remoteConfig.NewElements_AutoGenElement_Max);
        var special = _autoGenByDifficulty.GetSpecialSection(noteDatas.Count);
        if (special.type != IngameSectionType.Normal) {
            CustomGenerateSpecialSection(special.type, special.amount);
        }

        _autoGenByDifficulty.Process(ref noteDatas, SpecialSection_Start, SpecialSection_End, special.type,
            ref _listSpecialTiles, GetLongNoteDuration());
    }

    [ShowInInspector, ReadOnly] private NewTilesBySingleRule _autoGenBySingleRule;

    private void GenerateSpecialTileBySingleRule() {
        Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Auto gen by <color=red>Single Rule</color>");
        _autoGenBySingleRule ??= new NewTilesBySingleRule();
        _autoGenBySingleRule.ResetData();
        _autoGenBySingleRule.Process(ref noteDatas, ref _listSpecialTiles, GetLongNoteDuration());
        if (_autoGenBySingleRule.hasSpecialSection) {
            SpecialSection_Start = _autoGenBySingleRule.specialSectionStart;
            SpecialSection_End = _autoGenBySingleRule.specialSectionEnd;
            SpecialSection_Type = ElementTypeToSectionType(_autoGenBySingleRule.specialSectionType);
        }
    }

    [ShowInInspector, ReadOnly] private NewTilesByUnlockElement _autoGenByUnlockElement;

    private void GenerateSpecialTileByUnlockElement() {
        Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Auto gen by <color=red>Unlock Element</color>");
        _autoGenByUnlockElement ??= new NewTilesByUnlockElement();
        _autoGenByUnlockElement.ResetData();
        _autoGenByUnlockElement.Process(ref noteDatas, ref _listSpecialTiles, GetLongNoteDuration());
        if (_autoGenByUnlockElement.hasSpecialSection) {
            SpecialSection_Start = _autoGenByUnlockElement.specialSectionStart;
            SpecialSection_End = _autoGenByUnlockElement.specialSectionEnd;
            SpecialSection_Type = ElementTypeToSectionType(_autoGenByUnlockElement.specialSectionType);
        }
    }

    private void GenerateSpecialTileByAdmin() {
        Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Auto gen by <color=red>Admin</color>");
        int id = Configuration.GetSpecialSectionIndex();
        if (id >= 0) {
            CustomGenerateSpecialSection((IngameSectionType) id);
        }

        var listElements = Configuration.GetSpecialTiles();
        if (listElements != null) {
            foreach (var element in listElements) {
                SetSpecialTile(ElementTypeToSpecialTileType((SpecialTileIndex) element),
                    remoteConfig.NewElements_RuleShow_OldElementPercent);
            }
        }

        var listMoodChange = Configuration.GetSpecialMoodChange();
        if (listMoodChange != null && listMoodChange.Count != 0) {
            SetMoodChangeTile(listMoodChange);
        }
    }

    private SpecialTileConfig _autogenByLearningCurve;

    private void GenerateSpecialTileByLearningCurve() {
        Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Auto gen by <color=red>LearningCurve</color>");
        _autogenByLearningCurve ??= new SpecialTileConfig(remoteConfig.NewElements_RuleShow_Order,
            remoteConfig.NewElements_RuleShow_SongCount);
        int countEventSongEnd = AnalyticHelper.CountEvent(SONG_STATUS.song_end.ToString());
        bool isDone = false;
        var _listElements = new List<NoteElementType>();
         NoteElementType targetElement;
        do {
            targetElement = _autogenByLearningCurve.GetNewElementType(countEventSongEnd, _listElements);
            if (targetElement.IsSpecialSection()) {
                CustomGenerateSpecialSection(ElementTypeToSectionType(targetElement));
                isDone = SpecialSection_Type != IngameSectionType.Normal;
            } else {
                isDone = SetSpecialTile(targetElement, remoteConfig.NewElements_RuleShow_NewElementPercent);
            }

            if (!isDone) {
                _listElements.Add(targetElement);
                //Logger.EditorLogError(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"Can't show {_targetElement} at song count {countEventSongEnd}");
            }
        } while (targetElement != NoteElementType.None && !isDone);

        IngameSectionType special = IngameSectionType.Normal;
        // bổ sung special section
        if (!targetElement.IsSpecialSection()) {
            special = _autogenByLearningCurve.GetAvailableSpecialSection();
            if (special != IngameSectionType.Normal) {
                CustomGenerateSpecialSection(special);
            }
        }

        // bổ sung special tile
        _listElements = _autogenByLearningCurve.GetAvailableElements();
        if (_listElements.Contains(NoteElementType.MovingCircle)) {
            //ưu tiên moving circle vì khó thỏa mãn điều kiện
            _listElements.Remove(NoteElementType.MovingCircle);
            _listElements.Insert(0, NoteElementType.MovingCircle);
        }

        foreach (var element in _listElements) {
            SetSpecialTile(elementType: element, remoteConfig.NewElements_RuleShow_OldElementPercent);
        }

        if (isDone) {
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                $"New feature {targetElement} at song count {countEventSongEnd}");
            _autogenByLearningCurve.ShowElementType(targetElement, countEventSongEnd);
        }

        if (special != IngameSectionType.Normal) {
            _listElements.Insert(0, SectionTypeToElementType(special));
        }
    }

    public HashSet<NoteElementType> GetAvailableElementTypes() {
        return _listSpecialTiles;
    }

    private bool SetSpecialTile(NoteElementType elementType, int percent) {
        if (elementType == NoteElementType.None)
            return false;

#if UNITY_EDITOR
        List<int> idNotes = new List<int>();
#endif
        int totalNote = noteDatas.Count;
        bool setElement = false;
        if (percent <= 0)
            percent = 15;
        for (int i = 1; i < totalNote - 1; i++) {
            if (noteDatas[i].elementType != NoteElementType.None) {
                //already another type
                continue;
            }

            if (noteDatas[i - 1].elementType == NoteElementType.MovingCircle) { //note before is moving circle
                //already another type
                continue;
            }

            if (SpecialSection_Type != IngameSectionType.Normal) {
                if (i >= SpecialSection_Start - 1 && i <= SpecialSection_End + 1) {
                    continue;
                }
            }

            if (i <= 6 || i >= totalNote - 6) {
                continue;
            }

            if (i <= 10 || i >= totalNote - 10) {
                if (elementType.IsFakeElement()) {
                    continue;
                }
            }

            if (elementType.IsLongTileElement()) {
                if (noteDatas[i].duration < LongDurationValue) {
                    continue;
                }

                // check tiếp
                SetElement(i);
            } else if (elementType.IsMoodChangeElement()) {
                if (noteDatas[i].mood != noteDatas[i + 1].mood) {
                    SetElement(i);
                }
            } else {
                if (noteDatas[i].mood != noteDatas[i + 1].mood) { //mood change
                    continue;
                }

                if (noteDatas[i].duration >= LongDurationValue) { // long tile
                    continue;
                }

                if (noteDatas[i].distance < ShortDistanceValue) { // short distance
                    continue;
                }

                if (elementType == NoteElementType.MovingCircle) {
                    //check next tile
                    var nextNote = noteDatas[i + 1];

                    if (nextNote.elementType != NoteElementType.None) { //special
                        continue;
                    }

                    if (nextNote.mood != noteDatas[i + 2].mood) { //mood change
                        continue;
                    }

                    if (nextNote.duration > LongDurationValue) { //long tile
                        continue;
                    }

                    if (nextNote.distance < ShortDistanceValue) { //too close
                        continue;
                    }

                    if (noteDatas[i + 2].distance < ShortDistanceValue) {
                        //note liền sau 2 movingtile quá sát -> user sẽ k kịp move -> cần loại bỏ
                        continue;
                    }

                }

                SetElement(i);
            }
        }

        if (setElement) {
            _listSpecialTiles.Add(elementType); //normal specialTile
#if UNITY_EDITOR
            string temp = "";
            foreach (var note in idNotes) {
                temp += $"{note}, ";
            }

            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT, $"[{elementType}] {temp}");
#endif
        }

        return setElement;

        void SetElement(int i) {
            if (setElement) {
                int random = Random.Range(0, 9999) % 100;
                if (random > percent) {
                    return;
                }
            } else {
                setElement = true;
            }

            noteDatas[i].elementType = elementType;
            if (elementType == NoteElementType.FakeTile) {
                switch (noteDatas[i].pitch) {
                    case NoteData.Pitch.None:
                        noteDatas[i].fakeTile = NoteData.FakeTile.NONE;
                        break;

                    case NoteData.Pitch.Line1:
                        noteDatas[i].fakeTile = NoteData.FakeTile.MIDDLE;
                        break;

                    case NoteData.Pitch.Line2:
                        noteDatas[i].fakeTile = NoteData.FakeTile.MIDDLE_RIGHT;
                        break;

                    case NoteData.Pitch.Line3:
                        noteDatas[i].fakeTile = NoteData.FakeTile.LEFT | NoteData.FakeTile.RIGHT;
                        break;

                    case NoteData.Pitch.Line4:
                        noteDatas[i].fakeTile = NoteData.FakeTile.MIDDLE_LEFT;
                        break;

                    case NoteData.Pitch.Line5:
                        noteDatas[i].fakeTile = NoteData.FakeTile.MIDDLE;
                        break;
                }
            } else if (elementType == NoteElementType.MovingTile) {
                noteDatas[i].moving = GetRandomMovingTile();
            }

#if UNITY_EDITOR
            idNotes.Add(i);
#endif
        }
    }

    private void SetMoodChangeTile(List<NoteElementType> elementTypes) {
        int totalNote = noteDatas.Count;
        for (int i = 6; i < totalNote - 1; i++) {
            if (noteDatas[i].mood != noteDatas[i + 1].mood) {
                SetElement(i);
            }
        }

        void SetElement(int i) {
            var elementType = elementTypes[Random.Range(0, 9999) % elementTypes.Count];
            noteDatas[i].elementType = elementType;
        }
    }

    private void ResetSpecialSection() {
        SpecialSection_Type = IngameSectionType.Normal;
        SpecialSection_Start = -1;
        SpecialSection_End = -1;
    }

    private static NoteElementType ElementTypeToSpecialTileType(SpecialTileIndex elementType) {
        switch (elementType) {
            case SpecialTileIndex.FadeOut:
                return NoteElementType.FadeOut;

            case SpecialTileIndex.FadeInOut:
                return NoteElementType.FadeInOut;

            case SpecialTileIndex.FakeThunder:
                return NoteElementType.FakeThunder;

            case SpecialTileIndex.FakeConveyor:
                return NoteElementType.FakeConveyor;

            case SpecialTileIndex.MovingTeleport:
                return NoteElementType.Teleport;

            case SpecialTileIndex.MovingCircle:
                return NoteElementType.MovingCircle;

            case SpecialTileIndex.FakeTransform:
                return NoteElementType.FakeTransform;

            default:
                Logger.EditorLogError("NULLLLLLLLLLLLLL");
                return NoteElementType.None;
        }
    }

    public static IngameSectionType ElementTypeToSectionType(NoteElementType elementType) {
        switch (elementType) {
            case NoteElementType.SpecialMirror:
                return IngameSectionType.Mirror;

            case NoteElementType.SpecialUpsideDown:
                return IngameSectionType.UpsideDown;

            case NoteElementType.SpecialHyperBoost:
                return IngameSectionType.HyperBoost;

            case NoteElementType.SpecialZicZac:
                return IngameSectionType.ZicZac;

            default:
                Logger.EditorLogError("NULLLLLLLLLLLLLL");
                return IngameSectionType.Normal;
        }
    }

    private static NoteElementType SectionTypeToElementType(IngameSectionType sectionType) {
        switch (sectionType) {
            case IngameSectionType.Mirror:
                return NoteElementType.SpecialMirror;

            case IngameSectionType.UpsideDown:
                return NoteElementType.SpecialUpsideDown;

            case IngameSectionType.HyperBoost:
                return NoteElementType.SpecialHyperBoost;

            case IngameSectionType.ZicZac:
                return NoteElementType.SpecialZicZac;

            default:
                Logger.EditorLogError("NULLLLLLLLLLLLLL");
                return NoteElementType.None;
        }
    }

    private void ReCalculateInGameSpecialSectionByMiDiAfterFilterNote() {
        if (SpecialSection_Type == IngameSectionType.Normal)
            return;

        if (!HasNewElements()) {
            ResetSpecialSection();
            return;
        }

        NoteElementType elementType = SectionTypeToElementType(SpecialSection_Type);
        bool isValid = true;
        if (noteDatas[SpecialSection_Start].elementType != elementType) {
            bool foundReplace = false;
            //bị loại bớt note
            for (int i = SpecialSection_Start + 1; i >= 0; i--) {
                if (noteDatas[i].elementType == elementType) {
                    SpecialSection_End -= (SpecialSection_Start - i);
                    SpecialSection_Start = i;
                    foundReplace = true;
                    break;
                }
            }

            if (!foundReplace) {
                isValid = false;
            }
        }

        if (noteDatas[SpecialSection_End].elementType != elementType) {
            bool foundReplace = false;
            // bị dịch bớt note
            for (int i = SpecialSection_End + 1; i > SpecialSection_Start; i--) {
                if (noteDatas[i].elementType == elementType) {
                    SpecialSection_End = i;
                    foundReplace = true;
                    break;
                }
            }

            if (!foundReplace) {
                isValid = false;
            }
        }

        if (!isValid) {
            Logger.EditorLogError(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                $"Invalid data special section {SpecialSection_Type} after filter note ->> reset all !!!");
            ResetSpecialSection();
        } else {
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                $"Update data special section {SpecialSection_Type} [{SpecialSection_Start} -> {SpecialSection_End}]!!!");
            _existSpecialTileInMidi = true; // has special section
            _listSpecialTiles.Add(elementType); //midi specialSection
        }
    }

    private void CustomGenerateSpecialSection(IngameSectionType specialIndex, int range = -1) {
        ResetSpecialSection();

        switch (specialIndex) {
            case IngameSectionType.Mirror:
            case IngameSectionType.HyperBoost:
            case IngameSectionType.ZicZac:
                SpecialSection_Type = specialIndex;
                for (int index = noteDatas.Count / 2; index < noteDatas.Count - 10; index++) {
                    if (SpecialSection_Start < 0) {
                        if (noteDatas[index].duration >= LongDurationValue)
                            continue;
                        if (noteDatas[index].distance < ShortDistanceValue)
                            continue;

                        SpecialSection_Start = index;
                        index += range > 0 ? range : 15;
                    } else if (SpecialSection_End < 0) {
                        if (noteDatas[index].mood != noteDatas[index + 1].mood) {
                            SpecialSection_End = index - 1;
                            continue;
                        }

                        if (noteDatas[index].duration >= LongDurationValue)
                            continue;

                        SpecialSection_End = index;
                    } else {
                        break;
                    }
                }

                if (SpecialSection_Start > 0 && SpecialSection_End > 0) {
                    //qualify
                } else {
                    //not qualify -> reset
                    ResetSpecialSection();
                }

                break;

            case IngameSectionType.UpsideDown:
                SpecialSection_Type = specialIndex;
                var data = UpdateTileReverse(noteDatas, -1, -1, range); //TH-2637
                UpdateDistance(noteDatas);
                UpdateDuration(noteDatas);
                SpecialSection_Start = data.start;
                SpecialSection_End = data.end;
                break;
        }

        if (SpecialSection_Type != IngameSectionType.Normal) {
            _listSpecialTiles.Add(SectionTypeToElementType(SpecialSection_Type)); //normal specialSection
            noteDatas[SpecialSection_Start].elementType = SectionTypeToElementType(specialIndex);
            noteDatas[SpecialSection_End].elementType = SectionTypeToElementType(specialIndex);
            Logger.EditorLog(CONFIG_STRING.KEY_FEATURE_ELEMENT,
                $"[{SpecialSection_Type}] {SpecialSection_Start} -> {SpecialSection_End}");
        }
    }

    public static bool IsExistUpsideDown() {
        return instance.SpecialSection_Type == IngameSectionType.UpsideDown;
    }

    #endregion

    public NoteData.FakeTile GetRandomFake(NoteData.Pitch pitch) {
        switch (pitch) {
            case NoteData.Pitch.None:
                return NoteData.FakeTile.NONE;

            case NoteData.Pitch.Line1:
                return Random.value > 0.5f
                    ? NoteData.FakeTile.MIDDLE
                    : NoteData.FakeTile.MIDDLE | NoteData.FakeTile.RIGHT;

            case NoteData.Pitch.Line2:
                return NoteData.FakeTile.MIDDLE_RIGHT;

            case NoteData.Pitch.Line3:
                float random = Random.value;
                if (random < 0.33f)
                    return NoteData.FakeTile.LEFT;
                if (random >= 0.66f)
                    return NoteData.FakeTile.RIGHT;

                return NoteData.FakeTile.LEFT | NoteData.FakeTile.RIGHT;

            case NoteData.Pitch.Line4:
                return NoteData.FakeTile.MIDDLE_LEFT;

            case NoteData.Pitch.Line5:
                return Random.value > 0.5f
                    ? NoteData.FakeTile.MIDDLE
                    : NoteData.FakeTile.LEFT | NoteData.FakeTile.MIDDLE;
        }

        return NoteData.FakeTile.NONE;
    }

    public void UpdateListAutoGen(HashSet<NoteElementType> current) {
        _autoGenBySingleRule?.SetAutoList(current);
    }

    public string TrackElementType() {
        if (_autoGenByUnlockElement != null) {
            return _autoGenByUnlockElement.GetAllElement();
        }

        if (_autoGenBySingleRule != null) {
            return _autoGenBySingleRule.GetAllElement();
        }

        return string.Empty;
    }

    private void PlatformOnOnHit(Platform platform, int streakCount) {
        if (_listSpecialTiles.IsNullOrEmpty())
            return;

        NoteElementType elementType = NoteElementType.None;
        if (_listSpecialTiles.Contains(platform.elementType)) {
            elementType = platform.elementType;
        } else {
            //int special section
            int idNote = platform.noteID;
            if (SpecialSection_Type != IngameSectionType.Normal) {
                if (SpecialSection_Start < idNote && idNote < SpecialSection_End) {
                    elementType = SectionTypeToElementType(SpecialSection_Type);
                }
            }
        }

        if (elementType != NoteElementType.None) {
            int timePass = (int) GameController.instance.timePlay;
            NewElementsTracking.Track_NewElementHit(elementType, timePass, false);
        }
    }

    public void PlatformDead(int idLastNote) {
        if (_listSpecialTiles.IsNullOrEmpty())
            return;

        if (idLastNote > noteDatas.Count - 2)
            return;

        NoteElementType elementType = NoteElementType.None;
        if (_listSpecialTiles.Contains(noteDatas[idLastNote + 1].elementType)) {
            elementType = noteDatas[idLastNote + 1].elementType;
        } else {
            //int special section
            int idNote = idLastNote + 1;
            if (SpecialSection_Type != IngameSectionType.Normal) {
                if (SpecialSection_Start < idNote && idNote < SpecialSection_End) {
                    elementType = SectionTypeToElementType(SpecialSection_Type);
                }

                if (idNote < SpecialSection_Start) {
                    //too soon
                    _autoGenBySingleRule?.ResetRuleShowSpecialSection(false);
                } else {
                    _autoGenBySingleRule?.ResetRuleShowSpecialSection(true);
                }
            }
        }

        if (elementType != NoteElementType.None) {
            int timePass = (int) GameController.instance.timePlay;
            NewElementsTracking.Track_NewElementHit(elementType, timePass, true);
        }
    }

    public void GameComplete() {
        _autoGenBySingleRule?.ResetRuleShowSpecialSection(true);
        SavedSongDifficult(Difficulties.Endless);
    }

    public void PrepareMoodChange() {
        if (_increaseMoodChange) {
            return;
        }
        if (!song.isMusicalizationType) {
            IncreaseMoodChange();
        }

        _increaseMoodChange = true;
    }
}