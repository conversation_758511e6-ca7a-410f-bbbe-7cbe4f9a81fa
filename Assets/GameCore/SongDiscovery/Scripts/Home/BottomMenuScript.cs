using System;
using System.Buffers;
using System.Collections;
using System.Collections.Generic;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.UserProgression;
using TilesHop.DiscoveryChallenge;
using TilesHop.LiveEvent;
using UnityEngine;
using UnityEngine.UI;

public enum HomeTab {
    MAIN_HOME,
    SHOP_IAP,
    SHOP_BALL,
    SEARCH,
    STAR_JOURNEY,
    LIVE_EVENT,
}

public class BottomMenuScript : MonoBehaviour {
#if UNITY_ANDROID
    public static float heightBottomMenu = 75.1f;
#else
    public static float heightBottomMenu = 85f;
#endif
    public enum Type {
        Home,
        Search,
        Premium,
        LiveEvent,
        ShopIAP,
        ShopBall,
        StarJourney,
    }

    [SerializeField] protected MenuBottomButton menuHome;
    [SerializeField] protected MenuBottomButton menuSearch;
    
    [Header("Tab Shop IAP")]
    [SerializeField] protected MenuBottomButton menuShop;
    
    [Header("Tab shop ball")]
    [SerializeField] protected MenuBottomButton menuShopBall;
    
    protected MenuBottomButton      menuLiveEvent;
    protected MenuBottomButton      menuStarJourney;
    
    protected UIBottomMenuLiveEvent btnLiveEvent;
    
    protected UIBottomMenuTab       btnTabShopIap;
    protected UIBottomMenuTab       btnTabShopBall;
    protected UIBottomMenuTab       btnTabStarJourney;

    [SerializeField] private Transform groupButton;

    [SerializeField]
    protected RectTransform rectBottomMenu;

    [SerializeField] private RectTransform rectBottomMenuBg;

    [SerializeField] protected Color32 colorOn;
    [SerializeField] protected Color32 colorOff;

    public Action btnHomeOnClick;
    public Action btnSearchOnClick;
    public Action btnPremiumOnClick;
    public Action btnLiveEventOnClick;
    public Action btnShopDiamondOnClick;
    public Action btnShopBallOnClick;
    public Action btnStarJourneyOnClick;

    [SerializeField] private BlackBGTooltip bgToolTip;
    [SerializeField] private Image          decalImage;

    protected bool                 isShowingEvent;
    private   LiveEventFeatureType _eventType = LiveEventFeatureType.None;
    private   int                  _idLiveEventShowToolTip;

    protected Dictionary<HomeTab, int> tabOrderMapping;

    [HideInInspector] public bool isInited = false;

    public bool IsNeedShowExploreNotice =>
        RemoteConfig.instance.Onboarding_Home_ExploreNotice_Enable &&
        PlayerPrefs.GetInt(CONFIG_STRING.OnboardingFeature_ExploreAppear, 0) == 0 &&
        RemoteConfig.instance.Onboarding_Home_ExploreNotice_LevelAppear <= Configuration.GetGameLevel();
    
    public Transform TfSearch => menuSearch.mainButton.transform;

    public static bool IsLockedAction = false;

    public static bool isShopIapTabForm =>
        RemoteConfigBase.instance.ShopUI_BottomTab_Enable || UserProgressionController.EnableFeature;

    public static bool isShowBallTabForm =>
        UserProgressionController.EnableFeature || RemoteConfigBase.instance.Hybrid_UI_Rearrange;
    
    #region Unity Methods

    protected virtual void Awake() {
        menuHome.mainButton.onClick.AddListener(OnBtnHomeClick);
        menuSearch.mainButton.onClick.AddListener(OnBtnSearchClick);
        SetHeight();
        LocalizationManager.instance.OnLanguageChange += UpdateTextLiveEvent;
    }

    protected virtual void OnEnable() {
        BlackBGTooltip.OnClickAction += BlackBgTooltipOnOnClick;
        LiveEventManager.OnInitCompleted += LiveEvent_CheckActive;
        DiscoveryChallengeManager.OnEventStateChange += DiscoveryChallengeManagerOnOnEventStateChange;
        LiveEvent_CheckActive();
        CheckNoticeDiscover(DiscoveryChallengeManager.isInstanced
            ? DiscoveryChallengeManager.instanceSafe.EventState
            : ChallengeState.Disable);
        
        CheckToCreateButtonShopIAP();
        CheckToCreateButtonShopBall();
        CheckToCreateButtonStarJourney();
        CalculateSizeBottom();

        if (menuShop != null) {
            // free gems pack is not enable for VIP user
            bool isNeedToUpdateBadge = RemoteConfigBase.instance.ShopUI_Revamp_Enable &&
                Configuration.instance.IsShowFreeGemPack &&
                !SubscriptionController.IsSubscriptionVip() || RemoteConfigBase.instance.ShopDailyDeal_IsEnable;
        
            if (isNeedToUpdateBadge) {
                CheckNoticeShop();
            }
        }
        
        DailyDealShop.OnChangeState += CheckNoticeShop;
        IsLockedAction = false;
    }

    protected void Start() {
        if (decalImage != null) {
            UIHomeDecorationController.TryDecorBottomMenu(decalImage);
        }
    }

    protected virtual void OnDisable() {
        BlackBGTooltip.OnClickAction -= BlackBgTooltipOnOnClick;
        LiveEventManager.OnInitCompleted -= LiveEvent_CheckActive;
        DiscoveryChallengeManager.OnEventStateChange -= DiscoveryChallengeManagerOnOnEventStateChange;
        DailyDealShop.OnChangeState -= CheckNoticeShop;
    }

    protected virtual void OnDestroy() {
        if (LocalizationManager.instance != null) {
            LocalizationManager.instance.OnLanguageChange -= UpdateTextLiveEvent;
        }

        if (isShowingEvent) {
            UnregisterLiveEventEvents();
        }
    }

    #endregion

    #region Delegate Methods

    private bool CreateButtonLiveEvent() {
        if (!btnLiveEvent) {
            var uiButton = Resources.Load<UIBottomMenuLiveEvent>("icons/btnLiveEvent");
            btnLiveEvent = Instantiate(uiButton, groupButton);
            btnLiveEvent.transform.SetSiblingIndex(1);
            menuLiveEvent = btnLiveEvent.main;
            menuLiveEvent.mainButton.onClick.AddListener(OnBtnLiveEventClick);
            menuLiveEvent.mainButton.gameObject.SetActive(true);
            return true;
        } else {
            if (menuLiveEvent != null && menuLiveEvent.mainButton)
            {
                menuLiveEvent.mainButton.gameObject.SetActive(true);
            }
            return false;
        }
    }
    private void OnBtnLiveEventClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnLiveEventOnClick?.Invoke();
    }


    private LiveEventFeatureType GetLiveEventType() {
        if (LiveEventManager.instance && LiveEventManager.instance.IsActiveEvent) {
            return LiveEventFeatureType.Old2023;
        }

        if (BallSpinnerManager.IsActiveEvent) {
            return LiveEventFeatureType.BallSpinner;
        }

        return LiveEventFeatureType.None;
    }
    public void LiveEvent_CheckActive() {
        _eventType = GetLiveEventType();
        isShowingEvent = _eventType != LiveEventFeatureType.None;
        if (isShowingEvent) {
            var isCreateNew = CreateButtonLiveEvent();
            if (isCreateNew) {
                RegisterLiveEventEvents();
            }

            CheckNoticeLiveEvent();
            UpdateTextLiveEvent();
        } else if (menuLiveEvent != null && menuLiveEvent.mainButton) {
            menuLiveEvent.mainButton.gameObject.SetActive(false);
        }

        CalculateSizeBottom();
        isInited = true;
    }

    private void UnregisterLiveEventEvents() {
        switch (_eventType) {
            case LiveEventFeatureType.Old2023:
                LiveEventManager.OnUnlockSong -= LiveEventManagerOnOnUnlockSong;
                LiveEventManager.OnChangeNoticeState -= CheckNoticeLiveEvent;
                break;

            case LiveEventFeatureType.BallSpinner:
                BallSpinnerManager.OnChangeData -= CheckNoticeLiveEvent;
                break;

            default:
                Logger.EditorLogError("Not handle this!");
                break;
        }
    }

    private void RegisterLiveEventEvents() {
        switch (_eventType) {
            case LiveEventFeatureType.Old2023:
                LiveEventManager.OnUnlockSong += LiveEventManagerOnOnUnlockSong;
                LiveEventManager.OnChangeNoticeState += CheckNoticeLiveEvent;
                break;

            case LiveEventFeatureType.BallSpinner:
                BallSpinnerManager.OnChangeData += CheckNoticeLiveEvent;
                break;

            default:
                Logger.EditorLogError("Not handle this!");
                break;
        }
    }

    private void OnBtnSearchClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnSearchOnClick?.Invoke();
    }

    private void OnBtnHomeClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnHomeOnClick?.Invoke();
    }

    private void LiveEventManagerOnOnUnlockSong(int idEvent) {
        CheckNoticeLiveEvent();
    }

    private void CheckNoticeLiveEvent() {
        if (!isShowingEvent || menuLiveEvent == null) {
            return;
        }

        bool hasNotice = false;
        switch (_eventType) {
            case LiveEventFeatureType.Old2023:
                hasNotice = LiveEventManager.instance.HaveNotice;
                break;

            case LiveEventFeatureType.BallSpinner:
                hasNotice = BallSpinnerManager.instanceSafe.HasNotice();
                break;

            default:
                Logger.EditorLogError("Not handle this!");
                break;
        }

        menuLiveEvent.objNotice.SetActive(hasNotice);
    }

    public void CheckNoticeDiscover(ChallengeState state) {
        if (menuSearch.objNotice == null) {
            return;
        }

        bool isClickedAccept =
            DiscoveryChallengeManager.isInstanced && DiscoveryChallengeManager.instanceSafe.isClickedAccept;
        bool isShowNotice = HardcoreSongCollection.isInstanced && HardcoreSongCollection.instanceSafe.CanCollectMore &&
                            state is ChallengeState.Unknown or ChallengeState.Accepted;

        menuSearch.objNotice.SetActive(isShowNotice && !isClickedAccept);
    }

    private void DiscoveryChallengeManagerOnOnEventStateChange(ChallengeState state) {
        CheckNoticeDiscover(state);
    }

    #endregion

    private void UpdateTextLiveEvent() {
        if (menuLiveEvent == null) {
            return;
        }

        menuLiveEvent.txtMain.text = LocalizationManager.instance.GetLocalizedValue("EVENT").ToUpper();
        LocalizationManager.instance.UpdateFont(menuLiveEvent.txtMain);
    }

    protected virtual void CalculateSizeBottom() {
        PrepareTabOrderMaping();
        Dictionary<HomeTab, MenuBottomButton> tabMapping = new() {
            { HomeTab.MAIN_HOME, menuHome },
            { HomeTab.SEARCH, menuSearch },
        };
        
        List<HomeTab> visibleTabs = new() { HomeTab.MAIN_HOME, HomeTab.SEARCH };
        
        int numberTab = 2; //home (cố định) + search (cố định)
        bool isOpenLiveEvent = isShowingEvent && menuLiveEvent != null;
        
        if (isOpenLiveEvent) {
            numberTab += 1; //open live event
            tabMapping.Add(HomeTab.LIVE_EVENT, menuLiveEvent);
            visibleTabs.Add(HomeTab.LIVE_EVENT);
        }

        bool isShopIapTabEnable = btnTabShopIap != null && menuShop != null && menuShop.mainButton != null;
        if (isShopIapTabEnable) {
            numberTab++;
            tabMapping.Add(HomeTab.SHOP_IAP, menuShop);
            visibleTabs.Add(HomeTab.SHOP_IAP);
        }

        if (menuShopBall != null && menuShopBall.mainButton != null) {
            numberTab++;
            tabMapping.Add(HomeTab.SHOP_BALL, menuShopBall);
            visibleTabs.Add(HomeTab.SHOP_BALL);
        }

        if (menuStarJourney != null && menuStarJourney.mainButton != null) {
            numberTab++;
            tabMapping.Add(HomeTab.STAR_JOURNEY, menuStarJourney);
            visibleTabs.Add(HomeTab.STAR_JOURNEY);
        }

        if (numberTab >= 4) {
            menuHome.TogglePreserveAspect(false);
            menuSearch.TogglePreserveAspect(false);
        }

        float fullSize = 480f / numberTab;
        var sizeDelta = rectBottomMenu.sizeDelta;

        visibleTabs.Sort(CompareHomeTabOrder);

        for (int index = 0; index < visibleTabs.Count; index++) {
            Transform target = tabMapping[visibleTabs[index]].mainButton.transform;
            SetMenuButtonSize(target, index, numberTab, fullSize, sizeDelta);
        }
        
        if (isOpenLiveEvent) {
            menuLiveEvent.mainButton.gameObject.SetActive(true);
        } else {
            menuLiveEvent?.mainButton.gameObject.SetActive(false);
        }
        
        menuSearch.mainButton.gameObject.SetActive(true);
    }

    protected int CompareHomeTabOrder(HomeTab tab1, HomeTab tab2) {
        return tabOrderMapping[tab1].CompareTo(tabOrderMapping[tab2]);
    }
    
    protected void SetMenuButtonSize(Transform targetTransform, int index, int numberTab, float fullSize,
                                     Vector2 sizeDelta) {
        ((RectTransform) targetTransform).sizeDelta = new Vector2(fullSize, sizeDelta.y);
        targetTransform.localPosition = Vector3.right * (fullSize * (index - (numberTab - 1) / 2f));
    }

    private void SetHeight() {
        var currentSize = rectBottomMenu.sizeDelta;
        if (!Mathf.Approximately(currentSize.x, heightBottomMenu)) {
            //Set bg bottom menu scale
            var offsetMax = rectBottomMenuBg.offsetMax;
            float newTop = offsetMax.y * heightBottomMenu / currentSize.y;
            offsetMax = new Vector2(offsetMax.x, newTop);
            rectBottomMenuBg.offsetMax = offsetMax;

            //Set new bottom menu height
            currentSize.y = heightBottomMenu;
            rectBottomMenu.sizeDelta = currentSize;
        }
    }

    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public virtual void HighLight(Type type, bool hasAnimation = true) {
        HomeManager.CachedTabActived = type;
        menuHome.HighLight(type == Type.Home, colorOn, colorOff, hasAnimation);
        menuSearch.HighLight(type == Type.Search, colorOn, colorOff, hasAnimation);

        if (menuShop != null && menuShop.mainButton != null) {
            menuShop.HighLight(type == Type.ShopIAP, colorOn, colorOff, hasAnimation);
        }

        if (menuShopBall != null && menuShopBall.mainButton != null) {
            menuShopBall.HighLight(type == Type.ShopBall, colorOn, colorOff, hasAnimation);
        }
        
        if (menuStarJourney != null && menuStarJourney.mainButton != null) {
            menuStarJourney.HighLight(type == Type.StarJourney, colorOn, colorOff, hasAnimation);
        }
        
        menuLiveEvent?.HighLight(type == Type.LiveEvent, colorOn, colorOff, hasAnimation);
    }

    private IEnumerator IEWaitInitComplete(Action callback) {
        while (!isInited) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        yield return YieldPool.GetWaitForEndOfFrame();

        callback?.Invoke();
    }

    private void BlackBgTooltipOnOnClick() {
        if (menuSearch.isShowingTooltip) {
            if (RemoteConfig.instance.Onboarding_Home_ExploreNotice_RequiredClick) {
                return;
            }

            HideExploreNotice();
        } else if (menuLiveEvent != null && menuLiveEvent.isShowingTooltip) {
            menuLiveEvent.HideToolTip(groupButton, colorOff);
            bgToolTip.HideRequired();
        }
    }

    public void ShowExploreToolTip() {
        StartCoroutine(IEWaitInitComplete(() => {
            HomeManager.instance.blackBG.SetActive(true);
            menuSearch.ShowToolTip(colorOn);
            PlayerPrefs.SetInt(CONFIG_STRING.OnboardingFeature_ExploreAppear, 1);
        }));
    }

    public void HideExploreNotice() {
        if (!menuSearch.isShowingTooltip) {
            return;
        }

        bgToolTip.HideRequired();
        menuSearch.HideToolTip(groupButton, colorOff);
    }

    public void ShowEventToolTip(int idEvent) {
        StartCoroutine(IEWaitInitComplete(() => {
            HomeManager.instance.blackBG.SetActive(true);
            menuLiveEvent.ShowToolTip(colorOn);
            _idLiveEventShowToolTip = idEvent;
            var eventItem = LiveEventManager.instance.GetLiveEvent(idEvent);
            if (eventItem != null) {
                eventItem.CompleteShowTutorial();
                LiveEventTracker.Track_OnboardShow(eventItem.eventConfig.Id, eventItem.eventConfig.Title,
                    "home_songlist");
            }
        }));
    }

    public void HideLiveEventToolTip(int idEvent) {
        if (menuLiveEvent != null && menuLiveEvent.isShowingTooltip) {
            menuLiveEvent.HideToolTip(groupButton, colorOff);
            bgToolTip.HideRequired();
            var eventItem = LiveEventManager.instance.GetLiveEvent(idEvent);
            if (eventItem != null) {
                LiveEventTracker.Track_OnboardClick(eventItem.eventConfig.Id, eventItem.eventConfig.Title,
                    "home_songlist");
            }
        }
    }

    public bool IsShowingLiveEventToolTip() {
        return menuLiveEvent != null && menuLiveEvent.isShowingTooltip;
    }

    public int GetIdLiveEventToolTip() {
        return _idLiveEventShowToolTip;
    }
    
    protected virtual void CheckToCreateButtonShopIAP() {
        if (!isShopIapTabForm) {
            return;
        }
        
        if (btnTabShopIap == null) {
            var uiButton = Resources.Load<UIBottomMenuTab>("btnMenuShopIAP");
            btnTabShopIap = Instantiate(uiButton, groupButton);
            menuShop = btnTabShopIap.main;
            menuShop.mainButton.onClick.AddListener(OnBtnShopDiamondClick);
            menuShop.mainButton.gameObject.SetActive(true);
        } else if(menuShop.mainButton != null) {
            menuShop.mainButton.gameObject.SetActive(true);
        }
    }
    
    protected virtual void CheckToCreateButtonShopBall() {
        if (!RemoteConfigBase.instance.Hybrid_UI_Rearrange) {
            return;
        }
        
        if (btnTabShopBall == null) {
            var uiButton = Resources.Load<UIBottomMenuTab>("btnMenuShopBall");
            btnTabShopBall = Instantiate(uiButton, groupButton);
            menuShopBall = btnTabShopBall.main;
            menuShopBall.mainButton.onClick.AddListener(OnBtnShopBallClick);
            menuShopBall.mainButton.gameObject.SetActive(true);
        } else if(menuShopBall.mainButton != null) {
            menuShopBall.mainButton.gameObject.SetActive(true);
        }
    } 
    
    protected virtual void CheckToCreateButtonStarJourney() {
        if (!RemoteConfigBase.instance.Hybrid_UI_Rearrange/* || !StarsJourneyManager.isEnable*/) {
            return;
        }
        
        if (!btnTabStarJourney) {
            var uiButton = Resources.Load<UIBottomMenuTab>("btnMenuStarJourney");
            btnTabStarJourney = Instantiate(uiButton, groupButton);
            btnTabStarJourney.transform.SetSiblingIndex(1);
            menuStarJourney = btnTabStarJourney.main;
            menuStarJourney.mainButton.onClick.AddListener(OnBtnStarJourneyClick);
            menuStarJourney.mainButton.gameObject.SetActive(true);
        } else if (menuStarJourney != null && menuStarJourney.mainButton) {
            menuStarJourney.mainButton.gameObject.SetActive(true);
        }
    }
    
    protected void OnBtnShopDiamondClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnShopDiamondOnClick?.Invoke();
    }
    
    protected void OnBtnShopBallClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnShopBallOnClick?.Invoke();
    }

    protected void OnBtnStarJourneyClick() {
        if (IsLockedAction) {
            Logger.Log("Locked action bottom menu");
            return;
        }

        btnStarJourneyOnClick?.Invoke();
    }
    
    protected void CheckNoticeShop() {
        bool isShowBadge = Configuration.instance.IsAvailableFreeGemsInShop();
        ShowShopBadge(isShowBadge);
    }
    
    public void ShowShopBadge(bool isShowBadge) {
        if (menuShop != null && menuShop.objNotice != null) {
            menuShop.objNotice.SetActive(isShowBadge);
        }
    }

    public virtual void SetInteractableExceptDiscovery(bool isInteractable) {
        menuHome.mainButton.interactable = isInteractable;
        
        if (menuLiveEvent != null && menuLiveEvent.mainButton != null) {
            menuLiveEvent.mainButton.interactable = isInteractable;
        }

        if (menuShop != null && menuShop.mainButton != null) {
            menuShop.mainButton.interactable = isInteractable;
        }

        if(menuShopBall != null && menuShopBall.mainButton != null) {
            menuShopBall.mainButton.interactable = isInteractable;
        }

        if (menuStarJourney != null && menuStarJourney.mainButton != null) {
            menuStarJourney.mainButton.interactable = isInteractable;
        }
    }

    public virtual void PrepareTabOrderMaping() {
        if (tabOrderMapping != null) {
            return;
        }
        
        tabOrderMapping = new Dictionary<HomeTab, int>() {
            { HomeTab.SHOP_IAP, 100 },
            { HomeTab.SHOP_BALL, 200 },
            { HomeTab.MAIN_HOME, 300 },
            { HomeTab.STAR_JOURNEY, 400 },
            { HomeTab.LIVE_EVENT, 500 },
            { HomeTab.SEARCH, 600 },
        };
        
        string config = RemoteConfigBase.instance.Hybrid_BottomTabOrder;
        if (string.IsNullOrEmpty(config)) {
            return;
        }
        
        var (count, ranges) = StringExtension.SplitStringZeroAllocation(config);

        for (int i = 0; i < count; i++) {
            var (offset, length) = ranges[i];
            var tabName = config[offset..length];
            if (Enum.TryParse(tabName, true, out HomeTab tabType)) {
                tabOrderMapping[tabType] = i;
            }
        }

        ArrayPool<(int, int)>.Shared.Return(ranges);
    }

    /// <summary>
    /// </summary>
    /// <param name="from"></param>
    /// <param name="to"></param>
    /// <returns>(hideDirection, appearDirection)</returns>
    public (Direction, Direction) GetTabDirection(HomeTab from, HomeTab to) {
        if (!tabOrderMapping.ContainsKey(from) || !tabOrderMapping.ContainsKey(to)) {
            return (Direction.None, Direction.None);
        }

        return tabOrderMapping[from] < tabOrderMapping[to]
            ? (Direction.Left, Direction.Right)
            : (Direction.Right, Direction.Left);
    }

    public Transform GetStarFlyTarget() {
        if (menuStarJourney == null) {
            return null;
        }

        return menuStarJourney.mainButton.transform;
    }
}