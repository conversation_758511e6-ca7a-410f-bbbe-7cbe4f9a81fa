using System;
using System.Collections.Generic;
using UnityEngine;

namespace Inwave {
    /// <summary>
    /// trungvt
    /// </summary>
    public class Album : IData {
        //main data
        public int    ordering;
        public string name;
        public string square_cover;
        public string bg_cover;
        public string featured_playlist_id;
        public string tag;
        public string ball;

        //process data
        [NonSerialized] public Sprite sprIcon;
        [NonSerialized] public Sprite sprBg;

        private int[] _balls;
        private int   _indexBalls  = 0;
        private bool  _processBall = false;

        private void Process() {
            if (_processBall)
                return;

            _processBall = true;
            if (string.IsNullOrEmpty(ball)) {
                return;
            }
            
            
            List<int> dataBalls = new();
            var strBalls = ball.Split(';', StringSplitOptions.RemoveEmptyEntries);
            foreach (var str in strBalls) {
                if (int.TryParse(str, out int idBall) && !dataBalls.Contains(idBall) &&
                    BallManager.instance.ExistBallConfig(idBall)) {
                    dataBalls.Add(idBall);
                }
            }

            dataBalls.Shuffle();

            _balls = dataBalls.ToArray();
            _indexBalls = 0;
        }

        public int GetIdBall() {
            if (!_processBall) {
                Process();
            }

            if (_balls.IsNullOrEmpty())
                return -1;

            return _balls[_indexBalls++ % _balls.Length];
        }
    }
}