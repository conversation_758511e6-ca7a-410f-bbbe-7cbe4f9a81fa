using System.Collections.Generic;

namespace TilesHop.DiscoveryChallenge {
    public enum TRACK_KEY {
        discover_tool_tip_imp,
        discover_impression,
        banner_discover_impression,
        banner_discover_click,
        your_challenge_discover_onboarding,
        your_challenge_discover_accept_click,
        challenge_discover_detail_impression,
        your_challenge_discover_update,
        discover_your_challenge_timeout,
        discover_your_challenge_complete,
        your_challenge_golden_box_impression,
        your_challenge_golden_box_open,
        your_challenge_golden_box_claim,
        your_challenge_golden_box_song_select,
        your_challenge_golden_box_preview
    }

    public struct TRACK_PARAM {
        public const string source                          = "source";
        public const string banner_id                       = "banner_id";
        public const string banner_order                    = "banner_order";
        public const string challenge_current_detail_status = "challenge_current_detail_status";
        public const string challenge_current_status        = "challenge_current_status";
        public const string challenge_current_total_stars   = "challenge_current_total_stars";
        public const string challenge_num                   = "challenge_num";
        public const string challenge_total_songs           = "challenge_total_songs";
        public const string challenge_required_stars        = "challenge_required_stars";
        public const string challenge_unlocked_songs        = "challenge_unlocked_songs";
        public const string elapsed_time                    = "elapsed_time";
        public const string before_challenge_unlocked_songs = "before_challenge_unlocked_songs";
        public const string after_challenge_unlocked_songs  = "after_challenge_unlocked_songs";
        public const string action                          = "action";
        public const string before_challenge_status         = "before_challenge_status";
        public const string after_challenge_status          = "after_challenge_status";
        public const string location                        = "location";
        public const string song_acm_id                     = "song_acm_id";
        public const string song_name                       = "song_name";
        public const string your_challenge_status           = "your_challenge_status";
        public const string preview_num                     = "preview_num";
    }

    public enum BannerType {
        auto_play,
        manual_slide,
    }

    public enum ActionType {
        unlock,
        play
    }

    public enum Source {
        home,
        discover,
        result_screen
    }

    public class DiscoveryChallengeTracker {
        /// <summary>
        /// triggers when user sees discovery tool tip
        /// </summary>
        public static void Track_DiscoverToolTipImpression() {
            string eventKey = TRACK_KEY.discover_tool_tip_imp.ToString();
            AnalyticHelper.LogEvent(eventKey, null);
        }

        /// <summary>
        /// triggered when user sees 'Discover' tab
        /// </summary>
        public static void Track_DiscoverImpression(int challenge_num, ChallengeState state) {
            string eventKey = TRACK_KEY.discover_impression.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.your_challenge_status, ConvertStateToString(state)},
                {TRACK_PARAM.challenge_num.ToString(), challenge_num},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_Banner_Discover_Impression(int banner_id, int banner_order, BannerType type) {
            string eventKey = TRACK_KEY.banner_discover_impression.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.banner_id.ToString(), banner_id},
                {TRACK_PARAM.banner_order.ToString(), banner_order},
                {TRACK_NAME.type, type.ToString()},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_Banner_Discover_Click(int banner_id, int banner_order, BannerType type) {
            string eventKey = TRACK_KEY.banner_discover_click.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.banner_id.ToString(), banner_id},
                {TRACK_PARAM.banner_order.ToString(), banner_order},
                {TRACK_NAME.type, type.ToString()},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        /// <summary>
        /// triggers when user explore the challenge for the first time
        /// </summary>
        public static void Track_ChallengeOnboarding() {
            string eventKey = TRACK_KEY.your_challenge_discover_onboarding.ToString();
            AnalyticHelper.LogEvent(eventKey);
        }

        /// <summary>
        /// triggers every time user clicks accept your challenge in discover tab
        /// </summary>
        /// <param name="challenge_num">oridnal number of the challenge (e.g.: 1st challenge, 2nd challenge, etc.)</param>
        /// <param name="challenge_required_stars">total stars required to complete the challenge (i.e., achieve the golden mystery box)</param>
        /// <param name="challenge_current_total_stars">total stars earned in the challenge</param>
        /// <param name="challenge_total_songs"></param>
        /// <param name="challenge_unlocked_songs"></param>
        /// <param name="challenge_current_status"></param>
        /// <param name="songs"></param>
        /// <param name="elapsed_time"></param>
        public static void Track_ChallengeAcceptClick(Source source, int challenge_num, int challenge_required_stars,
                                                      int challenge_current_total_stars, int challenge_total_songs,
                                                      int challenge_unlocked_songs,
                                                      ChallengeState challenge_current_status, List<Song> songs,
                                                      int elapsed_time) {
            string eventKey = TRACK_KEY.your_challenge_discover_accept_click.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.source, source.ToString()},
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.challenge_required_stars, challenge_required_stars},
                {TRACK_PARAM.challenge_current_total_stars, challenge_current_total_stars},
                {TRACK_PARAM.challenge_total_songs, challenge_total_songs},
                {TRACK_PARAM.challenge_unlocked_songs, challenge_unlocked_songs},
                {TRACK_PARAM.challenge_current_status, ConvertStateToString(challenge_current_status)},
                {TRACK_PARAM.elapsed_time, elapsed_time},
            };
            AddSongData(ref param, songs);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        /// <summary>
        /// triggers every time user sees your challenge detail in discover tab (AFTER CLICKING CHALLENGE ACCEPT BUTTON)
        /// </summary>
        public static void Track_ChallengeDetailImpression(Source source, int challenge_num,
                                                           int challenge_required_stars,
                                                           int challenge_current_total_stars, int challenge_total_songs,
                                                           int challenge_unlocked_songs,
                                                           ChallengeState challenge_current_status, List<Song> songs,
                                                           int elapsed_time) {
            string eventKey = TRACK_KEY.challenge_discover_detail_impression.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.source, source.ToString()},
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.challenge_required_stars, challenge_required_stars},
                {TRACK_PARAM.challenge_current_total_stars, challenge_current_total_stars},
                {TRACK_PARAM.challenge_total_songs, challenge_total_songs},
                {TRACK_PARAM.challenge_unlocked_songs, challenge_unlocked_songs},
                {TRACK_PARAM.challenge_current_status, ConvertStateToString(challenge_current_status)},
                //{ TRACK_PARAM.challenge_current_detail_status, DetailStateToString(challenge_current_status) },
                {TRACK_PARAM.elapsed_time, elapsed_time},
            };
            AddSongData(ref param, songs);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        /// <summary>
        /// triggers every time user reaches song result screen of a song in the challenge song list OR unlocking new songs in the challenge song list
        /// </summary>
        public static void Track_ChallengeUpdate(int challenge_num, int challenge_required_stars,
                                                 int challenge_current_total_stars, int challenge_total_songs,
                                                 int before_challenge_unlocked_songs,
                                                 int after_challenge_unlocked_songs, ActionType actionType,
                                                 ChallengeState before_challenge_status,
                                                 ChallengeState after_challenge_status, List<Song> songs,
                                                 Song currentSong, int totalStarBefore, int elapsed_time) {
            string eventKey = TRACK_KEY.your_challenge_discover_update.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.challenge_required_stars, challenge_required_stars},
                {TRACK_PARAM.challenge_current_total_stars, challenge_current_total_stars},
                {TRACK_PARAM.challenge_total_songs, challenge_total_songs},
                {TRACK_PARAM.before_challenge_unlocked_songs, before_challenge_unlocked_songs},
                {TRACK_PARAM.after_challenge_unlocked_songs, after_challenge_unlocked_songs},
                {TRACK_PARAM.action, actionType.ToString()},
                {TRACK_PARAM.before_challenge_status, ConvertStateToString(before_challenge_status)},
                {TRACK_PARAM.after_challenge_status, ConvertStateToString(after_challenge_status)},
                {TRACK_PARAM.elapsed_time, elapsed_time},
            };
            AddSongChangeData(ref param, songs, currentSong, totalStarBefore, challenge_current_total_stars);
            AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_ChallengeTimOut(int challenge_num, int challenge_required_stars,
                                                 int challenge_current_total_stars, int challenge_total_songs,
                                                 int challenge_unlocked_songs, ChallengeState challenge_current_status,
                                                 List<Song> songs, int elapsed_time) {
            string eventKey = TRACK_KEY.discover_your_challenge_timeout.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.challenge_required_stars, challenge_required_stars},
                {TRACK_PARAM.challenge_current_total_stars, challenge_current_total_stars},
                {TRACK_PARAM.challenge_total_songs, challenge_total_songs},
                {TRACK_PARAM.challenge_unlocked_songs, challenge_unlocked_songs},
                {TRACK_PARAM.challenge_current_status, ConvertStateToString(challenge_current_status)},
                {TRACK_PARAM.elapsed_time, elapsed_time},
            };
            AddSongData(ref param, songs);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_ChallengeComplete(int challenge_num, int challenge_required_stars,
                                                   int challenge_current_total_stars, int challenge_total_songs,
                                                   int challenge_unlocked_songs,
                                                   ChallengeState challenge_current_status, List<Song> songs,
                                                   int elapsed_time) {
            string eventKey = TRACK_KEY.discover_your_challenge_complete.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.challenge_required_stars, challenge_required_stars},
                {TRACK_PARAM.challenge_current_total_stars, challenge_current_total_stars},
                {TRACK_PARAM.challenge_total_songs, challenge_total_songs},
                {TRACK_PARAM.challenge_unlocked_songs, challenge_unlocked_songs},
                {TRACK_PARAM.challenge_current_status, ConvertStateToString(challenge_current_status)},
                {TRACK_PARAM.elapsed_time, elapsed_time},
            };
            AddSongData(ref param, songs);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_ChallengeGoldenBoxImpression(int challenge_num, string location) {
            string eventKey = TRACK_KEY.your_challenge_golden_box_impression.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.location, location},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_ChallengeGoldenBoxOpen(int challenge_num, string location) {
            string eventKey = TRACK_KEY.your_challenge_golden_box_open.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.location, location},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_ChallengeGoldenBoxSongSelect(int challenge_num, string location, string song_acm_id,
                                                              string song_name) {
            string eventKey = TRACK_KEY.your_challenge_golden_box_song_select.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.location, location},
                {TRACK_PARAM.song_acm_id, song_acm_id},
                {TRACK_PARAM.song_name, song_name},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void Track_ChallengeGoldenBoxSongClaim(int challenge_num, string location, string song_acm_id,
                                                             string song_name) {
            string eventKey = TRACK_KEY.your_challenge_golden_box_claim.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.location, location},
                {TRACK_PARAM.song_acm_id, song_acm_id},
                {TRACK_PARAM.song_name, song_name},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        private static void AddSongChangeData(ref Dictionary<string, object> dictionary, List<Song> songs,
                                              Song currentSong, int totalBefore, int totalAfter) {
            if (songs == null)
                return;

            int total = songs.Count;
            if (total == 0)
                return;

            string format = "{{\"acm_id\":\"{0}\",\"star_before\":\"{1}\",\"star_after\":\"{2}\"}}";
            byte index = 0;
            byte afterStar;
            byte beforeStar;
            if (total >= 1) {
                index = 0;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_1st"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 2) {
                index = 1;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_2nd"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 3) {
                index = 2;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_3rd"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 4) {
                index = 3;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_4th"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 5) {
                index = 4;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_5th"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 6) {
                index = 5;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_6th"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 7) {
                index = 6;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_7th"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 8) {
                index = 7;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_8th"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 9) {
                index = 8;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_9th"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }

            if (total >= 10) {
                index = 9;
                afterStar = songs[index].Star;
                beforeStar = (songs[index] == currentSong)
                    ? (byte) (afterStar - (totalAfter - totalBefore))
                    : afterStar;
                dictionary["song_10th"] = string.Format(format, songs[index].acm_id_v3, beforeStar, afterStar);
            }
        }

        private static void AddSongData(ref Dictionary<string, object> dictionary, List<Song> songs) {
            if (songs == null)
                return;

            int total = songs.Count;
            if (total == 0)
                return;

            string format = "{{\"acm_id\":\"{0}\",\"star\":\"{1}\"}}";
            if (total >= 1)
                dictionary["song_1st"] = GetSongInfor(songs[0]);
            if (total >= 2)
                dictionary["song_2nd"] = GetSongInfor(songs[1]);
            if (total >= 3)
                dictionary["song_3rd"] = GetSongInfor(songs[2]);
            if (total >= 4)
                dictionary["song_4th"] = GetSongInfor(songs[3]);
            if (total >= 5)
                dictionary["song_5th"] = GetSongInfor(songs[4]);
            if (total >= 6)
                dictionary["song_6th"] = GetSongInfor(songs[5]);
            if (total >= 7)
                dictionary["song_7th"] = GetSongInfor(songs[6]);
            if (total >= 8)
                dictionary["song_8th"] = GetSongInfor(songs[7]);
            if (total >= 9)
                dictionary["song_9th"] = GetSongInfor(songs[8]);
            if (total >= 10)
                dictionary["song_10th"] = GetSongInfor(songs[9]);

            string GetSongInfor(Song song) {
                return string.Format(format, song.acm_id_v3, song.Star);
            }
        }

        private static string ConvertStateToString(ChallengeState state) {
            switch (state) {
                case ChallengeState.Unknown:
                    return "not_started";

                case ChallengeState.Accepted:
                    return "on_going";

                case ChallengeState.Completed:
                case ChallengeState.OpenedGoldenBox:
                    return "completed";

                case ChallengeState.Failed:
                    return "timeout";

                default:
                    return "hidden";
            }
        }

        public static void Track_ChallengeGoldenBoxPreview(int preview_num, int challenge_num,
                                                           int challenge_required_stars,
                                                           int challenge_current_total_stars, int challenge_total_songs,
                                                           int challenge_unlocked_songs,
                                                           ChallengeState challenge_current_status, List<Song> songs,
                                                           int elapsed_time) {
            string eventKey = TRACK_KEY.your_challenge_golden_box_preview.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.preview_num, preview_num},
                {TRACK_PARAM.challenge_num, challenge_num},
                {TRACK_PARAM.challenge_required_stars, challenge_required_stars},
                {TRACK_PARAM.challenge_current_total_stars, challenge_current_total_stars},
                {TRACK_PARAM.challenge_total_songs, challenge_total_songs},
                {TRACK_PARAM.challenge_unlocked_songs, challenge_unlocked_songs},
                {TRACK_PARAM.challenge_current_status, ConvertStateToString(challenge_current_status)},
                {TRACK_PARAM.elapsed_time, elapsed_time},
            };
            AddSongData(ref param, songs);
            AnalyticHelper.LogEvent(eventKey, param);
        }
    }
}