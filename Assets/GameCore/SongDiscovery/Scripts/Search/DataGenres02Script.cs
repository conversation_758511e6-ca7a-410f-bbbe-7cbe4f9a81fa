using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using TilesHop.Cores.Pooling;
using UnityEngine.U2D;
using UnityEngine.UI;

public class DataGenres02Script : DataCatalogs {
    #region Fields

    //public
    [Header("Config")] [SerializeField] private StandardScrollerAdapter scrollerAdapter;
    [SerializeField]                    private OptimizedCellView   prefCellView;
    [SerializeField]                    private SpriteAtlas         atlasGenres;
    [SerializeField]                    private Button              btnSeeAll;

    //private
    private List<string> _listGenres = new List<string>();
    private List<IData>  _datas;

    #endregion

    #region Unity Method

    protected override void Awake() {
        base.Awake();
        scrollerAdapter.Init(prefCellView.transform as RectTransform);

        //load genres
        string strListGenresRemote = RemoteConfig.instance.SongDiscovery_ListGenres;
        if (!string.IsNullOrEmpty(strListGenresRemote)) {
            List<string> listGenreRemote = strListGenresRemote.ToUpper().Replace("/", "_").Split(';').ToList();
            if (listGenreRemote.Count > 0) {
                foreach (string genre in listGenreRemote.Where(genre => !string.IsNullOrEmpty(genre))) {
                    _listGenres.Add(genre);
                }
            }
        }

        btnSeeAll.onClick.AddListener(btnSeeAllOnClick);
    }

    private void Start() {
        _datas = new List<IData>();
        int collum = (Mathf.Min(10, _listGenres.Count) + 1) / 2; // chỉ lấy tối đa 10
        for (int i = 0; i < collum; i++) {
            _datas.Add(new HeaderData());
        }

        Configuration.instance.StartCoroutine(SetData());
    }

    private void OnEnable() {
        scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
    }

    private void OnDisable() {
        scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
    }

    #endregion

    #region OptimizedScroller Handlers

    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_datas.Count <= item.ItemIndex) return;
        if (item.cellView is GroupItemGenreScript view) {
            //Item 1
            int realIndex = item.ItemIndex * 2;
            ItemGenreScript itemGenre = view.item1;
            if (_listGenres.Count <= realIndex) {
                itemGenre.gameObject.SetActive(false);
            } else {
                string genre = _listGenres[realIndex];
                itemGenre.SetImage(GetSpriteByName(genre));
                itemGenre.SetTitle(genre);
                itemGenre.ordering = realIndex + 1;

                itemGenre.onButtonClick = () => {
                    SoundManager.PlayGameButton();
                    catalogs02SearchScript.OpenGenre(genre);
                };
            }

            //Item 2
            int realIndex2 = item.ItemIndex * 2 + 1;
            ItemGenreScript itemGenre2 = view.item2;
            if (_listGenres.Count <= realIndex2) {
                itemGenre2.gameObject.SetActive(false);
            } else {
                string genre2 = _listGenres[realIndex2];
                itemGenre2.SetImage(GetSpriteByName(genre2));
                itemGenre2.SetTitle(genre2);
                itemGenre2.ordering = realIndex2 + 1;

                itemGenre2.onButtonClick = () => {
                    catalogs02SearchScript.OpenGenre(genre2);
                };
            }
        }
    }

    private IEnumerator SetData() {
        while (!scrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        scrollerAdapter.SetItems(_datas);
    }

    #endregion

    private Sprite GetSpriteByName(string genre) {
        Sprite sprite = atlasGenres.GetSprite(genre);

        if (sprite == null) {
            sprite = atlasGenres.GetSprite(GenreType.OTHERS);
            Logger.LogWarning(
                $"[GetSpriteByName] Cannot get sprite {genre.ToColor(Color.green)} in atlas {atlasGenres.name}");
        }

        return sprite;
    }

    private void btnSeeAllOnClick() {
        SoundManager.PlayGameButton();
        catalogs02SearchScript.OpenAllGenre();
        SearchScript.LogEventSeeAllClick(DiscoveryLocation.discover_genre.ToString());
    }
}