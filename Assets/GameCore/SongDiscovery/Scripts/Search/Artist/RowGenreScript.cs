using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;

public class RowGenreScript : OptimizedCellView {
    #region Fields

    [SerializeField] private ItemGenreScript genre01;
    [SerializeField] private ItemGenreScript genre02;

    #endregion

    #region Unity Method
    #endregion

    public void SetData(RowGenres rowGenres, Action<string> onClick) {
        SetCell(genre01, rowGenres.genreName01, rowGenres.genreSprite01, onClick);
        SetCell(genre02, rowGenres.genreName02, rowGenres.genreSprite02, onClick);
    }

    private void SetCell(ItemGenreScript cell, string genreName, Sprite generSprite, Action<string> onClick) {
        if (genreName != null) {
            cell.SetActive(true);
            cell.SetTitle(genreName);
            cell.SetImage(generSprite);
            cell.onButtonClick = () => { onClick?.Invoke(genreName); };
        } else {
            cell.SetActive(false);
        }
    }
    public override void SetData(IData _data) {
    }
}