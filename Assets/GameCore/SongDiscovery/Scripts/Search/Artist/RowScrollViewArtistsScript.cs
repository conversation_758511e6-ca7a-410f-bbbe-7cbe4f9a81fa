using System;
using System.Collections;
using System.Collections.Generic;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.EventSystems;

/// <summary>
/// trungvt
/// </summary>
public class RowScrollViewArtistsScript : OptimizedCellView {
    #region Fields

    [SerializeField] private StandardScrollerAdapter   scrollerAdapter;
    [SerializeField] private ItemArtistScript      prefItemArtistScript;

    [SerializeField] private SwipeItemScrollScript swipeItemScrollScript;

    //private
    private List<string>         _allArtist; //name of artist
    private List<IData>          _datas;
    private event Action<string> onClickArtist;
    #endregion

    #region Unity Methods

    private void Awake() {
        scrollerAdapter.Init(prefItemArtistScript.transform as RectTransform);
    }
    private void OnEnable() {
        scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
    }

    private void OnDisable() {
        scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
    }
    #endregion
    #region OptimizedScroller Handlers
    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_datas.Count < item.ItemIndex) return;
        if (item.cellView is ItemArtistScript view) {
            string artist = ((HeaderData) _datas[item.ItemIndex]).Title;
            view.ordering = item.ItemIndex + 1;
            view.SetImage(this, artist);
            view.SetArtistName(artist);
            view.onButtonClick = () => {
                //
                onClickArtist?.Invoke(artist);
            };
        }
    }
    private IEnumerator SetData() {
        while (!scrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
        scrollerAdapter.SetItems(_datas);
    }
    #endregion
    #region Methods

    #endregion
    public void SetData(RowArtists rowArtists, Action<string> onClickArtist, Action<PointerEventData> OnBeginDrag,
                        Action<PointerEventData> OnDrag, Action<PointerEventData> OnEndDrag) {
        _allArtist = rowArtists.artists;
        swipeItemScrollScript.onBeginDrag = OnBeginDrag;
        swipeItemScrollScript.onDrag = OnDrag;
        swipeItemScrollScript.onEndDrag = OnEndDrag;

        LoadItems(onClickArtist);
    }
    private void LoadItems(Action<string> onClickArtist) {
        this.onClickArtist = onClickArtist;
        _datas = new List<IData>();
        foreach (var item in _allArtist) {
            _datas.Add(new HeaderData(item));
        }
        Configuration.instance.StartCoroutine(SetData());
    }

    public override void SetData(IData _data) {
        
    }
}