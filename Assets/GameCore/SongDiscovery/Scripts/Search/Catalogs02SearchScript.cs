using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using GoogleMobileAds.Api;
using TilesHop.DiscoveryChallenge;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class Catalogs02SearchScript : TabScript {
    #region Fields

    [SerializeField] private SearchScript searchScript;

    [SerializeField] private ScrollRect              scrollRect;
    [SerializeField] private RectTransform           content;
    [SerializeField] private ScrollRectEventListener scrollRectEventListener;
    [SerializeField] private ListArtistsScript       listArtistsScript;
    [SerializeField] private ListAlbumScript         listAlbumScript;
    [SerializeField] private ListGenresScript        listGenresScript;

    [SerializeField] private TabArtistDetailScript      tabArtistDetailScript;
    [SerializeField] private TabAlbumDetailScript       tabAlbumDetailScript;
    [SerializeField] private TabGenreDetailScript       tabGenreDetailScript;
    [SerializeField] private ListSongsScript            listSongsScript;
    [SerializeField] private DataFavouriteSongs02Script dataFavouriteSongs02Script;
    public                   DataPopularListScript      dataPopularListScript;
    public                   DataRecentSongs02Script    dataRecentSongs02Script;

    [Space] [SerializeField] private List<DataDiscovery> dataDiscoveries;
    [SerializeField]         private List<DataDiscovery> orderedDiscoveries;
    private                          GameObject          _goldenBoxPopup;
    
    [SerializeField] private VerticalLayoutGroup      listLayout;

    private DataHardcoreSongs        _hardcoreSongs;
    private BannerDiscoveryChallenge _challengeBannerEntryPoint;

    private List<DiscoveryType> _discoveryTypes;
    private bool                _isFirstShow = true;

    public double ScrollPosition {
        get { return scrollRect.verticalNormalizedPosition; }
        set { scrollRect.DOVerticalNormalizedPos((float)value, 0.2f); }
    }
    
    #endregion

    #region Unity Method

    protected override void Awake() {
        base.Awake();

        if (!HasOverridedTransition) {
            OrderCategories();
        }
        
        HardcoreSongCollection.OnCollectNewHardcoreSong += HandleShowHardcoreCollectionOnCollect;
    }

    private void OnDestroy() {
        HardcoreSongCollection.OnCollectNewHardcoreSong -= HandleShowHardcoreCollectionOnCollect;
    }

    private void OnEnable() {
        searchScript.ShowButtonBack(false);
        if (dataFavouriteSongs02Script != null) {
            dataFavouriteSongs02Script.CheckActive();
        }

        if (DiscoveryChallengeManager.IsEnable() && DiscoveryChallengeManager.isInstanced) {
            if (DiscoveryChallengeManager.instanceSafe.IsActive) {
                DiscoveryChallengeManager.instanceSafe.DoneTooltipHome();
                HardcoreSongCollection.WaitHardcoreSongListDone(ShowDiscoveryChallengeBanner);
            } else {
                DiscoveryChallengeTracker.Track_DiscoverImpression(0, ChallengeState.Disable);
            }
        }
        
        if (HardcoreSongCollection.isInstanced && HardcoreSongCollection.IsHardcoreCollectionAvailable) {
            ShowHardcoreSong();
        }
    }
    private void ShowHardcoreSong() {
        // spawn challenge banner entry point
        if (_hardcoreSongs == null) {
            var prefHardCore = Resources.Load<DataHardcoreSongs>("Discovery/DataHardcore");

            if(prefHardCore != null) {
                _hardcoreSongs = Instantiate(prefHardCore, content);
            }
        }
        
        if (_hardcoreSongs != null) {
            _hardcoreSongs.Setup(this);
            orderedDiscoveries.Add(new DataDiscovery {
                discoveryType = DiscoveryType.HardCoreCollection,
                tfData = _hardcoreSongs.transform
            });
            ReorderCategories();
        }
    }
    private void ShowDiscoveryChallengeBanner() {
        if (!HardcoreSongCollection.instanceSafe.CanCollectMore) {
            DiscoveryChallengeTracker.Track_DiscoverImpression(DiscoveryChallengeManager.ChallengeNumber, ChallengeState.Disable);
            return;
        }

        // spawn challenge banner entry point
        if (_challengeBannerEntryPoint == null) {
            var prefBanner = Resources.Load<BannerDiscoveryChallenge>("UI/UIDiscoveryChallengeBanner");

            if(prefBanner != null) {
                _challengeBannerEntryPoint = Instantiate(prefBanner, content);
            }
        }
        
        if (_challengeBannerEntryPoint != null) {
            _challengeBannerEntryPoint.Show(this,
                DiscoveryChallengeManager.instanceSafe.GetConfig(),
                DiscoveryChallengeManager.instanceSafe.EventState,
                () => {
                    DiscoveryChallengeManager.instanceSafe.OnClickBanner(Source.discover);
                });

            orderedDiscoveries.Add(new DataDiscovery {
                discoveryType = DiscoveryType.ChallengeEntry,
                tfData = _challengeBannerEntryPoint.transform
            });
            ReorderCategories();
        }

        if (DiscoveryChallengeManager.isInstanced && !DiscoveryChallengeManager.instanceSafe.PlaySongFromChallenge) {
            // check xem nếu user đã completed challenge nhưng chưa nhận thưởng thì show lại GoldenBox
            if (DiscoveryChallengeManager.instanceSafe.EventState == ChallengeState.Completed && !_goldenBoxPopup) {
                _goldenBoxPopup = DiscoveryChallengeManager.instanceSafe.ShowPopupReward();
            }
        }

        DiscoveryChallengeTracker.Track_DiscoverImpression(DiscoveryChallengeManager.ChallengeNumber,
            DiscoveryChallengeManager.instanceSafe.EventState);
    }

    #endregion


    private void OrderCategories() {
        // Order
        if (!string.IsNullOrEmpty(RemoteConfig.instance.SongDiscovery_DataOrder)) {
            string[] dataOrder = RemoteConfig.instance.SongDiscovery_DataOrder.Split(';');

            if (_discoveryTypes == null) {
                _discoveryTypes = new List<DiscoveryType>();
                foreach (string s in dataOrder) {
                    if (Enum.TryParse(s, out DiscoveryType discoveryType)) {
                        _discoveryTypes.Add(discoveryType);
                    } else {
                        Logger.LogError("[Catalogs02SearchScript] try parse failed: " + s);
                    }
                }
            }

            int indexSibling = 0;
            orderedDiscoveries ??= new List<DataDiscovery>();

            foreach (DiscoveryType discoveryType in _discoveryTypes) {
                var findObjectDiscovery = FindObjectDiscovery(discoveryType);
                if (findObjectDiscovery != null) {
                    findObjectDiscovery.tfData.SetSiblingIndex(indexSibling++);
                    orderedDiscoveries.Add(findObjectDiscovery);
                }
            }
        }
        // On/Off
        for (int index = 0; index < dataDiscoveries.Count; index++) {
            DataDiscovery dataDiscovery = dataDiscoveries[index];
            dataDiscoveries.Remove(dataDiscovery);
            Destroy(dataDiscovery.tfData.gameObject);
            index--;
        }
    }
    private void CheckMergerDiscoverChallengeAndHardCoreList() {
        if (_hardcoreSongs && _hardcoreSongs.gameObject.activeSelf) {
            if (_challengeBannerEntryPoint&& _challengeBannerEntryPoint.gameObject.activeSelf) {
                //merger
                float sizeHardCore = _hardcoreSongs.GetSize();
                float sizeChallenge = _challengeBannerEntryPoint.GetSize();
                int indexHardCore = _hardcoreSongs.transform.GetSiblingIndex();
                int indexChallenge = _challengeBannerEntryPoint.transform.GetSiblingIndex();

                if (indexChallenge == indexHardCore + 1) {
                    //challenge is below
                    _hardcoreSongs.SetupBGDependent(sizeChallenge, false);
                    _challengeBannerEntryPoint.SetupBGDependent(sizeHardCore, true);
                }else if (indexHardCore == indexChallenge + 1) {
                    //hardcore is below
                    _hardcoreSongs.SetupBGDependent(sizeChallenge, true);
                    _challengeBannerEntryPoint.SetupBGDependent(sizeHardCore, false);
                } else {
                    //Independent
                    _hardcoreSongs.SetupBGIndependent();
                    _challengeBannerEntryPoint.SetupBGIndependent();
                }
                
            } else {
                //only _hardcore
                _hardcoreSongs.SetupBGIndependent();
            }
        } else {
            if (_challengeBannerEntryPoint&& _challengeBannerEntryPoint.gameObject.activeSelf) {
                //only _challenge
                _challengeBannerEntryPoint.SetupBGIndependent();
            } else {
               //no thing show!!!
            }
        }
    }

    private void HandleShowHardcoreCollectionOnCollect() {
        if (_hardcoreSongs) {
            _hardcoreSongs.Refresh();
        }
    }
    
    private void ReorderCategories() {
        if (string.IsNullOrEmpty(RemoteConfigBase.instance.SongDiscovery_DataOrder)) {
            return;
        }
        
        string[] dataOrder = RemoteConfigBase.instance.SongDiscovery_DataOrder.Split(';');
        List<DiscoveryType> discoveryTypes = new ();
        
        foreach (string s in dataOrder) {
            if (Enum.TryParse(s, out DiscoveryType discoveryType)) {
                discoveryTypes.Add(discoveryType);
            } else {
                Logger.LogError("[Catalogs02SearchScript reorder] try parse failed: " + s);
            }
        }

        orderedDiscoveries ??= new List<DataDiscovery>();
        int indexSibling = 0;
        foreach (DiscoveryType discoveryType in discoveryTypes) {
            if (FindOrderedObjectDiscovery(discoveryType, out var findOrderedObjectDiscovery)) {
                if (findOrderedObjectDiscovery.tfData != null) {
                    findOrderedObjectDiscovery.tfData.SetSiblingIndex(indexSibling++);
                }
                orderedDiscoveries.Add(findOrderedObjectDiscovery);
            }
        }
        CheckMergerDiscoverChallengeAndHardCoreList();
    }

    private DataDiscovery FindObjectDiscovery(DiscoveryType discoveryType) {
        foreach (DataDiscovery dataDiscovery in dataDiscoveries) {
            if (dataDiscovery.discoveryType == discoveryType) {
                dataDiscoveries.Remove(dataDiscovery);
                return dataDiscovery;
            }
        }

        return null;
    }

    private bool FindOrderedObjectDiscovery(DiscoveryType discoveryType, out DataDiscovery output) {
        foreach (DataDiscovery dataDiscovery in orderedDiscoveries) {
            if (dataDiscovery.discoveryType == discoveryType) {
                output = dataDiscovery;
                return true;
            }
        }
        
        output = null;
        return false;
    }

    public void ScrollToTop(Action onDone, out bool isAlreadyOnTop) {
        isAlreadyOnTop = scrollRect.verticalNormalizedPosition >= 0.99f;
        scrollRect.DOVerticalNormalizedPos(1, 0.2f).OnComplete(() => { onDone?.Invoke(); });
    }

    public void OpenArtist(string artist) {
        Debug.Log("[OpenArtist] " + artist);
        AnalyticHelper.Artist_Click(artist);
        Hide(Direction.Left);
        tabArtistDetailScript.ShowSongsOfArtist(artist, LastStateData.Locations.Catalogs);
    }

    public void OpenAlbum(string album) {
        Debug.Log("[OpenAlbum] " + album);
        AnalyticHelper.Album_Click(album);
        Hide(Direction.Left);
        tabAlbumDetailScript.ShowSongsOfAlbum(album, LastStateData.Locations.Catalogs);
    }

    public void OpenGenre(string genreType) {
        Debug.Log("OpenGenre " + genreType);
        AnalyticHelper.Genre_Click(genreType);

        Hide(Direction.Left);
        tabGenreDetailScript.ShowSongsOfGenre(genreType, LastStateData.Locations.Catalogs);
    }

    public void OpenAllArtist() {
        Hide(Direction.Left);
        listArtistsScript.Show(Direction.Right);
    }

    public void OpenAllGenre() {
        Hide(Direction.Left);
        listGenresScript.Show(Direction.Right);
    }

    public void OpenAllAlbum() {
        Hide(Direction.Left);
        listAlbumScript.Show(Direction.Right);
    }

    #region Scroller

    public void OnBeginDrag(PointerEventData eventData) {
        scrollRectEventListener.OnBeginDrag(eventData);
        scrollRect.OnBeginDrag(eventData);
    }

    public void OnDrag(PointerEventData eventData) {
        scrollRectEventListener.OnDrag(eventData);
        scrollRect.OnDrag(eventData);
    }

    public void OnEndDrag(PointerEventData eventData) {
        scrollRectEventListener.OnEndDrag(eventData);
        scrollRect.OnEndDrag(eventData);
    }

    #endregion

    #region Show more

    public void ShowSongs(SONG_PLAY_TYPE songPlayType, string titleLocalized, List<Song> songs,
        bool isAnimation = true) {
        Hide(isAnimation ? Direction.Left : Direction.None);
        listSongsScript.ShowSongs(songPlayType, titleLocalized, songs, isAnimation);
    }

    #endregion
    
    #region New Transition

    protected override bool HasOverridedTransition
    {
        get { return true; }
    }

    private List<Image> maskImages;
    private List<Mask> masks;

    private void ToggleMasks(bool isEnable) {
        foreach (Image maskImage in maskImages) {
            if(maskImage == null) continue;
            maskImage.enabled = isEnable;
        }

        foreach (Mask mask in masks) {
            if(mask == null) continue;
            mask.enabled = isEnable;
        }
    }

    public override void Show(Direction fromDirection, Action onShowDone = null, Action onStartHide = null, Ease ease = Ease.Linear) {
        if (_isFirstShow) {
            _isFirstShow = false;
            if (HasOverridedTransition) {
                OrderCategories();
            }

            SetupDiscoveryChallengeBanner();
        }

        base.Show(fromDirection, onShowDone, onStartHide, ease);
    }

    private void SetupDiscoveryChallengeBanner() {
        if (!DiscoveryChallengeManager.IsEnable() || !DiscoveryChallengeManager.isInstanced) {
            return;
        }

        if (DiscoveryChallengeManager.instanceSafe.IsActive) {
            DiscoveryChallengeManager.instanceSafe.DoneTooltipHome();

            // spawn challenge banner entry point
            if (_challengeBannerEntryPoint == null) {
                var prefBanner = Resources.Load<BannerDiscoveryChallenge>("UI/UIDiscoveryChallengeBanner");

                if (prefBanner != null) {
                    _challengeBannerEntryPoint = Instantiate(prefBanner, content);

                    if (_challengeBannerEntryPoint != null) {
                        orderedDiscoveries.Add(new DataDiscovery {
                            discoveryType = DiscoveryType.ChallengeEntry,
                            tfData = _challengeBannerEntryPoint.transform
                        });
                        ReorderCategories();
                    }
                }
            }

            if (DiscoveryChallengeManager.isInstanced && !DiscoveryChallengeManager.instanceSafe.PlaySongFromChallenge) {
                // check xem nếu user đã completed challenge nhưng chưa nhận thưởng thì show lại GoldenBox
                if (DiscoveryChallengeManager.instanceSafe.EventState == ChallengeState.Completed && !_goldenBoxPopup) {
                    _goldenBoxPopup = DiscoveryChallengeManager.instanceSafe.ShowPopupReward();
                }
            }
            
            DiscoveryChallengeTracker.Track_DiscoverImpression(DiscoveryChallengeManager.ChallengeNumber,
                DiscoveryChallengeManager.instanceSafe.EventState);
        } else {
            DiscoveryChallengeTracker.Track_DiscoverImpression(0, ChallengeState.Disable);
        }
    }

    protected override void TransitionShow(Direction fromDirection, Action onShowDone = null, Action onStartHide = null,
                                           Ease ease = Ease.Linear) {
        base.TransitionShow(fromDirection, onShowDone, onStartHide, ease);

        ToggleMasks(false);
    }

    protected override void FinishTransition(Action onShowDone = null) {
        base.FinishTransition(onShowDone);
        if (RemoteConfigBase.instance.UITransition_IsTransitionHomeAndTabs) {
            ToggleMasks(true);
        }
    }

    protected override void SetupTransitionTargets() {
        #region Init Lists

        if (transitionTargets != null) {
            transitionTargets.Clear();
        } else {
            transitionTargets = new List<Transform>();
        }
        
        if (alphaTransitionTargets != null) {
            alphaTransitionTargets.Clear();
        } else {
            alphaTransitionTargets = new List<CanvasGroup>();
        }
        
        if (maskImages != null) {
            maskImages.Clear();
        } else {
            maskImages = new List<Image>();
        }
        
        if (masks != null) {
            masks.Clear();
        } else {
            masks = new List<Mask>();
        }

        #endregion

        DataCatalogs[] catelogs = GetComponentsInChildren<DataCatalogs>();
        if (catelogs != null) {
            foreach (DataCatalogs catalog in catelogs) {
                transitionTargets.AddRange(catalog.GetTransitionElements());
                alphaTransitionTargets.Add(catalog.canvasGroup);
                masks.Add(catalog.mask);
                maskImages.Add(catalog.maskImage);
            }
        }

        newTransitionDuration = 0.5f;
        maxTransitionDelay = 0.2f;
        newTransitionGroupCount = 2;
        newTransitionFirstPositionMuiltiplier = 0.2f;
    }
    #endregion

    public void ShowHardCoreSongs() {
        //Debug.Log("Open Hardcore Songs");
        Hide(Direction.Left);
        searchScript.ShowHardCoreSongDetail();
    }

    public void SnapTo(RectTransform target, float offset = 0.5f, float align = 0.5f) {
        Canvas.ForceUpdateCanvases();
        Vector2 viewSize = ((RectTransform)this.transform).rect.size;
        Vector2 anchor =
                (Vector2)scrollRect.transform.InverseTransformPoint(content.position)
                - (Vector2)scrollRect.transform.InverseTransformPoint(target.position)
                - target.sizeDelta * offset
            /*- viewSize * align*/;
        anchor.x = 0;
        content.anchoredPosition = anchor;
    }
}