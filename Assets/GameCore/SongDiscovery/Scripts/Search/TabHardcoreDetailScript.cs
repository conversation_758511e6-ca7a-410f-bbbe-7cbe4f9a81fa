using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using TilesHop.Cores.Pooling;
using UnityEngine;

public class TabHardcoreDetailScript : TabScript {
    #region Fields

    [SerializeField] private SearchScript searchScript;

    [Header("Components")] [SerializeField]
    private StandardScrollerAdapter scrollerSongs;

    //private
    [HideInInspector] public LastStateData.Locations lastLocation;
    private                  OptimizedCellView       _prefSongItem;

    private List<Song>  _listSong = new List<Song>();
    private List<IData> _data;

    private Action<Song> _onClickSong;

    public double ScrollPosition {
        get {
            if (scrollerSongs.IsInitialized) {
                return scrollerSongs.GetNormalizedPosition();
            }

            return 0f;
        }
        set {
            if (scrollerSongs.VisibleItemsCount != 0) {
                scrollerSongs.SetNormalizedPosition(value);
            } else {
                wantedPosition = value;
            }
        }
    }

    private double wantedPosition = -1f;

    #endregion

    #region Unity Method

    protected override void Awake() {
        base.Awake();

        _prefSongItem = SongItem.GetSongItemPrefab();
        scrollerSongs.Init(_prefSongItem.transform as RectTransform);
    }

    private void OnEnable() {
        searchScript.ShowButtonBack(true);
        scrollerSongs.OnItemVisible += OnScroll_OnItemVisible;
        if (scrollerSongs.IsInitialized) {
            scrollerSongs.ForceRebuildLayoutNow();
        }

        SoundManager.PlaySFX_PopupOpen();

        if (HardcoreSongCollection.isInstanced
            && HardcoreSongCollection.IsHardcoreCollectionAvailable
            && HardcoreSongCollection.instanceSafe.GetCollectedHardcoreSongs(out _listSong)) {
            _data = new List<IData>();
            _data.AddRange(_listSong);

            Configuration.instance.StartCoroutine(SetData());
        }
    }

    protected override void OnDisable() {
        scrollerSongs.OnItemVisible -= OnScroll_OnItemVisible;
        base.OnDisable();
    }

    #endregion

    #region Optimized ScrollView

    private IEnumerator SetData() {
        while (!scrollerSongs.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        scrollerSongs.SetItems(_data);
        while (scrollerSongs.VisibleItemsCount == 0) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }

        if (wantedPosition >= 0) {
            scrollerSongs.SetNormalizedPosition(wantedPosition);
            wantedPosition = -1;
        }
    }

    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_data.Count < item.ItemIndex) {
            return;
        }

        SongItem view = item.cellView as SongItem;
        if (view != null) {
            Song song = (Song)_data[item.ItemIndex];
            view.SetSong(song, SONG_PLAY_TYPE.discover_hardcore, DiscoveryLocation.discover_hardcore.ToString(),
                item.ItemIndex + 1, _onClickSong);
            view.ordering = song.ordering;
            view.HighlightCurrentSongIfNeed(SceneFader.instance.LastPlaySong);
        }
    }

    #endregion

    public void ShowSongs(LastStateData.Locations location, Action<Song> onClickSong = null,
        bool isAnimation = true) {
        lastLocation = location;
        _onClickSong = onClickSong;

        Show(isAnimation ? Direction.Right : Direction.None);
    }

    /// <summary>
    /// ScrollToTop
    /// </summary>
    /// <param name="onDone"></param>
    /// <param name="isAlreadyOnTop"></param>
    public void ScrollToTop(Action onDone, out bool isAlreadyOnTop) {
        float timeScroll = 0.2f;
        scrollerSongs.SmoothScrollTo(0, timeScroll, scrollerSongs.Parameters.ContentPadding.top, 0f);
        isAlreadyOnTop = ScrollPosition == 0;
        DOVirtual.DelayedCall(timeScroll, () => { onDone?.Invoke(); });
    }

    public void SetScrollState(SearchScript search, int lastCountItem, double lastScrollPosition) {
        search.StartCoroutine(search.IESetScrollState(scrollerSongs, _data, lastCountItem, lastScrollPosition));
    }
}