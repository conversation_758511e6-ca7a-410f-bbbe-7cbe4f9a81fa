using System;
using UnityEngine;
using UnityEngine.UI;

public class ItemGenreScript : MonoBehaviour {
    #region Fields

    [SerializeField] private Image        image;
    [SerializeField] private Button       button;
    [SerializeField] private Text         title;
    [SerializeField] private ScrollerText scrollTitle;

    public int    ordering;
    public Action onButtonClick;
    #endregion

    #region Unity Method

    private void Awake() {
        button.onClick.AddListener(OnBtnClick);
        LocalizationManager.instance.UpdateFont(title);
    }

    #endregion

    /// <summary>
    /// SetImage
    /// </summary>
    /// <param name="sprite"></param>
    public void SetImage(Sprite sprite) {
        image.sprite = sprite;
    }

    public void SetTitle(string titleName) {
        if(this.title == null) return;
        if (!string.IsNullOrEmpty(titleName)) {
            string localizedValue = GenreType.GetLocalizedValueOfGenre(titleName);
            this.title.text = localizedValue;
            scrollTitle.Refresh(true);
        } else {
            this.title.gameObject.SetActive(false);
        }
    }

    /// <summary>
    /// SetActive
    /// </summary>
    /// <param name="isActive"></param>
    public void SetActive(bool isActive) {
        gameObject.SetActive(isActive);
    }

    public virtual void OnBtnClick() {
        onButtonClick?.Invoke();
    }
}