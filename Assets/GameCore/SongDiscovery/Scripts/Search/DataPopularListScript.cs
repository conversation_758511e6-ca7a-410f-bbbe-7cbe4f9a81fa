using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

public class DataPopularListScript : DataCatalogs {
    #region Fields

    //public
    [Header("Config")] [SerializeField] private StandardScrollerAdapter scrollerAdapter;
    [SerializeField]                    private OptimizedCellView          prefCellView;

    [SerializeField] private Button        btnSeeAll;
    [SerializeField] private LocalizedText titleLocalized;

    //private
    private List<Song>  _listPopular;
    private bool        _isNoAllAds;
    private List<IData> _datas;
    public List<Song> ListPopular {
        get {
            if (_listPopular != null) return _listPopular;
            else {
                Dictionary<string, Song> songsByTag = SongManager.instance.GetByTag("POPULAR");
                if (songsByTag != null) {
                    _listPopular = songsByTag.Values.ToList();
                }
                return _listPopular;
            }
        }
    }
    #endregion

    #region Unity Method

    protected override void Awake() {
        base.Awake();
        scrollerAdapter.Init(prefCellView.transform as RectTransform);
        if (_listPopular == null || _listPopular.Count == 0) {
            //get data popular songs
            Dictionary<string, Song> songsByTag = SongManager.instance.GetByTag("POPULAR");
            if (songsByTag != null) {
                _listPopular = songsByTag.Values.ToList();
            }

            if (_listPopular == null || _listPopular.Count == 0) {
                Destroy(gameObject);
                return;
            }

            //random 10 first songs
            Random10FirstSongs(_listPopular);
        }
        //init
        btnSeeAll.onClick.AddListener(btnSeeAllOnClick);
        btnSeeAll.gameObject.SetActive(_listPopular.Count >= 4);
        _isNoAllAds = Configuration.IsNoAllAds();
    }

    private void Start() {
        _datas = new List<IData>();
        _datas.AddRange(_listPopular.Take(Mathf.Min(9, _listPopular.Count)));
        Configuration.instance.StartCoroutine(SetData());
    }

    private void OnEnable() {
        scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
        if (_isNoAllAds != Configuration.IsNoAllAds()) {
            _isNoAllAds = !_isNoAllAds;
        }
    }

    private void OnDisable() {
        scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
    }

    #endregion

    #region OptimizedScroller Handlers
    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_datas.Count < item.ItemIndex) return;
        if (item.cellView is SongItemSmall view) {
            Song song = (Song) _datas[item.ItemIndex];
            view.ordering = item.ItemIndex + 1;
            view.SetSong(song, DiscoveryLocation.discover_popular.ToString());
            view.song_play_type = SONG_PLAY_TYPE.discover_popular;
        }
    }
    private IEnumerator SetData() {
        while (!scrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
        scrollerAdapter.SetItems(_datas);
    }
    #endregion
    private void btnSeeAllOnClick() {
        SoundManager.PlayGameButton();
        catalogs02SearchScript.ShowSongs(SONG_PLAY_TYPE.discover_popular, titleLocalized.key, _listPopular);
        SearchScript.LogEventSeeAllClick(DiscoveryLocation.discover_popular.ToString());
    }

    private void Random10FirstSongs(List<Song> listPopular) {
        List<Song> firstSongs = new List<Song>();
        for (int index = 0; index < listPopular.Count; index++) {
            int range = Random.Range(0, listPopular.Count);
            Song song = listPopular[range];
            listPopular.Remove(song);
            firstSongs.Add(song);

            if (firstSongs.Count >= 10) {
                break;
            }
        }
        listPopular.InsertRange(0, firstSongs);
    }
}