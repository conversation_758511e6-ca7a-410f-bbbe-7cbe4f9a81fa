using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

public class DataRecommend02Script : DataCatalogs {
    #region Fields

    //public
    [Header("Config")] [SerializeField] private StandardScrollerAdapter scrollerAdapter;
    [SerializeField]                    private OptimizedCellView   prefCellView;

    [SerializeField] private Button btnSeeAll;
    [SerializeField] private LocalizedText titleLocalized;

    //private
    private List<Song> _listRecommended;

    private List<IData> _datas;

    #endregion

    #region Unity Method

    protected override void Awake() {
        base.Awake();
        scrollerAdapter.Init(prefCellView.transform as RectTransform);

        //get data recommend songs
        Dictionary<string, Song> songsByTag = SongManager.instance.GetByTag("HOT");
        if (songsByTag != null) {
            _listRecommended = songsByTag.Values.ToList();
        }

        if (_listRecommended == null || _listRecommended.Count == 0) {
            Destroy(gameObject);
            return;
        }

        btnSeeAll.onClick.AddListener(btnSeeAllOnClick);
        btnSeeAll.gameObject.SetActive(_listRecommended.Count >= 4);
    }
    private void Start() {
        _datas = new List<IData>();
        _datas .AddRange( _listRecommended.Take(Mathf.Min(9, _listRecommended.Count)));

        Configuration.instance.StartCoroutine(SetData());
    }

    private void OnEnable() {
        scrollerAdapter.OnItemVisible += OnScroll_OnItemVisible;
    }

    private void OnDisable() {
        scrollerAdapter.OnItemVisible -= OnScroll_OnItemVisible;
    }
    #endregion

    #region OptimizedScroller Handlers
    private void OnScroll_OnItemVisible(StandardItemViewsHolder item) {
        if (_datas.Count < item.ItemIndex) return;
        if (item.cellView is SongItemSmall view) {
            Song song = (Song) _datas[item.ItemIndex];
            view.ordering = item.ItemIndex + 1;
            view.SetSong(song, DiscoveryLocation.discover_popular.ToString());
            view.song_play_type = SONG_PLAY_TYPE.discover_popular;
        }
    }
    private IEnumerator SetData() {
        while (!scrollerAdapter.IsInitialized) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
        scrollerAdapter.SetItems(_datas);
    }
    #endregion
    private void btnSeeAllOnClick() {
        catalogs02SearchScript.ShowSongs(SONG_PLAY_TYPE.discover_recommend, titleLocalized.key, _listRecommended);
        SearchScript.LogEventSeeAllClick(DiscoveryLocation.discover_recommend.ToString());
    }
}