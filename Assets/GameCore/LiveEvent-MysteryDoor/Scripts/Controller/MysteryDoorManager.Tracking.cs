using System.Collections.Generic;

namespace GameCore.LiveEvent.MysteryDoor {
    public partial class MysteryDoorManager {
        private static HashSet<SONG_STATUS> _listSongStatusToOverridePlayType;

        public static bool CheckCanOverrideSongPlayType(SONG_STATUS songStatus) {
            if (!EnableFeature  || state != State.Active) {
                return false;
            }
            
            _listSongStatusToOverridePlayType ??= new HashSet<SONG_STATUS>() {
                SONG_STATUS.song_start,
                SONG_STATUS.song_end,
                SONG_STATUS.song_result,
                SONG_STATUS.song_ap,
                SONG_STATUS.me_start,
                SONG_STATUS.me_result,
            };

            return _listSongStatusToOverridePlayType.Contains(songStatus);
        }

    }
}