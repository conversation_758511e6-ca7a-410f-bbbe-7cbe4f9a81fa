using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace GameCore.LiveEvent.MysteryDoor {
	public class UIOnboarding : MonoBehaviour, IEscapeHandler {
		[SerializeField] private bool highlightsInteractable = true;
		[SerializeField] private CanvasGroup canvasGroup;
		[SerializeField] protected Transform[] highLights;
		[SerializeField] protected Button btnClose;
		[SerializeField] private Transform tfCached;
		[SerializeField] private bool canEscape = true;

		private readonly Dictionary<Transform, CanvasHighlight> _originalDictionary = new();
		private bool _isHiding;

		protected virtual void OnEnable() {
			_isHiding = false;
			HighLightObjects();
			Show();
			EventEscapeManager.Push(this);
		}

		protected virtual void OnDisable() {
			EventEscapeManager.Pop(this);
			if (_originalDictionary != null && _originalDictionary.Count != 0) {
				DeHighLightObjects();
			}
		}

		protected virtual void Start() {
			btnClose.onClick.AddListener(OnBtnCloseClicked);
		}


		private void OnBtnCloseClicked() {
			if (_isHiding) return;
			Hide();
		}

		private void Show() {
			canvasGroup.alpha = 0f;
			canvasGroup.DOFade(1f, 0.2f);
		}

		protected void Hide() {
			_isHiding = true;
			canvasGroup.DOFade(0f, 0.2f).OnComplete(HandleCompleteHighlight);
		}

		protected void HandleCompleteHighlight() {
			DeHighLightObjects();
			gameObject.SetActive(false);
		}

		protected virtual void HighLightObjects() {
			foreach (var item in highLights) {
				if (!item) continue;
				if (_originalDictionary.ContainsKey(item)) continue;
				// item.SetParent(tfCached);

				bool isCreatedNewCanvas = false;

				if (!item.TryGetComponent(out Canvas itemCanvas)) {
					isCreatedNewCanvas = true;
					itemCanvas = item.gameObject.AddComponent<Canvas>();
				}

				GraphicRaycaster itemCanvasRaycaster = null;
				if (highlightsInteractable) {
					if (!item.TryGetComponent(out itemCanvasRaycaster)) {
						itemCanvasRaycaster = item.gameObject.AddComponent<GraphicRaycaster>();
					}
				}

				itemCanvas.overrideSorting = true;
				itemCanvas.sortingLayerID = SortingLayer.NameToID("SubCamera");

				_originalDictionary[item] = new CanvasHighlight() {
					isCreatedNew = isCreatedNewCanvas,
					canvasRaycaster = itemCanvasRaycaster,
					canvas = itemCanvas
				};
			}
		}

		protected virtual void DeHighLightObjects() {
			foreach (var item in _originalDictionary) {
				// item.Key.SetParent(item.Value);
				var canvasInfo = item.Value;
				canvasInfo.canvas.overrideSorting = false;
				if (canvasInfo.isCreatedNew) {
					// remove raycaster before canvas
					if (canvasInfo.canvasRaycaster) {
						Destroy(canvasInfo.canvasRaycaster);
					}

					Destroy(canvasInfo.canvas);
				}
			}
			_originalDictionary.Clear();
		}

		#region Escape Callback

		public bool CanHandleEventBack() {
			return gameObject.activeInHierarchy;
		}

		public virtual bool HandleEventBack() {
			if (_isHiding) return false;
			if (!canEscape) return false;
			Hide();
			return true;
		}

		#endregion
	}
}

