using UnityEngine;

namespace GameCore.LiveEvent.MysteryDoor {
    public class UIMysteryDoorKeySprite : MonoBehaviour {
        private KeyConfigStage _stageConfig;
        private KeyConfigData  _keyConfigData;
        private bool           _loadData = false;

        private void LoadData() {
            if (_loadData) {
                return;
            }

            _loadData = true;
            _keyConfigData = Resources.Load<KeyConfigData>("MysteryDoor/KeyConfigData");
            _stageConfig = MysteryDoorManager.instanceSafe.stageConfig;
        }

        public (KeyStageConfig config, KeyConfig sprite) GetConfigKey(byte idStage, byte b) {
            LoadData();
            if (idStage < 1)
                return (null, null);

            var stageData = _stageConfig.GetStage(idStage);
            if (stageData == null) {
                return (null, null);
            }

            if (b >= stageData.keys.Length)
                return (null, null);

            var configData = stageData.keys[b];
            var spriteData = _keyConfigData.GetConfig(configData.id);
            return (configData, spriteData);
        }
    }
}