using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Sirenix.OdinInspector;
using TileHop.EventDispatcher;
using TilesHop;
using TilesHop.Cores.Pooling;
using TilesHop.GameCore;
using UnityEngine;
using UnityEngine.UI;

namespace GameCore.LiveEvent.MysteryDoor {
    public class UIMysteryDoor : PopupUI {
		[Header("Sound")]
		[SerializeField] private AudioSource bgm;
        [SerializeField] private float volume;

		[Header("Top")]
        [SerializeField] private Button btnClose;

        [SerializeField] private Button         btnInfo;
        [SerializeField] private UIKeySlotGroup keySlotGroup;
        [SerializeField] private Image          bgStage;
        [SerializeField] private Sprite[]       spriteStages;

        [Header("Middle")]
        //[SerializeField] private UICountDown uiCountDown;
        [SerializeField] private Text txtCountdown;

        [SerializeField] private UIMysteryDoorMilestone[] milestones;
        [SerializeField] private Image[]                  progressImgs;
        [SerializeField] private RectTransform            curStageCheckTrans;

        [SerializeField] private Text txtCurrentStage;

        [Header("Bottom")]
        [SerializeField] private Transform iconDrillBit;

        [SerializeField] private Text txtDrillBit;

        [Space]
        [SerializeField] private GridLayoutGroup stoneGroupLayout;

        [SerializeField] private RectTransform stoneGroupTransform;
        [SerializeField] private UIItemStone stonePrefab;

        [Space]
        [SerializeField] private Transform keyParent;

        [SerializeField] private Image     keyPrefab;
        [SerializeField] private Text noDrillNoti;
        [SerializeField] private UITooltip tooltipUseDrillBeforeEnd;

        [Header("Help")]
        [SerializeField] private UIMysteryDoorKeySprite sprites;
        public Canvas holderCanvas;

        [Header("Change stage")]
        [SerializeField] private UIMysteryDoorAnim animChangeStage;

        [SerializeField] private GameObject objEnd;

        [ShowInInspector] private byte               _currentStage;
        private                   int                _amountDrill;
        private                   MysteryDoorManager _manager;
        private                   int                _countIECollectKey;

        [ShowInInspector] private UIItemStone[]          _stoneList;
        private                   Vector2                _rectStoneGroup;
        private                   float                  _cellSize;
        private                   List<Image>            _listKey    = new List<Image>();
        private                   Dictionary<byte, byte> _dictKeyPos = new Dictionary<byte, byte>();

        private bool _isAnimateVFX;
        private bool _isChangeStage;
        private bool _isRecievingReward;

        private UIStandardReward        _uiStandardReward;
        private UIMysteryDoorOnboarding _onboarding;

        private readonly float _timeScaleStone  = 0.3f;
        private readonly float _delayScaleStone = 0.02f;

        private Sequence _sequenceNoti;

        private UIAutoRecycleFx _drillFxPref;
        private UIAutoRecycleFx _breakStoneFxPref;
        private UIAutoRecycleFx _keyActiveFxPref;

        public static Action OnClose;

		private void Awake() {
            _manager = MysteryDoorManager.instanceSafe;
        }

        private void Start() {
            btnClose.onClick.AddListener(Close);
            btnInfo.onClick.AddListener(OnClickBtnInfo);
            animChangeStage.BindingKeys(keySlotGroup.groupKeys.gameObject);
        }

        protected override void OnEnable() {
            base.OnEnable();

			UIItemStone.OnDrill += UIItemStoneOnOnDrill;
            MysteryDoorManager.OnChangeDrill += UpdateAmountDrill;
            MysteryDoorManager.OnChangeStage += MysteryDoorManagerOnOnChangeStage;
            MysteryDoorManager.OnChangeState += MysteryDoorManagerOnOnChangeState;

            UpdateAmountDrill(MysteryDoorManager.currentDrill);

            StartCoroutine(IECountdown());
            SetUpVFX();
            _currentStage = _manager.levelReward;
            var stage = _manager.GetStage(_currentStage);
            if (stage == null) {
                _currentStage = _manager.idStage;
            }

            SetupKeyGroup(_currentStage);
            SetUpStage(_currentStage);
            SetBGReward(_currentStage);
            SetUpMilestones(_currentStage);
            StartCoroutine(IEProcessThread());

            objEnd.SetActive(_manager.isCompleted);
            curStageCheckTrans.gameObject.SetActive(!_manager.isCompleted);
            btnClose.gameObject.SetActive(true);

            StartCoroutine(IEPlayBGM());
        }

        protected override void OnDisable() {
            UIItemStone.OnDrill -= UIItemStoneOnOnDrill;
            MysteryDoorManager.OnChangeDrill -= UpdateAmountDrill;
            MysteryDoorManager.OnChangeStage -= MysteryDoorManagerOnOnChangeStage;
            MysteryDoorManager.OnChangeState -= MysteryDoorManagerOnOnChangeState;
            base.OnDisable();

			if (bgm && bgm.clip) {
				if (Configuration.instance && Configuration.instance.MusicIsOn()) {
					bgm.Stop();
					PreviewSongController.instanceSafe.ResumePreviewMusic();
				}
			}
            
            this.PostEvent(EventID.MysteryDoorStartingDone);
		}

        private IEnumerator IEPlayBGM() {
            PreviewSongController.instanceSafe.PausePreviewMusic();
            yield return YieldPool.GetWaitForSeconds(1f);
			if (bgm && bgm.clip) {
				if (Configuration.instance.MusicIsOn()) {
					GroundMusic.instance.StopMusic();
					bgm.volume = this.volume;
					bgm.Play();
				}
			}
		}

		private Transform GetGuideHandStone() {
            foreach (UIItemStone stone in _stoneList) {
                if (stone.CanOpen) {
                    if (!stone.IsEmpty)
                        return stone.transform;
                }
            }
            return null;
        }

        public override void Close() {
            base.Close();
            if (_manager) {
                _manager.TrySaveUserData();
            }
            if(MysteryDoorManager.state == MysteryDoorManager.State.DeActive && !_manager.isCompleted) {
                MysteryDoorManager.instanceSafe.EndEvent();
			}
			MysteryDoorAudioHelper.PlayGameButtonSFX();
			OnClose?.Invoke();
        }

        private void UIItemStoneOnOnDrill(UIItemStone stone) {
            if (_manager.isCompleted || _isChangeStage)
                return;

            if (_amountDrill <= 0) {
                //Logger.EditorLogError(MysteryDoorManager.FEATURE_NAME, "Not enough drill bit");
                iconDrillBit.DOKill();
                iconDrillBit.transform.SetScale(1f);
                iconDrillBit.DOPunchScale(Vector3.one * 0.3f, 0.2f);
                ShowNoDrillNoti();
                return;
            }

            var result = _manager.DrillCell(stone.row, stone.col);
            if (!result.success) {
                return;
            }

            stone.Drilled(result.keyValue);
            _drillFxPref.Spawn(stone.transform, Vector3.back * 50);
            SoundManager.PlayMysteryDoorDrill();
            if (!result.hasReward) {
                CheckEndEvent();
                return;
            }

            byte idReward = result.keyValue;
            StartCoroutine(IECollectKey(idReward));
        }

        private IEnumerator IECollectKey(byte idReward) {
            _isAnimateVFX = true;
            _countIECollectKey++;
            _manager.CollectKey(idReward);
            var key = _listKey[idReward - 1];
            float timeShake = 0.5f;
            key.transform.DOShakeScale(timeShake, 1.2f);
            SoundManager.PlayMysteryDoorCollectKey();
            yield return YieldPool.GetWaitForSeconds(timeShake + 0.3f);

            Transform target = keySlotGroup.GetTarget(idReward);
            float timeMove = 1f;
            Canvas canvas = key.gameObject.AddComponent<Canvas>();
            canvas.overrideSorting = true;
            Canvas parentCanvas = holderCanvas;
            canvas.sortingLayerName = parentCanvas.sortingLayerName;
            canvas.sortingOrder = parentCanvas.sortingOrder + 1;

            GameObject trail = key.transform.GetChild(0).gameObject;
            trail.SetActive(true);
            key.transform.DOMove(target.position, timeMove).SetEase(Ease.OutQuad);

            float scale = target.GetComponent<RectTransform>().rect.width / key.rectTransform.rect.width;

            key.transform.DOScale(scale, timeMove);
            yield return YieldPool.GetWaitForSeconds(timeMove + 0.1f);

            SoundManager.PlayMysteryDoorActiveKey();
            _keyActiveFxPref.Spawn(target);
            Destroy(canvas);
            key.gameObject.SetActive(false);

            keySlotGroup.SetCollected(idReward);

            _countIECollectKey--;
            if (_countIECollectKey == 0) { // handle in last IE collect key!!!
                _isAnimateVFX = false;
                if (_isChangeStage) {
                    if (_isRecievingReward) {
                        yield break;
                    }
                    
                    if (_manager.HasReward()) {
                        yield return IEGetReward(_manager.userData.levelReward);
                    }

                    ChangeStage();
                } else {
                    CheckEndEvent();
                }
            } else {
                CheckEndEvent();
            }
        }

        private IEnumerator IEProcessThread() {
            yield return YieldPool.GetWaitForEndOfFrame();

            curStageCheckTrans.anchoredPosition3D =
                milestones[_currentStage - 1].GetComponent<RectTransform>().anchoredPosition3D;
            while (isAnimating) {
                yield return YieldPool.GetWaitForEndOfFrame();
            }

            if (_manager.NeedShowOnboarding()) {
                yield return _manager.IEShowOnboarding();
            }

            if (_manager.NeedShowOnboardingHand()) {
				Transform targetStone = GetGuideHandStone();
                if (targetStone != null) {
                    yield return _manager.IEShowHandOnboarding(targetStone, holderCanvas.transform);
                }
            }

            if (!_manager.HasReward()) {
                yield break;
            }

            yield return IEGetReward(_manager.levelReward);
            yield return IEChangeStage();
        }

        private IEnumerator IEGetReward(byte level) {
            _isRecievingReward = true;
			for (int i = 0; i < _stoneList.Length; i++) {
				if (!_stoneList[i].CanOpen)
					continue;

				_stoneList[i].Break();
				_breakStoneFxPref.Spawn(_stoneList[i].transform, Vector3.back * 50);
			}

			yield return animChangeStage.Unlock(level);

            var reward = _manager.GetListReward();
            if (reward == null) {
                yield break;
            }

            if (!_uiStandardReward) {
                var popup = Util.ShowPopUp(PopupName.RewardStandardv3, holderCanvas.transform);
                if (popup) {
                    _uiStandardReward = popup.GetComponent<UIStandardReward>();
                }
            }

            bool isWait = false;
            if (_uiStandardReward) {
                isWait = true;
                _manager.GotReward();
                _uiStandardReward.Show(reward, "mystery_door", CurrencyEarnSource.mystery_door,
                    SongUnlockType.mystery_door, BoosterEarnSource.mystery_door, 100, () => { isWait = false; });
            }

            while (isWait) {
                yield return YieldPool.GetWaitForEndOfFrame();
            }

            if (!_manager.isCompleted && !IsEndEvent()) {
                curStageCheckTrans.GetComponent<RectTransform>().DOAnchorPosX(
                    milestones[_currentStage].GetComponent<RectTransform>().anchoredPosition.x, 0.5f);
                progressImgs[_currentStage - 1].DOFillAmount(1f, 0.5f);

                yield return YieldPool.GetWaitForSeconds(0.5f);

                SetUpMilestones(_manager.levelReward);
            } else {
                SetUpMilestones(_manager.levelReward);
                objEnd.SetActive(true);
                curStageCheckTrans.gameObject.SetActive(false);
            }

            _isRecievingReward = false;

            yield return null;
        }

        private void ChangeStage() {
            if (_manager.isCompleted) {
                _manager.FinishEvent();
                _isChangeStage = false;
				btnClose.gameObject.SetActive(true);
				return;
            }else if(IsEndEvent()) {
                CheckEndEvent();
                _manager.EndEvent();
                _isChangeStage = false;
				btnClose.gameObject.SetActive(true);
				return;
            }

            //todo: update UI middle
            StartCoroutine(IEChangeStage());
        }

        private IEnumerator IEChangeStage() {
            _currentStage = _manager.levelReward;
            SetUpStage(_currentStage, false);
            SetupKeyGroup(_currentStage);

			yield return animChangeStage.Lock();

            SetBGReward(_currentStage);
            animChangeStage.Idle();

            _isChangeStage = false;
			btnClose.gameObject.SetActive(true);
		}

        private void MysteryDoorManagerOnOnChangeStage() {
            if (_isAnimateVFX) {
                _isChangeStage = true;
                btnClose.gameObject.SetActive(false);
            } else {
                ChangeStage();
            }
        }

        private void UpdateAmountDrill(int current) {
            _amountDrill = current;
            txtDrillBit.text = current.ToString();
        }

        private void SetUpVFX() {
            if (!_drillFxPref) {
                _drillFxPref = Resources.Load<GameObject>("MysteryDoor/VFX_Drill").GetComponent<UIAutoRecycleFx>();
                _drillFxPref.CreatePool(5);
            } else {
                _drillFxPref.RecycleAll();
            }

            if (!_keyActiveFxPref) {
                _keyActiveFxPref = Resources.Load<GameObject>("MysteryDoor/KeyActiveFx")
                    .GetComponent<UIAutoRecycleFx>();
                _keyActiveFxPref.CreatePool(3);
            } else {
                _keyActiveFxPref.RecycleAll();
            }

            if (!_breakStoneFxPref) {
                _breakStoneFxPref = Resources.Load<GameObject>("MysteryDoor/VFX_StoneBreak").GetComponent<UIAutoRecycleFx>();
                _breakStoneFxPref.CreatePool(15);
            } else {
                _breakStoneFxPref.RecycleAll();
            }
        }

        private void SetupKeyGroup(byte idStage) {
            keySlotGroup.Setup(idStage, _manager.GetKeys(idStage), sprites);
            var collectedKeys = _manager.GetCollectedKey(idStage);
            if (collectedKeys.IsNullOrEmpty()) {
                return;
            }

            keySlotGroup.SetCollected(collectedKeys);
        }

        private void SetUpStage(byte idStage, bool useAnim = false) {
            if (_stoneList.IsNullOrEmpty()) {
                _stoneList = new UIItemStone[64];

                for (int i = 0; i < _stoneList.Length; i++) {
                    _stoneList[i] = Instantiate(stonePrefab, stoneGroupTransform);
                }
            }

            _dictKeyPos.Clear();
            var matrix = _manager.GetStageMatrix(idStage);
            int total = matrix.width * matrix.height;
            for (byte i = 0; i < matrix.width; i++) {
                for (byte j = 0; j < matrix.height; j++) {
                    byte index = (byte) (i * matrix.width + j);
                    byte value = matrix.matrix[i, j];
                    if (value > 99) {
                        value -= 100;
                    }
#if UNITY_EDITOR
                    _stoneList[i].gameObject.name = $"Stone[{i}][{j}] {value}";
#endif
                    if (value > 0) {
                        _dictKeyPos.Add(index, value);
                    }

                    _stoneList[index].Setup(i, j, matrix.matrix[i, j]);
                    if (useAnim) {
                        _stoneList[index].transform.localScale = Vector3.zero;
                        _stoneList[index].gameObject.SetActive(true);
                        //_stoneList[index].transform.DOScale(1, _timeScaleStone).SetEase(Ease.OutBack)
                        //    .SetDelay(index * _delayScaleStone);
                        _stoneList[index].transform.SetScale(1);

                    } else {
                        _stoneList[index].transform.localScale = Vector3.one;
                        _stoneList[index].gameObject.SetActive(true);
                    }
                }
            }

            for (int i = total; i < _stoneList.Length; i++) {
#if UNITY_EDITOR
                _stoneList[i].gameObject.name = "Stone Disable";
#endif
                _stoneList[i].gameObject.SetActive(false);
            }

            stoneGroupLayout.constraintCount = matrix.width;
            _rectStoneGroup = stoneGroupTransform.sizeDelta;
            float sizeX = _rectStoneGroup.x / matrix.width;
            float sizeY = (_rectStoneGroup.y - 10) / matrix.height;
            _cellSize = Mathf.Min(sizeX, sizeY);
            stoneGroupLayout.cellSize = Vector2.one * _cellSize;
            float delay = useAnim ? total * _delayScaleStone : 0;
            StartCoroutine(IESetupKey(idStage, delay));
        }

        private void SetBGReward(byte idStage) {
            bgStage.sprite = spriteStages[idStage - 1];
        }

        private IEnumerator IESetupKey(byte idStage, float delayTime) {
            foreach (Image key in _listKey) {
                key.gameObject.SetActive(false);
            }

            yield return YieldPool.GetWaitForSeconds(delayTime);

            var keys = _manager.GetKeys(idStage);
            var collectedKey = _manager.GetCollectedKey(idStage);
            for (byte i = 0; i < keys.Length; i++) {
                var key = keys[i];
                var itemKey = GetItemKey(i);
                if (collectedKey != null && collectedKey.Contains((byte) (i + 1))) {
                    //collected key
                    itemKey.gameObject.SetActive(false);
                } else {
                    var data = sprites.GetConfigKey(_currentStage, i);
#if UNITY_EDITOR
                    itemKey.gameObject.name = $"KEY {data.config.id}";
#endif
                    itemKey.sprite = data.sprite.iconMain;
                    var rect = (RectTransform) itemKey.transform;
                    if (data.config.direction == Direction.horizontal) {
                        rect.sizeDelta = new Vector2(key.w, key.h) * _cellSize;
                    } else {
                        rect.sizeDelta = new Vector2(key.h, key.w) * _cellSize;
                    }

                    rect.rotation = Quaternion.Euler(data.config.rotation);
                    rect.position = GetPositionForKey(i + 1);
                    rect.localScale = Vector3.one;
                    itemKey.gameObject.SetActive(true);
                }
            }
        }

        private Vector3 GetPositionForKey(int idKey) {
            Vector3 position = Vector3.zero;
            byte count = 0;
            foreach (var pair in _dictKeyPos) {
                if (pair.Value == idKey) {
                    position += _stoneList[pair.Key].transform.position;
                    count++;
                }
            }

            if (count == 0) {
                Logger.EditorLogError(MysteryDoorManager.FEATURE_NAME, $"Not found pos for key {idKey}");
                return Vector3.zero;
            } else {
                return position / count;
            }
        }

        private Image GetItemKey(byte index) {
            if (_listKey.Count > index)
                return _listKey[index];

            var key = Instantiate(keyPrefab, keyParent);
            key.preserveAspect = true;
            _listKey.Add(key);
            return key;
        }

		private IEnumerator IECountdown() {
			var delay = YieldPool.GetWaitForSeconds(1f);
			int timeLeft = _manager.GetRemainingTime();
			while (timeLeft > 0) {
				timeLeft = _manager.GetRemainingTime();
                if (timeLeft < 0) {
                    timeLeft = 0;
                }
				MysteryDoorHelper.FormatTimeToDisplay(timeLeft, txtCountdown, timeLeft, "000000");
				yield return delay;
			}
			OnCompleteCountDown();
		}

        private void SetUpMilestones(byte idStage) {
            for (int i = 0; i < milestones.Length; i++) {
                milestones[i].SetUp(i + 1, i + 1 == idStage, i + 1 < idStage, _manager.rewardDatas[i]);
            }

            for (int i = 0; i < progressImgs.Length; i++) {
                progressImgs[i].fillAmount = (idStage > i + 1) ? 1 : 0;
            }
        }

        private void OnCompleteCountDown() {
            if(_amountDrill > 0) {
                tooltipUseDrillBeforeEnd.OnClick();
            }
        }

        private void MysteryDoorManagerOnOnChangeState() {
            if (MysteryDoorManager.state == MysteryDoorManager.State.DeActive) {
                //Close();
            }
        }

        #region Show Onboarding

        private bool _isLoadingInfo;
        
        private void OnClickBtnInfo() {
            if (_isLoadingInfo) {
                return;
            }

			SoundManager.PlayGameButton();
			if (!_onboarding) {
                _isLoadingInfo = true;
                StartCoroutine(Util.ShowPopupAsync<UIMysteryDoorOnboarding>(PopupName.MysteryDoorOnboarding,
                    holderCanvas.transform, callback: HandleOnLoadInfoDone));

            } else {
                ShowInfo();
            }
		}

        private void HandleOnLoadInfoDone(UIMysteryDoorOnboarding popup) {
            if (!popup) {
                CustomException.Fire("[Mystery Door Onboarding]", "Cant load onboarding popup from resources!");
                return;
            }

            _onboarding = popup;
            ShowInfo();
        }

        private void ShowInfo() {
            if (!_onboarding) {
                return;
            }
            _onboarding.Show(null);
            MysteryDoorTracker.Track_EventInfo(_manager.questId);
        }
        
        #endregion

        private void ShowNoDrillNoti() {
            SoundManager.PlayMysteryDoorNoDrill();

            if (_sequenceNoti != null) {
                _sequenceNoti.Restart();
                _sequenceNoti.Play();
                return;
            }

            _sequenceNoti = DOTween.Sequence();
            _sequenceNoti.Append(noDrillNoti.transform.DOScale(Vector3.zero, 0f));
            _sequenceNoti.Join(noDrillNoti.transform.DOLocalMoveY(0f, 0f));
			_sequenceNoti.Join(noDrillNoti.DOFade(1, 0f));
			_sequenceNoti.Append(noDrillNoti.transform.DOScale(1f, 0.25f).SetEase(Ease.OutBack));
            _sequenceNoti.Append(noDrillNoti.transform.DOLocalMoveY(40f, 2f));
            _sequenceNoti.AppendInterval(0.2f);
            _sequenceNoti.Append(noDrillNoti.DOFade(0, 0.5f));
            _sequenceNoti.AppendCallback(() => noDrillNoti.transform.SetScale(0));

            _sequenceNoti.SetAutoKill(false);
            _sequenceNoti.SetUpdate(true);
        }

        private void CheckEndEvent() {
            if(IsEndEvent()) {
                DOVirtual.DelayedCall(1f, ()=>objEnd.SetActive(true));
            }
        }

        private bool IsEndEvent() {
            return MysteryDoorManager.state == MysteryDoorManager.State.DeActive && MysteryDoorManager.currentDrill == 0;

		}
    }
}