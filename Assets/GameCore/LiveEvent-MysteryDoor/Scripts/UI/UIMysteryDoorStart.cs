using DG.Tweening;
using Spine;
using Spine.Unity;
using System;
using TileHop.EventDispatcher;
using UnityEngine;
using UnityEngine.UI;

namespace GameCore.LiveEvent.MysteryDoor {
    public class UIMysteryDoorStart : PopupUI {
		private const string ANIM_IN = "Intro";
		private const string ANIM_IDLE = "Idle";
		private const string ANIM_OUT = "Out";
		[SerializeField] private Button btnStart;
        [SerializeField] private Button btnClose;
        [SerializeField] private SkeletonGraphic skeletonGraphic;
		[SerializeField] private Transform[] animatedTransforms;
		[SerializeField] private float animatedDuration;
		[SerializeField] private CanvasGroup txtDesc;
		[SerializeField] private ParticleSystem vfxDragDoorL;
		[SerializeField] private ParticleSystem vfxDragDoorR;
		private bool _accept;
        private Action<bool> _callback;

		private Tweener[] animatedTweeners;
		private int animatedCount;
		public static bool IsAutoShow;
        private bool _isAnimationShowed;

		private bool _isEnableBtnClose => MysteryDoorManager.instanceSafe.userData.questId != 1;

		public override bool HandleEventBack() {
			return false;
		}

		private void Awake() {
			if (skeletonGraphic != null) {
				skeletonGraphic.AnimationState.Complete += HandleOnAnimationComplete;
			}
		}

		private void Start() {
            btnStart.onClick.AddListener(OnBtnStartClicked);
            btnClose.onClick.AddListener(OnBtnCloseClicked);
        }

        protected override void OnEnable() {
            base.OnEnable();
            _accept = false;
			for (int i = 0; i < animatedCount; i++) {
				animatedTweeners[i].PlayForward();
			}
		}

        protected override void OnDisable() {
            base.OnDisable();
            _callback?.Invoke(_accept);
        }

		private void HandleOnAnimationComplete(TrackEntry trackEntry) {
			if (trackEntry.Animation.Name.Equals(ANIM_IN)) {
				PlayAnimIdle();
				btnClose.transform.DOScale(1f, 0.5f).SetEase(Ease.OutBack).From(0);
			}
		}

		private void OnBtnCloseClicked() {
            Close();
			MysteryDoorAudioHelper.PlayGameButtonSFX();
		}

        private void OnBtnStartClicked() {
			MysteryDoorAudioHelper.PlayGameButtonSFX();
			_accept = true;
            Close();
        }

        public void Show(Action<bool> callback) {
            this._callback = callback;
            this.gameObject.SetActive(true);

			btnClose.gameObject.SetActive(_isEnableBtnClose);
			btnClose.transform.SetScale(0f);
			if(IsAutoShow && !_isAnimationShowed && skeletonGraphic != null) {
				PlayAnimIn();
			} else {
				_isAnimationShowed = true;
				PlayAnimIdle();
				if (animatedCount == 0) {
					SetupAppearAnimation();
				}
			}
		}

        public override void Close() {
            if (skeletonGraphic != null && IsAutoShow && !_isAnimationShowed) {
				//PlayAnimOut();
				_isAnimationShowed = true;
				IsAutoShow = false;

				// destroy-on-close when using popup animation because of anim transition at opening
				destroyOnClose = true;
			}

			base.Close();
		}

		protected virtual void PlayAnimIn() {
			if (skeletonGraphic == null) {
				return;
			}
			skeletonGraphic.AnimationState.SetAnimation(0, ANIM_IN, false);
			DOVirtual.DelayedCall(0.5f, () => {
				vfxDragDoorL.gameObject.SetActive(true);
				vfxDragDoorL.Play();
				vfxDragDoorR.gameObject.SetActive(true);
				vfxDragDoorR.Play();
			});

			txtDesc.alpha = 0;
			txtDesc.DOFade(1f, 0.5f).SetDelay(1f);
		}

		private void PlayAnimOut() {
			if (skeletonGraphic == null) {
				return;
			}
			skeletonGraphic.AnimationState.SetAnimation(0, ANIM_OUT, false);
			bg.DOFade(0f, 1.5f).SetEase(Ease.Linear);
			btnClose.transform.DOScale(0f, 0.5f).SetEase(Ease.OutBack);
		}

		private void PlayAnimIdle() {
			if (skeletonGraphic == null) {
				return;
			}
			vfxDragDoorL.gameObject.SetActive(false);
			vfxDragDoorR.gameObject.SetActive(false);
			skeletonGraphic.AnimationState.SetAnimation(0, ANIM_IDLE, true);
			txtDesc.alpha = 1;
			btnClose.transform.SetScale(1f);
		}

		protected virtual void SetupAppearAnimation() {
			animatedCount = animatedTransforms.Length;
			animatedTweeners = new Tweener[animatedCount];

			// setup animated objects
			for (int i = 0; i < animatedCount; i++) {
				int index = i;
				animatedTweeners[index] = DOTween.To(
					x => animatedTransforms[index].localScale = Vector3.one * x,
					0.0f,
					1.0f,
					animatedDuration).SetAutoKill(false).SetEase(Ease.OutBack).SetId(index).Pause();
			}
		}
	}
}