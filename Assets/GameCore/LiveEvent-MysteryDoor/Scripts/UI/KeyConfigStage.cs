using System;
using UnityEngine;

namespace GameCore.LiveEvent.MysteryDoor {
    [CreateAssetMenu(fileName = "KeyConfigStage", menuName = "ScriptableObjects/KeyConfigStage")]
    public class KeyConfigStage : ScriptableObject {
        public StageConfig[] stages;

        public StageConfig GetStage(byte idStage) {
            for (int i = 0; i < stages.Length; i++) {
                if (stages[i].idStage == idStage) {
                    return stages[i];
                }
            }

            return null;
        }
    }

    [Serializable]
    public class StageConfig {
        public byte             idStage;
        public KeyStageConfig[] keys;
    }

    [Serializable]
    public class KeyStageConfig {
        public byte      id;
        public Direction direction;
        public Vector3   position;
        public Vector3   rotation;
    }

    public enum Direction {
        horizontal,
        vertical,
    }
}