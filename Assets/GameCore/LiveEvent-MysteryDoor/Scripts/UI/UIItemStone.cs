using DG.Tweening;
using System;
using UnityEngine;
using UnityEngine.UI;

namespace GameCore.LiveEvent.MysteryDoor {
	public class UIItemStone : MonoBehaviour {
		[SerializeField] private CanvasGroup maskStone;
		[SerializeField] private Text txtStone;
		[SerializeField] private Button btnStone;

		public byte col;
		public byte row;

		private bool _canOpen;
		private bool _isEmpty;
		public static event Action<UIItemStone> OnDrill;
		public bool CanOpen => _canOpen;
		public bool IsEmpty => _isEmpty;

		private void Start() {
			btnStone.onClick.AddListener(OnClickBtnStone);
		}

		private void OnEnable() {
			UIMysteryDoorAdmin.OnChangeDebug += UIMysteryDoorAdminOnOnChangeDebug;
		}

		private void OnDisable() {
			UIMysteryDoorAdmin.OnChangeDebug -= UIMysteryDoorAdminOnOnChangeDebug;
		}

		private void OnClickBtnStone() {
			if (!_canOpen) {
				return;
			}

			OnDrill?.Invoke(this);
		}

		public void Setup(byte row, byte col, byte value) {
			this.col = col;
			this.row = row;
			this._canOpen = value < 100;
			this._isEmpty = value is 0 or 100;
			if (!_canOpen) {
				Drilled(value - 100);
			} else {
				maskStone.gameObject.SetActive(true);
				maskStone.alpha = 1f;
				txtStone.text = value != 0 ? value.ToString() : string.Empty;
				txtStone.gameObject.SetActive(UIMysteryDoorAdmin.DEBUG);
			}
		}

		public void Drilled(int value) {
			_canOpen = false;
			Break();
			if (value > 0) {
				txtStone.text = value.ToString();
				txtStone.gameObject.SetActive(UIMysteryDoorAdmin.DEBUG);
			} else {
				txtStone.gameObject.SetActive(false);
			}
		}

		private void UIMysteryDoorAdminOnOnChangeDebug() {
			txtStone.gameObject.SetActive(UIMysteryDoorAdmin.DEBUG);
		}

		public void Break() {
			maskStone.DOFade(0, 0.1f)
					.SetDelay(0.25f)
					.OnComplete(() => maskStone.gameObject.SetActive(false));
		}
	}
}