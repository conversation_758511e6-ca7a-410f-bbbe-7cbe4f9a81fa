using System;
using System.Collections.Generic;
using System.Linq;
using Inwave;
using UnityEngine;
using UnityEngine.UI;

public class UIAdminPopup_SpecialTile : PopupUI {
    [SerializeField] private Toggle toggleSection;
    [SerializeField] private Toggle toggleTile;
    [SerializeField] private Toggle toggleOptionPrefab;

    [Space]
    [SerializeField] private Text txtValue;

    [SerializeField] private Button btnPlay;
    [SerializeField] private Button btnClose;

    private Song _song;

    private List<Toggle>                 _listOptionSection    = new List<Toggle>();
    private List<Toggle>                 _listOptionTile       = new List<Toggle>();
    private List<Toggle>                 _listOptionMoodChange = new List<Toggle>();
    
    private List<IngameSectionType>      _allSpecialSection;
    private List<SpecialTileIndex>       _allSpecialTile;
    private List<SpecialMoodChangeIndex> _allSpecialMoodChange;

    #region Unity Medhods

    private void Start() {
        btnPlay.onClick.AddListener(OnBtnPlayClicked);
        btnClose.onClick.AddListener(OnBtnCloseClicked);
        UpdateUI();
        toggleSection.onValueChanged.AddListener(OnChangeStateToggleSection);
        toggleTile.onValueChanged.AddListener(OnChangeStateToggleTile);
        OnChangeStateToggleSection(toggleSection.isOn);
        OnChangeStateToggleTile(toggleTile.isOn);
    }

    #endregion

    public void Show(Song song) {
        this._song = song;
    }

    private void UpdateUI() {
        _allSpecialSection = Enum.GetValues(typeof(IngameSectionType)).Cast<IngameSectionType>().ToList();
        
        int indexChildSection = toggleSection.transform.GetSiblingIndex();
        for (int i = 0; i < _allSpecialSection.Count; i++) {
            var option = Instantiate(toggleOptionPrefab, toggleOptionPrefab.transform.parent);
            option.transform.SetSiblingIndex(indexChildSection + i + 1);
            option.gameObject.SetActive(true);
            option.GetComponentInChildren<Text>().text = $"{(int) _allSpecialSection[i]} {_allSpecialSection[i].ToString()}";
            option.isOn = RemoteConfig.instance.NewElements_Section_Index.Contains( (int) _allSpecialSection[i] );
            _listOptionSection.Add(option);
        }
        
        _allSpecialTile = Enum.GetValues(typeof(SpecialTileIndex)).Cast<SpecialTileIndex>().ToList();
        int indexChildTile = toggleTile.transform.GetSiblingIndex();
        for (int i = 0; i < _allSpecialTile.Count; i++) {
            var option = Instantiate(toggleOptionPrefab, toggleOptionPrefab.transform.parent);
            option.transform.SetSiblingIndex(indexChildTile + i + 1);
            option.gameObject.SetActive(true);
            option.GetComponentInChildren<Text>().text =$"{(int) _allSpecialTile[i]} {_allSpecialTile[i].ToString()}";
            option.isOn = RemoteConfig.instance.NewElements_Tile_index.Contains((int) _allSpecialTile[i]);
            _listOptionTile.Add(option);
        }
        _allSpecialMoodChange = Enum.GetValues(typeof(SpecialMoodChangeIndex)).Cast<SpecialMoodChangeIndex>().ToList();

        int indexChildMoodChange = indexChildTile + _allSpecialTile.Count;
        for (int i = 0; i < _allSpecialMoodChange.Count; i++) {
            var option = Instantiate(toggleOptionPrefab, toggleOptionPrefab.transform.parent);
            option.transform.SetSiblingIndex(indexChildMoodChange + i + 1);
            option.gameObject.SetActive(true);
            option.GetComponentInChildren<Text>().text =$"{(int) _allSpecialMoodChange[i]} {_allSpecialMoodChange[i].ToString()}";
            option.isOn = RemoteConfig.instance.NewElements_MoodChange_Index.Contains((int) _allSpecialMoodChange[i]);
            _listOptionMoodChange.Add(option);
        }
    }

    private void OnChangeStateToggleSection(bool isOn) {
        foreach (var item in _listOptionSection) {
            item.interactable = isOn;
            if (!isOn) {
                item.isOn = false;
            }
        }
    }
    private void OnChangeStateToggleTile(bool isOn) {
        foreach (var item in _listOptionTile) {
            item.interactable = isOn;
            if (!isOn) {
                item.isOn = false;
            }
        }
        foreach (var item in _listOptionMoodChange) {
            item.interactable = isOn;
            if (!isOn) {
                item.isOn = false;
            }
        }
    }

    private void OnBtnPlayClicked() {
        List<int> optionSections = new List<int>();
        for (int i = 0; i < _listOptionSection.Count; i++) {
            if (_listOptionSection[i].isOn)
                optionSections.Add((int)_allSpecialSection[i]);
        }

        RemoteConfig.instance.NewElements_Section_Index = optionSections.ToArray();

        List<int> optionTile = new List<int>();
        for (int i = 0; i < _listOptionTile.Count; i++) {
            if (_listOptionTile[i].isOn)
                optionTile.Add((int)_allSpecialTile[i]);
        }

        RemoteConfig.instance.NewElements_Tile_index = optionTile.ToArray();

        List<int> optionMoodChange = new List<int>();
        for (int i = 0; i < _listOptionMoodChange.Count; i++) {
            if (_listOptionMoodChange[i].isOn)
                optionMoodChange.Add((int)_allSpecialMoodChange[i]);
        }

        RemoteConfig.instance.NewElements_MoodChange_Index = optionMoodChange.ToArray();

        //go to play
        Util.GoToGamePlay(_song, LOCATION_NAME.devInfo.ToString(), isSongClick: true);
    }

    private void OnBtnCloseClicked() {
        this.Close();
    }
}