using System.Collections;
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.Pool;

public static class IntroducingTileElementsHolder {
    private const string INTRO_DATA_RESOURCE_NAME     = "Special_Element_Intro";
    private const string ELEMENTS_INTRODUCED_PREF_KEY = "elements_introduced";
    
    private static SpecialElementIntroData _data;

    /// <summary>
    /// list các type đã giới thiệu
    /// </summary>
    private static List<NoteElementType> _listIntroducedTypes;
    private static bool               _isLoaded;

    private static void LoadData() {
        _listIntroducedTypes = new List<NoteElementType>();
        _listIntroducedTypes =
            JsonConvert.DeserializeObject<List<NoteElementType>>(PlayerPrefs.GetString(ELEMENTS_INTRODUCED_PREF_KEY));
        _listIntroducedTypes ??= new List<NoteElementType>();
        _data = Resources.Load<SpecialElementIntroData>(INTRO_DATA_RESOURCE_NAME);
    }

    public static void ShowIntroducing(HashSet<NoteElementType> elementTypesToIntroduce) {
        if (elementTypesToIntroduce == null || elementTypesToIntroduce.Count == 0) {
            return;
        }
        
        if (!_isLoaded) {
            LoadData();
            _isLoaded = true;
        }

        if (_data == null) {
            return;
        }
        
        // lọc ra các type chưa được giới thiệu
        var listTypesToIntroduce = ListPool<NoteElementType>.Get();
        foreach (NoteElementType noteElementType in elementTypesToIntroduce) {
            if (!_listIntroducedTypes.Contains(noteElementType)) {
                listTypesToIntroduce.Add(noteElementType);
            }
        }

        if (listTypesToIntroduce.Count == 0) {
            return;
        }

        var listIntro = new List<ElementIntro>();
        foreach (ElementIntro intro in _data.data) {
            if (listTypesToIntroduce.Contains(intro.type)) {
                listIntro.Add(intro);
            }
        }
        
        ListPool<NoteElementType>.Release(listTypesToIntroduce);

        //show popup
        UIController.ui.StartCoroutine(ShowPopupIntroduceElementAsync(listIntro));
    }

    public static void ShowIntroducing(NoteElementType noteElementType) {
        if(noteElementType == NoteElementType.None) {
            return;
        }

		if (!_isLoaded) {
			LoadData();
			_isLoaded = true;
		}

		if (_data == null) {
			return;
		}

		var listIntro = new List<ElementIntro>();
        foreach (ElementIntro intro in _data.data) {
            if (intro.type == noteElementType) {
                listIntro.Add(intro);
            }
        }

		//show popup
        UIController.ui.StartCoroutine(ShowPopupIntroduceElementAsync(listIntro));
	}

    public static void SetIntroduced(NoteElementType introducedType) {
        _listIntroducedTypes.Add(introducedType);
        string json = JsonConvert.SerializeObject(_listIntroducedTypes);
        PlayerPrefs.SetString(ELEMENTS_INTRODUCED_PREF_KEY, json);
    }
    
    private static IEnumerator ShowPopupIntroduceElementAsync(List<ElementIntro> listIntroData) {
        if (listIntroData.IsNullOrEmpty()) {
            yield break;
        }


        yield return Util.ShowPopupAsync<UIIntroduceElement>(PopupName.OnBoardingSpecialTile, callback: popup => {
            popup.Show(listIntroData);
        });
    }
}