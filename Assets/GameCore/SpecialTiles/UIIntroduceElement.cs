using System.Collections;
using System.Collections.Generic;
using TilesHop.GameCore.StarsJourney;
using UnityEngine;
using UnityEngine.UI;

public class UIIntroduceElement : PopupUI {
    #region Properties

    [SerializeField] private Text  txtTitle;
    [SerializeField] private Text  txtSubTitle;
    [SerializeField] private Transform animatedDemoContainer;
    [SerializeField] private GameObject loadingIndicator;
    
	[Space]
    [SerializeField] private Button btnNext;
    [SerializeField] private Text txtNext;

	private Animator _boosterDemo;
    private List<ElementIntro> _listIntro;

    /// <summary>
    /// số note type sẽ được show intro
    /// </summary>
    private int _count;

    /// <summary>
    /// Index của note type đang hiển thị, bắt đầu từ 1
    /// </summary>
    private int _currentRealIndex = 1;

    #endregion

    #region UNITY METHODS

    private void Start() {
        btnNext.onClick.AddListener(HandleBtnNextOnclick);
    }

    #endregion

    #region Methods

    public override bool HandleEventBack() {
        return false;
    }

    /// <summary>
    /// </summary>
    /// <param name="listIntroData">Kiểm tra null trước khi truyền vào</param>
    public void Show(List<ElementIntro> listIntroData) {
        _listIntro = listIntroData;
        _count = _listIntro.Count;

        // SORTING: order càng nhỏ càng được ưu tiên xuất hiện truớc
        _listIntro.Sort(IntroComparator);

        StartCoroutine(LoadAnimatedDemoAsync());
        UpdateBtnNextText();
        UpdateHeader();
    }

    private int IntroComparator(ElementIntro l, ElementIntro r) {
        return l.order.CompareTo(r.order);
    }

    private IEnumerator LoadAnimatedDemoAsync() {
        var assetRequest = Resources.LoadAsync<GameObject>("AnimatedDemo_SpecialTiles");
        
        loadingIndicator.SetActive(true);
        while (!assetRequest.isDone) {
            yield return null;
        }

        loadingIndicator.SetActive(false);
        if (animatedDemoContainer != null && assetRequest.asset != null) {
            var demoObject = Instantiate(assetRequest.asset as GameObject, animatedDemoContainer);

            if (demoObject.TryGetComponent(out _boosterDemo)) {
                UpdateContent();
            }
        }
    }
    
    private void HandleBtnNextOnclick() {
        if (_currentRealIndex == _count) {
            Close();
            return;
        }

        _currentRealIndex++;

        UpdateBtnNextText();
        UpdateContent();
        UpdateHeader();
    }

    private void UpdateBtnNextText() {
        if (_currentRealIndex == _count) {
            txtNext.text = LocalizationManager.instance.GetLocalizedValue("GOT_IT");
        } else {
            txtNext.text = LocalizationManager.instance.GetLocalizedValue("NEXT_VN_EN");
        }
    }

    private void UpdateContent() {
        if (_boosterDemo == null) {
            return;
        }
        
        ElementIntro item = _listIntro[_currentRealIndex - 1];
        _boosterDemo.Play(item.type.ToString());
        IntroducingTileElementsHolder.SetIntroduced(item.type);
        NewElementsTracking.Track_OnboardingElementsImpression(item.type, NotesManager.instance.song.acm_id_v3);
        StarsJourneyTracking.Track_TutorialElement(item.type);
    }

    private void UpdateHeader() {
        ElementIntro item = _listIntro[_currentRealIndex - 1];
        txtTitle.text = LocalizationManager.instance.GetLocalizedValue(item.title).ToUpper();
        txtSubTitle.text = LocalizationManager.instance.GetLocalizedValue("HOW_IT_WORKS");
    }

    #endregion
}