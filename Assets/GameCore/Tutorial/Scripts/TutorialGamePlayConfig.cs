using System;
using UnityEngine;

[Serializable]
public class TutorialGamePlayConfig {
    public                 float Phase1Duration;
    public                 bool  FollowMidi;
    [Range(0, 100)] public int   ChanceToNotStraight;

    public int[] OnlyOnLane;

    public TutorialGamePlayConfig() {
        Phase1Duration = 10;
        FollowMidi = true;
        ChanceToNotStraight = 50;
        OnlyOnLane = new int[] { 2, 3, 4 };
    }
}