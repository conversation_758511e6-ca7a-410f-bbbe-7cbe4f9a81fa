using System;
using System.Collections;
using System.Text;
using GameCore.BHBalancy;
using TilesHop.Cores.Boosters;
using UnityEngine;
using UnityEngine.Networking;

namespace TilesHop.LiveEvent.MilestoneEvent {
    public partial class MilestoneEvent {
        #region Fields

        private const string DATA_PATH = "MilestoneEvent";

        public event Action<bool> OnLoadExtraConfigDone;
        public Action onNeedRefreshProgress;
        public event Action OnDownloadTitleSpriteDone;
        public event Action OnDownloadTokenSpriteDone;
        public event Action OnDownloadKeyVisualSpriteDone;

        public Sprite tokenSprite;
        public Sprite titleSprite;
        public Sprite keyVisualSprite;

        private MilestoneEventData _data;

        private int _ingameTemporaryToken;

        public bool isSetupRewardsDone { get; private set; }

        #endregion

        #region Properties

        public MilestoneEventData data {
            get {
                if (_data == null) {
                    LoadEventData();
                }
                return _data ??= new MilestoneEventData();
            }
        }

        public SubEventConfig currentSubEventConfig {
            get {
                var events = config.subEvents.events;
                if (events == null || events.Length == 0) {
                    return null;
                }
                return events[data.eventIndex % events.Length];
            }
        }

        #endregion

        #region SAVE & LOAD

        public void SaveEventData() {
            if (_data != null) {
                SaveData.Save(DATA_PATH, _data);
            }
        }

        public void LoadEventData() {
            SaveData.TryLoad(DATA_PATH, ref _data);
        }

        #endregion

        #region Init Data

        public void InitEventData() {
            if (_data == null) {
                LoadEventData();
            }

            _data ??= new MilestoneEventData();
        }

        public IEnumerator RequestSubEventsConfig() {
            UnityWebRequest request = UnityWebRequest.Get(_config.extraConfigUrl);
            request.timeout = 5;
            yield return request.SendWebRequest();

            if (request.result is UnityWebRequest.Result.Success) {
                try {
                    _config.subEvents = JsonUtility.FromJson<SubEventConfigWrapper>(request.downloadHandler.text);
                    _config.isValid = config.subEvents.events.Length > 0;
                } catch (Exception e) {
                    _config.isValid = false;
                    Debug.Log(e.Message);
                } finally {
                    if (_config.isValid) {
                        var eventConfig = currentSubEventConfig;

                        FetchTokenSprite(eventConfig);
                        FetchTitleSprite(eventConfig);
                        FetchKeyVisualSprite(eventConfig);

                        if (eventConfig.milestones != null && eventConfig.milestones.Length > 0) {
                            eventConfig.milestones[^1].isLast = true;
                        }
                    }

                    OnLoadExtraConfigDone?.Invoke(_config.isValid);
                }
            } else {
                _config.isValid = false;
                OnLoadExtraConfigDone?.Invoke(false);
            }

            isDoneProcessConfig = true;
            SetupEventRewards();
        }

        #endregion

        #region Fetch Sprites

        private async void FetchTokenSprite(SubEventConfig eventConfig) {
            try {
                tokenSprite = await MilestoneEventHelper.DownloadSprite(eventConfig.tokenUrl);
                OnDownloadTokenSpriteDone?.Invoke();
            } catch (Exception e) {
                CustomException.Fire("[FetchTokenSprite]", e.Message);
            }
        }

        private async void FetchTitleSprite(SubEventConfig eventConfig) {
            try {
                titleSprite = await MilestoneEventHelper.DownloadSprite(eventConfig.titleUrl);
                OnDownloadTitleSpriteDone?.Invoke();
            } catch (Exception e) {
                CustomException.Fire("[FetchTitleSprite]", e.Message);
            }
        }

        private async void FetchKeyVisualSprite(SubEventConfig eventConfig) {
            try {
                keyVisualSprite = await MilestoneEventHelper.DownloadSprite(eventConfig.keyVisualUrl);
                OnDownloadKeyVisualSpriteDone?.Invoke();
            } catch (Exception e) {
                CustomException.Fire("[FetchKeyVisualSprite]", e.Message);
            }
        }

        #endregion

        #region Data processing

        /// <summary>
        /// After instruction, the user actually joins the event
        /// </summary>
        public void SetInstructed() {
            if (_data.isIntructed) {
                return;
            }

            _data.isIntructed = true;
            SetStartTime();
            SaveEventData();

            MilestoneEventTracker.Track_EventStart(_data.eventIndex + 1, GameController.gameplayCount);
			BalancyManager.canActiveOffer = true;
            CustomUserProperties.LE3_Milestone_Unlocked = true;
		}

        public bool IsLastMilestone() {
            return currentSubEventConfig.milestones[data.milestoneId].isLast;
        }

        
        /// <summary>
        /// sau khi thanh progress trong popup chạy tới mốc, xử lý nhận thưởng và reset reward
        /// </summary>
        public void RenewMilestoneRewards() {
            data.milestoneId++;
            data.milestoneReward.isValid = false;
            data.currentToken = 0;
            SetupEventRewards();
        }

        
        /// <summary>
        /// Reset data to start another sub event
        /// </summary>
        public void ResetForNewSubEvent() {
            MilestoneEventTracker.Track_EventEnd(GetEventID(), GetElapsedTimeFromActive());

            // TODO: check if different sub-event type => need to show instruction
            SetCurrentAndEarnedToken(0, 0);
            _data.eventIndex++;
            _data.milestoneId = 0;
            _data.isIntructed = true;
            _data.milestoneReward.isValid = false;
            _data.grandPrizeReward.isValid = false;

            // re-init pools
            _ballsRewardPool = null;
            _themesRewardPool = null;

            SetStartTime();
            SaveEventData();
            SetupEventRewards();

            MilestoneEventTracker.Track_EventStart(_data.eventIndex + 1, GameController.gameplayCount);

            BalancyManager.canActiveOffer = true;
        }

        
        /// <summary>
        /// Get progress for displaying progress bar
        /// </summary>
        /// <returns></returns>
        public int GetCurrentMilestone() {
            var currentSubEvent = currentSubEventConfig;
            return currentSubEvent.milestones[data.milestoneId].milestone;
        }

        public void SetCurrentAndEarnedToken(int current, int earned) {
            data.currentToken = current;
            data.earnedToken = earned;
            SaveEventData();
        }

        public void TransferEarnedTokenToWallet(int delta) {
            int transferAmount = Mathf.Min(delta, data.earnedToken);
            SetCurrentAndEarnedToken(data.currentToken + transferAmount, data.earnedToken - transferAmount);
        }

        public void GiveMilestoneReward() {
            GiveRewards(data.milestoneReward, false);
        }

        public void GiveGrandPrizeReward() {
            GiveRewards(data.grandPrizeReward, true);
        }

        // trả thưởng
        private void GiveRewards(GroupRewardData rewards, bool isGrandPrize) {
            StringBuilder rewardsStr = new StringBuilder();
            if (rewards.gems > 0) {
                _resourceService.GainGems(rewards.gems);
                rewardsStr.Append($"gem:{rewards.gems}");
            }

            if (rewards.rewardBalls?.Count > 0) {
                foreach (int ballId in rewards.rewardBalls) {
                    _resourceService.UnlockSkin(ballId);
                    MilestoneEventTracker.Track_BallReceive(ballId.ToString());
                }

                if (rewardsStr.Length > 0) {
                    rewardsStr.Append(';');
                }
                rewardsStr.Append($"ball:{rewards.rewardBalls.Count}");
            }

            if (rewards.rewardThemes?.Count > 0) {
                foreach (int themeId in rewards.rewardThemes) {
                    _resourceService.UnlockTheme(themeId);
                    MilestoneEventTracker.Track_ThemeReceive(themeId.ToString());
                }
                if (rewardsStr.Length > 0) {
                    rewardsStr.Append(';');
                }
                rewardsStr.Append($"theme:{rewards.rewardThemes.Count}");
            }

            if (rewards.rewardBoosters?.Count > 0) {
                foreach (var rewardBooster in rewards.rewardBoosters) {
                    _resourceService.GainBooster(rewardBooster);
                    if (rewardsStr.Length > 0) {
                        rewardsStr.Append(';');
                    }

                    rewardsStr.Append($"{BoosterManager.BoosterTypeToName(rewardBooster.boosterType)}:{rewardBooster.number}");
                }
            }

            if (rewards.rewardSongKeys?.Count > 0) {
                foreach (string songPath in rewards.rewardSongKeys) {
                    _resourceService.UnlockSong(songPath);
                    MilestoneEventTracker.Track_SongUnlock();
                }

                if (rewardsStr.Length > 0) {
                    rewardsStr.Append(';');
                }
                rewardsStr.Append($"song:{rewards.rewardSongKeys.Count}");
            }

            if (isGrandPrize) {
                MilestoneEventTracker.Track_EventGrandPrizeWin(GetEventID(), GetElapsedTimeFromActive(),
                    rewardsStr.ToString());
            } else {
                MilestoneEventTracker.Track_EventReward(GetEventID(), data.milestoneId + 1, rewardsStr.ToString());
            }
        }

        private void SetStartTime() {
            _data.startTime = DateTime.UtcNow;
        }

        public void RefreshMilestoneRewards() {
            if (CheckToCompensateReceivedRewards(ref data.milestoneReward)) {
                SaveEventData();
                onNeedRefreshProgress?.Invoke();
            }
        }

        public void RefreshGrandPrizeRewards() {
            if (CheckToCompensateReceivedRewards(ref data.grandPrizeReward)) {
                SaveEventData();
            }
        }

        private bool CheckToCompensateReceivedRewards(ref GroupRewardData rewards) {
            var skins = rewards.rewardBalls;
            var themes = rewards.rewardThemes;
            var songs = rewards.rewardSongKeys;
            bool isCompensated = false;

            for (int i = 0; skins != null && i < skins.Count; i++) {
                if (_resourceService.IsOpenSkin(skins[i])) {
                    skins.RemoveAt(i);
                    rewards.gems += config.gemsCompensation;
                    i--;
                    isCompensated = true;
                }
            }

            for (int i = 0; themes != null && i < themes.Count; i++) {
                if (_resourceService.IsOpenTheme(themes[i])) {
                    themes.RemoveAt(i);
                    rewards.gems += config.gemsCompensation;
                    i--;
                    isCompensated = true;
                }
            }

            for (int i = 0; songs != null && i < songs.Count; i++) {
                if (_resourceService.IsOpenSong(songs[i])) {
                    songs.RemoveAt(i);
                    rewards.gems += config.gemsCompensation;
                    i--;
                    isCompensated = true;
                }
            }

            return isCompensated;
        }

        #endregion

        #region Get configs & data

        public MilestoneConfig[] GetMilestoneConfigs() {
            return currentSubEventConfig?.milestones;
        }

        public int GetFirstBallReward() {
            if (!data.milestoneReward.isValid || data.milestoneReward.rewardBalls.IsNullOrEmpty()) {
                return -1;
            }

            return data.milestoneReward.rewardBalls[0];
        }

        public int GetFirstThemeReward() {
            if (!data.milestoneReward.isValid || data.milestoneReward.rewardThemes.IsNullOrEmpty()) {
                return -1;
            }

            return data.milestoneReward.rewardThemes[0];
        }


        public bool CheckReceivedMilestonesRewards() {
            return data.earnedToken > 0;
        }

        public int GetEventID() {
            return data.eventIndex + 1;
        }

        public float GetElapsedTimeFromActive() {
            return (float) (DateTime.Now - data.startTime).TotalSeconds;
        }

        public int GetCurrentToken() {
            return data.currentToken;
        }

        #endregion
    }
}