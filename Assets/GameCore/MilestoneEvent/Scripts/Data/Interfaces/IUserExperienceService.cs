using UnityEngine;

namespace TilesHop.LiveEvent.MilestoneEvent {
    public interface IUserExperienceService {

        #region User progress related

        public bool CheckHomePopupFlowRunning();
        
        public int GetTotalStar();
        
        public int GetSongStartQuantity();

        #endregion
        

        #region UI/UX related

		public bool GetIngameMainCamera(out Camera camera);
        
        public RectTransform GetFullScreenRectTransform();

        #endregion

        #region SFX

        public void PlayTokenItemTriggerSound();
        
        public void PlayMainPopupBgSound();
        
        public void PlayOnboardPopupOpenSound();
        
        public void PlayMainPopupOpenSound();
        
        public void PlayMainPopupCloseSound();
        
        public void PlayTooltipSound();

        public void PlayBtnHomeSound();
        
        public void PlayBtnClickSound();

        #endregion
    }
}