using Sirenix.OdinInspector;
using UnityEngine;

public class DeathlineCollider : MonoBehaviour {
    [SerializeField] private Platform platform;
    [SerializeField] private Collider col;

    [ShowInInspector] private bool _isTrigger = false;

    private void OnEnable() {
        _isTrigger = false;
        col.enabled = true;
        platform.OnChangeSlideState.AddListener(PlatformOnStartSlide);
    }

    private void OnDisable() {
        platform.OnChangeSlideState.RemoveListener(PlatformOnStartSlide);
    }

    private void PlatformOnStartSlide(bool isActive) {
        col.enabled = isActive;
    }

    private void OnCollisionEnter(Collision other) {
        if (_isTrigger) {
            return;
        }
        
        if (Ball.b.isInvincible) {
            return;
        }

        if (!other.gameObject.CompareTag(GAMETAG.Ball)) {
            return;
        }

        _isTrigger = true;
        col.enabled = false;
        Ball.b.FallToDeath();
    }
}