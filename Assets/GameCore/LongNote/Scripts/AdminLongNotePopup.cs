using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Inwave;
using UnityEngine;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;

public class AdminLongNotePopup : PopupUI {
    [SerializeField] private UIItemToggleLongNote[] itemLongNote;
    [SerializeField] private Text                   txtValue;
    [SerializeField] private Button                 btnPlay;
    [SerializeField] private Button                 btnClose;

    private List<LongNoteType> _selectedList;
    private Song               _song;

    private const string KeyAdminLongNote = "Admin_LongNoteSetting";

    #region  Unity Medhods
    private void Start() {
        _selectedList = new List<LongNoteType>();
        Load();
        for (int i = 0; i < itemLongNote.Length; i++) {
            var type = (LongNoteType)i;
            itemLongNote[i].SetData(type, _selectedList.Contains(type));
        }

        UpdateText();

        btnPlay.onClick.AddListener(OnBtnPlayClicked);
        btnClose.onClick.AddListener(OnBtnCloseClicked);
    }

    protected override void OnEnable() {
        UIItemToggleLongNote.OnChangeState += UIItemToggleLongNoteOnOnChangeState;
        base.OnEnable();
    }

    protected override void OnDisable() {
        UIItemToggleLongNote.OnChangeState -= UIItemToggleLongNoteOnOnChangeState;
        base.OnDisable();
    }
    #endregion

    public void Show(Song song) {
        this._song = song;
    }

    private void OnBtnPlayClicked() {
        //save config
        Save();
        //update remote config

        if (RemoteConfigBase.instance.LongNote_v2_IsEnable) {
            var config = Configuration.instance.GetLongNoteConfig();

            int[] array = new int[_selectedList.Count];
            for (int i = 0; i < array.Length; i++) {
                array[i] = (int)_selectedList[i];
            }

            config.LongNoteTypes = array;
            config.Init(force: true);
        } else {
            if (_selectedList.Count == 0) {
                RemoteConfigBase.instance.LongTile_IndexLine = 0; //default
                RemoteConfigBase.instance.LongTile_RangeRandom = null; //default
            } else if (_selectedList.Count == 1) {
                RemoteConfigBase.instance.LongTile_IndexLine = (int)_selectedList[0]; //default
                RemoteConfigBase.instance.LongTile_RangeRandom = null; //default
            } else {
                RemoteConfigBase.instance.LongTile_IndexLine = (int)_selectedList[0]; //default
                int[] array = new int[_selectedList.Count];
                for (int i = 0; i < array.Length; i++) {
                    array[i] = (int)_selectedList[i];
                }

                RemoteConfigBase.instance.LongTile_RangeRandom = array; //default
            }
        }

        //go to play
        Util.GoToGamePlay(_song, LOCATION_NAME.devInfo.ToString(), isSongClick: true);
    }

    private void OnBtnCloseClicked() {
        this.Close();
    }
    private void UIItemToggleLongNoteOnOnChangeState(LongNoteType type, bool isOn) {
        if (isOn) {
            if (!_selectedList.Contains(type)) {
                _selectedList.Add(type);
                UpdateText();
            }
        } else {
            if (_selectedList.Contains(type)) {
                _selectedList.Remove(type);
                UpdateText();
            }
        }
    }

    private void UpdateText() {
        string result = string.Empty;
        foreach (var item in _selectedList) {
            result += item.ToString() + " | ";
        }

        txtValue.text = result;
    }

    private void Load() {
        string loadedData = PlayerPrefs.GetString(KeyAdminLongNote, string.Empty);
        if (!string.IsNullOrEmpty(loadedData)) {
            string[] temp = loadedData.Split(';');
            foreach (var str in temp) {
                if (int.TryParse(str, out int value)) {
                    _selectedList.Add((LongNoteType)value);
                }
            }
        } else {
            var config = Configuration.instance.GetLongNoteConfig();
            if (config != null) {
                foreach (var value in config.LongNoteTypes) {
                    _selectedList.Add((LongNoteType)value);
                }
            } else {
                var defaults = RemoteConfigBase.instance.LongTile_RangeRandom;
                foreach (var value in defaults) {
                    _selectedList.Add((LongNoteType)value);
                }
            }
        }
    }

    private void Save() {
        if (_selectedList.Count == 0) {
            PlayerPrefs.DeleteKey(KeyAdminLongNote);
        } else {
            string dataSave = string.Empty;
            for (int i = 0; i < _selectedList.Count; i++) {
                dataSave += ((int)_selectedList[i]).ToString();
                if (i < _selectedList.Count - 1) {
                    dataSave += ';';
                }
            }

            PlayerPrefs.SetString(KeyAdminLongNote, dataSave);
        }
    }
}