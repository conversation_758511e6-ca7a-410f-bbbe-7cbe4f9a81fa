using System;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

public class ThemeCollections : EditorWindow {
    [MenuItem("InWave/Tools/ThemeCollections")]
    public static void ShowWindow() {
        GetWindow(typeof(ThemeCollections));
    }

    private string _currentTheme = "0";
    private int _indexTheme;

    private void Awake() {
        EditorSceneManager.sceneOpened += SceneOpened;

        minSize = new Vector2(200, 50);
    }

    private void OnDestroy() {
        EditorSceneManager.sceneOpened -= SceneOpened;
    }

    void OnGUI() {

        EditorGUILayout.BeginHorizontal();
        _currentTheme = EditorGUILayout.TextField("Current Theme", _currentTheme);

        if (GUILayout.Button("Go")) {
            GoTheme();
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.But<PERSON>("Back Theme")) {
            BackTheme();
        }

        if (GUILayout.Button("Next Theme")) {
            NextTheme();
        }

        EditorGUILayout.EndHorizontal();
    }

    private void NextTheme() {
        try {
            //Debug.Log("NextTheme");
            
            _indexTheme++;
            _currentTheme = _indexTheme.ToString();
            OpenScene(_currentTheme);

        } catch (Exception e) {
            _indexTheme--;
            _currentTheme = _indexTheme.ToString();
        }
    }

    private void BackTheme() {
        try {
            //Debug.Log("BackTheme");

            _indexTheme--;
            _currentTheme = _indexTheme.ToString();
            OpenScene(_currentTheme);
            
        } catch (Exception e) {
            _indexTheme++;
            _currentTheme = _indexTheme.ToString();
        }
    }

    private void GoTheme() {
        //Debug.Log("GoTheme");

        if (!string.IsNullOrEmpty(_currentTheme)) {
            _indexTheme = int.Parse(_currentTheme);
            OpenScene(_currentTheme);
        }
    }

    private void OpenScene(string index) {
        try {

            if (!Application.isPlaying) {
                string scenePath = $@"Assets\Scenes\Theme{index}.unity";
                var needSave = !(SceneManager.GetActiveScene().path.Equals(scenePath) ||
                                 string.IsNullOrEmpty(SceneManager.GetActiveScene().path));
                if (needSave) {
                    EditorSceneManager.SaveScene(SceneManager.GetActiveScene());
                }

                EditorSceneManager.OpenScene(scenePath);

            } else {
                RemoteConfig.instance.Theme_ForceID = _indexTheme;
                PlayerPrefs.SetInt(CONFIG_STRING.ForceTheme, RemoteConfig.instance.Theme_ForceID);
            }
        } catch (Exception e) {
            Debug.LogError(e.Message);
            EditorUtility.DisplayDialog("OpenScene " + index, e.Message, "Ok");
        }
    }

    private void SceneOpened(Scene scene, OpenSceneMode mode) {
        if (int.TryParse(scene.name.Replace("Theme", ""), out _indexTheme)) {
            _currentTheme = _indexTheme.ToString();
        }
    }
}