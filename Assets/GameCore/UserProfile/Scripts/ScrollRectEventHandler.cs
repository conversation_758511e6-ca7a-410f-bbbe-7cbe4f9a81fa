using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class ScrollRectEventHandler : MonoBehaviour
{
    [SerializeField] private ScrollRectEventListener scrollRectEventListener;
    [SerializeField] private ScrollRect              scrollRect;
    [SerializeField] private SwipeItemScrollScript   swipeItemScrollScript;

    protected virtual void Awake() {
        if (swipeItemScrollScript != null) {
            swipeItemScrollScript.onBeginDrag = OnBeginDrag;
            swipeItemScrollScript.onDrag = OnDrag;
            swipeItemScrollScript.onEndDrag = OnEndDrag;
        }
    }

    void OnBeginDrag(PointerEventData eventData) {
        scrollRectEventListener.OnBeginDrag(eventData);
        scrollRect.OnBeginDrag(eventData);
    }

    void OnDrag(PointerEventData eventData) {
        scrollRectEventListener.OnDrag(eventData);
        scrollRect.OnDrag(eventData);
    }

    void OnEndDrag(PointerEventData eventData) {
        scrollRectEventListener.OnEndDrag(eventData);
        scrollRect.OnEndDrag(eventData);
    }
}
