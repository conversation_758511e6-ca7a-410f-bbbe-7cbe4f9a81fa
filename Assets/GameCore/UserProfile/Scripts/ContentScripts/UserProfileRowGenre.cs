using UnityEngine;

public class UserProfileRowGenre : OptimizedCellView {
    #region Fields

    [SerializeField] private UserDataGenreItem genre01;
    [SerializeField] private UserDataGenreItem genre02;

    #endregion

    public void SetData(RowGenres rowGenres, bool isSelected1, bool isSelected2) {
        SetCell(genre01, rowGenres.genreName01, rowGenres.genreSprite01, isSelected1);
        SetCell(genre02, rowGenres.genreName02, rowGenres.genreSprite02, isSelected2);
    }

    private void SetCell(UserDataGenreItem cell, string genreName, Sprite generSprite, bool isSelected) {
        if (genreName != null) {
            cell.SetActive(true);
            cell.SetTitle(genreName);
            cell.SetImage(generSprite);
            cell.isPreviewing = false;
            cell.SetSelected(isSelected);
        } else {
            cell.SetActive(false);
        }
    }
    public override void SetData(IData _data) {
    }
}