using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UserProfileRowCharacter : OptimizedCellView
{
    #region Fields

    [SerializeField] private UserDataCharacterItem[] characterCells;

    #endregion

    public void SetData(RowCharacters rowCharacters) {
        for (int i = 0; i < characterCells.Length; i++) {
            SetCell(characterCells[i], rowCharacters.listIds[i], rowCharacters.listSprites[i]);
        }
    }

    private void SetCell(UserDataCharacterItem cell, string characterName, Sprite characterSprite) {
        if (characterName != null) {
            cell.SetActive(true);
            cell.SetBallName(characterName);
            cell.SetImage(characterSprite);
        } else {
            cell.SetActive(false);
        }
    }
    public override void SetData(IData _data) {
    }
}
