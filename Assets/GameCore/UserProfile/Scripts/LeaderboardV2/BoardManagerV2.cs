using System;
using System.Collections;
using System.Collections.Generic;
using Com.TheFallenGames.OSA.Core.CustomScroller.Leaderboard;
using UnityEngine;
using UnityEngine.UI;

public class BoardManagerV2 : BoardManager {
    [Header("Board Manager V2 ========")]
    [SerializeField]
    private GameObject holderBtnLogin;
	[SerializeField] RectTransform topHolder;

	[SerializeField] private Button btnLoginFormV2;
    [SerializeField] private Image  iconDaily;
    [SerializeField] private Image  iconWeekly;
    [SerializeField] private Image  iconGlobal;
    [SerializeField] private Image  iconFriend;

    [Header("Tabs")] [SerializeField] private Button     tabCurrentSong;
    [SerializeField]                  private Button     tabAllSongs;
    [SerializeField]                  private GameObject iconSelectedCurrent;
    [SerializeField]                  private GameObject iconSelectedAllSongs;
    [SerializeField]                  private Text       lbCurrent;
    [SerializeField]                  private Text       lbAllSongs;

    [Header("Me Item")] [SerializeField] private RectTransform meItemHolder;
    [SerializeField]                     private RectTransform scrollRect;
    
    [Header("Resources")]
    [SerializeField]                       private Color   txtColorMe;
    [SerializeField]                       private Color   txtColorDefault;
    [SerializeField]                       private Color   txtColorDefaultScore;
    public Sprite  borderMe;
    public Sprite  borderDefault;
    public Vector2 sizeBorderMe      = new Vector2(109, 109);
    public Vector2 sizeBorderDefault = new Vector2(68.5f, 68.5f);

    public Color ColorMe           => txtColorMe;
    public Color ColorDefault      => txtColorDefault;
    public Color ColorDefaultScore => txtColorDefaultScore;

    private LoginFormV2 _loginFormV2;
    private Song        _cachedLastSong;
    private BoardItemV2 _meItem;
    private long _meRank;
    
    protected override void Awake() {
        base.Awake();
        btnLoginFormV2.onClick.AddListener(ShowLoginForm);
        tabCurrentSong.onClick.AddListener(OnClickBtnCurrentSong);
        tabAllSongs.onClick.AddListener(OnClickBtnAllSongs);
        holderBtnLogin.gameObject.SetActive(CoreUser.instance.user == null);
        scrollRect.offsetMin = new Vector2(scrollRect.offsetMin.x, 0);
    }

    protected override void OnEnable() {
        scrollerAdapter.OnItemDisabled += OnScroll_OnItemDisabled;
        base.OnEnable();
    }

    protected override void OnDisable() {
        scrollerAdapter.OnItemDisabled -= OnScroll_OnItemDisabled;
        base.OnDisable();
    }

    protected override void InitScrollAdapter() {
        scrollerAdapter.Init(null, prefItem.transform as RectTransform, prefItemMe.transform as RectTransform);
    }

    protected override void HighlightButton(BoardType boardType) {
        base.HighlightButton(boardType);
        iconDaily.color = colorHighlightOff;
        iconWeekly.color = colorHighlightOff;
        iconGlobal.color = colorHighlightOff;
        iconFriend.color = colorHighlightOff;

        switch (boardType) {
            case BoardType.Daily:
                iconDaily.color = colorHighlightOn;

                break;
            case BoardType.Weekly:
                iconWeekly.color = colorHighlightOn;

                break;
            case BoardType.Global:
                iconGlobal.color = colorHighlightOn;

                break;
            case BoardType.Friend:
                iconFriend.color = colorHighlightOn;

                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(boardType), boardType, null);
        }
    }

    protected override void HandleUserLoggedIn(List<UserEntry> entries, BoardType boardType, long rank) {
        for (int i = 0; i < entries.Count; i++) {
            if (entries[i].auId == CoreUser.instance.user.auId) {
                rank = entries[i].ranks[_boardID];
                break;
                //showMe = false;
            }
        }

        UpdateBottomMeItem(boardType, rank);
    }

    protected override void HandleShowLogin() { }

    protected override void OnScroll_OnItemVisible(LBItemViewsHolder item) {
        if (_datas == null || _datas.Count <= item.ItemIndex) return;
        if (item.cellView is BoardItemV2 view) {
            switch (_datas[item.ItemIndex]) {
                case UserEntry userEntry:
                    view.Setup(userEntry, _boardID, true);
                    break;
                default:
                    Debug.LogError("Why");
                    break;
            }

            if (view.IsMe()) {
                ToggleShowMeItemBottom(false);
            }
        }
    }
    
    private void OnScroll_OnItemDisabled(LBItemViewsHolder item) {
        if (_datas == null || _datas.Count <= item.ItemIndex) return;
        if (item.cellView is BoardItemV2 view) {
            if (view.IsMe()) {
                ToggleShowMeItemBottom(true);
            }
        }
    }

    public override void SetupBoard(Song song = null, SONG_MODE mode = SONG_MODE.normal) {
        titleText.text = song != null ? song.name : _cachedLastSong.name;
        if (scrollerTitle != null) {
            scrollerTitle.Refresh(true);
        }

        this.gameObject.SetActive(true);
        if ((this.song != song && mode != this.songMode) || _globalList == null) {
            songMode = mode;
            this.song = song;
            _boardID = song == null
                ? CONFIG_STRING.DBKey_Total
                : Song.GetSongKey(Util.SongToBoardId(song.path), mode);

            _dailyList = null;
            _weeklyList = null;
            _globalList = null;
            _friendList = null;

            _isLoadingDaily = false;
            _isLoadingWeekly = false;
            _isLoadingGlobal = false;
        }

		ShowLeaderBoard(BoardType.Daily);
		if (scrollerAdapter.IsInitialized && scrollerAdapter.Data != null && scrollerAdapter.Data.Count != 0) {
			scrollerAdapter.ScrollTo(0);
		}

		if (song != null) {
            _cachedLastSong = song;
        }

		LayoutRebuilder.ForceRebuildLayoutImmediate(topHolder);
	}

    private void ShowLoginForm() {
        _loginFormV2 = Util.ShowPopUp(PopupName.LoginFormV2).GetComponent<LoginFormV2>();
        if (_loginFormV2 != null) {
            _loginFormV2.SetData(btnLoginAppleOnClick, btnLoginFacebookOnClick, btnLoginGoogleOnClick);
        }
    }

    private void OnClickBtnAllSongs() {
        if (iconSelectedAllSongs.activeInHierarchy) return;
        ClearOldLeaderboard();
        SwitchBoard(true);
        ShowLeaderBoard(BoardType.Daily);
        iconSelectedCurrent.SetActive(false);
        iconSelectedAllSongs.SetActive(true);
        lbCurrent.color = colorHighlightOff;
        lbAllSongs.color = colorHighlightOn;
    }

    private void OnClickBtnCurrentSong() {
        if (iconSelectedCurrent.activeInHierarchy) return;
        ClearOldLeaderboard();
        SwitchBoard(false);
        ShowLeaderBoard(BoardType.Daily);
        iconSelectedCurrent.SetActive(true);
        iconSelectedAllSongs.SetActive(false);
        lbCurrent.color = colorHighlightOn;
        lbAllSongs.color = colorHighlightOff;
    }

    private void ClearOldLeaderboard() {
        _datas = new List<IData>();
        scrollerAdapter.SetItems(_datas);
    }

    private void UpdateBottomMeItem(BoardType boardType, long rank) {
        ToggleShowMeItemBottom(true);

        _meRank = rank;
        
        _meItem.SetupMe(rank, _myScore[boardType], CoreUser.instance.user.photoUrl);
        _meItem.SetUpMeItemVisual();
        _meItem.UpdateRankInTop(rank > 0 && rank <= 3);
    }

    private void ToggleShowMeItemBottom(bool isShowing) {
        if (_meItem == null) {
            _meItem = Instantiate(prefItemMe, meItemHolder, false).GetComponent<BoardItemV2>();
            _meItem.GetComponent<RectTransform>().sizeDelta = new Vector2(450, 100);
        }

        meItemHolder.gameObject.SetActive(isShowing);
        scrollRect.offsetMin = new Vector2(scrollRect.offsetMin.x, isShowing ? meItemHolder.anchoredPosition.y : 0);
    }

    private void SwitchBoard(bool isAllSongs) {
        song = isAllSongs ? null : _cachedLastSong;
        
        _boardID = isAllSongs
            ? CONFIG_STRING.DBKey_Total
            : Song.GetSongKey(Util.SongToBoardId(song.path), songMode);
        
        _dailyList = null;
        _weeklyList = null;
        _globalList = null;
        _friendList = null;
        
        _isLoadingDaily = false;
        _isLoadingWeekly = false;
        _isLoadingGlobal = false;
    }
    
    protected override void UserLogged() {
       base.UserLogged();
       
       //Disable login form
       if (_loginFormV2 != null) {
           _loginFormV2.Close();
       }

       holderBtnLogin.gameObject.SetActive(false);

		LayoutRebuilder.ForceRebuildLayoutImmediate(topHolder);
	}
}