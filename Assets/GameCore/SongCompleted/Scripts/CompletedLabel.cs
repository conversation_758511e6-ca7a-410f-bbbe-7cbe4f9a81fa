using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class CompletedLabel : MonoBehaviour {
    [SerializeField] private Image mainImage;
    [SerializeField] private Sprite failedSprite;
    [SerializeField] private Sprite completedSprite;

    private void OnEnable() {
        bool isCompleted = CheckComplete();
        ShowLabel(isCompleted);
    }

    private bool CheckComplete() {
        if (UIController.ui.gameover.isCompletedSong) {
            return true;
        }
        
        if (RemoteConfigBase.instance.Hybrid_UniqueStarIngame && GameController.instance.isAlreadyCompletedSong) {
            return true;
        }

        return false;
    }

    public void ShowLabel(bool isCompleted) {
        if (isCompleted) {
            mainImage.sprite = completedSprite;
        } else {
            mainImage.sprite = failedSprite;
        }
    }
    
    // handle animation event
    public void ShakeScreen() {
        if (UIController.ui && UIController.ui.gameover) {
            UIController.ui.gameover.transform.DOShakePosition(0.5f, 5f, 20);
        }
    }
}
