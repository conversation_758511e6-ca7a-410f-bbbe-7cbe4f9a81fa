{"skeleton": {"hash": "l2AsKdWU5yA", "spine": "4.0.64", "x": -559.78, "y": 172.93, "width": 1137.23, "height": 719.52, "images": "./images/", "audio": "D:/WORK/TITTLE HOP/HALLOWEEN/Animation"}, "bones": [{"name": "root"}, {"name": "<PERSON>o cuoi bi ngo", "parent": "root", "x": 2.14, "y": 310.36, "color": "09ff00ff"}, {"name": "pumpkin", "parent": "<PERSON>o cuoi bi ngo", "rotation": 89.25, "x": -9.39, "y": 5.44}, {"name": "cat body copy", "parent": "<PERSON>o cuoi bi ngo", "length": 67.82, "rotation": 88.75, "x": -9.57, "y": 147.88}, {"name": "cat body copy2", "parent": "cat body copy", "length": 61.19, "rotation": 2.97, "x": 67.82}, {"name": "left hand 2", "parent": "cat body copy2", "length": 55.13, "rotation": -136.1, "x": 43.78, "y": -32.48}, {"name": "left hand", "parent": "left hand 2", "length": 57.22, "rotation": -89.09, "x": 55.37, "y": 0.69}, {"name": "right hand 2", "parent": "cat body copy2", "length": 48.1, "rotation": 156.23, "x": 42.54, "y": 29.32}, {"name": "right hand", "parent": "right hand 2", "length": 34.65, "rotation": 67.48, "x": 47.49, "y": 0.55}, {"name": "head", "parent": "cat body copy2", "x": 61.92, "y": 0.13}, {"name": "left eye", "parent": "head", "x": 62.23, "y": -40.06}, {"name": "eye 2", "parent": "head", "x": 66.4, "y": 34.69}, {"name": "eyebrow 1", "parent": "head", "x": 100.62, "y": -40.26}, {"name": "eyebrow 2", "parent": "head", "x": 103.01, "y": 28.47}, {"name": "Layer 129 copy", "parent": "head", "length": 39.48, "rotation": -32.81, "x": 84.17, "y": -47.83}, {"name": "Layer 129 copy2", "parent": "Layer 129 copy", "length": 38.38, "rotation": -0.13, "x": 39.74, "y": -0.43}, {"name": "Layer 129", "parent": "head", "length": 31.47, "rotation": 29.7, "x": 89.76, "y": 38.56}, {"name": "Layer 130", "parent": "Layer 129", "length": 38.21, "rotation": 0.81, "x": 31.47}, {"name": "cat  tail1", "parent": "cat body copy", "length": 46.37, "rotation": -18, "x": 38.94, "y": 2.53}, {"name": "light_floor", "parent": "<PERSON>o cuoi bi ngo", "x": -20.29, "y": 11.89}, {"name": "cat  tail1b", "parent": "cat  tail1", "length": 46.37, "x": 46.37}, {"name": "cat  tail1c", "parent": "cat  tail1b", "length": 46.37, "x": 46.37}, {"name": "cat  tail1d", "parent": "cat  tail1c", "length": 46.37, "x": 46.37}, {"name": "cat  tail1e", "parent": "cat  tail1d", "length": 46.37, "x": 46.37}, {"name": "noise", "parent": "head", "x": 39.17, "y": -2.38}, {"name": "Mouth", "parent": "noise", "x": -10.7, "y": -0.59}, {"name": "Bi ngo", "parent": "root", "x": -2.74, "y": 206.87, "color": "21ff00ff"}, {"name": "Bi ngo1", "parent": "Bi ngo", "x": -354.96, "y": 141.13, "color": "21ff00ff"}, {"name": "pumpkin-3", "parent": "Bi ngo1", "x": -16.2, "y": -0.36}, {"name": "Bi ngo2", "parent": "Bi ngo", "x": -270.2, "y": 158.3, "color": "21ff00ff"}, {"name": "pumpkin-4", "parent": "Bi ngo2", "x": -14.06, "y": -0.13}, {"name": "Bi ngo3", "parent": "Bi ngo", "x": 286.02, "y": 112.44, "color": "21ff00ff"}, {"name": "pumpkin-2", "parent": "Bi ngo3", "x": 12.41, "y": -1.27}, {"name": "Bi ngo4", "parent": "Bi ngo", "x": 399.52, "y": 46.41, "color": "21ff00ff"}, {"name": "pumpkin-1", "parent": "Bi ngo4", "x": -16.69, "y": 0.98}, {"name": "Hinh nom ma", "parent": "root", "x": 247.59, "y": 891.76, "color": "02ff00ff"}, {"name": "gost3", "parent": "Hinh nom ma", "length": 13.69, "rotation": -90.62, "x": -91.44, "y": -27.17}, {"name": "gost3b", "parent": "gost3", "length": 13.69, "x": 13.69}, {"name": "gost3c", "parent": "gost3b", "length": 13.69, "x": 13.69}, {"name": "gost4", "parent": "gost3c", "length": 48.21, "rotation": 0.09, "x": 13.69}, {"name": "gost", "parent": "Hinh nom ma", "length": 20.88, "rotation": -82.43, "x": 8.02, "y": -25.7}, {"name": "gost7", "parent": "gost", "length": 20.88, "x": 20.88}, {"name": "gost8", "parent": "gost7", "length": 20.88, "x": 20.88}, {"name": "gost2", "parent": "gost8", "length": 47.72, "rotation": 5.58, "x": 20.88}, {"name": "gost5", "parent": "Hinh nom ma", "length": 15.35, "rotation": -91.08, "x": 67.08, "y": -0.09}, {"name": "gost5b", "parent": "gost5", "length": 15.35, "x": 15.35}, {"name": "gost5c", "parent": "gost5b", "length": 15.35, "x": 15.35}, {"name": "gost6", "parent": "gost5c", "length": 47.77, "rotation": 1.6, "x": 15.35}], "slots": [{"name": "glow2", "bone": "Bi ngo4", "attachment": "glow2"}, {"name": "glow3", "bone": "Bi ngo3", "attachment": "glow2"}, {"name": "cat  tail", "bone": "cat  tail1", "attachment": "cat  tail"}, {"name": "glow4", "bone": "Bi ngo2", "attachment": "glow2"}, {"name": "glow5", "bone": "Bi ngo1", "attachment": "glow2"}, {"name": "cat body copy", "bone": "cat body copy", "attachment": "cat body copy"}, {"name": "Layer 131", "bone": "pumpkin", "color": "000000ff", "attachment": "Layer 130"}, {"name": "Layer 130", "bone": "pumpkin", "attachment": "Layer 130"}, {"name": "glow", "bone": "light_floor", "attachment": "glow"}, {"name": "pumpkin", "bone": "pumpkin", "attachment": "pumpkin"}, {"name": "right hand 2", "bone": "right hand 2", "attachment": "right hand 2"}, {"name": "Layer 129", "bone": "Layer 129", "attachment": "Layer 129"}, {"name": "Layer 129 copy", "bone": "Layer 129 copy", "attachment": "Layer 129 copy"}, {"name": "right hand", "bone": "right hand", "attachment": "right hand"}, {"name": "left hand 2", "bone": "left hand 2", "attachment": "left hand 2"}, {"name": "left hand", "bone": "left hand", "attachment": "right hand"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eyebrow 2", "bone": "eyebrow 2", "attachment": "eyebrow 2"}, {"name": "eyebrow 1", "bone": "eyebrow 1", "attachment": "eyebrow 1"}, {"name": "left eye", "bone": "left eye", "attachment": "left eye"}, {"name": "eye 2", "bone": "eye 2", "attachment": "right  eye"}, {"name": "mouth", "bone": "Mouth", "attachment": "mouth_nomal"}, {"name": "nose", "bone": "noise", "attachment": "nose"}, {"name": "gost", "bone": "gost", "attachment": "bg/gost"}, {"name": "gost2", "bone": "gost5", "attachment": "bg/gost2"}, {"name": "gost3", "bone": "gost3", "attachment": "bg/gost3"}, {"name": "bg/Pumkin2_light2", "bone": "pumpkin-2", "color": "000000ff", "attachment": "bg/Pumkin2_light"}, {"name": "bg/Pumkin2_light", "bone": "pumpkin-2", "attachment": "bg/Pumkin2_light"}, {"name": "pumpkin-2", "bone": "pumpkin-2", "attachment": "bg/pumpkin-2"}, {"name": "bg/Pumkin1_light2", "bone": "pumpkin-1", "color": "000000ff", "attachment": "bg/Pumkin1_light"}, {"name": "bg/Pumkin1_light", "bone": "pumpkin-1", "attachment": "bg/Pumkin1_light"}, {"name": "pumpkin-1", "bone": "pumpkin-1", "attachment": "bg/pumpkin-1"}, {"name": "bg/Pumkin4_light2", "bone": "pumpkin-4", "color": "000000ff", "attachment": "bg/Pumkin4_light"}, {"name": "bg/Pumkin4_light", "bone": "pumpkin-4", "attachment": "bg/Pumkin4_light"}, {"name": "pumpkin-4", "bone": "pumpkin-4", "attachment": "bg/pumpkin-4"}, {"name": "bg/Pumkin3_light2", "bone": "pumpkin-3", "color": "000000ff", "attachment": "bg/Pumkin3_light"}, {"name": "bg/Pumkin3_light", "bone": "pumpkin-3", "attachment": "bg/Pumkin3_light"}, {"name": "pumpkin-3", "bone": "pumpkin-3", "attachment": "bg/pumpkin-3"}], "skins": [{"name": "default", "attachments": {"bg/Pumkin1_light": {"bg/Pumkin1_light": {"x": -3.2, "y": 49.6, "width": 122, "height": 92}}, "bg/Pumkin1_light2": {"bg/Pumkin1_light": {"x": -3.2, "y": 49.6, "width": 122, "height": 92}}, "bg/Pumkin2_light": {"bg/Pumkin2_light": {"x": -9.25, "y": 41.22, "width": 84, "height": 73}}, "bg/Pumkin2_light2": {"bg/Pumkin2_light": {"x": -9.25, "y": 41.22, "width": 84, "height": 73}}, "bg/Pumkin3_light": {"bg/Pumkin3_light": {"x": 19.15, "y": 41.29, "width": 104, "height": 77}}, "bg/Pumkin3_light2": {"bg/Pumkin3_light": {"x": 19.15, "y": 41.29, "width": 104, "height": 77}}, "bg/Pumkin4_light": {"bg/Pumkin4_light": {"x": 13.15, "y": 29.38, "width": 71, "height": 64}}, "bg/Pumkin4_light2": {"bg/Pumkin4_light": {"x": 13.15, "y": 29.38, "width": 71, "height": 64}}, "cat body copy": {"cat body copy": {"x": 56.47, "y": 2.55, "rotation": -88.75, "width": 137, "height": 149}}, "cat  tail": {"cat  tail": {"type": "mesh", "uvs": [0.61553, 0.00208, 0.96281, 0.01967, 0.96279, 0.05312, 0.96276, 0.09279, 0.96274, 0.13532, 0.96271, 0.17207, 0.96269, 0.20864, 0.96267, 0.24775, 0.96264, 0.28973, 0.96262, 0.33603, 0.9626, 0.37859, 0.96258, 0.41711, 0.96255, 0.45746, 0.96253, 0.49277, 0.96251, 0.53189, 0.96248, 0.57385, 0.96245, 0.62186, 0.96243, 0.66394, 0.9624, 0.70629, 0.96238, 0.74141, 0.96237, 0.77849, 0.96235, 0.80987, 0.96233, 0.84959, 0.9623, 0.89095, 0.96227, 0.934, 0.96225, 0.9686, 0.71916, 0.99584, 0.28122, 0.99623, 0.03801, 0.97218, 0.038, 0.934, 0.038, 0.89095, 0.03799, 0.85291, 0.03798, 0.80808, 0.03798, 0.77849, 0.03797, 0.74321, 0.03797, 0.70629, 0.03796, 0.66574, 0.03796, 0.62298, 0.03795, 0.57565, 0.03794, 0.53411, 0.03794, 0.49457, 0.03793, 0.45524, 0.03792, 0.41711, 0.03792, 0.37637, 0.03791, 0.33783, 0.03791, 0.29417, 0.0379, 0.24774, 0.03789, 0.20864, 0.03789, 0.17027, 0.03788, 0.13532, 0.03787, 0.09279, 0.03787, 0.05312, 0.03787, 0.02143, 0.37229, 0.00208], "triangles": [46, 47, 6, 6, 47, 5, 47, 48, 5, 5, 48, 4, 48, 49, 4, 3, 4, 50, 4, 49, 50, 2, 3, 51, 3, 50, 51, 2, 51, 0, 51, 53, 0, 2, 0, 1, 51, 52, 53, 42, 43, 10, 10, 43, 9, 43, 44, 9, 44, 45, 9, 9, 45, 8, 45, 46, 8, 8, 46, 7, 7, 46, 6, 16, 38, 15, 38, 39, 15, 15, 39, 14, 39, 40, 14, 14, 40, 13, 13, 40, 12, 12, 40, 41, 12, 41, 11, 41, 42, 11, 11, 42, 10, 19, 20, 34, 20, 33, 34, 34, 35, 19, 19, 35, 18, 17, 18, 36, 18, 35, 36, 36, 37, 17, 17, 37, 16, 37, 38, 16, 26, 27, 25, 25, 27, 28, 28, 29, 25, 25, 29, 24, 23, 24, 30, 24, 29, 30, 22, 23, 31, 23, 30, 31, 31, 32, 22, 22, 32, 21, 21, 32, 20, 32, 33, 20], "vertices": [3, 21, 142.78, -2.96, 0.00049, 22, 96.42, -2.96, 0.05209, 23, 50.05, -2.96, 0.94742, 3, 21, 138.62, -12.02, 0.00109, 22, 92.25, -12.02, 0.07, 23, 45.89, -12.02, 0.92892, 3, 21, 130.59, -12.07, 0.00272, 22, 84.22, -12.07, 0.10615, 23, 37.86, -12.07, 0.89113, 4, 20, 167.43, -12.13, 2e-05, 21, 121.07, -12.13, 0.00704, 22, 74.71, -12.13, 0.16641, 23, 28.34, -12.13, 0.82653, 4, 20, 157.23, -12.19, 9e-05, 21, 110.86, -12.19, 0.01661, 22, 64.5, -12.19, 0.2473, 23, 18.14, -12.19, 0.736, 4, 20, 148.4, -12.25, 0.00033, 21, 102.04, -12.25, 0.03538, 22, 55.67, -12.25, 0.33999, 23, 9.31, -12.25, 0.6243, 4, 20, 139.63, -12.31, 0.00109, 21, 93.26, -12.31, 0.06827, 22, 46.9, -12.31, 0.43017, 23, 0.54, -12.31, 0.50048, 5, 18, 176.61, -12.36, 1e-05, 20, 130.24, -12.36, 0.00317, 21, 83.88, -12.36, 0.11977, 22, 37.51, -12.36, 0.50064, 23, -8.85, -12.36, 0.37641, 5, 18, 166.53, -12.43, 3e-05, 20, 120.17, -12.43, 0.00824, 21, 73.81, -12.43, 0.19173, 22, 27.44, -12.43, 0.53618, 23, -18.92, -12.43, 0.26381, 5, 18, 155.42, -12.5, 0.00011, 20, 109.06, -12.5, 0.01921, 21, 62.69, -12.5, 0.28085, 22, 16.33, -12.5, 0.52856, 23, -30.03, -12.5, 0.17126, 5, 18, 145.21, -12.56, 0.00041, 20, 98.84, -12.56, 0.04035, 21, 52.47, -12.56, 0.37731, 22, 6.11, -12.56, 0.47953, 23, -40.25, -12.56, 0.1024, 5, 18, 135.96, -12.62, 0.00132, 20, 89.6, -12.62, 0.07674, 21, 43.24, -12.62, 0.46573, 22, -3.13, -12.62, 0.40012, 23, -49.49, -12.62, 0.0561, 5, 18, 126.28, -12.69, 0.00372, 20, 79.91, -12.69, 0.13272, 21, 33.55, -12.69, 0.52884, 22, -12.81, -12.69, 0.30671, 23, -59.18, -12.69, 0.028, 5, 18, 117.8, -12.74, 0.00942, 20, 71.43, -12.74, 0.20944, 21, 25.08, -12.74, 0.55282, 22, -21.29, -12.74, 0.21565, 23, -67.65, -12.74, 0.01267, 5, 18, 108.42, -12.8, 0.02145, 20, 62.05, -12.8, 0.30253, 21, 15.69, -12.8, 0.53209, 22, -30.68, -12.8, 0.13877, 23, -77.04, -12.8, 0.00517, 5, 18, 98.34, -12.86, 0.04426, 20, 51.98, -12.86, 0.40097, 21, 5.61, -12.86, 0.47137, 22, -40.75, -12.86, 0.08152, 23, -87.11, -12.86, 0.00189, 5, 18, 86.82, -12.94, 0.08317, 20, 40.46, -12.94, 0.48865, 21, -5.91, -12.94, 0.384, 22, -52.27, -12.94, 0.04357, 23, -98.64, -12.94, 0.00061, 5, 18, 76.72, -13, 0.14307, 20, 30.36, -13, 0.54839, 21, -16, -13, 0.28726, 22, -62.37, -13, 0.02111, 23, -108.74, -13, 0.00018, 5, 18, 66.56, -13.06, 0.22648, 20, 20.19, -13.06, 0.56731, 21, -26.17, -13.06, 0.19694, 22, -72.54, -13.06, 0.00923, 23, -118.89, -13.06, 4e-05, 5, 18, 58.13, -13.12, 0.33167, 20, 11.77, -13.12, 0.54125, 21, -34.6, -13.12, 0.12345, 22, -80.96, -13.12, 0.00362, 23, -127.32, -13.12, 1e-05, 4, 18, 49.23, -13.18, 0.45197, 20, 2.87, -13.18, 0.47622, 21, -43.5, -13.18, 0.07054, 22, -89.86, -13.18, 0.00127, 4, 18, 41.7, -13.23, 0.57673, 20, -4.66, -13.23, 0.38626, 21, -51.03, -13.23, 0.03661, 22, -97.39, -13.23, 0.00039, 4, 18, 32.17, -13.28, 0.69396, 20, -14.19, -13.28, 0.28875, 21, -60.56, -13.28, 0.01719, 22, -106.93, -13.28, 0.00011, 4, 18, 22.24, -13.35, 0.79329, 20, -24.12, -13.35, 0.19942, 21, -70.49, -13.35, 0.00726, 22, -116.85, -13.35, 3e-05, 4, 18, 11.9, -13.41, 0.8681, 20, -34.46, -13.41, 0.12914, 21, -80.82, -13.41, 0.00275, 22, -127.18, -13.41, 1e-05, 3, 18, 3.61, -13.47, 0.91569, 20, -42.76, -13.47, 0.08334, 21, -89.12, -13.47, 0.00096, 3, 18, -2.97, -7.19, 0.93591, 20, -49.33, -7.19, 0.06367, 21, -95.7, -7.19, 0.00042, 3, 18, -3.14, 4.2, 0.93591, 20, -49.51, 4.2, 0.06367, 21, -95.87, 4.2, 0.00042, 3, 18, 2.59, 10.56, 0.91569, 20, -43.78, 10.56, 0.08334, 21, -90.14, 10.56, 0.00096, 4, 18, 11.76, 10.61, 0.8681, 20, -34.61, 10.61, 0.12914, 21, -80.97, 10.61, 0.00275, 22, -127.34, 10.61, 1e-05, 4, 18, 22.08, 10.69, 0.79329, 20, -24.28, 10.69, 0.19942, 21, -70.64, 10.69, 0.00726, 22, -117, 10.69, 3e-05, 4, 18, 31.22, 10.74, 0.69396, 20, -15.15, 10.74, 0.28875, 21, -61.51, 10.74, 0.01719, 22, -107.87, 10.74, 0.00011, 4, 18, 41.97, 10.81, 0.57673, 20, -4.39, 10.81, 0.38626, 21, -50.75, 10.81, 0.03661, 22, -97.11, 10.81, 0.00039, 4, 18, 49.08, 10.85, 0.45197, 20, 2.71, 10.85, 0.47622, 21, -43.65, 10.85, 0.07054, 22, -90.01, 10.85, 0.00127, 5, 18, 57.54, 10.91, 0.33167, 20, 11.18, 10.91, 0.54125, 21, -35.19, 10.91, 0.12345, 22, -81.55, 10.91, 0.00362, 23, -127.92, 10.91, 1e-05, 5, 18, 66.4, 10.97, 0.22648, 20, 20.04, 10.97, 0.56731, 21, -26.33, 10.97, 0.19694, 22, -72.68, 10.97, 0.00923, 23, -119.05, 10.97, 4e-05, 5, 18, 76.14, 11.03, 0.14307, 20, 29.77, 11.03, 0.54839, 21, -16.59, 11.03, 0.28726, 22, -62.96, 11.03, 0.02111, 23, -109.32, 11.03, 0.00018, 5, 18, 86.4, 11.1, 0.08317, 20, 40.03, 11.1, 0.48865, 21, -6.33, 11.1, 0.384, 22, -52.69, 11.1, 0.04357, 23, -99.06, 11.1, 0.00061, 5, 18, 97.75, 11.17, 0.04426, 20, 51.39, 11.17, 0.40097, 21, 5.03, 11.17, 0.47137, 22, -41.33, 11.17, 0.08152, 23, -87.7, 11.17, 0.00189, 5, 18, 107.72, 11.23, 0.02145, 20, 61.37, 11.23, 0.30253, 21, 15, 11.23, 0.53209, 22, -31.36, 11.23, 0.13877, 23, -77.73, 11.23, 0.00517, 5, 18, 117.21, 11.3, 0.00942, 20, 70.85, 11.3, 0.20944, 21, 24.49, 11.3, 0.55282, 22, -21.87, 11.3, 0.21565, 23, -68.24, 11.3, 0.01267, 5, 18, 126.65, 11.35, 0.00372, 20, 80.29, 11.35, 0.13272, 21, 33.92, 11.35, 0.52884, 22, -12.44, 11.35, 0.30671, 23, -58.8, 11.35, 0.028, 5, 18, 135.81, 11.41, 0.00132, 20, 89.44, 11.41, 0.07674, 21, 43.08, 11.41, 0.46573, 22, -3.29, 11.41, 0.40012, 23, -49.65, 11.41, 0.0561, 5, 18, 145.59, 11.48, 0.00041, 20, 99.22, 11.48, 0.04035, 21, 52.85, 11.48, 0.37731, 22, 6.49, 11.48, 0.47953, 23, -39.87, 11.48, 0.1024, 5, 18, 154.84, 11.53, 0.00011, 20, 108.47, 11.53, 0.01921, 21, 62.1, 11.53, 0.28085, 22, 15.74, 11.53, 0.52856, 23, -30.62, 11.53, 0.17126, 5, 18, 165.31, 11.6, 3e-05, 20, 118.94, 11.6, 0.00824, 21, 72.58, 11.6, 0.19173, 22, 26.22, 11.6, 0.53618, 23, -20.15, 11.6, 0.26381, 5, 18, 176.46, 11.68, 1e-05, 20, 130.09, 11.68, 0.00317, 21, 83.72, 11.68, 0.11977, 22, 37.36, 11.68, 0.50064, 23, -9, 11.68, 0.37641, 4, 20, 139.48, 11.73, 0.00109, 21, 93.11, 11.73, 0.06827, 22, 46.74, 11.73, 0.43017, 23, 0.38, 11.73, 0.50048, 4, 20, 148.68, 11.8, 0.00033, 21, 102.32, 11.8, 0.03538, 22, 55.96, 11.8, 0.33999, 23, 9.59, 11.8, 0.6243, 4, 20, 157.07, 11.85, 9e-05, 21, 110.71, 11.85, 0.01661, 22, 64.35, 11.85, 0.2473, 23, 17.98, 11.85, 0.736, 4, 20, 167.27, 11.91, 2e-05, 21, 120.92, 11.91, 0.00704, 22, 74.55, 11.91, 0.16641, 23, 28.18, 11.91, 0.82653, 3, 21, 130.43, 11.98, 0.00272, 22, 84.07, 11.98, 0.10615, 23, 37.71, 11.98, 0.89113, 3, 21, 138.04, 12.02, 0.00109, 22, 91.68, 12.02, 0.07, 23, 45.31, 12.02, 0.92892, 3, 21, 142.74, 3.36, 0.00049, 22, 96.38, 3.36, 0.05209, 23, 50.01, 3.36, 0.94742], "hull": 54, "edges": [0, 106, 0, 2, 50, 52, 52, 54, 54, 56, 104, 106, 72, 34, 88, 18, 96, 10, 100, 6, 92, 14, 80, 26, 84, 22, 76, 30, 64, 42, 68, 38, 60, 46, 100, 102, 102, 104, 2, 4, 4, 6, 102, 4, 96, 98, 98, 100, 6, 8, 8, 10, 98, 8, 92, 94, 94, 96, 10, 12, 12, 14, 94, 12, 88, 90, 90, 92, 14, 16, 16, 18, 90, 16, 84, 86, 86, 88, 18, 20, 20, 22, 86, 20, 80, 82, 82, 84, 22, 24, 24, 26, 82, 24, 76, 78, 78, 80, 26, 28, 28, 30, 78, 28, 72, 74, 74, 76, 30, 32, 32, 34, 74, 32, 68, 70, 70, 72, 34, 36, 36, 38, 70, 36, 64, 66, 66, 68, 38, 40, 40, 42, 66, 40, 60, 62, 62, 64, 42, 44, 44, 46, 62, 44, 56, 58, 58, 60, 46, 48, 48, 50, 58, 48, 56, 50], "width": 13, "height": 120}}, "eye 2": {"eye-3": {"x": -2.04, "y": -1.07, "rotation": -91.73, "width": 50, "height": 45}, "eye 2": {"x": -2.04, "y": -1.07, "rotation": -91.73, "width": 51, "height": 46}, "right  eye": {"x": -2.04, "y": -1.07, "rotation": -91.73, "width": 49, "height": 44}}, "eyebrow 1": {"eyebrow 1": {"x": -2.58, "y": -3.67, "rotation": -91.73, "width": 38, "height": 14}}, "eyebrow 2": {"eyebrow 2": {"x": -2.74, "y": 1.58, "rotation": -91.73, "width": 38, "height": 14}}, "glow": {"glow": {"x": 4.66, "y": -3.73, "width": 449, "height": 112}}, "glow2": {"glow2": {"x": -17.32, "y": 4.66, "width": 396, "height": 170}}, "glow3": {"glow2": {"x": 21.69, "y": -0.81, "scaleX": 0.8882, "width": 396, "height": 170}}, "glow4": {"glow2": {"x": -11.13, "y": -0.14, "scaleX": 0.6724, "width": 396, "height": 170}}, "glow5": {"glow2": {"x": -4.07, "y": -0.37, "width": 396, "height": 170}}, "gost": {"bg/gost": {"type": "mesh", "uvs": [0.11196, 0, 0.13786, 0, 0.13785, 0.02331, 0.16263, 0.08888, 0.18535, 0.14899, 0.20408, 0.19856, 0.22046, 0.2419, 0.23432, 0.27857, 0.26057, 0.34804, 0.28667, 0.41709, 0.32782, 0.4887, 0.36487, 0.55319, 0.69751, 0.59866, 0.78208, 0.68341, 0.8805, 0.78204, 0.9875, 0.88927, 0.9875, 0.88972, 0.9875, 0.94653, 0.7796, 0.94655, 0.69994, 0.98144, 0.46319, 1, 0.38552, 1, 0.12308, 0.9769, 0.05864, 0.8503, 0.0125, 0.75966, 0.0125, 0.65229, 0.26264, 0.56058, 0.23435, 0.4934, 0.2045, 0.42251, 0.17584, 0.35446, 0.14552, 0.28243, 0.1295, 0.24441, 0.11212, 0.20311, 0.09872, 0.15146, 0.08405, 0.0949, 0.08744, 0.00291], "triangles": [25, 26, 11, 25, 11, 12, 13, 24, 25, 13, 25, 12, 23, 24, 13, 23, 13, 14, 23, 14, 15, 16, 23, 15, 16, 18, 23, 17, 18, 16, 23, 21, 22, 21, 23, 20, 20, 23, 18, 19, 20, 18, 28, 29, 9, 27, 28, 9, 27, 9, 10, 26, 27, 10, 26, 10, 11, 31, 32, 5, 6, 31, 5, 30, 31, 6, 30, 6, 7, 8, 29, 30, 8, 30, 7, 29, 8, 9, 2, 0, 1, 35, 0, 2, 34, 35, 2, 34, 2, 3, 34, 3, 4, 33, 34, 4, 32, 33, 4, 32, 4, 5], "vertices": [2, 40, -1.09, 0.41, 0.98597, 41, -21.97, 0.41, 0.01403, 2, 40, -0.95, 1.44, 0.98573, 41, -21.83, 1.44, 0.01427, 2, 40, 1.63, 1.1, 0.9489, 41, -19.25, 1.1, 0.0511, 2, 40, 9.05, 1.12, 0.8594, 41, -11.84, 1.12, 0.1406, 3, 40, 15.84, 1.13, 0.68075, 41, -5.05, 1.13, 0.31827, 42, -25.92, 1.13, 0.00098, 3, 40, 21.44, 1.14, 0.44708, 41, 0.56, 1.14, 0.54216, 42, -20.32, 1.14, 0.01075, 4, 40, 26.34, 1.15, 0.22983, 41, 5.46, 1.15, 0.71545, 42, -15.43, 1.15, 0.05433, 43, -36.02, 4.68, 0.00039, 4, 40, 30.48, 1.15, 0.08808, 41, 9.6, 1.15, 0.73998, 42, -11.28, 1.15, 0.16705, 43, -31.9, 4.28, 0.00489, 4, 40, 38.33, 1.17, 0.02302, 41, 17.45, 1.17, 0.59995, 42, -3.43, 1.17, 0.34869, 43, -24.08, 3.54, 0.02834, 4, 40, 46.13, 1.19, 0.00376, 41, 25.25, 1.19, 0.37313, 42, 4.38, 1.19, 0.52149, 43, -16.31, 2.79, 0.10162, 4, 40, 54.31, 1.76, 0.00023, 41, 33.42, 1.76, 0.17161, 42, 12.55, 1.76, 0.57416, 43, -8.12, 2.57, 0.254, 3, 41, 40.78, 2.29, 0.05468, 42, 19.9, 2.29, 0.46981, 43, -0.75, 2.37, 0.47551, 3, 41, 47.58, 14.8, 0.01074, 42, 26.7, 14.8, 0.28409, 43, 7.23, 14.16, 0.70518, 3, 41, 57.44, 16.91, 0.00098, 42, 36.56, 16.91, 0.12397, 43, 17.25, 15.3, 0.87506, 2, 42, 48.03, 19.35, 0.03728, 43, 28.9, 16.62, 0.96272, 2, 42, 60.49, 22.01, 0.00859, 43, 41.56, 18.06, 0.99141, 2, 42, 60.54, 22, 0.00104, 43, 41.62, 18.05, 0.99896, 1, 43, 47.81, 16.6, 1, 1, 43, 45.92, 8.5, 1, 1, 43, 49, 4.51, 1, 1, 43, 48.87, -5.18, 1, 2, 42, 69.61, -3.49, 0.00104, 43, 48.17, -8.22, 0.99896, 2, 42, 65.67, -13.56, 0.00859, 43, 43.26, -17.84, 0.99141, 2, 42, 51.27, -14.24, 0.03728, 43, 28.86, -17.13, 0.96272, 3, 41, 61.85, -14.73, 0.00098, 42, 40.97, -14.73, 0.12397, 43, 18.56, -16.62, 0.87506, 3, 41, 49.92, -13.15, 0.01074, 42, 29.05, -13.15, 0.28408, 43, 6.84, -13.88, 0.70518, 3, 41, 41.06, -1.88, 0.05467, 42, 20.18, -1.88, 0.46982, 43, -0.88, -1.8, 0.47551, 4, 40, 54.33, -2.01, 0.00033, 41, 33.46, -2.01, 0.17151, 42, 12.57, -2.01, 0.57415, 43, -8.47, -1.2, 0.25401, 4, 40, 46.31, -2.15, 0.00376, 41, 25.42, -2.15, 0.37313, 42, 4.55, -2.15, 0.52149, 43, -16.47, -0.55, 0.10162, 4, 40, 38.6, -2.28, 0.02351, 41, 17.72, -2.28, 0.59946, 42, -3.16, -2.28, 0.34869, 43, -24.15, 0.07, 0.02834, 4, 40, 30.44, -2.42, 0.08808, 41, 9.56, -2.42, 0.73997, 42, -11.32, -2.42, 0.16705, 43, -32.28, 0.73, 0.00489, 4, 40, 26.14, -2.49, 0.2308, 41, 5.26, -2.49, 0.71447, 42, -15.62, -2.49, 0.05433, 43, -36.58, 1.07, 0.00039, 3, 40, 21.46, -2.58, 0.44708, 41, 0.58, -2.58, 0.54216, 42, -20.3, -2.58, 0.01076, 3, 40, 15.65, -2.35, 0.68176, 41, -5.22, -2.35, 0.31726, 42, -26.1, -2.35, 0.00098, 2, 40, 9.3, -2.09, 0.85957, 41, -11.59, -2.09, 0.14042, 2, 40, -0.89, -0.6, 0.94992, 41, -21.78, -0.6, 0.05008], "hull": 36, "edges": [0, 70, 0, 2, 2, 4, 22, 24, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 48, 50, 50, 52, 68, 70, 52, 22, 60, 14, 64, 10, 4, 6, 68, 6, 64, 66, 66, 68, 6, 8, 8, 10, 66, 8, 60, 62, 62, 64, 10, 12, 12, 14, 62, 12, 18, 56, 56, 58, 58, 60, 14, 16, 16, 18, 58, 16, 52, 54, 54, 56, 18, 20, 20, 22, 54, 20, 50, 24, 24, 26, 48, 26, 44, 46, 46, 48, 26, 28, 28, 30, 46, 28], "width": 20, "height": 56}}, "gost2": {"bg/gost2": {"type": "mesh", "uvs": [0.5379, 0.05101, 0.53952, 0.10647, 0.5406, 0.14367, 0.54171, 0.18183, 0.54274, 0.21722, 0.54396, 0.2589, 0.54489, 0.29062, 0.54602, 0.32964, 0.54727, 0.37231, 0.54841, 0.41165, 0.54946, 0.44774, 0.55057, 0.4859, 0.83272, 0.5452, 0.92256, 0.71403, 0.9875, 0.83607, 0.9875, 0.91275, 0.72602, 0.95444, 0.61363, 1, 0.44209, 1, 0.0125, 0.85392, 0.0125, 0.81549, 0.18676, 0.71651, 0.18693, 0.54862, 0.48716, 0.48234, 0.48716, 0.44771, 0.48717, 0.41401, 0.48718, 0.37405, 0.48719, 0.32961, 0.48719, 0.2906, 0.4872, 0.25795, 0.48721, 0.21804, 0.48722, 0.18085, 0.48722, 0.14366, 0.48723, 0.10738, 0.48724, 0.07111], "triangles": [22, 11, 12, 21, 22, 12, 21, 12, 13, 19, 20, 21, 14, 19, 21, 14, 21, 13, 16, 19, 14, 15, 16, 14, 18, 19, 16, 17, 18, 16, 26, 8, 9, 25, 26, 9, 25, 9, 10, 24, 25, 10, 23, 24, 10, 23, 10, 11, 22, 23, 11, 29, 30, 4, 5, 29, 4, 28, 29, 5, 28, 5, 6, 27, 28, 6, 27, 6, 7, 8, 26, 27, 8, 27, 7, 34, 0, 1, 33, 34, 1, 33, 1, 2, 32, 33, 2, 31, 32, 2, 31, 2, 3, 30, 31, 3, 30, 3, 4], "vertices": [2, 44, -0.79, 0.66, 0.98055, 45, -16.14, 0.66, 0.01945, 2, 44, 4.64, 0.83, 0.94629, 45, -10.7, 0.83, 0.05371, 3, 44, 8.28, 0.94, 0.85144, 45, -7.06, 0.94, 0.14843, 46, -22.41, 0.94, 0.00013, 3, 44, 12.02, 1.05, 0.68525, 45, -3.32, 1.05, 0.31285, 46, -18.67, 1.05, 0.0019, 3, 44, 15.49, 1.16, 0.47185, 45, 0.14, 1.16, 0.51593, 46, -15.2, 1.16, 0.01221, 4, 44, 19.58, 1.29, 0.26633, 45, 4.23, 1.29, 0.68402, 46, -11.12, 1.29, 0.04964, 47, -26.42, 2.03, 1e-05, 4, 44, 22.68, 1.38, 0.11838, 45, 7.34, 1.38, 0.74048, 46, -8.01, 1.38, 0.14088, 47, -23.31, 2.04, 0.00026, 4, 44, 26.5, 1.5, 0.03943, 45, 11.16, 1.5, 0.65742, 46, -4.19, 1.5, 0.30001, 47, -19.49, 2.05, 0.00314, 4, 44, 30.68, 1.63, 0.00924, 45, 15.34, 1.63, 0.47626, 46, -0.01, 1.63, 0.49588, 47, -15.3, 2.06, 0.01862, 4, 44, 34.53, 1.75, 0.00134, 45, 19.19, 1.75, 0.27656, 46, 3.85, 1.75, 0.65195, 47, -11.44, 2.07, 0.07015, 4, 44, 38.08, 1.86, 0.0001, 45, 22.73, 1.86, 0.12561, 46, 7.38, 1.86, 0.68785, 47, -7.91, 2.08, 0.18645, 3, 45, 26.47, 1.98, 0.04264, 46, 11.12, 1.98, 0.58289, 47, -4.17, 2.09, 0.37447, 3, 45, 32.06, 13.36, 0.01025, 46, 16.72, 13.36, 0.39257, 47, 1.75, 13.32, 0.59718, 3, 45, 48.53, 17.27, 0.00152, 46, 33.19, 17.27, 0.20464, 47, 18.32, 16.76, 0.79385, 3, 45, 60.44, 20.09, 0.00012, 46, 45.1, 20.09, 0.07063, 47, 30.31, 19.25, 0.92926, 2, 46, 52.62, 20.24, 0.02444, 47, 37.82, 19.18, 0.97556, 2, 46, 56.9, 9.85, 0.00628, 47, 41.81, 8.69, 0.99372, 2, 46, 61.44, 5.45, 0.0027, 47, 46.24, 4.16, 0.9973, 2, 46, 61.58, -1.41, 0.01061, 47, 46.17, -2.71, 0.98939, 2, 46, 47.58, -18.86, 0.04067, 47, 31.7, -19.76, 0.95933, 3, 45, 59.17, -18.94, 0.00013, 46, 43.82, -18.93, 0.09739, 47, 27.93, -19.73, 0.90249, 3, 45, 49.33, -12.15, 0.00152, 46, 33.99, -12.15, 0.21016, 47, 18.3, -12.67, 0.78832, 3, 45, 32.88, -12.45, 0.01011, 46, 17.54, -12.45, 0.39362, 47, 1.85, -12.51, 0.59627, 3, 45, 26.17, -0.57, 0.04264, 46, 10.82, -0.57, 0.58311, 47, -4.54, -0.44, 0.37426, 4, 44, 38.12, -0.63, 9e-05, 45, 22.77, -0.63, 0.12527, 46, 7.43, -0.63, 0.68817, 47, -7.93, -0.41, 0.18647, 4, 44, 34.81, -0.69, 0.00134, 45, 19.47, -0.69, 0.27656, 46, 4.13, -0.69, 0.65202, 47, -11.24, -0.38, 0.07008, 4, 44, 30.9, -0.77, 0.00919, 45, 15.56, -0.77, 0.47585, 46, 0.21, -0.77, 0.49633, 47, -15.15, -0.34, 0.01863, 4, 44, 26.55, -0.85, 0.03943, 45, 11.2, -0.85, 0.65742, 46, -4.14, -0.85, 0.30004, 47, -19.51, -0.31, 0.00311, 4, 44, 22.73, -0.93, 0.11824, 45, 7.38, -0.93, 0.74027, 46, -7.97, -0.93, 0.14123, 47, -23.33, -0.27, 0.00026, 3, 44, 19.52, -0.99, 0.26633, 45, 4.18, -0.99, 0.68402, 46, -11.17, -0.99, 0.04965, 3, 44, 15.61, -1.05, 0.47167, 45, 0.27, -1.05, 0.51598, 46, -15.08, -1.05, 0.01235, 3, 44, 11.97, -1.12, 0.68525, 45, -3.37, -1.12, 0.31285, 46, -18.72, -1.12, 0.0019, 3, 44, 8.32, -1.2, 0.8513, 45, -7.02, -1.2, 0.14855, 46, -22.36, -1.2, 0.00015, 2, 44, 4.77, -1.26, 0.94628, 45, -10.57, -1.26, 0.05372, 2, 44, 1.22, -1.33, 0.98049, 45, -14.13, -1.33, 0.01951], "hull": 35, "edges": [0, 68, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 22, 60, 8, 52, 16, 64, 4, 64, 66, 66, 68, 0, 2, 2, 4, 66, 2, 60, 62, 62, 64, 4, 6, 6, 8, 62, 6, 56, 12, 56, 58, 58, 60, 8, 10, 10, 12, 58, 10, 52, 54, 54, 56, 12, 14, 14, 16, 54, 14, 50, 52, 16, 18, 50, 18, 46, 48, 48, 50, 18, 20, 20, 22, 48, 20, 44, 24, 24, 26, 26, 28, 42, 26, 38, 28], "width": 20, "height": 49}}, "gost3": {"bg/gost3": {"type": "mesh", "uvs": [0.53798, 0.06956, 0.538, 0.12678, 0.53802, 0.17843, 0.53803, 0.22478, 0.53805, 0.29894, 0.53808, 0.35986, 0.5381, 0.41416, 0.53812, 0.47961, 0.84813, 0.54223, 0.9875, 0.78581, 0.9875, 0.89302, 0.91656, 0.92276, 0.61446, 0.9949, 0.42092, 0.9949, 0.41528, 0.99489, 0.0125, 0.85731, 0.0125, 0.79052, 0.22516, 0.54321, 0.46185, 0.48412, 0.4616, 0.41416, 0.46141, 0.35854, 0.4612, 0.29802, 0.46093, 0.22478, 0.46077, 0.17975, 0.46059, 0.12943, 0.46034, 0.0591], "triangles": [14, 15, 16, 17, 8, 9, 10, 11, 9, 16, 17, 9, 16, 9, 14, 8, 17, 18, 9, 13, 14, 9, 12, 13, 11, 12, 9, 20, 21, 4, 20, 4, 5, 19, 20, 5, 19, 5, 6, 7, 18, 19, 7, 19, 6, 18, 7, 8, 24, 25, 0, 24, 0, 1, 24, 1, 2, 23, 24, 2, 3, 22, 23, 3, 23, 2, 21, 3, 4, 21, 22, 3], "vertices": [4, 36, -1.24, 1.59, 0.82608, 37, -14.93, 1.59, 0.11075, 38, -28.61, 1.59, 0.058, 39, -42.3, 1.65, 0.00517, 4, 36, 4.37, 1.65, 0.75552, 37, -9.31, 1.65, 0.1527, 38, -23, 1.65, 0.08427, 39, -36.69, 1.71, 0.00751, 4, 36, 9.44, 1.71, 0.61759, 37, -4.26, 1.71, 0.23063, 38, -17.94, 1.71, 0.13977, 39, -31.63, 1.75, 0.01201, 4, 36, 13.98, 1.75, 0.45581, 37, 0.28, 1.75, 0.3026, 38, -13.4, 1.75, 0.22387, 39, -27.09, 1.8, 0.01773, 4, 36, 21.25, 1.84, 0.3063, 37, 7.55, 1.84, 0.32055, 38, -6.14, 1.84, 0.34563, 39, -19.82, 1.87, 0.02752, 4, 36, 27.21, 1.9, 0.18478, 37, 13.52, 1.9, 0.27524, 38, -0.17, 1.9, 0.47756, 39, -13.85, 1.92, 0.06243, 4, 36, 32.53, 1.96, 0.10075, 37, 18.84, 1.96, 0.17851, 38, 5.16, 1.96, 0.55524, 39, -8.53, 1.98, 0.1655, 4, 36, 38.94, 2.03, 0.04651, 37, 25.26, 2.03, 0.08838, 38, 11.57, 2.03, 0.50139, 39, -2.12, 2.04, 0.36372, 4, 36, 44.95, 14.5, 0.02182, 37, 31.26, 14.5, 0.02751, 38, 17.57, 14.5, 0.34176, 39, 3.9, 14.49, 0.60891, 4, 36, 68.75, 20.33, 0.0072, 37, 55.07, 20.33, 0.00522, 38, 41.38, 20.33, 0.17303, 39, 27.73, 20.29, 0.81455, 3, 36, 79.26, 20.44, 0.00148, 38, 51.88, 20.44, 0.05965, 39, 38.23, 20.39, 0.93887, 2, 38, 54.83, 17.64, 0.01164, 39, 41.17, 17.58, 0.98836, 2, 38, 62.03, 5.64, 8e-05, 39, 48.35, 5.55, 0.99992, 1, 39, 48.42, -2.18, 1, 2, 38, 62.12, -2.33, 0.01235, 39, 48.42, -2.41, 0.98765, 3, 37, 62.5, -18.59, 0.00348, 38, 48.81, -18.59, 0.06058, 39, 35.09, -18.65, 0.93594, 4, 36, 69.64, -18.66, 0.00222, 37, 55.96, -18.66, 0.01687, 38, 42.26, -18.66, 0.17334, 39, 28.55, -18.7, 0.80758, 4, 36, 45.31, -10.42, 0.01177, 37, 31.63, -10.42, 0.0509, 38, 17.94, -10.42, 0.33662, 39, 4.24, -10.43, 0.60072, 4, 36, 39.42, -1.01, 0.03923, 37, 25.74, -1.01, 0.10512, 38, 12.05, -1.01, 0.49674, 39, -1.64, -1.01, 0.35891, 4, 36, 32.56, -1.1, 0.08898, 37, 18.88, -1.1, 0.20541, 38, 5.19, -1.1, 0.54302, 39, -8.5, -1.09, 0.16259, 4, 36, 27.11, -1.16, 0.17241, 37, 13.43, -1.16, 0.30304, 38, -0.26, -1.16, 0.46536, 39, -13.95, -1.14, 0.05919, 4, 36, 21.18, -1.24, 0.28974, 37, 7.49, -1.24, 0.35775, 38, -6.19, -1.24, 0.32745, 39, -19.89, -1.21, 0.02506, 4, 36, 14.01, -1.33, 0.44124, 37, 0.32, -1.33, 0.33438, 38, -13.37, -1.33, 0.2088, 39, -27.06, -1.28, 0.01558, 4, 36, 9.6, -1.38, 0.60415, 37, -4.09, -1.38, 0.26022, 38, -17.78, -1.38, 0.12514, 39, -31.47, -1.33, 0.01049, 4, 36, 4.66, -1.45, 0.74515, 37, -9.02, -1.45, 0.17448, 38, -22.72, -1.45, 0.07389, 39, -36.4, -1.38, 0.00647, 4, 36, -2.23, -1.53, 0.81963, 37, -15.91, -1.53, 0.12472, 38, -29.6, -1.53, 0.05109, 39, -43.29, -1.46, 0.00455], "hull": 26, "edges": [0, 50, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 14, 42, 8, 46, 4, 42, 44, 44, 46, 4, 6, 6, 8, 44, 6, 46, 48, 48, 50, 0, 2, 2, 4, 48, 2, 40, 42, 8, 10, 40, 10, 36, 38, 38, 40, 10, 12, 12, 14, 38, 12], "width": 20, "height": 49}}, "head": {"head": {"x": 64.17, "y": -6.14, "rotation": -91.73, "width": 165, "height": 170}}, "Layer 129": {"Layer 129": {"x": 12.49, "y": -7.87, "rotation": -121.43, "width": 94, "height": 104}}, "Layer 129 copy": {"Layer 129 copy": {"x": 16.58, "y": 6.86, "rotation": -58.91, "width": 94, "height": 104}}, "Layer 130": {"Layer 130": {"x": 100.74, "y": -2.68, "rotation": -89.25, "width": 338, "height": 289}}, "Layer 131": {"Layer 130": {"x": 100.74, "y": -2.68, "rotation": -89.25, "width": 338, "height": 289}}, "left eye": {"eye-3": {"x": -0.12, "y": -1.28, "scaleX": -1, "rotation": -91.73, "width": 50, "height": 45}, "eye 2 copy": {"x": -0.23, "y": -1.04, "rotation": -91.73, "width": 51, "height": 45}, "left eye": {"x": -0.12, "y": -1.28, "rotation": -91.73, "width": 49, "height": 44}}, "left hand": {"left hand": {"x": 23.26, "y": -0.08, "rotation": -28.88, "width": 74, "height": 61}, "right hand": {"x": 23.38, "y": -1.44, "scaleX": -1, "rotation": 147.4, "width": 62, "height": 52}}, "left hand 2": {"left hand 2": {"x": 34.35, "y": -0.7, "rotation": -19.52, "width": 67, "height": 52}}, "mouth": {"mouth_nomal": {"x": -7.68, "y": 0.24, "rotation": -89.8, "width": 45, "height": 17}, "mouth_open": {"x": -12.23, "y": 0.22, "rotation": -89.8, "width": 45, "height": 28}}, "nose": {"nose": {"x": -6.28, "y": -0.87, "rotation": -91.56, "width": 15, "height": 11}}, "pumpkin": {"pumpkin": {"x": 108.54, "y": -7.08, "rotation": -89.25, "width": 303, "height": 230}}, "pumpkin-1": {"bg/pumpkin-1": {"x": 11.69, "y": 97.28, "width": 143, "height": 200}}, "pumpkin-2": {"bg/pumpkin-2": {"x": 6.7, "y": 68.28, "width": 119, "height": 142}}, "pumpkin-3": {"bg/pumpkin-3": {"x": 2.12, "y": 52.93, "width": 125, "height": 111}}, "pumpkin-4": {"bg/pumpkin-4": {"x": 1.53, "y": 39.69, "width": 90, "height": 85}}, "right hand": {"left hand": {"x": 24.26, "y": 0.34, "rotation": -23.27, "width": 74, "height": 61}, "right hand": {"x": 16.51, "y": 2.28, "rotation": 44.57, "width": 62, "height": 52}}, "right hand 2": {"right hand 2": {"x": 29.34, "y": 1.95, "rotation": 112.05, "width": 50, "height": 67}}}}], "animations": {"cat_action1": {"slots": {"eye 2": {"attachment": [{"time": 0.7333, "name": "eye 2"}, {"time": 0.8333, "name": "right  eye"}, {"time": 1.2667, "name": "eye-3"}, {"time": 1.7667, "name": "right  eye"}, {"time": 2.7333, "name": "eye 2"}, {"time": 2.8333, "name": "right  eye"}, {"time": 3.2667, "name": "eye-3"}, {"time": 3.7667, "name": "right  eye"}]}, "glow": {"rgba": [{"color": "ffffffff"}, {"time": 0.3667, "color": "ffffffc1"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffffc1"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffc1"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffffc1"}, {"time": 2, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffc1"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffffc1"}, {"time": 3, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffc1"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.8667, "color": "ffffffc1"}, {"time": 4, "color": "ffffffff"}]}, "glow2": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow3": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow4": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow5": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "Layer 130": {"rgba": [{"color": "ffffffff"}, {"time": 0.2667, "color": "e23c3c00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "e23c3c00"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.2667, "color": "e23c3c00"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.7667, "color": "e23c3c00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2667, "color": "e23c3c00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7333, "color": "e23c3c00"}, {"time": 2.9667, "color": "ffffffff"}, {"time": 3.2667, "color": "e23c3c00"}, {"time": 3.5333, "color": "ffffffff"}, {"time": 3.7667, "color": "e23c3c00"}, {"time": 4, "color": "ffffffff"}]}, "left eye": {"attachment": [{"time": 0.7333, "name": "eye 2 copy"}, {"time": 0.8333, "name": "left eye"}, {"time": 1.2667, "name": "eye-3"}, {"time": 1.7667, "name": "left eye"}, {"time": 2.7333, "name": "eye 2 copy"}, {"time": 2.8333, "name": "left eye"}, {"time": 3.2667, "name": "eye-3"}, {"time": 3.7667, "name": "left eye"}]}}, "bones": {"pumpkin": {"scale": [{"x": 0.98, "y": 1.027, "curve": [0.081, 0.98, 0.185, 1.032, 0.081, 1.027, 0.185, 0.934]}, {"time": 0.2667, "x": 1.032, "y": 0.934, "curve": [0.348, 1.032, 0.419, 0.98, 0.348, 0.934, 0.419, 1.027]}, {"time": 0.5, "x": 0.98, "y": 1.027, "curve": [0.581, 0.98, 0.652, 1.032, 0.581, 1.027, 0.652, 0.934]}, {"time": 0.7333, "x": 1.032, "y": 0.934, "curve": [0.815, 1.032, 0.885, 0.98, 0.815, 0.934, 0.885, 1.027]}, {"time": 0.9667, "x": 0.98, "y": 1.027, "curve": [1.007, 0.98, 1.076, 0.985, 1.007, 1.027, 1.076, 1.019]}, {"time": 1.1, "x": 0.991, "y": 1.007, "curve": [1.157, 1.006, 1.21, 1.032, 1.157, 0.98, 1.21, 0.934]}, {"time": 1.2667, "x": 1.032, "y": 0.934, "curve": [1.348, 1.032, 1.452, 0.98, 1.348, 0.934, 1.452, 1.027]}, {"time": 1.5333, "x": 0.98, "y": 1.027, "curve": [1.574, 0.98, 1.594, 0.994, 1.574, 1.027, 1.594, 1.003]}, {"time": 1.6333, "x": 1.006, "y": 0.981, "curve": [1.674, 1.019, 1.726, 1.032, 1.674, 0.957, 1.726, 0.934]}, {"time": 1.7667, "x": 1.032, "y": 0.934, "curve": [1.848, 1.032, 1.919, 0.98, 1.848, 0.934, 1.919, 1.027]}, {"time": 2, "x": 0.98, "y": 1.027, "curve": [2.081, 0.98, 2.185, 1.032, 2.081, 1.027, 2.185, 0.934]}, {"time": 2.2667, "x": 1.032, "y": 0.934, "curve": [2.348, 1.032, 2.419, 0.98, 2.348, 0.934, 2.419, 1.027]}, {"time": 2.5, "x": 0.98, "y": 1.027, "curve": [2.581, 0.98, 2.652, 1.032, 2.581, 1.027, 2.652, 0.934]}, {"time": 2.7333, "x": 1.032, "y": 0.934, "curve": [2.815, 1.032, 2.885, 0.98, 2.815, 0.934, 2.885, 1.027]}, {"time": 2.9667, "x": 0.98, "y": 1.027, "curve": [3.007, 0.98, 3.076, 0.985, 3.007, 1.027, 3.076, 1.019]}, {"time": 3.1, "x": 0.991, "y": 1.007, "curve": [3.157, 1.006, 3.21, 1.032, 3.157, 0.98, 3.21, 0.934]}, {"time": 3.2667, "x": 1.032, "y": 0.934, "curve": [3.348, 1.032, 3.452, 0.98, 3.348, 0.934, 3.452, 1.027]}, {"time": 3.5333, "x": 0.98, "y": 1.027, "curve": [3.574, 0.98, 3.593, 0.993, 3.574, 1.027, 3.593, 1.004]}, {"time": 3.6333, "x": 1.006, "y": 0.981, "curve": [3.674, 1.019, 3.726, 1.032, 3.674, 0.957, 3.726, 0.934]}, {"time": 3.7667, "x": 1.032, "y": 0.934, "curve": [3.79, 1.032, 3.811, 1.028, 3.79, 0.934, 3.811, 0.941]}, {"time": 3.8333, "x": 1.022, "y": 0.952, "curve": [3.889, 1.007, 3.942, 0.98, 3.889, 0.979, 3.942, 1.027]}, {"time": 4, "x": 0.98, "y": 1.027}]}, "left hand": {"rotate": [{"value": 21.91, "curve": "stepped"}, {"time": 1.8, "value": 21.91, "curve": [1.94, 21.91, 1.995, 3.04]}, {"time": 2, "value": 2.69, "curve": [2.025, 1.13, 3.761, 3.04]}, {"time": 3.7667, "value": 2.69, "curve": [3.792, 1.13, 3.922, 21.91]}, {"time": 4, "value": 21.91}]}, "Mouth": {"scale": [{"x": 1.401}, {"time": 0.2667}, {"time": 0.5, "x": 1.401}, {"time": 0.7333}, {"time": 0.9667, "x": 1.401}, {"time": 1.1, "x": 1.281}, {"time": 1.2667, "curve": "stepped"}, {"time": 1.7667}, {"time": 2, "x": 1.401}, {"time": 2.2667}, {"time": 2.5, "x": 1.401}, {"time": 2.7333}, {"time": 2.9667, "x": 1.401}, {"time": 3.1, "x": 1.281}, {"time": 3.2667, "curve": "stepped"}, {"time": 3.7667}, {"time": 3.8333, "x": 1.115}, {"time": 4, "x": 1.401}]}, "cat body copy": {"rotate": [{"value": 6.56, "curve": "stepped"}, {"time": 1.6667, "value": 6.56}, {"time": 2, "value": -11.46, "curve": "stepped"}, {"time": 3.7667, "value": -11.46}, {"time": 4, "value": 6.56}], "translate": [{"x": -0.17, "y": -7.57, "curve": [0.011, -0.18, 0.022, -0.19, 0.011, -8.18, 0.022, -8.62]}, {"time": 0.0333, "x": -0.19, "y": -8.62, "curve": [0.106, -0.19, 0.259, 0, 0.106, -8.62, 0.259, 0]}, {"time": 0.2667, "curve": [0.338, 0, 0.425, -0.14, 0.338, 0, 0.425, -6.48]}, {"time": 0.5, "x": -0.18, "y": -8.17, "curve": [0.512, -0.18, 0.523, -0.19, 0.512, -8.43, 0.523, -8.62]}, {"time": 0.5333, "x": -0.19, "y": -8.62, "curve": [0.607, -0.19, 0.66, -0.04, 0.607, -8.62, 0.66, -1.64]}, {"time": 0.7333, "y": -0.24, "curve": [0.741, 0, 0.758, 0, 0.741, -0.09, 0.758, 0]}, {"time": 0.7667, "curve": [0.848, 0, 0.919, -0.55, 0.848, 0, 0.919, -8.68]}, {"time": 1, "x": -0.55, "y": -8.68, "curve": [1.025, -0.55, 1.084, -0.18, 1.034, -8.68, 1.084, -8.26]}, {"time": 1.1, "x": -0.17, "y": -7.71, "curve": [1.157, -0.12, 1.21, -0.03, 1.157, -5.77, 1.21, -1.33]}, {"time": 1.2667, "y": -0.24, "curve": [1.275, 0, 1.292, 0, 1.275, -0.08, 1.292, 0]}, {"time": 1.3, "curve": [1.381, 0, 1.419, -0.55, 1.381, 0, 1.419, -8.68]}, {"time": 1.5, "x": -0.55, "y": -8.68, "curve": [1.525, -0.55, 1.584, -0.18, 1.534, -8.68, 1.584, -8.26]}, {"time": 1.6, "x": -0.17, "y": -7.71, "curve": [1.657, -0.12, 1.795, 0, 1.657, -5.77, 1.737, 9.25]}, {"time": 1.8, "y": 9.71, "curve": [1.881, 0, 1.952, -0.19, 1.881, 10.3, 1.952, -8.62]}, {"time": 2.0333, "x": -0.19, "y": -8.62, "curve": [2.106, -0.19, 2.259, 0, 2.106, -8.62, 2.259, 0]}, {"time": 2.2667, "curve": [2.348, 0, 2.452, -0.19, 2.348, 0, 2.452, -8.62]}, {"time": 2.5333, "x": -0.19, "y": -8.62, "curve": [2.607, -0.19, 2.66, -0.04, 2.607, -8.62, 2.66, -1.64]}, {"time": 2.7333, "y": -0.24, "curve": [2.741, 0, 2.758, 0, 2.741, -0.09, 2.758, 0]}, {"time": 2.7667, "curve": [2.848, 0, 2.919, -0.55, 2.848, 0, 2.919, -8.68]}, {"time": 3, "x": -0.55, "y": -8.68, "curve": [3.025, -0.55, 3.084, -0.18, 3.034, -8.68, 3.084, -8.26]}, {"time": 3.1, "x": -0.17, "y": -7.71, "curve": [3.157, -0.12, 3.21, -0.03, 3.157, -5.77, 3.21, -1.33]}, {"time": 3.2667, "y": -0.24, "curve": [3.275, 0, 3.292, 0, 3.275, -0.08, 3.292, 0]}, {"time": 3.3, "curve": [3.381, 0, 3.452, -0.55, 3.381, 0, 3.452, -25.08]}, {"time": 3.5333, "x": -0.55, "y": -25.08, "curve": [3.559, -0.55, 3.597, -0.48, 3.568, -25.08, 3.6, -17.79]}, {"time": 3.6333, "x": -0.39, "y": -9.82, "curve": [3.703, -0.23, 3.795, 0, 3.673, -0.71, 3.737, 9.25]}, {"time": 3.8, "y": 9.71, "curve": [3.811, 0, 3.822, 0, 3.811, 9.79, 3.822, 9.4]}, {"time": 3.8333, "x": -0.01, "y": 8.82, "curve": [3.89, -0.04, 3.943, -0.14, 3.89, 5.84, 3.943, -4.35]}, {"time": 4, "x": -0.17, "y": -7.57}]}, "head": {"rotate": [{"value": 9.72}, {"time": 0.2667, "value": 15.48}, {"time": 1.1, "value": 8.59}, {"time": 1.6667, "value": 7.37}, {"time": 1.7667, "value": 11.55}, {"time": 2, "value": -10.6}, {"time": 2.1333, "value": -22.1}, {"time": 3.6333, "value": -10.6}, {"time": 3.8, "value": -15.63}, {"time": 4, "value": 9.72}], "translate": [{"x": -4.14, "curve": [0.081, -4.14, 0.185, 0, 0.081, 0, 0.185, 0]}, {"time": 0.2667, "curve": [0.348, 0, 0.419, -4.14, 0.348, 0, 0.419, 0]}, {"time": 0.5, "x": -4.14, "curve": [0.581, -4.14, 0.652, 0, 0.581, 0, 0.652, 0]}, {"time": 0.7333, "curve": [0.815, 0, 0.885, -4.14, 0.815, 0, 0.885, 0]}, {"time": 0.9667, "x": -4.14, "curve": [1.007, -4.14, 1.076, -3.77, 1.007, 0, 1.076, 0]}, {"time": 1.1, "x": -3.25, "curve": [1.157, -2.03, 1.21, 0, 1.157, 0, 1.21, 0]}, {"time": 1.2667, "curve": [1.348, 0, 1.452, -4.14, 1.348, 0, 1.452, 0]}, {"time": 1.5333, "x": -4.14, "curve": [1.574, -4.14, 1.594, -3.06, 1.574, 0, 1.594, 0]}, {"time": 1.6333, "x": -2.07, "curve": [1.674, -1.04, 1.726, 0, 1.674, 0, 1.726, 0]}, {"time": 1.7667, "curve": [1.848, 0, 1.919, -4.14, 1.848, 0, 1.919, 0]}, {"time": 2, "x": -4.14, "curve": [2.081, -4.14, 2.185, 0, 2.081, 0, 2.185, 0]}, {"time": 2.2667, "curve": [2.348, 0, 2.419, -4.14, 2.348, 0, 2.419, 0]}, {"time": 2.5, "x": -4.14, "curve": [2.581, -4.14, 2.652, 0, 2.581, 0, 2.652, 0]}, {"time": 2.7333, "curve": [2.815, 0, 2.885, -4.14, 2.815, 0, 2.885, 0]}, {"time": 2.9667, "x": -4.14, "curve": [3.007, -4.14, 3.21, 0, 3.007, 0, 3.21, 0]}, {"time": 3.2667, "curve": [3.348, 0, 3.452, -4.14, 3.348, 0, 3.452, 0]}, {"time": 3.5333, "x": -4.14, "curve": [3.574, -4.14, 3.726, 0, 3.574, 0, 3.726, 0]}, {"time": 3.7667, "curve": [3.79, 0, 3.811, -0.33, 3.79, 0, 3.811, 0]}, {"time": 3.8333, "x": -0.81, "curve": [3.889, -2.01, 3.942, -4.14, 3.889, 0, 3.942, 0]}, {"time": 4, "x": -4.14}]}, "left hand 2": {"rotate": [{"value": -6.38, "curve": [1.256, -6.38, 2.511, 0]}, {"time": 3.7667, "curve": [3.848, 0, 3.922, -6.38]}, {"time": 4, "value": -6.38}], "translate": [{"x": 3.1, "y": 3.35}, {"time": 2, "x": 1.83, "y": -0.97, "curve": "stepped"}, {"time": 3.7667, "x": 1.83, "y": -0.97}, {"time": 4, "x": 3.1, "y": 3.35}]}, "right hand 2": {"rotate": [{"value": -9.45, "curve": [0.081, -9.45, 0.185, 0]}, {"time": 0.2667, "curve": [0.348, 0, 0.419, -9.45]}, {"time": 0.5, "value": -9.45, "curve": [0.581, -9.45, 0.652, 0]}, {"time": 0.7333, "curve": [0.815, 0, 0.885, -9.45]}, {"time": 0.9667, "value": -9.45, "curve": [1.007, -9.45, 1.076, -8.6]}, {"time": 1.1, "value": -7.41, "curve": [1.157, -4.63, 1.21, 0]}, {"time": 1.2667, "curve": [1.348, 0, 1.452, -9.45]}, {"time": 1.5333, "value": -9.45, "curve": [1.574, -9.45, 1.594, -6.98]}, {"time": 1.6333, "value": -4.73, "curve": [1.674, -2.36, 1.726, 0]}, {"time": 1.7667, "curve": [1.848, 0, 1.919, -16.93]}, {"time": 2, "value": -16.93, "curve": "stepped"}, {"time": 3.8333, "value": -16.93, "curve": [3.847, -16.93, 3.942, -9.45]}, {"time": 4, "value": -9.45}]}, "Layer 129 copy": {"rotate": [{"value": -9.13, "curve": [0.023, -10.2, 0.046, -10.94]}, {"time": 0.0667, "value": -10.94, "curve": [0.132, -10.94, 0.201, -3.77]}, {"time": 0.2667, "value": -1.14, "curve": [0.283, -0.46, 0.284, 0]}, {"time": 0.3, "curve": [0.37, 0, 0.432, -8.08]}, {"time": 0.5, "value": -10.31, "curve": [0.511, -10.66, 0.522, -10.94]}, {"time": 0.5333, "value": -10.94, "curve": [0.599, -10.94, 0.668, -3.77]}, {"time": 0.7333, "value": -1.14, "curve": [0.75, -0.46, 0.784, 0]}, {"time": 0.8, "curve": [0.881, 0, 0.952, -10.94]}, {"time": 1.0333, "value": -10.94, "curve": [1.058, -10.94, 1.092, -10.81]}, {"time": 1.1, "value": -10.6, "curve": [1.158, -9.18, 1.21, -3.44]}, {"time": 1.2667, "value": -1.14, "curve": [1.283, -0.46, 1.317, 0]}, {"time": 1.3333, "curve": [1.415, 0, 1.485, -10.94]}, {"time": 1.5667, "value": -10.94, "curve": [1.591, -10.94, 1.611, -9.78]}, {"time": 1.6333, "value": -8.52, "curve": [1.674, -6.19, 1.726, 10.5]}, {"time": 1.7667, "value": 12.14, "curve": [1.784, 12.82, 1.784, 10.38]}, {"time": 1.8, "value": 10.38, "curve": [1.881, 10.38, 1.986, -10.94]}, {"time": 2.0667, "value": -10.94, "curve": [2.132, -10.94, 2.201, -17.54]}, {"time": 2.2667, "value": -14.91, "curve": [2.283, -14.23, 2.284, -10.22]}, {"time": 2.3, "value": -10.22, "curve": [2.381, -10.22, 2.452, -10.94]}, {"time": 2.5333, "value": -10.94, "curve": [2.599, -10.94, 2.668, -3.77]}, {"time": 2.7333, "value": -1.14, "curve": [2.75, -0.46, 2.784, 0]}, {"time": 2.8, "curve": [2.881, 0, 2.952, -10.94]}, {"time": 3.0333, "value": -10.94, "curve": [3.058, -10.94, 3.092, -10.81]}, {"time": 3.1, "value": -10.6, "curve": [3.158, -9.18, 3.21, -3.44]}, {"time": 3.2667, "value": -1.14, "curve": [3.283, -0.46, 3.317, 0]}, {"time": 3.3333, "curve": [3.415, 0, 3.485, -10.94]}, {"time": 3.5667, "value": -10.94, "curve": [3.591, -10.94, 3.609, -9.93]}, {"time": 3.6333, "value": -8.52, "curve": [3.674, -6.19, 3.726, -2.78]}, {"time": 3.7667, "value": -1.14, "curve": [3.784, -0.46, 3.784, 0]}, {"time": 3.8, "curve": [3.811, 0, 3.822, -0.25]}, {"time": 3.8333, "value": -0.57, "curve": [3.886, -2.11, 3.945, -6.6]}, {"time": 4, "value": -9.13}]}, "Layer 129 copy2": {"rotate": [{"value": -6.66, "curve": [0.033, -8.92, 0.066, -10.94]}, {"time": 0.1, "value": -10.94, "curve": [0.149, -10.94, 0.218, -6.91]}, {"time": 0.2667, "value": -3.85, "curve": [0.3, -1.77, 0.301, 0]}, {"time": 0.3333, "curve": [0.384, 0, 0.443, -4.19]}, {"time": 0.5, "value": -7.39, "curve": [0.535, -9.35, 0.569, -10.94]}, {"time": 0.6, "value": -10.94, "curve": [0.649, -10.94, 0.684, -6.91]}, {"time": 0.7333, "value": -3.85, "curve": [0.766, -1.77, 0.801, 0]}, {"time": 0.8333, "curve": [0.914, 0, 0.986, -10.94]}, {"time": 1.0667, "value": -10.94, "curve": [1.075, -10.94, 1.027, -8.86]}, {"time": 1.1, "value": -10.63, "curve": [1.108, -10.83, 1.125, -10.94]}, {"time": 1.1333, "value": -10.94, "curve": [1.182, -10.94, 1.218, -6.91]}, {"time": 1.2667, "value": -3.85, "curve": [1.3, -1.77, 1.334, 0]}, {"time": 1.3667, "curve": [1.448, 0, 1.628, -10.75]}, {"time": 1.6333, "value": -10.61, "curve": [1.674, -9.61, 1.726, -6.4]}, {"time": 1.7667, "value": -3.85, "curve": [1.8, -1.77, 1.834, 0]}, {"time": 1.8667, "curve": [1.948, 0, 2.019, -10.94]}, {"time": 2.1, "value": -10.94, "curve": [2.149, -10.94, 2.218, -6.91]}, {"time": 2.2667, "value": -3.85, "curve": [2.3, -1.77, 2.301, 0]}, {"time": 2.3333, "curve": [2.415, 0, 2.519, -10.94]}, {"time": 2.6, "value": -10.94, "curve": [2.649, -10.94, 2.684, -6.91]}, {"time": 2.7333, "value": -3.85, "curve": [2.766, -1.77, 2.801, 0]}, {"time": 2.8333, "curve": [2.914, 0, 2.986, -10.94]}, {"time": 3.0667, "value": -10.94, "curve": [3.075, -10.94, 3.027, -8.86]}, {"time": 3.1, "value": -10.63, "curve": [3.108, -10.83, 3.125, -10.94]}, {"time": 3.1333, "value": -10.94, "curve": [3.182, -10.94, 3.218, -6.91]}, {"time": 3.2667, "value": -3.85, "curve": [3.3, -1.77, 3.334, 0]}, {"time": 3.3667, "curve": [3.448, 0, 3.625, -10.81]}, {"time": 3.6333, "value": -10.61, "curve": [3.674, -9.61, 3.726, -6.4]}, {"time": 3.7667, "value": -3.85, "curve": [3.789, -2.47, 3.811, -1.23]}, {"time": 3.8333, "value": -0.56, "curve": [3.845, -0.21, 3.856, 0]}, {"time": 3.8667, "curve": [3.914, 0, 3.957, -3.58]}, {"time": 4, "value": -6.66}]}, "Layer 129": {"rotate": [{"value": 10.39}, {"time": 0.0667, "value": 13.85}, {"time": 0.2667, "value": 2.77}, {"time": 0.3}, {"time": 0.5, "value": 11.87}, {"time": 0.5333, "value": 13.85}, {"time": 0.7333, "value": 2.77}, {"time": 0.8}, {"time": 1.0333, "value": 13.85}, {"time": 1.1, "value": 12.47}, {"time": 1.2667, "value": 2.77}, {"time": 1.3333}, {"time": 1.5667, "value": 13.85}, {"time": 1.6333, "value": 9.7}, {"time": 1.7667, "value": 16.05}, {"time": 1.8, "value": 10.38}, {"time": 2.0667, "value": 13.85}, {"time": 2.2667, "value": -11}, {"time": 2.3, "value": -10.22}, {"time": 2.5333, "value": 13.85}, {"time": 2.7333, "value": 2.77}, {"time": 2.8}, {"time": 3.0333, "value": 13.85}, {"time": 3.1, "value": 12.47}, {"time": 3.2667, "value": 2.77}, {"time": 3.3333}, {"time": 3.5667, "value": 13.85}, {"time": 3.6333, "value": 9.7}, {"time": 3.7667, "value": 2.77}, {"time": 3.8}, {"time": 3.8333, "value": 1.73}, {"time": 4, "value": 10.39}]}, "Layer 130": {"rotate": [{"value": 7.91}, {"time": 0.1, "value": 13.85}, {"time": 0.2667, "value": 5.54}, {"time": 0.3333}, {"time": 0.5, "value": 8.66}, {"time": 0.6, "value": 13.85}, {"time": 0.7333, "value": 5.54}, {"time": 0.8333}, {"time": 1.0667, "value": 13.85}, {"time": 1.1, "value": 12.47}, {"time": 1.1333, "value": 13.85}, {"time": 1.2667, "value": 5.54}, {"time": 1.3667}, {"time": 1.6333, "value": 12.47}, {"time": 1.7667, "value": 5.54}, {"time": 1.8667}, {"time": 2.1, "value": 13.85}, {"time": 2.2667, "value": 5.54}, {"time": 2.3333}, {"time": 2.6, "value": 13.85}, {"time": 2.7333, "value": 5.54}, {"time": 2.8333}, {"time": 3.0667, "value": 13.85}, {"time": 3.1, "value": 12.47}, {"time": 3.1333, "value": 13.85}, {"time": 3.2667, "value": 5.54}, {"time": 3.3667}, {"time": 3.6333, "value": 12.47}, {"time": 3.7667, "value": 5.54}, {"time": 3.8333, "value": 1.85}, {"time": 3.8667}, {"time": 4, "value": 7.91}]}, "Bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}, "light_floor": {"scale": [{}, {"time": 0.3667, "x": 0.8, "y": 0.8}, {"time": 0.5}, {"time": 0.8667, "x": 0.8, "y": 0.8}, {"time": 1}, {"time": 1.3333, "x": 0.8, "y": 0.8}, {"time": 1.5}, {"time": 1.8333, "x": 0.8, "y": 0.8}, {"time": 2}, {"time": 2.3333, "x": 0.8, "y": 0.8}, {"time": 2.5}, {"time": 2.8333, "x": 0.8, "y": 0.8}, {"time": 3}, {"time": 3.3333, "x": 0.8, "y": 0.8}, {"time": 3.5}, {"time": 3.8667, "x": 0.8, "y": 0.8}, {"time": 4}]}, "right hand": {"rotate": [{"time": 1.1}, {"time": 1.8333, "value": -24.59}, {"time": 2, "value": -21.99, "curve": "stepped"}, {"time": 3.8333, "value": -21.99}, {"time": 4}]}, "cat  tail1e": {"rotate": [{"value": -28.9, "curve": [0.052, -24.78, 0.115, 3.09]}, {"time": 0.1667, "value": 9.31, "curve": [0.22, 15.43, 0.248, 40.93]}, {"time": 0.3, "value": 40.93, "curve": [0.357, 40.93, 0.444, 35.57]}, {"time": 0.5, "value": 14.66, "curve": [0.549, -2.91, 0.586, -35.66]}, {"time": 0.6333, "value": -44.15, "curve": [0.66, -48.7, 0.674, -32.27]}, {"time": 0.7, "value": -32.27, "curve": [0.727, -32.27, 0.774, -30.94]}, {"time": 0.8, "value": -28.9, "curve": [0.852, -24.78, 0.915, 3.09]}, {"time": 0.9667, "value": 9.31, "curve": [1.02, 15.43, 1.048, 40.93]}, {"time": 1.1, "value": 40.93, "curve": [1.157, 40.93, 1.244, 35.57]}, {"time": 1.3, "value": 14.66, "curve": [1.349, -2.91, 1.386, -35.66]}, {"time": 1.4333, "value": -44.15, "curve": [1.46, -48.7, 1.474, -32.27]}, {"time": 1.5, "value": -32.27, "curve": [1.527, -32.27, 1.574, -30.94]}, {"time": 1.6, "value": -28.9, "curve": [1.652, -24.78, 1.715, 3.09]}, {"time": 1.7667, "value": 9.31, "curve": [1.82, 15.43, 1.848, 40.93]}, {"time": 1.9, "value": 40.93, "curve": [1.957, 40.93, 2.044, 35.57]}, {"time": 2.1, "value": 14.66, "curve": [2.149, -2.91, 2.186, -35.66]}, {"time": 2.2333, "value": -44.15, "curve": [2.26, -48.7, 2.274, -32.27]}, {"time": 2.3, "value": -32.27, "curve": [2.327, -32.27, 2.374, -30.94]}, {"time": 2.4, "value": -28.9, "curve": [2.452, -24.78, 2.515, 3.09]}, {"time": 2.5667, "value": 9.31, "curve": [2.62, 15.43, 2.648, 40.93]}, {"time": 2.7, "value": 40.93, "curve": [2.757, 40.93, 2.844, 35.57]}, {"time": 2.9, "value": 14.66, "curve": [2.949, -2.91, 2.986, -35.66]}, {"time": 3.0333, "value": -44.15, "curve": [3.06, -48.7, 3.074, -32.27]}, {"time": 3.1, "value": -32.27, "curve": [3.127, -32.27, 3.174, -30.94]}, {"time": 3.2, "value": -28.9, "curve": [3.252, -24.78, 3.315, 3.09]}, {"time": 3.3667, "value": 9.31, "curve": [3.42, 15.43, 3.448, 40.93]}, {"time": 3.5, "value": 40.93, "curve": [3.557, 40.93, 3.644, 35.57]}, {"time": 3.7, "value": 14.66, "curve": [3.749, -2.91, 3.786, -35.66]}, {"time": 3.8333, "value": -44.15, "curve": [3.86, -48.7, 3.874, -32.27]}, {"time": 3.9, "value": -32.27, "curve": [3.927, -32.27, 3.974, -30.94]}, {"time": 4, "value": -28.9}]}, "cat  tail1d": {"rotate": [{"value": -20.94, "curve": [0.039, -16.28, 0.094, 4.11]}, {"time": 0.1333, "value": 8.18, "curve": [0.174, 12.18, 0.195, 27.53]}, {"time": 0.2333, "value": 27.53, "curve": [0.286, 27.53, 0.314, 20.17]}, {"time": 0.3667, "value": 11.03, "curve": [0.41, 3.41, 0.49, -22.97]}, {"time": 0.5333, "value": -32.61, "curve": [0.568, -40.28, 0.599, -32.27]}, {"time": 0.6333, "value": -32.27, "curve": [0.686, -32.27, 0.748, -27.11]}, {"time": 0.8, "value": -20.94, "curve": [0.839, -16.28, 0.894, 4.11]}, {"time": 0.9333, "value": 8.18, "curve": [0.974, 12.18, 0.995, 27.53]}, {"time": 1.0333, "value": 27.53, "curve": [1.086, 27.53, 1.114, 20.17]}, {"time": 1.1667, "value": 11.03, "curve": [1.21, 3.41, 1.29, -22.97]}, {"time": 1.3333, "value": -32.61, "curve": [1.368, -40.28, 1.399, -32.27]}, {"time": 1.4333, "value": -32.27, "curve": [1.486, -32.27, 1.548, -27.11]}, {"time": 1.6, "value": -20.94, "curve": [1.639, -16.28, 1.694, 4.11]}, {"time": 1.7333, "value": 8.18, "curve": [1.774, 12.18, 1.795, 27.53]}, {"time": 1.8333, "value": 27.53, "curve": [1.886, 27.53, 1.914, 20.17]}, {"time": 1.9667, "value": 11.03, "curve": [2.01, 3.41, 2.09, -22.97]}, {"time": 2.1333, "value": -32.61, "curve": [2.168, -40.28, 2.199, -32.27]}, {"time": 2.2333, "value": -32.27, "curve": [2.286, -32.27, 2.348, -27.11]}, {"time": 2.4, "value": -20.94, "curve": [2.439, -16.28, 2.494, 4.11]}, {"time": 2.5333, "value": 8.18, "curve": [2.574, 12.18, 2.595, 27.53]}, {"time": 2.6333, "value": 27.53, "curve": [2.686, 27.53, 2.714, 20.17]}, {"time": 2.7667, "value": 11.03, "curve": [2.81, 3.41, 2.89, -22.97]}, {"time": 2.9333, "value": -32.61, "curve": [2.968, -40.28, 2.999, -32.27]}, {"time": 3.0333, "value": -32.27, "curve": [3.086, -32.27, 3.148, -27.11]}, {"time": 3.2, "value": -20.94, "curve": [3.239, -16.28, 3.294, 4.11]}, {"time": 3.3333, "value": 8.18, "curve": [3.374, 12.18, 3.395, 27.53]}, {"time": 3.4333, "value": 27.53, "curve": [3.486, 27.53, 3.514, 20.17]}, {"time": 3.5667, "value": 11.03, "curve": [3.61, 3.41, 3.69, -22.97]}, {"time": 3.7333, "value": -32.61, "curve": [3.768, -40.28, 3.799, -32.27]}, {"time": 3.8333, "value": -32.27, "curve": [3.886, -32.27, 3.948, -27.11]}, {"time": 4, "value": -20.94}]}, "cat  tail1c": {"rotate": [{"value": -11.41, "curve": [0.027, -8.32, 0.041, -5.4]}, {"time": 0.0667, "value": -3.36, "curve": [0.093, -1.35, 0.141, 11.9]}, {"time": 0.1667, "value": 11.9, "curve": [0.219, 11.9, 0.248, 18.99]}, {"time": 0.3, "value": 10.51, "curve": [0.344, 3.43, 0.423, -14.25]}, {"time": 0.4667, "value": -24.91, "curve": [0.501, -33.4, 0.532, -42.88]}, {"time": 0.5667, "value": -42.88, "curve": [0.645, -42.88, 0.722, -20.57]}, {"time": 0.8, "value": -11.41, "curve": [0.827, -8.32, 0.841, -5.4]}, {"time": 0.8667, "value": -3.36, "curve": [0.893, -1.35, 0.941, 11.9]}, {"time": 0.9667, "value": 11.9, "curve": [1.019, 11.9, 1.048, 18.99]}, {"time": 1.1, "value": 10.51, "curve": [1.144, 3.43, 1.223, -14.25]}, {"time": 1.2667, "value": -24.91, "curve": [1.301, -33.4, 1.332, -42.88]}, {"time": 1.3667, "value": -42.88, "curve": [1.445, -42.88, 1.522, -20.57]}, {"time": 1.6, "value": -11.41, "curve": [1.627, -8.32, 1.641, -5.4]}, {"time": 1.6667, "value": -3.36, "curve": [1.693, -1.35, 1.741, 11.9]}, {"time": 1.7667, "value": 11.9, "curve": [1.819, 11.9, 1.848, 18.99]}, {"time": 1.9, "value": 10.51, "curve": [1.944, 3.43, 2.023, -14.25]}, {"time": 2.0667, "value": -24.91, "curve": [2.101, -33.4, 2.132, -42.88]}, {"time": 2.1667, "value": -42.88, "curve": [2.245, -42.88, 2.322, -20.57]}, {"time": 2.4, "value": -11.41, "curve": [2.427, -8.32, 2.441, -5.4]}, {"time": 2.4667, "value": -3.36, "curve": [2.493, -1.35, 2.541, 11.9]}, {"time": 2.5667, "value": 11.9, "curve": [2.619, 11.9, 2.648, 18.99]}, {"time": 2.7, "value": 10.51, "curve": [2.744, 3.43, 2.823, -14.25]}, {"time": 2.8667, "value": -24.91, "curve": [2.901, -33.4, 2.932, -42.88]}, {"time": 2.9667, "value": -42.88, "curve": [3.045, -42.88, 3.122, -20.57]}, {"time": 3.2, "value": -11.41, "curve": [3.227, -8.32, 3.241, -5.4]}, {"time": 3.2667, "value": -3.36, "curve": [3.293, -1.35, 3.341, 11.9]}, {"time": 3.3667, "value": 11.9, "curve": [3.419, 11.9, 3.448, 18.99]}, {"time": 3.5, "value": 10.51, "curve": [3.544, 3.43, 3.623, -14.25]}, {"time": 3.6667, "value": -24.91, "curve": [3.701, -33.4, 3.732, -42.88]}, {"time": 3.7667, "value": -42.88, "curve": [3.845, -42.88, 3.922, -20.57]}, {"time": 4, "value": -11.41}]}, "cat  tail1b": {"rotate": [{"value": -3.38, "curve": [0.013, -2.38, 0.054, 0]}, {"time": 0.0667, "curve": [0.197, 0, 0.369, -32.27]}, {"time": 0.5, "value": -32.27, "curve": [0.605, -32.27, 0.696, -11.2]}, {"time": 0.8, "value": -3.38, "curve": [0.813, -2.38, 0.854, 0]}, {"time": 0.8667, "curve": [0.997, 0, 1.169, -32.27]}, {"time": 1.3, "value": -32.27, "curve": [1.405, -32.27, 1.496, -11.2]}, {"time": 1.6, "value": -3.38, "curve": [1.613, -2.38, 1.654, 0]}, {"time": 1.6667, "curve": [1.797, 0, 1.969, -32.27]}, {"time": 2.1, "value": -32.27, "curve": [2.205, -32.27, 2.296, -11.2]}, {"time": 2.4, "value": -3.38, "curve": [2.413, -2.38, 2.454, 0]}, {"time": 2.4667, "curve": [2.597, 0, 2.769, -32.27]}, {"time": 2.9, "value": -32.27, "curve": [3.005, -32.27, 3.096, -11.2]}, {"time": 3.2, "value": -3.38, "curve": [3.213, -2.38, 3.254, 0]}, {"time": 3.2667, "curve": [3.397, 0, 3.569, -32.27]}, {"time": 3.7, "value": -32.27, "curve": [3.805, -32.27, 3.896, -11.2]}, {"time": 4, "value": -3.38}]}, "cat  tail1": {"rotate": [{"curve": [0.131, 0, 0.236, -54.37]}, {"time": 0.3667, "value": -54.37, "curve": [0.497, -54.37, 0.669, 0]}, {"time": 0.8, "curve": [0.931, 0, 1.036, -54.37]}, {"time": 1.1667, "value": -54.37, "curve": [1.297, -54.37, 1.469, 0]}, {"time": 1.6, "curve": [1.731, 0, 1.836, -54.37]}, {"time": 1.9667, "value": -54.37, "curve": [2.097, -54.37, 2.269, 0]}, {"time": 2.4, "curve": [2.531, 0, 2.636, -54.37]}, {"time": 2.7667, "value": -54.37, "curve": [2.897, -54.37, 3.069, 0]}, {"time": 3.2, "curve": [3.331, 0, 3.436, -54.37]}, {"time": 3.5667, "value": -54.37, "curve": [3.697, -54.37, 3.869, 0]}, {"time": 4}]}}}, "cat_action2": {"slots": {"eye 2": {"attachment": [{"time": 0.7333, "name": "eye 2"}, {"time": 0.8333, "name": "right  eye"}, {"time": 1.2667, "name": "eye-3"}, {"time": 1.7667, "name": "right  eye"}, {"time": 2.7333, "name": "eye 2"}, {"time": 2.8333, "name": "right  eye"}, {"time": 3.2667, "name": "eye-3"}, {"time": 3.7667, "name": "right  eye"}]}, "glow": {"rgba": [{"color": "ffffffff"}, {"time": 0.3667, "color": "ffffffc1"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffffc1"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffc1"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffffc1"}, {"time": 2, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffffc1"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffffc1"}, {"time": 3, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffffc1"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.8667, "color": "ffffffc1"}, {"time": 4, "color": "ffffffff"}]}, "glow2": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow3": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow4": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow5": {"rgba": [{"color": "ffffffff", "curve": [0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 1, 0.061, 1, 0.139, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 1, 0.306, 1, 0.261, 0.62, 0.306, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 1, 0.427, 1, 0.505, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 1, 0.676, 1, 0.628, 0.62, 0.676, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 1, 0.794, 1, 0.872, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 1, 1.039, 1, 0.994, 0.62, 1.039, 0.62]}, {"time": 1.1, "color": "ffffff9f", "curve": [1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 1, 1.21, 1, 1.161, 0.62, 1.21, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 1, 1.327, 1, 1.405, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 1, 1.578, 1, 1.528, 0.62, 1.578, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 1, 1.694, 1, 1.772, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 1, 1.943, 1, 1.894, 0.62, 1.943, 1]}, {"time": 2, "color": "ffffffff", "curve": [2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 1, 2.061, 1, 2.139, 0.62]}, {"time": 2.2, "color": "ffffff9f", "curve": [2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 1, 2.306, 1, 2.261, 0.62, 2.306, 1]}, {"time": 2.3667, "color": "ffffffff", "curve": [2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 1, 2.427, 1, 2.505, 0.62]}, {"time": 2.5667, "color": "ffffff9f", "curve": [2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 1, 2.676, 1, 2.628, 0.62, 2.676, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 1, 2.794, 1, 2.872, 0.62]}, {"time": 2.9333, "color": "ffffff9f", "curve": [2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 1, 3.039, 1, 2.994, 0.62, 3.039, 0.62]}, {"time": 3.1, "color": "ffffff9f", "curve": [3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 1, 3.21, 1, 3.161, 0.62, 3.21, 1]}, {"time": 3.2667, "color": "ffffffff", "curve": [3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 1, 3.327, 1, 3.405, 0.62]}, {"time": 3.4667, "color": "ffffff9f", "curve": [3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 1, 3.573, 1, 3.528, 0.62, 3.573, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 1, 3.694, 1, 3.767, 0.63]}, {"time": 3.8333, "color": "ffffff9f", "curve": [3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 1, 3.943, 1, 3.894, 0.62, 3.943, 1]}, {"time": 4, "color": "ffffffff"}]}, "Layer 130": {"rgba": [{"color": "ffffffff"}, {"time": 0.2667, "color": "e23c3c00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "e23c3c00"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.2667, "color": "e23c3c00"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.7667, "color": "e23c3c00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2667, "color": "e23c3c00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7333, "color": "e23c3c00"}, {"time": 2.9667, "color": "ffffffff"}, {"time": 3.2667, "color": "e23c3c00"}, {"time": 3.5333, "color": "ffffffff"}, {"time": 3.7667, "color": "e23c3c00"}, {"time": 4, "color": "ffffffff"}]}, "left eye": {"attachment": [{"time": 0.7333, "name": "eye 2 copy"}, {"time": 0.8333, "name": "left eye"}, {"time": 1.2667, "name": "eye-3"}, {"time": 1.7667, "name": "left eye"}, {"time": 2.7333, "name": "eye 2 copy"}, {"time": 2.8333, "name": "left eye"}, {"time": 3.2667, "name": "eye-3"}, {"time": 3.7667, "name": "left eye"}]}, "left hand": {"attachment": [{"name": "left hand"}, {"time": 1.9, "name": "right hand"}, {"time": 3.8667, "name": "left hand"}]}, "mouth": {"attachment": [{"name": "mouth_open"}]}, "right hand": {"attachment": [{"time": 1.9, "name": "left hand"}, {"time": 3.8667, "name": "right hand"}]}}, "bones": {"pumpkin": {"scale": [{"x": 0.98, "y": 1.027, "curve": [0.081, 0.98, 0.185, 1.032, 0.081, 1.027, 0.185, 0.934]}, {"time": 0.2667, "x": 1.032, "y": 0.934, "curve": [0.348, 1.032, 0.419, 0.98, 0.348, 0.934, 0.419, 1.027]}, {"time": 0.5, "x": 0.98, "y": 1.027, "curve": [0.581, 0.98, 0.652, 1.032, 0.581, 1.027, 0.652, 0.934]}, {"time": 0.7333, "x": 1.032, "y": 0.934, "curve": [0.815, 1.032, 0.885, 0.98, 0.815, 0.934, 0.885, 1.027]}, {"time": 0.9667, "x": 0.98, "y": 1.027, "curve": [1.007, 0.98, 1.076, 0.985, 1.007, 1.027, 1.076, 1.019]}, {"time": 1.1, "x": 0.991, "y": 1.007, "curve": [1.157, 1.006, 1.21, 1.032, 1.157, 0.98, 1.21, 0.934]}, {"time": 1.2667, "x": 1.032, "y": 0.934, "curve": [1.348, 1.032, 1.452, 0.98, 1.348, 0.934, 1.452, 1.027]}, {"time": 1.5333, "x": 0.98, "y": 1.027, "curve": [1.574, 0.98, 1.594, 0.994, 1.574, 1.027, 1.594, 1.003]}, {"time": 1.6333, "x": 1.006, "y": 0.981, "curve": [1.674, 1.019, 1.726, 1.032, 1.674, 0.957, 1.726, 0.934]}, {"time": 1.7667, "x": 1.032, "y": 0.934, "curve": [1.848, 1.032, 1.919, 0.98, 1.848, 0.934, 1.919, 1.027]}, {"time": 2, "x": 0.98, "y": 1.027, "curve": [2.081, 0.98, 2.185, 1.032, 2.081, 1.027, 2.185, 0.934]}, {"time": 2.2667, "x": 1.032, "y": 0.934, "curve": [2.348, 1.032, 2.419, 0.98, 2.348, 0.934, 2.419, 1.027]}, {"time": 2.5, "x": 0.98, "y": 1.027, "curve": [2.581, 0.98, 2.652, 1.032, 2.581, 1.027, 2.652, 0.934]}, {"time": 2.7333, "x": 1.032, "y": 0.934, "curve": [2.815, 1.032, 2.885, 0.98, 2.815, 0.934, 2.885, 1.027]}, {"time": 2.9667, "x": 0.98, "y": 1.027, "curve": [3.007, 0.98, 3.076, 0.985, 3.007, 1.027, 3.076, 1.019]}, {"time": 3.1, "x": 0.991, "y": 1.007, "curve": [3.157, 1.006, 3.21, 1.032, 3.157, 0.98, 3.21, 0.934]}, {"time": 3.2667, "x": 1.032, "y": 0.934, "curve": [3.348, 1.032, 3.452, 0.98, 3.348, 0.934, 3.452, 1.027]}, {"time": 3.5333, "x": 0.98, "y": 1.027, "curve": [3.574, 0.98, 3.593, 0.993, 3.574, 1.027, 3.593, 1.004]}, {"time": 3.6333, "x": 1.006, "y": 0.981, "curve": [3.674, 1.019, 3.726, 1.032, 3.674, 0.957, 3.726, 0.934]}, {"time": 3.7667, "x": 1.032, "y": 0.934, "curve": [3.79, 1.032, 3.811, 1.028, 3.79, 0.934, 3.811, 0.941]}, {"time": 3.8333, "x": 1.022, "y": 0.952, "curve": [3.889, 1.007, 3.942, 0.98, 3.889, 0.979, 3.942, 1.027]}, {"time": 4, "x": 0.98, "y": 1.027}]}, "left hand": {"rotate": [{"value": 91.4, "curve": [0.14, 91.4, 0.324, 91.48]}, {"time": 0.5, "value": 91.57, "curve": [0.809, 91.73, 1.097, 91.91]}, {"time": 1.1, "value": 91.7, "curve": [1.124, 90.15, 1.754, 92.47]}, {"time": 1.7667, "value": 91.7, "curve": [1.77, 91.5, 1.782, 96.97]}, {"time": 1.8, "value": 103.49, "curve": [1.835, 116.14, 1.995, 3.04]}, {"time": 2, "value": 2.69, "curve": [2.025, 1.13, 3.761, 3.04]}, {"time": 3.7667, "value": 2.69, "curve": [3.792, 1.13, 3.932, 91.4]}, {"time": 4, "value": 91.4}]}, "Mouth": {"scale": [{"x": 1.401}, {"time": 0.2667}, {"time": 0.5, "x": 1.401}, {"time": 0.7333}, {"time": 0.9667, "x": 1.401}, {"time": 1.1, "x": 1.281}, {"time": 1.2667, "curve": "stepped"}, {"time": 1.7667}, {"time": 2, "x": 1.401}, {"time": 2.2667}, {"time": 2.5, "x": 1.401}, {"time": 2.7333}, {"time": 2.9667, "x": 1.401}, {"time": 3.1, "x": 1.281}, {"time": 3.2667, "curve": "stepped"}, {"time": 3.7667}, {"time": 3.8333, "x": 1.115}, {"time": 4, "x": 1.401}]}, "cat body copy": {"rotate": [{"value": 6.56, "curve": "stepped"}, {"time": 1.6667, "value": 6.56}, {"time": 2, "value": -11.46, "curve": "stepped"}, {"time": 3.7667, "value": -11.46}, {"time": 4, "value": 6.56}], "translate": [{"x": -0.17, "y": -7.57, "curve": [0.011, -0.18, 0.022, -0.19, 0.011, -8.18, 0.022, -8.62]}, {"time": 0.0333, "x": -0.19, "y": -8.62, "curve": [0.106, -0.19, 0.259, 0, 0.106, -8.62, 0.259, 0]}, {"time": 0.2667, "curve": [0.338, 0, 0.425, -0.14, 0.338, 0, 0.425, -6.48]}, {"time": 0.5, "x": -0.18, "y": -8.17, "curve": [0.512, -0.18, 0.523, -0.19, 0.512, -8.43, 0.523, -8.62]}, {"time": 0.5333, "x": -0.19, "y": -8.62, "curve": [0.607, -0.19, 0.66, -0.04, 0.607, -8.62, 0.66, -1.64]}, {"time": 0.7333, "y": -0.24, "curve": [0.741, 0, 0.758, 0, 0.741, -0.09, 0.758, 0]}, {"time": 0.7667, "curve": [0.848, 0, 0.919, -0.55, 0.848, 0, 0.919, -8.68]}, {"time": 1, "x": -0.55, "y": -8.68, "curve": [1.025, -0.55, 1.084, -0.18, 1.034, -8.68, 1.084, -8.26]}, {"time": 1.1, "x": -0.17, "y": -7.71, "curve": [1.157, -0.12, 1.21, -0.03, 1.157, -5.77, 1.21, -1.33]}, {"time": 1.2667, "y": -0.24, "curve": [1.275, 0, 1.292, 0, 1.275, -0.08, 1.292, 0]}, {"time": 1.3, "curve": [1.381, 0, 1.419, -0.55, 1.381, 0, 1.419, -8.68]}, {"time": 1.5, "x": -0.55, "y": -8.68, "curve": [1.525, -0.55, 1.584, -0.18, 1.534, -8.68, 1.584, -8.26]}, {"time": 1.6, "x": -0.17, "y": -7.71, "curve": [1.657, -0.12, 1.795, 0, 1.657, -5.77, 1.737, 9.25]}, {"time": 1.8, "y": 9.71, "curve": [1.881, 0, 1.952, -0.19, 1.881, 10.3, 1.952, -8.62]}, {"time": 2.0333, "x": -0.19, "y": -8.62, "curve": [2.106, -0.19, 2.259, 0, 2.106, -8.62, 2.259, 0]}, {"time": 2.2667, "curve": [2.348, 0, 2.452, -0.19, 2.348, 0, 2.452, -8.62]}, {"time": 2.5333, "x": -0.19, "y": -8.62, "curve": [2.607, -0.19, 2.66, -0.04, 2.607, -8.62, 2.66, -1.64]}, {"time": 2.7333, "y": -0.24, "curve": [2.741, 0, 2.758, 0, 2.741, -0.09, 2.758, 0]}, {"time": 2.7667, "curve": [2.848, 0, 2.919, -0.55, 2.848, 0, 2.919, -8.68]}, {"time": 3, "x": -0.55, "y": -8.68, "curve": [3.025, -0.55, 3.084, -0.18, 3.034, -8.68, 3.084, -8.26]}, {"time": 3.1, "x": -0.17, "y": -7.71, "curve": [3.157, -0.12, 3.21, -0.03, 3.157, -5.77, 3.21, -1.33]}, {"time": 3.2667, "y": -0.24, "curve": [3.275, 0, 3.292, 0, 3.275, -0.08, 3.292, 0]}, {"time": 3.3, "curve": [3.381, 0, 3.452, -0.55, 3.381, 0, 3.452, -25.08]}, {"time": 3.5333, "x": -0.55, "y": -25.08, "curve": [3.559, -0.55, 3.597, -0.48, 3.568, -25.08, 3.6, -17.79]}, {"time": 3.6333, "x": -0.39, "y": -9.82, "curve": [3.703, -0.23, 3.795, 0, 3.673, -0.71, 3.737, 9.25]}, {"time": 3.8, "y": 9.71, "curve": [3.811, 0, 3.822, 0, 3.811, 9.79, 3.822, 9.4]}, {"time": 3.8333, "x": -0.01, "y": 8.82, "curve": [3.89, -0.04, 3.943, -0.14, 3.89, 5.84, 3.943, -4.35]}, {"time": 4, "x": -0.17, "y": -7.57}]}, "head": {"rotate": [{"value": 9.72}, {"time": 0.2667, "value": 15.48}, {"time": 1.1, "value": 8.59}, {"time": 1.6667, "value": 7.37}, {"time": 1.7667, "value": 11.55}, {"time": 2, "value": -10.6}, {"time": 2.1333, "value": -22.1}, {"time": 3.6333, "value": -10.6}, {"time": 3.8, "value": -15.63}, {"time": 4, "value": 9.72}], "translate": [{"x": -4.14, "curve": [0.081, -4.14, 0.185, 0, 0.081, 0, 0.185, 0]}, {"time": 0.2667, "curve": [0.348, 0, 0.419, -4.14, 0.348, 0, 0.419, 0]}, {"time": 0.5, "x": -4.14, "curve": [0.581, -4.14, 0.652, 0, 0.581, 0, 0.652, 0]}, {"time": 0.7333, "curve": [0.815, 0, 0.885, -4.14, 0.815, 0, 0.885, 0]}, {"time": 0.9667, "x": -4.14, "curve": [1.007, -4.14, 1.076, -3.77, 1.007, 0, 1.076, 0]}, {"time": 1.1, "x": -3.25, "curve": [1.157, -2.03, 1.21, 0, 1.157, 0, 1.21, 0]}, {"time": 1.2667, "curve": [1.348, 0, 1.452, -4.14, 1.348, 0, 1.452, 0]}, {"time": 1.5333, "x": -4.14, "curve": [1.574, -4.14, 1.594, -3.06, 1.574, 0, 1.594, 0]}, {"time": 1.6333, "x": -2.07, "curve": [1.674, -1.04, 1.726, 0, 1.674, 0, 1.726, 0]}, {"time": 1.7667, "curve": [1.848, 0, 1.919, -4.14, 1.848, 0, 1.919, 0]}, {"time": 2, "x": -4.14, "curve": [2.081, -4.14, 2.185, 0, 2.081, 0, 2.185, 0]}, {"time": 2.2667, "curve": [2.348, 0, 2.419, -4.14, 2.348, 0, 2.419, 0]}, {"time": 2.5, "x": -4.14, "curve": [2.581, -4.14, 2.652, 0, 2.581, 0, 2.652, 0]}, {"time": 2.7333, "curve": [2.815, 0, 2.885, -4.14, 2.815, 0, 2.885, 0]}, {"time": 2.9667, "x": -4.14, "curve": [3.007, -4.14, 3.21, 0, 3.007, 0, 3.21, 0]}, {"time": 3.2667, "curve": [3.348, 0, 3.452, -4.14, 3.348, 0, 3.452, 0]}, {"time": 3.5333, "x": -4.14, "curve": [3.574, -4.14, 3.726, 0, 3.574, 0, 3.726, 0]}, {"time": 3.7667, "curve": [3.79, 0, 3.811, -0.33, 3.79, 0, 3.811, 0]}, {"time": 3.8333, "x": -0.81, "curve": [3.889, -2.01, 3.942, -4.14, 3.889, 0, 3.942, 0]}, {"time": 4, "x": -4.14}]}, "left hand 2": {"rotate": [{"value": 67.18, "curve": [0.026, 71.9, 0.052, 76.82]}, {"time": 0.0667, "value": 77, "curve": [0.116, 77.59, 0.199, 58.39]}, {"time": 0.2667, "value": 58.41, "curve": [0.302, 58.42, 0.404, 76.65]}, {"time": 0.4333, "value": 77, "curve": [0.461, 77.33, 0.478, 71.43]}, {"time": 0.5, "value": 66.18, "curve": [0.517, 62.11, 0.537, 58.4]}, {"time": 0.5667, "value": 58.41, "curve": [0.602, 58.42, 0.704, 76.65]}, {"time": 0.7333, "value": 77, "curve": [0.782, 77.59, 0.832, 58.39]}, {"time": 0.9, "value": 58.41, "curve": [0.936, 58.42, 1.038, 76.65]}, {"time": 1.0667, "value": 77, "curve": [1.094, 77.33, 1.111, 71.43]}, {"time": 1.1333, "value": 66.18, "curve": [1.15, 62.11, 1.17, 58.4]}, {"time": 1.2, "value": 58.41, "curve": [1.235, 58.42, 1.337, 76.65]}, {"time": 1.3667, "value": 77, "curve": [1.416, 77.59, 1.466, 58.39]}, {"time": 1.5333, "value": 58.41, "curve": [1.557, 58.42, 1.691, 58.41]}, {"time": 1.8, "value": 58.4, "curve": [1.859, 58.4, 1.945, 0]}, {"time": 2, "curve": "stepped"}, {"time": 3.7667, "curve": [3.848, 0, 3.976, 62.71]}, {"time": 4, "value": 67.18}], "translate": [{"x": -16.12, "y": 6.15, "curve": "stepped"}, {"time": 1.8, "x": -16.12, "y": 6.15}, {"time": 2, "x": 1.83, "y": -0.97, "curve": "stepped"}, {"time": 3.7667, "x": 1.83, "y": -0.97}, {"time": 4, "x": -16.12, "y": 6.15}]}, "right hand 2": {"rotate": [{"value": -9.45, "curve": [0.081, -9.45, 0.185, 0]}, {"time": 0.2667, "curve": [0.348, 0, 0.419, -9.45]}, {"time": 0.5, "value": -9.45, "curve": [0.581, -9.45, 0.652, 0]}, {"time": 0.7333, "curve": [0.815, 0, 0.885, -9.45]}, {"time": 0.9667, "value": -9.45, "curve": [1.007, -9.45, 1.076, -8.6]}, {"time": 1.1, "value": -7.41, "curve": [1.157, -4.63, 1.21, 0]}, {"time": 1.2667, "curve": [1.348, 0, 1.452, -9.45]}, {"time": 1.5333, "value": -9.45, "curve": [1.574, -9.45, 1.594, -6.98]}, {"time": 1.6333, "value": -4.73, "curve": [1.674, -2.36, 1.726, 0]}, {"time": 1.7667, "curve": [1.848, 0, 1.919, -80.82]}, {"time": 2, "value": -80.82, "curve": [2.014, -80.82, 2.076, -96.37]}, {"time": 2.1667, "value": -96.37, "curve": [2.215, -96.37, 2.252, -80.82]}, {"time": 2.3333, "value": -80.82, "curve": [2.347, -80.82, 2.409, -96.37]}, {"time": 2.5, "value": -96.37, "curve": [2.549, -96.37, 2.585, -80.82]}, {"time": 2.6667, "value": -80.82, "curve": [2.681, -80.82, 2.742, -96.37]}, {"time": 2.8333, "value": -96.37, "curve": [2.882, -96.37, 2.919, -80.82]}, {"time": 3, "value": -80.82, "curve": [3.014, -80.82, 3.076, -96.37]}, {"time": 3.1667, "value": -96.37, "curve": [3.215, -96.37, 3.252, -80.82]}, {"time": 3.3333, "value": -80.82, "curve": [3.347, -80.82, 3.409, -96.37]}, {"time": 3.5, "value": -96.37, "curve": [3.549, -96.37, 3.605, -80.82]}, {"time": 3.6667, "value": -80.82, "curve": "stepped"}, {"time": 3.8333, "value": -80.82, "curve": [3.915, -80.82, 3.942, -9.45]}, {"time": 4, "value": -9.45}]}, "Layer 129 copy": {"rotate": [{"value": -9.13, "curve": [0.023, -10.2, 0.046, -10.94]}, {"time": 0.0667, "value": -10.94, "curve": [0.132, -10.94, 0.201, -3.77]}, {"time": 0.2667, "value": -1.14, "curve": [0.283, -0.46, 0.284, 0]}, {"time": 0.3, "curve": [0.37, 0, 0.432, -8.08]}, {"time": 0.5, "value": -10.31, "curve": [0.511, -10.66, 0.522, -10.94]}, {"time": 0.5333, "value": -10.94, "curve": [0.599, -10.94, 0.668, -3.77]}, {"time": 0.7333, "value": -1.14, "curve": [0.75, -0.46, 0.784, 0]}, {"time": 0.8, "curve": [0.881, 0, 0.952, -10.94]}, {"time": 1.0333, "value": -10.94, "curve": [1.058, -10.94, 1.092, -10.81]}, {"time": 1.1, "value": -10.6, "curve": [1.158, -9.18, 1.21, -3.44]}, {"time": 1.2667, "value": -1.14, "curve": [1.283, -0.46, 1.317, 0]}, {"time": 1.3333, "curve": [1.415, 0, 1.485, -10.94]}, {"time": 1.5667, "value": -10.94, "curve": [1.591, -10.94, 1.611, -9.78]}, {"time": 1.6333, "value": -8.52, "curve": [1.674, -6.19, 1.726, 10.5]}, {"time": 1.7667, "value": 12.14, "curve": [1.784, 12.82, 1.784, 10.38]}, {"time": 1.8, "value": 10.38, "curve": [1.881, 10.38, 1.986, -10.94]}, {"time": 2.0667, "value": -10.94, "curve": [2.132, -10.94, 2.201, -17.54]}, {"time": 2.2667, "value": -14.91, "curve": [2.283, -14.23, 2.284, -10.22]}, {"time": 2.3, "value": -10.22, "curve": [2.381, -10.22, 2.452, -10.94]}, {"time": 2.5333, "value": -10.94, "curve": [2.599, -10.94, 2.668, -3.77]}, {"time": 2.7333, "value": -1.14, "curve": [2.75, -0.46, 2.784, 0]}, {"time": 2.8, "curve": [2.881, 0, 2.952, -10.94]}, {"time": 3.0333, "value": -10.94, "curve": [3.058, -10.94, 3.092, -10.81]}, {"time": 3.1, "value": -10.6, "curve": [3.158, -9.18, 3.21, -3.44]}, {"time": 3.2667, "value": -1.14, "curve": [3.283, -0.46, 3.317, 0]}, {"time": 3.3333, "curve": [3.415, 0, 3.485, -10.94]}, {"time": 3.5667, "value": -10.94, "curve": [3.591, -10.94, 3.609, -9.93]}, {"time": 3.6333, "value": -8.52, "curve": [3.674, -6.19, 3.726, -2.78]}, {"time": 3.7667, "value": -1.14, "curve": [3.784, -0.46, 3.784, 0]}, {"time": 3.8, "curve": [3.811, 0, 3.822, -0.25]}, {"time": 3.8333, "value": -0.57, "curve": [3.886, -2.11, 3.945, -6.6]}, {"time": 4, "value": -9.13}]}, "Layer 129 copy2": {"rotate": [{"value": -6.66, "curve": [0.033, -8.92, 0.066, -10.94]}, {"time": 0.1, "value": -10.94, "curve": [0.149, -10.94, 0.218, -6.91]}, {"time": 0.2667, "value": -3.85, "curve": [0.3, -1.77, 0.301, 0]}, {"time": 0.3333, "curve": [0.384, 0, 0.443, -4.19]}, {"time": 0.5, "value": -7.39, "curve": [0.535, -9.35, 0.569, -10.94]}, {"time": 0.6, "value": -10.94, "curve": [0.649, -10.94, 0.684, -6.91]}, {"time": 0.7333, "value": -3.85, "curve": [0.766, -1.77, 0.801, 0]}, {"time": 0.8333, "curve": [0.914, 0, 0.986, -10.94]}, {"time": 1.0667, "value": -10.94, "curve": [1.075, -10.94, 1.027, -8.86]}, {"time": 1.1, "value": -10.63, "curve": [1.108, -10.83, 1.125, -10.94]}, {"time": 1.1333, "value": -10.94, "curve": [1.182, -10.94, 1.218, -6.91]}, {"time": 1.2667, "value": -3.85, "curve": [1.3, -1.77, 1.334, 0]}, {"time": 1.3667, "curve": [1.448, 0, 1.628, -10.75]}, {"time": 1.6333, "value": -10.61, "curve": [1.674, -9.61, 1.726, -6.4]}, {"time": 1.7667, "value": -3.85, "curve": [1.8, -1.77, 1.834, 0]}, {"time": 1.8667, "curve": [1.948, 0, 2.019, -10.94]}, {"time": 2.1, "value": -10.94, "curve": [2.149, -10.94, 2.218, -6.91]}, {"time": 2.2667, "value": -3.85, "curve": [2.3, -1.77, 2.301, 0]}, {"time": 2.3333, "curve": [2.415, 0, 2.519, -10.94]}, {"time": 2.6, "value": -10.94, "curve": [2.649, -10.94, 2.684, -6.91]}, {"time": 2.7333, "value": -3.85, "curve": [2.766, -1.77, 2.801, 0]}, {"time": 2.8333, "curve": [2.914, 0, 2.986, -10.94]}, {"time": 3.0667, "value": -10.94, "curve": [3.075, -10.94, 3.027, -8.86]}, {"time": 3.1, "value": -10.63, "curve": [3.108, -10.83, 3.125, -10.94]}, {"time": 3.1333, "value": -10.94, "curve": [3.182, -10.94, 3.218, -6.91]}, {"time": 3.2667, "value": -3.85, "curve": [3.3, -1.77, 3.334, 0]}, {"time": 3.3667, "curve": [3.448, 0, 3.625, -10.81]}, {"time": 3.6333, "value": -10.61, "curve": [3.674, -9.61, 3.726, -6.4]}, {"time": 3.7667, "value": -3.85, "curve": [3.789, -2.47, 3.811, -1.23]}, {"time": 3.8333, "value": -0.56, "curve": [3.845, -0.21, 3.856, 0]}, {"time": 3.8667, "curve": [3.914, 0, 3.957, -3.58]}, {"time": 4, "value": -6.66}]}, "Layer 129": {"rotate": [{"value": 10.39}, {"time": 0.0667, "value": 13.85}, {"time": 0.2667, "value": 2.77}, {"time": 0.3}, {"time": 0.5, "value": 11.87}, {"time": 0.5333, "value": 13.85}, {"time": 0.7333, "value": 2.77}, {"time": 0.8}, {"time": 1.0333, "value": 13.85}, {"time": 1.1, "value": 12.47}, {"time": 1.2667, "value": 2.77}, {"time": 1.3333}, {"time": 1.5667, "value": 13.85}, {"time": 1.6333, "value": 9.7}, {"time": 1.7667, "value": 16.05}, {"time": 1.8, "value": 10.38}, {"time": 2.0667, "value": 13.85}, {"time": 2.2667, "value": -11}, {"time": 2.3, "value": -10.22}, {"time": 2.5333, "value": 13.85}, {"time": 2.7333, "value": 2.77}, {"time": 2.8}, {"time": 3.0333, "value": 13.85}, {"time": 3.1, "value": 12.47}, {"time": 3.2667, "value": 2.77}, {"time": 3.3333}, {"time": 3.5667, "value": 13.85}, {"time": 3.6333, "value": 9.7}, {"time": 3.7667, "value": 2.77}, {"time": 3.8}, {"time": 3.8333, "value": 1.73}, {"time": 4, "value": 10.39}]}, "Layer 130": {"rotate": [{"value": 7.91}, {"time": 0.1, "value": 13.85}, {"time": 0.2667, "value": 5.54}, {"time": 0.3333}, {"time": 0.5, "value": 8.66}, {"time": 0.6, "value": 13.85}, {"time": 0.7333, "value": 5.54}, {"time": 0.8333}, {"time": 1.0667, "value": 13.85}, {"time": 1.1, "value": 12.47}, {"time": 1.1333, "value": 13.85}, {"time": 1.2667, "value": 5.54}, {"time": 1.3667}, {"time": 1.6333, "value": 12.47}, {"time": 1.7667, "value": 5.54}, {"time": 1.8667}, {"time": 2.1, "value": 13.85}, {"time": 2.2667, "value": 5.54}, {"time": 2.3333}, {"time": 2.6, "value": 13.85}, {"time": 2.7333, "value": 5.54}, {"time": 2.8333}, {"time": 3.0667, "value": 13.85}, {"time": 3.1, "value": 12.47}, {"time": 3.1333, "value": 13.85}, {"time": 3.2667, "value": 5.54}, {"time": 3.3667}, {"time": 3.6333, "value": 12.47}, {"time": 3.7667, "value": 5.54}, {"time": 3.8333, "value": 1.85}, {"time": 3.8667}, {"time": 4, "value": 7.91}]}, "Bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}, "light_floor": {"scale": [{}, {"time": 0.3667, "x": 0.8, "y": 0.8}, {"time": 0.5}, {"time": 0.8667, "x": 0.8, "y": 0.8}, {"time": 1}, {"time": 1.3333, "x": 0.8, "y": 0.8}, {"time": 1.5}, {"time": 1.8333, "x": 0.8, "y": 0.8}, {"time": 2}, {"time": 2.3333, "x": 0.8, "y": 0.8}, {"time": 2.5}, {"time": 2.8333, "x": 0.8, "y": 0.8}, {"time": 3}, {"time": 3.3333, "x": 0.8, "y": 0.8}, {"time": 3.5}, {"time": 3.8667, "x": 0.8, "y": 0.8}, {"time": 4}]}, "right hand": {"rotate": [{"time": 1.1}, {"time": 1.8333, "value": -24.59}, {"time": 2, "value": -89.38, "curve": "stepped"}, {"time": 3.8333, "value": -89.38}, {"time": 4}]}, "cat  tail1": {"rotate": [{"curve": [0.111, 0, 0.222, -13.57]}, {"time": 0.3333, "value": -27.18, "curve": [0.444, -40.78, 0.556, -54.37]}, {"time": 0.6667, "value": -54.37, "curve": [0.888, -54.37, 1.282, 0]}, {"time": 1.3333, "curve": [1.444, 0, 1.556, -13.57]}, {"time": 1.6667, "value": -27.18, "curve": [1.778, -40.78, 1.889, -54.37]}, {"time": 2, "value": -54.37, "curve": [2.222, -54.37, 2.615, 0]}, {"time": 2.6667, "curve": [2.777, 0, 2.889, -13.57]}, {"time": 3, "value": -27.18, "curve": [3.111, -40.78, 3.223, -54.37]}, {"time": 3.3333, "value": -54.37, "curve": [3.555, -54.37, 3.778, 0]}, {"time": 4}]}, "cat  tail1b": {"rotate": [{"value": -3.38, "curve": [0.023, -2.38, 0.045, -1.46]}, {"time": 0.0667, "value": -0.9, "curve": [0.091, -0.36, 0.145, 0]}, {"time": 0.1667, "curve": [0.233, 0, 0.268, 9.56]}, {"time": 0.3333, "value": 14.32, "curve": [0.37, 17, 0.43, 18.32]}, {"time": 0.4667, "value": 15.65, "curve": [0.585, 7.08, 0.715, -32.27]}, {"time": 0.8333, "value": -32.27, "curve": [1.012, -32.27, 1.282, -5.65]}, {"time": 1.3333, "value": -3.38, "curve": [1.356, -2.38, 1.378, -1.46]}, {"time": 1.4, "value": -0.9, "curve": [1.424, -0.36, 1.479, 0]}, {"time": 1.5, "curve": [1.567, 0, 1.601, 9.56]}, {"time": 1.6667, "value": 14.32, "curve": [1.704, 17, 1.763, 18.32]}, {"time": 1.8, "value": 15.65, "curve": [1.918, 7.08, 2.048, -32.27]}, {"time": 2.1667, "value": -32.27, "curve": [2.345, -32.27, 2.615, -5.65]}, {"time": 2.6667, "value": -3.38, "curve": [2.689, -2.38, 2.711, -1.46]}, {"time": 2.7333, "value": -0.9, "curve": [2.757, -0.36, 2.812, 0]}, {"time": 2.8333, "curve": [2.9, 0, 2.934, 9.56]}, {"time": 3, "value": 14.32, "curve": [3.037, 17, 3.096, 18.32]}, {"time": 3.1333, "value": 15.65, "curve": [3.251, 7.08, 3.382, -32.27]}, {"time": 3.5, "value": -32.27, "curve": [3.678, -32.27, 3.822, -11.2]}, {"time": 4, "value": -3.38}]}, "cat  tail1c": {"rotate": [{"value": -11.41, "curve": [0.045, -8.32, 0.123, -5.4]}, {"time": 0.1667, "value": -3.36, "curve": [0.213, -1.35, 0.223, 11.9]}, {"time": 0.2667, "value": 11.9, "curve": [0.289, 11.9, 0.313, 12.41]}, {"time": 0.3333, "value": 12.89, "curve": [0.4, 14.48, 0.467, 16.84]}, {"time": 0.5333, "value": 10.51, "curve": [0.607, 3.43, 0.693, -14.25]}, {"time": 0.7667, "value": -24.91, "curve": [0.826, -33.4, 0.874, -42.88]}, {"time": 0.9333, "value": -42.88, "curve": [1.067, -42.88, 1.282, -14.95]}, {"time": 1.3333, "value": -11.41, "curve": [1.379, -8.32, 1.456, -5.4]}, {"time": 1.5, "value": -3.36, "curve": [1.546, -1.35, 1.556, 11.9]}, {"time": 1.6, "value": 11.9, "curve": [1.622, 11.9, 1.646, 12.41]}, {"time": 1.6667, "value": 12.89, "curve": [1.734, 14.48, 1.801, 16.84]}, {"time": 1.8667, "value": 10.51, "curve": [1.941, 3.43, 2.027, -14.25]}, {"time": 2.1, "value": -24.91, "curve": [2.16, -33.4, 2.207, -42.88]}, {"time": 2.2667, "value": -42.88, "curve": [2.4, -42.88, 2.615, -14.95]}, {"time": 2.6667, "value": -11.41, "curve": [2.712, -8.32, 2.789, -5.4]}, {"time": 2.8333, "value": -3.36, "curve": [2.879, -1.35, 2.889, 11.9]}, {"time": 2.9333, "value": 11.9, "curve": [2.956, 11.9, 2.98, 12.41]}, {"time": 3, "value": 12.89, "curve": [3.067, 14.48, 3.134, 16.84]}, {"time": 3.2, "value": 10.51, "curve": [3.274, 3.43, 3.36, -14.25]}, {"time": 3.4333, "value": -24.91, "curve": [3.493, -33.4, 3.541, -42.88]}, {"time": 3.6, "value": -42.88, "curve": [3.734, -42.88, 3.867, -20.57]}, {"time": 4, "value": -11.41}]}, "cat  tail1d": {"rotate": [{"value": -20.94, "curve": [0.067, -16.28, 0.134, 4.11]}, {"time": 0.2, "value": 8.18, "curve": [0.245, 10.85, 0.288, 18.43]}, {"time": 0.3333, "value": 23.3, "curve": [0.356, 25.72, 0.378, 27.53]}, {"time": 0.4, "value": 27.53, "curve": [0.488, 27.53, 0.578, 20.17]}, {"time": 0.6667, "value": 11.03, "curve": [0.741, 3.41, 0.827, -22.97]}, {"time": 0.9, "value": -32.61, "curve": [0.96, -40.28, 1.007, -32.27]}, {"time": 1.0667, "value": -32.27, "curve": [1.156, -32.27, 1.282, -24.54]}, {"time": 1.3333, "value": -20.94, "curve": [1.401, -16.28, 1.467, 4.11]}, {"time": 1.5333, "value": 8.18, "curve": [1.579, 10.85, 1.621, 18.43]}, {"time": 1.6667, "value": 23.3, "curve": [1.689, 25.72, 1.711, 27.53]}, {"time": 1.7333, "value": 27.53, "curve": [1.822, 27.53, 1.912, 20.17]}, {"time": 2, "value": 11.03, "curve": [2.074, 3.41, 2.16, -22.97]}, {"time": 2.2333, "value": -32.61, "curve": [2.293, -40.28, 2.341, -32.27]}, {"time": 2.4, "value": -32.27, "curve": [2.489, -32.27, 2.615, -24.54]}, {"time": 2.6667, "value": -20.94, "curve": [2.734, -16.28, 2.801, 4.11]}, {"time": 2.8667, "value": 8.18, "curve": [2.912, 10.85, 2.955, 18.43]}, {"time": 3, "value": 23.3, "curve": [3.023, 25.72, 3.045, 27.53]}, {"time": 3.0667, "value": 27.53, "curve": [3.155, 27.53, 3.245, 20.17]}, {"time": 3.3333, "value": 11.03, "curve": [3.407, 3.41, 3.493, -22.97]}, {"time": 3.5667, "value": -32.61, "curve": [3.626, -40.28, 3.674, -32.27]}, {"time": 3.7333, "value": -32.27, "curve": [3.822, -32.27, 3.912, -27.11]}, {"time": 4, "value": -20.94}]}, "cat  tail1e": {"rotate": [{"value": -28.9, "curve": [0.09, -24.78, 0.179, 3.09]}, {"time": 0.2667, "value": 9.31, "curve": [0.289, 10.84, 0.313, 13.86]}, {"time": 0.3333, "value": 16.77, "curve": [0.401, 26.52, 0.467, 40.93]}, {"time": 0.5333, "value": 40.93, "curve": [0.63, 40.93, 0.739, 35.57]}, {"time": 0.8333, "value": 14.66, "curve": [0.916, -2.91, 0.986, -35.66]}, {"time": 1.0667, "value": -44.15, "curve": [1.113, -48.7, 1.156, -32.27]}, {"time": 1.2, "value": -32.27, "curve": [1.245, -32.27, 1.289, -30.94]}, {"time": 1.3333, "value": -28.9, "curve": [1.423, -24.78, 1.512, 3.09]}, {"time": 1.6, "value": 9.31, "curve": [1.623, 10.84, 1.646, 13.86]}, {"time": 1.6667, "value": 16.77, "curve": [1.735, 26.52, 1.801, 40.93]}, {"time": 1.8667, "value": 40.93, "curve": [1.963, 40.93, 2.072, 35.57]}, {"time": 2.1667, "value": 14.66, "curve": [2.25, -2.91, 2.32, -35.66]}, {"time": 2.4, "value": -44.15, "curve": [2.446, -48.7, 2.489, -32.27]}, {"time": 2.5333, "value": -32.27, "curve": [2.578, -32.27, 2.622, -30.94]}, {"time": 2.6667, "value": -28.9, "curve": [2.756, -24.78, 2.846, 3.09]}, {"time": 2.9333, "value": 9.31, "curve": [2.956, 10.84, 2.98, 13.86]}, {"time": 3, "value": 16.77, "curve": [3.068, 26.52, 3.134, 40.93]}, {"time": 3.2, "value": 40.93, "curve": [3.296, 40.93, 3.405, 35.57]}, {"time": 3.5, "value": 14.66, "curve": [3.583, -2.91, 3.653, -35.66]}, {"time": 3.7333, "value": -44.15, "curve": [3.779, -48.7, 3.823, -32.27]}, {"time": 3.8667, "value": -32.27, "curve": [3.911, -32.27, 3.955, -30.94]}, {"time": 4, "value": -28.9}]}}}, "cat_action3": {"slots": {"bg/Pumkin1_light": {"rgba": [{"color": "ffffff00"}]}, "eye 2": {"attachment": [{"time": 0.7333, "name": "eye 2"}, {"time": 0.8333, "name": "right  eye"}, {"time": 2.2333, "name": "eye 2"}, {"time": 2.3333, "name": "right  eye"}, {"time": 3, "name": "eye-3"}, {"time": 3.6667, "name": "right  eye"}]}, "glow": {"rgba": [{"color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}, {"time": 3.3667, "color": "ffffff00"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.8667, "color": "ffffff00"}, {"time": 4, "color": "ffffffff"}]}, "glow2": {"rgba": [{"color": "ffffffff", "curve": [0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 0.62, 0.304, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 0.62, 0.675, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 0.62, 1.038, 1]}, {"time": 1.1, "color": "ffffffff", "curve": [1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 0.62]}, {"time": 1.3, "color": "ffffff9f", "curve": [1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 0.62, 1.441, 1]}, {"time": 1.5, "color": "ffffffff", "curve": [1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 0.62]}, {"time": 1.7, "color": "ffffff9f", "curve": [1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 0.62, 1.804, 1]}, {"time": 1.8667, "color": "ffffffff", "curve": [1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 0.62]}, {"time": 2.0667, "color": "ffffff9f", "curve": [2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 0.62, 2.175, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": [2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 0.62]}, {"time": 2.4333, "color": "ffffff9f", "curve": [2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 0.62, 2.538, 1]}, {"time": 2.6, "color": "ffffffff", "curve": [2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 0.62]}, {"time": 2.8, "color": "ffffff9f", "curve": [2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 0.62, 2.942, 1]}, {"time": 3, "color": "ffffffff", "curve": [3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 0.62]}, {"time": 3.0667, "color": "ffffff9f", "curve": [3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 0.62, 3.175, 1]}, {"time": 3.2333, "color": "ffffffff", "curve": [3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 0.62]}, {"time": 3.4333, "color": "ffffff9f", "curve": [3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 0.62, 3.538, 1]}, {"time": 3.6, "color": "ffffffff", "curve": [3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 0.74]}, {"time": 3.7667, "color": "ffffffa6", "curve": [3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 0.64, 3.789, 0.62]}, {"time": 3.8, "color": "ffffff9f", "curve": [3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 0.62, 3.942, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow3": {"rgba": [{"color": "ffffffff", "curve": [0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 0.62, 0.304, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 0.62, 0.675, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 0.62, 1.038, 1]}, {"time": 1.1, "color": "ffffffff", "curve": [1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 0.62]}, {"time": 1.3, "color": "ffffff9f", "curve": [1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 0.62, 1.441, 1]}, {"time": 1.5, "color": "ffffffff", "curve": [1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 0.62]}, {"time": 1.7, "color": "ffffff9f", "curve": [1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 0.62, 1.804, 1]}, {"time": 1.8667, "color": "ffffffff", "curve": [1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 0.62]}, {"time": 2.0667, "color": "ffffff9f", "curve": [2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 0.62, 2.175, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": [2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 0.62]}, {"time": 2.4333, "color": "ffffff9f", "curve": [2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 0.62, 2.538, 1]}, {"time": 2.6, "color": "ffffffff", "curve": [2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 0.62]}, {"time": 2.8, "color": "ffffff9f", "curve": [2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 0.62, 2.942, 1]}, {"time": 3, "color": "ffffffff", "curve": [3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 0.62]}, {"time": 3.0667, "color": "ffffff9f", "curve": [3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 0.62, 3.175, 1]}, {"time": 3.2333, "color": "ffffffff", "curve": [3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 0.62]}, {"time": 3.4333, "color": "ffffff9f", "curve": [3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 0.62, 3.538, 1]}, {"time": 3.6, "color": "ffffffff", "curve": [3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 0.74]}, {"time": 3.7667, "color": "ffffffa6", "curve": [3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 0.64, 3.789, 0.62]}, {"time": 3.8, "color": "ffffff9f", "curve": [3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 0.62, 3.942, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow4": {"rgba": [{"color": "ffffffff", "curve": [0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 0.62, 0.304, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 0.62, 0.675, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 0.62, 1.038, 1]}, {"time": 1.1, "color": "ffffffff", "curve": [1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 0.62]}, {"time": 1.3, "color": "ffffff9f", "curve": [1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 0.62, 1.441, 1]}, {"time": 1.5, "color": "ffffffff", "curve": [1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 0.62]}, {"time": 1.7, "color": "ffffff9f", "curve": [1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 0.62, 1.804, 1]}, {"time": 1.8667, "color": "ffffffff", "curve": [1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 0.62]}, {"time": 2.0667, "color": "ffffff9f", "curve": [2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 0.62, 2.175, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": [2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 0.62]}, {"time": 2.4333, "color": "ffffff9f", "curve": [2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 0.62, 2.538, 1]}, {"time": 2.6, "color": "ffffffff", "curve": [2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 0.62]}, {"time": 2.8, "color": "ffffff9f", "curve": [2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 0.62, 2.942, 1]}, {"time": 3, "color": "ffffffff", "curve": [3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 0.62]}, {"time": 3.0667, "color": "ffffff9f", "curve": [3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 0.62, 3.175, 1]}, {"time": 3.2333, "color": "ffffffff", "curve": [3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 0.62]}, {"time": 3.4333, "color": "ffffff9f", "curve": [3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 0.62, 3.538, 1]}, {"time": 3.6, "color": "ffffffff", "curve": [3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 0.74]}, {"time": 3.7667, "color": "ffffffa6", "curve": [3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 0.64, 3.789, 0.62]}, {"time": 3.8, "color": "ffffff9f", "curve": [3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 0.62, 3.942, 1]}, {"time": 4, "color": "ffffffff"}]}, "glow5": {"rgba": [{"color": "ffffffff", "curve": [0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 1, 0.062, 1, 0.137, 0.62]}, {"time": 0.2, "color": "ffffff9f", "curve": [0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 1, 0.304, 1, 0.263, 0.62, 0.304, 1]}, {"time": 0.3667, "color": "ffffffff", "curve": [0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 1, 0.429, 1, 0.504, 0.62]}, {"time": 0.5667, "color": "ffffff9f", "curve": [0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 1, 0.675, 1, 0.629, 0.62, 0.675, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 1, 0.796, 1, 0.871, 0.62]}, {"time": 0.9333, "color": "ffffff9f", "curve": [0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 1, 1.038, 1, 0.996, 0.62, 1.038, 1]}, {"time": 1.1, "color": "ffffffff", "curve": [1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 1, 1.162, 1, 1.237, 0.62]}, {"time": 1.3, "color": "ffffff9f", "curve": [1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 1, 1.441, 1, 1.363, 0.62, 1.441, 1]}, {"time": 1.5, "color": "ffffffff", "curve": [1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 1, 1.562, 1, 1.637, 0.62]}, {"time": 1.7, "color": "ffffff9f", "curve": [1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 1, 1.804, 1, 1.763, 0.62, 1.804, 1]}, {"time": 1.8667, "color": "ffffffff", "curve": [1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 1, 1.929, 1, 2.004, 0.62]}, {"time": 2.0667, "color": "ffffff9f", "curve": [2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 1, 2.175, 1, 2.129, 0.62, 2.175, 1]}, {"time": 2.2333, "color": "ffffffff", "curve": [2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 1, 2.296, 1, 2.371, 0.62]}, {"time": 2.4333, "color": "ffffff9f", "curve": [2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 1, 2.538, 1, 2.496, 0.62, 2.538, 1]}, {"time": 2.6, "color": "ffffffff", "curve": [2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 1, 2.662, 1, 2.737, 0.62]}, {"time": 2.8, "color": "ffffff9f", "curve": [2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 1, 2.942, 1, 2.863, 0.62, 2.942, 1]}, {"time": 3, "color": "ffffffff", "curve": [3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 1, 3.062, 1, 3.004, 0.62]}, {"time": 3.0667, "color": "ffffff9f", "curve": [3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 1, 3.175, 1, 3.129, 0.62, 3.175, 1]}, {"time": 3.2333, "color": "ffffffff", "curve": [3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 1, 3.296, 1, 3.371, 0.62]}, {"time": 3.4333, "color": "ffffff9f", "curve": [3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 1, 3.538, 1, 3.496, 0.62, 3.538, 1]}, {"time": 3.6, "color": "ffffffff", "curve": [3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 1, 3.652, 1, 3.712, 0.74]}, {"time": 3.7667, "color": "ffffffa6", "curve": [3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 1, 3.789, 1, 3.778, 0.64, 3.789, 0.62]}, {"time": 3.8, "color": "ffffff9f", "curve": [3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 1, 3.942, 1, 3.863, 0.62, 3.942, 1]}, {"time": 4, "color": "ffffffff"}]}, "Layer 130": {"rgba": [{"color": "ffffffff"}, {"time": 0.2333, "color": "e23c3c00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "e23c3c00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.2333, "color": "e23c3c00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.7333, "color": "e23c3c00"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "e23c3c00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7333, "color": "e23c3c00"}, {"time": 3, "color": "ffffffff"}, {"time": 3.2333, "color": "e23c3c00"}, {"time": 3.4333, "color": "ffffffff"}, {"time": 3.7333, "color": "e23c3c00"}, {"time": 4, "color": "ffffffff"}]}, "left eye": {"attachment": [{"time": 0.7333, "name": "eye 2 copy"}, {"time": 0.8333, "name": "left eye"}, {"time": 2.2333, "name": "eye 2 copy"}, {"time": 2.3333, "name": "left eye"}, {"time": 3, "name": "eye-3"}, {"time": 3.6667, "name": "left eye"}]}, "left hand": {"attachment": [{"name": "left hand"}, {"time": 3.1, "name": "right hand"}, {"time": 3.6, "name": "left hand"}]}, "mouth": {"attachment": [{"name": "mouth_open"}, {"time": 3, "name": "mouth_nomal"}, {"time": 3.6667, "name": "mouth_open"}]}, "right hand": {"attachment": [{"name": "left hand"}, {"time": 3.1, "name": "right hand"}, {"time": 3.6, "name": "left hand"}]}}, "bones": {"pumpkin": {"translate": [{"curve": [0.083, 0, 0.15, 0.28, 0.04, 16.57, 0.141, 38.95]}, {"time": 0.2333, "x": 0.28, "y": 38.71, "curve": [0.316, 0.28, 0.417, 0, 0.342, 38.42, 0.441, 18.18]}, {"time": 0.5, "curve": [0.584, 0, 0.65, 0.28, 0.551, 16.06, 0.64, 38.95]}, {"time": 0.7333, "x": 0.28, "y": 38.71, "curve": [0.817, 0.28, 0.916, 0, 0.841, 38.42, 0.931, 21.31]}, {"time": 1, "curve": [1.083, 0, 1.15, 0.28, 1.061, 20.35, 1.141, 38.95]}, {"time": 1.2333, "x": 0.28, "y": 38.71, "curve": [1.317, 0.28, 1.417, 0, 1.342, 38.42, 1.445, 20.63]}, {"time": 1.5, "curve": [1.583, 0, 1.65, 0.28, 1.547, 19.49, 1.641, 38.95]}, {"time": 1.7333, "x": 0.28, "y": 38.71, "curve": [1.816, 0.28, 1.917, 0, 1.842, 38.42, 1.949, 18.06]}, {"time": 2, "curve": [2.084, 0, 2.15, 0.28, 2.039, 16.57, 2.14, 38.95]}, {"time": 2.2333, "x": 0.28, "y": 38.71, "curve": [2.317, 0.28, 2.417, 0, 2.341, 38.42, 2.441, 18.18]}, {"time": 2.5, "curve": [2.583, 0, 2.65, 0.28, 2.552, 16.06, 2.641, 38.95]}, {"time": 2.7333, "x": 0.28, "y": 38.71, "curve": [2.817, 0.28, 2.917, 0, 2.842, 38.42, 2.931, 21.31]}, {"time": 3, "curve": [3.084, 0, 3.15, 0, 3.001, -0.28, 3.15, 0]}, {"time": 3.2333, "curve": [3.3, 0, 3.367, 0, 3.3, 0, 3.367, -16.08]}, {"time": 3.4333, "y": -9.65, "curve": [3.45, 0, 3.483, 0, 3.45, -8.04, 3.483, -5.02]}, {"time": 3.5, "curve": [3.583, 0, 3.65, 0.28, 3.558, 17.33, 3.641, 38.95]}, {"time": 3.7333, "x": 0.28, "y": 38.71, "curve": [3.744, 0.28, 3.756, 0.28, 3.745, 38.68, 3.756, 38.42]}, {"time": 3.7667, "x": 0.27, "y": 37.99, "curve": [3.843, 0.21, 3.928, 0, 3.863, 34.23, 3.954, 16.19]}, {"time": 4}], "scale": [{"x": 0.98, "y": 1.027, "curve": [0.076, 1.025, 0.15, 1.032, 0.069, 0.96, 0.15, 0.934]}, {"time": 0.2333, "x": 1.032, "y": 0.934, "curve": [0.316, 1.032, 0.417, 1.019, 0.339, 0.934, 0.46, 1.002]}, {"time": 0.5, "x": 0.98, "y": 1.027, "curve": [0.582, 1.022, 0.65, 1.032, 0.562, 0.98, 0.606, 0.933]}, {"time": 0.7333, "x": 1.032, "y": 0.934, "curve": [0.817, 1.032, 0.899, 1.025, 0.817, 0.934, 0.937, 0.968]}, {"time": 1, "x": 0.98, "y": 1.027, "curve": [1.081, 1.02, 1.15, 1.032, 1.069, 0.96, 1.121, 0.932]}, {"time": 1.2333, "x": 1.032, "y": 0.934, "curve": [1.317, 1.032, 1.398, 1.021, 1.317, 0.936, 1.436, 0.967]}, {"time": 1.5, "x": 0.98, "y": 1.027, "curve": [1.573, 1.022, 1.65, 1.032, 1.562, 0.967, 1.65, 0.934]}, {"time": 1.7333, "x": 1.032, "y": 0.934, "curve": [1.816, 1.032, 1.899, 1.024, 1.839, 0.935, 1.939, 0.969]}, {"time": 2, "x": 0.98, "y": 1.027, "curve": [2.082, 1.02, 2.15, 1.032, 2.064, 0.966, 2.15, 0.934]}, {"time": 2.2333, "x": 1.032, "y": 0.934, "curve": [2.317, 1.032, 2.373, 1.033, 2.317, 0.934, 2.43, 0.951]}, {"time": 2.5, "x": 0.98, "y": 1.027, "curve": [2.572, 1.026, 2.65, 1.032, 2.561, 0.952, 2.65, 0.934]}, {"time": 2.7333, "x": 1.032, "y": 0.934, "curve": [2.817, 1.032, 2.873, 1.033, 2.817, 0.934, 2.93, 0.951]}, {"time": 3, "x": 0.98, "y": 1.027, "curve": [3.072, 1.026, 3.15, 1.034, 3.062, 0.952, 3.15, 0.932]}, {"time": 3.2333, "x": 1.032, "y": 0.934, "curve": [3.336, 1.03, 3.388, 1.021, 3.309, 0.936, 3.377, 0.957]}, {"time": 3.4333, "x": 1.003, "y": 0.991, "curve": [3.45, 0.997, 3.483, 0.989, 3.451, 1.002, 3.484, 1.014]}, {"time": 3.5, "x": 0.98, "y": 1.027, "curve": [3.573, 1.021, 3.65, 1.031, 3.542, 0.969, 3.596, 0.933]}, {"time": 3.7333, "x": 1.032, "y": 0.934, "curve": [3.745, 1.032, 3.756, 1.032, 3.745, 0.934, 3.756, 0.934]}, {"time": 3.7667, "x": 1.032, "y": 0.935, "curve": [3.868, 1.031, 3.926, 1.015, 3.861, 0.942, 3.948, 0.978]}, {"time": 4, "x": 0.98, "y": 1.027}]}, "left hand": {"rotate": [{"value": 96.37, "curve": "stepped"}, {"time": 3.0667, "value": 96.37, "curve": [3.092, 94.82, 3.159, 9.24]}, {"time": 3.2, "value": 6.15, "curve": [3.217, 4.93, 3.217, 3.71]}, {"time": 3.2333, "value": 2.69, "curve": [3.259, 1.13, 3.275, 0]}, {"time": 3.3, "curve": [3.325, 0, 3.38, 8.7]}, {"time": 3.4333, "value": 20.95, "curve": [3.559, 49.51, 3.721, 97.08]}, {"time": 3.7333, "value": 96.37}]}, "Mouth": {"scale": [{"x": 1.033, "curve": [0.044, 1.272, 0.088, 1.553, 0.044, 1, 0.088, 1]}, {"time": 0.1333, "x": 1.553, "curve": [0.222, 1.553, 0.311, 0.697, 0.222, 1, 0.311, 1]}, {"time": 0.4, "x": 0.697, "curve": [0.48, 0.697, 0.553, 1.553, 0.48, 1, 0.553, 1]}, {"time": 0.6333, "x": 1.553, "curve": [0.722, 1.553, 0.847, 0.697, 0.722, 1, 0.847, 1]}, {"time": 0.9, "x": 0.697, "curve": [0.98, 0.697, 1.054, 1.553, 0.98, 1, 1.054, 1]}, {"time": 1.1333, "x": 1.553, "curve": [1.222, 1.553, 1.312, 0.697, 1.222, 1, 1.312, 1]}, {"time": 1.4, "x": 0.697, "curve": [1.48, 0.697, 1.587, 1.553, 1.48, 1, 1.587, 1]}, {"time": 1.6667, "x": 1.553, "curve": [1.756, 1.553, 1.847, 0.697, 1.756, 1, 1.847, 1]}, {"time": 1.9, "x": 0.697, "curve": [1.98, 0.697, 2.087, 1.553, 1.98, 1, 2.087, 1]}, {"time": 2.1667, "x": 1.553, "curve": [2.255, 1.553, 2.344, 0.697, 2.255, 1, 2.344, 1]}, {"time": 2.4333, "x": 0.697, "curve": [2.513, 0.697, 2.587, 1.553, 2.513, 1, 2.587, 1]}, {"time": 2.6667, "x": 1.553, "curve": [2.755, 1.553, 2.88, 0.697, 2.755, 1, 2.88, 1]}, {"time": 2.9333, "x": 0.697, "curve": [2.951, 0.697, 2.333, 0.725, 2.951, 1, 2.333, 1]}, {"time": 3, "x": 0.725}, {"time": 3.2333, "curve": [3.301, 1, 3.368, 1.05, 3.301, 1, 3.368, 1]}, {"time": 3.4333, "x": 1.091, "curve": [3.534, 1.15, 3.634, 1.188, 3.534, 1, 3.634, 1]}, {"time": 3.7333, "curve": [3.744, 0.979, 3.755, 0.955, 3.744, 1, 3.755, 1]}, {"time": 3.7667, "x": 0.928, "curve": [3.811, 0.825, 3.858, 0.697, 3.811, 1, 3.858, 1]}, {"time": 3.9, "x": 0.697, "curve": [3.934, 0.697, 3.968, 0.853, 3.934, 1, 3.968, 1]}, {"time": 4, "x": 1.033}]}, "right hand": {"rotate": [{"value": -71.14, "curve": "stepped"}, {"time": 3.1, "value": -71.14}, {"time": 3.4333, "value": -28.46}, {"time": 3.7333, "value": -71.14}]}, "cat body copy": {"translate": [{"x": -0.19, "y": -8.62, "curve": [0.075, -0.19, 0.259, 0, 0.03, 8.25, 0.163, 30.12]}, {"time": 0.2667, "y": 30.03, "curve": [0.35, 0, 0.417, -0.19, 0.41, 29.92, 0.477, 3.8]}, {"time": 0.5, "x": -0.19, "y": -8.62, "curve": [0.575, -0.19, 0.758, 0, 0.53, 8.25, 0.663, 30.12]}, {"time": 0.7667, "y": 30.03, "curve": [0.85, 0, 0.917, -0.19, 0.91, 29.92, 0.977, 3.8]}, {"time": 1, "x": -0.19, "y": -8.62, "curve": [1.075, -0.19, 1.258, 0, 1.03, 8.25, 1.163, 30.12]}, {"time": 1.2667, "y": 30.03, "curve": [1.35, 0, 1.416, -0.19, 1.41, 29.92, 1.477, 3.8]}, {"time": 1.5, "x": -0.19, "y": -8.62, "curve": [1.575, -0.19, 1.759, 0, 1.53, 8.25, 1.663, 30.12]}, {"time": 1.7667, "y": 30.03, "curve": [1.85, 0, 1.917, -0.19, 1.91, 29.92, 1.977, 3.8]}, {"time": 2, "x": -0.19, "y": -8.62, "curve": [2.075, -0.19, 2.258, 0, 2.03, 8.25, 2.163, 30.12]}, {"time": 2.2667, "y": 30.03, "curve": [2.35, 0, 2.417, -0.19, 2.41, 29.92, 2.477, 3.8]}, {"time": 2.5, "x": -0.19, "y": -8.62, "curve": [2.575, -0.19, 2.758, 0, 2.53, 8.25, 2.663, 30.12]}, {"time": 2.7667, "y": 30.03, "curve": [2.842, 0, 2.992, -0.19, 2.878, 29.95, 2.992, -5.55]}, {"time": 3, "x": -0.19, "y": -8.62, "curve": [3.075, -0.19, 3.158, -0.05, 3.075, -8.62, 3.158, -1.65]}, {"time": 3.2333, "x": -0.01, "y": -0.24, "curve": [3.242, 0, 3.258, 0, 3.242, -0.09, 3.258, 0]}, {"time": 3.2667, "curve": [3.325, 0, 3.375, -0.09, 3.325, 0, 3.375, -4.22]}, {"time": 3.4333, "x": -0.14, "y": -6.76, "curve": [3.458, -0.17, 3.475, -0.19, 3.458, -7.84, 3.475, -8.62]}, {"time": 3.5, "x": -0.19, "y": -8.62, "curve": [3.575, -0.19, 3.758, 0, 3.532, 5.59, 3.645, 30.19]}, {"time": 3.7667, "y": 30.34, "curve": [3.85, 0, 3.917, -0.19, 3.923, 30.53, 3.977, 3.8]}, {"time": 4, "x": -0.19, "y": -8.62}]}, "head": {"translate": [{"x": -4.14, "curve": [0.083, -4.14, 0.15, 0, 0.083, 0, 0.15, 0]}, {"time": 0.2333, "curve": [0.316, 0, 0.417, -4.14, 0.316, 0, 0.417, 0]}, {"time": 0.5, "x": -4.14, "curve": [0.584, -4.14, 0.65, 0, 0.584, 0, 0.65, 0]}, {"time": 0.7333, "curve": [0.817, 0, 0.916, -4.14, 0.817, 0, 0.916, 0]}, {"time": 1, "x": -4.14, "curve": [1.083, -4.14, 1.15, 0, 1.083, 0, 1.15, 0]}, {"time": 1.2333, "curve": [1.317, 0, 1.417, -4.14, 1.317, 0, 1.417, 0]}, {"time": 1.5, "x": -4.14, "curve": [1.583, -4.14, 1.65, 0, 1.583, 0, 1.65, 0]}, {"time": 1.7333, "curve": [1.816, 0, 1.917, -4.14, 1.816, 0, 1.917, 0]}, {"time": 2, "x": -4.14, "curve": [2.084, -4.14, 2.15, 0, 2.084, 0, 2.15, 0]}, {"time": 2.2333, "curve": [2.317, 0, 2.417, -4.14, 2.317, 0, 2.417, 0]}, {"time": 2.5, "x": -4.14, "curve": [2.583, -4.14, 2.65, 0, 2.583, 0, 2.65, 0]}, {"time": 2.7333, "curve": [2.817, 0, 2.917, -4.14, 2.817, 0, 2.917, 0]}, {"time": 3, "x": -4.14, "curve": [3.084, -4.14, 3.15, 0, 3.084, 0, 3.15, 0]}, {"time": 3.2333, "curve": [3.3, 0, 3.367, -2.66, 3.3, 0, 3.367, 0]}, {"time": 3.4333, "x": -3.71, "curve": [3.451, -3.97, 3.484, -4.14, 3.451, 0, 3.484, 0]}, {"time": 3.5, "x": -4.14, "curve": [3.583, -4.14, 3.65, 0, 3.583, 0, 3.65, 0]}, {"time": 3.7333, "curve": [3.744, 0, 3.756, -0.08, 3.744, 0, 3.756, 0]}, {"time": 3.7667, "x": -0.21, "curve": [3.843, -1.04, 3.928, -4.14, 3.843, 0, 3.928, 0]}, {"time": 4, "x": -4.14}]}, "left hand 2": {"rotate": [{"value": 33.59, "curve": [0.012, 31.7, 0.023, 29.58]}, {"time": 0.0333, "value": 27.39, "curve": [0.165, 55.98, 0.259, 57.08]}, {"time": 0.3, "value": 57.08, "curve": [0.38, 57.08, 0.412, 52.08]}, {"time": 0.5333, "value": 27.39, "curve": [0.665, 55.98, 0.76, 57.08]}, {"time": 0.8, "value": 57.08, "curve": [0.881, 57.08, 0.912, 52.08]}, {"time": 1.0333, "value": 27.39, "curve": [1.165, 55.98, 1.26, 57.08]}, {"time": 1.3, "value": 57.08, "curve": [1.381, 57.08, 1.445, 52.08]}, {"time": 1.5667, "value": 27.39, "curve": [1.699, 55.98, 1.76, 57.08]}, {"time": 1.8, "value": 57.08, "curve": [1.881, 57.08, 1.945, 52.08]}, {"time": 2.0667, "value": 27.39, "curve": [2.198, 55.98, 2.26, 57.08]}, {"time": 2.3, "value": 57.08, "curve": [2.38, 57.08, 2.446, 52.08]}, {"time": 2.5667, "value": 27.39, "curve": [2.698, 55.98, 2.76, 57.08]}, {"time": 2.8, "value": 57.08, "curve": [2.856, 57.08, 2.952, 49.05]}, {"time": 3, "value": 39.04, "curve": [3.085, 21.5, 3.148, -2.22]}, {"time": 3.2333, "curve": [3.305, 1.86, 3.372, 8.11]}, {"time": 3.4333, "value": 15.88, "curve": [3.57, 32.92, 3.678, 57.08]}, {"time": 3.7333, "value": 57.08, "curve": [3.741, 57.08, 3.752, 56.89]}, {"time": 3.7667, "value": 56.41, "curve": [3.82, 54.62, 3.912, 48.89]}, {"time": 4, "value": 33.59}]}, "right hand 2": {"rotate": [{"value": -59.31, "curve": [0.011, -57.22, 0.022, -54.82]}, {"time": 0.0333, "value": -52.42, "curve": [0.141, -87.49, 0.259, -87.58]}, {"time": 0.3, "value": -87.58, "curve": [0.38, -87.58, 0.385, -84.89]}, {"time": 0.5333, "value": -52.42, "curve": [0.64, -87.49, 0.76, -87.58]}, {"time": 0.8, "value": -87.58, "curve": [0.881, -87.58, 0.884, -84.89]}, {"time": 1.0333, "value": -52.42, "curve": [1.141, -87.49, 1.26, -87.58]}, {"time": 1.3, "value": -87.58, "curve": [1.381, -87.58, 1.417, -84.89]}, {"time": 1.5667, "value": -52.42, "curve": [1.674, -87.49, 1.76, -87.58]}, {"time": 1.8, "value": -87.58, "curve": [1.881, -87.58, 1.918, -84.89]}, {"time": 2.0667, "value": -52.42, "curve": [2.174, -87.49, 2.26, -87.58]}, {"time": 2.3, "value": -87.58, "curve": [2.38, -87.58, 2.418, -84.89]}, {"time": 2.5667, "value": -52.42, "curve": [2.674, -87.49, 2.76, -87.58]}, {"time": 2.8, "value": -87.58, "curve": [2.859, -87.58, 2.96, -78.58]}, {"time": 3, "value": -68.41, "curve": [3.101, -43.51, 3.139, -3.14]}, {"time": 3.2333, "curve": [3.304, 2.35, 3.372, -5.52]}, {"time": 3.4333, "value": -17.45, "curve": [3.569, -43.17, 3.678, -87.58]}, {"time": 3.7333, "value": -87.58, "curve": [3.742, -87.58, 3.753, -87.46]}, {"time": 3.7667, "value": -87, "curve": [3.816, -85.36, 3.898, -79.6]}, {"time": 4, "value": -59.31}]}, "Layer 129 copy": {"rotate": [{"value": -10.32, "curve": [0.011, -10.68, 0.022, -10.94]}, {"time": 0.0333, "value": -10.94, "curve": [0.1, -10.94, 0.166, -3.77]}, {"time": 0.2333, "value": -1.14, "curve": [0.25, -0.46, 0.284, 0]}, {"time": 0.3, "curve": [0.383, 0, 0.45, -10.94]}, {"time": 0.5333, "value": -10.94, "curve": [0.601, -10.94, 0.667, -3.77]}, {"time": 0.7333, "value": -1.14, "curve": [0.751, -0.46, 0.784, 0]}, {"time": 0.8, "curve": [0.883, 0, 0.95, -10.94]}, {"time": 1.0333, "value": -10.94, "curve": [1.1, -10.94, 1.167, -3.77]}, {"time": 1.2333, "value": -1.14, "curve": [1.251, -0.46, 1.283, 0]}, {"time": 1.3, "curve": [1.383, 0, 1.45, -10.94]}, {"time": 1.5333, "value": -10.94, "curve": [1.6, -10.94, 1.666, -3.77]}, {"time": 1.7333, "value": -1.14, "curve": [1.75, -0.46, 1.784, 0]}, {"time": 1.8, "curve": [1.883, 0, 1.95, -10.94]}, {"time": 2.0333, "value": -10.94, "curve": [2.101, -10.94, 2.167, -3.77]}, {"time": 2.2333, "value": -1.14, "curve": [2.251, -0.46, 2.284, 0]}, {"time": 2.3, "curve": [2.384, 0, 2.45, -10.94]}, {"time": 2.5333, "value": -10.94, "curve": [2.6, -10.94, 2.667, -3.77]}, {"time": 2.7333, "value": -1.14, "curve": [2.751, -0.46, 2.783, 0]}, {"time": 2.8, "curve": [2.866, 0, 2.934, -7.03]}, {"time": 3, "value": -9.8, "curve": [3.017, -10.48, 3.017, -10.94]}, {"time": 3.0333, "value": -10.94, "curve": [3.101, -10.94, 3.167, -3.77]}, {"time": 3.2333, "value": -1.14, "curve": [3.251, -0.46, 3.284, 0]}, {"time": 3.3, "curve": [3.35, 0, 3.383, -3.93]}, {"time": 3.4333, "value": -7.08, "curve": [3.467, -9.18, 3.5, -10.94]}, {"time": 3.5333, "value": -10.94, "curve": [3.6, -10.94, 3.667, -3.77]}, {"time": 3.7333, "value": -1.14, "curve": [3.742, -0.8, 3.754, -0.52]}, {"time": 3.7667, "value": -0.32, "curve": [3.779, -0.12, 3.792, 0]}, {"time": 3.8, "curve": [3.872, 0, 3.932, -8.09]}, {"time": 4, "value": -10.32}]}, "Layer 129 copy2": {"rotate": [{"value": -7.41, "curve": [0.035, -9.35, 0.069, -10.94]}, {"time": 0.1, "value": -10.94, "curve": [0.15, -10.94, 0.184, -6.91]}, {"time": 0.2333, "value": -3.85, "curve": [0.268, -1.77, 0.301, 0]}, {"time": 0.3333, "curve": [0.417, 0, 0.517, -10.94]}, {"time": 0.6, "value": -10.94, "curve": [0.65, -10.94, 0.683, -6.91]}, {"time": 0.7333, "value": -3.85, "curve": [0.767, -1.77, 0.8, 0]}, {"time": 0.8333, "curve": [0.916, 0, 1.017, -10.94]}, {"time": 1.1, "value": -10.94, "curve": [1.151, -10.94, 1.183, -6.91]}, {"time": 1.2333, "value": -3.85, "curve": [1.267, -1.77, 1.3, 0]}, {"time": 1.3333, "curve": [1.417, 0, 1.517, -10.94]}, {"time": 1.6, "value": -10.94, "curve": [1.65, -10.94, 1.684, -6.91]}, {"time": 1.7333, "value": -3.85, "curve": [1.768, -1.77, 1.801, 0]}, {"time": 1.8333, "curve": [1.917, 0, 2.017, -10.94]}, {"time": 2.1, "value": -10.94, "curve": [2.15, -10.94, 2.183, -6.91]}, {"time": 2.2333, "value": -3.85, "curve": [2.267, -1.77, 2.3, 0]}, {"time": 2.3333, "curve": [2.416, 0, 2.517, -10.94]}, {"time": 2.6, "value": -10.94, "curve": [2.651, -10.94, 2.683, -6.91]}, {"time": 2.7333, "value": -3.85, "curve": [2.767, -1.77, 2.8, 0]}, {"time": 2.8333, "curve": [2.884, 0, 2.95, -3.93]}, {"time": 3, "value": -7.08, "curve": [3.033, -9.18, 3.067, -10.94]}, {"time": 3.1, "value": -10.94, "curve": [3.15, -10.94, 3.183, -6.93]}, {"time": 3.2333, "value": -3.85, "curve": [3.267, -1.77, 3.3, 0]}, {"time": 3.3333, "curve": [3.367, 0, 3.4, -1.73]}, {"time": 3.4333, "value": -3.85, "curve": [3.484, -7, 3.55, -10.94]}, {"time": 3.6, "value": -10.94, "curve": [3.651, -10.94, 3.683, -6.91]}, {"time": 3.7333, "value": -3.85, "curve": [3.745, -3.16, 3.756, -2.52]}, {"time": 3.7667, "value": -1.95, "curve": [3.789, -0.8, 3.811, 0]}, {"time": 3.8333, "curve": [3.885, 0, 3.944, -4.21]}, {"time": 4, "value": -7.41}]}, "Layer 129": {"rotate": [{"value": 11.87}, {"time": 0.0333, "value": 13.85}, {"time": 0.2333, "value": 2.77}, {"time": 0.3}, {"time": 0.5333, "value": 13.85}, {"time": 0.7333, "value": 2.77}, {"time": 0.8}, {"time": 1.0333, "value": 13.85}, {"time": 1.2333, "value": 2.77}, {"time": 1.3}, {"time": 1.5333, "value": 13.85}, {"time": 1.7333, "value": 2.77}, {"time": 1.8}, {"time": 2.0333, "value": 13.85}, {"time": 2.2333, "value": 2.77}, {"time": 2.3}, {"time": 2.5333, "value": 13.85}, {"time": 2.7333, "value": 2.77}, {"time": 2.8}, {"time": 3, "value": 11.08}, {"time": 3.0333, "value": 13.85}, {"time": 3.2333, "value": 2.77}, {"time": 3.3}, {"time": 3.4333, "value": 8.31}, {"time": 3.5333, "value": 13.85}, {"time": 3.7333, "value": 2.77}, {"time": 3.7667, "value": 1.39}, {"time": 3.8}, {"time": 4, "value": 11.87}]}, "Layer 130": {"rotate": [{"value": 8.66}, {"time": 0.1, "value": 13.85}, {"time": 0.2333, "value": 5.54}, {"time": 0.3333}, {"time": 0.6, "value": 13.85}, {"time": 0.7333, "value": 5.54}, {"time": 0.8333}, {"time": 1.1, "value": 13.85}, {"time": 1.2333, "value": 5.54}, {"time": 1.3333}, {"time": 1.6, "value": 13.85}, {"time": 1.7333, "value": 5.54}, {"time": 1.8333}, {"time": 2.1, "value": 13.85}, {"time": 2.2333, "value": 5.54}, {"time": 2.3333}, {"time": 2.6, "value": 13.85}, {"time": 2.7333, "value": 5.54}, {"time": 2.8333}, {"time": 3, "value": 8.31}, {"time": 3.1, "value": 13.85}, {"time": 3.2333, "value": 5.54}, {"time": 3.3333}, {"time": 3.4333, "value": 5.54}, {"time": 3.6, "value": 13.85}, {"time": 3.7333, "value": 5.54}, {"time": 3.7667, "value": 3.69}, {"time": 3.8333}, {"time": 4, "value": 8.66}]}, "Bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}, "light_floor": {"scale": [{}, {"time": 0.3667, "x": 0.76, "y": 0.76}, {"time": 0.5}, {"time": 0.8333, "x": 0.76, "y": 0.76}, {"time": 1}, {"time": 1.3667, "x": 0.76, "y": 0.76}, {"time": 1.5}, {"time": 1.9, "x": 0.76, "y": 0.76}, {"time": 2}, {"time": 2.3667, "x": 0.76, "y": 0.76}, {"time": 2.5}, {"time": 2.8667, "x": 0.76, "y": 0.76}, {"time": 3}, {"time": 3.3667, "x": 0.76, "y": 0.76}, {"time": 3.5}, {"time": 3.8667, "x": 0.76, "y": 0.76}, {"time": 4}]}, "cat  tail1": {"rotate": [{"curve": [0.131, 0, 0.236, -54.37]}, {"time": 0.3667, "value": -54.37, "curve": [0.497, -54.37, 0.669, 0]}, {"time": 0.8, "curve": [0.931, 0, 1.036, -54.37]}, {"time": 1.1667, "value": -54.37, "curve": [1.297, -54.37, 1.469, 0]}, {"time": 1.6, "curve": [1.731, 0, 1.836, -54.37]}, {"time": 1.9667, "value": -54.37, "curve": [2.097, -54.37, 2.269, 0]}, {"time": 2.4, "curve": [2.531, 0, 2.636, -54.37]}, {"time": 2.7667, "value": -54.37, "curve": [2.897, -54.37, 3.069, 0]}, {"time": 3.2, "curve": [3.331, 0, 3.436, -54.37]}, {"time": 3.5667, "value": -54.37, "curve": [3.697, -54.37, 3.869, 0]}, {"time": 4}]}, "cat  tail1b": {"rotate": [{"value": -3.38, "curve": [0.013, -2.38, 0.054, 0]}, {"time": 0.0667, "curve": [0.197, 0, 0.369, -32.27]}, {"time": 0.5, "value": -32.27, "curve": [0.605, -32.27, 0.696, -11.2]}, {"time": 0.8, "value": -3.38, "curve": [0.813, -2.38, 0.854, 0]}, {"time": 0.8667, "curve": [0.997, 0, 1.169, -32.27]}, {"time": 1.3, "value": -32.27, "curve": [1.405, -32.27, 1.496, -11.2]}, {"time": 1.6, "value": -3.38, "curve": [1.613, -2.38, 1.654, 0]}, {"time": 1.6667, "curve": [1.797, 0, 1.969, -32.27]}, {"time": 2.1, "value": -32.27, "curve": [2.205, -32.27, 2.296, -11.2]}, {"time": 2.4, "value": -3.38, "curve": [2.413, -2.38, 2.454, 0]}, {"time": 2.4667, "curve": [2.597, 0, 2.769, -32.27]}, {"time": 2.9, "value": -32.27, "curve": [3.005, -32.27, 3.096, -11.2]}, {"time": 3.2, "value": -3.38, "curve": [3.213, -2.38, 3.254, 0]}, {"time": 3.2667, "curve": [3.397, 0, 3.569, -32.27]}, {"time": 3.7, "value": -32.27, "curve": [3.805, -32.27, 3.896, -11.2]}, {"time": 4, "value": -3.38}]}, "cat  tail1c": {"rotate": [{"value": -11.41, "curve": [0.027, -8.32, 0.041, -5.4]}, {"time": 0.0667, "value": -3.36, "curve": [0.093, -1.35, 0.141, 11.9]}, {"time": 0.1667, "value": 11.9, "curve": [0.219, 11.9, 0.248, 18.99]}, {"time": 0.3, "value": 10.51, "curve": [0.344, 3.43, 0.423, -14.25]}, {"time": 0.4667, "value": -24.91, "curve": [0.501, -33.4, 0.532, -42.88]}, {"time": 0.5667, "value": -42.88, "curve": [0.645, -42.88, 0.722, -20.57]}, {"time": 0.8, "value": -11.41, "curve": [0.827, -8.32, 0.841, -5.4]}, {"time": 0.8667, "value": -3.36, "curve": [0.893, -1.35, 0.941, 11.9]}, {"time": 0.9667, "value": 11.9, "curve": [1.019, 11.9, 1.048, 18.99]}, {"time": 1.1, "value": 10.51, "curve": [1.144, 3.43, 1.223, -14.25]}, {"time": 1.2667, "value": -24.91, "curve": [1.301, -33.4, 1.332, -42.88]}, {"time": 1.3667, "value": -42.88, "curve": [1.445, -42.88, 1.522, -20.57]}, {"time": 1.6, "value": -11.41, "curve": [1.627, -8.32, 1.641, -5.4]}, {"time": 1.6667, "value": -3.36, "curve": [1.693, -1.35, 1.741, 11.9]}, {"time": 1.7667, "value": 11.9, "curve": [1.819, 11.9, 1.848, 18.99]}, {"time": 1.9, "value": 10.51, "curve": [1.944, 3.43, 2.023, -14.25]}, {"time": 2.0667, "value": -24.91, "curve": [2.101, -33.4, 2.132, -42.88]}, {"time": 2.1667, "value": -42.88, "curve": [2.245, -42.88, 2.322, -20.57]}, {"time": 2.4, "value": -11.41, "curve": [2.427, -8.32, 2.441, -5.4]}, {"time": 2.4667, "value": -3.36, "curve": [2.493, -1.35, 2.541, 11.9]}, {"time": 2.5667, "value": 11.9, "curve": [2.619, 11.9, 2.648, 18.99]}, {"time": 2.7, "value": 10.51, "curve": [2.744, 3.43, 2.823, -14.25]}, {"time": 2.8667, "value": -24.91, "curve": [2.901, -33.4, 2.932, -42.88]}, {"time": 2.9667, "value": -42.88, "curve": [3.045, -42.88, 3.122, -20.57]}, {"time": 3.2, "value": -11.41, "curve": [3.227, -8.32, 3.241, -5.4]}, {"time": 3.2667, "value": -3.36, "curve": [3.293, -1.35, 3.341, 11.9]}, {"time": 3.3667, "value": 11.9, "curve": [3.419, 11.9, 3.448, 18.99]}, {"time": 3.5, "value": 10.51, "curve": [3.544, 3.43, 3.623, -14.25]}, {"time": 3.6667, "value": -24.91, "curve": [3.701, -33.4, 3.732, -42.88]}, {"time": 3.7667, "value": -42.88, "curve": [3.845, -42.88, 3.922, -20.57]}, {"time": 4, "value": -11.41}]}, "cat  tail1d": {"rotate": [{"value": -20.94, "curve": [0.039, -16.28, 0.094, 4.11]}, {"time": 0.1333, "value": 8.18, "curve": [0.174, 12.18, 0.195, 27.53]}, {"time": 0.2333, "value": 27.53, "curve": [0.286, 27.53, 0.314, 20.17]}, {"time": 0.3667, "value": 11.03, "curve": [0.41, 3.41, 0.49, -22.97]}, {"time": 0.5333, "value": -32.61, "curve": [0.568, -40.28, 0.599, -32.27]}, {"time": 0.6333, "value": -32.27, "curve": [0.686, -32.27, 0.748, -27.11]}, {"time": 0.8, "value": -20.94, "curve": [0.839, -16.28, 0.894, 4.11]}, {"time": 0.9333, "value": 8.18, "curve": [0.974, 12.18, 0.995, 27.53]}, {"time": 1.0333, "value": 27.53, "curve": [1.086, 27.53, 1.114, 20.17]}, {"time": 1.1667, "value": 11.03, "curve": [1.21, 3.41, 1.29, -22.97]}, {"time": 1.3333, "value": -32.61, "curve": [1.368, -40.28, 1.399, -32.27]}, {"time": 1.4333, "value": -32.27, "curve": [1.486, -32.27, 1.548, -27.11]}, {"time": 1.6, "value": -20.94, "curve": [1.639, -16.28, 1.694, 4.11]}, {"time": 1.7333, "value": 8.18, "curve": [1.774, 12.18, 1.795, 27.53]}, {"time": 1.8333, "value": 27.53, "curve": [1.886, 27.53, 1.914, 20.17]}, {"time": 1.9667, "value": 11.03, "curve": [2.01, 3.41, 2.09, -22.97]}, {"time": 2.1333, "value": -32.61, "curve": [2.168, -40.28, 2.199, -32.27]}, {"time": 2.2333, "value": -32.27, "curve": [2.286, -32.27, 2.348, -27.11]}, {"time": 2.4, "value": -20.94, "curve": [2.439, -16.28, 2.494, 4.11]}, {"time": 2.5333, "value": 8.18, "curve": [2.574, 12.18, 2.595, 27.53]}, {"time": 2.6333, "value": 27.53, "curve": [2.686, 27.53, 2.714, 20.17]}, {"time": 2.7667, "value": 11.03, "curve": [2.81, 3.41, 2.89, -22.97]}, {"time": 2.9333, "value": -32.61, "curve": [2.968, -40.28, 2.999, -32.27]}, {"time": 3.0333, "value": -32.27, "curve": [3.086, -32.27, 3.148, -27.11]}, {"time": 3.2, "value": -20.94, "curve": [3.239, -16.28, 3.294, 4.11]}, {"time": 3.3333, "value": 8.18, "curve": [3.374, 12.18, 3.395, 27.53]}, {"time": 3.4333, "value": 27.53, "curve": [3.486, 27.53, 3.514, 20.17]}, {"time": 3.5667, "value": 11.03, "curve": [3.61, 3.41, 3.69, -22.97]}, {"time": 3.7333, "value": -32.61, "curve": [3.768, -40.28, 3.799, -32.27]}, {"time": 3.8333, "value": -32.27, "curve": [3.886, -32.27, 3.948, -27.11]}, {"time": 4, "value": -20.94}]}, "cat  tail1e": {"rotate": [{"value": -28.9, "curve": [0.052, -24.78, 0.115, 3.09]}, {"time": 0.1667, "value": 9.31, "curve": [0.22, 15.43, 0.248, 40.93]}, {"time": 0.3, "value": 40.93, "curve": [0.357, 40.93, 0.444, 35.57]}, {"time": 0.5, "value": 14.66, "curve": [0.549, -2.91, 0.586, -35.66]}, {"time": 0.6333, "value": -44.15, "curve": [0.66, -48.7, 0.674, -32.27]}, {"time": 0.7, "value": -32.27, "curve": [0.727, -32.27, 0.774, -30.94]}, {"time": 0.8, "value": -28.9, "curve": [0.852, -24.78, 0.915, 3.09]}, {"time": 0.9667, "value": 9.31, "curve": [1.02, 15.43, 1.048, 40.93]}, {"time": 1.1, "value": 40.93, "curve": [1.157, 40.93, 1.244, 35.57]}, {"time": 1.3, "value": 14.66, "curve": [1.349, -2.91, 1.386, -35.66]}, {"time": 1.4333, "value": -44.15, "curve": [1.46, -48.7, 1.474, -32.27]}, {"time": 1.5, "value": -32.27, "curve": [1.527, -32.27, 1.574, -30.94]}, {"time": 1.6, "value": -28.9, "curve": [1.652, -24.78, 1.715, 3.09]}, {"time": 1.7667, "value": 9.31, "curve": [1.82, 15.43, 1.848, 40.93]}, {"time": 1.9, "value": 40.93, "curve": [1.957, 40.93, 2.044, 35.57]}, {"time": 2.1, "value": 14.66, "curve": [2.149, -2.91, 2.186, -35.66]}, {"time": 2.2333, "value": -44.15, "curve": [2.26, -48.7, 2.274, -32.27]}, {"time": 2.3, "value": -32.27, "curve": [2.327, -32.27, 2.374, -30.94]}, {"time": 2.4, "value": -28.9, "curve": [2.452, -24.78, 2.515, 3.09]}, {"time": 2.5667, "value": 9.31, "curve": [2.62, 15.43, 2.648, 40.93]}, {"time": 2.7, "value": 40.93, "curve": [2.757, 40.93, 2.844, 35.57]}, {"time": 2.9, "value": 14.66, "curve": [2.949, -2.91, 2.986, -35.66]}, {"time": 3.0333, "value": -44.15, "curve": [3.06, -48.7, 3.074, -32.27]}, {"time": 3.1, "value": -32.27, "curve": [3.127, -32.27, 3.174, -30.94]}, {"time": 3.2, "value": -28.9, "curve": [3.252, -24.78, 3.315, 3.09]}, {"time": 3.3667, "value": 9.31, "curve": [3.42, 15.43, 3.448, 40.93]}, {"time": 3.5, "value": 40.93, "curve": [3.557, 40.93, 3.644, 35.57]}, {"time": 3.7, "value": 14.66, "curve": [3.749, -2.91, 3.786, -35.66]}, {"time": 3.8333, "value": -44.15, "curve": [3.86, -48.7, 3.874, -32.27]}, {"time": 3.9, "value": -32.27, "curve": [3.927, -32.27, 3.974, -30.94]}, {"time": 4, "value": -28.9}]}}}, "cat_idle": {"slots": {"eye 2": {"attachment": [{"name": "eye 2"}, {"time": 0.1667, "name": "right  eye"}, {"time": 2, "name": "eye 2"}]}, "glow": {"rgba": [{"color": "ffffffff"}, {"time": 0.2667, "color": "ffffffc1"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffffc1"}, {"time": 1, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffffc1"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffffc1"}, {"time": 2, "color": "ffffffff"}]}, "glow2": {"rgba": [{"color": "ffffffb1", "curve": [0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 0.8, 0.089, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": [0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 0.62]}, {"time": 0.3333, "color": "ffffff9f", "curve": [0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 0.62, 0.442, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 0.62]}, {"time": 0.7, "color": "ffffff9f", "curve": [0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 0.62, 0.804, 1]}, {"time": 0.8667, "color": "ffffffff", "curve": [0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 0.62]}, {"time": 1.0667, "color": "ffffff9f", "curve": [1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 0.62, 1.208, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 0.62, 1.571, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 0.62, 1.973, 0.62]}, {"time": 2, "color": "ffffffb1"}]}, "glow3": {"rgba": [{"color": "ffffffb1", "curve": [0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 0.8, 0.089, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": [0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 0.62]}, {"time": 0.3333, "color": "ffffff9f", "curve": [0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 0.62, 0.442, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 0.62]}, {"time": 0.7, "color": "ffffff9f", "curve": [0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 0.62, 0.804, 1]}, {"time": 0.8667, "color": "ffffffff", "curve": [0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 0.62]}, {"time": 1.0667, "color": "ffffff9f", "curve": [1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 0.62, 1.208, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 0.62, 1.571, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 0.62, 1.973, 0.62]}, {"time": 2, "color": "ffffffb1"}]}, "glow4": {"rgba": [{"color": "ffffffb1", "curve": [0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 0.8, 0.089, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": [0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 0.62]}, {"time": 0.3333, "color": "ffffff9f", "curve": [0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 0.62, 0.442, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 0.62]}, {"time": 0.7, "color": "ffffff9f", "curve": [0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 0.62, 0.804, 1]}, {"time": 0.8667, "color": "ffffffff", "curve": [0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 0.62]}, {"time": 1.0667, "color": "ffffff9f", "curve": [1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 0.62, 1.208, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 0.62, 1.571, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 0.62, 1.973, 0.62]}, {"time": 2, "color": "ffffffb1"}]}, "glow5": {"rgba": [{"color": "ffffffb1", "curve": [0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 1, 0.089, 1, 0.042, 0.8, 0.089, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": [0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 1, 0.196, 1, 0.271, 0.62]}, {"time": 0.3333, "color": "ffffff9f", "curve": [0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 1, 0.442, 1, 0.396, 0.62, 0.442, 1]}, {"time": 0.5, "color": "ffffffff", "curve": [0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 1, 0.562, 1, 0.637, 0.62]}, {"time": 0.7, "color": "ffffff9f", "curve": [0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 1, 0.804, 1, 0.763, 0.62, 0.804, 1]}, {"time": 0.8667, "color": "ffffffff", "curve": [0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 1, 0.929, 1, 1.004, 0.62]}, {"time": 1.0667, "color": "ffffff9f", "curve": [1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 1, 1.208, 1, 1.129, 0.62, 1.208, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 1, 1.329, 1, 1.404, 0.62]}, {"time": 1.4667, "color": "ffffff9f", "curve": [1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 1, 1.571, 1, 1.529, 0.62, 1.571, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 1, 1.696, 1, 1.771, 0.62]}, {"time": 1.8333, "color": "ffffff9f", "curve": [1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 1, 1.942, 1, 1.896, 0.62, 1.973, 0.62]}, {"time": 2, "color": "ffffffb1"}]}, "Layer 130": {"rgba": [{"color": "ffffffff"}, {"time": 0.2667, "color": "e23c3c00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7667, "color": "e23c3c00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.2667, "color": "e23c3c00"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.7667, "color": "e23c3c00"}, {"time": 2, "color": "ffffffff"}]}, "left eye": {"attachment": [{"name": "eye 2 copy"}, {"time": 0.1667, "name": "left eye"}, {"time": 2, "name": "eye 2 copy"}]}}, "bones": {"pumpkin": {"scale": [{"x": 0.98, "y": 1.027, "curve": [0.083, 0.98, 0.184, 1.032, 0.083, 1.027, 0.184, 0.934]}, {"time": 0.2667, "x": 1.032, "y": 0.934, "curve": [0.35, 1.032, 0.422, 1, 0.35, 0.934, 0.422, 1]}, {"time": 0.5}, {"time": 0.7667, "x": 1.032, "y": 0.934, "curve": [0.85, 1.032, 0.917, 0.98, 0.85, 0.934, 0.917, 1.027]}, {"time": 1, "x": 0.98, "y": 1.027, "curve": [1.084, 0.98, 1.183, 1.032, 1.084, 1.027, 1.183, 0.934]}, {"time": 1.2667, "x": 1.032, "y": 0.934, "curve": [1.35, 1.032, 1.417, 0.98, 1.35, 0.934, 1.417, 1.027]}, {"time": 1.5, "x": 0.98, "y": 1.027, "curve": [1.583, 0.98, 1.684, 1.032, 1.583, 1.027, 1.684, 0.934]}, {"time": 1.7667, "x": 1.032, "y": 0.934, "curve": [1.85, 1.032, 1.917, 0.98, 1.85, 0.934, 1.917, 1.027]}, {"time": 2, "x": 0.98, "y": 1.027}]}, "cat body copy": {"translate": [{"x": -0.18, "y": -8.38, "curve": [0.008, -0.18, 0.025, -0.19, 0.008, -8.54, 0.025, -8.62]}, {"time": 0.0333, "x": -0.19, "y": -8.62, "curve": [0.109, -0.19, 0.258, 0, 0.109, -8.62, 0.258, 0]}, {"time": 0.2667, "curve": [0.35, 0, 0.45, -0.19, 0.35, 0, 0.45, -8.62]}, {"time": 0.5333, "x": -0.19, "y": -8.62, "curve": [0.608, -0.19, 0.759, 0, 0.608, -8.62, 0.759, 0]}, {"time": 0.7667, "curve": [0.85, 0, 0.95, -0.19, 0.85, 0, 0.95, -8.62]}, {"time": 1.0333, "x": -0.19, "y": -8.62, "curve": [1.108, -0.19, 1.258, 0, 1.108, -8.62, 1.258, 0]}, {"time": 1.2667, "curve": [1.35, 0, 1.45, -0.19, 1.35, 0, 1.45, -8.62]}, {"time": 1.5333, "x": -0.19, "y": -8.62, "curve": [1.609, -0.19, 1.758, 0, 1.609, -8.62, 1.758, 0]}, {"time": 1.7667, "curve": [1.85, 0, 1.925, -0.15, 1.85, 0, 1.925, -6.99]}, {"time": 2, "x": -0.18, "y": -8.38}]}, "head": {"translate": [{"x": -4.14, "curve": [0.083, -4.14, 0.184, 0, 0.083, 0, 0.184, 0]}, {"time": 0.2667, "curve": [0.35, 0, 0.417, -4.14, 0.35, 0, 0.417, 0]}, {"time": 0.5, "x": -4.14, "curve": [0.583, -4.14, 0.683, 0, 0.583, 0, 0.683, 0]}, {"time": 0.7667, "curve": [0.85, 0, 0.917, -4.14, 0.85, 0, 0.917, 0]}, {"time": 1, "x": -4.14, "curve": [1.084, -4.14, 1.183, 0, 1.084, 0, 1.183, 0]}, {"time": 1.2667, "curve": [1.35, 0, 1.417, -4.14, 1.35, 0, 1.417, 0]}, {"time": 1.5, "x": -4.14, "curve": [1.583, -4.14, 1.684, 0, 1.583, 0, 1.684, 0]}, {"time": 1.7667, "curve": [1.85, 0, 1.917, -4.14, 1.85, 0, 1.917, 0]}, {"time": 2, "x": -4.14}]}, "left hand 2": {"rotate": [{"value": 7.39, "curve": [0.083, 7.39, 0.184, 0]}, {"time": 0.2667, "curve": [0.35, 0, 0.417, 7.39]}, {"time": 0.5, "value": 7.39, "curve": [0.583, 7.39, 0.683, 0]}, {"time": 0.7667, "curve": [0.85, 0, 0.917, 7.39]}, {"time": 1, "value": 7.39, "curve": [1.084, 7.39, 1.183, 0]}, {"time": 1.2667, "curve": [1.35, 0, 1.417, 7.39]}, {"time": 1.5, "value": 7.39, "curve": [1.583, 7.39, 1.684, 0]}, {"time": 1.7667, "curve": [1.85, 0, 1.917, 7.39]}, {"time": 2, "value": 7.39}]}, "right hand 2": {"rotate": [{"value": -9.45, "curve": [0.083, -9.45, 0.184, 0]}, {"time": 0.2667, "curve": [0.35, 0, 0.417, -9.45]}, {"time": 0.5, "value": -9.45, "curve": [0.583, -9.45, 0.683, 0]}, {"time": 0.7667, "curve": [0.85, 0, 0.917, -9.45]}, {"time": 1, "value": -9.45, "curve": [1.084, -9.45, 1.183, 0]}, {"time": 1.2667, "curve": [1.35, 0, 1.417, -9.45]}, {"time": 1.5, "value": -9.45, "curve": [1.583, -9.45, 1.684, 0]}, {"time": 1.7667, "curve": [1.85, 0, 1.917, -9.45]}, {"time": 2, "value": -9.45}]}, "left hand": {"rotate": [{"value": 9.64, "curve": [0.025, 11.16, 0.042, 12.29]}, {"time": 0.0667, "value": 12.29, "curve": [0.108, 12.29, 0.159, 9.24]}, {"time": 0.2, "value": 6.15, "curve": [0.217, 4.93, 0.25, 3.71]}, {"time": 0.2667, "value": 2.69, "curve": [0.292, 1.13, 0.309, 0]}, {"time": 0.3333, "curve": [0.417, 0, 0.483, 12.29]}, {"time": 0.5667, "value": 12.29, "curve": [0.609, 12.29, 0.659, 9.24]}, {"time": 0.7, "value": 6.15, "curve": [0.717, 4.93, 0.75, 3.71]}, {"time": 0.7667, "value": 2.69, "curve": [0.792, 1.13, 0.808, 0]}, {"time": 0.8333, "curve": [0.916, 0, 0.984, 12.29]}, {"time": 1.0667, "value": 12.29, "curve": [1.109, 12.29, 1.159, 9.24]}, {"time": 1.2, "value": 6.15, "curve": [1.217, 4.93, 1.25, 3.71]}, {"time": 1.2667, "value": 2.69, "curve": [1.292, 1.13, 1.309, 0]}, {"time": 1.3333, "curve": [1.417, 0, 1.483, 12.29]}, {"time": 1.5667, "value": 12.29, "curve": [1.608, 12.29, 1.659, 9.24]}, {"time": 1.7, "value": 6.15, "curve": [1.717, 4.93, 1.75, 3.71]}, {"time": 1.7667, "value": 2.69, "curve": [1.792, 1.13, 1.809, 0]}, {"time": 1.8333, "curve": [1.917, 0, 1.942, 6.16]}, {"time": 2, "value": 9.64}]}, "Layer 129 copy": {"rotate": [{"value": -9.8, "curve": [0.017, -10.5, 0.05, -10.94]}, {"time": 0.0667, "value": -10.94, "curve": [0.133, -10.94, 0.2, -3.77]}, {"time": 0.2667, "value": -1.14, "curve": [0.284, -0.46, 0.283, 0]}, {"time": 0.3, "curve": [0.383, 0, 0.484, -10.94]}, {"time": 0.5667, "value": -10.94, "curve": [0.634, -10.94, 0.7, -3.77]}, {"time": 0.7667, "value": -1.14, "curve": [0.784, -0.46, 0.784, 0]}, {"time": 0.8, "curve": [0.883, 0, 0.983, -10.94]}, {"time": 1.0667, "value": -10.94, "curve": [1.134, -10.94, 1.2, -3.77]}, {"time": 1.2667, "value": -1.14, "curve": [1.284, -0.46, 1.284, 0]}, {"time": 1.3, "curve": [1.383, 0, 1.483, -10.94]}, {"time": 1.5667, "value": -10.94, "curve": [1.633, -10.94, 1.7, -3.77]}, {"time": 1.7667, "value": -1.14, "curve": [1.784, -0.46, 1.783, 0]}, {"time": 1.8, "curve": [1.883, 0, 1.933, -7]}, {"time": 2, "value": -9.8}]}, "Layer 129 copy2": {"rotate": [{"value": -7.09, "curve": [0.033, -9.19, 0.067, -10.94]}, {"time": 0.1, "value": -10.94, "curve": [0.15, -10.94, 0.217, -6.91]}, {"time": 0.2667, "value": -3.85, "curve": [0.301, -1.77, 0.334, 0]}, {"time": 0.3667, "curve": [0.45, 0, 0.516, -10.94]}, {"time": 0.6, "value": -10.94, "curve": [0.65, -10.94, 0.717, -6.91]}, {"time": 0.7667, "value": -3.85, "curve": [0.801, -1.77, 0.834, 0]}, {"time": 0.8667, "curve": [0.95, 0, 1.017, -10.94]}, {"time": 1.1, "value": -10.94, "curve": [1.15, -10.94, 1.217, -6.93]}, {"time": 1.2667, "value": -3.85, "curve": [1.3, -1.77, 1.333, 0]}, {"time": 1.3667, "curve": [1.45, 0, 1.517, -10.94]}, {"time": 1.6, "value": -10.94, "curve": [1.651, -10.94, 1.717, -6.91]}, {"time": 1.7667, "value": -3.85, "curve": [1.801, -1.77, 1.834, 0]}, {"time": 1.8667, "curve": [1.95, 0, 1.95, -3.95]}, {"time": 2, "value": -7.09}]}, "Layer 129": {"rotate": [{"value": 11.08}, {"time": 0.0667, "value": 13.85}, {"time": 0.2667, "value": 2.77}, {"time": 0.3}, {"time": 0.5667, "value": 13.85}, {"time": 0.7667, "value": 2.77}, {"time": 0.8}, {"time": 1.0667, "value": 13.85}, {"time": 1.2667, "value": 2.77}, {"time": 1.3}, {"time": 1.5667, "value": 13.85}, {"time": 1.7667, "value": 2.77}, {"time": 1.8}, {"time": 2, "value": 11.08}]}, "Layer 130": {"rotate": [{"value": 8.31}, {"time": 0.1, "value": 13.85}, {"time": 0.2667, "value": 5.54}, {"time": 0.3667}, {"time": 0.6, "value": 13.85}, {"time": 0.7667, "value": 5.54}, {"time": 0.8667}, {"time": 1.1, "value": 13.85}, {"time": 1.2667, "value": 5.54}, {"time": 1.3667}, {"time": 1.6, "value": 13.85}, {"time": 1.7667, "value": 5.54}, {"time": 1.8667}, {"time": 2, "value": 8.31}]}, "cat  tail1": {"rotate": [{"value": -14.21, "curve": [0.167, -30.16, 0.334, -54.37]}, {"time": 0.5, "value": -54.37, "curve": [0.75, -54.37, 1.017, 0]}, {"time": 1.2667, "curve": [1.516, 0, 1.851, 0]}, {"time": 2, "value": -14.21}]}, "cat  tail1b": {"rotate": [{"value": 2.58, "curve": [0.222, -4.11, 0.451, -32.27]}, {"time": 0.6667, "value": -32.27, "curve": [0.868, -32.27, 1.067, -11.22]}, {"time": 1.2667, "value": -3.38, "curve": [1.292, -2.38, 1.309, -1.46]}, {"time": 1.3333, "value": -0.9, "curve": [1.36, -0.36, 1.376, 0]}, {"time": 1.4, "curve": [1.65, 0, 1.943, 4.31]}, {"time": 2, "value": 2.58}]}, "cat  tail1c": {"rotate": [{"value": 22.8, "curve": [0.022, 25.55, 0.05, 11.9]}, {"time": 0.0667, "value": 11.9, "curve": [0.166, 11.9, 0.267, 18.99]}, {"time": 0.3667, "value": 10.51, "curve": [0.45, 3.43, 0.517, -14.25]}, {"time": 0.6, "value": -24.91, "curve": [0.667, -33.4, 0.733, -42.88]}, {"time": 0.8, "value": -42.88, "curve": [0.951, -42.88, 1.117, -20.5]}, {"time": 1.2667, "value": -11.41, "curve": [1.318, -8.32, 1.351, -5.4]}, {"time": 1.4, "value": -3.36, "curve": [1.452, -1.35, 1.517, 11.9]}, {"time": 1.5667, "value": 11.9, "curve": [1.666, 11.9, 1.767, 18.99]}, {"time": 1.8667, "value": 10.51, "curve": [1.95, 3.43, 1.982, 20.57]}, {"time": 2, "value": 22.8}]}, "cat  tail1d": {"rotate": [{"value": 24.4, "curve": [0.039, 27.53, 0.134, 27.53]}, {"time": 0.2, "value": 27.53, "curve": [0.3, 27.53, 0.4, 20.17]}, {"time": 0.5, "value": 11.03, "curve": [0.583, 3.41, 0.684, -22.97]}, {"time": 0.7667, "value": -32.61, "curve": [0.834, -40.28, 0.9, -32.27]}, {"time": 0.9667, "value": -32.27, "curve": [1.067, -32.27, 1.167, -27.09]}, {"time": 1.2667, "value": -20.94, "curve": [1.342, -16.28, 1.392, 4.11]}, {"time": 1.4667, "value": 8.18, "curve": [1.543, 12.18, 1.626, 27.53]}, {"time": 1.7, "value": 27.53, "curve": [1.8, 27.53, 1.9, 16.3]}, {"time": 2, "value": 24.4}]}, "cat  tail1e": {"rotate": [{"value": 18.98, "curve": [0.022, 21.64, 0.05, 8.27]}, {"time": 0.0667, "value": 9.31, "curve": [0.169, 15.43, 0.268, 40.93]}, {"time": 0.3667, "value": 40.93, "curve": [0.475, 40.93, 0.56, 35.57]}, {"time": 0.6667, "value": 14.66, "curve": [0.76, -2.91, 0.876, -35.66]}, {"time": 0.9667, "value": -44.15, "curve": [1.018, -48.7, 1.051, -32.27]}, {"time": 1.1, "value": -32.27, "curve": [1.15, -32.27, 1.217, -30.93]}, {"time": 1.2667, "value": -28.9, "curve": [1.368, -24.78, 1.468, 3.09]}, {"time": 1.5667, "value": 9.31, "curve": [1.669, 15.43, 1.768, 40.93]}, {"time": 1.8667, "value": 40.93, "curve": [1.975, 40.93, 1.95, 13]}, {"time": 2, "value": 18.98}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}, "Bi ngo": {"scale": [{"x": 0, "y": 0}]}, "light_floor": {"scale": [{}, {"time": 0.2667, "x": 0.8, "y": 0.8}, {"time": 0.5}, {"time": 0.7667, "x": 0.8, "y": 0.8}, {"time": 1}, {"time": 1.2667, "x": 0.8, "y": 0.8}, {"time": 1.5}, {"time": 1.7667, "x": 0.8, "y": 0.8}, {"time": 2}]}}}, "ghost_idle": {"bones": {"gost3": {"rotate": [{"value": -2.92, "curve": [0.101, 1.3, 0.201, 6.6]}, {"time": 0.3, "value": 6.6, "curve": [0.466, 6.6, 0.634, -8.09]}, {"time": 0.8, "value": -8.09, "curve": [0.867, -8.09, 0.933, -5.7]}, {"time": 1, "value": -2.92, "curve": [1.101, 1.3, 1.201, 6.6]}, {"time": 1.3, "value": 6.6, "curve": [1.467, 6.6, 1.634, -8.09]}, {"time": 1.8, "value": -8.09, "curve": [1.867, -8.09, 1.934, -5.77]}, {"time": 2, "value": -2.92}]}, "gost3b": {"rotate": [{"value": -6.01, "curve": [0.129, -1.96, 0.273, 6.6]}, {"time": 0.4, "value": 6.6, "curve": [0.54, 6.6, 0.662, -3.58]}, {"time": 0.8, "value": -6.93, "curve": [0.829, -7.59, 0.873, -8.09]}, {"time": 0.9, "value": -8.09, "curve": [0.939, -8.09, 0.967, -7.06]}, {"time": 1, "value": -6.01, "curve": [1.129, -1.96, 1.273, 6.6]}, {"time": 1.4, "value": 6.6, "curve": [1.54, 6.6, 1.662, -3.58]}, {"time": 1.8, "value": -6.93, "curve": [1.829, -7.59, 1.873, -8.09]}, {"time": 1.9, "value": -8.09, "curve": [1.939, -8.09, 1.962, -7.27]}, {"time": 2, "value": -6.01}]}, "gost3c": {"rotate": [{"value": -7.82, "curve": [0.156, -6.11, 0.311, 6.6]}, {"time": 0.4667, "value": 6.6, "curve": [0.579, 6.6, 0.69, 0.1]}, {"time": 0.8, "value": -4.25, "curve": [0.857, -6.39, 0.912, -8.09]}, {"time": 0.9667, "value": -8.09, "curve": [0.978, -8.09, 0.989, -7.94]}, {"time": 1, "value": -7.82, "curve": [1.156, -6.11, 1.311, 6.6]}, {"time": 1.4667, "value": 6.6, "curve": [1.579, 6.6, 1.69, 0.1]}, {"time": 1.8, "value": -4.25, "curve": [1.857, -6.39, 1.912, -8.09]}, {"time": 1.9667, "value": -8.09, "curve": [1.978, -8.09, 1.989, -7.94]}, {"time": 2, "value": -7.82}]}, "gost4": {"rotate": [{"value": -7.67, "curve": [0.017, -7.94, 0.05, -8.09]}, {"time": 0.0667, "value": -8.09, "curve": [0.233, -8.09, 0.4, 6.6]}, {"time": 0.5667, "value": 6.6, "curve": [0.651, 6.6, 0.718, 2.95]}, {"time": 0.8, "value": -0.74, "curve": [0.868, -3.66, 0.933, -6.62]}, {"time": 1, "value": -7.67, "curve": [1.017, -7.94, 1.05, -8.09]}, {"time": 1.0667, "value": -8.09, "curve": [1.233, -8.09, 1.4, 6.6]}, {"time": 1.5667, "value": 6.6, "curve": [1.651, 6.6, 1.718, 2.95]}, {"time": 1.8, "value": -0.74, "curve": [1.868, -3.66, 1.934, -6.61]}, {"time": 2, "value": -7.67}]}, "gost": {"rotate": [{"value": -6.63, "curve": [0.134, -3.85, 0.267, 3.03]}, {"time": 0.4, "value": 3.03, "curve": [0.517, 3.03, 0.651, -2.26]}, {"time": 0.7667, "value": -5.42, "curve": [0.818, -6.76, 0.851, -7.75]}, {"time": 0.9, "value": -7.75, "curve": [0.934, -7.75, 0.967, -7.32]}, {"time": 1, "value": -6.63, "curve": [1.134, -3.85, 1.267, 3.03]}, {"time": 1.4, "value": 3.03, "curve": [1.517, 3.03, 1.651, -2.26]}, {"time": 1.7667, "value": -5.42, "curve": [1.818, -6.76, 1.851, -7.75]}, {"time": 1.9, "value": -7.75, "curve": [1.934, -7.75, 1.968, -7.34]}, {"time": 2, "value": -6.63}]}, "gost7": {"rotate": [{"value": -7.65, "curve": [0.162, -6.92, 0.339, 3.03]}, {"time": 0.5, "value": 3.03, "curve": [0.589, 3.03, 0.679, 0]}, {"time": 0.7667, "value": -2.85, "curve": [0.817, -4.46, 0.851, -6.02]}, {"time": 0.9, "value": -6.9, "curve": [0.929, -7.38, 0.967, -7.8]}, {"time": 1, "value": -7.65, "curve": [1.162, -6.92, 1.339, 3.03]}, {"time": 1.5, "value": 3.03, "curve": [1.589, 3.03, 1.679, 0]}, {"time": 1.7667, "value": -2.85, "curve": [1.817, -4.46, 1.851, -6.02]}, {"time": 1.9, "value": -6.9, "curve": [1.929, -7.38, 1.996, -7.68]}, {"time": 2, "value": -7.65}]}, "gost8": {"rotate": [{"value": -7.2, "curve": [0.023, -7.54, 0.045, -7.75]}, {"time": 0.0667, "value": -7.75, "curve": [0.234, -7.75, 0.4, 3.03]}, {"time": 0.5667, "value": 3.03, "curve": [0.628, 3.03, 0.706, 1.6]}, {"time": 0.7667, "value": -0.23, "curve": [0.817, -1.73, 0.851, -3.5]}, {"time": 0.9, "value": -4.93, "curve": [0.934, -5.88, 0.967, -6.7]}, {"time": 1, "value": -7.2, "curve": [1.023, -7.54, 1.045, -7.75]}, {"time": 1.0667, "value": -7.75, "curve": [1.234, -7.75, 1.4, 3.03]}, {"time": 1.5667, "value": 3.03, "curve": [1.628, 3.03, 1.706, 1.6]}, {"time": 1.7667, "value": -0.23, "curve": [1.817, -1.73, 1.851, -3.5]}, {"time": 1.9, "value": -4.93, "curve": [1.934, -5.88, 1.967, -6.7]}, {"time": 2, "value": -7.2}]}, "gost2": {"rotate": [{"value": -5.4, "curve": [0.051, -6.76, 0.117, -7.75]}, {"time": 0.1667, "value": -7.75, "curve": [0.333, -7.75, 0.5, 3.03]}, {"time": 0.6667, "value": 3.03, "curve": [0.7, 3.03, 0.734, 2.61]}, {"time": 0.7667, "value": 1.92, "curve": [0.817, 0.89, 0.851, -0.73]}, {"time": 0.9, "value": -2.36, "curve": [0.934, -3.43, 0.967, -4.5]}, {"time": 1, "value": -5.4, "curve": [1.051, -6.76, 1.117, -7.75]}, {"time": 1.1667, "value": -7.75, "curve": [1.333, -7.75, 1.5, 3.03]}, {"time": 1.6667, "value": 3.03, "curve": [1.7, 3.03, 1.734, 2.61]}, {"time": 1.7667, "value": 1.92, "curve": [1.817, 0.89, 1.851, -0.73]}, {"time": 1.9, "value": -2.36, "curve": [1.934, -3.43, 1.967, -4.49]}, {"time": 2, "value": -5.4}]}, "gost6": {"rotate": [{"value": -3.75, "curve": [0.039, -4.91, 0.095, -5.7]}, {"time": 0.1333, "value": -5.7, "curve": [0.262, -5.7, 0.374, 2.43]}, {"time": 0.5, "value": 6.21, "curve": [0.54, 7.34, 0.595, 8.17]}, {"time": 0.6333, "value": 8.17, "curve": [0.695, 8.17, 0.74, 6.33]}, {"time": 0.8, "value": 3.97, "curve": [0.823, 3.1, 0.845, 2.16]}, {"time": 0.8667, "value": 1.23, "curve": [0.912, -0.6, 0.956, -2.44]}, {"time": 1, "value": -3.75, "curve": [1.039, -4.91, 1.095, -5.7]}, {"time": 1.1333, "value": -5.7, "curve": [1.262, -5.7, 1.374, 2.43]}, {"time": 1.5, "value": 6.21, "curve": [1.54, 7.34, 1.595, 8.17]}, {"time": 1.6333, "value": 8.17, "curve": [1.695, 8.17, 1.74, 6.33]}, {"time": 1.8, "value": 3.97, "curve": [1.823, 3.1, 1.845, 2.16]}, {"time": 1.8667, "value": 1.23, "curve": [1.912, -0.6, 1.956, -2.42]}, {"time": 2, "value": -3.75}]}, "gost5": {"rotate": [{"value": -3.22, "curve": [0.122, 0.75, 0.244, 8.17]}, {"time": 0.3667, "value": 8.17, "curve": [0.412, 8.17, 0.457, 7.17]}, {"time": 0.5, "value": 5.69, "curve": [0.601, 2.42, 0.701, -3.1]}, {"time": 0.8, "value": -5, "curve": [0.823, -5.42, 0.845, -5.7]}, {"time": 0.8667, "value": -5.7, "curve": [0.911, -5.7, 0.956, -4.67]}, {"time": 1, "value": -3.22, "curve": [1.122, 0.75, 1.244, 8.17]}, {"time": 1.3667, "value": 8.17, "curve": [1.412, 8.17, 1.457, 7.17]}, {"time": 1.5, "value": 5.69, "curve": [1.601, 2.42, 1.701, -3.1]}, {"time": 1.8, "value": -5, "curve": [1.823, -5.42, 1.845, -5.7]}, {"time": 1.8667, "value": -5.7, "curve": [1.911, -5.7, 1.956, -4.67]}, {"time": 2, "value": -3.22}]}, "gost5b": {"rotate": [{"value": -5.31, "curve": [0.151, -3.01, 0.317, 8.17]}, {"time": 0.4667, "value": 8.17, "curve": [0.483, 8.17, 0.484, 8]}, {"time": 0.5, "value": 7.75, "curve": [0.601, 6.25, 0.701, 0.83]}, {"time": 0.8, "value": -2.63, "curve": [0.823, -3.39, 0.845, -4.11]}, {"time": 0.8667, "value": -4.61, "curve": [0.896, -5.23, 0.94, -5.7]}, {"time": 0.9667, "value": -5.7, "curve": [0.984, -5.7, 0.989, -5.48]}, {"time": 1, "value": -5.31, "curve": [1.151, -3.01, 1.317, 8.17]}, {"time": 1.4667, "value": 8.17, "curve": [1.483, 8.17, 1.484, 8]}, {"time": 1.5, "value": 7.75, "curve": [1.601, 6.25, 1.701, 0.83]}, {"time": 1.8, "value": -2.63, "curve": [1.823, -3.39, 1.845, -4.11]}, {"time": 1.8667, "value": -4.61, "curve": [1.896, -5.23, 1.94, -5.7]}, {"time": 1.9667, "value": -5.7, "curve": [1.984, -5.7, 1.984, -5.58]}, {"time": 2, "value": -5.31}]}, "gost5c": {"rotate": [{"value": -5.51, "curve": [0.011, -5.63, 0.022, -5.7]}, {"time": 0.0333, "value": -5.7, "curve": [0.189, -5.7, 0.346, 6.37]}, {"time": 0.5, "value": 7.91, "curve": [0.512, 8.01, 0.523, 8.17]}, {"time": 0.5333, "value": 8.17, "curve": [0.623, 8.17, 0.712, 4.26]}, {"time": 0.8, "value": 0.58, "curve": [0.823, -0.34, 0.845, -1.26]}, {"time": 0.8667, "value": -2.08, "curve": [0.912, -3.7, 0.956, -5.05]}, {"time": 1, "value": -5.51, "curve": [1.011, -5.63, 1.022, -5.7]}, {"time": 1.0333, "value": -5.7, "curve": [1.189, -5.7, 1.345, 6.37]}, {"time": 1.5, "value": 7.91, "curve": [1.512, 8.01, 1.523, 8.17]}, {"time": 1.5333, "value": 8.17, "curve": [1.623, 8.17, 1.712, 4.26]}, {"time": 1.8, "value": 0.58, "curve": [1.823, -0.34, 1.845, -1.26]}, {"time": 1.8667, "value": -2.08, "curve": [1.912, -3.7, 1.956, -5.04]}, {"time": 2, "value": -5.51}]}, "Meo cuoi bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Bi ngo": {"scale": [{"x": 0, "y": 0}]}}}, "pumpkin_action1": {"slots": {"bg/Pumkin1_light": {"rgba": [{"color": "ffffff0d", "curve": [0.036, 1, 0.068, 1, 0.036, 1, 0.068, 1, 0.036, 1, 0.068, 1, 0.036, 0.16, 0.068, 0.4]}, {"time": 0.1, "color": "ffffff9d", "curve": [0.131, 1, 0.163, 1, 0.131, 1, 0.163, 1, 0.131, 1, 0.163, 1, 0.131, 0.82, 0.163, 1]}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.311, 1, 0.389, 1, 0.311, 1, 0.389, 1, 0.311, 1, 0.389, 1, 0.311, 1, 0.389, 0.01]}, {"time": 0.4667, "color": "ffffff01", "curve": [0.555, 1, 0.644, 1, 0.555, 1, 0.644, 1, 0.555, 1, 0.644, 1, 0.555, 0.01, 0.644, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 1, 0.889, 0.01]}, {"time": 0.9667, "color": "ffffff01", "curve": [0.978, 1, 0.989, 1, 0.978, 1, 0.989, 1, 0.978, 1, 0.989, 1, 0.978, 0, 0.989, 0.02]}, {"time": 1, "color": "ffffff0d", "curve": [1.036, 1, 1.068, 1, 1.036, 1, 1.068, 1, 1.036, 1, 1.068, 1, 1.036, 0.16, 1.068, 0.4]}, {"time": 1.1, "color": "ffffff9d", "curve": [1.131, 1, 1.163, 1, 1.131, 1, 1.163, 1, 1.131, 1, 1.163, 1, 1.131, 0.82, 1.163, 1]}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2333, "color": "ffffffff", "curve": [1.311, 1, 1.389, 1, 1.311, 1, 1.389, 1, 1.311, 1, 1.389, 1, 1.311, 1, 1.389, 0.01]}, {"time": 1.4667, "color": "ffffff01", "curve": [1.555, 1, 1.644, 1, 1.555, 1, 1.644, 1, 1.555, 1, 1.644, 1, 1.555, 0.01, 1.644, 1]}, {"time": 1.7333, "color": "ffffffff", "curve": [1.811, 1, 1.889, 1, 1.811, 1, 1.889, 1, 1.811, 1, 1.889, 1, 1.811, 1, 1.889, 0.01]}, {"time": 1.9667, "color": "ffffff01", "curve": [1.978, 1, 1.989, 1, 1.978, 1, 1.989, 1, 1.978, 1, 1.989, 1, 1.978, 0, 1.989, 0.02]}, {"time": 2, "color": "ffffff0d", "curve": [2.036, 1, 2.068, 1, 2.036, 1, 2.068, 1, 2.036, 1, 2.068, 1, 2.036, 0.16, 2.068, 0.4]}, {"time": 2.1, "color": "ffffff9d", "curve": [2.131, 1, 2.163, 1, 2.131, 1, 2.163, 1, 2.131, 1, 2.163, 1, 2.131, 0.82, 2.163, 1]}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2333, "color": "ffffffff", "curve": [2.311, 1, 2.389, 1, 2.311, 1, 2.389, 1, 2.311, 1, 2.389, 1, 2.311, 1, 2.389, 0.01]}, {"time": 2.4667, "color": "ffffff01", "curve": [2.555, 1, 2.644, 1, 2.555, 1, 2.644, 1, 2.555, 1, 2.644, 1, 2.555, 0.01, 2.644, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.811, 1, 2.889, 1, 2.811, 1, 2.889, 1, 2.811, 1, 2.889, 1, 2.811, 1, 2.889, 0.01]}, {"time": 2.9667, "color": "ffffff01", "curve": [2.978, 1, 2.989, 1, 2.978, 1, 2.989, 1, 2.978, 1, 2.989, 1, 2.978, 0, 2.989, 0.02]}, {"time": 3, "color": "ffffff0d", "curve": [3.036, 1, 3.068, 1, 3.036, 1, 3.068, 1, 3.036, 1, 3.068, 1, 3.036, 0.16, 3.068, 0.4]}, {"time": 3.1, "color": "ffffff9d", "curve": [3.131, 1, 3.163, 1, 3.131, 1, 3.163, 1, 3.131, 1, 3.163, 1, 3.131, 0.82, 3.163, 1]}, {"time": 3.2, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2333, "color": "ffffffff", "curve": [3.311, 1, 3.389, 1, 3.311, 1, 3.389, 1, 3.311, 1, 3.389, 1, 3.311, 1, 3.389, 0.01]}, {"time": 3.4667, "color": "ffffff01", "curve": [3.555, 1, 3.644, 1, 3.555, 1, 3.644, 1, 3.555, 1, 3.644, 1, 3.555, 0.01, 3.644, 1]}, {"time": 3.7333, "color": "ffffffff", "curve": [3.811, 1, 3.889, 1, 3.811, 1, 3.889, 1, 3.811, 1, 3.889, 1, 3.811, 1, 3.889, 0.01]}, {"time": 3.9667, "color": "ffffff01", "curve": [3.978, 1, 3.989, 1, 3.978, 1, 3.989, 1, 3.978, 1, 3.989, 1, 3.978, 0, 3.989, 0.02]}, {"time": 4, "color": "ffffff0d"}]}, "bg/Pumkin2_light": {"rgba": [{"color": "ffffff63", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0.46, 0.022, 0.54]}, {"time": 0.0333, "color": "ffffff9d", "curve": [0.064, 1, 0.096, 1, 0.064, 1, 0.096, 1, 0.064, 1, 0.096, 1, 0.064, 0.82, 0.096, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": [0.245, 1, 0.323, 1, 0.245, 1, 0.323, 1, 0.245, 1, 0.323, 1, 0.245, 1, 0.323, 0.01]}, {"time": 0.4, "color": "ffffff01", "curve": [0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 0.01, 0.578, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": [0.745, 1, 0.823, 1, 0.745, 1, 0.823, 1, 0.745, 1, 0.823, 1, 0.745, 1, 0.823, 0.01]}, {"time": 0.9, "color": "ffffff01", "curve": [0.938, 1, 0.967, 1, 0.938, 1, 0.967, 1, 0.938, 1, 0.967, 1, 0.938, 0.01, 0.967, 0.17]}, {"time": 1, "color": "ffffff63", "curve": [1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 0.46, 1.022, 0.54]}, {"time": 1.0333, "color": "ffffff9d", "curve": [1.064, 1, 1.096, 1, 1.064, 1, 1.096, 1, 1.064, 1, 1.096, 1, 1.064, 0.82, 1.096, 1]}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff", "curve": [1.245, 1, 1.323, 1, 1.245, 1, 1.323, 1, 1.245, 1, 1.323, 1, 1.245, 1, 1.323, 0.01]}, {"time": 1.4, "color": "ffffff01", "curve": [1.489, 1, 1.578, 1, 1.489, 1, 1.578, 1, 1.489, 1, 1.578, 1, 1.489, 0.01, 1.578, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": [1.745, 1, 1.823, 1, 1.745, 1, 1.823, 1, 1.745, 1, 1.823, 1, 1.745, 1, 1.823, 0.01]}, {"time": 1.9, "color": "ffffff01", "curve": [1.938, 1, 1.967, 1, 1.938, 1, 1.967, 1, 1.938, 1, 1.967, 1, 1.938, 0.01, 1.967, 0.17]}, {"time": 2, "color": "ffffff63", "curve": [2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 0.46, 2.022, 0.54]}, {"time": 2.0333, "color": "ffffff9d", "curve": [2.064, 1, 2.096, 1, 2.064, 1, 2.096, 1, 2.064, 1, 2.096, 1, 2.064, 0.82, 2.096, 1]}, {"time": 2.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.245, 1, 2.323, 1, 2.245, 1, 2.323, 1, 2.245, 1, 2.323, 1, 2.245, 1, 2.323, 0.01]}, {"time": 2.4, "color": "ffffff01", "curve": [2.489, 1, 2.578, 1, 2.489, 1, 2.578, 1, 2.489, 1, 2.578, 1, 2.489, 0.01, 2.578, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": [2.745, 1, 2.823, 1, 2.745, 1, 2.823, 1, 2.745, 1, 2.823, 1, 2.745, 1, 2.823, 0.01]}, {"time": 2.9, "color": "ffffff01", "curve": [2.938, 1, 2.967, 1, 2.938, 1, 2.967, 1, 2.938, 1, 2.967, 1, 2.938, 0.01, 2.967, 0.17]}, {"time": 3, "color": "ffffff63", "curve": [3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 0.46, 3.022, 0.54]}, {"time": 3.0333, "color": "ffffff9d", "curve": [3.064, 1, 3.096, 1, 3.064, 1, 3.096, 1, 3.064, 1, 3.096, 1, 3.064, 0.82, 3.096, 1]}, {"time": 3.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff", "curve": [3.245, 1, 3.323, 1, 3.245, 1, 3.323, 1, 3.245, 1, 3.323, 1, 3.245, 1, 3.323, 0.01]}, {"time": 3.4, "color": "ffffff01", "curve": [3.489, 1, 3.578, 1, 3.489, 1, 3.578, 1, 3.489, 1, 3.578, 1, 3.489, 0.01, 3.578, 1]}, {"time": 3.6667, "color": "ffffffff", "curve": [3.745, 1, 3.823, 1, 3.745, 1, 3.823, 1, 3.745, 1, 3.823, 1, 3.745, 1, 3.823, 0.01]}, {"time": 3.9, "color": "ffffff01", "curve": [3.938, 1, 3.969, 1, 3.938, 1, 3.969, 1, 3.938, 1, 3.969, 1, 3.938, 0.01, 3.969, 0.18]}, {"time": 4, "color": "ffffff63"}]}, "bg/Pumkin3_light": {"rgba": [{"color": "fffffff4", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0.98, 0.022, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 0.01]}, {"time": 0.2667, "color": "ffffff01", "curve": [0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 0.01, 0.444, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 0.01]}, {"time": 0.7667, "color": "ffffff01", "curve": [0.855, 1, 0.922, 1, 0.855, 1, 0.922, 1, 0.855, 1, 0.922, 1, 0.855, 0.01, 0.922, 0.77]}, {"time": 1, "color": "fffffff4", "curve": [1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 0.98, 1.022, 1]}, {"time": 1.0333, "color": "ffffffff", "curve": [1.111, 1, 1.189, 1, 1.111, 1, 1.189, 1, 1.111, 1, 1.189, 1, 1.111, 1, 1.189, 0.01]}, {"time": 1.2667, "color": "ffffff01", "curve": [1.355, 1, 1.444, 1, 1.355, 1, 1.444, 1, 1.355, 1, 1.444, 1, 1.355, 0.01, 1.444, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": [1.611, 1, 1.689, 1, 1.611, 1, 1.689, 1, 1.611, 1, 1.689, 1, 1.611, 1, 1.689, 0.01]}, {"time": 1.7667, "color": "ffffff01", "curve": [1.855, 1, 1.922, 1, 1.855, 1, 1.922, 1, 1.855, 1, 1.922, 1, 1.855, 0.01, 1.922, 0.77]}, {"time": 2, "color": "fffffff4", "curve": [2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 0.98, 2.022, 1]}, {"time": 2.0333, "color": "ffffffff", "curve": [2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 1, 2.189, 0.01]}, {"time": 2.2667, "color": "ffffff01", "curve": [2.355, 1, 2.444, 1, 2.355, 1, 2.444, 1, 2.355, 1, 2.444, 1, 2.355, 0.01, 2.444, 1]}, {"time": 2.5333, "color": "ffffffff", "curve": [2.611, 1, 2.689, 1, 2.611, 1, 2.689, 1, 2.611, 1, 2.689, 1, 2.611, 1, 2.689, 0.01]}, {"time": 2.7667, "color": "ffffff01", "curve": [2.855, 1, 2.922, 1, 2.855, 1, 2.922, 1, 2.855, 1, 2.922, 1, 2.855, 0.01, 2.922, 0.77]}, {"time": 3, "color": "fffffff4", "curve": [3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 0.98, 3.022, 1]}, {"time": 3.0333, "color": "ffffffff", "curve": [3.111, 1, 3.189, 1, 3.111, 1, 3.189, 1, 3.111, 1, 3.189, 1, 3.111, 1, 3.189, 0.01]}, {"time": 3.2667, "color": "ffffff01", "curve": [3.355, 1, 3.444, 1, 3.355, 1, 3.444, 1, 3.355, 1, 3.444, 1, 3.355, 0.01, 3.444, 1]}, {"time": 3.5333, "color": "ffffffff", "curve": [3.611, 1, 3.689, 1, 3.611, 1, 3.689, 1, 3.611, 1, 3.689, 1, 3.611, 1, 3.689, 0.01]}, {"time": 3.7667, "color": "ffffff01", "curve": [3.855, 1, 3.911, 1, 3.855, 1, 3.911, 1, 3.855, 1, 3.911, 1, 3.855, 0.01, 3.911, 1]}, {"time": 4, "color": "ffffffff"}]}, "bg/Pumkin4_light": {"rgba": [{"color": "ffffff9d", "curve": [0.031, 1, 0.063, 1, 0.031, 1, 0.063, 1, 0.031, 1, 0.063, 1, 0.031, 0.82, 0.063, 1]}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff", "curve": [0.211, 1, 0.289, 1, 0.211, 1, 0.289, 1, 0.211, 1, 0.289, 1, 0.211, 1, 0.289, 0.01]}, {"time": 0.3667, "color": "ffffff01", "curve": [0.455, 1, 0.544, 1, 0.455, 1, 0.544, 1, 0.455, 1, 0.544, 1, 0.455, 0.01, 0.544, 1]}, {"time": 0.6333, "color": "ffffffff", "curve": [0.711, 1, 0.789, 1, 0.711, 1, 0.789, 1, 0.711, 1, 0.789, 1, 0.711, 1, 0.789, 0.01]}, {"time": 0.8667, "color": "ffffff01", "curve": [0.918, 1, 0.956, 1, 0.918, 1, 0.956, 1, 0.918, 1, 0.956, 1, 0.918, 0.01, 0.956, 0.33]}, {"time": 1, "color": "ffffff9d", "curve": [1.031, 1, 1.063, 1, 1.031, 1, 1.063, 1, 1.031, 1, 1.063, 1, 1.031, 0.82, 1.063, 1]}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff", "curve": [1.211, 1, 1.289, 1, 1.211, 1, 1.289, 1, 1.211, 1, 1.289, 1, 1.211, 1, 1.289, 0.01]}, {"time": 1.3667, "color": "ffffff01", "curve": [1.455, 1, 1.544, 1, 1.455, 1, 1.544, 1, 1.455, 1, 1.544, 1, 1.455, 0.01, 1.544, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.711, 1, 1.789, 1, 1.711, 1, 1.789, 1, 1.711, 1, 1.789, 1, 1.711, 1, 1.789, 0.01]}, {"time": 1.8667, "color": "ffffff01", "curve": [1.918, 1, 1.956, 1, 1.918, 1, 1.956, 1, 1.918, 1, 1.956, 1, 1.918, 0.01, 1.956, 0.33]}, {"time": 2, "color": "ffffff9d", "curve": [2.031, 1, 2.063, 1, 2.031, 1, 2.063, 1, 2.031, 1, 2.063, 1, 2.031, 0.82, 2.063, 1]}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff", "curve": [2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 0.01]}, {"time": 2.3667, "color": "ffffff01", "curve": [2.455, 1, 2.544, 1, 2.455, 1, 2.544, 1, 2.455, 1, 2.544, 1, 2.455, 0.01, 2.544, 1]}, {"time": 2.6333, "color": "ffffffff", "curve": [2.711, 1, 2.789, 1, 2.711, 1, 2.789, 1, 2.711, 1, 2.789, 1, 2.711, 1, 2.789, 0.01]}, {"time": 2.8667, "color": "ffffff01", "curve": [2.918, 1, 2.956, 1, 2.918, 1, 2.956, 1, 2.918, 1, 2.956, 1, 2.918, 0.01, 2.956, 0.33]}, {"time": 3, "color": "ffffff9d", "curve": [3.031, 1, 3.063, 1, 3.031, 1, 3.063, 1, 3.031, 1, 3.063, 1, 3.031, 0.82, 3.063, 1]}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1333, "color": "ffffffff", "curve": [3.211, 1, 3.289, 1, 3.211, 1, 3.289, 1, 3.211, 1, 3.289, 1, 3.211, 1, 3.289, 0.01]}, {"time": 3.3667, "color": "ffffff01", "curve": [3.455, 1, 3.544, 1, 3.455, 1, 3.544, 1, 3.455, 1, 3.544, 1, 3.455, 0.01, 3.544, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.711, 1, 3.789, 1, 3.711, 1, 3.789, 1, 3.711, 1, 3.789, 1, 3.711, 1, 3.789, 0.01]}, {"time": 3.8667, "color": "ffffff01", "curve": [3.918, 1, 3.958, 1, 3.918, 1, 3.958, 1, 3.918, 1, 3.958, 1, 3.918, 0.01, 3.958, 0.34]}, {"time": 4, "color": "ffffff9d"}]}, "glow2": {"rgba": [{"color": "ffffff2d"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff10"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff10"}, {"time": 1, "color": "ffffff2d"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff10"}, {"time": 1.7333, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff10"}, {"time": 2, "color": "ffffff2d"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.4667, "color": "ffffff10"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.9667, "color": "ffffff10"}, {"time": 3, "color": "ffffff2d"}, {"time": 3.2333, "color": "ffffffff"}, {"time": 3.4667, "color": "ffffff10"}, {"time": 3.7333, "color": "ffffffff"}, {"time": 3.9667, "color": "ffffff10"}, {"time": 4, "color": "ffffff2d"}]}, "glow3": {"rgba": [{"color": "ffffff69"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4, "color": "ffffff10"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff10"}, {"time": 1, "color": "ffffff69"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff10"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff10"}, {"time": 2, "color": "ffffff69"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff10"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.9, "color": "ffffff10"}, {"time": 3, "color": "ffffff69"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.4, "color": "ffffff10"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 3.9, "color": "ffffff10"}, {"time": 4, "color": "ffffff69"}]}, "glow4": {"rgba": [{"color": "ffffffff"}, {"time": 0.2333, "color": "ffffff10"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff10"}, {"time": 1, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff10"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff10"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff10"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff10"}, {"time": 3, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff10"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.7333, "color": "ffffff10"}, {"time": 4, "color": "ffffffff"}]}, "glow5": {"rgba": [{"color": "ffffffff"}, {"time": 0.2333, "color": "ffffff74"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff74"}, {"time": 1, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff74"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff74"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff74"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff74"}, {"time": 3, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff74"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.7333, "color": "ffffff74"}, {"time": 4, "color": "ffffffff"}]}}, "bones": {"pumpkin-3": {"scale": [{"x": 0.724, "y": 2.206, "curve": [0.092, 0.712, 0.178, 0.928, 0.173, 2.213, 0.178, 1.713]}, {"time": 0.2667, "x": 0.928, "y": 1.713, "curve": [0.351, 0.928, 0.373, 0.844, 0.364, 1.713, 0.438, 1.956]}, {"time": 0.5, "x": 0.842, "y": 1.872, "curve": [0.599, 0.84, 0.627, 0.936, 0.578, 1.766, 0.669, 1.636]}, {"time": 0.7333, "x": 0.928, "y": 1.713, "curve": [0.876, 0.917, 0.911, 0.831, 0.822, 1.819, 0.911, 1.872]}, {"time": 1, "x": 0.831, "y": 1.872, "curve": [1.094, 0.831, 1.178, 0.928, 1.107, 1.872, 1.178, 1.713]}, {"time": 1.2667, "x": 0.928, "y": 1.713, "curve": [1.361, 0.928, 1.445, 0.861, 1.373, 1.713, 1.444, 1.872]}, {"time": 1.5333, "x": 0.842, "y": 1.872, "curve": [1.632, 0.821, 1.724, 0.836, 1.631, 1.872, 1.69, 1.847]}, {"time": 1.7667, "x": 0.928, "y": 1.713, "curve": [1.864, 1.138, 1.922, 1.902, 1.884, 1.509, 1.89, 0.83]}, {"time": 2, "x": 1.897, "y": 0.831, "curve": [2.11, 1.891, 2.178, 1.451, 2.12, 0.833, 2.178, 1.255]}, {"time": 2.2667, "x": 1.451, "y": 1.255, "curve": [2.368, 1.451, 2.398, 1.596, 2.369, 1.255, 2.441, 1.116]}, {"time": 2.5333, "x": 1.581, "y": 1.117, "curve": [2.625, 1.571, 2.689, 1.451, 2.654, 1.118, 2.689, 1.255]}, {"time": 2.7667, "x": 1.451, "y": 1.255, "curve": [2.858, 1.451, 2.902, 1.579, 2.86, 1.255, 2.912, 1.117]}, {"time": 3, "x": 1.581, "y": 1.117, "curve": [3.101, 1.584, 3.178, 1.451, 3.103, 1.117, 3.178, 1.255]}, {"time": 3.2667, "x": 1.451, "y": 1.255, "curve": [3.368, 1.451, 3.444, 1.581, 3.369, 1.255, 3.444, 1.117]}, {"time": 3.5333, "x": 1.581, "y": 1.117, "curve": [3.625, 1.581, 3.704, 1.544, 3.627, 1.117, 3.709, 1.161]}, {"time": 3.7667, "x": 1.451, "y": 1.255, "curve": [3.85, 1.327, 3.913, 0.746, 3.836, 1.367, 3.922, 2.206]}, {"time": 4, "x": 0.724, "y": 2.206}]}, "pumpkin-4": {"scale": [{"x": 1.484, "y": 0.647, "curve": [0.078, 1.558, 0.152, 1.422, 0.058, 0.579, 0.156, 0.76]}, {"time": 0.2333, "x": 1.362, "y": 0.815, "curve": [0.327, 1.293, 0.412, 1.242, 0.315, 0.873, 0.415, 0.896]}, {"time": 0.5, "x": 1.249, "y": 0.865, "curve": [0.615, 1.259, 0.678, 1.362, 0.589, 0.832, 0.678, 0.815]}, {"time": 0.7667, "x": 1.362, "y": 0.815, "curve": [0.865, 1.362, 0.914, 1.261, 0.849, 0.815, 0.926, 0.892]}, {"time": 1, "x": 1.249, "y": 0.865, "curve": [1.106, 1.235, 1.178, 1.368, 1.089, 0.832, 1.183, 0.78]}, {"time": 1.2667, "x": 1.362, "y": 0.815, "curve": [1.364, 1.354, 1.422, 1.254, 1.344, 0.848, 1.426, 0.892]}, {"time": 1.5, "x": 1.249, "y": 0.865, "curve": [1.607, 1.243, 1.678, 1.362, 1.589, 0.832, 1.678, 0.811]}, {"time": 1.7667, "x": 1.362, "y": 0.815, "curve": [1.96, 1.362, 1.922, 0.931, 1.952, 0.825, 1.922, 2.019]}, {"time": 2, "x": 0.931, "y": 2.019, "curve": [2.089, 0.931, 2.178, 1.093, 2.089, 2.019, 2.178, 1.727]}, {"time": 2.2667, "x": 1.093, "y": 1.727, "curve": [2.345, 1.093, 2.422, 1.034, 2.345, 1.727, 2.422, 1.829]}, {"time": 2.5, "x": 1.034, "y": 1.829, "curve": [2.667, 1.034, 2.678, 1.093, 2.667, 1.829, 2.678, 1.727]}, {"time": 2.7667, "x": 1.093, "y": 1.727, "curve": [2.845, 1.093, 2.834, 1.034, 2.845, 1.727, 2.834, 1.827]}, {"time": 3, "x": 1.034, "y": 1.827, "curve": [3.167, 1.034, 3.178, 1.093, 3.167, 1.827, 3.178, 1.727]}, {"time": 3.2667, "x": 1.093, "y": 1.727, "curve": [3.345, 1.093, 3.334, 1.034, 3.345, 1.727, 3.334, 1.827]}, {"time": 3.5, "x": 1.034, "y": 1.827, "curve": [3.59, 1.034, 3.682, 1.061, 3.59, 1.827, 3.666, 1.803]}, {"time": 3.7667, "x": 1.093, "y": 1.727, "curve": [3.859, 1.128, 3.919, 1.409, 3.808, 1.696, 3.912, 0.694]}, {"time": 4, "x": 1.484, "y": 0.647}]}, "pumpkin-2": {"scale": [{"x": 0.783, "y": 1.371, "curve": [0.092, 0.783, 0.181, 0.846, 0.092, 1.371, 0.181, 1.28]}, {"time": 0.2667, "x": 0.846, "y": 1.28, "curve": [0.347, 0.846, 0.425, 0.783, 0.347, 1.28, 0.425, 1.371]}, {"time": 0.5, "x": 0.783, "y": 1.371, "curve": [0.675, 0.783, 0.681, 0.846, 0.675, 1.371, 0.681, 1.28]}, {"time": 0.7667, "x": 0.846, "y": 1.28, "curve": [0.847, 0.846, 0.84, 0.783, 0.847, 1.28, 0.84, 1.371]}, {"time": 1, "x": 0.783, "y": 1.371, "curve": [1.17, 0.783, 1.181, 0.846, 1.17, 1.371, 1.181, 1.28]}, {"time": 1.2667, "x": 0.846, "y": 1.28, "curve": [1.347, 0.846, 1.335, 0.783, 1.347, 1.28, 1.365, 1.249]}, {"time": 1.5, "x": 0.783, "y": 1.371, "curve": [1.578, 0.783, 1.677, 0.782, 1.582, 1.446, 1.639, 1.536]}, {"time": 1.7333, "x": 0.846, "y": 1.419, "curve": [1.794, 0.915, 1.898, 1.412, 1.791, 1.346, 1.837, 0.871]}, {"time": 2, "x": 1.553, "y": 0.741, "curve": [2.081, 1.665, 2.179, 1.45, 2.086, 0.672, 2.179, 0.871]}, {"time": 2.2667, "x": 1.45, "y": 0.871, "curve": [2.346, 1.45, 2.423, 1.323, 2.346, 0.871, 2.423, 0.933]}, {"time": 2.5, "x": 1.323, "y": 0.933, "curve": [2.669, 1.323, 2.679, 1.45, 2.669, 0.933, 2.679, 0.871]}, {"time": 2.7667, "x": 1.45, "y": 0.871, "curve": [2.846, 1.45, 2.835, 1.323, 2.846, 0.871, 2.835, 0.933]}, {"time": 3, "x": 1.323, "y": 0.933, "curve": [3.169, 1.323, 3.179, 1.45, 3.169, 0.933, 3.179, 0.871]}, {"time": 3.2667, "x": 1.45, "y": 0.871, "curve": [3.346, 1.45, 3.336, 1.323, 3.346, 0.871, 3.336, 0.933]}, {"time": 3.5, "x": 1.323, "y": 0.933, "curve": [3.556, 1.323, 3.579, 1.45, 3.556, 0.933, 3.579, 0.871]}, {"time": 3.6667, "x": 1.45, "y": 0.871, "curve": [3.746, 1.45, 3.889, 0.783, 3.746, 0.871, 3.889, 1.371]}, {"time": 4, "x": 0.783, "y": 1.371}]}, "pumpkin-1": {"scale": [{"x": 1.123, "y": 0.939, "curve": [0.078, 1.179, 0.156, 1.207, 0.08, 0.884, 0.156, 0.897]}, {"time": 0.2333, "x": 1.207, "y": 0.897, "curve": [0.34, 1.207, 0.411, 1.118, 0.327, 0.897, 0.411, 0.943]}, {"time": 0.5, "x": 1.123, "y": 0.939, "curve": [0.591, 1.128, 0.679, 1.221, 0.597, 0.935, 0.678, 0.892]}, {"time": 0.7667, "x": 1.207, "y": 0.897, "curve": [0.862, 1.192, 0.922, 1.123, 0.852, 0.901, 0.915, 0.941]}, {"time": 1, "x": 1.123, "y": 0.939, "curve": [1.107, 1.123, 1.178, 1.214, 1.094, 0.936, 1.184, 0.867]}, {"time": 1.2667, "x": 1.207, "y": 0.897, "curve": [1.372, 1.199, 1.422, 1.119, 1.344, 0.925, 1.422, 0.936]}, {"time": 1.5, "x": 1.123, "y": 0.939, "curve": [1.596, 1.128, 1.656, 1.21, 1.58, 0.942, 1.656, 0.897]}, {"time": 1.7333, "x": 1.207, "y": 0.897, "curve": [1.88, 1.203, 1.911, 0.764, 1.913, 0.897, 1.911, 1.726]}, {"time": 2, "x": 0.764, "y": 1.726, "curve": [2.095, 0.764, 2.185, 0.972, 2.103, 1.726, 2.193, 1.309]}, {"time": 2.2667, "x": 0.939, "y": 1.356, "curve": [2.344, 0.907, 2.422, 0.892, 2.344, 1.406, 2.422, 1.431]}, {"time": 2.5, "x": 0.892, "y": 1.431, "curve": [2.595, 0.892, 2.678, 0.943, 2.603, 1.431, 2.681, 1.334]}, {"time": 2.7667, "x": 0.939, "y": 1.356, "curve": [2.865, 0.934, 2.922, 0.894, 2.865, 1.382, 2.923, 1.44]}, {"time": 3, "x": 0.892, "y": 1.431, "curve": [3.094, 0.889, 3.178, 0.939, 3.104, 1.42, 3.178, 1.356]}, {"time": 3.2667, "x": 0.939, "y": 1.356, "curve": [3.351, 0.939, 3.423, 0.9, 3.361, 1.356, 3.423, 1.42]}, {"time": 3.5, "x": 0.892, "y": 1.431, "curve": [3.594, 0.882, 3.734, 0.89, 3.602, 1.447, 3.743, 1.409]}, {"time": 3.8, "x": 0.939, "y": 1.356, "curve": [3.892, 1.007, 3.94, 1.055, 3.876, 1.286, 3.92, 0.995]}, {"time": 4, "x": 1.123, "y": 0.939}]}, "Meo cuoi bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}}}, "pumpkin_action2": {"slots": {"bg/Pumkin1_light": {"rgba": [{"color": "ffffff0d", "curve": [0.036, 1, 0.068, 1, 0.036, 1, 0.068, 1, 0.036, 1, 0.068, 1, 0.036, 0.16, 0.068, 0.4]}, {"time": 0.1, "color": "ffffff9d", "curve": [0.131, 1, 0.163, 1, 0.131, 1, 0.163, 1, 0.131, 1, 0.163, 1, 0.131, 0.82, 0.163, 1]}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.311, 1, 0.389, 1, 0.311, 1, 0.389, 1, 0.311, 1, 0.389, 1, 0.311, 1, 0.389, 0.01]}, {"time": 0.4667, "color": "ffffff01", "curve": [0.555, 1, 0.644, 1, 0.555, 1, 0.644, 1, 0.555, 1, 0.644, 1, 0.555, 0.01, 0.644, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 1, 0.889, 0.01]}, {"time": 0.9667, "color": "ffffff01", "curve": [0.978, 1, 0.989, 1, 0.978, 1, 0.989, 1, 0.978, 1, 0.989, 1, 0.978, 0, 0.989, 0.02]}, {"time": 1, "color": "ffffff0d", "curve": [1.036, 1, 1.068, 1, 1.036, 1, 1.068, 1, 1.036, 1, 1.068, 1, 1.036, 0.16, 1.068, 0.4]}, {"time": 1.1, "color": "ffffff9d", "curve": [1.131, 1, 1.163, 1, 1.131, 1, 1.163, 1, 1.131, 1, 1.163, 1, 1.131, 0.82, 1.163, 1]}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2333, "color": "ffffffff", "curve": [1.311, 1, 1.389, 1, 1.311, 1, 1.389, 1, 1.311, 1, 1.389, 1, 1.311, 1, 1.389, 0.01]}, {"time": 1.4667, "color": "ffffff01", "curve": [1.555, 1, 1.644, 1, 1.555, 1, 1.644, 1, 1.555, 1, 1.644, 1, 1.555, 0.01, 1.644, 1]}, {"time": 1.7333, "color": "ffffffff", "curve": [1.811, 1, 1.889, 1, 1.811, 1, 1.889, 1, 1.811, 1, 1.889, 1, 1.811, 1, 1.889, 0.01]}, {"time": 1.9667, "color": "ffffff01", "curve": [1.978, 1, 1.989, 1, 1.978, 1, 1.989, 1, 1.978, 1, 1.989, 1, 1.978, 0, 1.989, 0.02]}, {"time": 2, "color": "ffffff0d", "curve": [2.036, 1, 2.068, 1, 2.036, 1, 2.068, 1, 2.036, 1, 2.068, 1, 2.036, 0.16, 2.068, 0.4]}, {"time": 2.1, "color": "ffffff9d", "curve": [2.131, 1, 2.163, 1, 2.131, 1, 2.163, 1, 2.131, 1, 2.163, 1, 2.131, 0.82, 2.163, 1]}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2333, "color": "ffffffff", "curve": [2.311, 1, 2.389, 1, 2.311, 1, 2.389, 1, 2.311, 1, 2.389, 1, 2.311, 1, 2.389, 0.01]}, {"time": 2.4667, "color": "ffffff01", "curve": [2.555, 1, 2.644, 1, 2.555, 1, 2.644, 1, 2.555, 1, 2.644, 1, 2.555, 0.01, 2.644, 1]}, {"time": 2.7333, "color": "ffffffff", "curve": [2.811, 1, 2.889, 1, 2.811, 1, 2.889, 1, 2.811, 1, 2.889, 1, 2.811, 1, 2.889, 0.01]}, {"time": 2.9667, "color": "ffffff01", "curve": [2.978, 1, 2.989, 1, 2.978, 1, 2.989, 1, 2.978, 1, 2.989, 1, 2.978, 0, 2.989, 0.02]}, {"time": 3, "color": "ffffff0d", "curve": [3.036, 1, 3.068, 1, 3.036, 1, 3.068, 1, 3.036, 1, 3.068, 1, 3.036, 0.16, 3.068, 0.4]}, {"time": 3.1, "color": "ffffff9d", "curve": [3.131, 1, 3.163, 1, 3.131, 1, 3.163, 1, 3.131, 1, 3.163, 1, 3.131, 0.82, 3.163, 1]}, {"time": 3.2, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2333, "color": "ffffffff", "curve": [3.311, 1, 3.389, 1, 3.311, 1, 3.389, 1, 3.311, 1, 3.389, 1, 3.311, 1, 3.389, 0.01]}, {"time": 3.4667, "color": "ffffff01", "curve": [3.555, 1, 3.644, 1, 3.555, 1, 3.644, 1, 3.555, 1, 3.644, 1, 3.555, 0.01, 3.644, 1]}, {"time": 3.7333, "color": "ffffffff", "curve": [3.811, 1, 3.889, 1, 3.811, 1, 3.889, 1, 3.811, 1, 3.889, 1, 3.811, 1, 3.889, 0.01]}, {"time": 3.9667, "color": "ffffff01", "curve": [3.978, 1, 3.989, 1, 3.978, 1, 3.989, 1, 3.978, 1, 3.989, 1, 3.978, 0, 3.989, 0.02]}, {"time": 4, "color": "ffffff0d"}]}, "bg/Pumkin2_light": {"rgba": [{"color": "ffffff63", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0.46, 0.022, 0.54]}, {"time": 0.0333, "color": "ffffff9d", "curve": [0.064, 1, 0.096, 1, 0.064, 1, 0.096, 1, 0.064, 1, 0.096, 1, 0.064, 0.82, 0.096, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": [0.245, 1, 0.323, 1, 0.245, 1, 0.323, 1, 0.245, 1, 0.323, 1, 0.245, 1, 0.323, 0.01]}, {"time": 0.4, "color": "ffffff01", "curve": [0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 0.01, 0.578, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": [0.745, 1, 0.823, 1, 0.745, 1, 0.823, 1, 0.745, 1, 0.823, 1, 0.745, 1, 0.823, 0.01]}, {"time": 0.9, "color": "ffffff01", "curve": [0.938, 1, 0.967, 1, 0.938, 1, 0.967, 1, 0.938, 1, 0.967, 1, 0.938, 0.01, 0.967, 0.17]}, {"time": 1, "color": "ffffff63", "curve": [1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 0.46, 1.022, 0.54]}, {"time": 1.0333, "color": "ffffff9d", "curve": [1.064, 1, 1.096, 1, 1.064, 1, 1.096, 1, 1.064, 1, 1.096, 1, 1.064, 0.82, 1.096, 1]}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff", "curve": [1.245, 1, 1.323, 1, 1.245, 1, 1.323, 1, 1.245, 1, 1.323, 1, 1.245, 1, 1.323, 0.01]}, {"time": 1.4, "color": "ffffff01", "curve": [1.489, 1, 1.578, 1, 1.489, 1, 1.578, 1, 1.489, 1, 1.578, 1, 1.489, 0.01, 1.578, 1]}, {"time": 1.6667, "color": "ffffffff", "curve": [1.745, 1, 1.823, 1, 1.745, 1, 1.823, 1, 1.745, 1, 1.823, 1, 1.745, 1, 1.823, 0.01]}, {"time": 1.9, "color": "ffffff01", "curve": [1.938, 1, 1.967, 1, 1.938, 1, 1.967, 1, 1.938, 1, 1.967, 1, 1.938, 0.01, 1.967, 0.17]}, {"time": 2, "color": "ffffff63", "curve": [2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 0.46, 2.022, 0.54]}, {"time": 2.0333, "color": "ffffff9d", "curve": [2.064, 1, 2.096, 1, 2.064, 1, 2.096, 1, 2.064, 1, 2.096, 1, 2.064, 0.82, 2.096, 1]}, {"time": 2.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.245, 1, 2.323, 1, 2.245, 1, 2.323, 1, 2.245, 1, 2.323, 1, 2.245, 1, 2.323, 0.01]}, {"time": 2.4, "color": "ffffff01", "curve": [2.489, 1, 2.578, 1, 2.489, 1, 2.578, 1, 2.489, 1, 2.578, 1, 2.489, 0.01, 2.578, 1]}, {"time": 2.6667, "color": "ffffffff", "curve": [2.745, 1, 2.823, 1, 2.745, 1, 2.823, 1, 2.745, 1, 2.823, 1, 2.745, 1, 2.823, 0.01]}, {"time": 2.9, "color": "ffffff01", "curve": [2.938, 1, 2.967, 1, 2.938, 1, 2.967, 1, 2.938, 1, 2.967, 1, 2.938, 0.01, 2.967, 0.17]}, {"time": 3, "color": "ffffff63", "curve": [3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 0.46, 3.022, 0.54]}, {"time": 3.0333, "color": "ffffff9d", "curve": [3.064, 1, 3.096, 1, 3.064, 1, 3.096, 1, 3.064, 1, 3.096, 1, 3.064, 0.82, 3.096, 1]}, {"time": 3.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff", "curve": [3.245, 1, 3.323, 1, 3.245, 1, 3.323, 1, 3.245, 1, 3.323, 1, 3.245, 1, 3.323, 0.01]}, {"time": 3.4, "color": "ffffff01", "curve": [3.489, 1, 3.578, 1, 3.489, 1, 3.578, 1, 3.489, 1, 3.578, 1, 3.489, 0.01, 3.578, 1]}, {"time": 3.6667, "color": "ffffffff", "curve": [3.745, 1, 3.823, 1, 3.745, 1, 3.823, 1, 3.745, 1, 3.823, 1, 3.745, 1, 3.823, 0.01]}, {"time": 3.9, "color": "ffffff01", "curve": [3.938, 1, 3.969, 1, 3.938, 1, 3.969, 1, 3.938, 1, 3.969, 1, 3.938, 0.01, 3.969, 0.18]}, {"time": 4, "color": "ffffff63"}]}, "bg/Pumkin3_light": {"rgba": [{"color": "fffffff4", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0.98, 0.022, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 0.01]}, {"time": 0.2667, "color": "ffffff01", "curve": [0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 0.01, 0.444, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 0.01]}, {"time": 0.7667, "color": "ffffff01", "curve": [0.855, 1, 0.922, 1, 0.855, 1, 0.922, 1, 0.855, 1, 0.922, 1, 0.855, 0.01, 0.922, 0.77]}, {"time": 1, "color": "fffffff4", "curve": [1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 1, 1.022, 1, 1.011, 0.98, 1.022, 1]}, {"time": 1.0333, "color": "ffffffff", "curve": [1.111, 1, 1.189, 1, 1.111, 1, 1.189, 1, 1.111, 1, 1.189, 1, 1.111, 1, 1.189, 0.01]}, {"time": 1.2667, "color": "ffffff01", "curve": [1.355, 1, 1.444, 1, 1.355, 1, 1.444, 1, 1.355, 1, 1.444, 1, 1.355, 0.01, 1.444, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": [1.611, 1, 1.689, 1, 1.611, 1, 1.689, 1, 1.611, 1, 1.689, 1, 1.611, 1, 1.689, 0.01]}, {"time": 1.7667, "color": "ffffff01", "curve": [1.855, 1, 1.922, 1, 1.855, 1, 1.922, 1, 1.855, 1, 1.922, 1, 1.855, 0.01, 1.922, 0.77]}, {"time": 2, "color": "fffffff4", "curve": [2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 1, 2.022, 1, 2.011, 0.98, 2.022, 1]}, {"time": 2.0333, "color": "ffffffff", "curve": [2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 1, 2.189, 0.01]}, {"time": 2.2667, "color": "ffffff01", "curve": [2.355, 1, 2.444, 1, 2.355, 1, 2.444, 1, 2.355, 1, 2.444, 1, 2.355, 0.01, 2.444, 1]}, {"time": 2.5333, "color": "ffffffff", "curve": [2.611, 1, 2.689, 1, 2.611, 1, 2.689, 1, 2.611, 1, 2.689, 1, 2.611, 1, 2.689, 0.01]}, {"time": 2.7667, "color": "ffffff01", "curve": [2.855, 1, 2.922, 1, 2.855, 1, 2.922, 1, 2.855, 1, 2.922, 1, 2.855, 0.01, 2.922, 0.77]}, {"time": 3, "color": "fffffff4", "curve": [3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 1, 3.022, 1, 3.011, 0.98, 3.022, 1]}, {"time": 3.0333, "color": "ffffffff", "curve": [3.111, 1, 3.189, 1, 3.111, 1, 3.189, 1, 3.111, 1, 3.189, 1, 3.111, 1, 3.189, 0.01]}, {"time": 3.2667, "color": "ffffff01", "curve": [3.355, 1, 3.444, 1, 3.355, 1, 3.444, 1, 3.355, 1, 3.444, 1, 3.355, 0.01, 3.444, 1]}, {"time": 3.5333, "color": "ffffffff", "curve": [3.611, 1, 3.689, 1, 3.611, 1, 3.689, 1, 3.611, 1, 3.689, 1, 3.611, 1, 3.689, 0.01]}, {"time": 3.7667, "color": "ffffff01", "curve": [3.855, 1, 3.911, 1, 3.855, 1, 3.911, 1, 3.855, 1, 3.911, 1, 3.855, 0.01, 3.911, 1]}, {"time": 4, "color": "ffffffff"}]}, "bg/Pumkin4_light": {"rgba": [{"color": "ffffff9d", "curve": [0.031, 1, 0.063, 1, 0.031, 1, 0.063, 1, 0.031, 1, 0.063, 1, 0.031, 0.82, 0.063, 1]}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff", "curve": [0.211, 1, 0.289, 1, 0.211, 1, 0.289, 1, 0.211, 1, 0.289, 1, 0.211, 1, 0.289, 0.01]}, {"time": 0.3667, "color": "ffffff01", "curve": [0.455, 1, 0.544, 1, 0.455, 1, 0.544, 1, 0.455, 1, 0.544, 1, 0.455, 0.01, 0.544, 1]}, {"time": 0.6333, "color": "ffffffff", "curve": [0.711, 1, 0.789, 1, 0.711, 1, 0.789, 1, 0.711, 1, 0.789, 1, 0.711, 1, 0.789, 0.01]}, {"time": 0.8667, "color": "ffffff01", "curve": [0.918, 1, 0.956, 1, 0.918, 1, 0.956, 1, 0.918, 1, 0.956, 1, 0.918, 0.01, 0.956, 0.33]}, {"time": 1, "color": "ffffff9d", "curve": [1.031, 1, 1.063, 1, 1.031, 1, 1.063, 1, 1.031, 1, 1.063, 1, 1.031, 0.82, 1.063, 1]}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff", "curve": [1.211, 1, 1.289, 1, 1.211, 1, 1.289, 1, 1.211, 1, 1.289, 1, 1.211, 1, 1.289, 0.01]}, {"time": 1.3667, "color": "ffffff01", "curve": [1.455, 1, 1.544, 1, 1.455, 1, 1.544, 1, 1.455, 1, 1.544, 1, 1.455, 0.01, 1.544, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": [1.711, 1, 1.789, 1, 1.711, 1, 1.789, 1, 1.711, 1, 1.789, 1, 1.711, 1, 1.789, 0.01]}, {"time": 1.8667, "color": "ffffff01", "curve": [1.918, 1, 1.956, 1, 1.918, 1, 1.956, 1, 1.918, 1, 1.956, 1, 1.918, 0.01, 1.956, 0.33]}, {"time": 2, "color": "ffffff9d", "curve": [2.031, 1, 2.063, 1, 2.031, 1, 2.063, 1, 2.031, 1, 2.063, 1, 2.031, 0.82, 2.063, 1]}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff", "curve": [2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 0.01]}, {"time": 2.3667, "color": "ffffff01", "curve": [2.455, 1, 2.544, 1, 2.455, 1, 2.544, 1, 2.455, 1, 2.544, 1, 2.455, 0.01, 2.544, 1]}, {"time": 2.6333, "color": "ffffffff", "curve": [2.711, 1, 2.789, 1, 2.711, 1, 2.789, 1, 2.711, 1, 2.789, 1, 2.711, 1, 2.789, 0.01]}, {"time": 2.8667, "color": "ffffff01", "curve": [2.918, 1, 2.956, 1, 2.918, 1, 2.956, 1, 2.918, 1, 2.956, 1, 2.918, 0.01, 2.956, 0.33]}, {"time": 3, "color": "ffffff9d", "curve": [3.031, 1, 3.063, 1, 3.031, 1, 3.063, 1, 3.031, 1, 3.063, 1, 3.031, 0.82, 3.063, 1]}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1333, "color": "ffffffff", "curve": [3.211, 1, 3.289, 1, 3.211, 1, 3.289, 1, 3.211, 1, 3.289, 1, 3.211, 1, 3.289, 0.01]}, {"time": 3.3667, "color": "ffffff01", "curve": [3.455, 1, 3.544, 1, 3.455, 1, 3.544, 1, 3.455, 1, 3.544, 1, 3.455, 0.01, 3.544, 1]}, {"time": 3.6333, "color": "ffffffff", "curve": [3.711, 1, 3.789, 1, 3.711, 1, 3.789, 1, 3.711, 1, 3.789, 1, 3.711, 1, 3.789, 0.01]}, {"time": 3.8667, "color": "ffffff01", "curve": [3.918, 1, 3.958, 1, 3.918, 1, 3.958, 1, 3.918, 1, 3.958, 1, 3.918, 0.01, 3.958, 0.34]}, {"time": 4, "color": "ffffff9d"}]}, "glow2": {"rgba": [{"color": "ffffff2d"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff10"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff10"}, {"time": 1, "color": "ffffff2d"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff10"}, {"time": 1.7333, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff10"}, {"time": 2, "color": "ffffff2d"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.4667, "color": "ffffff10"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.9667, "color": "ffffff10"}, {"time": 3, "color": "ffffff2d"}, {"time": 3.2333, "color": "ffffffff"}, {"time": 3.4667, "color": "ffffff10"}, {"time": 3.7333, "color": "ffffffff"}, {"time": 3.9667, "color": "ffffff10"}, {"time": 4, "color": "ffffff2d"}]}, "glow3": {"rgba": [{"color": "ffffff69"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4, "color": "ffffff10"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff10"}, {"time": 1, "color": "ffffff69"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff10"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff10"}, {"time": 2, "color": "ffffff69"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff10"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.9, "color": "ffffff10"}, {"time": 3, "color": "ffffff69"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.4, "color": "ffffff10"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 3.9, "color": "ffffff10"}, {"time": 4, "color": "ffffff69"}]}, "glow4": {"rgba": [{"color": "ffffffff"}, {"time": 0.2333, "color": "ffffff10"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff10"}, {"time": 1, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff10"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff10"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff10"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff10"}, {"time": 3, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff10"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.7333, "color": "ffffff10"}, {"time": 4, "color": "ffffffff"}]}, "glow5": {"rgba": [{"color": "ffffffff"}, {"time": 0.2333, "color": "ffffff74"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff74"}, {"time": 1, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff74"}, {"time": 1.5, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff74"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff74"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff74"}, {"time": 3, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff74"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.7333, "color": "ffffff74"}, {"time": 4, "color": "ffffffff"}]}}, "bones": {"pumpkin-3": {"rotate": [{"value": 15.81, "curve": "stepped"}, {"time": 1.6667, "value": 15.81, "curve": [1.778, 5.63, 1.889, -14.74]}, {"time": 2, "value": -14.74, "curve": "stepped"}, {"time": 3.6667, "value": -14.74, "curve": [3.778, -14.74, 3.889, 15.81]}, {"time": 4, "value": 15.81}], "scale": [{"x": 1.164, "y": 0.95, "curve": [0.083, 1.164, 0.15, 1, 0.083, 0.95, 0.15, 1]}, {"time": 0.2333, "curve": [0.316, 1, 0.416, 1.164, 0.316, 1, 0.416, 0.95]}, {"time": 0.5, "x": 1.164, "y": 0.95, "curve": [0.583, 1.164, 0.65, 1, 0.583, 0.95, 0.65, 1]}, {"time": 0.7333, "curve": [0.816, 1, 0.911, 1.164, 0.816, 1, 0.911, 0.95]}, {"time": 1, "x": 1.164, "y": 0.95, "curve": [1.083, 1.164, 1.15, 1, 1.083, 0.95, 1.15, 1]}, {"time": 1.2333, "curve": [1.316, 1, 1.416, 1.164, 1.316, 1, 1.416, 0.95]}, {"time": 1.5, "x": 1.164, "y": 0.95, "curve": [1.583, 1.164, 1.65, 1, 1.583, 0.95, 1.65, 1]}, {"time": 1.7333, "curve": [1.816, 1, 1.911, 1.164, 1.816, 1, 1.911, 0.95]}, {"time": 2, "x": 1.164, "y": 0.95, "curve": [2.083, 1.164, 2.15, 1, 2.083, 0.95, 2.15, 1]}, {"time": 2.2333, "curve": [2.316, 1, 2.416, 1.164, 2.316, 1, 2.416, 0.95]}, {"time": 2.5, "x": 1.164, "y": 0.95, "curve": [2.583, 1.164, 2.65, 1, 2.583, 0.95, 2.65, 1]}, {"time": 2.7333, "curve": [2.816, 1, 2.911, 1.164, 2.816, 1, 2.911, 0.95]}, {"time": 3, "x": 1.164, "y": 0.95, "curve": [3.083, 1.164, 3.15, 1, 3.083, 0.95, 3.15, 1]}, {"time": 3.2333, "curve": [3.316, 1, 3.416, 1.164, 3.316, 1, 3.416, 0.95]}, {"time": 3.5, "x": 1.164, "y": 0.95, "curve": [3.583, 1.164, 3.65, 1, 3.583, 0.95, 3.65, 1]}, {"time": 3.7333, "curve": [3.816, 1, 3.916, 1.164, 3.816, 1, 3.916, 0.95]}, {"time": 4, "x": 1.164, "y": 0.95}]}, "pumpkin-4": {"rotate": [{"value": 15.81, "curve": "stepped"}, {"time": 1.6667, "value": 15.81, "curve": [1.778, 5.63, 1.889, -14.74]}, {"time": 2, "value": -14.74, "curve": "stepped"}, {"time": 3.6667, "value": -14.74, "curve": [3.778, -14.74, 3.889, 15.81]}, {"time": 4, "value": 15.81}], "scale": [{"x": 1.099, "y": 0.934, "curve": [0.012, 1.102, 0.023, 1.104, 0.012, 0.932, 0.023, 0.931]}, {"time": 0.0333, "x": 1.104, "y": 0.931, "curve": [0.092, 1.104, 0.142, 1.052, 0.092, 0.931, 0.142, 0.965]}, {"time": 0.2, "x": 1.022, "y": 0.985, "curve": [0.226, 1.01, 0.242, 1, 0.226, 0.994, 0.242, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.45, 1.104, 0.35, 1, 0.45, 0.931]}, {"time": 0.5333, "x": 1.104, "y": 0.931, "curve": [0.592, 1.104, 0.642, 1.053, 0.592, 0.931, 0.642, 0.965]}, {"time": 0.7, "x": 1.022, "y": 0.985, "curve": [0.726, 1.01, 0.742, 1, 0.726, 0.994, 0.742, 1]}, {"time": 0.7667, "curve": [0.829, 1, 0.9, 1.057, 0.829, 1, 0.9, 0.962]}, {"time": 0.9667, "x": 1.087, "y": 0.942, "curve": [0.978, 1.092, 0.989, 1.096, 0.978, 0.939, 0.989, 0.936]}, {"time": 1, "x": 1.099, "y": 0.934, "curve": [1.012, 1.102, 1.023, 1.104, 1.012, 0.932, 1.023, 0.931]}, {"time": 1.0333, "x": 1.104, "y": 0.931, "curve": [1.092, 1.104, 1.142, 1.052, 1.092, 0.931, 1.142, 0.965]}, {"time": 1.2, "x": 1.022, "y": 0.985, "curve": [1.226, 1.01, 1.242, 1, 1.226, 0.994, 1.242, 1]}, {"time": 1.2667, "curve": [1.35, 1, 1.45, 1.104, 1.35, 1, 1.45, 0.931]}, {"time": 1.5333, "x": 1.104, "y": 0.931, "curve": [1.592, 1.104, 1.642, 1.053, 1.592, 0.931, 1.642, 0.965]}, {"time": 1.7, "x": 1.022, "y": 0.985, "curve": [1.726, 1.01, 1.742, 1, 1.726, 0.994, 1.742, 1]}, {"time": 1.7667, "curve": [1.829, 1, 1.9, 1.057, 1.829, 1, 1.9, 0.962]}, {"time": 1.9667, "x": 1.087, "y": 0.942, "curve": [1.978, 1.092, 1.989, 1.096, 1.978, 0.939, 1.989, 0.936]}, {"time": 2, "x": 1.099, "y": 0.934, "curve": [2.012, 1.102, 2.023, 1.104, 2.012, 0.932, 2.023, 0.931]}, {"time": 2.0333, "x": 1.104, "y": 0.931, "curve": [2.092, 1.104, 2.142, 1.052, 2.092, 0.931, 2.142, 0.965]}, {"time": 2.2, "x": 1.022, "y": 0.985, "curve": [2.226, 1.01, 2.242, 1, 2.226, 0.994, 2.242, 1]}, {"time": 2.2667, "curve": [2.35, 1, 2.45, 1.104, 2.35, 1, 2.45, 0.931]}, {"time": 2.5333, "x": 1.104, "y": 0.931, "curve": [2.592, 1.104, 2.642, 1.053, 2.592, 0.931, 2.642, 0.965]}, {"time": 2.7, "x": 1.022, "y": 0.985, "curve": [2.726, 1.01, 2.742, 1, 2.726, 0.994, 2.742, 1]}, {"time": 2.7667, "curve": [2.829, 1, 2.9, 1.057, 2.829, 1, 2.9, 0.962]}, {"time": 2.9667, "x": 1.087, "y": 0.942, "curve": [2.978, 1.092, 2.989, 1.096, 2.978, 0.939, 2.989, 0.936]}, {"time": 3, "x": 1.099, "y": 0.934, "curve": [3.012, 1.102, 3.023, 1.104, 3.012, 0.932, 3.023, 0.931]}, {"time": 3.0333, "x": 1.104, "y": 0.931, "curve": [3.092, 1.104, 3.142, 1.052, 3.092, 0.931, 3.142, 0.965]}, {"time": 3.2, "x": 1.022, "y": 0.985, "curve": [3.226, 1.01, 3.242, 1, 3.226, 0.994, 3.242, 1]}, {"time": 3.2667, "curve": [3.35, 1, 3.45, 1.104, 3.35, 1, 3.45, 0.931]}, {"time": 3.5333, "x": 1.104, "y": 0.931, "curve": [3.592, 1.104, 3.642, 1.053, 3.592, 0.931, 3.642, 0.965]}, {"time": 3.7, "x": 1.022, "y": 0.985, "curve": [3.726, 1.01, 3.742, 1, 3.726, 0.994, 3.742, 1]}, {"time": 3.7667, "curve": [3.829, 1, 3.9, 1.057, 3.829, 1, 3.9, 0.962]}, {"time": 3.9667, "x": 1.087, "y": 0.942, "curve": [3.978, 1.092, 3.989, 1.096, 3.978, 0.939, 3.989, 0.936]}, {"time": 4, "x": 1.099, "y": 0.934}]}, "pumpkin-2": {"rotate": [{"value": 15.81, "curve": "stepped"}, {"time": 1.6667, "value": 15.81, "curve": [1.778, 5.63, 1.889, -14.74]}, {"time": 2, "value": -14.74, "curve": "stepped"}, {"time": 3.6667, "value": -14.74, "curve": [3.778, -14.74, 3.889, 15.81]}, {"time": 4, "value": 15.81}], "scale": [{"x": 1.116, "y": 0.964, "curve": [0.034, 1.116, 0.067, 1.097, 0.034, 0.964, 0.067, 0.97]}, {"time": 0.1, "x": 1.075, "y": 0.977, "curve": [0.151, 1.042, 0.217, 1, 0.151, 0.987, 0.217, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.417, 1.116, 0.35, 1, 0.417, 0.964]}, {"time": 0.5, "x": 1.116, "y": 0.964, "curve": [0.534, 1.116, 0.567, 1.098, 0.534, 0.964, 0.567, 0.97]}, {"time": 0.6, "x": 1.075, "y": 0.977, "curve": [0.651, 1.042, 0.717, 1, 0.651, 0.987, 0.717, 1]}, {"time": 0.7667, "curve": [0.802, 1, 0.835, 1.021, 0.802, 1, 0.835, 0.993]}, {"time": 0.8667, "x": 1.045, "y": 0.986, "curve": [0.91, 1.078, 0.956, 1.116, 0.91, 0.976, 0.956, 0.964]}, {"time": 1, "x": 1.116, "y": 0.964, "curve": [1.034, 1.116, 1.067, 1.097, 1.034, 0.964, 1.067, 0.97]}, {"time": 1.1, "x": 1.075, "y": 0.977, "curve": [1.151, 1.042, 1.217, 1, 1.151, 0.987, 1.217, 1]}, {"time": 1.2667, "curve": [1.35, 1, 1.417, 1.116, 1.35, 1, 1.417, 0.964]}, {"time": 1.5, "x": 1.116, "y": 0.964, "curve": [1.534, 1.116, 1.567, 1.098, 1.534, 0.964, 1.567, 0.97]}, {"time": 1.6, "x": 1.075, "y": 0.977, "curve": [1.651, 1.042, 1.717, 1, 1.651, 0.987, 1.717, 1]}, {"time": 1.7667, "curve": [1.802, 1, 1.835, 1.021, 1.802, 1, 1.835, 0.993]}, {"time": 1.8667, "x": 1.045, "y": 0.986, "curve": [1.91, 1.078, 1.956, 1.116, 1.91, 0.976, 1.956, 0.964]}, {"time": 2, "x": 1.116, "y": 0.964, "curve": [2.034, 1.116, 2.067, 1.097, 2.034, 0.964, 2.067, 0.97]}, {"time": 2.1, "x": 1.075, "y": 0.977, "curve": [2.151, 1.042, 2.217, 1, 2.151, 0.987, 2.217, 1]}, {"time": 2.2667, "curve": [2.35, 1, 2.417, 1.116, 2.35, 1, 2.417, 0.964]}, {"time": 2.5, "x": 1.116, "y": 0.964, "curve": [2.534, 1.116, 2.567, 1.098, 2.534, 0.964, 2.567, 0.97]}, {"time": 2.6, "x": 1.075, "y": 0.977, "curve": [2.651, 1.042, 2.717, 1, 2.651, 0.987, 2.717, 1]}, {"time": 2.7667, "curve": [2.802, 1, 2.835, 1.021, 2.802, 1, 2.835, 0.993]}, {"time": 2.8667, "x": 1.045, "y": 0.986, "curve": [2.91, 1.078, 2.956, 1.116, 2.91, 0.976, 2.956, 0.964]}, {"time": 3, "x": 1.116, "y": 0.964, "curve": [3.034, 1.116, 3.067, 1.097, 3.034, 0.964, 3.067, 0.97]}, {"time": 3.1, "x": 1.075, "y": 0.977, "curve": [3.151, 1.042, 3.217, 1, 3.151, 0.987, 3.217, 1]}, {"time": 3.2667, "curve": [3.35, 1, 3.417, 1.116, 3.35, 1, 3.417, 0.964]}, {"time": 3.5, "x": 1.116, "y": 0.964, "curve": [3.534, 1.116, 3.567, 1.098, 3.534, 0.964, 3.567, 0.97]}, {"time": 3.6, "x": 1.075, "y": 0.977, "curve": [3.651, 1.042, 3.717, 1, 3.651, 0.987, 3.717, 1]}, {"time": 3.7667, "curve": [3.802, 1, 3.835, 1.021, 3.802, 1, 3.835, 0.993]}, {"time": 3.8667, "x": 1.045, "y": 0.986, "curve": [3.91, 1.078, 3.952, 1.116, 3.91, 0.976, 3.952, 0.964]}, {"time": 4, "x": 1.116, "y": 0.964}]}, "pumpkin-1": {"rotate": [{"value": 15.81, "curve": "stepped"}, {"time": 1.6667, "value": 15.81, "curve": [1.778, 5.63, 1.889, -14.74]}, {"time": 2, "value": -14.74, "curve": "stepped"}, {"time": 3.6667, "value": -14.74, "curve": [3.778, -14.74, 3.889, 15.81]}, {"time": 4, "value": 15.81}], "scale": [{"x": 1.123, "y": 0.939, "curve": [0.075, 1.123, 0.158, 1.023, 0.075, 0.939, 0.158, 0.988]}, {"time": 0.2333, "x": 1.003, "y": 0.998, "curve": [0.241, 1.001, 0.258, 1, 0.241, 0.999, 0.258, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.416, 1.123, 0.35, 1, 0.416, 0.939]}, {"time": 0.5, "x": 1.123, "y": 0.939, "curve": [0.575, 1.123, 0.658, 1.023, 0.575, 0.939, 0.658, 0.988]}, {"time": 0.7333, "x": 1.003, "y": 0.998, "curve": [0.741, 1.001, 0.758, 1, 0.741, 0.999, 0.758, 1]}, {"time": 0.7667, "curve": [0.85, 1, 0.922, 1.123, 0.85, 1, 0.922, 0.939]}, {"time": 1, "x": 1.123, "y": 0.939, "curve": [1.075, 1.123, 1.158, 1.023, 1.075, 0.939, 1.158, 0.988]}, {"time": 1.2333, "x": 1.003, "y": 0.998, "curve": [1.241, 1.001, 1.258, 1, 1.241, 0.999, 1.258, 1]}, {"time": 1.2667, "curve": [1.35, 1, 1.416, 1.123, 1.35, 1, 1.416, 0.939]}, {"time": 1.5, "x": 1.123, "y": 0.939, "curve": [1.575, 1.123, 1.658, 1.023, 1.575, 0.939, 1.658, 0.988]}, {"time": 1.7333, "x": 1.003, "y": 0.998, "curve": [1.741, 1.001, 1.758, 1, 1.741, 0.999, 1.758, 1]}, {"time": 1.7667, "curve": [1.85, 1, 1.922, 1.123, 1.85, 1, 1.922, 0.939]}, {"time": 2, "x": 1.123, "y": 0.939, "curve": [2.075, 1.123, 2.158, 1.023, 2.075, 0.939, 2.158, 0.988]}, {"time": 2.2333, "x": 1.003, "y": 0.998, "curve": [2.241, 1.001, 2.258, 1, 2.241, 0.999, 2.258, 1]}, {"time": 2.2667, "curve": [2.35, 1, 2.416, 1.123, 2.35, 1, 2.416, 0.939]}, {"time": 2.5, "x": 1.123, "y": 0.939, "curve": [2.575, 1.123, 2.658, 1.023, 2.575, 0.939, 2.658, 0.988]}, {"time": 2.7333, "x": 1.003, "y": 0.998, "curve": [2.741, 1.001, 2.758, 1, 2.741, 0.999, 2.758, 1]}, {"time": 2.7667, "curve": [2.85, 1, 2.922, 1.123, 2.85, 1, 2.922, 0.939]}, {"time": 3, "x": 1.123, "y": 0.939, "curve": [3.075, 1.123, 3.158, 1.023, 3.075, 0.939, 3.158, 0.988]}, {"time": 3.2333, "x": 1.003, "y": 0.998, "curve": [3.241, 1.001, 3.258, 1, 3.241, 0.999, 3.258, 1]}, {"time": 3.2667, "curve": [3.35, 1, 3.416, 1.123, 3.35, 1, 3.416, 0.939]}, {"time": 3.5, "x": 1.123, "y": 0.939, "curve": [3.575, 1.123, 3.658, 1.023, 3.575, 0.939, 3.658, 0.988]}, {"time": 3.7333, "x": 1.003, "y": 0.998, "curve": [3.741, 1.001, 3.758, 1, 3.741, 0.999, 3.758, 1]}, {"time": 3.7667, "curve": [3.85, 1, 3.916, 1.123, 3.85, 1, 3.916, 0.939]}, {"time": 4, "x": 1.123, "y": 0.939}]}, "Meo cuoi bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}}}, "pumpkin_action3": {"slots": {"bg/Pumkin1_light": {"rgba": [{"color": "ffffff5f"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff75"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff24"}, {"time": 1.9, "color": "ffffff00"}, {"time": 2, "color": "ffffff5f"}]}, "bg/Pumkin2_light": {"rgba": [{"color": "ffffffed"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffff75"}, {"time": 1.8667, "color": "ffffffa8"}, {"time": 2, "color": "ffffffed"}]}, "bg/Pumkin3_light": {"rgba": [{"color": "fffffff4", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0.98, 0.022, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 0.01]}, {"time": 0.2667, "color": "ffffff01", "curve": [0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 0.01, 0.444, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 0.01]}, {"time": 0.7667, "color": "ffffff01", "curve": [0.855, 1, 0.944, 1, 0.855, 1, 0.944, 1, 0.855, 1, 0.944, 1, 0.855, 0.01, 0.944, 1]}, {"time": 1.0333, "color": "ffffffff", "curve": [1.111, 1, 1.189, 1, 1.111, 1, 1.189, 1, 1.111, 1, 1.189, 1, 1.111, 1, 1.189, 0.01]}, {"time": 1.2667, "color": "ffffff01", "curve": [1.355, 1, 1.444, 1, 1.355, 1, 1.444, 1, 1.355, 1, 1.444, 1, 1.355, 0.01, 1.444, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": [1.611, 1, 1.689, 1, 1.611, 1, 1.689, 1, 1.611, 1, 1.689, 1, 1.611, 1, 1.689, 0.01]}, {"time": 1.7667, "color": "ffffff01", "curve": [1.8, 1, 1.834, 1, 1.8, 1, 1.834, 1, 1.8, 1, 1.834, 1, 1.8, 0.01, 1.834, 0.15]}, {"time": 1.8667, "color": "ffffff52", "curve": [1.911, 1, 1.956, 1, 1.911, 1, 1.956, 1, 1.911, 1, 1.956, 1, 1.911, 0.56, 1.956, 0.85]}, {"time": 2, "color": "fffffff4"}]}, "bg/Pumkin4_light": {"rgba": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.7333, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffff7f"}, {"time": 2, "color": "ffffff00"}]}, "glow2": {"rgba": [{"time": 0.5333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.1333, "color": "ffffffff"}]}, "glow3": {"rgba": [{"time": 0.4333, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "glow4": {"rgba": [{"time": 0.1, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}]}, "glow5": {"rgba": [{"color": "ffffff7f"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8667, "color": "ffffffff"}, {"time": 2, "color": "ffffff7f"}]}}, "bones": {"pumpkin-1": {"rotate": [{"time": 0.2333}, {"time": 0.4667, "value": 9.17}, {"time": 0.6667, "value": 10.78}, {"time": 0.9, "value": -5.12, "curve": "stepped"}, {"time": 1.1667, "value": -5.12}, {"time": 1.3333, "value": -5.83}, {"time": 1.5333, "value": -0.83}, {"time": 1.5667}], "translate": [{"time": 0.5667, "curve": [0.677, 0, 0.689, 1.63, 0.612, 56.5, 0.689, 122.44]}, {"time": 0.8, "x": 1.63, "y": 122.44, "curve": [0.911, 1.63, 0.922, 0, 0.911, 122.44, 0.96, 66.68]}, {"time": 1.0333}], "scale": [{"x": 0.877, "y": 1.134, "curve": [0.066, 0.892, 0.115, 0.947, 0.066, 1.118, 0.115, 1.058]}, {"time": 0.1667, "x": 0.978, "y": 1.024, "curve": [0.188, 0.991, 0.21, 1, 0.188, 1.01, 0.21, 1]}, {"time": 0.2333}, {"time": 0.4667, "x": 1.12, "y": 0.893}, {"time": 0.5667, "x": 0.801, "y": 1.371}, {"time": 0.7, "x": 0.843, "y": 1.397}, {"time": 0.9, "x": 1.165, "y": 0.902}, {"time": 1.0333, "x": 0.792, "y": 1.341, "curve": [1.089, 0.856, 1.177, 1.085, 1.089, 1.276, 1.177, 0.895]}, {"time": 1.2333, "x": 1.085, "y": 0.895, "curve": [1.289, 1.085, 1.444, 0.878, 1.289, 0.895, 1.444, 1.146]}, {"time": 1.5, "x": 0.878, "y": 1.146, "curve": [1.509, 0.878, 1.52, 0.882, 1.509, 1.146, 1.52, 1.142]}, {"time": 1.5333, "x": 0.888, "y": 1.135, "curve": [1.595, 0.917, 1.687, 1, 1.595, 1.1, 1.687, 1]}, {"time": 1.7333}, {"time": 1.8667, "x": 0.927, "y": 1.079}, {"time": 1.9667, "x": 0.873, "y": 1.139, "curve": [1.979, 0.873, 1.99, 0.875, 1.979, 1.139, 1.99, 1.137]}, {"time": 2, "x": 0.877, "y": 1.134}]}, "pumpkin-2": {"rotate": [{"time": 0.1}, {"time": 0.3333, "value": 9.17}, {"time": 0.5333, "value": 10.78}, {"time": 0.7667, "value": -5.12}, {"time": 1.0333, "value": 5.12}, {"time": 1.2, "value": -5.83}, {"time": 1.4333}], "translate": [{"time": 0.4333, "curve": [0.544, 0, 0.555, 1.63, 0.479, 56.5, 0.555, 122.44]}, {"time": 0.6667, "x": 1.63, "y": 122.44, "curve": [0.777, 1.63, 0.789, 0, 0.777, 122.44, 0.827, 66.68]}, {"time": 0.9}], "scale": [{"x": 0.955, "y": 1.049, "curve": [0.011, 0.963, 0.022, 0.971, 0.011, 1.04, 0.022, 1.031]}, {"time": 0.0333, "x": 0.978, "y": 1.024, "curve": [0.055, 0.991, 0.077, 1, 0.055, 1.01, 0.077, 1]}, {"time": 0.1}, {"time": 0.3333, "x": 1.12, "y": 0.893}, {"time": 0.4333, "x": 0.801, "y": 1.371}, {"time": 0.5667, "x": 0.843, "y": 1.397}, {"time": 0.7667, "x": 1.165, "y": 0.902}, {"time": 0.9, "x": 0.792, "y": 1.341, "curve": [0.955, 0.856, 1.044, 1.085, 0.955, 1.276, 1.044, 0.895]}, {"time": 1.1, "x": 1.085, "y": 0.895, "curve": [1.155, 1.085, 1.311, 0.878, 1.155, 0.895, 1.311, 1.146]}, {"time": 1.3667, "x": 0.878, "y": 1.146, "curve": [1.405, 0.878, 1.475, 0.936, 1.405, 1.146, 1.475, 1.076]}, {"time": 1.5333, "x": 0.972, "y": 1.033, "curve": [1.559, 0.988, 1.583, 1, 1.559, 1.014, 1.583, 1]}, {"time": 1.6}, {"time": 1.7, "x": 0.946, "y": 1.06}, {"time": 1.8333, "x": 0.873, "y": 1.139, "curve": [1.845, 0.873, 1.856, 0.875, 1.845, 1.139, 1.856, 1.137]}, {"time": 1.8667, "x": 0.877, "y": 1.134, "curve": [1.918, 0.889, 1.96, 0.925, 1.918, 1.121, 1.96, 1.082]}, {"time": 2, "x": 0.955, "y": 1.049}]}, "pumpkin-3": {"rotate": [{"value": -8.51}, {"time": 0.2, "value": 5.12, "curve": "stepped"}, {"time": 0.4667, "value": 5.12}, {"time": 0.6333, "value": -5.83}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.5333}, {"time": 1.7667, "value": -9.17}, {"time": 1.8667, "value": -9.97}, {"time": 1.9667, "value": -10.78}, {"time": 2, "value": -8.51}], "translate": [{"x": 1.03, "y": 102.7, "curve": [0.026, 1.36, 0.055, 1.63, 0.03, 114.56, 0.063, 122.44]}, {"time": 0.1, "x": 1.63, "y": 122.44, "curve": [0.211, 1.63, 0.222, 0, 0.211, 122.44, 0.26, 66.68]}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.8667, "curve": [1.932, 0, 1.963, 0.57, 1.897, 37.67, 1.941, 78.97]}, {"time": 2, "x": 1.03, "y": 102.7}], "scale": [{"x": 0.843, "y": 1.397}, {"time": 0.2, "x": 1.165, "y": 0.902}, {"time": 0.3333, "x": 0.792, "y": 1.341, "curve": [0.389, 0.856, 0.477, 1.085, 0.389, 1.276, 0.477, 0.895]}, {"time": 0.5333, "x": 1.085, "y": 0.895, "curve": [0.589, 1.085, 0.744, 0.878, 0.589, 0.895, 0.744, 1.146]}, {"time": 0.8, "x": 0.878, "y": 1.146, "curve": [0.855, 0.878, 0.977, 1, 0.855, 1.146, 0.977, 1]}, {"time": 1.0333}, {"time": 1.2667, "x": 0.873, "y": 1.139, "curve": [1.377, 0.873, 1.444, 1, 1.377, 1.139, 1.444, 1]}, {"time": 1.5333}, {"time": 1.7667, "x": 1.12, "y": 0.893}, {"time": 1.8667, "x": 0.801, "y": 1.371}, {"time": 2, "x": 0.843, "y": 1.397}]}, "pumpkin-4": {"rotate": [{"value": -7.86}, {"time": 0.0333, "value": -9.17}, {"time": 0.2333, "value": -10.78}, {"time": 0.4667, "value": 5.12, "curve": "stepped"}, {"time": 0.7333, "value": 5.12}, {"time": 0.9, "value": -5.83}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.8}, {"time": 1.8667, "value": -2.62}, {"time": 2, "value": -7.86}], "translate": [{"time": 0.1333, "curve": [0.244, 0, 0.255, 1.63, 0.179, 56.5, 0.255, 122.44]}, {"time": 0.3667, "x": 1.63, "y": 122.44, "curve": [0.477, 1.63, 0.489, 0, 0.477, 122.44, 0.527, 66.68]}, {"time": 0.6}], "scale": [{"x": 1.103, "y": 0.908}, {"time": 0.0333, "x": 1.12, "y": 0.893}, {"time": 0.1333, "x": 0.801, "y": 1.371}, {"time": 0.2667, "x": 0.843, "y": 1.397}, {"time": 0.4667, "x": 1.165, "y": 0.902}, {"time": 0.6, "x": 0.792, "y": 1.341, "curve": [0.655, 0.856, 0.744, 1.085, 0.655, 1.276, 0.744, 0.895]}, {"time": 0.8, "x": 1.085, "y": 0.895, "curve": [0.855, 1.085, 1.011, 0.878, 0.855, 0.895, 1.011, 1.146]}, {"time": 1.0667, "x": 0.878, "y": 1.146, "curve": [1.122, 0.878, 1.244, 1, 1.122, 1.146, 1.244, 1]}, {"time": 1.3}, {"time": 1.5333, "x": 0.873, "y": 1.139, "curve": [1.615, 0.873, 1.673, 0.942, 1.615, 1.139, 1.673, 1.064]}, {"time": 1.7333, "x": 0.978, "y": 1.024, "curve": [1.755, 0.991, 1.777, 1, 1.755, 1.01, 1.777, 1]}, {"time": 1.8}, {"time": 1.8667, "x": 1.034, "y": 0.969}, {"time": 2, "x": 1.103, "y": 0.908}]}, "Meo cuoi bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}}}, "pumpkin_idle": {"slots": {"bg/Pumkin1_light": {"rgba": [{"color": "ffffff0d", "curve": [0.036, 1, 0.068, 1, 0.036, 1, 0.068, 1, 0.036, 1, 0.068, 1, 0.036, 0.16, 0.068, 0.4]}, {"time": 0.1, "color": "ffffff9d", "curve": [0.131, 1, 0.163, 1, 0.131, 1, 0.163, 1, 0.131, 1, 0.163, 1, 0.131, 0.82, 0.163, 1]}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": [0.311, 1, 0.389, 1, 0.311, 1, 0.389, 1, 0.311, 1, 0.389, 1, 0.311, 1, 0.389, 0.01]}, {"time": 0.4667, "color": "ffffff01", "curve": [0.555, 1, 0.644, 1, 0.555, 1, 0.644, 1, 0.555, 1, 0.644, 1, 0.555, 0.01, 0.644, 1]}, {"time": 0.7333, "color": "ffffffff", "curve": [0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 1, 0.889, 0.01]}, {"time": 0.9667, "color": "ffffff01", "curve": [0.978, 1, 0.989, 1, 0.978, 1, 0.989, 1, 0.978, 1, 0.989, 1, 0.978, 0, 0.989, 0.02]}, {"time": 1, "color": "ffffff0d"}]}, "bg/Pumkin2_light": {"rgba": [{"color": "ffffff63", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0.46, 0.022, 0.54]}, {"time": 0.0333, "color": "ffffff9d", "curve": [0.064, 1, 0.096, 1, 0.064, 1, 0.096, 1, 0.064, 1, 0.096, 1, 0.064, 0.82, 0.096, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": [0.245, 1, 0.323, 1, 0.245, 1, 0.323, 1, 0.245, 1, 0.323, 1, 0.245, 1, 0.323, 0.01]}, {"time": 0.4, "color": "ffffff01", "curve": [0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 0.01, 0.578, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": [0.745, 1, 0.823, 1, 0.745, 1, 0.823, 1, 0.745, 1, 0.823, 1, 0.745, 1, 0.823, 0.01]}, {"time": 0.9, "color": "ffffff01", "curve": [0.938, 1, 0.969, 1, 0.938, 1, 0.969, 1, 0.938, 1, 0.969, 1, 0.938, 0.01, 0.969, 0.18]}, {"time": 1, "color": "ffffff63"}]}, "bg/Pumkin3_light": {"rgba": [{"color": "fffffff4", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0.98, 0.022, 1]}, {"time": 0.0333, "color": "ffffffff", "curve": [0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 1, 0.111, 1, 0.189, 0.01]}, {"time": 0.2667, "color": "ffffff01", "curve": [0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 1, 0.444, 1, 0.355, 0.01, 0.444, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": [0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 1, 0.611, 1, 0.689, 0.01]}, {"time": 0.7667, "color": "ffffff01", "curve": [0.855, 1, 0.911, 1, 0.855, 1, 0.911, 1, 0.855, 1, 0.911, 1, 0.855, 0.01, 0.911, 1]}, {"time": 1, "color": "ffffffff"}]}, "bg/Pumkin4_light": {"rgba": [{"color": "ffffff9d", "curve": [0.031, 1, 0.063, 1, 0.031, 1, 0.063, 1, 0.031, 1, 0.063, 1, 0.031, 0.82, 0.063, 1]}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff", "curve": [0.211, 1, 0.289, 1, 0.211, 1, 0.289, 1, 0.211, 1, 0.289, 1, 0.211, 1, 0.289, 0.01]}, {"time": 0.3667, "color": "ffffff01", "curve": [0.455, 1, 0.544, 1, 0.455, 1, 0.544, 1, 0.455, 1, 0.544, 1, 0.455, 0.01, 0.544, 1]}, {"time": 0.6333, "color": "ffffffff", "curve": [0.711, 1, 0.789, 1, 0.711, 1, 0.789, 1, 0.711, 1, 0.789, 1, 0.711, 1, 0.789, 0.01]}, {"time": 0.8667, "color": "ffffff01", "curve": [0.918, 1, 0.958, 1, 0.918, 1, 0.958, 1, 0.918, 1, 0.958, 1, 0.918, 0.01, 0.958, 0.34]}, {"time": 1, "color": "ffffff9d"}]}, "glow2": {"rgba": [{"color": "ffffff2d"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff10"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff10"}, {"time": 1, "color": "ffffff2d"}]}, "glow3": {"rgba": [{"color": "ffffff69"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4, "color": "ffffff10"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff10"}, {"time": 1, "color": "ffffff69"}]}, "glow4": {"rgba": [{"color": "ffffffff"}, {"time": 0.2333, "color": "ffffff10"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff10"}, {"time": 1, "color": "ffffffff"}]}, "glow5": {"rgba": [{"color": "ffffffff"}, {"time": 0.2333, "color": "ffffff74"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff74"}, {"time": 1, "color": "ffffffff"}]}}, "bones": {"pumpkin-3": {"scale": [{"x": 1.164, "y": 0.95, "curve": [0.083, 1.164, 0.15, 1, 0.083, 0.95, 0.15, 1]}, {"time": 0.2333, "curve": [0.316, 1, 0.416, 1.164, 0.316, 1, 0.416, 0.95]}, {"time": 0.5, "x": 1.164, "y": 0.95, "curve": [0.583, 1.164, 0.65, 1, 0.583, 0.95, 0.65, 1]}, {"time": 0.7333, "curve": [0.816, 1, 0.916, 1.164, 0.816, 1, 0.916, 0.95]}, {"time": 1, "x": 1.164, "y": 0.95}]}, "pumpkin-4": {"scale": [{"x": 1.099, "y": 0.934, "curve": [0.012, 1.102, 0.023, 1.104, 0.012, 0.932, 0.023, 0.931]}, {"time": 0.0333, "x": 1.104, "y": 0.931, "curve": [0.092, 1.104, 0.142, 1.052, 0.092, 0.931, 0.142, 0.965]}, {"time": 0.2, "x": 1.022, "y": 0.985, "curve": [0.226, 1.01, 0.242, 1, 0.226, 0.994, 0.242, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.45, 1.104, 0.35, 1, 0.45, 0.931]}, {"time": 0.5333, "x": 1.104, "y": 0.931, "curve": [0.592, 1.104, 0.642, 1.053, 0.592, 0.931, 0.642, 0.965]}, {"time": 0.7, "x": 1.022, "y": 0.985, "curve": [0.726, 1.01, 0.742, 1, 0.726, 0.994, 0.742, 1]}, {"time": 0.7667, "curve": [0.829, 1, 0.9, 1.057, 0.829, 1, 0.9, 0.962]}, {"time": 0.9667, "x": 1.087, "y": 0.942, "curve": [0.978, 1.092, 0.989, 1.096, 0.978, 0.939, 0.989, 0.936]}, {"time": 1, "x": 1.099, "y": 0.934}]}, "pumpkin-2": {"scale": [{"x": 1.116, "y": 0.964, "curve": [0.034, 1.116, 0.067, 1.097, 0.034, 0.964, 0.067, 0.97]}, {"time": 0.1, "x": 1.075, "y": 0.977, "curve": [0.151, 1.042, 0.217, 1, 0.151, 0.987, 0.217, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.417, 1.116, 0.35, 1, 0.417, 0.964]}, {"time": 0.5, "x": 1.116, "y": 0.964, "curve": [0.534, 1.116, 0.567, 1.098, 0.534, 0.964, 0.567, 0.97]}, {"time": 0.6, "x": 1.075, "y": 0.977, "curve": [0.651, 1.042, 0.717, 1, 0.651, 0.987, 0.717, 1]}, {"time": 0.7667, "curve": [0.802, 1, 0.835, 1.021, 0.802, 1, 0.835, 0.993]}, {"time": 0.8667, "x": 1.045, "y": 0.986, "curve": [0.91, 1.078, 0.952, 1.116, 0.91, 0.976, 0.952, 0.964]}, {"time": 1, "x": 1.116, "y": 0.964}]}, "pumpkin-1": {"scale": [{"x": 1.123, "y": 0.939, "curve": [0.075, 1.123, 0.158, 1.023, 0.075, 0.939, 0.158, 0.988]}, {"time": 0.2333, "x": 1.003, "y": 0.998, "curve": [0.241, 1.001, 0.258, 1, 0.241, 0.999, 0.258, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.416, 1.123, 0.35, 1, 0.416, 0.939]}, {"time": 0.5, "x": 1.123, "y": 0.939, "curve": [0.575, 1.123, 0.658, 1.023, 0.575, 0.939, 0.658, 0.988]}, {"time": 0.7333, "x": 1.003, "y": 0.998, "curve": [0.741, 1.001, 0.758, 1, 0.741, 0.999, 0.758, 1]}, {"time": 0.7667, "curve": [0.85, 1, 0.916, 1.123, 0.85, 1, 0.916, 0.939]}, {"time": 1, "x": 1.123, "y": 0.939}]}, "Meo cuoi bi ngo": {"scale": [{"x": 0, "y": 0}]}, "Hinh nom ma": {"scale": [{"x": 0, "y": 0}]}}}}}