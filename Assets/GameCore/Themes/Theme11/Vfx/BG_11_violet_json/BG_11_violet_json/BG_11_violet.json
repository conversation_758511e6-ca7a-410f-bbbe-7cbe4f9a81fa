{"skeleton": {"hash": "PToAMDNz4l4", "spine": "4.0.62", "x": -550.7, "y": -88.21, "width": 1247.49, "height": 1200.88, "images": "./image/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -395.51, "y": 474.6}, {"name": "cow_body", "parent": "bone", "rotation": -1.31, "x": -2.33, "y": 43.88}, {"name": "cow_leg_01", "parent": "cow_body", "length": 56.5, "rotation": -136.51, "x": -39.51, "y": 21.14}, {"name": "cow_leg_02", "parent": "cow_leg_01", "length": 29.2, "rotation": 16.25, "x": 57.5, "y": 0.03}, {"name": "cow_leg_03", "parent": "cow_body", "length": 27.78, "rotation": -126.92, "x": -42.68, "y": 12.98}, {"name": "cow_leg_04", "parent": "cow_leg_03", "length": 34.12, "rotation": 54.33, "x": 27.76, "y": -0.2}, {"name": "cow_leg_05", "parent": "bone", "length": 16.04, "rotation": -40.82, "x": -43.85, "y": 2.38, "color": "ff3f00ff"}, {"name": "cow_leg_06", "parent": "cow_body", "length": 39.72, "rotation": -107.7, "x": 47.91, "y": 16.02}, {"name": "cow_leg_07", "parent": "cow_leg_06", "length": 19.56, "rotation": -0.68, "x": 39.57, "y": 0.07}, {"name": "cow_leg_08", "parent": "bone", "length": 8.46, "rotation": -70.97, "x": 35.98, "y": 2.1, "color": "ff3f00ff"}, {"name": "cow_leg_09", "parent": "cow_body", "length": 46.73, "rotation": -48.59, "x": 60.88, "y": 28.71}, {"name": "cow_leg_010", "parent": "cow_leg_09", "length": 21.47, "rotation": -78.57, "x": 46.93, "y": -0.18}, {"name": "cow_leg_011", "parent": "bone", "length": 16.28, "rotation": -180, "x": 82.48, "y": 19.42, "color": "ff3f00ff"}, {"name": "cow_head", "parent": "cow_body", "length": 53.77, "rotation": -15.55, "x": 98.29, "y": 87.89}, {"name": "cow_tile", "parent": "cow_body", "rotation": 3, "x": -59.84, "y": 63.81}, {"name": "bone2", "parent": "root", "x": 413.37, "y": 469.52}, {"name": "car", "parent": "bone2", "x": -3.79, "y": 32.49}, {"name": "car2", "parent": "bone2", "x": -4.12, "y": 45.49}, {"name": "Asset 7", "parent": "root", "x": 606.34, "y": 476.57}, {"name": "Asset 3_b", "parent": "Asset 7", "length": 38.74, "rotation": -3.85, "x": -20.49, "y": 58.25}, {"name": "Asset 3_a", "parent": "Asset 7", "length": 54.19, "rotation": -169.26, "x": 0.33, "y": 134.49}, {"name": "Asset 8", "parent": "Asset 7", "x": 14.98, "y": 167.02}, {"name": "Asset 9", "parent": "Asset 7", "x": -68.33, "y": 162.21, "color": "abe323ff"}, {"name": "target", "parent": "bone", "x": -97.9, "y": 1.29, "color": "ff3f00ff"}, {"name": "cow_tile3", "parent": "cow_tile"}, {"name": "cow_tile4", "parent": "cow_tile3", "length": 8.03, "rotation": 162.38, "x": -0.33, "y": 0.79}, {"name": "cow_tile5", "parent": "cow_tile4", "length": 10.28, "rotation": 13.61, "x": 8.32, "y": 0.13}, {"name": "cow_tile6", "parent": "cow_tile5", "length": 10.51, "rotation": 17.04, "x": 10.75, "y": 0.08}, {"name": "cow_tile7", "parent": "cow_tile6", "length": 8.58, "rotation": 13.09, "x": 10.9, "y": -0.04}, {"name": "cow_tile8", "parent": "cow_tile7", "length": 8.35, "rotation": 9.91, "x": 8.45, "y": -0.04}, {"name": "cow_tile9", "parent": "cow_tile8", "length": 9.55, "rotation": -2.08, "x": 8.47, "y": 0.15}, {"name": "cow_tile10", "parent": "cow_tile9", "length": 7.05, "rotation": -8.87, "x": 9.51, "y": -0.09}, {"name": "cow_tile11", "parent": "cow_tile10", "length": 8.23, "rotation": -14.12, "x": 6.56, "y": 0.13}, {"name": "cow_tile12", "parent": "cow_tile11", "length": 7.44, "rotation": -12.01, "x": 8.24, "y": 0.34}, {"name": "cow_tile13", "parent": "cow_tile12", "length": 5.85, "rotation": -6.06, "x": 7.29, "y": 0.01}, {"name": "cow_tile14", "parent": "cow_tile13", "length": 5.73, "rotation": -11.16, "x": 5.85}, {"name": "dot", "parent": "root", "x": -10.13, "y": 905.77}, {"name": "bone3", "parent": "dot", "x": 581.25, "y": -372.02, "scaleX": 1.1702, "scaleY": 1.1702}, {"name": "bone4", "parent": "dot", "x": 606.15, "y": -380.32, "scaleX": 1.3461, "scaleY": 1.3461}, {"name": "star_all", "parent": "root", "x": 38.24, "y": 817.72}, {"name": "bone5", "parent": "star_all", "x": 381.36, "y": 4.42}, {"name": "star_all3", "parent": "star_all", "x": -191.93, "y": 109.96, "scaleX": 1.7092, "scaleY": 1.7092}, {"name": "bone7", "parent": "star_all", "x": -438.04, "y": 254.21, "scaleX": 0.776, "scaleY": 0.776}, {"name": "bone8", "parent": "star_all", "x": -496.9, "y": -35.97, "scaleX": 0.4282, "scaleY": 0.4282}, {"name": "bone9", "parent": "dot", "x": 471.17, "y": 121.22, "scaleX": 0.6524, "scaleY": 0.6524}, {"name": "bone10", "parent": "dot", "x": 383.39, "y": 141.46, "scaleX": 0.2809, "scaleY": 0.2809}, {"name": "bone11", "parent": "dot", "x": 383.84, "y": 34.81, "scaleX": 0.5164, "scaleY": 0.5164}, {"name": "bone12", "parent": "dot", "x": 283.21, "y": 24.47, "scaleX": 0.2517, "scaleY": 0.2517}, {"name": "bone13", "parent": "dot", "x": 134.96, "y": 129.56, "scaleX": 0.3307, "scaleY": 0.3307}, {"name": "bone14", "parent": "dot", "x": 114.23, "y": 106.4, "scaleX": 0.2938, "scaleY": 0.2938}, {"name": "bone15", "parent": "dot", "x": 8.99, "y": 134.33, "scaleX": 0.2775, "scaleY": 0.2775}, {"name": "bone16", "parent": "dot", "x": -257.54, "y": 126.25, "scaleX": 0.5834, "scaleY": 0.5834}, {"name": "bone17", "parent": "dot", "x": -238.42, "y": 50.08, "scaleX": 0.2656, "scaleY": 0.2656}, {"name": "bone18", "parent": "dot", "x": -337.82, "y": 93.71, "scaleX": 0.2656, "scaleY": 0.2656}, {"name": "bone19", "parent": "dot", "x": -322.95, "y": -34.37, "scaleX": 0.5289, "scaleY": 0.5289}, {"name": "bone20", "parent": "dot", "x": -241.28, "y": -189.87, "scaleX": 0.269, "scaleY": 0.269}, {"name": "bone6", "parent": "cow_tile3", "x": 443.55, "y": -600.14}, {"name": "bone21", "parent": "bone6", "length": 46.64, "rotation": 174.41, "x": -438.34, "y": 596.58}, {"name": "bone22", "parent": "bone21", "length": 36.5, "rotation": 106.94, "x": 47.33, "y": -0.05}, {"name": "target2", "parent": "root", "x": -494.62, "y": 557.42, "color": "ff3f00ff"}], "slots": [{"name": "car", "bone": "car", "attachment": "car"}, {"name": "lamp", "bone": "car2", "attachment": "lamp", "blend": "screen"}, {"name": "Asset 7", "bone": "Asset 7", "attachment": "Asset 3"}, {"name": "Asset 3_a", "bone": "Asset 3_a", "attachment": "Asset 3_a"}, {"name": "Asset 3_b", "bone": "Asset 3_b", "attachment": "Asset 3_b"}, {"name": "cow_tile", "bone": "cow_tile", "attachment": "cow_tile"}, {"name": "cow_body", "bone": "cow_body", "attachment": "cow_body"}, {"name": "cow_leg_01", "bone": "cow_leg_01", "attachment": "cow_leg_01"}, {"name": "cow_head", "bone": "cow_head", "attachment": "cow_head"}, {"name": "cow_leg_02", "bone": "cow_leg_03", "attachment": "cow_leg_02"}, {"name": "cow_leg_03", "bone": "cow_leg_06", "attachment": "cow_leg_03"}, {"name": "cow_leg_04", "bone": "cow_leg_09", "attachment": "cow_leg_04"}, {"name": "cow_tile2", "bone": "cow_tile", "attachment": "cow_tile2"}, {"name": "glow", "bone": "bone3", "color": "fc10c18a", "attachment": "glow", "blend": "additive"}, {"name": "glow2", "bone": "bone4", "color": "fc10ab8a", "attachment": "glow", "blend": "additive"}, {"name": "star", "bone": "bone5", "attachment": "star", "blend": "additive"}, {"name": "star3", "bone": "bone7", "attachment": "star", "blend": "additive"}, {"name": "star4", "bone": "bone8", "attachment": "star", "blend": "additive"}, {"name": "star2", "bone": "star_all3", "attachment": "star", "blend": "additive"}, {"name": "dot", "bone": "bone9", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot2", "bone": "bone10", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot3", "bone": "bone11", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot4", "bone": "bone12", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot5", "bone": "bone13", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot6", "bone": "bone14", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot7", "bone": "bone15", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot8", "bone": "bone16", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot9", "bone": "bone17", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot10", "bone": "bone18", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot11", "bone": "bone19", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}, {"name": "dot12", "bone": "bone20", "color": "ec6ef6a2", "attachment": "dot", "blend": "additive"}], "ik": [{"name": "cow_leg_05", "order": 1, "bones": ["cow_leg_03", "cow_leg_04"], "target": "cow_leg_05"}, {"name": "cow_leg_08", "order": 2, "bones": ["cow_leg_06", "cow_leg_07"], "target": "cow_leg_08", "bendPositive": false}, {"name": "cow_leg_011", "order": 3, "bones": ["cow_leg_09", "cow_leg_010"], "target": "cow_leg_011", "bendPositive": false}, {"name": "target", "bones": ["cow_leg_01", "cow_leg_02"], "target": "target"}, {"name": "target2", "order": 4, "bones": ["bone21", "bone22"], "target": "target2"}], "transform": [{"name": "Asset 9", "order": 6, "bones": ["Asset 8"], "target": "Asset 9", "x": 83.31, "y": 4.54, "mixRotate": 0, "mixX": -1, "mixScaleX": 0, "mixShearY": 0}], "path": [{"name": "cow_tile2", "order": 5, "bones": ["cow_tile4", "cow_tile5", "cow_tile6", "cow_tile7", "cow_tile8", "cow_tile9", "cow_tile10", "cow_tile11", "cow_tile12", "cow_tile13", "cow_tile14"], "target": "cow_tile2"}], "skins": [{"name": "default", "attachments": {"Asset 3_a": {"Asset 3_a": {"x": 26.98, "y": 1.89, "scaleX": 1.0697, "scaleY": 1.0697, "rotation": 169.26, "width": 54, "height": 54}}, "Asset 3_b": {"Asset 3_b": {"x": 19.05, "y": -0.31, "scaleX": 1.17, "scaleY": 1.17, "rotation": 3.85, "width": 33, "height": 28}}, "Asset 7": {"Asset 3": {"type": "mesh", "uvs": [0.41057, 0, 0.52884, 0, 1, 0.29186, 1, 0.3318, 0.54123, 0.33726, 0.56845, 0.52139, 0.71583, 0.65489, 0.78466, 0.75044, 0.84959, 1, 0.71722, 1, 0.66448, 0.75879, 0.60627, 0.66844, 0.4867, 0.55706, 0.446, 0.33827, 0, 0.34603, 0, 0.32133], "triangles": [12, 5, 6, 11, 12, 6, 10, 11, 6, 7, 10, 6, 9, 10, 7, 9, 7, 8, 4, 1, 2, 3, 4, 2, 13, 1, 4, 13, 0, 1, 15, 0, 13, 14, 15, 13, 5, 12, 13, 5, 13, 4], "vertices": [1, 23, 32.98, 84.64, 1, 1, 22, -39.12, 80.06, 1, 2, 23, 88.51, 11.64, 0.00756, 22, 5.2, 7.1, 0.99244, 1, 22, 5.16, -2.86, 1, 3, 23, 44.98, 0.52, 0.44677, 22, -38.33, -4.02, 0.49756, 19, -23.35, 162.73, 0.05567, 3, 23, 47.35, -45.39, 0.29527, 22, -35.96, -49.93, 0.22664, 19, -20.98, 116.82, 0.47809, 3, 23, 61.16, -78.73, 0.16045, 22, -22.15, -83.27, 0.10909, 19, -7.17, 83.48, 0.73046, 3, 23, 67.58, -102.58, 0.08828, 22, -15.73, -107.12, 0.05608, 19, -0.76, 59.63, 0.85564, 1, 19, 5.11, -2.6, 1, 3, 23, 60.9, -164.76, 2e-05, 22, -22.41, -169.29, 1e-05, 19, -7.43, -2.55, 0.99998, 3, 23, 56.18, -104.61, 0.08812, 22, -27.13, -109.14, 0.05599, 19, -12.16, 57.6, 0.85589, 3, 23, 50.76, -82.06, 0.16081, 22, -32.55, -86.6, 0.1094, 19, -17.57, 80.15, 0.72979, 3, 23, 39.56, -54.25, 0.2669, 22, -43.75, -58.78, 0.1995, 19, -28.77, 107.96, 0.53359, 3, 23, 35.95, 0.31, 0.53693, 22, -47.36, -4.23, 0.40891, 19, -32.38, 162.52, 0.05416, 1, 23, -6.33, -1.43, 1, 1, 23, -6.3, 4.72, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 16, 18, 20, 22, 22, 24, 28, 30, 6, 8, 26, 28, 30, 0, 8, 10, 24, 26, 18, 20, 14, 16], "width": 73, "height": 192}}, "car": {"car": {"type": "mesh", "uvs": [0.81345, 0.20971, 1, 0.24736, 1, 0.4722, 0.95947, 0.66495, 0.86102, 0.70863, 0.85521, 0.72538, 0.84577, 0.80545, 0.83118, 0.87937, 0.80635, 0.93466, 0.77774, 0.95053, 0.74751, 0.93807, 0.72384, 0.90379, 0.70803, 0.85493, 0.69946, 0.80111, 0.69776, 0.79043, 0.23045, 0.87006, 0.22486, 0.87102, 0.21785, 0.93101, 0.19497, 1, 0.11828, 1, 0.09288, 0.94258, 0.082, 0.8976, 0.08092, 0.83364, 0.07839, 0.80708, 0.0773, 0.80681, 0.02625, 0.76732, 0, 0.59431, 0, 0.49792, 0.27512, 0.28709, 0.37652, 0, 0.71107, 0, 0.13036, 0.72352, 0.12766, 0.75738, 0.13004, 0.78611, 0.13503, 0.81219, 0.14261, 0.82598, 0.15184, 0.83364, 0.16445, 0.82803, 0.1725, 0.81455, 0.17717, 0.79994, 0.18032, 0.72792, 0.17492, 0.70227, 0.15676, 0.676, 0.14459, 0.68146, 0.13592, 0.7012, 0.18109, 0.76526, 0.16623, 0.68301, 0.98014, 0.47973, 0.75722, 0.64546, 0.75086, 0.67001, 0.74885, 0.70148, 0.74949, 0.72238, 0.7539, 0.74488, 0.75922, 0.76346, 0.76805, 0.77665, 0.77721, 0.77903, 0.79546, 0.75049, 0.7994, 0.73559, 0.80134, 0.71081, 0.80085, 0.67505, 0.79742, 0.6504, 0.79163, 0.6352, 0.78543, 0.62524, 0.77809, 0.62231, 0.77001, 0.62463, 0.76261, 0.63382, 0.7898, 0.76704], "triangles": [28, 15, 40, 47, 0, 1, 2, 47, 1, 26, 27, 44, 30, 0, 65, 0, 4, 60, 0, 47, 4, 3, 47, 2, 27, 28, 43, 49, 14, 30, 47, 3, 4, 26, 24, 25, 14, 29, 30, 14, 15, 28, 14, 28, 29, 26, 31, 24, 62, 63, 0, 63, 64, 0, 0, 61, 62, 64, 65, 0, 0, 60, 61, 48, 30, 65, 49, 30, 48, 4, 59, 60, 28, 42, 43, 46, 42, 28, 43, 44, 27, 41, 46, 28, 5, 58, 59, 52, 51, 50, 31, 26, 44, 4, 5, 59, 40, 41, 28, 58, 61, 59, 59, 61, 60, 48, 50, 49, 48, 52, 50, 61, 58, 63, 57, 56, 58, 65, 52, 48, 53, 52, 65, 53, 65, 64, 53, 64, 63, 61, 63, 62, 53, 63, 54, 58, 56, 63, 40, 16, 45, 63, 56, 54, 56, 55, 54, 66, 55, 56, 33, 32, 31, 50, 14, 49, 14, 50, 51, 46, 41, 45, 45, 41, 40, 13, 14, 51, 6, 58, 5, 57, 58, 6, 31, 23, 24, 32, 23, 31, 33, 23, 32, 44, 33, 31, 33, 35, 34, 44, 43, 42, 37, 44, 42, 42, 45, 38, 45, 42, 46, 39, 38, 45, 44, 35, 33, 36, 35, 44, 42, 38, 37, 22, 23, 33, 37, 36, 44, 52, 12, 13, 52, 13, 51, 16, 39, 45, 40, 15, 16, 6, 56, 57, 7, 56, 6, 66, 56, 7, 34, 21, 22, 34, 22, 33, 53, 11, 12, 53, 12, 52, 16, 38, 39, 17, 38, 16, 8, 66, 7, 55, 66, 8, 10, 11, 53, 10, 53, 54, 20, 21, 34, 9, 55, 8, 54, 55, 9, 10, 54, 9, 19, 20, 34, 19, 34, 35, 19, 35, 36, 18, 38, 17, 37, 38, 18, 36, 37, 18, 19, 36, 18], "vertices": [1, 18, 116.44, 35.77, 1, 1, 18, 183.79, 31.33, 1, 1, 18, 183.79, 4.8, 1, 1, 18, 169.16, -17.94, 1, 1, 18, 133.62, -23.1, 1, 1, 17, 131.19, -8.23, 1, 1, 17, 127.78, -17.68, 1, 1, 17, 122.51, -26.4, 1, 1, 17, 113.55, -32.92, 1, 1, 17, 103.22, -34.8, 1, 1, 17, 92.31, -33.33, 1, 1, 17, 83.77, -29.28, 1, 1, 17, 78.06, -23.52, 1, 1, 17, 74.97, -17.17, 1, 1, 18, 74.68, -32.75, 1, 1, 18, -94.02, -42.15, 1, 1, 17, -96.37, -25.41, 1, 1, 17, -98.9, -32.49, 1, 1, 17, -107.16, -40.63, 1, 1, 17, -134.84, -40.63, 1, 1, 17, -144.02, -33.86, 1, 1, 17, -147.94, -28.55, 1, 1, 17, -148.33, -21, 1, 1, 17, -149.25, -17.87, 1, 1, 18, -149.31, -34.68, 1, 2, 17, -168.07, -13.18, 0.00248, 18, -167.74, -30.02, 0.99752, 1, 18, -177.21, -9.61, 1, 1, 18, -177.21, 1.77, 1, 1, 18, -77.9, 26.64, 1, 1, 18, -41.29, 60.52, 1, 1, 18, 79.48, 60.52, 1, 1, 17, -130.48, -8.01, 1, 1, 17, -131.46, -12.01, 1, 1, 17, -130.6, -15.4, 1, 1, 17, -128.8, -18.47, 1, 1, 17, -126.06, -20.1, 1, 1, 17, -122.73, -21, 1, 1, 17, -118.18, -20.34, 1, 1, 17, -115.27, -18.75, 1, 1, 17, -113.58, -17.03, 1, 1, 17, -112.45, -8.53, 1, 1, 17, -114.4, -5.5, 1, 1, 17, -120.95, -2.4, 1, 1, 17, -125.35, -3.05, 1, 1, 17, -128.48, -5.38, 1, 1, 17, -112.05, -12.95, 1, 1, 17, -117.54, -3.23, 1, 1, 18, 176.62, 3.91, 1, 1, 17, 95.82, 1.2, 1, 1, 17, 93.52, -1.7, 1, 1, 17, 92.79, -5.41, 1, 1, 17, 93.02, -7.88, 1, 1, 17, 94.62, -10.53, 1, 1, 17, 96.54, -12.72, 1, 1, 17, 99.73, -14.28, 1, 1, 17, 103.03, -14.56, 1, 1, 17, 109.62, -11.19, 1, 1, 17, 111.04, -9.43, 1, 1, 17, 111.74, -6.51, 1, 1, 17, 111.56, -2.29, 1, 1, 17, 110.33, 0.62, 1, 1, 17, 108.24, 2.41, 1, 1, 17, 106, 3.59, 1, 1, 17, 103.35, 3.93, 1, 1, 17, 100.43, 3.66, 1, 1, 17, 97.76, 2.57, 1, 1, 17, 107.57, -13.15, 1], "hull": 31, "edges": [72, 74, 74, 76, 76, 78, 78, 90, 90, 80, 80, 82, 82, 92, 92, 84, 84, 86, 86, 88, 88, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 2, 0, 0, 60, 2, 4, 94, 4, 94, 6, 6, 8, 14, 16, 16, 18, 18, 20, 32, 34, 34, 36, 4, 6, 8, 10, 46, 44, 20, 22, 22, 24, 10, 12, 12, 14, 42, 44, 42, 40, 36, 38, 40, 38, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 132, 132, 112, 112, 114, 116, 114, 118, 116, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 96, 24, 26, 26, 28, 28, 30, 30, 32, 46, 48, 48, 50], "width": 361, "height": 118}}, "cow_body": {"cow_body": {"x": 19.16, "y": 41.62, "width": 196, "height": 117}}, "cow_head": {"cow_head": {"x": 19.65, "y": 11.67, "rotation": 18.74, "width": 64, "height": 75}}, "cow_leg_01": {"cow_leg_01": {"type": "mesh", "uvs": [1, 0.07845, 1, 0.36396, 0.92825, 0.45272, 0.77289, 0.52006, 0.57025, 0.52158, 0.51385, 0.59841, 0.38819, 0.67406, 0.19988, 0.83178, 0.2877, 1, 0, 1, 0, 0.77896, 0.02175, 0.74765, 0.15549, 0.55507, 0.18639, 0.51059, 0.21005, 0.48334, 0.36002, 0.31068, 0.38424, 0.1907, 0.48054, 0.05534, 0.629, 0, 0.85327, 0], "triangles": [9, 7, 8, 7, 12, 6, 11, 12, 7, 7, 9, 10, 7, 10, 11, 19, 0, 1, 19, 1, 18, 1, 3, 18, 2, 3, 1, 16, 17, 18, 3, 16, 18, 4, 15, 16, 3, 4, 16, 5, 14, 15, 4, 5, 15, 6, 14, 5, 13, 14, 6, 12, 13, 6], "vertices": [1, 3, -23.81, 6.86, 1, 1, 3, -4.94, 26.74, 1, 1, 3, 5.45, 28.63, 1, 1, 3, 19.71, 24.02, 1, 1, 3, 32.6, 11.99, 1, 2, 3, 41.23, 13.96, 0.80667, 4, -11.71, 17.93, 0.19333, 2, 3, 54.16, 11.71, 0.50845, 4, 0.07, 12.15, 0.49155, 1, 4, 21.4, 5.62, 1, 1, 24, 16.43, -12.24, 1, 1, 24, -8.6, -12.24, 1, 1, 4, 25.78, -11.95, 1, 2, 3, 82.15, -5.11, 0.06356, 4, 22.23, -11.83, 0.93644, 2, 3, 60.99, -10.51, 0.45436, 4, 0.4, -11.1, 0.54564, 2, 3, 56.1, -11.76, 0.54463, 4, -4.64, -10.93, 0.45537, 2, 3, 52.8, -12.24, 0.60669, 4, -7.94, -10.47, 0.39331, 1, 3, 31.93, -15.29, 1, 1, 3, 22.47, -22.19, 1, 1, 3, 7.45, -25.85, 1, 1, 3, -5.58, -20.82, 1, 1, 3, -19.73, -7.39, 1], "hull": 20, "edges": [8, 6, 6, 4, 4, 2, 16, 18, 14, 16, 34, 32, 32, 30, 18, 20, 34, 36, 36, 38, 2, 0, 38, 0, 20, 22, 26, 28, 28, 30, 22, 24, 24, 26, 12, 14, 8, 10, 10, 12], "width": 87, "height": 96}}, "cow_leg_02": {"cow_leg_02": {"type": "mesh", "uvs": [1, 0.09213, 1, 0.28228, 0.51288, 0.44064, 0.52073, 0.64855, 0.6438, 0.78218, 0.94437, 1, 0.41972, 1, 0.34931, 0.94526, 0.30187, 0.91708, 0.28552, 0.87298, 0.25336, 0.70917, 0, 0.49282, 0, 0.42115, 0.03823, 0.32887, 0.23734, 0.1542, 0.47517, 0, 0.83774, 0], "triangles": [6, 4, 5, 6, 7, 4, 4, 7, 9, 7, 8, 9, 4, 10, 3, 4, 9, 10, 3, 10, 2, 10, 11, 2, 11, 12, 2, 12, 13, 2, 13, 14, 2, 2, 14, 1, 0, 1, 15, 15, 1, 14, 15, 16, 0], "vertices": [1, 5, -15.45, 10.65, 1, 1, 5, -3.29, 19.79, 1, 2, 5, 19.75, 10.2, 0.92431, 6, 4.2, 13.42, 0.07569, 3, 5, 33.62, 19.17, 0.0348, 6, 19.8, 8.04, 0.95909, 7, -7.94, 14.38, 0.00611, 2, 6, 31.6, 9.96, 0.48776, 7, 3.1, 9.79, 0.51224, 1, 7, 24.5, 5.25, 1, 2, 6, 45.28, -4.66, 0.00343, 7, 7.03, -9.84, 0.99657, 2, 6, 40.17, -6.31, 0.13392, 7, 1.82, -8.55, 0.86608, 2, 6, 37.4, -7.63, 0.3308, 7, -1.23, -8.21, 0.6692, 2, 6, 33.82, -7.26, 0.6551, 7, -4.08, -6.01, 0.3449, 1, 6, 20.89, -4.69, 1, 1, 6, 1.04, -10.15, 1, 2, 5, 31.63, -10.06, 0.00562, 6, -4.42, -8.43, 0.99438, 2, 5, 24.73, -12.58, 0.26907, 6, -10.66, -4.56, 0.73093, 2, 5, 8.64, -13.21, 0.94217, 6, -21.1, 7.7, 0.05783, 1, 5, -7.47, -12.24, 1, 1, 5, -17.05, 0.52, 1], "hull": 17, "edges": [4, 2, 8, 10, 10, 12, 20, 22, 30, 32, 2, 0, 32, 0, 22, 24, 24, 26, 26, 28, 28, 30, 16, 18, 18, 20, 12, 14, 14, 16, 4, 6, 6, 8], "width": 44, "height": 80}}, "cow_leg_03": {"cow_leg_03": {"type": "mesh", "uvs": [1, 0.3202, 0.8793, 0.43256, 0.52788, 0.59123, 0.48077, 0.65754, 0.33145, 0.82248, 0.69338, 1, 0.05876, 1, 0.08108, 0.87343, 0, 0.91001, 0, 0.77153, 0.09099, 0.71143, 0.16643, 0.55697, 0.18129, 0.28204, 0.22591, 0.15793, 0.46141, 0, 1, 0], "triangles": [14, 15, 0, 13, 14, 0, 12, 13, 0, 1, 12, 0, 2, 11, 12, 1, 2, 12, 3, 11, 2, 10, 11, 3, 4, 10, 3, 9, 10, 4, 7, 9, 4, 8, 9, 7, 6, 7, 4, 5, 6, 4], "vertices": [2, 9, -26.21, 18.03, 0.00692, 8, 13.33, 18.06, 0.99308, 2, 9, -16.86, 16.06, 0.04421, 8, 22.68, 16.1, 0.95579, 2, 9, -1.52, 6.55, 0.81292, 8, 38.04, 6.61, 0.18708, 1, 9, 3.71, 6.28, 1, 2, 10, -0.52, 5.82, 0.12304, 9, 17.1, 4.42, 0.87696, 2, 10, 16.35, 15.15, 0.94638, 9, 25.36, 21.84, 0.05362, 1, 10, 8.65, -8.38, 1, 2, 10, 0.02, -4.63, 0.94511, 9, 23.65, -3.75, 0.05489, 2, 10, 1.61, -8.48, 0.96226, 9, 27.19, -5.95, 0.03774, 1, 9, 17.42, -9.04, 1, 1, 9, 12.11, -7.01, 1, 2, 9, 0.32, -7.66, 0.03362, 8, 39.91, -7.59, 0.96638, 1, 8, 20.35, -13.22, 1, 1, 8, 11.07, -14.35, 1, 1, 8, -2.86, -9.16, 1, 1, 8, -9.24, 10.86, 1], "hull": 16, "edges": [28, 30, 28, 26, 26, 24, 24, 22, 20, 18, 16, 18, 16, 14, 14, 12, 10, 12, 8, 10, 8, 6, 4, 2, 0, 30, 2, 0, 4, 6, 20, 22], "width": 39, "height": 74}}, "cow_leg_04": {"cow_leg_04": {"type": "mesh", "uvs": [0.66172, 0.06566, 0.71283, 0.18762, 0.71711, 0.3188, 1, 0.63257, 1, 0.72661, 0.74277, 1, 0.60714, 1, 0.25956, 1, 0.46684, 0.81488, 0.65832, 0.8245, 0.73253, 0.70742, 0.41822, 0.53851, 0.3133, 0.51739, 0.15208, 0.50204, 0.06252, 0.41567, 0, 0.26405, 0, 0, 0.575, 0], "triangles": [7, 8, 6, 6, 8, 9, 6, 9, 5, 5, 9, 4, 4, 9, 10, 10, 3, 4, 3, 10, 2, 10, 11, 2, 11, 12, 2, 12, 1, 2, 12, 13, 14, 1, 12, 15, 0, 1, 17, 17, 1, 15, 12, 14, 15, 15, 16, 17], "vertices": [1, 11, 4.62, 19.35, 1, 1, 11, 13.03, 15.61, 1, 1, 11, 20.27, 9.53, 1, 3, 11, 47.32, 6.05, 0.85907, 12, -6.03, 1.61, 0.11005, 13, -17.9, -20.94, 0.03088, 2, 11, 52.4, 1.57, 0.38572, 12, -0.64, 5.7, 0.61428, 2, 12, 23.44, 6.52, 0.78359, 13, -4.01, 5.51, 0.21641, 2, 12, 27.87, 0.69, 0.0195, 13, 3.32, 5.51, 0.9805, 3, 11, 40.71, -41.44, 0.01368, 12, 39.21, -14.27, 0.00834, 13, 22.09, 5.51, 0.97798, 3, 11, 38.12, -24.23, 0.13747, 12, 21.82, -13.4, 0.45929, 13, 10.89, -7.81, 0.40324, 3, 11, 45.48, -16.93, 0.06589, 12, 16.13, -4.74, 0.69989, 13, 0.55, -7.12, 0.23422, 3, 11, 41.81, -8.35, 0.37787, 12, 6.99, -6.64, 0.56363, 13, -3.45, -15.55, 0.0585, 1, 11, 21.46, -13.03, 1, 1, 11, 16.57, -16.28, 1, 1, 11, 9.98, -22.08, 1, 1, 11, 2.12, -21.59, 1, 1, 11, -8.3, -16.9, 1, 2, 11, -22.56, -4.33, 0.99999, 13, 36.1, -66.49, 1e-05, 1, 11, -2.02, 18.96, 1], "hull": 18, "edges": [20, 6, 6, 8, 20, 8, 18, 10, 10, 12, 18, 12, 30, 32, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 10, 8, 4, 6, 4, 2, 2, 0, 32, 34, 0, 34], "width": 54, "height": 72}}, "cow_tile": {"cow_tile": {"type": "mesh", "uvs": [0.90047, 0.04857, 0.96881, 0.04783, 0.99253, 0.19676, 0.99235, 0.60032, 0.95939, 0.86528, 0.91577, 0.93593, 0.88317, 0.98874, 0.8244, 0.89472, 0.78606, 0.81594, 0.75316, 0.72034, 0.71918, 0.70813, 0.68583, 0.69615, 0.66254, 0.69827, 0.6364, 0.70065, 0.60677, 0.70064, 0.57319, 0.70063, 0.54554, 0.70062, 0.49635, 0.7006, 0.4614, 0.70059, 0.42242, 0.70057, 0.38631, 0.70056, 0.3463, 0.70055, 0.31688, 0.70053, 0.28804, 0.70052, 0.26425, 0.70052, 0.2352, 0.7005, 0.17206, 0.80029, 0.11078, 0.89716, 0.04521, 0.64505, 0, 0.27483, 0, 0.19561, 0.00102, 0.14941, 0.08524, 0.14911, 0.1254, 0.14897, 0.18816, 0.14875, 0.21408, 0.23367, 0.23399, 0.2989, 0.25924, 0.29374, 0.28911, 0.28763, 0.31887, 0.28154, 0.35165, 0.27484, 0.38091, 0.26886, 0.41174, 0.26255, 0.4552, 0.25366, 0.48567, 0.24743, 0.52315, 0.23251, 0.5522, 0.22095, 0.59148, 0.20683, 0.62022, 0.19828, 0.65271, 0.18555, 0.68043, 0.17238, 0.71608, 0.15404, 0.75723, 0.13018, 0.79289, 0.09891, 0.83449, 0.04929], "triangles": [32, 29, 30, 31, 32, 30, 28, 29, 32, 26, 33, 34, 26, 34, 35, 25, 26, 35, 25, 35, 36, 27, 32, 33, 27, 33, 26, 28, 32, 27, 25, 36, 37, 24, 37, 38, 25, 37, 24, 23, 24, 38, 22, 38, 39, 23, 38, 22, 21, 39, 40, 22, 39, 21, 20, 41, 42, 40, 41, 20, 21, 40, 20, 19, 42, 43, 20, 42, 19, 19, 43, 18, 18, 43, 44, 17, 44, 45, 18, 44, 17, 17, 45, 16, 16, 45, 46, 15, 46, 47, 16, 46, 15, 15, 47, 14, 12, 49, 50, 14, 47, 48, 13, 48, 49, 13, 14, 48, 49, 12, 13, 11, 50, 51, 11, 12, 50, 9, 10, 51, 11, 51, 10, 52, 9, 51, 53, 9, 52, 8, 9, 53, 7, 53, 54, 8, 53, 7, 6, 0, 5, 3, 4, 0, 3, 0, 1, 3, 1, 2, 5, 0, 4, 0, 7, 54, 6, 7, 0], "vertices": [2, 26, -1.38, -6.67, 0.99999, 27, -9.32, -6.75, 1e-05, 1, 26, -10.1, -6.02, 1, 1, 26, -12.91, -2.82, 1, 1, 26, -12.28, 5.22, 1, 1, 26, -7.67, 10.19, 1, 2, 26, -2, 11.18, 0.981, 27, -10.22, 11.08, 0.019, 2, 26, 2.25, 11.92, 0.90928, 27, -5.99, 11.89, 0.09072, 2, 26, 9.6, 9.47, 0.46642, 27, 1.41, 9.56, 0.53358, 3, 26, 14.38, 7.53, 0.10932, 27, 6.21, 7.69, 0.86447, 28, -4.19, 7.71, 0.02621, 3, 26, 18.43, 5.31, 0.00311, 27, 10.3, 5.53, 0.60851, 28, -0.06, 5.62, 0.38838, 2, 27, 14.63, 5.03, 0.07999, 28, 4.27, 5.19, 0.92001, 2, 28, 8.53, 4.77, 0.83726, 29, -2.04, 4.81, 0.16274, 2, 28, 11.51, 4.68, 0.33966, 29, 0.95, 4.76, 0.66034, 3, 28, 14.85, 4.59, 0.0232, 29, 4.29, 4.7, 0.94991, 30, -4.3, 4.71, 0.0269, 2, 29, 8.08, 4.58, 0.55738, 30, -0.5, 4.6, 0.44262, 3, 29, 12.38, 4.44, 0.02726, 30, 3.79, 4.47, 0.96848, 31, -4.53, 4.45, 0.00426, 2, 30, 7.33, 4.37, 0.70957, 31, -0.99, 4.36, 0.29043, 2, 31, 5.31, 4.19, 0.97151, 32, -4.36, 4.23, 0.02849, 2, 31, 9.78, 4.08, 0.42577, 32, 0.11, 4.23, 0.57423, 2, 32, 5.1, 4.24, 0.84054, 33, -2.03, 4.26, 0.15946, 2, 32, 9.72, 4.25, 0.09894, 33, 2.59, 4.3, 0.90106, 2, 33, 7.71, 4.36, 0.60333, 34, -0.53, 4.38, 0.39667, 3, 33, 11.48, 4.39, 0.05489, 34, 3.23, 4.44, 0.90942, 35, -4.23, 4.44, 0.03569, 3, 34, 6.92, 4.5, 0.61393, 35, -0.54, 4.51, 0.38576, 36, -6.4, 4.51, 0.00031, 3, 34, 9.97, 4.55, 0.15403, 35, 2.51, 4.56, 0.76331, 36, -3.36, 4.56, 0.08267, 2, 35, 6.22, 4.63, 0.42008, 36, 0.36, 4.63, 0.57992, 1, 36, 8.41, 6.77, 1, 1, 36, 16.21, 8.84, 1, 1, 36, 24.69, 3.95, 1, 1, 36, 30.61, -3.35, 1, 1, 36, 30.64, -4.93, 1, 1, 36, 30.53, -5.86, 1, 1, 36, 19.75, -6.06, 1, 1, 36, 14.61, -6.15, 1, 1, 36, 6.58, -6.3, 1, 2, 35, 9.09, -4.66, 0.02691, 36, 3.23, -4.66, 0.97309, 2, 35, 6.52, -3.4, 0.34874, 36, 0.66, -3.4, 0.65126, 3, 34, 10.74, -3.58, 0.04454, 35, 3.29, -3.56, 0.89889, 36, -2.57, -3.56, 0.05657, 2, 34, 6.92, -3.76, 0.61531, 35, -0.53, -3.75, 0.38469, 3, 33, 11.31, -3.99, 0.09274, 34, 3.11, -3.94, 0.89689, 35, -4.33, -3.94, 0.01037, 2, 33, 7.12, -4.16, 0.70336, 34, -1.08, -4.14, 0.29664, 3, 32, 10.42, -4.39, 0.06385, 33, 3.37, -4.32, 0.92104, 34, -4.83, -4.32, 0.01511, 2, 32, 6.48, -4.52, 0.6126, 33, -0.57, -4.49, 0.3874, 3, 31, 10.34, -4.88, 0.33674, 32, 0.91, -4.7, 0.6613, 33, -6.13, -4.72, 0.00196, 3, 30, 14.73, -4.91, 0.00089, 31, 6.43, -4.9, 0.8834, 32, -2.99, -4.83, 0.11571, 2, 30, 9.92, -5.07, 0.24815, 31, 1.63, -5.07, 0.75185, 3, 29, 14.76, -5.23, 0.0032, 30, 6.2, -5.19, 0.78937, 31, -2.09, -5.21, 0.20743, 3, 29, 9.72, -5.35, 0.31496, 30, 1.17, -5.33, 0.68498, 31, -7.13, -5.36, 5e-05, 3, 28, 16.49, -5.54, 0.00644, 29, 6.04, -5.41, 0.82861, 30, -2.52, -5.4, 0.16496, 3, 28, 12.32, -5.61, 0.264, 29, 1.88, -5.53, 0.73392, 30, -6.68, -5.53, 0.00208, 2, 28, 8.77, -5.73, 0.78129, 29, -1.68, -5.68, 0.21871, 3, 27, 14.36, -6.06, 0.09667, 28, 4.19, -5.9, 0.90064, 29, -6.25, -5.9, 0.00269, 2, 27, 9.08, -6.22, 0.69775, 28, -1.09, -6.15, 0.30225, 3, 26, 12.43, -6.7, 0.08721, 27, 4.48, -6.57, 0.89416, 28, -5.68, -6.58, 0.01862, 2, 26, 7.04, -7.29, 0.61282, 27, -0.89, -7.24, 0.38718], "hull": 55, "edges": [2, 4, 4, 6, 6, 8, 54, 56, 56, 58, 58, 60, 60, 62, 106, 108, 68, 70, 70, 72, 66, 68, 62, 64, 64, 66, 50, 52, 52, 54, 16, 18, 12, 14, 14, 16, 2, 0, 0, 108, 8, 10, 10, 12, 104, 106, 80, 82, 82, 84, 38, 40, 40, 42, 76, 78, 78, 80, 42, 44, 44, 46, 72, 74, 74, 76, 46, 48, 48, 50, 84, 86, 86, 88, 34, 36, 36, 38, 88, 90, 90, 92, 30, 32, 32, 34, 92, 94, 94, 96, 26, 28, 28, 30, 96, 98, 98, 100, 22, 24, 24, 26, 100, 102, 102, 104, 18, 20, 20, 22], "width": 128, "height": 20}}, "cow_tile2": {"cow_tile2": {"type": "path", "lengths": [53, 93.94, 147.25], "vertexCount": 9, "vertices": [1, 58, -9.73, 10.91, 1, 1, 58, 0.64, -0.2, 1, 2, 58, 14.26, -14.8, 0.69919, 59, -4.45, 35.94, 0.30081, 2, 58, 39.54, -13.21, 0.44375, 59, -10.31, 11.3, 0.55625, 2, 58, 47.78, -0.36, 0.96054, 59, -0.43, -0.34, 0.03946, 2, 58, 54.39, 9.95, 0.51627, 59, 7.5, -9.67, 0.48373, 1, 59, 28.91, -11.56, 1, 1, 59, 36.13, 0.02, 1, 1, 58, 29.23, 36.51, 1]}}, "dot": {"dot": {"width": 16, "height": 16}}, "dot2": {"dot": {"width": 16, "height": 16}}, "dot3": {"dot": {"width": 16, "height": 16}}, "dot4": {"dot": {"width": 16, "height": 16}}, "dot5": {"dot": {"width": 16, "height": 16}}, "dot6": {"dot": {"width": 16, "height": 16}}, "dot7": {"dot": {"width": 16, "height": 16}}, "dot8": {"dot": {"width": 16, "height": 16}}, "dot9": {"dot": {"width": 16, "height": 16}}, "dot10": {"dot": {"width": 16, "height": 16}}, "dot11": {"dot": {"width": 16, "height": 16}}, "dot12": {"dot": {"width": 16, "height": 16}}, "glow": {"glow": {"scaleX": 0.4011, "rotation": -10.8, "width": 128, "height": 128}}, "glow2": {"glow": {"rotation": -10.8, "width": 128, "height": 128}}, "lamp": {"lamp": {"x": -271.25, "y": -43.25, "width": 200, "height": 153}}, "star": {"star": {"width": 105, "height": 105}}, "star2": {"star": {"width": 105, "height": 105}}, "star3": {"star": {"width": 105, "height": 105}}, "star4": {"star": {"width": 105, "height": 105}}}}], "animations": {"BG_11_violet_anim": {"slots": {"dot": {"rgba": [{"color": "ec6ef668"}, {"time": 1.0333, "color": "ec6ef600"}, {"time": 2.6333, "color": "ec6ef6a2"}, {"time": 3.2, "color": "ec6ef668"}]}, "dot2": {"rgba": [{"color": "ec6ef6a2"}, {"time": 1.6, "color": "ec6ef600"}, {"time": 3.2, "color": "ec6ef6a2"}]}, "dot3": {"rgba": [{"color": "ec6ef66c"}, {"time": 1.0667, "color": "ec6ef600"}, {"time": 2.6667, "color": "ec6ef6a2"}, {"time": 3.2, "color": "ec6ef66c"}]}, "dot4": {"rgba": [{"color": "ec6ef64f"}, {"time": 0.7667, "color": "ec6ef600"}, {"time": 2.3667, "color": "ec6ef6a2"}, {"time": 3.2, "color": "ec6ef64f"}]}, "dot5": {"rgba": [{"color": "ec6ef665"}, {"time": 1, "color": "ec6ef600"}, {"time": 2.6, "color": "ec6ef6a2"}, {"time": 3.2, "color": "ec6ef665"}]}, "dot6": {"rgba": [{"color": "ec6ef6a2"}, {"time": 1.6, "color": "ec6ef600"}, {"time": 3.2, "color": "ec6ef6a2"}]}, "dot7": {"rgba": [{"color": "ec6ef665"}, {"time": 0.6, "color": "ec6ef6a2"}, {"time": 2.2, "color": "ec6ef600"}, {"time": 3.2, "color": "ec6ef665"}]}, "dot8": {"rgba": [{"color": "ec6ef648"}, {"time": 0.7, "color": "ec6ef600"}, {"time": 2.3, "color": "ec6ef6a2"}, {"time": 3.2, "color": "ec6ef648"}]}, "dot9": {"rgba": [{"color": "ec6ef6a2"}, {"time": 1.6, "color": "ec6ef600"}, {"time": 3.2, "color": "ec6ef6a2"}]}, "dot10": {"rgba": [{"color": "ec6ef653"}, {"time": 0.7667, "color": "ec6ef6a2"}, {"time": 2.3667, "color": "ec6ef600"}, {"time": 3.2, "color": "ec6ef653"}]}, "dot11": {"rgba": [{"color": "ec6ef653"}, {"time": 0.7667, "color": "ec6ef6a2"}, {"time": 2.3667, "color": "ec6ef600"}, {"time": 3.2, "color": "ec6ef653"}]}, "dot12": {"rgba": [{"color": "ec6ef6a2"}, {"time": 1.6, "color": "ec6ef600"}, {"time": 3.2, "color": "ec6ef6a2"}]}, "star": {"rgba": [{"color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 3.2, "color": "ffffff00"}]}, "star2": {"rgba": [{"color": "ffffffff"}, {"time": 1.6, "color": "ffffff19"}, {"time": 3.2, "color": "ffffffff"}]}, "star3": {"rgba": [{"color": "ffffff22"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 3.0667, "color": "ffffff00"}, {"time": 3.2, "color": "ffffff22"}]}, "star4": {"rgba": [{"color": "ffffff68"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00"}, {"time": 3.2, "color": "ffffff68"}]}}, "bones": {"car2": {"translate": [{"curve": [0.097, 0, 0.203, 0, 0.097, 0, 0.203, -3.45]}, {"time": 0.3, "y": -3.45, "curve": [0.361, 0, 0.406, 0, 0.361, -3.45, 0.406, 0]}, {"time": 0.4667, "curve": [0.527, 0, 0.606, 0, 0.527, 0, 0.606, -3.45]}, {"time": 0.6667, "y": -3.45, "curve": [0.715, 0, 0.751, 0, 0.715, -3.45, 0.751, 0]}, {"time": 0.8, "curve": [0.897, 0, 1.003, 0, 0.897, 0, 1.003, -3.45]}, {"time": 1.1, "y": -3.45, "curve": [1.161, 0, 1.206, 0, 1.161, -3.45, 1.206, 0]}, {"time": 1.2667, "curve": [1.327, 0, 1.406, 0, 1.327, 0, 1.406, -3.45]}, {"time": 1.4667, "y": -3.45, "curve": [1.515, 0, 1.551, 0, 1.515, -3.45, 1.551, 0]}, {"time": 1.6, "curve": [1.697, 0, 1.803, 0, 1.697, 0, 1.803, -3.45]}, {"time": 1.9, "y": -3.45, "curve": [1.961, 0, 2.006, 0, 1.961, -3.45, 2.006, 0]}, {"time": 2.0667, "curve": [2.127, 0, 2.206, 0, 2.127, 0, 2.206, -3.45]}, {"time": 2.2667, "y": -3.45, "curve": [2.315, 0, 2.351, 0, 2.315, -3.45, 2.351, 0]}, {"time": 2.4, "curve": [2.497, 0, 2.603, 0, 2.497, 0, 2.603, -3.45]}, {"time": 2.7, "y": -3.45, "curve": [2.761, 0, 2.806, 0, 2.761, -3.45, 2.806, 0]}, {"time": 2.8667, "curve": [2.927, 0, 3.006, 0, 2.927, 0, 3.006, -3.45]}, {"time": 3.0667, "y": -3.45, "curve": [3.115, 0, 3.151, 0, 3.115, -3.45, 3.151, -1.15]}, {"time": 3.2}]}, "cow_body": {"rotate": [{"curve": [0.133, 0, 0.267, -3.17]}, {"time": 0.4, "value": -4.47, "curve": [0.478, -5.24, 0.556, -6.21]}, {"time": 0.6333, "value": -6.21, "curve": [0.833, -6.21, 1.033, -3.26]}, {"time": 1.2333, "value": -3.26, "curve": [1.267, -3.26, 1.3, -4.43]}, {"time": 1.3333, "value": -4.76, "curve": [1.4, -5.42, 1.467, -6.21]}, {"time": 1.5333, "value": -6.21, "curve": [1.722, -6.21, 1.911, -3.92]}, {"time": 2.1, "value": -3.39, "curve": [2.156, -3.23, 2.211, -3.39]}, {"time": 2.2667, "value": -3.23, "curve": [2.367, -2.94, 2.467, 0]}, {"time": 2.5667, "curve": "stepped"}, {"time": 3.2}], "translate": [{"curve": [0.067, 0, 0.133, -8.05, 0.067, 0, 0.133, -0.4]}, {"time": 0.2, "x": -8.53, "y": -0.51, "curve": [0.3, -9.23, 0.4, -8.53, 0.3, -0.67, 0.4, -0.51]}, {"time": 0.5, "x": -9.23, "y": -0.79, "curve": [0.544, -9.55, 0.589, -19.32, 0.544, -0.92, 0.589, -1.99]}, {"time": 0.6333, "x": -19.32, "y": -1.99, "curve": [0.689, -19.32, 0.744, -16.65, 0.689, -1.99, 0.744, 0.05]}, {"time": 0.8, "x": -15.31, "y": 0.51, "curve": [0.878, -13.43, 0.956, -9.9, 0.878, 1.15, 0.956, 1.32]}, {"time": 1.0333, "x": -9.66, "y": 1.32, "curve": [1.1, -9.45, 1.167, -9.66, 1.1, 1.32, 1.167, -0.51]}, {"time": 1.2333, "x": -9.45, "y": -0.51, "curve": [1.278, -9.31, 1.322, -8.63, 1.278, -0.51, 1.322, -0.51]}, {"time": 1.3667, "x": -8.63, "y": -0.51, "curve": [1.422, -8.63, 1.478, -19.32, 1.422, -0.51, 1.478, -1.99]}, {"time": 1.5333, "x": -19.32, "y": -1.99, "curve": [1.578, -19.32, 1.622, -16.33, 1.578, -1.99, 1.622, 0.12]}, {"time": 1.6667, "x": -15.31, "y": 0.51, "curve": [1.756, -13.27, 1.844, -11.42, 1.756, 1.3, 1.844, 1.56]}, {"time": 1.9333, "x": -10.14, "y": 1.56, "curve": [1.989, -9.34, 2.044, -9.77, 1.989, 1.56, 2.044, 0.42]}, {"time": 2.1, "x": -9.05, "y": 0.13, "curve": [2.156, -8.34, 2.211, -6.95, 2.156, -0.17, 2.211, -0.22]}, {"time": 2.2667, "x": -5.88, "y": -0.22, "curve": [2.367, -3.94, 2.467, 0, 2.367, -0.22, 2.467, 0]}, {"time": 2.5667, "curve": "stepped"}, {"time": 3.2}]}, "cow_head": {"rotate": [{"curve": [0.122, 0, 0.244, -45.14]}, {"time": 0.3667, "value": -45.14, "curve": "stepped"}, {"time": 0.5, "value": -45.14, "curve": [0.6, -45.14, 0.7, -51.7]}, {"time": 0.8, "value": -51.7, "curve": [0.889, -51.7, 0.978, -45.14]}, {"time": 1.0667, "value": -45.14, "curve": [1.122, -45.14, 1.178, -47.89]}, {"time": 1.2333, "value": -47.89, "curve": [1.311, -47.89, 1.389, -39.78]}, {"time": 1.4667, "value": -39.78, "curve": [1.533, -39.78, 1.6, -51.7]}, {"time": 1.6667, "value": -51.7, "curve": [1.767, -51.7, 1.867, -51]}, {"time": 1.9667, "value": -45.14, "curve": [2.011, -42.54, 2.056, -32.11]}, {"time": 2.1, "value": -26.33, "curve": [2.167, -17.67, 2.233, -7.21]}, {"time": 2.3, "value": -1.81, "curve": [2.389, 5.39, 2.478, 11.45]}, {"time": 2.5667, "value": 11.45, "curve": [2.644, 11.45, 2.722, 9.49]}, {"time": 2.8, "value": 8.51}], "translate": [{"curve": [0.033, 0, 0.067, 0, 0.035, -0.2, 0.067, 0]}, {"time": 0.1, "y": -0.51, "curve": [0.189, 0, 0.278, 5.25, 0.189, -1.87, 0.278, -7.91]}, {"time": 0.3667, "x": 5.25, "y": -8.59, "curve": [0.8, 5.25, 1.233, 5.25, 0.8, -11.91, 1.233, -12.51]}, {"time": 1.6667, "x": 5.25, "y": -12.51, "curve": [1.811, 5.25, 1.956, 5.25, 1.811, -12.51, 1.956, -11.29]}, {"time": 2.1, "x": 5.25, "y": -9.38, "curve": [2.322, 5.25, 2.544, 0, 2.322, -6.43, 2.544, 2.08]}, {"time": 2.7667, "y": 2.08, "curve": [2.911, 0, 3.056, 0, 2.911, 2.08, 3.056, 0.69]}, {"time": 3.2}]}, "cow_leg_011": {"rotate": [{"curve": [0.1, 0, 0.2, 41.61]}, {"time": 0.3, "value": 58.99, "curve": [0.367, 70.58, 0.433, 86.91]}, {"time": 0.5, "value": 86.91, "curve": [0.522, 86.91, 0.544, 86.91]}, {"time": 0.5667, "value": 84.28, "curve": [0.578, 82.96, 0.589, 80.4]}, {"time": 0.6, "value": 67.3, "curve": [0.611, 54.19, 0.622, 10.28]}, {"time": 0.6333, "value": 5.65, "curve": [0.689, -17.49, 0.744, -17.49]}, {"time": 0.8, "value": -17.49, "curve": [0.944, -17.49, 1.089, 70.71]}, {"time": 1.2333, "value": 70.71, "curve": [1.278, 70.71, 1.322, 70.71]}, {"time": 1.3667, "value": 65.86, "curve": [1.4, 62.22, 1.433, 52.95]}, {"time": 1.4667, "value": 44.86, "curve": [1.478, 42.16, 1.489, 40.04]}, {"time": 1.5, "value": 33.5, "curve": [1.511, 26.97, 1.522, 9.05]}, {"time": 1.5333, "value": 5.65, "curve": [1.578, -7.94, 1.622, -17.49]}, {"time": 1.6667, "value": -17.49, "curve": [1.811, -17.49, 1.956, 23.09]}, {"time": 2.1, "value": 43.75, "curve": [2.2, 58.06, 2.302, 63.78]}, {"time": 2.4, "value": 58.25, "curve": [2.478, 53.86, 2.556, 53.45]}, {"time": 2.6333, "value": 45.09, "curve": [2.711, 36.72, 2.789, 13.72]}, {"time": 2.8667, "value": 8.07, "curve": [2.978, 0, 3.089, 0]}, {"time": 3.2}], "translate": [{"curve": [0.126, 0.28, 0.244, 1.55, 0.122, 0, 0.244, -6.93]}, {"time": 0.3667, "x": 1.55, "y": -6.93, "curve": [0.411, 1.55, 0.456, -1.61, 0.411, -6.93, 0.456, -5.6]}, {"time": 0.5, "x": -8.47, "y": -5.6, "curve": [0.522, -11.9, 0.544, -19.1, 0.522, -5.6, 0.544, -17.27]}, {"time": 0.5667, "x": -29.31, "y": -17.27, "curve": [0.589, -39.53, 0.611, -69.77, 0.589, -17.27, 0.611, -16.19]}, {"time": 0.6333, "x": -69.77, "y": -15.09, "curve": [0.689, -69.77, 0.744, -69.77, 0.689, -12.34, 0.744, -7.51]}, {"time": 0.8, "x": -64.8, "y": -5.71, "curve": [0.9, -55.85, 1, -9.66, 0.9, -2.47, 1, 0.05]}, {"time": 1.1, "x": -2.64, "y": 0.05, "curve": [1.189, 3.6, 1.278, 3.6, 1.189, 0.05, 1.278, -1.4]}, {"time": 1.3667, "x": 3.6, "y": -5.6, "curve": [1.4, 3.6, 1.433, -14.64, 1.4, -7.17, 1.433, -17.27]}, {"time": 1.4667, "x": -29.31, "y": -17.27, "curve": [1.489, -39.09, 1.511, -69.77, 1.489, -17.27, 1.511, -16.38]}, {"time": 1.5333, "x": -69.77, "y": -15.09, "curve": [1.578, -69.77, 1.622, -69.42, 1.578, -12.52, 1.622, -6.64]}, {"time": 1.6667, "x": -64.8, "y": -5.71, "curve": [1.811, -49.75, 1.956, -24.37, 1.811, -2.68, 1.956, -2.68]}, {"time": 2.1, "x": -10.76, "y": -2.68, "curve": [2.189, -2.38, 2.278, -1.65, 2.189, -2.68, 2.278, -4.04]}, {"time": 2.3667, "x": 1.16, "y": -4.04, "curve": [2.433, 3.28, 2.5, 4.04, 2.433, -4.04, 2.5, -3.44]}, {"time": 2.5667, "x": 4.04, "y": -2.82, "curve": [2.644, 4.04, 2.789, 0, 2.644, -2.09, 2.789, 0]}, {"time": 2.8667, "curve": "stepped"}, {"time": 3.2}]}, "target2": {"translate": [{"curve": [0.122, 0, 0.244, -4.03, 0.122, 0, 0.244, -2.38]}, {"time": 0.3667, "x": -7.53, "y": -3.77, "curve": [0.456, -10.08, 0.544, -18.15, 0.456, -4.78, 0.544, -7.19]}, {"time": 0.6333, "x": -18.15, "y": -7.19, "curve": [0.767, -18.15, 0.9, -17.12, 0.767, -7.19, 0.9, -4.52]}, {"time": 1.0333, "x": -16.78, "y": -3.08, "curve": [1.122, -16.55, 1.211, -16.78, 1.122, -2.12, 1.211, 0]}, {"time": 1.3, "x": -16.44, "curve": [1.378, -16.14, 1.456, -10.96, 1.378, 0, 1.456, -0.52]}, {"time": 1.5333, "x": -10.96, "y": -0.68, "curve": [1.667, -10.96, 1.8, -17.12, 1.667, -0.97, 1.8, -1.37]}, {"time": 1.9333, "x": -17.12, "y": -1.37, "curve": [2.156, -17.12, 2.378, -10.12, 2.156, -1.37, 2.378, 6.51]}, {"time": 2.6, "x": -7.19, "y": 6.51, "curve": [2.811, -4.41, 2.989, 0, 2.811, 6.51, 2.989, 0]}, {"time": 3.2}]}, "bone5": {"translate": [{"curve": "stepped"}, {"time": 1.6, "curve": "stepped"}, {"time": 3.2}]}, "bone7": {"translate": [{"curve": "stepped"}, {"time": 1.5, "curve": "stepped"}, {"time": 3.0667, "curve": "stepped"}, {"time": 3.2}]}, "bone8": {"translate": [{"curve": "stepped"}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.8667, "curve": "stepped"}, {"time": 3.2}]}}, "deform": {"default": {"cow_tile": {"cow_tile": [{"time": 1.8333, "vertices": [-1.34955, 1.47608, -0.9151, 1.77838, -1.34955, 1.47608, -1.34955, 1.47608, -1.34955, 1.47608, -1.34955, 1.47608, -1.34955, 1.47608, -0.9151, 1.77838, -1.34955, 1.47608, -0.9151, 1.77838, -1.34955, 1.47608, -0.9151, 1.77838, -1.34955, 1.47608, -0.9151, 1.77838, -0.4054, 1.95847, -1.34955, 1.47608, -0.9151, 1.77838, -0.4054, 1.95847, -0.9151, 1.77838, -0.4054, 1.95847, -0.4054, 1.95847, 0.09283, 1.9978, -0.4054, 1.95847, 0.09283, 1.9978, -0.4054, 1.95847, 0.09283, 1.9978, 0.54456, 1.92444, 0.09283, 1.9978, 0.54456, 1.92444, 0.09283, 1.9978, 0.54456, 1.92444, 1.08189, 1.68207, 0.54456, 1.92444, 1.08189, 1.68207, 1.08189, 1.68207, 1.70164, 1.05103, 1.08189, 1.68207, 1.70164, 1.05103, 1.70164, 1.05103, 1.888, 0.66003, 1.70164, 1.05103, 1.888, 0.66003, 1.888, 0.66003, 1.99039, 0.19574, 1.888, 0.66003, 1.99039, 0.19574, 1.96808, -0.35623, 1.99039, 0.19574, 1.96808, -0.35623, 1.76489, -0.94064, 1.99039, 0.19574, 1.96808, -0.35623, 1.76489, -0.94064, 1.96808, -0.35623, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.76489, -0.94064, 1.96808, -0.35623, 1.76489, -0.94064, 1.96808, -0.35623, 1.76489, -0.94064, 1.99039, 0.19574, 1.96808, -0.35623, 1.76489, -0.94064, 1.99039, 0.19574, 1.96808, -0.35623, 1.888, 0.66003, 1.99039, 0.19574, 1.96808, -0.35623, 1.888, 0.66003, 1.99039, 0.19574, 1.70164, 1.05103, 1.888, 0.66003, 1.99039, 0.19574, 1.70164, 1.05103, 1.888, 0.66003, 1.08189, 1.68207, 1.70164, 1.05103, 1.888, 0.66003, 0.54456, 1.92444, 1.08189, 1.68207, 1.70164, 1.05103, 0.54456, 1.92444, 1.08189, 1.68207, 0.09283, 1.9978, 0.54456, 1.92444, 1.08189, 1.68207, 0.09283, 1.9978, 0.54456, 1.92444, 1.08189, 1.68207, -0.4054, 1.95847, 0.09283, 1.9978, 0.54456, 1.92444, -0.4054, 1.95847, 0.09283, 1.9978, 0.54456, 1.92444, -0.4054, 1.95847, 0.09283, 1.9978, -0.9151, 1.77838, -0.4054, 1.95847, 0.09283, 1.9978, -0.9151, 1.77838, -0.4054, 1.95847, -1.34955, 1.47608, -0.9151, 1.77838, -0.4054, 1.95847, -1.34955, 1.47608, -0.9151, 1.77838]}]}}}}}}