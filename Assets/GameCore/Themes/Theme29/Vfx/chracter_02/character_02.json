{"skeleton": {"hash": "5bgf8LHUm+A", "spine": "4.0.64", "x": -322.03, "y": -49.25, "width": 734.68, "height": 711.05, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "x": -43.64}, {"name": "all2", "parent": "all", "x": 97.95, "y": 151.78, "color": "959595ff"}, {"name": "all3", "parent": "all2", "length": 121.71, "rotation": 119.43, "x": -2.66, "y": 24.46}, {"name": "head", "parent": "all3", "length": 60.1, "rotation": 10.99, "x": 135.62, "y": -17.73}, {"name": "all4", "parent": "all3", "length": 188.41, "rotation": -123.2, "x": 135.53, "y": -80.72}, {"name": "Asset 4", "parent": "all4", "length": 88.94, "rotation": 42.52, "x": 27.62, "y": 36.02}, {"name": "all5", "parent": "all4", "length": 64.62, "rotation": 76.18, "x": 76.87, "y": 32.61}, {"name": "all6", "parent": "all", "length": 227.73, "rotation": 98.25, "x": 175.92, "y": 11.83}, {"name": "all7", "parent": "all3", "length": 149.85, "rotation": 45.01, "x": 5.11, "y": 22.71, "color": "959595ff"}, {"name": "all9", "parent": "all7", "length": 163.48, "rotation": 58.37, "x": 148.69, "y": -0.05}, {"name": "Asset 8", "parent": "all3", "length": 66.39, "rotation": 38.77, "x": 187.05, "y": 50.78}, {"name": "Asset 9", "parent": "Asset 8", "length": 54.91, "rotation": 5.98, "x": 68.35, "y": -0.16}, {"name": "Asset 10", "parent": "Asset 9", "length": 70.12, "rotation": 19.06, "x": 54.91}, {"name": "Asset 11", "parent": "Asset 10", "length": 45.33, "rotation": 20.84, "x": 70.12}, {"name": "Asset 7a", "parent": "all3", "length": 43.07, "rotation": 64.76, "x": 137.06, "y": 100.2}, {"name": "Asset 7b", "parent": "Asset 7a", "length": 43.07, "x": 43.07}, {"name": "Asset 12", "parent": "Asset 7b", "length": 27.9, "rotation": 43.11, "x": 43.07}, {"name": "Asset 13", "parent": "Asset 12", "length": 37.34, "rotation": 18.4, "x": 27.9}, {"name": "all16", "parent": "all3", "x": 167.9, "y": 62.36}, {"name": "all17", "parent": "all16", "length": 126.67, "rotation": 179.12, "x": -14.05, "y": 0.94}, {"name": "all20", "parent": "all17", "length": 137.53, "rotation": -15.31, "x": 120.61, "color": "abe323ff"}, {"name": "all24", "parent": "all3", "x": 167.9, "y": 62.36}, {"name": "all25", "parent": "all24", "length": 120.61, "rotation": 179.12, "x": -8.24, "y": 0.26}, {"name": "all26", "parent": "all25", "length": 43.69, "rotation": -40.12, "x": 94.39, "y": -23.67}, {"name": "all27", "parent": "all26", "length": 27.13, "rotation": 25.93, "x": 42.96, "y": 1.88}, {"name": "all32", "parent": "all24", "rotation": 160.84, "x": -128.84, "y": 2.13, "color": "ff3f00ff"}, {"name": "all28", "parent": "all32", "length": 137.53}, {"name": "all29", "parent": "all28", "length": 22.32, "rotation": -127.57, "x": 60.76, "y": -32.29}, {"name": "all30", "parent": "all29", "length": 22.85, "rotation": -43.55, "x": 22.32}, {"name": "all31", "parent": "all24", "x": -4.51, "y": 11.29}, {"name": "target", "parent": "root", "x": 11.95, "y": 40.5, "color": "ff3f00ff"}, {"name": "target2", "parent": "target", "color": "ff3f00ff"}, {"name": "target3", "parent": "all24", "rotation": -119.43, "x": -152.96, "y": 70.4, "color": "ff3f00ff"}, {"name": "all10", "parent": "all", "length": 74.67, "rotation": -174.92, "x": -186.16, "y": 94.21, "color": "ff3f00ff"}, {"name": "target4", "parent": "root", "x": 158.83, "y": 419.54, "color": "ff3f00ff"}, {"name": "target5", "parent": "root", "x": 230.27, "y": 321.54, "color": "ff3f00ff"}, {"name": "Asset 14", "parent": "all9", "length": 79.12, "rotation": -18.41, "x": -0.79, "y": -16.58}, {"name": "target6", "parent": "root", "x": -195.45, "y": 184.46, "color": "ff3f00ff"}, {"name": "target7", "parent": "root", "x": -34.94, "y": 343.06, "color": "ff3f00ff"}, {"name": "target8", "parent": "all2", "x": 45.29, "y": 85.43, "color": "ff3f00ff"}, {"name": "all8", "parent": "target5", "x": 24.94, "y": -17.62}, {"name": "bone4", "parent": "root", "x": -45.39, "y": 67.27}, {"name": "bone3", "parent": "bone4", "x": -55.87, "y": -46.7}, {"name": "bone2", "parent": "bone4", "x": -137.72, "y": 253.44, "scaleX": 1.1041}, {"name": "bone5", "parent": "root", "x": -45.39, "y": 67.27, "scaleX": -1, "color": "ff0000ff"}, {"name": "bone6", "parent": "bone5", "x": -55.87, "y": -46.7, "scaleY": 0.6216, "color": "ff0000ff"}, {"name": "bone7", "parent": "bone5", "x": -137.72, "y": 253.44, "scaleX": 1.1041, "color": "ff0000ff"}, {"name": "bone8", "parent": "bone6", "color": "ff0000ff"}, {"name": "bone9", "parent": "bone7", "color": "ff0000ff"}, {"name": "bone10", "parent": "bone2", "rotation": 180, "color": "ff0000ff"}, {"name": "bone11", "parent": "bone3", "rotation": 180, "color": "ff0000ff"}, {"name": "bone12", "parent": "root", "x": 44.1, "y": 81.1}, {"name": "bone13", "parent": "bone12", "x": -55.87, "y": -46.7}, {"name": "bone14", "parent": "bone13", "rotation": 180, "color": "ff0000ff"}, {"name": "bone15", "parent": "bone12", "x": -137.72, "y": 253.44, "scaleX": 1.1041}, {"name": "glow_big", "parent": "all", "rotation": 180, "x": 54.13, "y": 20.58, "color": "ff0000ff"}, {"name": "target9", "parent": "target5", "color": "ff3f00ff"}, {"name": "all11", "parent": "all8", "x": -19.65, "y": 18.14}, {"name": "01", "parent": "all11", "length": 311.13, "rotation": -93.5, "x": -0.82, "y": 0.34}, {"name": "02", "parent": "all11", "length": 211.39, "rotation": -75.14, "x": 4.6, "y": -17}, {"name": "03", "parent": "all11", "length": 206.65, "rotation": 105.42, "x": 3.05, "y": -1.08}, {"name": "04", "parent": "all11", "length": 188.11, "rotation": 91.46, "x": 5.04, "y": 10.49}, {"name": "05", "parent": "all11", "length": 161.27, "rotation": 82.06, "x": 15.39, "y": 0.12}, {"name": "06", "parent": "all11", "length": 56.33, "rotation": -33.11, "x": 5.98, "y": -7.28}, {"name": "Asset 27a3", "parent": "all11", "length": 203.86, "rotation": -102.95, "x": -4.63, "y": -5.58}, {"name": "Asset 27b3", "parent": "all11", "length": 201.97, "rotation": 137.77, "x": -16.78, "y": 11.67}, {"name": "glow (1)2", "parent": "all11", "length": 187.97, "rotation": -139.68, "x": -5.9, "y": -6.89}, {"name": "07", "parent": "all11", "length": 109.81, "rotation": 53.85, "x": 12.74, "y": -1.18}, {"name": "target10", "parent": "target9", "color": "ff3f00ff"}, {"name": "shadow", "parent": "all", "x": 38.95, "y": 244.92, "scaleX": 2.2724, "scaleY": 2.2724}], "slots": [{"name": "01", "bone": "01", "attachment": "01"}, {"name": "02", "bone": "02", "attachment": "02"}, {"name": "03", "bone": "03", "attachment": "03"}, {"name": "04", "bone": "04", "attachment": "04"}, {"name": "05", "bone": "05", "attachment": "05"}, {"name": "06", "bone": "06", "attachment": "06"}, {"name": "07", "bone": "07", "attachment": "07"}, {"name": "Asset 27a3", "bone": "Asset 27a3", "attachment": "Asset 27a"}, {"name": "Asset 27b3", "bone": "Asset 27b3", "attachment": "Asset 27b"}, {"name": "Asset 27c3", "bone": "glow (1)2", "attachment": "Asset 27c"}, {"name": "lighting03", "bone": "bone15", "blend": "additive"}, {"name": "leg_01", "bone": "all7", "attachment": "leg_01"}, {"name": "Asset 8", "bone": "Asset 8", "attachment": "Asset 8"}, {"name": "body", "bone": "all3", "attachment": "body"}, {"name": "Asset 1", "bone": "all6", "attachment": "Asset 1"}, {"name": "Asset 4", "bone": "Asset 4", "attachment": "Asset 4"}, {"name": "Asset 5", "bone": "all2", "attachment": "Asset 5"}, {"name": "Asset 7", "bone": "Asset 7a", "attachment": "Asset 7"}, {"name": "Asset 9", "bone": "Asset 14", "attachment": "Asset 9"}, {"name": "Asset 6", "bone": "Asset 14", "attachment": "Asset 6"}, {"name": "hand", "bone": "all4", "attachment": "hand"}, {"name": "glow (1)2", "bone": "target9", "color": "ff0042ff", "attachment": "glow (1)", "blend": "additive"}, {"name": "hand_04", "bone": "all25", "attachment": "hand_04"}, {"name": "hand_03", "bone": "all28", "attachment": "hand_03"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "glow (1)", "bone": "target9", "attachment": "glow (1)", "blend": "additive"}, {"name": "glow (1)15", "bone": "target10", "blend": "additive"}, {"name": "lighting01", "bone": "bone2", "blend": "additive"}, {"name": "lighting02", "bone": "bone7", "blend": "additive"}, {"name": "glow (1)3", "bone": "bone8", "blend": "additive"}, {"name": "glow (1)13", "bone": "glow_big", "blend": "additive"}, {"name": "glow (1)7", "bone": "bone10", "blend": "additive"}, {"name": "glow (1)9", "bone": "bone11", "blend": "additive"}, {"name": "glow (1)11", "bone": "bone14", "blend": "additive"}, {"name": "glow (1)5", "bone": "bone9", "blend": "additive"}, {"name": "glow (1)4", "bone": "bone8", "color": "f10fffff", "blend": "additive"}, {"name": "glow (1)14", "bone": "glow_big", "color": "f10fffff", "blend": "additive"}, {"name": "glow (1)8", "bone": "bone10", "color": "f10fffff", "blend": "additive"}, {"name": "glow (1)10", "bone": "bone11", "color": "f10fffff", "blend": "additive"}, {"name": "glow (1)12", "bone": "bone14", "color": "f10fffff", "blend": "additive"}, {"name": "glow (1)6", "bone": "bone9", "color": "f10fffff", "blend": "additive"}, {"name": "shadow", "bone": "shadow", "color": "ffffff00", "attachment": "shadow"}], "ik": [{"name": "all10", "order": 6, "bones": ["all7", "all9"], "target": "all10"}, {"name": "all32", "order": 2, "bones": ["all25"], "target": "all32", "compress": true, "stretch": true}, {"name": "target", "bones": ["all17", "all20"], "target": "target", "bendPositive": false}, {"name": "target2", "order": 3, "bones": ["all28"], "target": "target2", "compress": true, "stretch": true}, {"name": "target3", "order": 4, "bones": ["all29", "all30"], "target": "target3", "bendPositive": false}, {"name": "target3a", "order": 5, "bones": ["all26", "all27"], "target": "target3"}, {"name": "target4", "order": 8, "bones": ["Asset 4"], "target": "target4", "compress": true, "stretch": true}, {"name": "target4b", "order": 9, "bones": ["all5"], "target": "target4", "compress": true, "stretch": true}, {"name": "target5", "order": 7, "bones": ["all4"], "target": "target5", "compress": true, "stretch": true}, {"name": "target6", "order": 10, "bones": ["Asset 14"], "target": "target6"}, {"name": "target7", "order": 11, "bones": ["head"], "target": "target7"}, {"name": "target8", "order": 12, "bones": ["all6"], "target": "target8"}], "transform": [{"name": "all20", "order": 1, "bones": ["all32"], "target": "all20", "mixRotate": 0, "mixX": 0.5, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"01": {"01": {"x": 174.94, "y": -7.8, "rotation": 93.5, "width": 139, "height": 395}, "01_a": {"x": 174.83, "y": -7.6, "rotation": 93.69, "width": 139, "height": 395}}, "02": {"02": {"x": 109.35, "y": -2.09, "rotation": 75.14, "width": 55, "height": 218}, "02_a": {"x": 109.88, "y": -1.81, "rotation": 75.14, "width": 55, "height": 218}}, "03": {"03": {"x": 131.58, "y": -2.75, "rotation": -105.42, "width": 71, "height": 282}, "03_a": {"x": 131.78, "y": -3.38, "rotation": -105.42, "width": 71, "height": 282}}, "04": {"04": {"x": 104.17, "y": -0.84, "rotation": -91.46, "width": 32, "height": 215}, "04_a": {"x": 103.83, "y": -0.99, "rotation": -91.98, "width": 32, "height": 215}}, "05": {"05": {"x": 85.86, "y": -26.6, "rotation": -82.06, "width": 74, "height": 184}, "05_a": {"x": 86.88, "y": -27.14, "rotation": -82.3, "width": 74, "height": 184}}, "06": {"06": {"x": 28.55, "y": 7.36, "rotation": 33.11, "width": 63, "height": 45}, "06_a": {"x": 29.41, "y": 6.64, "rotation": 33.11, "width": 63, "height": 45}}, "07": {"07": {"x": 112.82, "y": 120.93, "rotation": -53.85, "width": 177, "height": 357}, "07_a": {"x": 112.58, "y": 120.89, "rotation": -54.25, "width": 177, "height": 357}}, "Asset 1": {"Asset 1": {"type": "mesh", "uvs": [0.99999, 0.3008, 0.81823, 0.71191, 1, 0.76436, 1, 0.82466, 0.93106, 0.84887, 1, 0.90132, 1, 1, 0.29709, 1, 0.14803, 0.9392, 0.23407, 0.8733, 0.18026, 0.77382, 0.34275, 0.72473, 0, 0.30377, 0, 0, 1, 0, 0.62713, 0.75642, 0.48501, 0.88166, 0.38662, 0.96283, 0.64353, 0.97095], "triangles": [0, 13, 14, 12, 13, 0, 0, 11, 12, 1, 11, 0, 15, 11, 1, 15, 1, 2, 3, 15, 2, 4, 15, 3, 10, 16, 9, 15, 10, 11, 15, 16, 10, 4, 16, 15, 17, 9, 16, 8, 9, 17, 18, 16, 4, 18, 4, 5, 17, 16, 18, 7, 8, 17, 18, 5, 6, 7, 17, 18, 6, 7, 18], "vertices": [170.61, -40.46, 64.44, -10.56, 48.58, -22.76, 32.7, -20.46, 27.11, -14.15, 12.52, -17.54, -13.46, -13.77, -5.49, 41.19, 12.21, 50.52, 28.58, 41.28, 55.38, 41.69, 66.46, 27.11, 181.16, 37.83, 261.13, 26.24, 249.79, -51.95, 54.89, 6.08, 23.54, 21.98, 3.28, 32.77, -1.77, 12.99], "hull": 15, "edges": [26, 28, 24, 26, 22, 24, 0, 28, 0, 2, 2, 4, 18, 16, 12, 14, 16, 14, 4, 6, 6, 8, 10, 12, 8, 10, 20, 22, 20, 18], "width": 79, "height": 266}}, "Asset 4": {"Asset 4": {"type": "mesh", "uvs": [1, 0.41341, 1, 0.51799, 0.71252, 0.70583, 0.59344, 1, 0.50146, 1, 0.44767, 0.87451, 0.5672, 0.60896, 0.62532, 0.49054, 0.52941, 0.60471, 0.48173, 0.69995, 0.34352, 0.89422, 0.44774, 1, 0.27199, 1, 0.18404, 0.9434, 0.09946, 1, 0, 1, 0, 0.85927, 0.06675, 0.78468, 0.05347, 0.65, 0.09817, 0.49799, 0.23366, 0.40072, 0.33559, 0.37521, 0.44323, 0.07196, 0.618, 0, 0.90084, 0.05002, 0.03716, 0.89287, 0.10176, 0.92095, 0.16816, 0.90322, 0.21442, 0.90152, 0.27544, 0.94855, 0.34645, 0.98214, 0.49523, 0.88258, 0.51145, 0.92314, 0.59109, 0.88795], "triangles": [19, 28, 18, 28, 19, 20, 10, 20, 9, 21, 9, 20, 18, 28, 17, 9, 21, 8, 8, 21, 7, 21, 22, 7, 22, 23, 7, 7, 23, 24, 32, 5, 31, 2, 33, 6, 33, 31, 6, 31, 5, 6, 6, 7, 2, 2, 7, 1, 7, 0, 1, 7, 24, 0, 12, 30, 11, 12, 13, 29, 12, 29, 30, 29, 13, 28, 13, 26, 27, 13, 14, 26, 15, 25, 14, 14, 25, 26, 15, 16, 25, 29, 10, 30, 11, 30, 10, 29, 28, 10, 13, 27, 28, 27, 26, 17, 28, 27, 17, 17, 26, 25, 28, 20, 10, 25, 16, 17, 4, 32, 3, 32, 4, 5, 32, 33, 3, 3, 33, 2, 32, 31, 33], "vertices": [1, 7, 56.01, -26.06, 1, 1, 7, 47.88, -28.76, 1, 1, 7, 24.47, -6.28, 1, 1, 5, 82.85, 23.84, 1, 1, 5, 65.87, 23.17, 1, 1, 7, 1.41, 11.07, 1, 2, 7, 27.55, 10.03, 0.99043, 6, 51.95, -12.14, 0.00957, 2, 7, 38.54, 7.56, 0.5181, 6, 62.63, -8.12, 0.4819, 2, 7, 26.72, 13.73, 0.00455, 6, 49.22, -9.49, 0.99545, 1, 6, 40.55, -12.67, 1, 1, 5, 53.32, 37.1, 1, 1, 5, 64.3, 29.02, 1, 1, 5, 46.76, 27.88, 1, 1, 5, 37.68, 31.99, 1, 1, 5, 29.54, 26.76, 1, 1, 5, 19.62, 26.11, 1, 1, 5, 18.86, 37.77, 1, 1, 6, 3.8, 7.85, 1, 1, 6, 9.77, 17.4, 1, 1, 6, 21.16, 24.43, 1, 1, 6, 36.78, 22.23, 1, 1, 6, 46.05, 17.5, 1, 1, 6, 70.21, 30.37, 1, 1, 6, 87.57, 24.07, 1, 2, 7, 81.22, -7.26, 0.57373, 6, 107.02, 3.12, 0.42627, 2, 5, 22.75, 35.22, 0.88767, 6, -4.13, 2.71, 0.11233, 2, 5, 29.34, 33.32, 0.94149, 6, -0.55, -3.16, 0.05851, 2, 5, 35.87, 35.22, 0.76873, 6, 5.54, -6.17, 0.23127, 2, 5, 40.48, 35.66, 0.8332, 6, 9.24, -8.96, 0.1668, 2, 5, 46.82, 32.16, 0.95789, 6, 11.55, -15.82, 0.04211, 1, 5, 54.09, 29.84, 1, 2, 5, 71.97, 38.84, 0.42857, 7, 5.34, 6.48, 0.57143, 2, 5, 71.35, 33.67, 0.77429, 7, -0.03, 5.32, 0.22571, 2, 5, 82.45, 33.57, 0.64857, 7, 3.38, -5.85, 0.35143], "hull": 25, "edges": [30, 32, 32, 34, 34, 36, 38, 40, 46, 48, 22, 20, 20, 18, 12, 10, 10, 8, 28, 30, 26, 28, 22, 24, 26, 24, 40, 42, 42, 44, 44, 46, 12, 14, 14, 16, 16, 18, 0, 48, 6, 8, 0, 2, 4, 2, 4, 6, 38, 36], "width": 100, "height": 83}}, "Asset 5": {"Asset 5": {"type": "mesh", "uvs": [0.54181, 0.15912, 0.46281, 0.28166, 0.58605, 0.44735, 0.57025, 0.57161, 0.64461, 0.71664, 0.76584, 0.6939, 0.89663, 0.70879, 0.95436, 0.80644, 0.90608, 0.91915, 0.5707, 0.98799, 0.39458, 0.92202, 0.15159, 0.68819, 0.12516, 0.51974, 0.15854, 0.40381, 0.05138, 0.29314, 0.04339, 0.1672, 0.20584, 0.00937, 0.39329, 0, 0.83626, 0.76715, 0.90929, 0.83583, 0.86365, 0.87737, 0.3428, 0.04168, 0.3962, 0.09272, 0.43625, 0.14558, 0.30943, 0.07722, 0.39787, 0.19479], "triangles": [21, 16, 17, 0, 23, 22, 21, 17, 22, 24, 21, 22, 22, 17, 0, 25, 22, 23, 0, 25, 23, 18, 5, 6, 18, 6, 7, 19, 18, 7, 20, 18, 19, 8, 20, 19, 8, 19, 7, 4, 5, 18, 20, 4, 18, 9, 20, 8, 24, 16, 21, 15, 16, 24, 1, 25, 0, 22, 14, 15, 13, 14, 1, 24, 22, 15, 25, 14, 22, 1, 14, 25, 13, 1, 2, 12, 13, 2, 3, 12, 2, 11, 12, 3, 11, 3, 4, 10, 11, 4, 9, 10, 4, 20, 9, 4], "vertices": [1, 23, 98.53, -15.75, 1, 2, 25, -18.06, 15.69, 0.00494, 24, 16.18, 9.67, 0.99506, 3, 29, 39.69, -25.25, 0.00309, 25, 4.7, 20.44, 0.5498, 24, 36.53, 20.92, 0.44711, 4, 28, 32.12, -29.32, 0.00112, 29, 25.43, -17.59, 0.23068, 25, 20.39, 16.45, 0.71826, 24, 52.71, 21.72, 0.04994, 3, 28, 18.39, -15.37, 0.67452, 29, 6.07, -14.65, 0.31866, 25, 39.88, 18.27, 0.00681, 4, 28, 12.29, -22.11, 0.24724, 29, 5.23, -23.71, 0.1168, 25, 38.52, 27.27, 0.0025, 23, 167.2, -34.99, 0.63346, 1, 23, 173.34, -27.76, 1, 1, 23, 186.45, -30.22, 1, 1, 23, 197.68, -40.24, 1, 1, 28, 5.95, 18.06, 1, 2, 28, 21.04, 16.59, 0.8847, 29, -10.97, 12.5, 0.1153, 2, 29, 23.83, 15.74, 0.58314, 25, 29.98, -15.51, 0.41686, 2, 25, 8.1, -13.44, 0.99289, 24, 49.76, -10.45, 0.00711, 2, 25, -6.3, -8.41, 0.12425, 24, 34.52, -9.89, 0.87575, 2, 25, -21.82, -13.32, 0.06381, 24, 21.13, -19.15, 0.93619, 1, 24, 4.94, -21.66, 1, 1, 24, -16.8, -12.64, 1, 1, 23, 75.32, -15.13, 1, 4, 28, 3.31, -16.19, 0.14585, 29, -5.51, -24.36, 0.0689, 25, 48.78, 30.48, 0.00147, 23, 177.95, -35.15, 0.78377, 4, 28, -5.54, -10.87, 0.05785, 29, -15.78, -25.41, 0.02733, 25, 58.49, 33.98, 0.00058, 23, 188.27, -34.86, 0.91424, 4, 28, -5.31, -4.58, 0.1688, 29, -19.37, -20.24, 0.01831, 25, 63.23, 29.83, 0.00039, 23, 191.47, -40.29, 0.8125, 2, 24, -13.78, -2.49, 0.45561, 23, 78.36, -20.86, 0.54439, 2, 24, -7.64, 2.06, 0.32424, 23, 86, -20.7, 0.67576, 3, 25, -35.8, 17, 0.00108, 24, -1.16, 5.7, 0.41377, 23, 93.4, -21.49, 0.58515, 2, 24, -8.91, -4.29, 0.50623, 23, 81.29, -25.15, 0.49377, 3, 25, -30, 13.17, 0.00927, 24, 5.52, 3.76, 0.61148, 23, 97.72, -26.94, 0.37925], "hull": 18, "edges": [32, 34, 0, 2, 2, 4, 4, 6, 6, 8, 16, 14, 12, 14, 30, 32, 26, 24, 24, 22, 22, 20, 16, 18, 20, 18, 34, 0, 8, 10, 10, 12, 26, 28, 28, 30], "width": 71, "height": 130}}, "Asset 6": {"Asset 6": {"x": 27.68, "y": -2.4, "rotation": 155.68, "width": 104, "height": 66}}, "Asset 7": {"Asset 7": {"type": "mesh", "uvs": [1, 0.21543, 0.93745, 0.07766, 0.7965, 0.09977, 0.67153, 0.11938, 0.53624, 0.14061, 0.43009, 0.15726, 0.4115, 0.32864, 0.38839, 0.54171, 0.23684, 0.56189, 0.14865, 0.57363, 0.13316, 0.62567, 0.17705, 0.77875, 0.23257, 0.76344, 0.23515, 0.6471, 0.3707, 0.6471, 0.40427, 1, 0.26097, 1, 0.15898, 0.95019, 0, 0.55219, 0.00997, 0.41367, 0.07134, 0.41109, 0.20943, 0.42695, 0.32145, 0.43981, 0.34785, 0.26048, 0.3862, 0, 0.51806, 0, 0.65541, 0, 0.80379, 0, 1, 0], "triangles": [22, 23, 6, 7, 22, 6, 8, 21, 22, 8, 22, 7, 9, 20, 21, 9, 21, 8, 10, 20, 9, 17, 10, 11, 18, 19, 20, 20, 10, 18, 17, 18, 10, 14, 12, 13, 16, 12, 14, 17, 11, 12, 16, 17, 12, 16, 14, 15, 1, 27, 28, 27, 3, 26, 2, 27, 1, 2, 3, 27, 4, 25, 26, 4, 26, 3, 5, 24, 25, 5, 25, 4, 1, 28, 0, 23, 24, 5, 6, 23, 5], "vertices": [1, 15, -11.19, 14.66, 1, 1, 15, -1.54, 4.28, 1, 2, 15, 21.91, 4.12, 0.92364, 17, -44.08, 46.9, 0.07636, 2, 15, 42.7, 3.97, 0.85593, 17, -29, 32.59, 0.14407, 2, 15, 65.21, 3.82, 0.78263, 17, -12.68, 17.09, 0.21737, 2, 15, 82.87, 3.69, 0.72513, 17, 0.13, 4.94, 0.27487, 3, 15, 86.82, 15.43, 0.40188, 17, 11.04, 10.81, 0.38559, 18, -12.59, 15.57, 0.21253, 2, 17, 24.6, 18.1, 0.52324, 18, 2.59, 18.22, 0.47676, 2, 17, 42.7, 0.57, 0.37564, 18, 14.23, -4.13, 0.62436, 2, 17, 53.24, -9.63, 0.28974, 18, 21, -17.13, 0.71026, 2, 17, 57.66, -9.04, 0.11778, 18, 25.38, -17.98, 0.88222, 1, 18, 32.15, -6.92, 1, 1, 18, 27.38, 1.03, 1, 2, 17, 47.28, 4.41, 0.00272, 18, 19.78, -1.93, 0.99728, 1, 18, 10.52, 18.58, 1, 1, 18, 30.74, 33.83, 1, 1, 18, 40.53, 12.15, 1, 1, 18, 44.32, -4.72, 1, 1, 18, 29.79, -40.24, 1, 2, 17, 60.62, -34.14, 0.19587, 18, 20.28, -42.72, 0.80413, 2, 17, 53.58, -26.77, 0.55644, 18, 15.92, -33.51, 0.44356, 2, 17, 38.85, -9.18, 0.45706, 18, 7.49, -12.16, 0.54294, 2, 17, 26.9, 5.1, 0.37644, 18, 0.66, 5.15, 0.62356, 3, 15, 97.01, 9.9, 0.20438, 17, 14.7, -0.2, 0.42631, 18, -12.58, 3.98, 0.36931, 2, 15, 89.33, -7.82, 0.50125, 17, -3.02, -7.88, 0.49875, 2, 15, 67.5, -6.22, 0.60839, 17, -17.86, 8.2, 0.39161, 2, 15, 44.76, -4.56, 0.72, 17, -33.33, 24.95, 0.28, 2, 15, 20.19, -2.76, 0.84057, 17, -50.03, 43.05, 0.15943, 1, 15, -12.29, -0.38, 1], "hull": 29, "edges": [44, 14, 48, 10, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 36, 34, 30, 32, 34, 32, 36, 38, 0, 56, 2, 0, 38, 40, 52, 54, 54, 56, 2, 4, 4, 6, 48, 50, 50, 52, 6, 8, 8, 10, 10, 12, 12, 14, 44, 46, 46, 48, 40, 42, 42, 44, 14, 16, 16, 18], "width": 166, "height": 70}}, "Asset 8": {"Asset 8": {"type": "mesh", "uvs": [0.55539, 0, 0.64175, 0, 0.76196, 0.02025, 0.84676, 0.03004, 0.90691, 0.05619, 1, 0.17166, 0.99686, 0.2526, 0.96615, 0.35443, 0.92293, 0.48382, 0.8859, 0.59251, 0.86503, 0.71795, 0.8687, 0.89428, 0.83265, 0.99999, 0.81467, 1, 0.7397, 0.93031, 0.66428, 0.92243, 0.59804, 0.85397, 0.5222, 0.74938, 0.42494, 0.58408, 0.3227, 0.41032, 0.26623, 0.40371, 0.21141, 0.39729, 0.11954, 0.41894, 0, 0.44712, 0, 0.42885, 0.06847, 0.29162, 0.08491, 0.21822, 0.14891, 0.132, 0.25119, 0.08605, 0.36074, 0.03023, 0.45957, 0, 0.97763, 0.20093, 0.94859, 0.3216, 0.89053, 0.44227, 0.84973, 0.58199, 0.8356, 0.70742, 0.8356, 0.86302, 0.81991, 0.94717], "triangles": [13, 37, 12, 32, 4, 31, 10, 35, 34, 9, 10, 34, 36, 35, 10, 37, 36, 11, 8, 33, 32, 32, 31, 6, 7, 32, 6, 6, 31, 5, 8, 32, 7, 8, 9, 33, 36, 10, 11, 31, 4, 5, 12, 37, 11, 22, 26, 27, 22, 25, 26, 27, 21, 22, 24, 25, 22, 23, 24, 22, 21, 27, 28, 19, 20, 28, 21, 28, 20, 29, 19, 28, 30, 19, 29, 18, 19, 30, 0, 18, 30, 17, 18, 0, 17, 0, 1, 1, 34, 17, 9, 34, 33, 1, 2, 34, 35, 16, 34, 33, 34, 2, 32, 33, 3, 36, 14, 35, 37, 14, 36, 32, 3, 4, 33, 2, 3, 34, 16, 17, 35, 15, 16, 35, 14, 15, 13, 14, 37], "vertices": [3, 11, 108.67, -34.61, 0.0003, 12, 36.51, -38.46, 0.95937, 13, -29.95, -30.34, 0.04033, 2, 11, 87.9, -42.91, 0.0765, 12, 14.99, -44.56, 0.9235, 2, 11, 58.04, -52.07, 0.54127, 12, -15.67, -50.55, 0.45873, 2, 11, 37.18, -59.06, 0.81348, 12, -37.14, -55.33, 0.18652, 2, 11, 21.47, -61.74, 0.9125, 12, -53.04, -56.36, 0.0875, 1, 3, 217.74, 2.35, 1, 1, 3, 209.11, 8.14, 1, 1, 3, 201.67, 21.48, 1, 1, 3, 192.74, 39.36, 1, 1, 3, 185.34, 54.55, 1, 1, 3, 174.01, 67.15, 1, 1, 3, 153.88, 77.41, 1, 1, 3, 146.69, 92.19, 1, 3, 11, -1.22, 59.3, 0.40162, 12, -62.99, 66.39, 0.00141, 3, 148.97, 96.25, 0.59698, 1, 11, 20.13, 58.23, 1, 2, 11, 38.64, 64.54, 0.83526, 12, -22.81, 67.45, 0.16474, 3, 11, 57.82, 62.78, 0.66543, 12, -3.91, 63.7, 0.33357, 13, -34.8, 79.42, 0.001, 3, 11, 81.03, 57.65, 0.35272, 12, 18.63, 56.17, 0.61727, 13, -15.95, 64.94, 0.03001, 3, 11, 112.28, 47.36, 0.04203, 12, 48.64, 42.68, 0.64478, 13, 8.01, 42.39, 0.31319, 2, 12, 80.18, 28.5, 0.04798, 13, 33.19, 18.69, 0.95202, 3, 12, 94.48, 31.67, 0.00071, 13, 47.74, 17.02, 0.99858, 14, -14.86, 23.86, 0.00071, 2, 13, 61.87, 15.4, 0.83085, 14, -2.23, 17.32, 0.16915, 2, 13, 85.78, 16.82, 0.01213, 14, 20.62, 10.15, 0.98787, 1, 14, 50.36, 0.81, 1, 1, 14, 49.41, -1.33, 1, 1, 14, 26.05, -10.13, 1, 2, 13, 93.29, -9.34, 0.01437, 14, 18.33, -16.97, 0.98563, 2, 13, 76.12, -19.42, 0.49446, 14, -1.3, -20.29, 0.50554, 2, 13, 49.33, -23.8, 0.99975, 14, -27.89, -14.85, 0.00025, 2, 12, 83.96, -20.99, 0.04361, 13, 20.6, -29.33, 0.95639, 2, 12, 60.39, -31.69, 0.56542, 13, -5.17, -31.74, 0.43458, 1, 3, 217.32, 9.23, 1, 2, 11, -1.17, -34.21, 0.18529, 3, 207.56, 23.37, 0.81471, 3, 11, 7.06, -14.28, 0.83352, 12, -62.43, -7.65, 2e-05, 3, 201.5, 44.06, 0.16646, 2, 11, 10.23, 6.25, 0.90504, 3, 191.11, 62.05, 0.09496, 2, 11, 7.66, 22.51, 0.34484, 3, 178.93, 73.13, 0.65516, 3, 11, 0.26, 41, 0.28242, 12, -63.43, 48.04, 0.0001, 3, 161.58, 82.91, 0.71748, 3, 11, 0.04, 52.51, 0.47169, 12, -62.45, 59.51, 0.00095, 3, 154.2, 91.74, 0.52736], "hull": 31, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 46, 48, 48, 50, 50, 52, 52, 54, 42, 44, 44, 46, 38, 40, 40, 42, 54, 56, 2, 0, 0, 60, 34, 36, 36, 38, 2, 4, 4, 6, 30, 32, 32, 34, 14, 16, 16, 18, 56, 58, 58, 60], "width": 259, "height": 128}}, "Asset 9": {"Asset 9": {"type": "mesh", "uvs": [1, 0.26843, 0.91885, 0.50283, 0.60038, 0.64475, 0.60753, 0.73539, 0.53076, 0.78509, 0.52644, 0.88542, 0.43554, 0.91942, 0.43826, 1, 0.29325, 1, 0.28857, 0.94322, 0.27935, 0.92566, 0.17623, 0.95358, 0.04267, 0.92923, 0, 0.82466, 0, 0.7636, 0.05295, 0.80463, 0.11019, 0.81539, 0.10508, 0.79678, 0.26703, 0.67935, 0.27292, 0.61571, 0.36465, 0.54651, 0.40263, 0.55239, 0.47678, 0.49855, 0.47843, 0.43783, 0.57221, 0.4003, 0.77812, 0.00303, 1, 0, 0.33885, 0.67383, 0.4494, 0.83758, 0.44558, 0.81274, 0.4785, 0.5922, 0.30632, 0.77755, 0.35684, 0.87293, 0.25433, 0.77811, 0.49883, 0.68496, 0.38611, 0.72645, 0.40753, 0.62389], "triangles": [6, 28, 5, 5, 28, 4, 32, 29, 28, 32, 35, 29, 28, 29, 4, 29, 34, 4, 29, 35, 34, 4, 34, 3, 34, 2, 3, 35, 36, 34, 35, 27, 36, 36, 30, 34, 34, 30, 2, 36, 20, 21, 36, 27, 20, 27, 19, 20, 2, 22, 24, 2, 24, 1, 36, 21, 30, 21, 22, 30, 2, 30, 22, 24, 22, 23, 0, 1, 25, 1, 24, 25, 25, 26, 0, 8, 32, 7, 32, 6, 7, 8, 9, 32, 12, 16, 11, 11, 33, 10, 11, 16, 33, 9, 10, 32, 12, 15, 16, 12, 13, 15, 10, 31, 32, 10, 33, 31, 6, 32, 28, 32, 31, 35, 13, 14, 15, 16, 17, 33, 33, 17, 18, 33, 18, 31, 31, 27, 35, 31, 18, 27, 18, 19, 27], "vertices": [1, 10, -0.36, 27.31, 1, 1, 10, 40.09, 43.51, 1, 1, 10, 110.03, 11.23, 1, 1, 10, 119.17, 23.52, 1, 1, 10, 137.8, 17.65, 1, 2, 34, -28.61, 14.99, 0.0121, 10, 149.98, 29.34, 0.9879, 2, 34, -7.27, 18.8, 0.58904, 10, 169.2, 19.33, 0.41096, 2, 34, -6.68, 32.34, 0.87238, 10, 177.94, 29.68, 0.12762, 1, 34, 26.54, 29.34, 1, 1, 34, 26.75, 19.75, 1, 1, 34, 28.6, 16.62, 1, 1, 34, 52.64, 19.16, 1, 1, 34, 82.87, 12.33, 1, 1, 34, 91.07, -6.05, 1, 1, 34, 90.15, -16.27, 1, 1, 34, 78.64, -8.31, 1, 1, 34, 65.69, -5.33, 1, 1, 34, 66.58, -8.55, 1, 2, 34, 27.71, -24.85, 0.78572, 10, 170.23, -36.6, 0.21428, 2, 34, 25.4, -35.37, 0.61382, 10, 161.97, -43.52, 0.38618, 2, 34, 3.34, -45.06, 0.27325, 10, 138.59, -37.72, 0.72675, 2, 34, -5.27, -43.29, 0.15626, 10, 132.86, -31.05, 0.84374, 2, 34, -23.07, -50.77, 0.00919, 10, 114.2, -26.1, 0.99081, 2, 34, -24.36, -60.89, 0.00048, 10, 106.99, -33.33, 0.99952, 1, 10, 86.88, -23.3, 1, 1, 10, 6.78, -40.08, 1, 1, 10, -31, -5.78, 1, 2, 34, 11.17, -24.29, 0.42013, 10, 157.48, -26.05, 0.57987, 2, 34, -11.68, 5.39, 0.08986, 10, 157.52, 11.41, 0.91014, 1, 10, 155.33, 7.75, 1, 2, 34, -22.05, -35.06, 0.01978, 10, 124.6, -14.29, 0.98022, 2, 34, 20.19, -7.61, 0.87283, 10, 174.81, -18.35, 0.12717, 2, 34, 10.05, 9.4, 0.98702, 10, 177.18, 1.3, 0.01298, 2, 34, 32.11, -8.59, 0.97656, 10, 183.65, -26.41, 0.02344, 2, 34, -25.31, -19.12, 0.01033, 10, 131.76, 0.32, 0.98967, 2, 34, 1.14, -14.51, 0.25232, 10, 155.51, -12.18, 0.74768, 2, 34, -5.31, -31.23, 0.23892, 10, 140.19, -21.48, 0.76108], "hull": 27, "edges": [28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 12, 14, 12, 10, 10, 8, 8, 6, 6, 4, 0, 52, 2, 4, 0, 2, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 48, 50, 14, 16, 18, 16, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18], "width": 230, "height": 168}}, "Asset 27a3": {"Asset 27a": {"x": 143.42, "y": 31.67, "rotation": 102.95, "width": 115, "height": 285}}, "Asset 27b3": {"Asset 27b": {"x": 175.15, "y": -17.94, "rotation": -137.77, "width": 258, "height": 274}}, "Asset 27c3": {"Asset 27c": {"x": 172.03, "y": 6.15, "rotation": 124.1, "width": 192, "height": 292}}, "body": {"body": {"x": 72.64, "y": -32.51, "rotation": -119.43, "width": 296, "height": 210}}, "glow (1)": {"glow (1)": {"x": 3.38, "y": -6.84, "scaleX": 0.9273, "scaleY": 0.9273, "width": 128, "height": 128}}, "glow (1)2": {"glow (1)": {"x": -0.37, "y": 0.35, "scaleX": 2.8555, "scaleY": 2.8555, "width": 128, "height": 128}}, "glow (1)3": {"glow (1)": {"scaleX": 0.2123, "scaleY": 0.2123, "width": 128, "height": 128}}, "glow (1)4": {"glow (1)": {"scaleX": 0.4856, "scaleY": 0.4856, "width": 128, "height": 128}}, "glow (1)5": {"glow (1)": {"scaleX": 0.2123, "scaleY": 0.2123, "width": 128, "height": 128}}, "glow (1)6": {"glow (1)": {"scaleX": 0.4856, "scaleY": 0.4856, "width": 128, "height": 128}}, "glow (1)7": {"glow (1)": {"scaleX": 0.2123, "scaleY": 0.2123, "width": 128, "height": 128}}, "glow (1)8": {"glow (1)": {"scaleX": 0.4856, "scaleY": 0.4856, "width": 128, "height": 128}}, "glow (1)9": {"glow (1)": {"scaleX": 0.2123, "scaleY": 0.2123, "width": 128, "height": 128}}, "glow (1)10": {"glow (1)": {"scaleX": 0.4856, "scaleY": 0.4856, "width": 128, "height": 128}}, "glow (1)11": {"glow (1)": {"scaleX": 0.2123, "scaleY": 0.2123, "width": 128, "height": 128}}, "glow (1)12": {"glow (1)": {"scaleX": 0.4856, "scaleY": 0.4856, "width": 128, "height": 128}}, "glow (1)13": {"glow (1)": {"scaleX": 0.2123, "scaleY": 0.2123, "width": 128, "height": 128}}, "glow (1)14": {"glow (1)": {"scaleX": 0.4856, "scaleY": 0.4856, "width": 128, "height": 128}}, "glow (1)15": {"glow (1)": {"x": 3.38, "y": -6.84, "scaleX": 6.9948, "scaleY": 6.9948, "width": 128, "height": 128}}, "hand": {"hand": {"type": "mesh", "uvs": [0.57693, 0, 0.77389, 0.23661, 1, 0.50823, 1, 0.65779, 1, 0.81203, 0.88136, 0.84957, 0.8016, 0.80466, 0.67254, 0.86207, 0.55462, 0.94031, 0.49135, 0.99542, 0.42122, 1, 0.33857, 0.94629, 0.24583, 0.94629, 0.17525, 0.94629, 0.03856, 0.95261, 0, 0.85511, 0, 0.51984, 0.00037, 0.43379, 0.02893, 0.19446, 0.09591, 0.10171, 0.27285, 0.05805, 0.40699, 0.12141, 0.50812, 0, 0.26964, 0.14303, 0.32879, 0.26429, 0.32192, 0.70723, 0.47171, 0.71676, 0.71192, 0.65868, 0.96601, 0.72942, 0.47969, 0.29716, 0.73445, 0.38339, 0.8706, 0.4386, 0.58749, 0.29146, 0.96878, 0.5544, 0.85684, 0.69902, 0.58665, 0.67734, 0.18659, 0.30784, 0.19596, 0.70764, 0.9093, 0.57549, 0.76832, 0.52805, 0.6173, 0.51017, 0.52997, 0.50623, 0.409, 0.49976, 0.29589, 0.47538], "triangles": [26, 10, 25, 8, 26, 35, 25, 42, 26, 24, 21, 42, 22, 32, 29, 42, 21, 29, 41, 32, 40, 13, 14, 37, 37, 14, 15, 13, 37, 12, 16, 36, 37, 12, 37, 25, 16, 17, 36, 17, 18, 36, 18, 19, 36, 37, 15, 16, 37, 43, 25, 37, 36, 43, 25, 43, 42, 43, 24, 42, 36, 23, 43, 43, 23, 24, 23, 36, 20, 21, 24, 20, 24, 23, 20, 20, 36, 19, 27, 40, 30, 40, 32, 30, 35, 40, 27, 26, 41, 35, 26, 42, 41, 35, 41, 40, 42, 29, 41, 41, 29, 32, 12, 25, 11, 10, 11, 25, 9, 10, 26, 32, 22, 0, 29, 21, 22, 32, 0, 1, 8, 35, 7, 7, 27, 6, 7, 35, 27, 5, 28, 4, 6, 34, 5, 5, 38, 28, 5, 34, 38, 28, 3, 4, 6, 39, 34, 28, 33, 3, 28, 38, 33, 34, 31, 38, 33, 2, 3, 38, 31, 33, 33, 31, 2, 31, 39, 1, 1, 2, 31, 34, 39, 31, 27, 39, 6, 39, 30, 1, 27, 30, 39, 9, 26, 8, 30, 32, 1], "vertices": [2, 5, 99.54, 44.39, 0.38, 3, 118.17, -188.31, 0.62, 2, 5, 146.84, 27.58, 0.84857, 3, 78.21, -218.69, 0.15143, 2, 5, 201.13, 8.29, 0.99714, 3, 32.35, -253.56, 0.00286, 1, 5, 201.96, -4.24, 1, 1, 5, 202.81, -17.17, 1, 1, 5, 175.31, -22.14, 1, 1, 5, 156.44, -19.6, 1, 2, 5, 126.62, -26.4, 0.87042, 3, 44.11, -172.22, 0.12958, 2, 5, 99.52, -34.77, 0.82245, 3, 51.94, -144.96, 0.17755, 2, 5, 85.05, -40.37, 0.73475, 3, 55.18, -129.79, 0.26525, 2, 5, 68.7, -41.83, 0.63759, 3, 62.91, -115.31, 0.36241, 2, 5, 49.11, -38.6, 0.55913, 3, 76.34, -100.68, 0.44087, 1, 3, 87, -81.78, 1, 1, 3, 95.12, -67.39, 1, 1, 3, 110.37, -39.27, 1, 1, 3, 121.94, -35.44, 1, 1, 3, 146.47, -49.27, 1, 1, 3, 152.72, -52.9, 1, 1, 3, 166.95, -68.6, 1, 1, 3, 166.03, -86.08, 1, 2, 5, 28.86, 34.84, 0.20239, 3, 148.88, -123.94, 0.79761, 2, 5, 60.53, 31.59, 0.64544, 3, 128.82, -148.67, 0.35456, 2, 5, 83.47, 43.33, 0.48571, 3, 126.08, -174.29, 0.51429, 2, 5, 28.58, 27.67, 0.21976, 3, 143.03, -119.78, 0.78024, 2, 5, 43.33, 18.39, 0.17492, 3, 127.27, -126.96, 0.82508, 1, 3, 95.71, -107.19, 1, 1, 3, 77.11, -138.19, 1, 2, 5, 134.48, -8.73, 0.51843, 3, 54.57, -188.49, 0.48157, 1, 5, 202.18, -11.41, 1, 1, 3, 106.63, -157.5, 1, 2, 5, 138.99, 14.63, 0.60794, 3, 71.32, -205.49, 0.39206, 1, 5, 173.24, 11.92, 1, 2, 5, 105.36, 19.97, 0.23896, 3, 94.17, -180.33, 0.76104, 1, 5, 190.93, 4.2, 1, 1, 5, 163.46, -9.46, 1, 2, 5, 100.27, -11.8, 0.655, 3, 60.33, -171.52, 0.345, 1, 3, 139.74, -97.05, 1, 1, 3, 109.54, -82.3, 1, 2, 5, 175.76, 1.64, 0.89714, 3, 22.26, -252.32, 0.10286, 2, 5, 141.55, 3.53, 0.69334, 3, 46.14, -220.15, 0.30666, 2, 5, 106.22, 2.7, 0.30453, 3, 68.5, -185.38, 0.69547, 2, 5, 86.92, 1.6, 0.1262, 3, 80.17, -166.02, 0.8738, 1, 3, 96.8, -138.74, 1, 1, 3, 113.92, -113.69, 1], "hull": 23, "edges": [0, 44, 28, 30, 30, 32, 26, 28, 20, 22, 16, 18, 18, 20, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 22, 24, 24, 26, 4, 6, 6, 8, 0, 2, 2, 4, 12, 14, 14, 16, 8, 10, 10, 12], "width": 234, "height": 84}}, "hand_03": {"hand_03": {"type": "mesh", "uvs": [0.62496, 0.08253, 0.78037, 0.37154, 0.70419, 0.64157, 0.68738, 0.70119, 0.68552, 0.74891, 0.71505, 0.77347, 0.78737, 0.82458, 0.88018, 0.8663, 1, 0.94564, 1, 1, 0, 1, 0, 0.92831, 0.05104, 0.86934, 0.22332, 0.8327, 0.31998, 0.79132, 0.32363, 0.76186, 0.30568, 0.71356, 0.26132, 0.65228, 0.19585, 0.56184, 0, 0.36127, 0, 0, 0.50512, 0, 0.384, 0.70657, 0.60343, 0.6989, 0.49571, 0.64695, 0.62692, 0.77977, 0.43551, 0.78316], "triangles": [14, 15, 26, 25, 26, 4, 25, 4, 5, 4, 26, 15, 15, 23, 4, 23, 22, 24, 23, 15, 22, 15, 16, 22, 4, 23, 3, 16, 17, 22, 22, 17, 24, 3, 23, 2, 23, 24, 2, 17, 18, 24, 2, 24, 1, 1, 24, 18, 18, 19, 1, 19, 0, 1, 19, 21, 0, 19, 20, 21, 13, 14, 26, 6, 26, 25, 9, 13, 26, 9, 10, 13, 13, 11, 12, 9, 26, 6, 6, 7, 9, 7, 8, 9, 13, 10, 11, 25, 5, 6], "vertices": [1, 27, 7.63, 43.1, 1, 1, 27, 61.77, 48.58, 1, 1, 27, 106.57, 29.12, 1, 1, 27, 116.46, 24.83, 1, 1, 27, 124.68, 22.68, 1, 2, 31, 22.5, 13.33, 0.58468, 27, 129.71, 24.95, 0.41532, 1, 31, 30.75, 4.23, 1, 1, 31, 41.33, -3.19, 1, 1, 31, 54.99, -17.32, 1, 1, 31, 54.99, -26.99, 1, 1, 31, -59.01, -26.99, 1, 1, 31, -59.01, -14.23, 1, 1, 31, -53.2, -3.74, 1, 1, 31, -33.56, 2.79, 1, 2, 31, -22.54, 10.15, 0.59572, 27, 122.49, -19.61, 0.40428, 1, 27, 117.48, -18.01, 1, 1, 27, 108.65, -18.03, 1, 1, 27, 96.87, -20.46, 1, 1, 27, 79.49, -24.04, 1, 1, 27, 39.63, -37.61, 1, 1, 27, -22.97, -22.89, 1, 1, 27, -9.79, 33.17, 1, 1, 27, 109.48, -9.06, 1, 1, 27, 113.88, 15.61, 1, 1, 27, 102.06, 5.77, 1, 2, 31, 12.45, 12.21, 0.64365, 27, 128.5, 14.92, 0.35635, 2, 31, -9.37, 11.6, 0.73632, 27, 124.09, -6.46, 0.26368], "hull": 22, "edges": [18, 20, 30, 8, 18, 16, 20, 22, 38, 40, 38, 36, 32, 30, 40, 42, 42, 0, 0, 2, 6, 8, 32, 34, 34, 36, 2, 4, 4, 6, 28, 30, 8, 10, 26, 28, 22, 24, 24, 26, 10, 12, 12, 14, 14, 16], "width": 114, "height": 178}}, "hand_04": {"hand_04": {"type": "mesh", "uvs": [0.62054, 0.13768, 0.78821, 0.33971, 0.84918, 0.52381, 1, 0.73887, 1, 0.87735, 0.91616, 0.95764, 0.76788, 1, 0.46811, 1, 0.41476, 0.92134, 0.39189, 0.78937, 0.24454, 0.65741, 0.20136, 0.57106, 0, 0.46516, 0, 0.37247, 0.05444, 0.29279, 0.03332, 0.22144, 0.04312, 0.12086, 0.12224, 0.04635, 0.2474, 0, 0.41984, 0], "triangles": [0, 14, 18, 18, 19, 0, 17, 18, 15, 17, 15, 16, 18, 14, 15, 11, 12, 13, 14, 1, 11, 11, 13, 14, 1, 14, 0, 2, 10, 11, 2, 11, 1, 9, 10, 2, 9, 2, 3, 6, 9, 3, 9, 6, 8, 6, 3, 4, 6, 7, 8, 6, 4, 5], "vertices": [1, 3, 159.1, 13.81, 1, 1, 23, 41.91, 48.44, 1, 1, 23, 75.1, 38.57, 1, 1, 23, 118.37, 35.3, 1, 1, 23, 140.75, 23.12, 1, 1, 23, 149, 7.37, 1, 1, 23, 147.49, -11.72, 1, 1, 23, 130.59, -42.8, 1, 1, 23, 114.86, -41.41, 1, 1, 23, 92.24, -32.18, 1, 1, 23, 62.61, -35.85, 1, 1, 23, 46.21, -32.74, 1, 1, 23, 17.74, -44.3, 1, 1, 23, 2.76, -36.15, 1, 1, 23, -7.05, -23.5, 1, 1, 23, -19.77, -19.42, 1, 1, 23, -35.48, -9.56, 1, 2, 23, -43.06, 5.19, 0.26286, 3, 202.63, 56.77, 0.73714, 1, 3, 202.8, 39.71, 1, 1, 3, 192.8, 21.99, 1], "hull": 20, "edges": [36, 34, 34, 32, 30, 32, 30, 28, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 10, 8, 10, 12, 12, 14, 36, 38, 38, 0, 0, 2, 8, 6, 4, 6, 2, 4, 24, 26, 28, 26], "width": 118, "height": 184}}, "head": {"head": {"x": 35.94, "y": 0.28, "rotation": -130.41, "width": 141, "height": 123}}, "leg_01": {"leg_01": {"x": 85.01, "y": -0.23, "rotation": -166.49, "width": 217, "height": 93}}, "lighting01": {"lighting01": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 43, 46.47, -11.09, 0.99997, 44, 25.99, -501.48, 3e-05, 1, 43, 14.47, -11.09, 1, 1, 43, -17.53, -11.09, 1, 2, 43, -17.53, 9.39, 0.9975, 44, -38.01, -481, 0.0025, 2, 43, -17.53, 29.87, 0.98643, 44, -38.01, -460.52, 0.01357, 2, 43, -17.53, 50.35, 0.9671, 44, -38.01, -440.04, 0.0329, 2, 43, -17.53, 70.83, 0.94045, 44, -38.01, -419.56, 0.05955, 2, 43, -17.53, 91.31, 0.90703, 44, -38.01, -399.08, 0.09297, 2, 43, -17.53, 111.79, 0.86732, 44, -38.01, -378.6, 0.13268, 2, 43, -17.53, 132.27, 0.82193, 44, -38.01, -358.12, 0.17807, 2, 43, -17.53, 152.75, 0.77146, 44, -38.01, -337.64, 0.22854, 2, 43, -17.53, 173.23, 0.71662, 44, -38.01, -317.16, 0.28338, 2, 43, -17.53, 193.71, 0.65818, 44, -38.01, -296.68, 0.34182, 2, 43, -17.53, 214.19, 0.59701, 44, -38.01, -276.2, 0.40299, 2, 43, -17.53, 234.67, 0.53408, 44, -38.01, -255.72, 0.46592, 2, 43, -17.53, 255.15, 0.47044, 44, -38.01, -235.24, 0.52956, 2, 43, -17.53, 275.63, 0.40718, 44, -38.01, -214.76, 0.59281, 2, 43, -17.53, 296.11, 0.34547, 44, -38.01, -194.28, 0.65453, 2, 43, -17.53, 316.59, 0.28634, 44, -38.01, -173.8, 0.71366, 2, 43, -17.53, 337.07, 0.23082, 44, -38.01, -153.32, 0.76918, 2, 43, -17.53, 357.55, 0.17974, 44, -38.01, -132.84, 0.82026, 2, 43, -17.53, 378.03, 0.13386, 44, -38.01, -112.36, 0.86614, 2, 43, -17.53, 398.51, 0.09381, 44, -38.01, -91.88, 0.90619, 2, 43, -17.53, 418.99, 0.0602, 44, -38.01, -71.4, 0.9398, 2, 43, -17.53, 439.47, 0.03363, 44, -38.01, -50.92, 0.96637, 2, 43, -17.53, 459.95, 0.01452, 44, -38.01, -30.44, 0.98548, 2, 43, -17.53, 480.43, 0.00346, 44, -38.01, -9.96, 0.99654, 2, 43, -17.53, 500.91, 2e-05, 44, -38.01, 10.52, 0.99998, 1, 44, -6.01, 10.52, 1, 1, 44, 25.99, 10.52, 1, 2, 43, 46.47, 480.43, 0.00284, 44, 25.99, -9.96, 0.99716, 2, 43, 46.47, 459.95, 0.01535, 44, 25.99, -30.44, 0.98465, 2, 43, 46.47, 439.47, 0.03315, 44, 25.99, -50.92, 0.96685, 2, 43, 46.47, 418.99, 0.06005, 44, 25.99, -71.4, 0.93995, 2, 43, 46.47, 398.51, 0.09372, 44, 25.99, -91.88, 0.90628, 2, 43, 46.47, 378.03, 0.1338, 44, 25.99, -112.36, 0.8662, 2, 43, 46.47, 357.55, 0.1797, 44, 25.99, -132.84, 0.8203, 2, 43, 46.47, 337.07, 0.23078, 44, 25.99, -153.32, 0.76922, 2, 43, 46.47, 316.59, 0.28631, 44, 25.99, -173.8, 0.71369, 2, 43, 46.47, 296.11, 0.3454, 44, 25.99, -194.28, 0.6546, 2, 43, 46.47, 275.63, 0.40729, 44, 25.99, -214.76, 0.59271, 2, 43, 46.47, 255.15, 0.47048, 44, 25.99, -235.24, 0.52952, 2, 43, 46.47, 234.67, 0.53409, 44, 25.99, -255.72, 0.46591, 2, 43, 46.47, 214.19, 0.59703, 44, 25.99, -276.2, 0.40297, 2, 43, 46.47, 193.71, 0.65819, 44, 25.99, -296.68, 0.34181, 2, 43, 46.47, 173.23, 0.71662, 44, 25.99, -317.16, 0.28338, 2, 43, 46.47, 152.75, 0.77144, 44, 25.99, -337.64, 0.22856, 2, 43, 46.47, 132.27, 0.82191, 44, 25.99, -358.12, 0.17809, 2, 43, 46.47, 111.79, 0.86727, 44, 25.99, -378.6, 0.13273, 2, 43, 46.47, 91.31, 0.90689, 44, 25.99, -399.08, 0.09311, 2, 43, 46.47, 70.83, 0.94016, 44, 25.99, -419.56, 0.05984, 2, 43, 46.47, 50.35, 0.96652, 44, 25.99, -440.04, 0.03348, 2, 43, 46.47, 29.87, 0.98537, 44, 25.99, -460.52, 0.01463, 2, 43, 46.47, 9.39, 0.99644, 44, 25.99, -481, 0.00356, 2, 43, 14.47, 9.39, 0.9971, 44, -6.01, -481, 0.0029, 2, 43, 14.47, 29.87, 0.98595, 44, -6.01, -460.52, 0.01405, 2, 43, 14.47, 50.35, 0.96682, 44, -6.01, -440.04, 0.03318, 2, 43, 14.47, 70.83, 0.94032, 44, -6.01, -419.56, 0.05968, 2, 43, 14.47, 91.31, 0.90696, 44, -6.01, -399.08, 0.09304, 2, 43, 14.47, 111.79, 0.8673, 44, -6.01, -378.6, 0.1327, 2, 43, 14.47, 132.27, 0.82192, 44, -6.01, -358.12, 0.17808, 2, 43, 14.47, 152.75, 0.77145, 44, -6.01, -337.64, 0.22855, 2, 43, 14.47, 173.23, 0.71661, 44, -6.01, -317.16, 0.28339, 2, 43, 14.47, 193.71, 0.65818, 44, -6.01, -296.68, 0.34182, 2, 43, 14.47, 214.19, 0.59702, 44, -6.01, -276.2, 0.40298, 2, 43, 14.47, 234.67, 0.5341, 44, -6.01, -255.72, 0.4659, 2, 43, 14.47, 255.15, 0.47045, 44, -6.01, -235.24, 0.52955, 2, 43, 14.47, 275.63, 0.40719, 44, -6.01, -214.76, 0.59281, 2, 43, 14.47, 296.11, 0.34545, 44, -6.01, -194.28, 0.65455, 2, 43, 14.47, 316.59, 0.28633, 44, -6.01, -173.8, 0.71367, 2, 43, 14.47, 337.07, 0.2308, 44, -6.01, -153.32, 0.7692, 2, 43, 14.47, 357.55, 0.17972, 44, -6.01, -132.84, 0.82028, 2, 43, 14.47, 378.03, 0.13383, 44, -6.01, -112.36, 0.86617, 2, 43, 14.47, 398.51, 0.09376, 44, -6.01, -91.88, 0.90624, 2, 43, 14.47, 418.99, 0.06012, 44, -6.01, -71.4, 0.93988, 2, 43, 14.47, 439.47, 0.03345, 44, -6.01, -50.92, 0.96655, 2, 43, 14.47, 459.95, 0.0142, 44, -6.01, -30.44, 0.9858, 2, 43, 14.47, 480.43, 0.00272, 44, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting02": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 43, 46.47, -11.09, 0.99997, 44, 25.99, -501.48, 3e-05, 1, 43, 14.47, -11.09, 1, 1, 43, -17.53, -11.09, 1, 2, 43, -17.53, 9.39, 0.9975, 44, -38.01, -481, 0.0025, 2, 43, -17.53, 29.87, 0.98643, 44, -38.01, -460.52, 0.01357, 2, 43, -17.53, 50.35, 0.9671, 44, -38.01, -440.04, 0.0329, 2, 43, -17.53, 70.83, 0.94045, 44, -38.01, -419.56, 0.05955, 2, 43, -17.53, 91.31, 0.90703, 44, -38.01, -399.08, 0.09297, 2, 43, -17.53, 111.79, 0.86732, 44, -38.01, -378.6, 0.13268, 2, 43, -17.53, 132.27, 0.82193, 44, -38.01, -358.12, 0.17807, 2, 43, -17.53, 152.75, 0.77146, 44, -38.01, -337.64, 0.22854, 2, 43, -17.53, 173.23, 0.71662, 44, -38.01, -317.16, 0.28338, 2, 43, -17.53, 193.71, 0.65818, 44, -38.01, -296.68, 0.34182, 2, 43, -17.53, 214.19, 0.59701, 44, -38.01, -276.2, 0.40299, 2, 43, -17.53, 234.67, 0.53408, 44, -38.01, -255.72, 0.46592, 2, 43, -17.53, 255.15, 0.47044, 44, -38.01, -235.24, 0.52956, 2, 43, -17.53, 275.63, 0.40718, 44, -38.01, -214.76, 0.59281, 2, 43, -17.53, 296.11, 0.34547, 44, -38.01, -194.28, 0.65453, 2, 43, -17.53, 316.59, 0.28634, 44, -38.01, -173.8, 0.71366, 2, 43, -17.53, 337.07, 0.23082, 44, -38.01, -153.32, 0.76918, 2, 43, -17.53, 357.55, 0.17974, 44, -38.01, -132.84, 0.82026, 2, 43, -17.53, 378.03, 0.13386, 44, -38.01, -112.36, 0.86614, 2, 43, -17.53, 398.51, 0.09381, 44, -38.01, -91.88, 0.90619, 2, 43, -17.53, 418.99, 0.0602, 44, -38.01, -71.4, 0.9398, 2, 43, -17.53, 439.47, 0.03363, 44, -38.01, -50.92, 0.96637, 2, 43, -17.53, 459.95, 0.01452, 44, -38.01, -30.44, 0.98548, 2, 43, -17.53, 480.43, 0.00346, 44, -38.01, -9.96, 0.99654, 2, 43, -17.53, 500.91, 2e-05, 44, -38.01, 10.52, 0.99998, 1, 44, -6.01, 10.52, 1, 1, 44, 25.99, 10.52, 1, 2, 43, 46.47, 480.43, 0.00284, 44, 25.99, -9.96, 0.99716, 2, 43, 46.47, 459.95, 0.01535, 44, 25.99, -30.44, 0.98465, 2, 43, 46.47, 439.47, 0.03315, 44, 25.99, -50.92, 0.96685, 2, 43, 46.47, 418.99, 0.06005, 44, 25.99, -71.4, 0.93995, 2, 43, 46.47, 398.51, 0.09372, 44, 25.99, -91.88, 0.90628, 2, 43, 46.47, 378.03, 0.1338, 44, 25.99, -112.36, 0.8662, 2, 43, 46.47, 357.55, 0.1797, 44, 25.99, -132.84, 0.8203, 2, 43, 46.47, 337.07, 0.23078, 44, 25.99, -153.32, 0.76922, 2, 43, 46.47, 316.59, 0.28631, 44, 25.99, -173.8, 0.71369, 2, 43, 46.47, 296.11, 0.3454, 44, 25.99, -194.28, 0.6546, 2, 43, 46.47, 275.63, 0.40729, 44, 25.99, -214.76, 0.59271, 2, 43, 46.47, 255.15, 0.47048, 44, 25.99, -235.24, 0.52952, 2, 43, 46.47, 234.67, 0.53409, 44, 25.99, -255.72, 0.46591, 2, 43, 46.47, 214.19, 0.59703, 44, 25.99, -276.2, 0.40297, 2, 43, 46.47, 193.71, 0.65819, 44, 25.99, -296.68, 0.34181, 2, 43, 46.47, 173.23, 0.71662, 44, 25.99, -317.16, 0.28338, 2, 43, 46.47, 152.75, 0.77144, 44, 25.99, -337.64, 0.22856, 2, 43, 46.47, 132.27, 0.82191, 44, 25.99, -358.12, 0.17809, 2, 43, 46.47, 111.79, 0.86727, 44, 25.99, -378.6, 0.13273, 2, 43, 46.47, 91.31, 0.90689, 44, 25.99, -399.08, 0.09311, 2, 43, 46.47, 70.83, 0.94016, 44, 25.99, -419.56, 0.05984, 2, 43, 46.47, 50.35, 0.96652, 44, 25.99, -440.04, 0.03348, 2, 43, 46.47, 29.87, 0.98537, 44, 25.99, -460.52, 0.01463, 2, 43, 46.47, 9.39, 0.99644, 44, 25.99, -481, 0.00356, 2, 43, 14.47, 9.39, 0.9971, 44, -6.01, -481, 0.0029, 2, 43, 14.47, 29.87, 0.98595, 44, -6.01, -460.52, 0.01405, 2, 43, 14.47, 50.35, 0.96682, 44, -6.01, -440.04, 0.03318, 2, 43, 14.47, 70.83, 0.94032, 44, -6.01, -419.56, 0.05968, 2, 43, 14.47, 91.31, 0.90696, 44, -6.01, -399.08, 0.09304, 2, 43, 14.47, 111.79, 0.8673, 44, -6.01, -378.6, 0.1327, 2, 43, 14.47, 132.27, 0.82192, 44, -6.01, -358.12, 0.17808, 2, 43, 14.47, 152.75, 0.77145, 44, -6.01, -337.64, 0.22855, 2, 43, 14.47, 173.23, 0.71661, 44, -6.01, -317.16, 0.28339, 2, 43, 14.47, 193.71, 0.65818, 44, -6.01, -296.68, 0.34182, 2, 43, 14.47, 214.19, 0.59702, 44, -6.01, -276.2, 0.40298, 2, 43, 14.47, 234.67, 0.5341, 44, -6.01, -255.72, 0.4659, 2, 43, 14.47, 255.15, 0.47045, 44, -6.01, -235.24, 0.52955, 2, 43, 14.47, 275.63, 0.40719, 44, -6.01, -214.76, 0.59281, 2, 43, 14.47, 296.11, 0.34545, 44, -6.01, -194.28, 0.65455, 2, 43, 14.47, 316.59, 0.28633, 44, -6.01, -173.8, 0.71367, 2, 43, 14.47, 337.07, 0.2308, 44, -6.01, -153.32, 0.7692, 2, 43, 14.47, 357.55, 0.17972, 44, -6.01, -132.84, 0.82028, 2, 43, 14.47, 378.03, 0.13383, 44, -6.01, -112.36, 0.86617, 2, 43, 14.47, 398.51, 0.09376, 44, -6.01, -91.88, 0.90624, 2, 43, 14.47, 418.99, 0.06012, 44, -6.01, -71.4, 0.93988, 2, 43, 14.47, 439.47, 0.03345, 44, -6.01, -50.92, 0.96655, 2, 43, 14.47, 459.95, 0.0142, 44, -6.01, -30.44, 0.9858, 2, 43, 14.47, 480.43, 0.00272, 44, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting03": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 43, 46.47, -11.09, 0.99997, 44, 25.99, -501.48, 3e-05, 1, 43, 14.47, -11.09, 1, 1, 43, -17.53, -11.09, 1, 2, 43, -17.53, 9.39, 0.9975, 44, -38.01, -481, 0.0025, 2, 43, -17.53, 29.87, 0.98643, 44, -38.01, -460.52, 0.01357, 2, 43, -17.53, 50.35, 0.9671, 44, -38.01, -440.04, 0.0329, 2, 43, -17.53, 70.83, 0.94045, 44, -38.01, -419.56, 0.05955, 2, 43, -17.53, 91.31, 0.90703, 44, -38.01, -399.08, 0.09297, 2, 43, -17.53, 111.79, 0.86732, 44, -38.01, -378.6, 0.13268, 2, 43, -17.53, 132.27, 0.82193, 44, -38.01, -358.12, 0.17807, 2, 43, -17.53, 152.75, 0.77146, 44, -38.01, -337.64, 0.22854, 2, 43, -17.53, 173.23, 0.71662, 44, -38.01, -317.16, 0.28338, 2, 43, -17.53, 193.71, 0.65818, 44, -38.01, -296.68, 0.34182, 2, 43, -17.53, 214.19, 0.59701, 44, -38.01, -276.2, 0.40299, 2, 43, -17.53, 234.67, 0.53408, 44, -38.01, -255.72, 0.46592, 2, 43, -17.53, 255.15, 0.47044, 44, -38.01, -235.24, 0.52956, 2, 43, -17.53, 275.63, 0.40718, 44, -38.01, -214.76, 0.59281, 2, 43, -17.53, 296.11, 0.34547, 44, -38.01, -194.28, 0.65453, 2, 43, -17.53, 316.59, 0.28634, 44, -38.01, -173.8, 0.71366, 2, 43, -17.53, 337.07, 0.23082, 44, -38.01, -153.32, 0.76918, 2, 43, -17.53, 357.55, 0.17974, 44, -38.01, -132.84, 0.82026, 2, 43, -17.53, 378.03, 0.13386, 44, -38.01, -112.36, 0.86614, 2, 43, -17.53, 398.51, 0.09381, 44, -38.01, -91.88, 0.90619, 2, 43, -17.53, 418.99, 0.0602, 44, -38.01, -71.4, 0.9398, 2, 43, -17.53, 439.47, 0.03363, 44, -38.01, -50.92, 0.96637, 2, 43, -17.53, 459.95, 0.01452, 44, -38.01, -30.44, 0.98548, 2, 43, -17.53, 480.43, 0.00346, 44, -38.01, -9.96, 0.99654, 2, 43, -17.53, 500.91, 2e-05, 44, -38.01, 10.52, 0.99998, 1, 44, -6.01, 10.52, 1, 1, 44, 25.99, 10.52, 1, 2, 43, 46.47, 480.43, 0.00284, 44, 25.99, -9.96, 0.99716, 2, 43, 46.47, 459.95, 0.01535, 44, 25.99, -30.44, 0.98465, 2, 43, 46.47, 439.47, 0.03315, 44, 25.99, -50.92, 0.96685, 2, 43, 46.47, 418.99, 0.06005, 44, 25.99, -71.4, 0.93995, 2, 43, 46.47, 398.51, 0.09372, 44, 25.99, -91.88, 0.90628, 2, 43, 46.47, 378.03, 0.1338, 44, 25.99, -112.36, 0.8662, 2, 43, 46.47, 357.55, 0.1797, 44, 25.99, -132.84, 0.8203, 2, 43, 46.47, 337.07, 0.23078, 44, 25.99, -153.32, 0.76922, 2, 43, 46.47, 316.59, 0.28631, 44, 25.99, -173.8, 0.71369, 2, 43, 46.47, 296.11, 0.3454, 44, 25.99, -194.28, 0.6546, 2, 43, 46.47, 275.63, 0.40729, 44, 25.99, -214.76, 0.59271, 2, 43, 46.47, 255.15, 0.47048, 44, 25.99, -235.24, 0.52952, 2, 43, 46.47, 234.67, 0.53409, 44, 25.99, -255.72, 0.46591, 2, 43, 46.47, 214.19, 0.59703, 44, 25.99, -276.2, 0.40297, 2, 43, 46.47, 193.71, 0.65819, 44, 25.99, -296.68, 0.34181, 2, 43, 46.47, 173.23, 0.71662, 44, 25.99, -317.16, 0.28338, 2, 43, 46.47, 152.75, 0.77144, 44, 25.99, -337.64, 0.22856, 2, 43, 46.47, 132.27, 0.82191, 44, 25.99, -358.12, 0.17809, 2, 43, 46.47, 111.79, 0.86727, 44, 25.99, -378.6, 0.13273, 2, 43, 46.47, 91.31, 0.90689, 44, 25.99, -399.08, 0.09311, 2, 43, 46.47, 70.83, 0.94016, 44, 25.99, -419.56, 0.05984, 2, 43, 46.47, 50.35, 0.96652, 44, 25.99, -440.04, 0.03348, 2, 43, 46.47, 29.87, 0.98537, 44, 25.99, -460.52, 0.01463, 2, 43, 46.47, 9.39, 0.99644, 44, 25.99, -481, 0.00356, 2, 43, 14.47, 9.39, 0.9971, 44, -6.01, -481, 0.0029, 2, 43, 14.47, 29.87, 0.98595, 44, -6.01, -460.52, 0.01405, 2, 43, 14.47, 50.35, 0.96682, 44, -6.01, -440.04, 0.03318, 2, 43, 14.47, 70.83, 0.94032, 44, -6.01, -419.56, 0.05968, 2, 43, 14.47, 91.31, 0.90696, 44, -6.01, -399.08, 0.09304, 2, 43, 14.47, 111.79, 0.8673, 44, -6.01, -378.6, 0.1327, 2, 43, 14.47, 132.27, 0.82192, 44, -6.01, -358.12, 0.17808, 2, 43, 14.47, 152.75, 0.77145, 44, -6.01, -337.64, 0.22855, 2, 43, 14.47, 173.23, 0.71661, 44, -6.01, -317.16, 0.28339, 2, 43, 14.47, 193.71, 0.65818, 44, -6.01, -296.68, 0.34182, 2, 43, 14.47, 214.19, 0.59702, 44, -6.01, -276.2, 0.40298, 2, 43, 14.47, 234.67, 0.5341, 44, -6.01, -255.72, 0.4659, 2, 43, 14.47, 255.15, 0.47045, 44, -6.01, -235.24, 0.52955, 2, 43, 14.47, 275.63, 0.40719, 44, -6.01, -214.76, 0.59281, 2, 43, 14.47, 296.11, 0.34545, 44, -6.01, -194.28, 0.65455, 2, 43, 14.47, 316.59, 0.28633, 44, -6.01, -173.8, 0.71367, 2, 43, 14.47, 337.07, 0.2308, 44, -6.01, -153.32, 0.7692, 2, 43, 14.47, 357.55, 0.17972, 44, -6.01, -132.84, 0.82028, 2, 43, 14.47, 378.03, 0.13383, 44, -6.01, -112.36, 0.86617, 2, 43, 14.47, 398.51, 0.09376, 44, -6.01, -91.88, 0.90624, 2, 43, 14.47, 418.99, 0.06012, 44, -6.01, -71.4, 0.93988, 2, 43, 14.47, 439.47, 0.03345, 44, -6.01, -50.92, 0.96655, 2, 43, 14.47, 459.95, 0.0142, 44, -6.01, -30.44, 0.9858, 2, 43, 14.47, 480.43, 0.00272, 44, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting04": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 43, 46.47, -11.09, 0.99997, 44, 25.99, -501.48, 3e-05, 1, 43, 14.47, -11.09, 1, 1, 43, -17.53, -11.09, 1, 2, 43, -17.53, 9.39, 0.9975, 44, -38.01, -481, 0.0025, 2, 43, -17.53, 29.87, 0.98643, 44, -38.01, -460.52, 0.01357, 2, 43, -17.53, 50.35, 0.9671, 44, -38.01, -440.04, 0.0329, 2, 43, -17.53, 70.83, 0.94045, 44, -38.01, -419.56, 0.05955, 2, 43, -17.53, 91.31, 0.90703, 44, -38.01, -399.08, 0.09297, 2, 43, -17.53, 111.79, 0.86732, 44, -38.01, -378.6, 0.13268, 2, 43, -17.53, 132.27, 0.82193, 44, -38.01, -358.12, 0.17807, 2, 43, -17.53, 152.75, 0.77146, 44, -38.01, -337.64, 0.22854, 2, 43, -17.53, 173.23, 0.71662, 44, -38.01, -317.16, 0.28338, 2, 43, -17.53, 193.71, 0.65818, 44, -38.01, -296.68, 0.34182, 2, 43, -17.53, 214.19, 0.59701, 44, -38.01, -276.2, 0.40299, 2, 43, -17.53, 234.67, 0.53408, 44, -38.01, -255.72, 0.46592, 2, 43, -17.53, 255.15, 0.47044, 44, -38.01, -235.24, 0.52956, 2, 43, -17.53, 275.63, 0.40718, 44, -38.01, -214.76, 0.59281, 2, 43, -17.53, 296.11, 0.34547, 44, -38.01, -194.28, 0.65453, 2, 43, -17.53, 316.59, 0.28634, 44, -38.01, -173.8, 0.71366, 2, 43, -17.53, 337.07, 0.23082, 44, -38.01, -153.32, 0.76918, 2, 43, -17.53, 357.55, 0.17974, 44, -38.01, -132.84, 0.82026, 2, 43, -17.53, 378.03, 0.13386, 44, -38.01, -112.36, 0.86614, 2, 43, -17.53, 398.51, 0.09381, 44, -38.01, -91.88, 0.90619, 2, 43, -17.53, 418.99, 0.0602, 44, -38.01, -71.4, 0.9398, 2, 43, -17.53, 439.47, 0.03363, 44, -38.01, -50.92, 0.96637, 2, 43, -17.53, 459.95, 0.01452, 44, -38.01, -30.44, 0.98548, 2, 43, -17.53, 480.43, 0.00346, 44, -38.01, -9.96, 0.99654, 2, 43, -17.53, 500.91, 2e-05, 44, -38.01, 10.52, 0.99998, 1, 44, -6.01, 10.52, 1, 1, 44, 25.99, 10.52, 1, 2, 43, 46.47, 480.43, 0.00284, 44, 25.99, -9.96, 0.99716, 2, 43, 46.47, 459.95, 0.01535, 44, 25.99, -30.44, 0.98465, 2, 43, 46.47, 439.47, 0.03315, 44, 25.99, -50.92, 0.96685, 2, 43, 46.47, 418.99, 0.06005, 44, 25.99, -71.4, 0.93995, 2, 43, 46.47, 398.51, 0.09372, 44, 25.99, -91.88, 0.90628, 2, 43, 46.47, 378.03, 0.1338, 44, 25.99, -112.36, 0.8662, 2, 43, 46.47, 357.55, 0.1797, 44, 25.99, -132.84, 0.8203, 2, 43, 46.47, 337.07, 0.23078, 44, 25.99, -153.32, 0.76922, 2, 43, 46.47, 316.59, 0.28631, 44, 25.99, -173.8, 0.71369, 2, 43, 46.47, 296.11, 0.3454, 44, 25.99, -194.28, 0.6546, 2, 43, 46.47, 275.63, 0.40729, 44, 25.99, -214.76, 0.59271, 2, 43, 46.47, 255.15, 0.47048, 44, 25.99, -235.24, 0.52952, 2, 43, 46.47, 234.67, 0.53409, 44, 25.99, -255.72, 0.46591, 2, 43, 46.47, 214.19, 0.59703, 44, 25.99, -276.2, 0.40297, 2, 43, 46.47, 193.71, 0.65819, 44, 25.99, -296.68, 0.34181, 2, 43, 46.47, 173.23, 0.71662, 44, 25.99, -317.16, 0.28338, 2, 43, 46.47, 152.75, 0.77144, 44, 25.99, -337.64, 0.22856, 2, 43, 46.47, 132.27, 0.82191, 44, 25.99, -358.12, 0.17809, 2, 43, 46.47, 111.79, 0.86727, 44, 25.99, -378.6, 0.13273, 2, 43, 46.47, 91.31, 0.90689, 44, 25.99, -399.08, 0.09311, 2, 43, 46.47, 70.83, 0.94016, 44, 25.99, -419.56, 0.05984, 2, 43, 46.47, 50.35, 0.96652, 44, 25.99, -440.04, 0.03348, 2, 43, 46.47, 29.87, 0.98537, 44, 25.99, -460.52, 0.01463, 2, 43, 46.47, 9.39, 0.99644, 44, 25.99, -481, 0.00356, 2, 43, 14.47, 9.39, 0.9971, 44, -6.01, -481, 0.0029, 2, 43, 14.47, 29.87, 0.98595, 44, -6.01, -460.52, 0.01405, 2, 43, 14.47, 50.35, 0.96682, 44, -6.01, -440.04, 0.03318, 2, 43, 14.47, 70.83, 0.94032, 44, -6.01, -419.56, 0.05968, 2, 43, 14.47, 91.31, 0.90696, 44, -6.01, -399.08, 0.09304, 2, 43, 14.47, 111.79, 0.8673, 44, -6.01, -378.6, 0.1327, 2, 43, 14.47, 132.27, 0.82192, 44, -6.01, -358.12, 0.17808, 2, 43, 14.47, 152.75, 0.77145, 44, -6.01, -337.64, 0.22855, 2, 43, 14.47, 173.23, 0.71661, 44, -6.01, -317.16, 0.28339, 2, 43, 14.47, 193.71, 0.65818, 44, -6.01, -296.68, 0.34182, 2, 43, 14.47, 214.19, 0.59702, 44, -6.01, -276.2, 0.40298, 2, 43, 14.47, 234.67, 0.5341, 44, -6.01, -255.72, 0.4659, 2, 43, 14.47, 255.15, 0.47045, 44, -6.01, -235.24, 0.52955, 2, 43, 14.47, 275.63, 0.40719, 44, -6.01, -214.76, 0.59281, 2, 43, 14.47, 296.11, 0.34545, 44, -6.01, -194.28, 0.65455, 2, 43, 14.47, 316.59, 0.28633, 44, -6.01, -173.8, 0.71367, 2, 43, 14.47, 337.07, 0.2308, 44, -6.01, -153.32, 0.7692, 2, 43, 14.47, 357.55, 0.17972, 44, -6.01, -132.84, 0.82028, 2, 43, 14.47, 378.03, 0.13383, 44, -6.01, -112.36, 0.86617, 2, 43, 14.47, 398.51, 0.09376, 44, -6.01, -91.88, 0.90624, 2, 43, 14.47, 418.99, 0.06012, 44, -6.01, -71.4, 0.93988, 2, 43, 14.47, 439.47, 0.03345, 44, -6.01, -50.92, 0.96655, 2, 43, 14.47, 459.95, 0.0142, 44, -6.01, -30.44, 0.9858, 2, 43, 14.47, 480.43, 0.00272, 44, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}}, "lighting02": {"lighting01": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 46, 46.47, -11.09, 0.99997, 47, 25.99, -501.48, 3e-05, 1, 46, 14.47, -11.09, 1, 1, 46, -17.53, -11.09, 1, 2, 46, -17.53, 9.39, 0.9975, 47, -38.01, -481, 0.0025, 2, 46, -17.53, 29.87, 0.98643, 47, -38.01, -460.52, 0.01357, 2, 46, -17.53, 50.35, 0.9671, 47, -38.01, -440.04, 0.0329, 2, 46, -17.53, 70.83, 0.94045, 47, -38.01, -419.56, 0.05955, 2, 46, -17.53, 91.31, 0.90703, 47, -38.01, -399.08, 0.09297, 2, 46, -17.53, 111.79, 0.86732, 47, -38.01, -378.6, 0.13268, 2, 46, -17.53, 132.27, 0.82193, 47, -38.01, -358.12, 0.17807, 2, 46, -17.53, 152.75, 0.77146, 47, -38.01, -337.64, 0.22854, 2, 46, -17.53, 173.23, 0.71662, 47, -38.01, -317.16, 0.28338, 2, 46, -17.53, 193.71, 0.65818, 47, -38.01, -296.68, 0.34182, 2, 46, -17.53, 214.19, 0.59701, 47, -38.01, -276.2, 0.40299, 2, 46, -17.53, 234.67, 0.53408, 47, -38.01, -255.72, 0.46592, 2, 46, -17.53, 255.15, 0.47044, 47, -38.01, -235.24, 0.52956, 2, 46, -17.53, 275.63, 0.40718, 47, -38.01, -214.76, 0.59281, 2, 46, -17.53, 296.11, 0.34547, 47, -38.01, -194.28, 0.65453, 2, 46, -17.53, 316.59, 0.28634, 47, -38.01, -173.8, 0.71366, 2, 46, -17.53, 337.07, 0.23082, 47, -38.01, -153.32, 0.76918, 2, 46, -17.53, 357.55, 0.17974, 47, -38.01, -132.84, 0.82026, 2, 46, -17.53, 378.03, 0.13386, 47, -38.01, -112.36, 0.86614, 2, 46, -17.53, 398.51, 0.09381, 47, -38.01, -91.88, 0.90619, 2, 46, -17.53, 418.99, 0.0602, 47, -38.01, -71.4, 0.9398, 2, 46, -17.53, 439.47, 0.03363, 47, -38.01, -50.92, 0.96637, 2, 46, -17.53, 459.95, 0.01452, 47, -38.01, -30.44, 0.98548, 2, 46, -17.53, 480.43, 0.00346, 47, -38.01, -9.96, 0.99654, 2, 46, -17.53, 500.91, 2e-05, 47, -38.01, 10.52, 0.99998, 1, 47, -6.01, 10.52, 1, 1, 47, 25.99, 10.52, 1, 2, 46, 46.47, 480.43, 0.00284, 47, 25.99, -9.96, 0.99716, 2, 46, 46.47, 459.95, 0.01535, 47, 25.99, -30.44, 0.98465, 2, 46, 46.47, 439.47, 0.03315, 47, 25.99, -50.92, 0.96685, 2, 46, 46.47, 418.99, 0.06005, 47, 25.99, -71.4, 0.93995, 2, 46, 46.47, 398.51, 0.09372, 47, 25.99, -91.88, 0.90628, 2, 46, 46.47, 378.03, 0.1338, 47, 25.99, -112.36, 0.8662, 2, 46, 46.47, 357.55, 0.1797, 47, 25.99, -132.84, 0.8203, 2, 46, 46.47, 337.07, 0.23078, 47, 25.99, -153.32, 0.76922, 2, 46, 46.47, 316.59, 0.28631, 47, 25.99, -173.8, 0.71369, 2, 46, 46.47, 296.11, 0.3454, 47, 25.99, -194.28, 0.6546, 2, 46, 46.47, 275.63, 0.40729, 47, 25.99, -214.76, 0.59271, 2, 46, 46.47, 255.15, 0.47048, 47, 25.99, -235.24, 0.52952, 2, 46, 46.47, 234.67, 0.53409, 47, 25.99, -255.72, 0.46591, 2, 46, 46.47, 214.19, 0.59703, 47, 25.99, -276.2, 0.40297, 2, 46, 46.47, 193.71, 0.65819, 47, 25.99, -296.68, 0.34181, 2, 46, 46.47, 173.23, 0.71662, 47, 25.99, -317.16, 0.28338, 2, 46, 46.47, 152.75, 0.77144, 47, 25.99, -337.64, 0.22856, 2, 46, 46.47, 132.27, 0.82191, 47, 25.99, -358.12, 0.17809, 2, 46, 46.47, 111.79, 0.86727, 47, 25.99, -378.6, 0.13273, 2, 46, 46.47, 91.31, 0.90689, 47, 25.99, -399.08, 0.09311, 2, 46, 46.47, 70.83, 0.94016, 47, 25.99, -419.56, 0.05984, 2, 46, 46.47, 50.35, 0.96652, 47, 25.99, -440.04, 0.03348, 2, 46, 46.47, 29.87, 0.98537, 47, 25.99, -460.52, 0.01463, 2, 46, 46.47, 9.39, 0.99644, 47, 25.99, -481, 0.00356, 2, 46, 14.47, 9.39, 0.9971, 47, -6.01, -481, 0.0029, 2, 46, 14.47, 29.87, 0.98595, 47, -6.01, -460.52, 0.01405, 2, 46, 14.47, 50.35, 0.96682, 47, -6.01, -440.04, 0.03318, 2, 46, 14.47, 70.83, 0.94032, 47, -6.01, -419.56, 0.05968, 2, 46, 14.47, 91.31, 0.90696, 47, -6.01, -399.08, 0.09304, 2, 46, 14.47, 111.79, 0.8673, 47, -6.01, -378.6, 0.1327, 2, 46, 14.47, 132.27, 0.82192, 47, -6.01, -358.12, 0.17808, 2, 46, 14.47, 152.75, 0.77145, 47, -6.01, -337.64, 0.22855, 2, 46, 14.47, 173.23, 0.71661, 47, -6.01, -317.16, 0.28339, 2, 46, 14.47, 193.71, 0.65818, 47, -6.01, -296.68, 0.34182, 2, 46, 14.47, 214.19, 0.59702, 47, -6.01, -276.2, 0.40298, 2, 46, 14.47, 234.67, 0.5341, 47, -6.01, -255.72, 0.4659, 2, 46, 14.47, 255.15, 0.47045, 47, -6.01, -235.24, 0.52955, 2, 46, 14.47, 275.63, 0.40719, 47, -6.01, -214.76, 0.59281, 2, 46, 14.47, 296.11, 0.34545, 47, -6.01, -194.28, 0.65455, 2, 46, 14.47, 316.59, 0.28633, 47, -6.01, -173.8, 0.71367, 2, 46, 14.47, 337.07, 0.2308, 47, -6.01, -153.32, 0.7692, 2, 46, 14.47, 357.55, 0.17972, 47, -6.01, -132.84, 0.82028, 2, 46, 14.47, 378.03, 0.13383, 47, -6.01, -112.36, 0.86617, 2, 46, 14.47, 398.51, 0.09376, 47, -6.01, -91.88, 0.90624, 2, 46, 14.47, 418.99, 0.06012, 47, -6.01, -71.4, 0.93988, 2, 46, 14.47, 439.47, 0.03345, 47, -6.01, -50.92, 0.96655, 2, 46, 14.47, 459.95, 0.0142, 47, -6.01, -30.44, 0.9858, 2, 46, 14.47, 480.43, 0.00272, 47, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting02": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 46, 46.47, -11.09, 0.99997, 47, 25.99, -501.48, 3e-05, 1, 46, 14.47, -11.09, 1, 1, 46, -17.53, -11.09, 1, 2, 46, -17.53, 9.39, 0.9975, 47, -38.01, -481, 0.0025, 2, 46, -17.53, 29.87, 0.98643, 47, -38.01, -460.52, 0.01357, 2, 46, -17.53, 50.35, 0.9671, 47, -38.01, -440.04, 0.0329, 2, 46, -17.53, 70.83, 0.94045, 47, -38.01, -419.56, 0.05955, 2, 46, -17.53, 91.31, 0.90703, 47, -38.01, -399.08, 0.09297, 2, 46, -17.53, 111.79, 0.86732, 47, -38.01, -378.6, 0.13268, 2, 46, -17.53, 132.27, 0.82193, 47, -38.01, -358.12, 0.17807, 2, 46, -17.53, 152.75, 0.77146, 47, -38.01, -337.64, 0.22854, 2, 46, -17.53, 173.23, 0.71662, 47, -38.01, -317.16, 0.28338, 2, 46, -17.53, 193.71, 0.65818, 47, -38.01, -296.68, 0.34182, 2, 46, -17.53, 214.19, 0.59701, 47, -38.01, -276.2, 0.40299, 2, 46, -17.53, 234.67, 0.53408, 47, -38.01, -255.72, 0.46592, 2, 46, -17.53, 255.15, 0.47044, 47, -38.01, -235.24, 0.52956, 2, 46, -17.53, 275.63, 0.40718, 47, -38.01, -214.76, 0.59281, 2, 46, -17.53, 296.11, 0.34547, 47, -38.01, -194.28, 0.65453, 2, 46, -17.53, 316.59, 0.28634, 47, -38.01, -173.8, 0.71366, 2, 46, -17.53, 337.07, 0.23082, 47, -38.01, -153.32, 0.76918, 2, 46, -17.53, 357.55, 0.17974, 47, -38.01, -132.84, 0.82026, 2, 46, -17.53, 378.03, 0.13386, 47, -38.01, -112.36, 0.86614, 2, 46, -17.53, 398.51, 0.09381, 47, -38.01, -91.88, 0.90619, 2, 46, -17.53, 418.99, 0.0602, 47, -38.01, -71.4, 0.9398, 2, 46, -17.53, 439.47, 0.03363, 47, -38.01, -50.92, 0.96637, 2, 46, -17.53, 459.95, 0.01452, 47, -38.01, -30.44, 0.98548, 2, 46, -17.53, 480.43, 0.00346, 47, -38.01, -9.96, 0.99654, 2, 46, -17.53, 500.91, 2e-05, 47, -38.01, 10.52, 0.99998, 1, 47, -6.01, 10.52, 1, 1, 47, 25.99, 10.52, 1, 2, 46, 46.47, 480.43, 0.00284, 47, 25.99, -9.96, 0.99716, 2, 46, 46.47, 459.95, 0.01535, 47, 25.99, -30.44, 0.98465, 2, 46, 46.47, 439.47, 0.03315, 47, 25.99, -50.92, 0.96685, 2, 46, 46.47, 418.99, 0.06005, 47, 25.99, -71.4, 0.93995, 2, 46, 46.47, 398.51, 0.09372, 47, 25.99, -91.88, 0.90628, 2, 46, 46.47, 378.03, 0.1338, 47, 25.99, -112.36, 0.8662, 2, 46, 46.47, 357.55, 0.1797, 47, 25.99, -132.84, 0.8203, 2, 46, 46.47, 337.07, 0.23078, 47, 25.99, -153.32, 0.76922, 2, 46, 46.47, 316.59, 0.28631, 47, 25.99, -173.8, 0.71369, 2, 46, 46.47, 296.11, 0.3454, 47, 25.99, -194.28, 0.6546, 2, 46, 46.47, 275.63, 0.40729, 47, 25.99, -214.76, 0.59271, 2, 46, 46.47, 255.15, 0.47048, 47, 25.99, -235.24, 0.52952, 2, 46, 46.47, 234.67, 0.53409, 47, 25.99, -255.72, 0.46591, 2, 46, 46.47, 214.19, 0.59703, 47, 25.99, -276.2, 0.40297, 2, 46, 46.47, 193.71, 0.65819, 47, 25.99, -296.68, 0.34181, 2, 46, 46.47, 173.23, 0.71662, 47, 25.99, -317.16, 0.28338, 2, 46, 46.47, 152.75, 0.77144, 47, 25.99, -337.64, 0.22856, 2, 46, 46.47, 132.27, 0.82191, 47, 25.99, -358.12, 0.17809, 2, 46, 46.47, 111.79, 0.86727, 47, 25.99, -378.6, 0.13273, 2, 46, 46.47, 91.31, 0.90689, 47, 25.99, -399.08, 0.09311, 2, 46, 46.47, 70.83, 0.94016, 47, 25.99, -419.56, 0.05984, 2, 46, 46.47, 50.35, 0.96652, 47, 25.99, -440.04, 0.03348, 2, 46, 46.47, 29.87, 0.98537, 47, 25.99, -460.52, 0.01463, 2, 46, 46.47, 9.39, 0.99644, 47, 25.99, -481, 0.00356, 2, 46, 14.47, 9.39, 0.9971, 47, -6.01, -481, 0.0029, 2, 46, 14.47, 29.87, 0.98595, 47, -6.01, -460.52, 0.01405, 2, 46, 14.47, 50.35, 0.96682, 47, -6.01, -440.04, 0.03318, 2, 46, 14.47, 70.83, 0.94032, 47, -6.01, -419.56, 0.05968, 2, 46, 14.47, 91.31, 0.90696, 47, -6.01, -399.08, 0.09304, 2, 46, 14.47, 111.79, 0.8673, 47, -6.01, -378.6, 0.1327, 2, 46, 14.47, 132.27, 0.82192, 47, -6.01, -358.12, 0.17808, 2, 46, 14.47, 152.75, 0.77145, 47, -6.01, -337.64, 0.22855, 2, 46, 14.47, 173.23, 0.71661, 47, -6.01, -317.16, 0.28339, 2, 46, 14.47, 193.71, 0.65818, 47, -6.01, -296.68, 0.34182, 2, 46, 14.47, 214.19, 0.59702, 47, -6.01, -276.2, 0.40298, 2, 46, 14.47, 234.67, 0.5341, 47, -6.01, -255.72, 0.4659, 2, 46, 14.47, 255.15, 0.47045, 47, -6.01, -235.24, 0.52955, 2, 46, 14.47, 275.63, 0.40719, 47, -6.01, -214.76, 0.59281, 2, 46, 14.47, 296.11, 0.34545, 47, -6.01, -194.28, 0.65455, 2, 46, 14.47, 316.59, 0.28633, 47, -6.01, -173.8, 0.71367, 2, 46, 14.47, 337.07, 0.2308, 47, -6.01, -153.32, 0.7692, 2, 46, 14.47, 357.55, 0.17972, 47, -6.01, -132.84, 0.82028, 2, 46, 14.47, 378.03, 0.13383, 47, -6.01, -112.36, 0.86617, 2, 46, 14.47, 398.51, 0.09376, 47, -6.01, -91.88, 0.90624, 2, 46, 14.47, 418.99, 0.06012, 47, -6.01, -71.4, 0.93988, 2, 46, 14.47, 439.47, 0.03345, 47, -6.01, -50.92, 0.96655, 2, 46, 14.47, 459.95, 0.0142, 47, -6.01, -30.44, 0.9858, 2, 46, 14.47, 480.43, 0.00272, 47, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting03": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 46, 46.47, -11.09, 0.99997, 47, 25.99, -501.48, 3e-05, 1, 46, 14.47, -11.09, 1, 1, 46, -17.53, -11.09, 1, 2, 46, -17.53, 9.39, 0.9975, 47, -38.01, -481, 0.0025, 2, 46, -17.53, 29.87, 0.98643, 47, -38.01, -460.52, 0.01357, 2, 46, -17.53, 50.35, 0.9671, 47, -38.01, -440.04, 0.0329, 2, 46, -17.53, 70.83, 0.94045, 47, -38.01, -419.56, 0.05955, 2, 46, -17.53, 91.31, 0.90703, 47, -38.01, -399.08, 0.09297, 2, 46, -17.53, 111.79, 0.86732, 47, -38.01, -378.6, 0.13268, 2, 46, -17.53, 132.27, 0.82193, 47, -38.01, -358.12, 0.17807, 2, 46, -17.53, 152.75, 0.77146, 47, -38.01, -337.64, 0.22854, 2, 46, -17.53, 173.23, 0.71662, 47, -38.01, -317.16, 0.28338, 2, 46, -17.53, 193.71, 0.65818, 47, -38.01, -296.68, 0.34182, 2, 46, -17.53, 214.19, 0.59701, 47, -38.01, -276.2, 0.40299, 2, 46, -17.53, 234.67, 0.53408, 47, -38.01, -255.72, 0.46592, 2, 46, -17.53, 255.15, 0.47044, 47, -38.01, -235.24, 0.52956, 2, 46, -17.53, 275.63, 0.40718, 47, -38.01, -214.76, 0.59281, 2, 46, -17.53, 296.11, 0.34547, 47, -38.01, -194.28, 0.65453, 2, 46, -17.53, 316.59, 0.28634, 47, -38.01, -173.8, 0.71366, 2, 46, -17.53, 337.07, 0.23082, 47, -38.01, -153.32, 0.76918, 2, 46, -17.53, 357.55, 0.17974, 47, -38.01, -132.84, 0.82026, 2, 46, -17.53, 378.03, 0.13386, 47, -38.01, -112.36, 0.86614, 2, 46, -17.53, 398.51, 0.09381, 47, -38.01, -91.88, 0.90619, 2, 46, -17.53, 418.99, 0.0602, 47, -38.01, -71.4, 0.9398, 2, 46, -17.53, 439.47, 0.03363, 47, -38.01, -50.92, 0.96637, 2, 46, -17.53, 459.95, 0.01452, 47, -38.01, -30.44, 0.98548, 2, 46, -17.53, 480.43, 0.00346, 47, -38.01, -9.96, 0.99654, 2, 46, -17.53, 500.91, 2e-05, 47, -38.01, 10.52, 0.99998, 1, 47, -6.01, 10.52, 1, 1, 47, 25.99, 10.52, 1, 2, 46, 46.47, 480.43, 0.00284, 47, 25.99, -9.96, 0.99716, 2, 46, 46.47, 459.95, 0.01535, 47, 25.99, -30.44, 0.98465, 2, 46, 46.47, 439.47, 0.03315, 47, 25.99, -50.92, 0.96685, 2, 46, 46.47, 418.99, 0.06005, 47, 25.99, -71.4, 0.93995, 2, 46, 46.47, 398.51, 0.09372, 47, 25.99, -91.88, 0.90628, 2, 46, 46.47, 378.03, 0.1338, 47, 25.99, -112.36, 0.8662, 2, 46, 46.47, 357.55, 0.1797, 47, 25.99, -132.84, 0.8203, 2, 46, 46.47, 337.07, 0.23078, 47, 25.99, -153.32, 0.76922, 2, 46, 46.47, 316.59, 0.28631, 47, 25.99, -173.8, 0.71369, 2, 46, 46.47, 296.11, 0.3454, 47, 25.99, -194.28, 0.6546, 2, 46, 46.47, 275.63, 0.40729, 47, 25.99, -214.76, 0.59271, 2, 46, 46.47, 255.15, 0.47048, 47, 25.99, -235.24, 0.52952, 2, 46, 46.47, 234.67, 0.53409, 47, 25.99, -255.72, 0.46591, 2, 46, 46.47, 214.19, 0.59703, 47, 25.99, -276.2, 0.40297, 2, 46, 46.47, 193.71, 0.65819, 47, 25.99, -296.68, 0.34181, 2, 46, 46.47, 173.23, 0.71662, 47, 25.99, -317.16, 0.28338, 2, 46, 46.47, 152.75, 0.77144, 47, 25.99, -337.64, 0.22856, 2, 46, 46.47, 132.27, 0.82191, 47, 25.99, -358.12, 0.17809, 2, 46, 46.47, 111.79, 0.86727, 47, 25.99, -378.6, 0.13273, 2, 46, 46.47, 91.31, 0.90689, 47, 25.99, -399.08, 0.09311, 2, 46, 46.47, 70.83, 0.94016, 47, 25.99, -419.56, 0.05984, 2, 46, 46.47, 50.35, 0.96652, 47, 25.99, -440.04, 0.03348, 2, 46, 46.47, 29.87, 0.98537, 47, 25.99, -460.52, 0.01463, 2, 46, 46.47, 9.39, 0.99644, 47, 25.99, -481, 0.00356, 2, 46, 14.47, 9.39, 0.9971, 47, -6.01, -481, 0.0029, 2, 46, 14.47, 29.87, 0.98595, 47, -6.01, -460.52, 0.01405, 2, 46, 14.47, 50.35, 0.96682, 47, -6.01, -440.04, 0.03318, 2, 46, 14.47, 70.83, 0.94032, 47, -6.01, -419.56, 0.05968, 2, 46, 14.47, 91.31, 0.90696, 47, -6.01, -399.08, 0.09304, 2, 46, 14.47, 111.79, 0.8673, 47, -6.01, -378.6, 0.1327, 2, 46, 14.47, 132.27, 0.82192, 47, -6.01, -358.12, 0.17808, 2, 46, 14.47, 152.75, 0.77145, 47, -6.01, -337.64, 0.22855, 2, 46, 14.47, 173.23, 0.71661, 47, -6.01, -317.16, 0.28339, 2, 46, 14.47, 193.71, 0.65818, 47, -6.01, -296.68, 0.34182, 2, 46, 14.47, 214.19, 0.59702, 47, -6.01, -276.2, 0.40298, 2, 46, 14.47, 234.67, 0.5341, 47, -6.01, -255.72, 0.4659, 2, 46, 14.47, 255.15, 0.47045, 47, -6.01, -235.24, 0.52955, 2, 46, 14.47, 275.63, 0.40719, 47, -6.01, -214.76, 0.59281, 2, 46, 14.47, 296.11, 0.34545, 47, -6.01, -194.28, 0.65455, 2, 46, 14.47, 316.59, 0.28633, 47, -6.01, -173.8, 0.71367, 2, 46, 14.47, 337.07, 0.2308, 47, -6.01, -153.32, 0.7692, 2, 46, 14.47, 357.55, 0.17972, 47, -6.01, -132.84, 0.82028, 2, 46, 14.47, 378.03, 0.13383, 47, -6.01, -112.36, 0.86617, 2, 46, 14.47, 398.51, 0.09376, 47, -6.01, -91.88, 0.90624, 2, 46, 14.47, 418.99, 0.06012, 47, -6.01, -71.4, 0.93988, 2, 46, 14.47, 439.47, 0.03345, 47, -6.01, -50.92, 0.96655, 2, 46, 14.47, 459.95, 0.0142, 47, -6.01, -30.44, 0.9858, 2, 46, 14.47, 480.43, 0.00272, 47, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting04": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 46, 46.47, -11.09, 0.99997, 47, 25.99, -501.48, 3e-05, 1, 46, 14.47, -11.09, 1, 1, 46, -17.53, -11.09, 1, 2, 46, -17.53, 9.39, 0.9975, 47, -38.01, -481, 0.0025, 2, 46, -17.53, 29.87, 0.98643, 47, -38.01, -460.52, 0.01357, 2, 46, -17.53, 50.35, 0.9671, 47, -38.01, -440.04, 0.0329, 2, 46, -17.53, 70.83, 0.94045, 47, -38.01, -419.56, 0.05955, 2, 46, -17.53, 91.31, 0.90703, 47, -38.01, -399.08, 0.09297, 2, 46, -17.53, 111.79, 0.86732, 47, -38.01, -378.6, 0.13268, 2, 46, -17.53, 132.27, 0.82193, 47, -38.01, -358.12, 0.17807, 2, 46, -17.53, 152.75, 0.77146, 47, -38.01, -337.64, 0.22854, 2, 46, -17.53, 173.23, 0.71662, 47, -38.01, -317.16, 0.28338, 2, 46, -17.53, 193.71, 0.65818, 47, -38.01, -296.68, 0.34182, 2, 46, -17.53, 214.19, 0.59701, 47, -38.01, -276.2, 0.40299, 2, 46, -17.53, 234.67, 0.53408, 47, -38.01, -255.72, 0.46592, 2, 46, -17.53, 255.15, 0.47044, 47, -38.01, -235.24, 0.52956, 2, 46, -17.53, 275.63, 0.40718, 47, -38.01, -214.76, 0.59281, 2, 46, -17.53, 296.11, 0.34547, 47, -38.01, -194.28, 0.65453, 2, 46, -17.53, 316.59, 0.28634, 47, -38.01, -173.8, 0.71366, 2, 46, -17.53, 337.07, 0.23082, 47, -38.01, -153.32, 0.76918, 2, 46, -17.53, 357.55, 0.17974, 47, -38.01, -132.84, 0.82026, 2, 46, -17.53, 378.03, 0.13386, 47, -38.01, -112.36, 0.86614, 2, 46, -17.53, 398.51, 0.09381, 47, -38.01, -91.88, 0.90619, 2, 46, -17.53, 418.99, 0.0602, 47, -38.01, -71.4, 0.9398, 2, 46, -17.53, 439.47, 0.03363, 47, -38.01, -50.92, 0.96637, 2, 46, -17.53, 459.95, 0.01452, 47, -38.01, -30.44, 0.98548, 2, 46, -17.53, 480.43, 0.00346, 47, -38.01, -9.96, 0.99654, 2, 46, -17.53, 500.91, 2e-05, 47, -38.01, 10.52, 0.99998, 1, 47, -6.01, 10.52, 1, 1, 47, 25.99, 10.52, 1, 2, 46, 46.47, 480.43, 0.00284, 47, 25.99, -9.96, 0.99716, 2, 46, 46.47, 459.95, 0.01535, 47, 25.99, -30.44, 0.98465, 2, 46, 46.47, 439.47, 0.03315, 47, 25.99, -50.92, 0.96685, 2, 46, 46.47, 418.99, 0.06005, 47, 25.99, -71.4, 0.93995, 2, 46, 46.47, 398.51, 0.09372, 47, 25.99, -91.88, 0.90628, 2, 46, 46.47, 378.03, 0.1338, 47, 25.99, -112.36, 0.8662, 2, 46, 46.47, 357.55, 0.1797, 47, 25.99, -132.84, 0.8203, 2, 46, 46.47, 337.07, 0.23078, 47, 25.99, -153.32, 0.76922, 2, 46, 46.47, 316.59, 0.28631, 47, 25.99, -173.8, 0.71369, 2, 46, 46.47, 296.11, 0.3454, 47, 25.99, -194.28, 0.6546, 2, 46, 46.47, 275.63, 0.40729, 47, 25.99, -214.76, 0.59271, 2, 46, 46.47, 255.15, 0.47048, 47, 25.99, -235.24, 0.52952, 2, 46, 46.47, 234.67, 0.53409, 47, 25.99, -255.72, 0.46591, 2, 46, 46.47, 214.19, 0.59703, 47, 25.99, -276.2, 0.40297, 2, 46, 46.47, 193.71, 0.65819, 47, 25.99, -296.68, 0.34181, 2, 46, 46.47, 173.23, 0.71662, 47, 25.99, -317.16, 0.28338, 2, 46, 46.47, 152.75, 0.77144, 47, 25.99, -337.64, 0.22856, 2, 46, 46.47, 132.27, 0.82191, 47, 25.99, -358.12, 0.17809, 2, 46, 46.47, 111.79, 0.86727, 47, 25.99, -378.6, 0.13273, 2, 46, 46.47, 91.31, 0.90689, 47, 25.99, -399.08, 0.09311, 2, 46, 46.47, 70.83, 0.94016, 47, 25.99, -419.56, 0.05984, 2, 46, 46.47, 50.35, 0.96652, 47, 25.99, -440.04, 0.03348, 2, 46, 46.47, 29.87, 0.98537, 47, 25.99, -460.52, 0.01463, 2, 46, 46.47, 9.39, 0.99644, 47, 25.99, -481, 0.00356, 2, 46, 14.47, 9.39, 0.9971, 47, -6.01, -481, 0.0029, 2, 46, 14.47, 29.87, 0.98595, 47, -6.01, -460.52, 0.01405, 2, 46, 14.47, 50.35, 0.96682, 47, -6.01, -440.04, 0.03318, 2, 46, 14.47, 70.83, 0.94032, 47, -6.01, -419.56, 0.05968, 2, 46, 14.47, 91.31, 0.90696, 47, -6.01, -399.08, 0.09304, 2, 46, 14.47, 111.79, 0.8673, 47, -6.01, -378.6, 0.1327, 2, 46, 14.47, 132.27, 0.82192, 47, -6.01, -358.12, 0.17808, 2, 46, 14.47, 152.75, 0.77145, 47, -6.01, -337.64, 0.22855, 2, 46, 14.47, 173.23, 0.71661, 47, -6.01, -317.16, 0.28339, 2, 46, 14.47, 193.71, 0.65818, 47, -6.01, -296.68, 0.34182, 2, 46, 14.47, 214.19, 0.59702, 47, -6.01, -276.2, 0.40298, 2, 46, 14.47, 234.67, 0.5341, 47, -6.01, -255.72, 0.4659, 2, 46, 14.47, 255.15, 0.47045, 47, -6.01, -235.24, 0.52955, 2, 46, 14.47, 275.63, 0.40719, 47, -6.01, -214.76, 0.59281, 2, 46, 14.47, 296.11, 0.34545, 47, -6.01, -194.28, 0.65455, 2, 46, 14.47, 316.59, 0.28633, 47, -6.01, -173.8, 0.71367, 2, 46, 14.47, 337.07, 0.2308, 47, -6.01, -153.32, 0.7692, 2, 46, 14.47, 357.55, 0.17972, 47, -6.01, -132.84, 0.82028, 2, 46, 14.47, 378.03, 0.13383, 47, -6.01, -112.36, 0.86617, 2, 46, 14.47, 398.51, 0.09376, 47, -6.01, -91.88, 0.90624, 2, 46, 14.47, 418.99, 0.06012, 47, -6.01, -71.4, 0.93988, 2, 46, 14.47, 439.47, 0.03345, 47, -6.01, -50.92, 0.96655, 2, 46, 14.47, 459.95, 0.0142, 47, -6.01, -30.44, 0.9858, 2, 46, 14.47, 480.43, 0.00272, 47, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}}, "lighting03": {"lighting01": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 53, 46.47, -11.09, 0.99997, 55, 25.99, -501.48, 3e-05, 1, 53, 14.47, -11.09, 1, 1, 53, -17.53, -11.09, 1, 2, 53, -17.53, 9.39, 0.9975, 55, -38.01, -481, 0.0025, 2, 53, -17.53, 29.87, 0.98643, 55, -38.01, -460.52, 0.01357, 2, 53, -17.53, 50.35, 0.9671, 55, -38.01, -440.04, 0.0329, 2, 53, -17.53, 70.83, 0.94045, 55, -38.01, -419.56, 0.05955, 2, 53, -17.53, 91.31, 0.90703, 55, -38.01, -399.08, 0.09297, 2, 53, -17.53, 111.79, 0.86732, 55, -38.01, -378.6, 0.13268, 2, 53, -17.53, 132.27, 0.82193, 55, -38.01, -358.12, 0.17807, 2, 53, -17.53, 152.75, 0.77146, 55, -38.01, -337.64, 0.22854, 2, 53, -17.53, 173.23, 0.71662, 55, -38.01, -317.16, 0.28338, 2, 53, -17.53, 193.71, 0.65818, 55, -38.01, -296.68, 0.34182, 2, 53, -17.53, 214.19, 0.59701, 55, -38.01, -276.2, 0.40299, 2, 53, -17.53, 234.67, 0.53408, 55, -38.01, -255.72, 0.46592, 2, 53, -17.53, 255.15, 0.47044, 55, -38.01, -235.24, 0.52956, 2, 53, -17.53, 275.63, 0.40718, 55, -38.01, -214.76, 0.59281, 2, 53, -17.53, 296.11, 0.34547, 55, -38.01, -194.28, 0.65453, 2, 53, -17.53, 316.59, 0.28634, 55, -38.01, -173.8, 0.71366, 2, 53, -17.53, 337.07, 0.23082, 55, -38.01, -153.32, 0.76918, 2, 53, -17.53, 357.55, 0.17974, 55, -38.01, -132.84, 0.82026, 2, 53, -17.53, 378.03, 0.13386, 55, -38.01, -112.36, 0.86614, 2, 53, -17.53, 398.51, 0.09381, 55, -38.01, -91.88, 0.90619, 2, 53, -17.53, 418.99, 0.0602, 55, -38.01, -71.4, 0.9398, 2, 53, -17.53, 439.47, 0.03363, 55, -38.01, -50.92, 0.96637, 2, 53, -17.53, 459.95, 0.01452, 55, -38.01, -30.44, 0.98548, 2, 53, -17.53, 480.43, 0.00346, 55, -38.01, -9.96, 0.99654, 2, 53, -17.53, 500.91, 2e-05, 55, -38.01, 10.52, 0.99998, 1, 55, -6.01, 10.52, 1, 1, 55, 25.99, 10.52, 1, 2, 53, 46.47, 480.43, 0.00284, 55, 25.99, -9.96, 0.99716, 2, 53, 46.47, 459.95, 0.01535, 55, 25.99, -30.44, 0.98465, 2, 53, 46.47, 439.47, 0.03315, 55, 25.99, -50.92, 0.96685, 2, 53, 46.47, 418.99, 0.06005, 55, 25.99, -71.4, 0.93995, 2, 53, 46.47, 398.51, 0.09372, 55, 25.99, -91.88, 0.90628, 2, 53, 46.47, 378.03, 0.1338, 55, 25.99, -112.36, 0.8662, 2, 53, 46.47, 357.55, 0.1797, 55, 25.99, -132.84, 0.8203, 2, 53, 46.47, 337.07, 0.23078, 55, 25.99, -153.32, 0.76922, 2, 53, 46.47, 316.59, 0.28631, 55, 25.99, -173.8, 0.71369, 2, 53, 46.47, 296.11, 0.3454, 55, 25.99, -194.28, 0.6546, 2, 53, 46.47, 275.63, 0.40729, 55, 25.99, -214.76, 0.59271, 2, 53, 46.47, 255.15, 0.47048, 55, 25.99, -235.24, 0.52952, 2, 53, 46.47, 234.67, 0.53409, 55, 25.99, -255.72, 0.46591, 2, 53, 46.47, 214.19, 0.59703, 55, 25.99, -276.2, 0.40297, 2, 53, 46.47, 193.71, 0.65819, 55, 25.99, -296.68, 0.34181, 2, 53, 46.47, 173.23, 0.71662, 55, 25.99, -317.16, 0.28338, 2, 53, 46.47, 152.75, 0.77144, 55, 25.99, -337.64, 0.22856, 2, 53, 46.47, 132.27, 0.82191, 55, 25.99, -358.12, 0.17809, 2, 53, 46.47, 111.79, 0.86727, 55, 25.99, -378.6, 0.13273, 2, 53, 46.47, 91.31, 0.90689, 55, 25.99, -399.08, 0.09311, 2, 53, 46.47, 70.83, 0.94016, 55, 25.99, -419.56, 0.05984, 2, 53, 46.47, 50.35, 0.96652, 55, 25.99, -440.04, 0.03348, 2, 53, 46.47, 29.87, 0.98537, 55, 25.99, -460.52, 0.01463, 2, 53, 46.47, 9.39, 0.99644, 55, 25.99, -481, 0.00356, 2, 53, 14.47, 9.39, 0.9971, 55, -6.01, -481, 0.0029, 2, 53, 14.47, 29.87, 0.98595, 55, -6.01, -460.52, 0.01405, 2, 53, 14.47, 50.35, 0.96682, 55, -6.01, -440.04, 0.03318, 2, 53, 14.47, 70.83, 0.94032, 55, -6.01, -419.56, 0.05968, 2, 53, 14.47, 91.31, 0.90696, 55, -6.01, -399.08, 0.09304, 2, 53, 14.47, 111.79, 0.8673, 55, -6.01, -378.6, 0.1327, 2, 53, 14.47, 132.27, 0.82192, 55, -6.01, -358.12, 0.17808, 2, 53, 14.47, 152.75, 0.77145, 55, -6.01, -337.64, 0.22855, 2, 53, 14.47, 173.23, 0.71661, 55, -6.01, -317.16, 0.28339, 2, 53, 14.47, 193.71, 0.65818, 55, -6.01, -296.68, 0.34182, 2, 53, 14.47, 214.19, 0.59702, 55, -6.01, -276.2, 0.40298, 2, 53, 14.47, 234.67, 0.5341, 55, -6.01, -255.72, 0.4659, 2, 53, 14.47, 255.15, 0.47045, 55, -6.01, -235.24, 0.52955, 2, 53, 14.47, 275.63, 0.40719, 55, -6.01, -214.76, 0.59281, 2, 53, 14.47, 296.11, 0.34545, 55, -6.01, -194.28, 0.65455, 2, 53, 14.47, 316.59, 0.28633, 55, -6.01, -173.8, 0.71367, 2, 53, 14.47, 337.07, 0.2308, 55, -6.01, -153.32, 0.7692, 2, 53, 14.47, 357.55, 0.17972, 55, -6.01, -132.84, 0.82028, 2, 53, 14.47, 378.03, 0.13383, 55, -6.01, -112.36, 0.86617, 2, 53, 14.47, 398.51, 0.09376, 55, -6.01, -91.88, 0.90624, 2, 53, 14.47, 418.99, 0.06012, 55, -6.01, -71.4, 0.93988, 2, 53, 14.47, 439.47, 0.03345, 55, -6.01, -50.92, 0.96655, 2, 53, 14.47, 459.95, 0.0142, 55, -6.01, -30.44, 0.9858, 2, 53, 14.47, 480.43, 0.00272, 55, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting02": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 53, 46.47, -11.09, 0.99997, 55, 25.99, -501.48, 3e-05, 1, 53, 14.47, -11.09, 1, 1, 53, -17.53, -11.09, 1, 2, 53, -17.53, 9.39, 0.9975, 55, -38.01, -481, 0.0025, 2, 53, -17.53, 29.87, 0.98643, 55, -38.01, -460.52, 0.01357, 2, 53, -17.53, 50.35, 0.9671, 55, -38.01, -440.04, 0.0329, 2, 53, -17.53, 70.83, 0.94045, 55, -38.01, -419.56, 0.05955, 2, 53, -17.53, 91.31, 0.90703, 55, -38.01, -399.08, 0.09297, 2, 53, -17.53, 111.79, 0.86732, 55, -38.01, -378.6, 0.13268, 2, 53, -17.53, 132.27, 0.82193, 55, -38.01, -358.12, 0.17807, 2, 53, -17.53, 152.75, 0.77146, 55, -38.01, -337.64, 0.22854, 2, 53, -17.53, 173.23, 0.71662, 55, -38.01, -317.16, 0.28338, 2, 53, -17.53, 193.71, 0.65818, 55, -38.01, -296.68, 0.34182, 2, 53, -17.53, 214.19, 0.59701, 55, -38.01, -276.2, 0.40299, 2, 53, -17.53, 234.67, 0.53408, 55, -38.01, -255.72, 0.46592, 2, 53, -17.53, 255.15, 0.47044, 55, -38.01, -235.24, 0.52956, 2, 53, -17.53, 275.63, 0.40718, 55, -38.01, -214.76, 0.59281, 2, 53, -17.53, 296.11, 0.34547, 55, -38.01, -194.28, 0.65453, 2, 53, -17.53, 316.59, 0.28634, 55, -38.01, -173.8, 0.71366, 2, 53, -17.53, 337.07, 0.23082, 55, -38.01, -153.32, 0.76918, 2, 53, -17.53, 357.55, 0.17974, 55, -38.01, -132.84, 0.82026, 2, 53, -17.53, 378.03, 0.13386, 55, -38.01, -112.36, 0.86614, 2, 53, -17.53, 398.51, 0.09381, 55, -38.01, -91.88, 0.90619, 2, 53, -17.53, 418.99, 0.0602, 55, -38.01, -71.4, 0.9398, 2, 53, -17.53, 439.47, 0.03363, 55, -38.01, -50.92, 0.96637, 2, 53, -17.53, 459.95, 0.01452, 55, -38.01, -30.44, 0.98548, 2, 53, -17.53, 480.43, 0.00346, 55, -38.01, -9.96, 0.99654, 2, 53, -17.53, 500.91, 2e-05, 55, -38.01, 10.52, 0.99998, 1, 55, -6.01, 10.52, 1, 1, 55, 25.99, 10.52, 1, 2, 53, 46.47, 480.43, 0.00284, 55, 25.99, -9.96, 0.99716, 2, 53, 46.47, 459.95, 0.01535, 55, 25.99, -30.44, 0.98465, 2, 53, 46.47, 439.47, 0.03315, 55, 25.99, -50.92, 0.96685, 2, 53, 46.47, 418.99, 0.06005, 55, 25.99, -71.4, 0.93995, 2, 53, 46.47, 398.51, 0.09372, 55, 25.99, -91.88, 0.90628, 2, 53, 46.47, 378.03, 0.1338, 55, 25.99, -112.36, 0.8662, 2, 53, 46.47, 357.55, 0.1797, 55, 25.99, -132.84, 0.8203, 2, 53, 46.47, 337.07, 0.23078, 55, 25.99, -153.32, 0.76922, 2, 53, 46.47, 316.59, 0.28631, 55, 25.99, -173.8, 0.71369, 2, 53, 46.47, 296.11, 0.3454, 55, 25.99, -194.28, 0.6546, 2, 53, 46.47, 275.63, 0.40729, 55, 25.99, -214.76, 0.59271, 2, 53, 46.47, 255.15, 0.47048, 55, 25.99, -235.24, 0.52952, 2, 53, 46.47, 234.67, 0.53409, 55, 25.99, -255.72, 0.46591, 2, 53, 46.47, 214.19, 0.59703, 55, 25.99, -276.2, 0.40297, 2, 53, 46.47, 193.71, 0.65819, 55, 25.99, -296.68, 0.34181, 2, 53, 46.47, 173.23, 0.71662, 55, 25.99, -317.16, 0.28338, 2, 53, 46.47, 152.75, 0.77144, 55, 25.99, -337.64, 0.22856, 2, 53, 46.47, 132.27, 0.82191, 55, 25.99, -358.12, 0.17809, 2, 53, 46.47, 111.79, 0.86727, 55, 25.99, -378.6, 0.13273, 2, 53, 46.47, 91.31, 0.90689, 55, 25.99, -399.08, 0.09311, 2, 53, 46.47, 70.83, 0.94016, 55, 25.99, -419.56, 0.05984, 2, 53, 46.47, 50.35, 0.96652, 55, 25.99, -440.04, 0.03348, 2, 53, 46.47, 29.87, 0.98537, 55, 25.99, -460.52, 0.01463, 2, 53, 46.47, 9.39, 0.99644, 55, 25.99, -481, 0.00356, 2, 53, 14.47, 9.39, 0.9971, 55, -6.01, -481, 0.0029, 2, 53, 14.47, 29.87, 0.98595, 55, -6.01, -460.52, 0.01405, 2, 53, 14.47, 50.35, 0.96682, 55, -6.01, -440.04, 0.03318, 2, 53, 14.47, 70.83, 0.94032, 55, -6.01, -419.56, 0.05968, 2, 53, 14.47, 91.31, 0.90696, 55, -6.01, -399.08, 0.09304, 2, 53, 14.47, 111.79, 0.8673, 55, -6.01, -378.6, 0.1327, 2, 53, 14.47, 132.27, 0.82192, 55, -6.01, -358.12, 0.17808, 2, 53, 14.47, 152.75, 0.77145, 55, -6.01, -337.64, 0.22855, 2, 53, 14.47, 173.23, 0.71661, 55, -6.01, -317.16, 0.28339, 2, 53, 14.47, 193.71, 0.65818, 55, -6.01, -296.68, 0.34182, 2, 53, 14.47, 214.19, 0.59702, 55, -6.01, -276.2, 0.40298, 2, 53, 14.47, 234.67, 0.5341, 55, -6.01, -255.72, 0.4659, 2, 53, 14.47, 255.15, 0.47045, 55, -6.01, -235.24, 0.52955, 2, 53, 14.47, 275.63, 0.40719, 55, -6.01, -214.76, 0.59281, 2, 53, 14.47, 296.11, 0.34545, 55, -6.01, -194.28, 0.65455, 2, 53, 14.47, 316.59, 0.28633, 55, -6.01, -173.8, 0.71367, 2, 53, 14.47, 337.07, 0.2308, 55, -6.01, -153.32, 0.7692, 2, 53, 14.47, 357.55, 0.17972, 55, -6.01, -132.84, 0.82028, 2, 53, 14.47, 378.03, 0.13383, 55, -6.01, -112.36, 0.86617, 2, 53, 14.47, 398.51, 0.09376, 55, -6.01, -91.88, 0.90624, 2, 53, 14.47, 418.99, 0.06012, 55, -6.01, -71.4, 0.93988, 2, 53, 14.47, 439.47, 0.03345, 55, -6.01, -50.92, 0.96655, 2, 53, 14.47, 459.95, 0.0142, 55, -6.01, -30.44, 0.9858, 2, 53, 14.47, 480.43, 0.00272, 55, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting03": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 53, 46.47, -11.09, 0.99997, 55, 25.99, -501.48, 3e-05, 1, 53, 14.47, -11.09, 1, 1, 53, -17.53, -11.09, 1, 2, 53, -17.53, 9.39, 0.9975, 55, -38.01, -481, 0.0025, 2, 53, -17.53, 29.87, 0.98643, 55, -38.01, -460.52, 0.01357, 2, 53, -17.53, 50.35, 0.9671, 55, -38.01, -440.04, 0.0329, 2, 53, -17.53, 70.83, 0.94045, 55, -38.01, -419.56, 0.05955, 2, 53, -17.53, 91.31, 0.90703, 55, -38.01, -399.08, 0.09297, 2, 53, -17.53, 111.79, 0.86732, 55, -38.01, -378.6, 0.13268, 2, 53, -17.53, 132.27, 0.82193, 55, -38.01, -358.12, 0.17807, 2, 53, -17.53, 152.75, 0.77146, 55, -38.01, -337.64, 0.22854, 2, 53, -17.53, 173.23, 0.71662, 55, -38.01, -317.16, 0.28338, 2, 53, -17.53, 193.71, 0.65818, 55, -38.01, -296.68, 0.34182, 2, 53, -17.53, 214.19, 0.59701, 55, -38.01, -276.2, 0.40299, 2, 53, -17.53, 234.67, 0.53408, 55, -38.01, -255.72, 0.46592, 2, 53, -17.53, 255.15, 0.47044, 55, -38.01, -235.24, 0.52956, 2, 53, -17.53, 275.63, 0.40718, 55, -38.01, -214.76, 0.59281, 2, 53, -17.53, 296.11, 0.34547, 55, -38.01, -194.28, 0.65453, 2, 53, -17.53, 316.59, 0.28634, 55, -38.01, -173.8, 0.71366, 2, 53, -17.53, 337.07, 0.23082, 55, -38.01, -153.32, 0.76918, 2, 53, -17.53, 357.55, 0.17974, 55, -38.01, -132.84, 0.82026, 2, 53, -17.53, 378.03, 0.13386, 55, -38.01, -112.36, 0.86614, 2, 53, -17.53, 398.51, 0.09381, 55, -38.01, -91.88, 0.90619, 2, 53, -17.53, 418.99, 0.0602, 55, -38.01, -71.4, 0.9398, 2, 53, -17.53, 439.47, 0.03363, 55, -38.01, -50.92, 0.96637, 2, 53, -17.53, 459.95, 0.01452, 55, -38.01, -30.44, 0.98548, 2, 53, -17.53, 480.43, 0.00346, 55, -38.01, -9.96, 0.99654, 2, 53, -17.53, 500.91, 2e-05, 55, -38.01, 10.52, 0.99998, 1, 55, -6.01, 10.52, 1, 1, 55, 25.99, 10.52, 1, 2, 53, 46.47, 480.43, 0.00284, 55, 25.99, -9.96, 0.99716, 2, 53, 46.47, 459.95, 0.01535, 55, 25.99, -30.44, 0.98465, 2, 53, 46.47, 439.47, 0.03315, 55, 25.99, -50.92, 0.96685, 2, 53, 46.47, 418.99, 0.06005, 55, 25.99, -71.4, 0.93995, 2, 53, 46.47, 398.51, 0.09372, 55, 25.99, -91.88, 0.90628, 2, 53, 46.47, 378.03, 0.1338, 55, 25.99, -112.36, 0.8662, 2, 53, 46.47, 357.55, 0.1797, 55, 25.99, -132.84, 0.8203, 2, 53, 46.47, 337.07, 0.23078, 55, 25.99, -153.32, 0.76922, 2, 53, 46.47, 316.59, 0.28631, 55, 25.99, -173.8, 0.71369, 2, 53, 46.47, 296.11, 0.3454, 55, 25.99, -194.28, 0.6546, 2, 53, 46.47, 275.63, 0.40729, 55, 25.99, -214.76, 0.59271, 2, 53, 46.47, 255.15, 0.47048, 55, 25.99, -235.24, 0.52952, 2, 53, 46.47, 234.67, 0.53409, 55, 25.99, -255.72, 0.46591, 2, 53, 46.47, 214.19, 0.59703, 55, 25.99, -276.2, 0.40297, 2, 53, 46.47, 193.71, 0.65819, 55, 25.99, -296.68, 0.34181, 2, 53, 46.47, 173.23, 0.71662, 55, 25.99, -317.16, 0.28338, 2, 53, 46.47, 152.75, 0.77144, 55, 25.99, -337.64, 0.22856, 2, 53, 46.47, 132.27, 0.82191, 55, 25.99, -358.12, 0.17809, 2, 53, 46.47, 111.79, 0.86727, 55, 25.99, -378.6, 0.13273, 2, 53, 46.47, 91.31, 0.90689, 55, 25.99, -399.08, 0.09311, 2, 53, 46.47, 70.83, 0.94016, 55, 25.99, -419.56, 0.05984, 2, 53, 46.47, 50.35, 0.96652, 55, 25.99, -440.04, 0.03348, 2, 53, 46.47, 29.87, 0.98537, 55, 25.99, -460.52, 0.01463, 2, 53, 46.47, 9.39, 0.99644, 55, 25.99, -481, 0.00356, 2, 53, 14.47, 9.39, 0.9971, 55, -6.01, -481, 0.0029, 2, 53, 14.47, 29.87, 0.98595, 55, -6.01, -460.52, 0.01405, 2, 53, 14.47, 50.35, 0.96682, 55, -6.01, -440.04, 0.03318, 2, 53, 14.47, 70.83, 0.94032, 55, -6.01, -419.56, 0.05968, 2, 53, 14.47, 91.31, 0.90696, 55, -6.01, -399.08, 0.09304, 2, 53, 14.47, 111.79, 0.8673, 55, -6.01, -378.6, 0.1327, 2, 53, 14.47, 132.27, 0.82192, 55, -6.01, -358.12, 0.17808, 2, 53, 14.47, 152.75, 0.77145, 55, -6.01, -337.64, 0.22855, 2, 53, 14.47, 173.23, 0.71661, 55, -6.01, -317.16, 0.28339, 2, 53, 14.47, 193.71, 0.65818, 55, -6.01, -296.68, 0.34182, 2, 53, 14.47, 214.19, 0.59702, 55, -6.01, -276.2, 0.40298, 2, 53, 14.47, 234.67, 0.5341, 55, -6.01, -255.72, 0.4659, 2, 53, 14.47, 255.15, 0.47045, 55, -6.01, -235.24, 0.52955, 2, 53, 14.47, 275.63, 0.40719, 55, -6.01, -214.76, 0.59281, 2, 53, 14.47, 296.11, 0.34545, 55, -6.01, -194.28, 0.65455, 2, 53, 14.47, 316.59, 0.28633, 55, -6.01, -173.8, 0.71367, 2, 53, 14.47, 337.07, 0.2308, 55, -6.01, -153.32, 0.7692, 2, 53, 14.47, 357.55, 0.17972, 55, -6.01, -132.84, 0.82028, 2, 53, 14.47, 378.03, 0.13383, 55, -6.01, -112.36, 0.86617, 2, 53, 14.47, 398.51, 0.09376, 55, -6.01, -91.88, 0.90624, 2, 53, 14.47, 418.99, 0.06012, 55, -6.01, -71.4, 0.93988, 2, 53, 14.47, 439.47, 0.03345, 55, -6.01, -50.92, 0.96655, 2, 53, 14.47, 459.95, 0.0142, 55, -6.01, -30.44, 0.9858, 2, 53, 14.47, 480.43, 0.00272, 55, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}, "lighting04": {"type": "mesh", "uvs": [1, 1, 0.5, 1, 0, 1, 0, 0.96, 0, 0.92, 0, 0.88, 0, 0.84, 0, 0.8, 0, 0.76, 0, 0.72, 0, 0.68, 0, 0.64, 0, 0.6, 0, 0.56, 0, 0.52, 0, 0.48, 0, 0.44, 0, 0.4, 0, 0.36, 0, 0.32, 0, 0.28, 0, 0.24, 0, 0.2, 0, 0.16, 0, 0.12, 0, 0.08, 0, 0.04, 0, 0, 0.5, 0, 1, 0, 1, 0.04, 1, 0.08, 1, 0.12, 1, 0.16, 1, 0.2, 1, 0.24, 1, 0.28, 1, 0.32, 1, 0.36, 1, 0.4, 1, 0.44, 1, 0.48, 1, 0.52, 1, 0.56, 1, 0.6, 1, 0.64, 1, 0.68, 1, 0.72, 1, 0.76, 1, 0.8, 1, 0.84, 1, 0.88, 1, 0.92, 1, 0.96, 0.5, 0.96, 0.5, 0.92, 0.5, 0.88, 0.5, 0.84, 0.5, 0.8, 0.5, 0.76, 0.5, 0.72, 0.5, 0.68, 0.5, 0.64, 0.5, 0.6, 0.5, 0.56, 0.5, 0.52, 0.5, 0.48, 0.5, 0.44, 0.5, 0.4, 0.5, 0.36, 0.5, 0.32, 0.5, 0.28, 0.5, 0.24, 0.5, 0.2, 0.5, 0.16, 0.5, 0.12, 0.5, 0.08, 0.5, 0.04], "triangles": [18, 19, 70, 19, 20, 71, 68, 69, 38, 70, 19, 71, 71, 20, 72, 72, 21, 73, 71, 72, 35, 72, 73, 34, 67, 16, 68, 17, 18, 69, 68, 17, 69, 69, 18, 70, 16, 17, 68, 15, 16, 67, 66, 15, 67, 14, 15, 66, 67, 68, 39, 66, 67, 40, 41, 66, 40, 38, 69, 37, 70, 71, 36, 37, 70, 36, 73, 74, 33, 34, 73, 33, 73, 22, 74, 21, 22, 73, 74, 23, 75, 75, 76, 31, 24, 25, 76, 76, 77, 30, 77, 28, 29, 30, 77, 29, 77, 26, 28, 26, 27, 28, 25, 26, 77, 76, 25, 77, 75, 24, 76, 23, 24, 75, 22, 23, 74, 74, 75, 32, 32, 75, 31, 33, 74, 32, 31, 76, 30, 20, 21, 72, 36, 71, 35, 35, 72, 34, 69, 70, 37, 39, 68, 38, 40, 67, 39, 65, 66, 41, 54, 3, 55, 12, 13, 64, 63, 12, 64, 11, 12, 63, 62, 11, 63, 10, 11, 62, 61, 10, 62, 9, 10, 61, 60, 9, 61, 8, 9, 60, 7, 8, 59, 6, 7, 58, 64, 13, 65, 13, 14, 65, 65, 14, 66, 2, 3, 54, 4, 5, 56, 55, 4, 56, 54, 55, 52, 58, 7, 59, 55, 56, 51, 52, 55, 51, 59, 60, 47, 49, 58, 48, 61, 62, 45, 46, 61, 45, 47, 60, 46, 44, 63, 43, 64, 65, 42, 42, 65, 41, 43, 64, 42, 63, 64, 43, 62, 63, 44, 45, 62, 44, 60, 61, 46, 48, 59, 47, 58, 59, 48, 57, 58, 49, 50, 57, 49, 56, 57, 50, 51, 56, 50, 59, 8, 60, 57, 6, 58, 3, 4, 55, 1, 2, 54, 5, 6, 57, 56, 5, 57, 53, 54, 52, 1, 54, 53, 0, 1, 53], "vertices": [2, 53, 46.47, -11.09, 0.99997, 55, 25.99, -501.48, 3e-05, 1, 53, 14.47, -11.09, 1, 1, 53, -17.53, -11.09, 1, 2, 53, -17.53, 9.39, 0.9975, 55, -38.01, -481, 0.0025, 2, 53, -17.53, 29.87, 0.98643, 55, -38.01, -460.52, 0.01357, 2, 53, -17.53, 50.35, 0.9671, 55, -38.01, -440.04, 0.0329, 2, 53, -17.53, 70.83, 0.94045, 55, -38.01, -419.56, 0.05955, 2, 53, -17.53, 91.31, 0.90703, 55, -38.01, -399.08, 0.09297, 2, 53, -17.53, 111.79, 0.86732, 55, -38.01, -378.6, 0.13268, 2, 53, -17.53, 132.27, 0.82193, 55, -38.01, -358.12, 0.17807, 2, 53, -17.53, 152.75, 0.77146, 55, -38.01, -337.64, 0.22854, 2, 53, -17.53, 173.23, 0.71662, 55, -38.01, -317.16, 0.28338, 2, 53, -17.53, 193.71, 0.65818, 55, -38.01, -296.68, 0.34182, 2, 53, -17.53, 214.19, 0.59701, 55, -38.01, -276.2, 0.40299, 2, 53, -17.53, 234.67, 0.53408, 55, -38.01, -255.72, 0.46592, 2, 53, -17.53, 255.15, 0.47044, 55, -38.01, -235.24, 0.52956, 2, 53, -17.53, 275.63, 0.40718, 55, -38.01, -214.76, 0.59281, 2, 53, -17.53, 296.11, 0.34547, 55, -38.01, -194.28, 0.65453, 2, 53, -17.53, 316.59, 0.28634, 55, -38.01, -173.8, 0.71366, 2, 53, -17.53, 337.07, 0.23082, 55, -38.01, -153.32, 0.76918, 2, 53, -17.53, 357.55, 0.17974, 55, -38.01, -132.84, 0.82026, 2, 53, -17.53, 378.03, 0.13386, 55, -38.01, -112.36, 0.86614, 2, 53, -17.53, 398.51, 0.09381, 55, -38.01, -91.88, 0.90619, 2, 53, -17.53, 418.99, 0.0602, 55, -38.01, -71.4, 0.9398, 2, 53, -17.53, 439.47, 0.03363, 55, -38.01, -50.92, 0.96637, 2, 53, -17.53, 459.95, 0.01452, 55, -38.01, -30.44, 0.98548, 2, 53, -17.53, 480.43, 0.00346, 55, -38.01, -9.96, 0.99654, 2, 53, -17.53, 500.91, 2e-05, 55, -38.01, 10.52, 0.99998, 1, 55, -6.01, 10.52, 1, 1, 55, 25.99, 10.52, 1, 2, 53, 46.47, 480.43, 0.00284, 55, 25.99, -9.96, 0.99716, 2, 53, 46.47, 459.95, 0.01535, 55, 25.99, -30.44, 0.98465, 2, 53, 46.47, 439.47, 0.03315, 55, 25.99, -50.92, 0.96685, 2, 53, 46.47, 418.99, 0.06005, 55, 25.99, -71.4, 0.93995, 2, 53, 46.47, 398.51, 0.09372, 55, 25.99, -91.88, 0.90628, 2, 53, 46.47, 378.03, 0.1338, 55, 25.99, -112.36, 0.8662, 2, 53, 46.47, 357.55, 0.1797, 55, 25.99, -132.84, 0.8203, 2, 53, 46.47, 337.07, 0.23078, 55, 25.99, -153.32, 0.76922, 2, 53, 46.47, 316.59, 0.28631, 55, 25.99, -173.8, 0.71369, 2, 53, 46.47, 296.11, 0.3454, 55, 25.99, -194.28, 0.6546, 2, 53, 46.47, 275.63, 0.40729, 55, 25.99, -214.76, 0.59271, 2, 53, 46.47, 255.15, 0.47048, 55, 25.99, -235.24, 0.52952, 2, 53, 46.47, 234.67, 0.53409, 55, 25.99, -255.72, 0.46591, 2, 53, 46.47, 214.19, 0.59703, 55, 25.99, -276.2, 0.40297, 2, 53, 46.47, 193.71, 0.65819, 55, 25.99, -296.68, 0.34181, 2, 53, 46.47, 173.23, 0.71662, 55, 25.99, -317.16, 0.28338, 2, 53, 46.47, 152.75, 0.77144, 55, 25.99, -337.64, 0.22856, 2, 53, 46.47, 132.27, 0.82191, 55, 25.99, -358.12, 0.17809, 2, 53, 46.47, 111.79, 0.86727, 55, 25.99, -378.6, 0.13273, 2, 53, 46.47, 91.31, 0.90689, 55, 25.99, -399.08, 0.09311, 2, 53, 46.47, 70.83, 0.94016, 55, 25.99, -419.56, 0.05984, 2, 53, 46.47, 50.35, 0.96652, 55, 25.99, -440.04, 0.03348, 2, 53, 46.47, 29.87, 0.98537, 55, 25.99, -460.52, 0.01463, 2, 53, 46.47, 9.39, 0.99644, 55, 25.99, -481, 0.00356, 2, 53, 14.47, 9.39, 0.9971, 55, -6.01, -481, 0.0029, 2, 53, 14.47, 29.87, 0.98595, 55, -6.01, -460.52, 0.01405, 2, 53, 14.47, 50.35, 0.96682, 55, -6.01, -440.04, 0.03318, 2, 53, 14.47, 70.83, 0.94032, 55, -6.01, -419.56, 0.05968, 2, 53, 14.47, 91.31, 0.90696, 55, -6.01, -399.08, 0.09304, 2, 53, 14.47, 111.79, 0.8673, 55, -6.01, -378.6, 0.1327, 2, 53, 14.47, 132.27, 0.82192, 55, -6.01, -358.12, 0.17808, 2, 53, 14.47, 152.75, 0.77145, 55, -6.01, -337.64, 0.22855, 2, 53, 14.47, 173.23, 0.71661, 55, -6.01, -317.16, 0.28339, 2, 53, 14.47, 193.71, 0.65818, 55, -6.01, -296.68, 0.34182, 2, 53, 14.47, 214.19, 0.59702, 55, -6.01, -276.2, 0.40298, 2, 53, 14.47, 234.67, 0.5341, 55, -6.01, -255.72, 0.4659, 2, 53, 14.47, 255.15, 0.47045, 55, -6.01, -235.24, 0.52955, 2, 53, 14.47, 275.63, 0.40719, 55, -6.01, -214.76, 0.59281, 2, 53, 14.47, 296.11, 0.34545, 55, -6.01, -194.28, 0.65455, 2, 53, 14.47, 316.59, 0.28633, 55, -6.01, -173.8, 0.71367, 2, 53, 14.47, 337.07, 0.2308, 55, -6.01, -153.32, 0.7692, 2, 53, 14.47, 357.55, 0.17972, 55, -6.01, -132.84, 0.82028, 2, 53, 14.47, 378.03, 0.13383, 55, -6.01, -112.36, 0.86617, 2, 53, 14.47, 398.51, 0.09376, 55, -6.01, -91.88, 0.90624, 2, 53, 14.47, 418.99, 0.06012, 55, -6.01, -71.4, 0.93988, 2, 53, 14.47, 439.47, 0.03345, 55, -6.01, -50.92, 0.96655, 2, 53, 14.47, 459.95, 0.0142, 55, -6.01, -30.44, 0.9858, 2, 53, 14.47, 480.43, 0.00272, 55, -6.01, -9.96, 0.99728], "hull": 54, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 0], "width": 64, "height": 512}}, "shadow": {"shadow": {"x": -11.65, "y": -5.39, "width": 256, "height": 204}}}}], "animations": {"character_2_blue": {"slots": {"01": {"rgba": [{"color": "ffffff00"}]}, "02": {"rgba": [{"color": "ffffff00"}]}, "03": {"rgba": [{"color": "ffffff00"}]}, "04": {"rgba": [{"color": "ffffff00"}]}, "05": {"rgba": [{"color": "ffffff00"}]}, "06": {"rgba": [{"color": "ffffff00"}]}, "07": {"rgba": [{"color": "ffffff00"}]}, "Asset 27a3": {"rgba": [{"color": "ffffff00"}]}, "Asset 27b3": {"rgba": [{"color": "ffffff00"}]}, "Asset 27c3": {"rgba": [{"color": "ffffff00"}]}, "glow (1)": {"attachment": [{"name": null}]}, "glow (1)2": {"attachment": [{"name": null}]}}, "bones": {"all2": {"translate": [{"curve": [0.11, 0, 0.229, 3.25, 0.11, -2.98, 0.223, -6.28]}, {"time": 0.3333, "x": 8.34, "y": -6.28, "curve": [0.437, 13.42, 0.556, 17.91, 0.444, -6.28, 0.556, -5.1]}, {"time": 0.6667, "x": 17.91, "y": -3.73, "curve": [0.802, 17.91, 0.931, 13.92, 0.802, -2.05, 0.931, 3.75]}, {"time": 1.0667, "x": 6.93, "y": 3.75, "curve": [1.138, 3.24, 1.254, 0.91, 1.143, 3.75, 1.259, 1.48]}, {"time": 1.3333}]}, "target5": {"translate": [{"x": -7.74, "y": -21.01, "curve": [0.2, -7.74, 0.409, -4.66, 0.2, -21.01, 0.4, -17.27]}, {"time": 0.6, "x": -1.23, "y": -17.27, "curve": [0.666, -0.06, 0.711, 0.37, 0.656, -17.27, 0.711, -17.48]}, {"time": 0.7667, "x": 0.37, "y": -17.82, "curve": [0.922, 0.37, 1.079, -4.44, 0.922, -18.76, 1.073, -19.57]}, {"time": 1.2333, "x": -7.05, "y": -20.51, "curve": [1.265, -7.59, 1.3, -7.74, 1.266, -20.7, 1.3, -21.01]}, {"time": 1.3333, "x": -7.74, "y": -21.01}]}, "target4": {"translate": [{"curve": [0.108, -3.87, 0.24, -13.49, 0.127, 0, 0.264, -4.79]}, {"time": 0.3667, "x": -14.51, "y": -8.95, "curve": [0.477, -15.39, 0.611, -13.44, 0.454, -12.5, 0.59, -21.27]}, {"time": 0.7, "x": -11.23, "y": -21.27, "curve": [0.819, -8.26, 0.948, 3.44, 0.819, -21.27, 0.984, -16.05]}, {"time": 1.0667, "x": 3.44, "y": -11.49, "curve": [1.151, 3.44, 1.261, 1.93, 1.128, -8.12, 1.249, 0]}, {"time": 1.3333}]}, "Asset 8": {"rotate": [{"curve": [0.042, 1.62, 0.107, 6.98]}, {"time": 0.1667, "value": 6.98, "curve": [0.243, 6.98, 0.324, -3.93]}, {"time": 0.4, "value": -3.93, "curve": [0.485, -3.93, 0.6, -2.42]}, {"time": 0.6667, "curve": [0.718, 1.83, 0.774, 6.98]}, {"time": 0.8333, "value": 6.98, "curve": [0.91, 6.98, 1.024, -3.93]}, {"time": 1.1, "value": -3.93, "curve": [1.185, -3.93, 1.28, -2.05]}, {"time": 1.3333}]}, "Asset 9": {"rotate": [{"value": -2.96, "curve": [0.034, -1.45, 0.074, 2.18]}, {"time": 0.1, "value": 3.94, "curve": [0.149, 7.23, 0.207, 10.27]}, {"time": 0.2667, "value": 10.27, "curve": [0.343, 10.27, 0.424, -7.03]}, {"time": 0.5, "value": -7.03, "curve": [0.551, -7.03, 0.616, -5.68]}, {"time": 0.6667, "value": -4.27, "curve": [0.701, -3.34, 0.733, -1.6]}, {"time": 0.7667, "curve": [0.826, 2.81, 0.874, 10.27]}, {"time": 0.9333, "value": 10.27, "curve": [1.01, 10.27, 1.09, -7.03]}, {"time": 1.1667, "value": -7.03, "curve": [1.218, -7.03, 1.287, -5.05]}, {"time": 1.3333, "value": -2.96}]}, "Asset 10": {"rotate": [{"value": -5.47, "curve": [0.073, -2.58, 0.127, -0.86]}, {"time": 0.1667, "value": 1.8, "curve": [0.217, 5.12, 0.274, 10.52]}, {"time": 0.3333, "value": 10.52, "curve": [0.418, 10.52, 0.515, -7.23]}, {"time": 0.6, "value": -7.23, "curve": [0.625, -7.23, 0.641, -7.15]}, {"time": 0.6667, "value": -6.35, "curve": [0.718, -4.74, 0.749, -2.38]}, {"time": 0.8, "curve": [0.859, 2.78, 0.941, 10.52]}, {"time": 1, "value": 10.52, "curve": [1.085, 10.52, 1.149, -7.23]}, {"time": 1.2333, "value": -7.23, "curve": [1.259, -7.23, 1.314, -6.57]}, {"time": 1.3333, "value": -5.47}]}, "Asset 11": {"rotate": [{"value": -3.23, "curve": [0.051, -2.3, 0.116, -0.65]}, {"time": 0.1667, "value": 0.62, "curve": [0.285, 3.59, 0.381, 10.52]}, {"time": 0.5, "value": 10.52, "curve": [0.661, 10.52, 0.839, -6.98]}, {"time": 1, "value": -6.98, "curve": [1.11, -6.98, 1.22, -5.08]}, {"time": 1.3333, "value": -3.23}]}, "Asset 7a": {"rotate": [{"curve": [0.079, -8.13, 0.156, -8.25]}, {"time": 0.2333, "value": -8.25, "curve": [0.311, -8.25, 0.389, 5.9]}, {"time": 0.4667, "value": 5.9, "curve": [0.533, 5.9, 0.61, 5.9]}, {"time": 0.6667, "curve": [0.745, -8.13, 0.822, -8.25]}, {"time": 0.9, "value": -8.25, "curve": [0.978, -8.25, 1.056, 5.9]}, {"time": 1.1333, "value": 5.9, "curve": [1.2, 5.9, 1.276, 5.11]}, {"time": 1.3333}]}, "Asset 7b": {"rotate": [{"value": -3.67, "curve": [0.056, -3.67, 0.111, -8.25]}, {"time": 0.1667, "value": -8.25, "curve": [0.233, -8.25, 0.3, 5.9]}, {"time": 0.3667, "value": 5.9, "curve": [0.467, 5.9, 0.567, -0.41]}, {"time": 0.6667, "value": -3.67, "curve": [0.711, -5.12, 0.756, -8.25]}, {"time": 0.8, "value": -8.25, "curve": [0.878, -8.25, 0.956, 5.9]}, {"time": 1.0333, "value": 5.9, "curve": [1.133, 5.9, 1.237, -3.67]}, {"time": 1.3333, "value": -3.67}]}, "Asset 12": {"rotate": [{"value": 3.54, "curve": [0.122, 3.54, 0.244, -8.25]}, {"time": 0.3667, "value": -8.25, "curve": [0.444, -8.25, 0.522, 5.9]}, {"time": 0.6, "value": 5.9, "curve": [0.622, 5.9, 0.644, 4.32]}, {"time": 0.6667, "value": 3.54, "curve": [0.778, -0.39, 0.889, -8.25]}, {"time": 1, "value": -8.25, "curve": [1.078, -8.25, 1.156, 5.9]}, {"time": 1.2333, "value": 5.9, "curve": [1.267, 5.9, 1.308, 3.54]}, {"time": 1.3333, "value": 3.54}]}, "Asset 13": {"rotate": [{"value": 4.32, "curve": [0.006, 5.72, 0.022, 5.9]}, {"time": 0.0333, "value": 5.9, "curve": [0.178, 5.9, 0.322, -8.25]}, {"time": 0.4667, "value": -8.25, "curve": [0.533, -8.25, 0.608, -8.25]}, {"time": 0.6667, "value": 4.32, "curve": [0.673, 5.72, 0.689, 5.9]}, {"time": 0.7, "value": 5.9, "curve": [0.844, 5.9, 0.989, -8.25]}, {"time": 1.1333, "value": -8.25, "curve": [1.2, -8.25, 1.268, -1.8]}, {"time": 1.3333, "value": 4.32}]}}}, "character_2_pink": {"slots": {"01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "01_a"}, {"time": 0.2667, "name": "01"}, {"time": 1.5667, "name": "01_a"}, {"time": 1.6, "name": "01"}]}, "02": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00"}], "attachment": [{"time": 0.1, "name": "02_a"}, {"time": 0.1333, "name": "02"}]}, "03": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0.5, "name": "03_a"}, {"time": 0.5333, "name": "03"}]}, "04": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "04_a"}, {"time": 0.8333, "name": "04"}, {"time": 2.4333, "name": "04_a"}, {"time": 2.5, "name": "04"}]}, "05": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "05_a"}, {"time": 1.1, "name": "05"}]}, "06": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0.1, "name": "06_a"}]}, "07": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"time": 0.2667, "name": "07_a"}, {"time": 0.3, "name": "07"}, {"time": 1.9333, "name": "07_a"}, {"time": 1.9667, "name": "07"}]}, "Asset 27a3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}]}, "Asset 27b3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff00"}]}, "Asset 27c3": {"rgba": [{"color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "glow (1)2": {"rgba": [{"time": 0.5667, "color": "ff004200"}, {"time": 0.9, "color": "ff0042fe"}, {"time": 1.2667, "color": "ff004200"}, {"time": 1.6, "color": "ff0042fe"}, {"time": 1.9667, "color": "ff004200"}, {"time": 2.3, "color": "ff0042fe"}, {"time": 2.6667, "color": "ff004200"}]}, "glow (1)3": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)4": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)5": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)6": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)7": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)8": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)9": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)10": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)11": {"attachment": [{"time": 1.2667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.7, "name": "glow (1)"}, {"time": 1.9667, "name": null}]}, "glow (1)12": {"attachment": [{"time": 1.2667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.7, "name": "glow (1)"}, {"time": 1.9667, "name": null}]}, "glow (1)13": {"rgba": [{"time": 0.0667, "color": "ffffff5d"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffff5d"}, {"time": 1.2, "color": "ffffff00"}, {"time": 2.0333, "color": "ffffff5d"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.4, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.2, "name": null}, {"time": 2.0333, "name": "glow (1)"}, {"time": 2.3333, "name": null}]}, "glow (1)14": {"rgba": [{"time": 0.0667, "color": "f10fff5d"}, {"time": 0.4, "color": "f10fff00"}, {"time": 0.8667, "color": "f10fff5d"}, {"time": 1.2, "color": "f10fff00"}, {"time": 2.0333, "color": "f10fff5d"}, {"time": 2.3333, "color": "f10fff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.4, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.2, "name": null}, {"time": 2.0333, "name": "glow (1)"}, {"time": 2.3333, "name": null}]}, "glow (1)15": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffff29", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffff9b", "curve": "stepped"}, {"time": 1.5, "color": "ffffff31"}], "attachment": [{"time": 0.1, "name": "glow (1)"}, {"time": 0.1333, "name": null}, {"time": 0.2333, "name": "glow (1)"}, {"time": 0.2667, "name": null}, {"time": 0.5, "name": "glow (1)"}, {"time": 0.5333, "name": null}, {"time": 0.7667, "name": "glow (1)"}, {"time": 0.8, "name": null}, {"time": 1.0333, "name": "glow (1)"}, {"time": 1.0667, "name": null}]}, "lighting01": {"attachment": [{"name": "lighting01"}, {"time": 0.0667, "name": "lighting02"}, {"time": 0.1333, "name": "lighting03"}, {"time": 0.2333, "name": "lighting04"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "lighting02"}, {"time": 0.5333, "name": "lighting03"}, {"time": 0.6333, "name": "lighting04"}, {"time": 0.7, "name": null}]}, "lighting02": {"attachment": [{"time": 0.2333, "name": "lighting01"}, {"time": 0.3, "name": "lighting02"}, {"time": 0.4, "name": "lighting03"}, {"time": 0.5, "name": "lighting04"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "lighting01"}, {"time": 0.9333, "name": "lighting02"}, {"time": 1, "name": "lighting03"}, {"time": 1.1333, "name": "lighting04"}, {"time": 1.2667, "name": "lighting01"}, {"time": 1.3333, "name": "lighting02"}, {"time": 1.4, "name": "lighting03"}, {"time": 1.5, "name": "lighting04"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "lighting01"}, {"time": 2.0333, "name": "lighting02"}, {"time": 2.1, "name": "lighting03"}, {"time": 2.2, "name": "lighting04"}, {"time": 2.3333, "name": "lighting01"}, {"time": 2.4, "name": "lighting02"}, {"time": 2.4667, "name": "lighting03"}, {"time": 2.5667, "name": "lighting04"}, {"time": 2.6667, "name": null}]}, "lighting03": {"attachment": [{"time": 1.2667, "name": "lighting01"}, {"time": 1.3333, "name": "lighting02"}, {"time": 1.4, "name": "lighting03"}, {"time": 1.5, "name": "lighting04"}, {"time": 1.5667, "name": null}, {"time": 1.7, "name": "lighting02"}, {"time": 1.7667, "name": "lighting03"}, {"time": 1.9, "name": "lighting04"}, {"time": 1.9667, "name": null}]}}, "bones": {"bone2": {"translate": [{"x": 87.76, "y": -77.51, "curve": "stepped"}, {"time": 0.3333, "x": 87.76, "y": -77.51}, {"time": 0.7333, "x": 107, "y": -75.68}]}, "bone3": {"rotate": [{"time": 0.3}, {"time": 0.6667, "value": -8.3}], "translate": [{"x": -30.84, "y": 12.07}, {"time": 0.2667, "x": -42.01, "y": 16.98}, {"time": 0.3, "x": -126.96, "y": 12.09}], "scale": [{}, {"time": 0.2667, "x": 0.344, "y": 0.909}, {"time": 0.6667, "x": 0.917, "y": 0.909}]}, "bone6": {"rotate": [{"time": 0.2333}, {"time": 0.5333, "value": 12.55}, {"time": 0.5667}, {"time": 0.8667, "value": -77.85}, {"time": 1.1667, "value": -62.18}, {"time": 1.6333, "value": -78.65}, {"time": 2.2333, "value": -85.14}], "translate": [{"time": 0.2333, "x": 80.16, "y": -25.45}, {"time": 0.5333, "x": 56.35, "y": -30.74}, {"time": 0.5667}, {"time": 0.6667, "x": -126.96, "y": 12.09}, {"time": 0.8667, "x": -113.85, "y": 83.89, "curve": "stepped"}, {"time": 1.6333, "x": -113.85, "y": 83.89}, {"time": 1.9667, "x": -77.99, "y": 218.02}], "scale": [{"time": 0.2333}, {"time": 0.5333, "x": 0.344, "y": 0.909}, {"time": 0.7667, "x": 0.917, "y": 0.909}, {"time": 0.8667}, {"time": 1.1667, "x": 0.917, "y": 0.909, "curve": "stepped"}, {"time": 1.6333, "x": 0.917, "y": 0.909}, {"time": 2.2333, "x": 0.086, "y": 0.909}]}, "bone7": {"rotate": [{"time": 0.2333}, {"time": 0.5333, "value": -1.32}, {"time": 0.8667, "value": -57.23, "curve": "stepped"}, {"time": 1.9667, "value": -57.23}, {"time": 2.2333, "value": -91.64}, {"time": 2.3333, "value": -89.4}, {"time": 2.5667, "value": -84.32}], "translate": [{"time": 0.2333, "x": 87.76, "y": -77.51, "curve": "stepped"}, {"time": 0.6, "x": 87.76, "y": -77.51}, {"time": 0.8667, "x": 257.03, "y": -101.48, "curve": "stepped"}, {"time": 1.6333, "x": 257.03, "y": -101.48}, {"time": 1.9667, "x": 198.82, "y": -55.47}, {"time": 2.6667, "x": 195.32, "y": -129.65}]}, "bone5": {"rotate": [{"value": 35.15, "curve": "stepped"}, {"time": 0.8667, "value": -53.32, "curve": "stepped"}, {"time": 1.6333, "value": 15.45}], "translate": [{"x": 113.29, "y": 14.29, "curve": "stepped"}, {"time": 0.8667, "x": 70.23, "y": 60.85}]}, "bone8": {"scale": [{"time": 1.9667, "x": 1.786, "y": 1.786}, {"time": 2.6667, "x": 6.158, "y": 1.597}]}, "bone9": {"scale": [{"time": 1.9667, "x": 0.574, "y": 0.574}]}, "bone11": {"translate": [{"time": 0.0667, "x": 11.3, "y": -0.36}, {"time": 0.2, "x": 6.2, "y": 1.88}, {"time": 0.3, "x": 6.2, "y": -7.74}], "scale": [{}, {"time": 0.2, "x": 1.084, "y": 0.436}]}, "bone14": {"translate": [{"time": 1.3333, "x": 11.3, "y": -0.36}, {"time": 1.4667, "x": 6.2, "y": 1.88}, {"time": 1.5667, "x": 6.2, "y": -7.74}], "scale": [{"time": 1.2667}, {"time": 1.4667, "x": 1.084, "y": 0.436}]}, "bone13": {"rotate": [{"time": 1.5667}, {"time": 1.9333, "value": -8.3}], "translate": [{"time": 1.2667, "x": -30.84, "y": 12.07}, {"time": 1.5333, "x": -42.01, "y": 16.98}, {"time": 1.5667, "x": -126.96, "y": 12.09}], "scale": [{"time": 1.2667}, {"time": 1.5333, "x": 0.344, "y": 0.909}, {"time": 1.9333, "x": 0.917, "y": 0.909}]}, "bone15": {"translate": [{"time": 1.2667, "x": 87.76, "y": -77.51, "curve": "stepped"}, {"time": 1.6, "x": 87.76, "y": -77.51}, {"time": 2, "x": 107, "y": -75.68}]}, "glow_big": {"translate": [{"time": 0.0667, "x": -6.26, "y": 179.85}], "scale": [{"time": 0.0667, "x": -20.83, "y": -20.83}]}, "target5": {"translate": [{"x": -7.74, "y": -21.01, "curve": [0.2, -7.74, 0.409, -4.66, 0.2, -21.01, 0.4, -17.27]}, {"time": 0.6, "x": -1.23, "y": -17.27, "curve": [0.666, -0.06, 0.711, 0.37, 0.656, -17.27, 0.711, -17.48]}, {"time": 0.7667, "x": 0.37, "y": -17.82, "curve": [0.922, 0.37, 1.079, -4.44, 0.922, -18.76, 1.073, -19.57]}, {"time": 1.2333, "x": -7.05, "y": -20.51, "curve": [1.265, -7.59, 1.3, -7.74, 1.266, -20.7, 1.3, -21.01]}, {"time": 1.3333, "x": -7.74, "y": -21.01, "curve": [1.533, -7.74, 1.742, -4.66, 1.533, -21.01, 1.733, -17.27]}, {"time": 1.9333, "x": -1.23, "y": -17.27, "curve": [1.999, -0.06, 2.044, 0.37, 1.989, -17.27, 2.044, -17.48]}, {"time": 2.1, "x": 0.37, "y": -17.82, "curve": [2.256, 0.37, 2.413, -4.44, 2.256, -18.76, 2.407, -19.57]}, {"time": 2.5667, "x": -7.05, "y": -20.51, "curve": [2.598, -7.59, 2.633, -7.74, 2.6, -20.7, 2.633, -21.01]}, {"time": 2.6667, "x": -7.74, "y": -21.01}]}, "all2": {"translate": [{"curve": [0.11, 0, 0.229, 3.25, 0.11, -2.98, 0.223, -6.28]}, {"time": 0.3333, "x": 8.34, "y": -6.28, "curve": [0.437, 13.42, 0.556, 17.91, 0.444, -6.28, 0.556, -5.1]}, {"time": 0.6667, "x": 17.91, "y": -3.73, "curve": [0.802, 17.91, 0.931, 13.92, 0.802, -2.05, 0.931, 3.75]}, {"time": 1.0667, "x": 6.93, "y": 3.75, "curve": [1.138, 3.24, 1.244, 0, 1.143, 3.75, 1.244, 2.4]}, {"time": 1.3333, "curve": [1.444, 0, 1.563, 3.25, 1.444, -2.98, 1.556, -6.28]}, {"time": 1.6667, "x": 8.34, "y": -6.28, "curve": [1.771, 13.42, 1.89, 17.91, 1.777, -6.28, 1.89, -5.1]}, {"time": 2, "x": 17.91, "y": -3.73, "curve": [2.136, 17.91, 2.264, 13.92, 2.136, -2.05, 2.264, 3.75]}, {"time": 2.4, "x": 6.93, "y": 3.75, "curve": [2.472, 3.24, 2.587, 0.91, 2.476, 3.75, 2.592, 1.48]}, {"time": 2.6667}]}, "target4": {"translate": [{"curve": [0.108, -3.87, 0.24, -13.49, 0.127, 0, 0.264, -4.79]}, {"time": 0.3667, "x": -14.51, "y": -8.95, "curve": [0.477, -15.39, 0.611, -13.44, 0.454, -12.5, 0.59, -21.27]}, {"time": 0.7, "x": -11.23, "y": -21.27, "curve": [0.819, -8.26, 0.948, 3.44, 0.819, -21.27, 0.984, -16.05]}, {"time": 1.0667, "x": 3.44, "y": -11.49, "curve": [1.151, 3.44, 1.244, 3.2, 1.128, -8.12, 1.244, 0]}, {"time": 1.3333, "curve": [1.441, -3.87, 1.573, -13.49, 1.46, 0, 1.598, -4.79]}, {"time": 1.7, "x": -14.51, "y": -8.95, "curve": [1.81, -15.39, 1.945, -13.44, 1.787, -12.5, 1.923, -21.27]}, {"time": 2.0333, "x": -11.23, "y": -21.27, "curve": [2.152, -8.26, 2.281, 3.44, 2.152, -21.27, 2.317, -16.05]}, {"time": 2.4, "x": 3.44, "y": -11.49, "curve": [2.485, 3.44, 2.594, 1.93, 2.461, -8.12, 2.582, 0]}, {"time": 2.6667}]}, "Asset 8": {"rotate": [{"curve": [0.042, 1.62, 0.107, 6.98]}, {"time": 0.1667, "value": 6.98, "curve": [0.243, 6.98, 0.324, -3.93]}, {"time": 0.4, "value": -3.93, "curve": [0.485, -3.93, 0.6, -2.42]}, {"time": 0.6667, "curve": [0.718, 1.83, 0.774, 6.98]}, {"time": 0.8333, "value": 6.98, "curve": [0.91, 6.98, 1.024, -3.93]}, {"time": 1.1, "value": -3.93, "curve": [1.185, -3.93, 1.256, -2.98]}, {"time": 1.3333, "curve": [1.376, 1.62, 1.441, 6.98]}, {"time": 1.5, "value": 6.98, "curve": [1.576, 6.98, 1.657, -3.93]}, {"time": 1.7333, "value": -3.93, "curve": [1.818, -3.93, 1.933, -2.42]}, {"time": 2, "curve": [2.051, 1.83, 2.107, 6.98]}, {"time": 2.1667, "value": 6.98, "curve": [2.243, 6.98, 2.357, -3.93]}, {"time": 2.4333, "value": -3.93, "curve": [2.518, -3.93, 2.613, -2.05]}, {"time": 2.6667}]}, "Asset 9": {"rotate": [{"value": -2.96, "curve": [0.034, -1.45, 0.074, 2.18]}, {"time": 0.1, "value": 3.94, "curve": [0.149, 7.23, 0.207, 10.27]}, {"time": 0.2667, "value": 10.27, "curve": [0.343, 10.27, 0.424, -7.03]}, {"time": 0.5, "value": -7.03, "curve": [0.551, -7.03, 0.616, -5.68]}, {"time": 0.6667, "value": -4.27, "curve": [0.701, -3.34, 0.733, -1.6]}, {"time": 0.7667, "curve": [0.826, 2.81, 0.874, 10.27]}, {"time": 0.9333, "value": 10.27, "curve": [1.01, 10.27, 1.09, -7.03]}, {"time": 1.1667, "value": -7.03, "curve": [1.218, -7.03, 1.278, -5.43]}, {"time": 1.3333, "value": -2.96, "curve": [1.367, -1.45, 1.407, 2.18]}, {"time": 1.4333, "value": 3.94, "curve": [1.483, 7.23, 1.541, 10.27]}, {"time": 1.6, "value": 10.27, "curve": [1.676, 10.27, 1.757, -7.03]}, {"time": 1.8333, "value": -7.03, "curve": [1.884, -7.03, 1.949, -5.68]}, {"time": 2, "value": -4.27, "curve": [2.034, -3.34, 2.066, -1.6]}, {"time": 2.1, "curve": [2.159, 2.81, 2.207, 10.27]}, {"time": 2.2667, "value": 10.27, "curve": [2.343, 10.27, 2.424, -7.03]}, {"time": 2.5, "value": -7.03, "curve": [2.551, -7.03, 2.62, -5.05]}, {"time": 2.6667, "value": -2.96}]}, "Asset 10": {"rotate": [{"value": -5.47, "curve": [0.073, -2.58, 0.127, -0.86]}, {"time": 0.1667, "value": 1.8, "curve": [0.217, 5.12, 0.274, 10.52]}, {"time": 0.3333, "value": 10.52, "curve": [0.418, 10.52, 0.515, -7.23]}, {"time": 0.6, "value": -7.23, "curve": [0.625, -7.23, 0.641, -7.15]}, {"time": 0.6667, "value": -6.35, "curve": [0.718, -4.74, 0.749, -2.38]}, {"time": 0.8, "curve": [0.859, 2.78, 0.941, 10.52]}, {"time": 1, "value": 10.52, "curve": [1.085, 10.52, 1.149, -7.23]}, {"time": 1.2333, "value": -7.23, "curve": [1.259, -7.23, 1.3, -6.78]}, {"time": 1.3333, "value": -5.47, "curve": [1.407, -2.58, 1.46, -0.86]}, {"time": 1.5, "value": 1.8, "curve": [1.55, 5.12, 1.607, 10.52]}, {"time": 1.6667, "value": 10.52, "curve": [1.751, 10.52, 1.849, -7.23]}, {"time": 1.9333, "value": -7.23, "curve": [1.959, -7.23, 1.975, -7.15]}, {"time": 2, "value": -6.35, "curve": [2.051, -4.74, 2.082, -2.38]}, {"time": 2.1333, "curve": [2.193, 2.78, 2.274, 10.52]}, {"time": 2.3333, "value": 10.52, "curve": [2.418, 10.52, 2.482, -7.23]}, {"time": 2.5667, "value": -7.23, "curve": [2.592, -7.23, 2.647, -6.57]}, {"time": 2.6667, "value": -5.47}]}, "Asset 11": {"rotate": [{"value": -3.23, "curve": [0.051, -2.3, 0.116, -0.65]}, {"time": 0.1667, "value": 0.62, "curve": [0.285, 3.59, 0.381, 10.52]}, {"time": 0.5, "value": 10.52, "curve": [0.661, 10.52, 0.839, -6.98]}, {"time": 1, "value": -6.98, "curve": [1.11, -6.98, 1.222, -5.26]}, {"time": 1.3333, "value": -3.23, "curve": [1.384, -2.3, 1.449, -0.65]}, {"time": 1.5, "value": 0.62, "curve": [1.619, 3.59, 1.715, 10.52]}, {"time": 1.8333, "value": 10.52, "curve": [1.994, 10.52, 2.172, -6.98]}, {"time": 2.3333, "value": -6.98, "curve": [2.444, -6.98, 2.554, -5.08]}, {"time": 2.6667, "value": -3.23}]}, "Asset 7a": {"rotate": [{"curve": [0.079, -8.13, 0.156, -8.25]}, {"time": 0.2333, "value": -8.25, "curve": [0.311, -8.25, 0.389, 5.9]}, {"time": 0.4667, "value": 5.9, "curve": [0.533, 5.9, 0.61, 5.9]}, {"time": 0.6667, "curve": [0.745, -8.13, 0.822, -8.25]}, {"time": 0.9, "value": -8.25, "curve": [0.978, -8.25, 1.056, 5.9]}, {"time": 1.1333, "value": 5.9, "curve": [1.2, 5.9, 1.276, 5.9]}, {"time": 1.3333, "curve": [1.412, -8.13, 1.489, -8.25]}, {"time": 1.5667, "value": -8.25, "curve": [1.644, -8.25, 1.722, 5.9]}, {"time": 1.8, "value": 5.9, "curve": [1.867, 5.9, 1.943, 5.9]}, {"time": 2, "curve": [2.079, -8.13, 2.156, -8.25]}, {"time": 2.2333, "value": -8.25, "curve": [2.311, -8.25, 2.389, 5.9]}, {"time": 2.4667, "value": 5.9, "curve": [2.533, 5.9, 2.609, 5.11]}, {"time": 2.6667}]}, "Asset 7b": {"rotate": [{"value": -3.67, "curve": [0.056, -3.67, 0.111, -8.25]}, {"time": 0.1667, "value": -8.25, "curve": [0.233, -8.25, 0.3, 5.9]}, {"time": 0.3667, "value": 5.9, "curve": [0.467, 5.9, 0.567, -0.41]}, {"time": 0.6667, "value": -3.67, "curve": [0.711, -5.12, 0.756, -8.25]}, {"time": 0.8, "value": -8.25, "curve": [0.878, -8.25, 0.956, 5.9]}, {"time": 1.0333, "value": 5.9, "curve": [1.133, 5.9, 1.233, -0.64]}, {"time": 1.3333, "value": -3.67, "curve": [1.389, -5.35, 1.444, -8.25]}, {"time": 1.5, "value": -8.25, "curve": [1.567, -8.25, 1.633, 5.9]}, {"time": 1.7, "value": 5.9, "curve": [1.8, 5.9, 1.9, -0.41]}, {"time": 2, "value": -3.67, "curve": [2.044, -5.12, 2.089, -8.25]}, {"time": 2.1333, "value": -8.25, "curve": [2.211, -8.25, 2.289, 5.9]}, {"time": 2.3667, "value": 5.9, "curve": [2.467, 5.9, 2.571, -3.67]}, {"time": 2.6667, "value": -3.67}]}, "Asset 12": {"rotate": [{"value": 3.54, "curve": [0.122, 3.54, 0.244, -8.25]}, {"time": 0.3667, "value": -8.25, "curve": [0.444, -8.25, 0.522, 5.9]}, {"time": 0.6, "value": 5.9, "curve": [0.622, 5.9, 0.644, 4.32]}, {"time": 0.6667, "value": 3.54, "curve": [0.778, -0.39, 0.889, -8.25]}, {"time": 1, "value": -8.25, "curve": [1.078, -8.25, 1.156, 5.9]}, {"time": 1.2333, "value": 5.9, "curve": [1.267, 5.9, 1.3, 4.55]}, {"time": 1.3333, "value": 3.54, "curve": [1.456, -0.17, 1.578, -8.25]}, {"time": 1.7, "value": -8.25, "curve": [1.778, -8.25, 1.856, 5.9]}, {"time": 1.9333, "value": 5.9, "curve": [1.956, 5.9, 1.978, 4.32]}, {"time": 2, "value": 3.54, "curve": [2.111, -0.39, 2.222, -8.25]}, {"time": 2.3333, "value": -8.25, "curve": [2.411, -8.25, 2.489, 5.9]}, {"time": 2.5667, "value": 5.9, "curve": [2.6, 5.9, 2.642, 3.54]}, {"time": 2.6667, "value": 3.54}]}, "Asset 13": {"rotate": [{"value": 4.32, "curve": [0.006, 5.72, 0.022, 5.9]}, {"time": 0.0333, "value": 5.9, "curve": [0.178, 5.9, 0.322, -8.25]}, {"time": 0.4667, "value": -8.25, "curve": [0.533, -8.25, 0.608, -8.25]}, {"time": 0.6667, "value": 4.32, "curve": [0.673, 5.72, 0.689, 5.9]}, {"time": 0.7, "value": 5.9, "curve": [0.844, 5.9, 0.989, -8.25]}, {"time": 1.1333, "value": -8.25, "curve": [1.2, -8.25, 1.275, -8.25]}, {"time": 1.3333, "value": 4.32, "curve": [1.34, 5.72, 1.356, 5.9]}, {"time": 1.3667, "value": 5.9, "curve": [1.511, 5.9, 1.656, -8.25]}, {"time": 1.8, "value": -8.25, "curve": [1.867, -8.25, 1.942, -8.25]}, {"time": 2, "value": 4.32, "curve": [2.006, 5.72, 2.022, 5.9]}, {"time": 2.0333, "value": 5.9, "curve": [2.178, 5.9, 2.322, -8.25]}, {"time": 2.4667, "value": -8.25, "curve": [2.533, -8.25, 2.602, -1.8]}, {"time": 2.6667, "value": 4.32}]}, "target10": {"scale": [{"time": 0.1, "x": 0.636, "y": 0.636}]}, "01": {"scale": [{"time": 0.2}, {"time": 0.3667, "x": 1.273}, {"time": 1.5333}, {"time": 1.7, "x": 1.273}]}, "03": {"scale": [{"time": 0.5}, {"time": 0.5667, "x": 1.506}]}, "07": {"scale": [{"time": 1.0333}, {"time": 1.2, "x": 1.527}]}}}, "character_2_yellow": {"slots": {"01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffff00"}], "attachment": [{"time": 0.2333, "name": "01_a"}, {"time": 0.2667, "name": "01"}, {"time": 1.5667, "name": "01_a"}, {"time": 1.6, "name": "01"}]}, "02": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00"}], "attachment": [{"time": 0.1, "name": "02_a"}, {"time": 0.1333, "name": "02"}]}, "03": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0.5, "name": "03_a"}, {"time": 0.5333, "name": "03"}]}, "04": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "04_a"}, {"time": 0.8333, "name": "04"}, {"time": 2.4333, "name": "04_a"}, {"time": 2.5, "name": "04"}]}, "05": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{"time": 1.0333, "name": "05_a"}, {"time": 1.1, "name": "05"}]}, "06": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0.1, "name": "06_a"}]}, "07": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"time": 0.2667, "name": "07_a"}, {"time": 0.3, "name": "07"}, {"time": 1.9333, "name": "07_a"}, {"time": 1.9667, "name": "07"}]}, "Asset 27a3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}]}, "Asset 27b3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff00"}]}, "Asset 27c3": {"rgba": [{"color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "glow (1)2": {"rgba": [{"time": 0.5667, "color": "ff004200"}, {"time": 0.9, "color": "ff0042fe"}, {"time": 1.2667, "color": "ff004200"}, {"time": 1.6, "color": "ff0042fe"}, {"time": 1.9667, "color": "ff004200"}, {"time": 2.3, "color": "ff0042fe"}, {"time": 2.6667, "color": "ff004200"}]}, "glow (1)3": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)4": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)5": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)6": {"attachment": [{"time": 0.2333, "name": "glow (1)"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "glow (1)"}, {"time": 2.6667, "name": null}]}, "glow (1)7": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)8": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)9": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)10": {"attachment": [{"name": "glow (1)"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "glow (1)"}, {"time": 0.7, "name": null}]}, "glow (1)11": {"attachment": [{"time": 1.2667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.7, "name": "glow (1)"}, {"time": 1.9667, "name": null}]}, "glow (1)12": {"attachment": [{"time": 1.2667, "name": "glow (1)"}, {"time": 1.5667, "name": null}, {"time": 1.7, "name": "glow (1)"}, {"time": 1.9667, "name": null}]}, "glow (1)13": {"rgba": [{"time": 0.0667, "color": "ffffff5d"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffff5d"}, {"time": 1.2, "color": "ffffff00"}, {"time": 2.0333, "color": "ffffff5d"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.4, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.2, "name": null}, {"time": 2.0333, "name": "glow (1)"}, {"time": 2.3333, "name": null}]}, "glow (1)14": {"rgba": [{"time": 0.0667, "color": "f10fff5d"}, {"time": 0.4, "color": "f10fff00"}, {"time": 0.8667, "color": "f10fff5d"}, {"time": 1.2, "color": "f10fff00"}, {"time": 2.0333, "color": "f10fff5d"}, {"time": 2.3333, "color": "f10fff00"}], "attachment": [{"time": 0.0667, "name": "glow (1)"}, {"time": 0.4, "name": null}, {"time": 0.8667, "name": "glow (1)"}, {"time": 1.2, "name": null}, {"time": 2.0333, "name": "glow (1)"}, {"time": 2.3333, "name": null}]}, "glow (1)15": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffff29", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffff9b", "curve": "stepped"}, {"time": 1.5, "color": "ffffff31"}], "attachment": [{"time": 0.1, "name": "glow (1)"}, {"time": 0.1333, "name": null}, {"time": 0.2333, "name": "glow (1)"}, {"time": 0.2667, "name": null}, {"time": 0.5, "name": "glow (1)"}, {"time": 0.5333, "name": null}, {"time": 0.7667, "name": "glow (1)"}, {"time": 0.8, "name": null}, {"time": 1.0333, "name": "glow (1)"}, {"time": 1.0667, "name": null}]}, "lighting01": {"attachment": [{"name": "lighting01"}, {"time": 0.0667, "name": "lighting02"}, {"time": 0.1333, "name": "lighting03"}, {"time": 0.2333, "name": "lighting04"}, {"time": 0.3, "name": null}, {"time": 0.4667, "name": "lighting02"}, {"time": 0.5333, "name": "lighting03"}, {"time": 0.6333, "name": "lighting04"}, {"time": 0.7, "name": null}]}, "lighting02": {"attachment": [{"time": 0.2333, "name": "lighting01"}, {"time": 0.3, "name": "lighting02"}, {"time": 0.4, "name": "lighting03"}, {"time": 0.5, "name": "lighting04"}, {"time": 0.5667, "name": null}, {"time": 0.8667, "name": "lighting01"}, {"time": 0.9333, "name": "lighting02"}, {"time": 1, "name": "lighting03"}, {"time": 1.1333, "name": "lighting04"}, {"time": 1.2667, "name": "lighting01"}, {"time": 1.3333, "name": "lighting02"}, {"time": 1.4, "name": "lighting03"}, {"time": 1.5, "name": "lighting04"}, {"time": 1.5667, "name": null}, {"time": 1.9667, "name": "lighting01"}, {"time": 2.0333, "name": "lighting02"}, {"time": 2.1, "name": "lighting03"}, {"time": 2.2, "name": "lighting04"}, {"time": 2.3333, "name": "lighting01"}, {"time": 2.4, "name": "lighting02"}, {"time": 2.4667, "name": "lighting03"}, {"time": 2.5667, "name": "lighting04"}, {"time": 2.6667, "name": null}]}, "lighting03": {"attachment": [{"time": 1.2667, "name": "lighting01"}, {"time": 1.3333, "name": "lighting02"}, {"time": 1.4, "name": "lighting03"}, {"time": 1.5, "name": "lighting04"}, {"time": 1.5667, "name": null}, {"time": 1.7, "name": "lighting02"}, {"time": 1.7667, "name": "lighting03"}, {"time": 1.9, "name": "lighting04"}, {"time": 1.9667, "name": null}]}, "shadow": {"rgba": [{"color": "ff000000"}, {"time": 0.3, "color": "ff0e0e94"}, {"time": 0.4333, "color": "ffffff94"}, {"time": 0.8333, "color": "ff00d700"}]}}, "bones": {"bone2": {"translate": [{"x": 87.76, "y": -77.51, "curve": "stepped"}, {"time": 0.3333, "x": 87.76, "y": -77.51}, {"time": 0.7333, "x": 107, "y": -75.68}]}, "bone3": {"rotate": [{"time": 0.3}, {"time": 0.6667, "value": -8.3}], "translate": [{"x": -30.84, "y": 12.07}, {"time": 0.2667, "x": -42.01, "y": 16.98}, {"time": 0.3, "x": -126.96, "y": 12.09}], "scale": [{}, {"time": 0.2667, "x": 0.344, "y": 0.909}, {"time": 0.6667, "x": 0.917, "y": 0.909}]}, "bone6": {"rotate": [{"time": 0.2333}, {"time": 0.5333, "value": 12.55}, {"time": 0.5667}, {"time": 0.8667, "value": -77.85}, {"time": 1.1667, "value": -62.18}, {"time": 1.6333, "value": -78.65}, {"time": 2.2333, "value": -85.14}], "translate": [{"time": 0.2333, "x": 80.16, "y": -25.45}, {"time": 0.5333, "x": 56.35, "y": -30.74}, {"time": 0.5667}, {"time": 0.6667, "x": -126.96, "y": 12.09}, {"time": 0.8667, "x": -113.85, "y": 83.89, "curve": "stepped"}, {"time": 1.6333, "x": -113.85, "y": 83.89}, {"time": 1.9667, "x": -77.99, "y": 218.02}], "scale": [{"time": 0.2333}, {"time": 0.5333, "x": 0.344, "y": 0.909}, {"time": 0.7667, "x": 0.917, "y": 0.909}, {"time": 0.8667}, {"time": 1.1667, "x": 0.917, "y": 0.909, "curve": "stepped"}, {"time": 1.6333, "x": 0.917, "y": 0.909}, {"time": 2.2333, "x": 0.086, "y": 0.909}]}, "bone7": {"rotate": [{"time": 0.2333}, {"time": 0.5333, "value": -1.32}, {"time": 0.8667, "value": -57.23, "curve": "stepped"}, {"time": 1.9667, "value": -57.23}, {"time": 2.2333, "value": -91.64}, {"time": 2.3333, "value": -89.4}, {"time": 2.5667, "value": -84.32}], "translate": [{"time": 0.2333, "x": 87.76, "y": -77.51, "curve": "stepped"}, {"time": 0.6, "x": 87.76, "y": -77.51}, {"time": 0.8667, "x": 257.03, "y": -101.48, "curve": "stepped"}, {"time": 1.6333, "x": 257.03, "y": -101.48}, {"time": 1.9667, "x": 198.82, "y": -55.47}, {"time": 2.6667, "x": 195.32, "y": -129.65}]}, "bone5": {"rotate": [{"value": 35.15, "curve": "stepped"}, {"time": 0.8667, "value": -53.32, "curve": "stepped"}, {"time": 1.6333, "value": 15.45}], "translate": [{"x": 113.29, "y": 14.29, "curve": "stepped"}, {"time": 0.8667, "x": 70.23, "y": 60.85}]}, "bone8": {"scale": [{"time": 1.9667, "x": 1.786, "y": 1.786}, {"time": 2.6667, "x": 6.158, "y": 1.597}]}, "bone9": {"scale": [{"time": 1.9667, "x": 0.574, "y": 0.574}]}, "bone11": {"translate": [{"time": 0.0667, "x": 11.3, "y": -0.36}, {"time": 0.2, "x": 6.2, "y": 1.88}, {"time": 0.3, "x": 6.2, "y": -7.74}], "scale": [{}, {"time": 0.2, "x": 1.084, "y": 0.436}]}, "bone14": {"translate": [{"time": 1.3333, "x": 11.3, "y": -0.36}, {"time": 1.4667, "x": 6.2, "y": 1.88}, {"time": 1.5667, "x": 6.2, "y": -7.74}], "scale": [{"time": 1.2667}, {"time": 1.4667, "x": 1.084, "y": 0.436}]}, "bone13": {"rotate": [{"time": 1.5667}, {"time": 1.9333, "value": -8.3}], "translate": [{"time": 1.2667, "x": -30.84, "y": 12.07}, {"time": 1.5333, "x": -42.01, "y": 16.98}, {"time": 1.5667, "x": -126.96, "y": 12.09}], "scale": [{"time": 1.2667}, {"time": 1.5333, "x": 0.344, "y": 0.909}, {"time": 1.9333, "x": 0.917, "y": 0.909}]}, "bone15": {"translate": [{"time": 1.2667, "x": 87.76, "y": -77.51, "curve": "stepped"}, {"time": 1.6, "x": 87.76, "y": -77.51}, {"time": 2, "x": 107, "y": -75.68}]}, "glow_big": {"translate": [{"time": 0.0667, "x": -6.26, "y": 179.85}], "scale": [{"time": 0.0667, "x": -20.83, "y": -20.83}]}, "target5": {"translate": [{"x": -7.74, "y": -21.01, "curve": [0.2, -7.74, 0.409, -4.66, 0.2, -21.01, 0.4, -17.27]}, {"time": 0.6, "x": -1.23, "y": -17.27, "curve": [0.666, -0.06, 0.711, 0.37, 0.656, -17.27, 0.711, -17.48]}, {"time": 0.7667, "x": 0.37, "y": -17.82, "curve": [0.922, 0.37, 1.079, -4.44, 0.922, -18.76, 1.073, -19.57]}, {"time": 1.2333, "x": -7.05, "y": -20.51, "curve": [1.265, -7.59, 1.3, -7.74, 1.266, -20.7, 1.3, -21.01]}, {"time": 1.3333, "x": -7.74, "y": -21.01, "curve": [1.533, -7.74, 1.742, -4.66, 1.533, -21.01, 1.733, -17.27]}, {"time": 1.9333, "x": -1.23, "y": -17.27, "curve": [1.999, -0.06, 2.044, 0.37, 1.989, -17.27, 2.044, -17.48]}, {"time": 2.1, "x": 0.37, "y": -17.82, "curve": [2.256, 0.37, 2.413, -4.44, 2.256, -18.76, 2.407, -19.57]}, {"time": 2.5667, "x": -7.05, "y": -20.51, "curve": [2.598, -7.59, 2.633, -7.74, 2.6, -20.7, 2.633, -21.01]}, {"time": 2.6667, "x": -7.74, "y": -21.01}]}, "all2": {"translate": [{"curve": [0.11, 0, 0.229, 3.25, 0.11, -2.98, 0.223, -6.28]}, {"time": 0.3333, "x": 8.34, "y": -6.28, "curve": [0.437, 13.42, 0.556, 17.91, 0.444, -6.28, 0.556, -5.1]}, {"time": 0.6667, "x": 17.91, "y": -3.73, "curve": [0.802, 17.91, 0.931, 13.92, 0.802, -2.05, 0.931, 3.75]}, {"time": 1.0667, "x": 6.93, "y": 3.75, "curve": [1.138, 3.24, 1.244, 0, 1.143, 3.75, 1.244, 2.4]}, {"time": 1.3333, "curve": [1.444, 0, 1.563, 3.25, 1.444, -2.98, 1.556, -6.28]}, {"time": 1.6667, "x": 8.34, "y": -6.28, "curve": [1.771, 13.42, 1.89, 17.91, 1.777, -6.28, 1.89, -5.1]}, {"time": 2, "x": 17.91, "y": -3.73, "curve": [2.136, 17.91, 2.264, 13.92, 2.136, -2.05, 2.264, 3.75]}, {"time": 2.4, "x": 6.93, "y": 3.75, "curve": [2.472, 3.24, 2.587, 0.91, 2.476, 3.75, 2.592, 1.48]}, {"time": 2.6667}]}, "target4": {"translate": [{"curve": [0.108, -3.87, 0.24, -13.49, 0.127, 0, 0.264, -4.79]}, {"time": 0.3667, "x": -14.51, "y": -8.95, "curve": [0.477, -15.39, 0.611, -13.44, 0.454, -12.5, 0.59, -21.27]}, {"time": 0.7, "x": -11.23, "y": -21.27, "curve": [0.819, -8.26, 0.948, 3.44, 0.819, -21.27, 0.984, -16.05]}, {"time": 1.0667, "x": 3.44, "y": -11.49, "curve": [1.151, 3.44, 1.244, 3.2, 1.128, -8.12, 1.244, 0]}, {"time": 1.3333, "curve": [1.441, -3.87, 1.573, -13.49, 1.46, 0, 1.598, -4.79]}, {"time": 1.7, "x": -14.51, "y": -8.95, "curve": [1.81, -15.39, 1.945, -13.44, 1.787, -12.5, 1.923, -21.27]}, {"time": 2.0333, "x": -11.23, "y": -21.27, "curve": [2.152, -8.26, 2.281, 3.44, 2.152, -21.27, 2.317, -16.05]}, {"time": 2.4, "x": 3.44, "y": -11.49, "curve": [2.485, 3.44, 2.594, 1.93, 2.461, -8.12, 2.582, 0]}, {"time": 2.6667}]}, "Asset 8": {"rotate": [{"curve": [0.042, 1.62, 0.107, 6.98]}, {"time": 0.1667, "value": 6.98, "curve": [0.243, 6.98, 0.324, -3.93]}, {"time": 0.4, "value": -3.93, "curve": [0.485, -3.93, 0.6, -2.42]}, {"time": 0.6667, "curve": [0.718, 1.83, 0.774, 6.98]}, {"time": 0.8333, "value": 6.98, "curve": [0.91, 6.98, 1.024, -3.93]}, {"time": 1.1, "value": -3.93, "curve": [1.185, -3.93, 1.256, -2.98]}, {"time": 1.3333, "curve": [1.376, 1.62, 1.441, 6.98]}, {"time": 1.5, "value": 6.98, "curve": [1.576, 6.98, 1.657, -3.93]}, {"time": 1.7333, "value": -3.93, "curve": [1.818, -3.93, 1.933, -2.42]}, {"time": 2, "curve": [2.051, 1.83, 2.107, 6.98]}, {"time": 2.1667, "value": 6.98, "curve": [2.243, 6.98, 2.357, -3.93]}, {"time": 2.4333, "value": -3.93, "curve": [2.518, -3.93, 2.613, -2.05]}, {"time": 2.6667}]}, "Asset 9": {"rotate": [{"value": -2.96, "curve": [0.034, -1.45, 0.074, 2.18]}, {"time": 0.1, "value": 3.94, "curve": [0.149, 7.23, 0.207, 10.27]}, {"time": 0.2667, "value": 10.27, "curve": [0.343, 10.27, 0.424, -7.03]}, {"time": 0.5, "value": -7.03, "curve": [0.551, -7.03, 0.616, -5.68]}, {"time": 0.6667, "value": -4.27, "curve": [0.701, -3.34, 0.733, -1.6]}, {"time": 0.7667, "curve": [0.826, 2.81, 0.874, 10.27]}, {"time": 0.9333, "value": 10.27, "curve": [1.01, 10.27, 1.09, -7.03]}, {"time": 1.1667, "value": -7.03, "curve": [1.218, -7.03, 1.278, -5.43]}, {"time": 1.3333, "value": -2.96, "curve": [1.367, -1.45, 1.407, 2.18]}, {"time": 1.4333, "value": 3.94, "curve": [1.483, 7.23, 1.541, 10.27]}, {"time": 1.6, "value": 10.27, "curve": [1.676, 10.27, 1.757, -7.03]}, {"time": 1.8333, "value": -7.03, "curve": [1.884, -7.03, 1.949, -5.68]}, {"time": 2, "value": -4.27, "curve": [2.034, -3.34, 2.066, -1.6]}, {"time": 2.1, "curve": [2.159, 2.81, 2.207, 10.27]}, {"time": 2.2667, "value": 10.27, "curve": [2.343, 10.27, 2.424, -7.03]}, {"time": 2.5, "value": -7.03, "curve": [2.551, -7.03, 2.62, -5.05]}, {"time": 2.6667, "value": -2.96}]}, "Asset 10": {"rotate": [{"value": -5.47, "curve": [0.073, -2.58, 0.127, -0.86]}, {"time": 0.1667, "value": 1.8, "curve": [0.217, 5.12, 0.274, 10.52]}, {"time": 0.3333, "value": 10.52, "curve": [0.418, 10.52, 0.515, -7.23]}, {"time": 0.6, "value": -7.23, "curve": [0.625, -7.23, 0.641, -7.15]}, {"time": 0.6667, "value": -6.35, "curve": [0.718, -4.74, 0.749, -2.38]}, {"time": 0.8, "curve": [0.859, 2.78, 0.941, 10.52]}, {"time": 1, "value": 10.52, "curve": [1.085, 10.52, 1.149, -7.23]}, {"time": 1.2333, "value": -7.23, "curve": [1.259, -7.23, 1.3, -6.78]}, {"time": 1.3333, "value": -5.47, "curve": [1.407, -2.58, 1.46, -0.86]}, {"time": 1.5, "value": 1.8, "curve": [1.55, 5.12, 1.607, 10.52]}, {"time": 1.6667, "value": 10.52, "curve": [1.751, 10.52, 1.849, -7.23]}, {"time": 1.9333, "value": -7.23, "curve": [1.959, -7.23, 1.975, -7.15]}, {"time": 2, "value": -6.35, "curve": [2.051, -4.74, 2.082, -2.38]}, {"time": 2.1333, "curve": [2.193, 2.78, 2.274, 10.52]}, {"time": 2.3333, "value": 10.52, "curve": [2.418, 10.52, 2.482, -7.23]}, {"time": 2.5667, "value": -7.23, "curve": [2.592, -7.23, 2.647, -6.57]}, {"time": 2.6667, "value": -5.47}]}, "Asset 11": {"rotate": [{"value": -3.23, "curve": [0.051, -2.3, 0.116, -0.65]}, {"time": 0.1667, "value": 0.62, "curve": [0.285, 3.59, 0.381, 10.52]}, {"time": 0.5, "value": 10.52, "curve": [0.661, 10.52, 0.839, -6.98]}, {"time": 1, "value": -6.98, "curve": [1.11, -6.98, 1.222, -5.26]}, {"time": 1.3333, "value": -3.23, "curve": [1.384, -2.3, 1.449, -0.65]}, {"time": 1.5, "value": 0.62, "curve": [1.619, 3.59, 1.715, 10.52]}, {"time": 1.8333, "value": 10.52, "curve": [1.994, 10.52, 2.172, -6.98]}, {"time": 2.3333, "value": -6.98, "curve": [2.444, -6.98, 2.554, -5.08]}, {"time": 2.6667, "value": -3.23}]}, "Asset 7a": {"rotate": [{"curve": [0.079, -8.13, 0.156, -8.25]}, {"time": 0.2333, "value": -8.25, "curve": [0.311, -8.25, 0.389, 5.9]}, {"time": 0.4667, "value": 5.9, "curve": [0.533, 5.9, 0.61, 5.9]}, {"time": 0.6667, "curve": [0.745, -8.13, 0.822, -8.25]}, {"time": 0.9, "value": -8.25, "curve": [0.978, -8.25, 1.056, 5.9]}, {"time": 1.1333, "value": 5.9, "curve": [1.2, 5.9, 1.276, 5.9]}, {"time": 1.3333, "curve": [1.412, -8.13, 1.489, -8.25]}, {"time": 1.5667, "value": -8.25, "curve": [1.644, -8.25, 1.722, 5.9]}, {"time": 1.8, "value": 5.9, "curve": [1.867, 5.9, 1.943, 5.9]}, {"time": 2, "curve": [2.079, -8.13, 2.156, -8.25]}, {"time": 2.2333, "value": -8.25, "curve": [2.311, -8.25, 2.389, 5.9]}, {"time": 2.4667, "value": 5.9, "curve": [2.533, 5.9, 2.609, 5.11]}, {"time": 2.6667}]}, "Asset 7b": {"rotate": [{"value": -3.67, "curve": [0.056, -3.67, 0.111, -8.25]}, {"time": 0.1667, "value": -8.25, "curve": [0.233, -8.25, 0.3, 5.9]}, {"time": 0.3667, "value": 5.9, "curve": [0.467, 5.9, 0.567, -0.41]}, {"time": 0.6667, "value": -3.67, "curve": [0.711, -5.12, 0.756, -8.25]}, {"time": 0.8, "value": -8.25, "curve": [0.878, -8.25, 0.956, 5.9]}, {"time": 1.0333, "value": 5.9, "curve": [1.133, 5.9, 1.233, -0.64]}, {"time": 1.3333, "value": -3.67, "curve": [1.389, -5.35, 1.444, -8.25]}, {"time": 1.5, "value": -8.25, "curve": [1.567, -8.25, 1.633, 5.9]}, {"time": 1.7, "value": 5.9, "curve": [1.8, 5.9, 1.9, -0.41]}, {"time": 2, "value": -3.67, "curve": [2.044, -5.12, 2.089, -8.25]}, {"time": 2.1333, "value": -8.25, "curve": [2.211, -8.25, 2.289, 5.9]}, {"time": 2.3667, "value": 5.9, "curve": [2.467, 5.9, 2.571, -3.67]}, {"time": 2.6667, "value": -3.67}]}, "Asset 12": {"rotate": [{"value": 3.54, "curve": [0.122, 3.54, 0.244, -8.25]}, {"time": 0.3667, "value": -8.25, "curve": [0.444, -8.25, 0.522, 5.9]}, {"time": 0.6, "value": 5.9, "curve": [0.622, 5.9, 0.644, 4.32]}, {"time": 0.6667, "value": 3.54, "curve": [0.778, -0.39, 0.889, -8.25]}, {"time": 1, "value": -8.25, "curve": [1.078, -8.25, 1.156, 5.9]}, {"time": 1.2333, "value": 5.9, "curve": [1.267, 5.9, 1.3, 4.55]}, {"time": 1.3333, "value": 3.54, "curve": [1.456, -0.17, 1.578, -8.25]}, {"time": 1.7, "value": -8.25, "curve": [1.778, -8.25, 1.856, 5.9]}, {"time": 1.9333, "value": 5.9, "curve": [1.956, 5.9, 1.978, 4.32]}, {"time": 2, "value": 3.54, "curve": [2.111, -0.39, 2.222, -8.25]}, {"time": 2.3333, "value": -8.25, "curve": [2.411, -8.25, 2.489, 5.9]}, {"time": 2.5667, "value": 5.9, "curve": [2.6, 5.9, 2.642, 3.54]}, {"time": 2.6667, "value": 3.54}]}, "Asset 13": {"rotate": [{"value": 4.32, "curve": [0.006, 5.72, 0.022, 5.9]}, {"time": 0.0333, "value": 5.9, "curve": [0.178, 5.9, 0.322, -8.25]}, {"time": 0.4667, "value": -8.25, "curve": [0.533, -8.25, 0.608, -8.25]}, {"time": 0.6667, "value": 4.32, "curve": [0.673, 5.72, 0.689, 5.9]}, {"time": 0.7, "value": 5.9, "curve": [0.844, 5.9, 0.989, -8.25]}, {"time": 1.1333, "value": -8.25, "curve": [1.2, -8.25, 1.275, -8.25]}, {"time": 1.3333, "value": 4.32, "curve": [1.34, 5.72, 1.356, 5.9]}, {"time": 1.3667, "value": 5.9, "curve": [1.511, 5.9, 1.656, -8.25]}, {"time": 1.8, "value": -8.25, "curve": [1.867, -8.25, 1.942, -8.25]}, {"time": 2, "value": 4.32, "curve": [2.006, 5.72, 2.022, 5.9]}, {"time": 2.0333, "value": 5.9, "curve": [2.178, 5.9, 2.322, -8.25]}, {"time": 2.4667, "value": -8.25, "curve": [2.533, -8.25, 2.602, -1.8]}, {"time": 2.6667, "value": 4.32}]}, "target10": {"scale": [{"time": 0.1, "x": 0.636, "y": 0.636}]}, "01": {"scale": [{"time": 0.2}, {"time": 0.3667, "x": 1.273}, {"time": 1.5333}, {"time": 1.7, "x": 1.273}]}, "03": {"scale": [{"time": 0.5}, {"time": 0.5667, "x": 1.506}]}, "07": {"scale": [{"time": 1.0333}, {"time": 1.2, "x": 1.527}]}, "shadow": {"translate": [{}, {"time": 0.9333}], "scale": [{}, {"time": 0.9333, "x": 1.095, "y": 1.095}]}}}}}