using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TreeLayerAdjuster : MonoBehaviour {
    [SerializeField] private SpriteRenderer backGround;
    [SerializeField] private SpriteRenderer frontGround;

    private static int decreaseValue = 1;
    
    private int _defaultBack;
    private int _defaultFront;
    private void Start() {
        _defaultBack = backGround.sortingOrder;
        _defaultFront = frontGround.sortingOrder;
    }

    private void OnCollected() {
        backGround.sortingOrder -= decreaseValue;
        frontGround.sortingOrder -= decreaseValue;

        decreaseValue++;
        if (decreaseValue == int.MaxValue) {
            decreaseValue = 1;
        }
        
        if (backGround.sortingOrder == int.MinValue) {
            backGround.sortingOrder = _defaultBack;
        }

        if (frontGround.sortingOrder == int.MinValue) {
            frontGround.sortingOrder = _defaultFront;
        }
    }

    private void OnTriggerEnter(Collider other) {
        if (other.CompareTag("Collector")) {
            OnCollected();
        }
    }
}
