{"skeleton": {"hash": "URDqLgzb6ec", "spine": "4.0.64", "x": -143.21, "y": -3.98, "width": 280.03, "height": 392.33, "fps": 60, "images": "./images/", "audio": "D:/WORK/TITTLE HOP/CHISTMAST THEME/Animation"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 0.17, "y": 12.5}, {"name": "Christmas tree 3", "parent": "bone", "length": 101.48, "rotation": 89.75, "x": 0.39, "y": 46.86}, {"name": "Christmas tree 2", "parent": "Christmas tree 3", "length": 84.93, "rotation": 0.83, "x": 101.48, "y": 0.44}, {"name": "Christmas tree 1", "parent": "Christmas tree 2", "x": 86.25, "y": 0.42}, {"name": "mouth", "parent": "Christmas tree 1", "x": -11.37, "y": 1.93}, {"name": "eye1", "parent": "Christmas tree 1", "x": 14.81, "y": -22.67}, {"name": "eye2", "parent": "Christmas tree 1", "x": 15.49, "y": 24.66}, {"name": "left hand", "parent": "Christmas tree 2", "length": 45.29, "rotation": -66.41, "x": 53.56, "y": -52.03}, {"name": "left hand2", "parent": "left hand", "length": 42.12, "rotation": 17.17, "x": 45.29}, {"name": "right hand", "parent": "Christmas tree 2", "length": 47.35, "rotation": 64.67, "x": 52.18, "y": 60.14}, {"name": "right hand2", "parent": "right hand", "length": 44.47, "rotation": -17.57, "x": 47.35}, {"name": "left foot", "parent": "Christmas tree 3", "length": 53.52, "rotation": -140.57, "x": 40.35, "y": -27.08}, {"name": "left foot2", "parent": "left foot", "length": 43.52, "rotation": -5.17, "x": 53.52}, {"name": "right foot", "parent": "Christmas tree 3", "length": 50.6, "rotation": 176.67, "x": 42.59, "y": 40.55}, {"name": "right foot2", "parent": "right foot", "length": 46.44, "rotation": 3.58, "x": 50.6}, {"name": "ball2", "parent": "Christmas tree 2", "length": 22.75, "rotation": 179.97, "x": 7.58, "y": 40.07}, {"name": "ball 3", "parent": "Christmas tree 2", "length": 26.51, "rotation": -179.16, "x": 22.51, "y": -54.39}, {"name": "ball1", "parent": "Christmas tree 3", "length": 22.43, "rotation": 179.7, "x": 31.83, "y": -29.83}, {"name": "chan t", "parent": "bone", "x": -43.13, "y": -7.32, "color": "ff3f00ff"}, {"name": "chan p", "parent": "bone", "x": 32.06, "y": -9.6, "color": "ff3f00ff"}, {"name": "right foot3", "parent": "right foot2", "length": 23.66, "rotation": -78.8, "x": 44.57, "y": -0.79}, {"name": "left foot3", "parent": "left foot2", "length": 20.02, "rotation": 88.64, "x": 41.87, "y": 0.71}], "slots": [{"name": "right foot", "bone": "right foot", "attachment": "right foot"}, {"name": "left foot", "bone": "left foot", "attachment": "left foot"}, {"name": "right hand", "bone": "right hand", "attachment": "right hand"}, {"name": "left hand", "bone": "left hand", "attachment": "left hand"}, {"name": "Christmas tree 3", "bone": "Christmas tree 3", "attachment": "Christmas tree 3"}, {"name": "Christmas tree 2", "bone": "Christmas tree 2", "attachment": "Christmas tree 2"}, {"name": "Christmas tree 1", "bone": "Christmas tree 1", "attachment": "Christmas tree 1"}, {"name": "eye2", "bone": "eye2", "attachment": "eye2"}, {"name": "eye1", "bone": "eye1", "attachment": "eye1"}, {"name": "mouth", "bone": "mouth", "attachment": "mouth"}, {"name": "star", "bone": "Christmas tree 1", "attachment": "star"}, {"name": "ball 3", "bone": "ball 3", "attachment": "ball 3"}, {"name": "ball2", "bone": "ball2", "attachment": "ball2"}, {"name": "ball1", "bone": "ball1", "attachment": "ball1"}], "ik": [{"name": "chan p", "order": 1, "bones": ["left foot", "left foot2"], "target": "chan p", "bendPositive": false}, {"name": "chan t", "bones": ["right foot", "right foot2"], "target": "chan t"}], "skins": [{"name": "default", "attachments": {"ball1": {"ball1": {"x": 11.2, "y": -0.1, "rotation": 90.55, "width": 21, "height": 25}}, "ball2": {"ball2": {"x": 11.15, "y": 0.3, "rotation": 89.44, "width": 24, "height": 27}}, "ball 3": {"ball 3": {"x": 14.05, "y": -0.25, "rotation": 88.57, "width": 30, "height": 33}}, "Christmas tree 1": {"Christmas tree 1": {"x": 34.3, "y": 1.58, "rotation": -90.59, "width": 165, "height": 146}}, "Christmas tree 2": {"Christmas tree 2": {"x": 30.05, "y": 2.43, "rotation": -90.59, "width": 212, "height": 119}}, "Christmas tree 3": {"Christmas tree 3": {"x": 57.5, "y": 0.49, "rotation": -89.75, "width": 273, "height": 139}}, "eye1": {"eye1": {"x": 0.24, "y": -0.05, "rotation": -90.59, "width": 30, "height": 30}}, "eye2": {"eye2": {"x": 0.04, "y": -0.39, "rotation": -90.59, "width": 30, "height": 30}}, "left foot": {"left foot": {"type": "mesh", "uvs": [0.14498, 0.00824, 0.21752, 0.08882, 0.29164, 0.17115, 0.36575, 0.25347, 0.43659, 0.33215, 0.51107, 0.42118, 0.59973, 0.52716, 0.68921, 0.63413, 0.73121, 0.68434, 0.77933, 0.75528, 0.95414, 0.75529, 0.99127, 0.7914, 0.98765, 0.85267, 0.77479, 0.98789, 0.71823, 0.99296, 0.64115, 0.8886, 0.53517, 0.74511, 0.44978, 0.64354, 0.36235, 0.53955, 0.28577, 0.44847, 0.21806, 0.36793, 0.14321, 0.27891, 0.07694, 0.20009, 0, 0.10858, 0, 0.09937, 0.09781, 0.00547], "triangles": [13, 14, 9, 12, 13, 9, 12, 9, 10, 12, 10, 11, 17, 18, 6, 16, 17, 6, 7, 16, 6, 8, 15, 16, 8, 16, 7, 15, 8, 9, 9, 14, 15, 23, 24, 25, 22, 23, 25, 1, 22, 25, 1, 25, 0, 21, 22, 1, 21, 1, 2, 20, 21, 2, 20, 2, 3, 19, 20, 3, 19, 3, 4, 19, 4, 5, 18, 19, 5, 18, 5, 6], "vertices": [2, 12, 0.11, 6.72, 0.99802, 13, -53.8, 1.89, 0.00198, 2, 12, 9.68, 6.98, 0.99073, 13, -44.29, 3, 0.00927, 2, 12, 19.45, 7.24, 0.96391, 13, -34.59, 4.14, 0.03609, 2, 12, 29.22, 7.5, 0.89559, 13, -24.88, 5.28, 0.10441, 2, 12, 38.56, 7.74, 0.76675, 13, -15.6, 6.36, 0.23325, 2, 12, 48.81, 7.64, 0.58125, 13, -5.38, 7.19, 0.41875, 2, 12, 61.02, 7.53, 0.37646, 13, 6.79, 8.17, 0.62354, 2, 12, 73.35, 7.41, 0.20308, 13, 19.08, 9.16, 0.79692, 2, 12, 79.13, 7.35, 0.09731, 13, 24.85, 9.63, 0.90269, 3, 12, 86.7, 6.53, 0.02875, 13, 32.45, 9.49, 0.73411, 22, 8.62, 9.65, 0.23714, 1, 22, 21.23, 1.52, 1, 1, 22, 22.15, -2.95, 1, 1, 22, 18.91, -7.42, 1, 1, 22, -3.05, -7.79, 1, 3, 12, 99.96, -11.06, 0.01194, 13, 47.24, -6.83, 0.43949, 22, -7.35, -5.56, 0.54857, 2, 12, 88.49, -10.27, 0.07052, 13, 35.75, -7.07, 0.92948, 2, 12, 72.72, -9.17, 0.1972, 13, 19.95, -7.4, 0.8028, 2, 12, 60.99, -9.09, 0.37511, 13, 8.26, -8.38, 0.62489, 2, 12, 48.99, -9, 0.58107, 13, -3.7, -9.37, 0.41893, 2, 12, 38.47, -8.93, 0.76653, 13, -14.18, -10.24, 0.23347, 2, 12, 29.18, -8.86, 0.89559, 13, -23.45, -11.02, 0.10441, 2, 12, 18.9, -8.79, 0.96379, 13, -33.69, -11.87, 0.03621, 2, 12, 9.8, -8.72, 0.99072, 13, -42.76, -12.62, 0.00928, 2, 12, -0.76, -8.65, 0.99794, 13, -53.29, -13.5, 0.00206, 2, 12, -1.41, -8.12, 0.99969, 13, -53.97, -13.04, 0.00031, 2, 12, -2.64, 3.74, 0.9997, 13, -56.27, -1.34, 0.0003], "hull": 26, "edges": [0, 50, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 46, 48, 48, 50, 8, 38, 8, 10, 36, 38, 10, 36, 14, 16, 32, 14, 28, 30, 30, 32, 18, 30, 18, 28, 32, 34, 34, 36, 10, 12, 12, 14, 34, 12, 38, 40, 6, 8, 40, 6, 40, 42, 4, 6, 42, 4, 42, 44, 44, 46, 0, 2, 2, 4, 44, 2], "width": 86, "height": 90}}, "left hand": {"left hand": {"type": "mesh", "uvs": [0.94988, 0.0146, 1, 0.18038, 1, 0.20479, 0.94903, 0.36941, 0.83111, 0.36947, 0.74236, 0.49678, 0.67018, 0.57371, 0.61764, 0.6297, 0.53875, 0.71378, 0.47372, 0.78309, 0.37077, 0.85419, 0.26816, 0.92504, 0.17582, 0.96654, 0.10136, 1, 0.0674, 1, 0.01112, 0.92424, 0.01089, 0.80057, 0.07123, 0.77197, 0.12034, 0.75135, 0.20115, 0.71742, 0.28861, 0.65801, 0.36944, 0.58912, 0.43364, 0.52662, 0.51115, 0.45116, 0.57608, 0.38794, 0.63409, 0.33147, 0.70767, 0.23055, 0.72536, 0.08577, 0.79394, 0.01486], "triangles": [1, 28, 0, 26, 28, 4, 2, 3, 1, 26, 27, 28, 4, 28, 1, 3, 4, 1, 5, 26, 4, 25, 26, 5, 6, 25, 5, 24, 25, 6, 7, 23, 24, 6, 7, 24, 8, 23, 7, 22, 23, 8, 9, 22, 8, 21, 22, 9, 10, 21, 9, 20, 21, 10, 15, 16, 17, 11, 19, 20, 11, 20, 10, 12, 18, 19, 12, 19, 11, 13, 14, 15, 17, 18, 12, 17, 13, 15, 12, 13, 17], "vertices": [2, 8, 95.9, 19.94, 0.00158, 9, 54.24, 4.12, 0.99842, 2, 8, 95.56, 8.29, 0.00154, 9, 50.47, -6.92, 0.99846, 2, 8, 94.91, 6.84, 0.00538, 9, 49.42, -8.11, 0.99462, 2, 8, 86.39, -1.06, 0.01728, 9, 38.95, -13.15, 0.98272, 2, 8, 76.81, 3.23, 0.04545, 9, 31.07, -6.22, 0.95455, 2, 8, 66.22, -1.08, 0.10975, 9, 19.67, -7.21, 0.89025, 2, 8, 58.31, -3.01, 0.22175, 9, 11.55, -6.72, 0.77825, 2, 8, 52.55, -4.42, 0.37897, 9, 5.63, -6.36, 0.62103, 2, 8, 43.91, -6.53, 0.55875, 9, -3.25, -5.83, 0.44125, 2, 8, 36.78, -8.27, 0.72719, 9, -10.57, -5.39, 0.27281, 2, 8, 26.53, -8.73, 0.8562, 9, -20.5, -2.8, 0.1438, 2, 8, 16.31, -9.19, 0.93673, 9, -30.4, -0.23, 0.06327, 2, 8, 7.71, -8.29, 0.97727, 9, -38.35, 3.18, 0.02273, 2, 8, 0.78, -7.56, 0.99347, 9, -44.76, 5.92, 0.00653, 2, 8, -1.98, -6.32, 0.99823, 9, -47.03, 7.92, 0.00177, 2, 8, -4.53, 0.23, 0.99931, 9, -47.54, 14.92, 0.00069, 2, 8, -1.26, 7.57, 0.99823, 9, -42.25, 20.97, 0.00177, 2, 8, 4.4, 7.06, 0.99346, 9, -36.99, 18.82, 0.00654, 2, 8, 8.94, 6.5, 0.97728, 9, -32.82, 16.94, 0.02272, 2, 8, 16.4, 5.56, 0.93672, 9, -25.96, 13.84, 0.06328, 2, 8, 25.08, 5.9, 0.85623, 9, -17.57, 11.6, 0.14377, 2, 8, 33.48, 7.04, 0.72717, 9, -9.21, 10.21, 0.27283, 2, 8, 40.36, 8.4, 0.55879, 9, -2.24, 9.48, 0.44121, 2, 8, 48.66, 10.05, 0.37895, 9, 6.18, 8.61, 0.62105, 2, 8, 55.61, 11.43, 0.22181, 9, 13.24, 7.88, 0.77819, 2, 8, 61.83, 12.67, 0.10974, 9, 19.54, 7.22, 0.89026, 2, 8, 70.49, 15.97, 0.04551, 9, 28.79, 7.82, 0.95449, 2, 8, 75.78, 23.91, 0.01738, 9, 36.18, 13.84, 0.98262, 2, 8, 83.24, 25.61, 0.00549, 9, 43.81, 13.27, 0.99451], "hull": 29, "edges": [0, 56, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 26, 28, 28, 30, 30, 32, 32, 34, 50, 52, 52, 54, 54, 56, 18, 42, 18, 20, 20, 22, 40, 20, 38, 22, 34, 36, 36, 38, 22, 24, 24, 26, 36, 24, 26, 34, 42, 44, 16, 18, 44, 16, 44, 46, 14, 16, 46, 14, 46, 48, 48, 50, 10, 12, 12, 14, 48, 12, 10, 50, 8, 52, 38, 40, 40, 42], "width": 89, "height": 65}}, "mouth": {"mouth": {"x": 2.66, "y": -1.41, "rotation": -90.59, "width": 34, "height": 12}}, "right foot": {"right foot": {"type": "mesh", "uvs": [0.71348, 0.0071, 0.93234, 0.01048, 0.97798, 0.02852, 0.95777, 0.12257, 0.93131, 0.2457, 0.90737, 0.35708, 0.87916, 0.46974, 0.87931, 0.55995, 0.87945, 0.64915, 0.8796, 0.74038, 0.87971, 0.80761, 0.93578, 0.95825, 0.73111, 0.99067, 0.43639, 0.99087, 0.11056, 0.99109, 0.02911, 0.97652, 0.02985, 0.91051, 0.3901, 0.83835, 0.4041, 0.73665, 0.4102, 0.64548, 0.41601, 0.55865, 0.42202, 0.46866, 0.44211, 0.35231, 0.46089, 0.24352, 0.48172, 0.12286, 0.50102, 0.01106], "triangles": [14, 15, 16, 13, 14, 16, 13, 17, 12, 16, 17, 13, 12, 17, 11, 20, 21, 7, 19, 20, 7, 8, 19, 7, 18, 19, 8, 18, 8, 9, 10, 17, 18, 10, 18, 9, 17, 10, 11, 2, 0, 1, 24, 25, 0, 3, 24, 0, 2, 3, 0, 4, 24, 3, 23, 24, 4, 5, 23, 4, 22, 23, 5, 6, 22, 5, 21, 22, 6, 21, 6, 7], "vertices": [2, 14, -2.42, -0.98, 0.99648, 15, -52.98, 2.33, 0.00352, 2, 14, -2.51, 6.25, 0.99645, 15, -52.62, 9.55, 0.00355, 2, 14, -0.68, 7.88, 0.98706, 15, -50.69, 11.06, 0.01294, 2, 14, 9.41, 7.84, 0.95942, 15, -40.62, 10.39, 0.04058, 2, 14, 22.61, 7.79, 0.88786, 15, -27.45, 9.52, 0.11214, 2, 14, 34.56, 7.74, 0.75535, 15, -15.53, 8.73, 0.24465, 2, 14, 46.64, 7.57, 0.56866, 15, -3.48, 7.8, 0.43134, 2, 14, 56.28, 8.17, 0.36497, 15, 6.18, 7.8, 0.63503, 2, 14, 65.8, 8.77, 0.19295, 15, 15.72, 7.81, 0.80705, 2, 14, 75.55, 9.39, 0.08061, 15, 25.48, 7.81, 0.91939, 2, 14, 82.73, 9.84, 0.02457, 15, 32.68, 7.82, 0.97543, 3, 14, 98.7, 12.69, 0.00294, 15, 48.79, 9.67, 0.34849, 21, -9.44, 6.18, 0.64857, 1, 21, -2.14, 8.27, 1, 1, 21, 7.41, 6.4, 1, 1, 21, 17.96, 4.33, 1, 1, 21, 20.29, 2.28, 1, 1, 21, 18.9, -4.64, 1, 3, 14, 87.02, -6.08, 0.01283, 15, 35.97, -8.34, 0.74431, 21, 5.73, -9.91, 0.24286, 2, 14, 76.13, -6.3, 0.07929, 15, 25.08, -7.88, 0.92071, 2, 14, 66.38, -6.71, 0.19271, 15, 15.33, -7.68, 0.80729, 2, 14, 57.09, -7.09, 0.36504, 15, 6.04, -7.48, 0.63496, 2, 14, 47.47, -7.5, 0.56862, 15, -3.59, -7.29, 0.43138, 2, 14, 35, -7.61, 0.75544, 15, -16.04, -6.62, 0.24456, 2, 14, 23.35, -7.72, 0.88784, 15, -27.68, -6, 0.11216, 2, 14, 10.42, -7.84, 0.95948, 15, -40.59, -5.32, 0.04052, 2, 14, -1.56, -7.95, 0.9871, 15, -52.55, -4.68, 0.0129], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 10, 12, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 12, 42, 42, 44, 10, 44, 44, 46, 8, 10, 46, 8, 46, 48, 48, 50, 4, 6, 6, 8, 48, 6, 18, 20, 36, 18, 34, 20, 34, 22, 24, 26, 26, 28, 34, 26, 40, 42, 12, 14, 40, 14, 36, 38, 38, 40, 14, 16, 16, 18, 38, 16], "width": 33, "height": 107}}, "right hand": {"right hand": {"type": "mesh", "uvs": [0.27936, 0.12057, 0.29226, 0.23067, 0.32978, 0.28123, 0.36735, 0.33185, 0.46726, 0.43139, 0.56237, 0.52616, 0.65083, 0.61429, 0.77415, 0.70659, 0.83745, 0.73563, 0.92637, 0.77641, 0.96032, 0.79198, 0.98886, 0.82877, 0.98957, 0.92683, 0.93186, 1, 0.90043, 1, 0.86886, 0.98604, 0.77948, 0.94653, 0.66934, 0.89784, 0.55846, 0.79717, 0.45701, 0.70505, 0.35962, 0.61663, 0.27412, 0.5383, 0.19045, 0.46166, 0.12254, 0.39944, 0.0491, 0.33216, 0.01088, 0.25329, 0.01115, 0.12293, 0.09042, 0.01432, 0.22942, 0.01545], "triangles": [27, 25, 26, 24, 25, 27, 0, 24, 27, 0, 27, 28, 1, 23, 24, 1, 24, 0, 22, 23, 1, 22, 1, 2, 21, 22, 2, 21, 2, 3, 20, 21, 3, 4, 20, 3, 19, 20, 4, 19, 4, 5, 18, 19, 5, 18, 5, 6, 17, 6, 7, 18, 6, 17, 16, 7, 8, 17, 7, 16, 15, 8, 9, 16, 8, 15, 14, 15, 9, 10, 14, 9, 10, 12, 14, 12, 10, 11, 13, 14, 12], "vertices": [2, 10, 74.74, -20.74, 0.01555, 11, 32.37, -11.51, 0.98445, 2, 10, 70.7, -14.72, 0.04711, 11, 26.71, -6.99, 0.95289, 2, 10, 66.3, -13.14, 0.11533, 11, 22.02, -6.8, 0.88467, 2, 10, 61.88, -11.55, 0.23243, 11, 17.34, -6.62, 0.76757, 2, 10, 51.1, -9.39, 0.39453, 11, 6.41, -7.82, 0.60547, 2, 10, 40.83, -7.34, 0.57695, 11, -4, -8.97, 0.42305, 2, 10, 31.29, -5.43, 0.74424, 11, -13.68, -10.03, 0.25576, 2, 10, 18.81, -4.58, 0.86912, 11, -25.83, -12.98, 0.13088, 2, 10, 12.9, -5.22, 0.94451, 11, -31.27, -15.38, 0.05549, 2, 10, 4.6, -6.12, 0.9808, 11, -38.91, -18.74, 0.0192, 2, 10, 1.44, -6.47, 0.9939, 11, -41.82, -20.03, 0.0061, 2, 10, -1.87, -5.36, 0.99844, 11, -45.31, -19.97, 0.00156, 2, 10, -4.6, 0.4, 0.99943, 11, -49.65, -15.3, 0.00057, 2, 10, -1.92, 6.87, 0.99843, 11, -49.05, -8.32, 0.00157, 2, 10, 0.62, 8.04, 0.99389, 11, -46.98, -6.44, 0.00611, 2, 10, 3.55, 8.39, 0.98079, 11, -44.29, -5.22, 0.01921, 2, 10, 11.85, 9.39, 0.9445, 11, -36.68, -1.77, 0.0555, 2, 10, 22.08, 10.62, 0.86909, 11, -27.3, 2.49, 0.13091, 2, 10, 33.78, 8.81, 0.74423, 11, -15.6, 4.3, 0.25577, 2, 10, 44.48, 7.15, 0.57692, 11, -4.89, 5.95, 0.42308, 2, 10, 54.76, 5.55, 0.39452, 11, 5.39, 7.53, 0.60548, 2, 10, 63.8, 4.11, 0.2324, 11, 14.44, 8.89, 0.7676, 2, 10, 72.65, 2.71, 0.11532, 11, 23.3, 10.22, 0.88468, 2, 10, 79.83, 1.56, 0.04709, 11, 30.49, 11.29, 0.95291, 2, 10, 87.6, 0.33, 0.01554, 11, 38.27, 12.46, 0.98446, 2, 10, 92.83, -2.91, 0.00469, 11, 44.24, 10.96, 0.99531, 2, 10, 96.36, -10.61, 0.00127, 11, 49.92, 4.68, 0.99873, 2, 10, 92.9, -19.98, 0.00128, 11, 49.46, -5.29, 0.99872, 2, 10, 81.64, -25.09, 0.00472, 11, 40.26, -13.57, 0.99528], "hull": 29, "edges": [0, 56, 0, 2, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 48, 50, 50, 52, 52, 54, 54, 56, 6, 8, 40, 8, 34, 36, 12, 36, 36, 38, 38, 40, 8, 10, 10, 12, 38, 10, 14, 34, 14, 16, 32, 34, 16, 32, 16, 18, 18, 20, 28, 30, 30, 32, 18, 30, 40, 42, 42, 6, 42, 44, 2, 4, 4, 6, 44, 4, 44, 46, 46, 48, 2, 46, 48, 0], "width": 89, "height": 65}}, "star": {"star": {"x": 115.81, "y": 1.75, "rotation": -90.59, "width": 53, "height": 51}}}}], "animations": {"TreeDance_00": {"bones": {"Christmas tree 3": {"translate": [{"curve": [0.167, 0, 0.333, 0, 0.167, 0, 0.333, -5.35]}, {"time": 0.5, "y": -5.35, "curve": [0.667, 0, 0.833, 0, 0.667, -5.35, 0.833, 0]}, {"time": 1}]}, "right hand": {"rotate": [{"value": 66.2, "curve": [0.034, 65.54, 0.067, 65.09]}, {"time": 0.1, "value": 65.09, "curve": [0.267, 65.09, 0.433, 75.71]}, {"time": 0.6, "value": 75.71, "curve": [0.734, 75.71, 0.868, 68.89]}, {"time": 1, "value": 66.2}]}, "left hand": {"rotate": [{"value": -70.41, "curve": [0.034, -69.86, 0.067, -69.49]}, {"time": 0.1, "value": -69.49, "curve": [0.267, -69.49, 0.433, -78.41]}, {"time": 0.6, "value": -78.41, "curve": [0.734, -78.41, 0.868, -72.68]}, {"time": 1, "value": -70.41}]}, "right hand2": {"rotate": [{"value": 27.23}]}, "left hand2": {"rotate": [{"value": -25.99}]}, "eye2": {"scale": [{"time": 0.3333}, {"time": 0.4, "x": 0.1}, {"time": 0.4333}]}, "eye1": {"scale": [{"time": 0.3333}, {"time": 0.4, "x": 0.1}, {"time": 0.4333}]}, "ball2": {"rotate": [{"value": -12.54, "curve": [0.167, -12.54, 0.333, 16.35]}, {"time": 0.5, "value": 16.35, "curve": [0.667, 16.35, 0.833, -12.54]}, {"time": 1, "value": -12.54}]}, "ball 3": {"rotate": [{"value": -9.54, "curve": [0.034, -11.33, 0.067, -12.54]}, {"time": 0.1, "value": -12.54, "curve": [0.267, -12.54, 0.433, 16.35]}, {"time": 0.6, "value": 16.35, "curve": [0.734, 16.35, 0.868, -2.2]}, {"time": 1, "value": -9.54}]}, "ball1": {"rotate": [{"value": -4.99, "curve": [0.057, -9.21, 0.112, -12.54]}, {"time": 0.1667, "value": -12.54, "curve": [0.333, -12.54, 0.5, 16.35]}, {"time": 0.6667, "value": 16.35, "curve": [0.778, 16.35, 0.89, 3.57]}, {"time": 1, "value": -4.99}]}}}, "TreeDance_01": {"bones": {"Christmas tree 3": {"rotate": [{"curve": [0.083, 0, 0.144, -0.74]}, {"time": 0.25, "curve": [0.355, 0.73, 0.417, 8.06]}, {"time": 0.5, "value": 7.89, "curve": [0.695, 7.51, 0.751, -9.34]}, {"time": 1, "value": -9.45, "curve": [1.296, -9.58, 1.333, 6.33]}, {"time": 1.5, "value": 6.33, "curve": [1.605, 6.33, 1.667, 0]}, {"time": 1.75}], "translate": [{"curve": [0.083, 0, 0.167, 0, 0.083, 0, 0.167, -12.68]}, {"time": 0.25, "y": -12.68, "curve": [0.333, 0, 0.417, 0, 0.333, -12.68, 0.403, 5.63]}, {"time": 0.5, "y": 5.78, "curve": [0.583, 0, 0.667, 0, 0.627, 5.99, 0.667, -23.75]}, {"time": 0.75, "y": -23.75, "curve": [0.833, 0, 0.917, 14.14, 0.833, -23.75, 0.89, 3.55]}, {"time": 1, "x": 14.14, "y": 3.66, "curve": [1.083, 14.14, 1.167, 0, 1.124, 3.77, 1.167, -12.68]}, {"time": 1.25, "y": -12.68, "curve": [1.333, 0, 1.417, 0, 1.333, -12.68, 1.396, 5.35]}, {"time": 1.5, "y": 5.35, "curve": [1.583, 0, 1.667, 0, 1.637, 5.35, 1.667, -12.68]}, {"time": 1.75, "y": -12.68, "curve": [1.833, 0, 1.917, 0, 1.833, -12.68, 1.917, 0]}, {"time": 2}]}, "chan p": {"translate": [{"time": 0.25, "curve": [0.333, 40.73, 0.441, 48.08, 0.333, 29.2, 0.458, 66.81]}, {"time": 0.5, "x": 48.06, "y": 64.71, "curve": [0.619, 48.03, 0.667, 0, 0.592, 60.04, 0.667, 0]}, {"time": 0.75, "curve": [0.834, 0, 0.918, -18.83, 0.834, 0, 0.918, 18.44]}, {"time": 1, "x": -18.83, "y": 18.44, "curve": [1.084, -18.83, 1.167, 0, 1.084, 18.44, 1.167, 0]}, {"time": 1.25, "curve": [1.423, 0, 1.428, 52.54, 1.426, 0, 1.439, 44.88]}, {"time": 1.5, "x": 51.9, "y": 43.81, "curve": [1.641, 50.65, 1.619, 0, 1.641, 41.32, 1.667, 0]}, {"time": 1.75}]}, "chan t": {"rotate": [{"time": 0.25}, {"time": 0.4333, "value": 58.83}, {"time": 0.6333, "value": 42.98}, {"time": 0.7, "value": 13.14}, {"time": 0.8, "value": 53.74}, {"time": 0.8833, "value": -32.28}, {"time": 1.05, "value": -35.71}, {"time": 1.2, "value": -15.7}, {"time": 1.25, "value": 9.45}], "translate": [{"time": 0.3333}, {"time": 0.4, "y": -3.69}, {"time": 0.5833, "x": 23.85, "y": 10.7}, {"time": 0.6833, "y": -8.3}, {"time": 0.75}, {"time": 0.85, "x": -40.52, "y": 30.13}, {"time": 1, "x": -32.28, "y": 59.95}, {"time": 1.25}]}, "right hand": {"rotate": [{"value": 25.89, "curve": [0.042, 19.05, 0.092, 12.15]}, {"time": 0.1333, "value": 12.15, "curve": [0.217, 12.15, 0.3, 58.62]}, {"time": 0.3833, "value": 58.62, "curve": [0.467, 58.62, 0.55, -1.36]}, {"time": 0.6333, "value": -1.36, "curve": [0.717, -1.36, 0.8, 56.88]}, {"time": 0.8833, "value": 56.88, "curve": [0.967, 56.88, 1.05, -1.36]}, {"time": 1.1333, "value": -1.36, "curve": [1.217, -1.36, 1.3, 56.88]}, {"time": 1.3833, "value": 56.88, "curve": [1.467, 56.88, 1.55, -1.36]}, {"time": 1.6333, "value": -1.36, "curve": [1.717, -1.36, 1.8, 39.59]}, {"time": 1.8833, "value": 39.59, "curve": [1.9, 39.59, 1.917, 38.54]}, {"time": 1.9333, "value": 36.73, "curve": [1.959, 34.06, 1.976, 30.02]}, {"time": 2, "value": 25.89}]}, "right hand2": {"rotate": [{"value": 39.59}]}, "left hand": {"rotate": [{"value": -17.86, "curve": [0.042, -8.52, 0.092, 0.91]}, {"time": 0.1333, "value": 0.91, "curve": [0.217, 0.91, 0.3, -55.11]}, {"time": 0.3833, "value": -55.11, "curve": [0.467, -55.11, 0.55, -3.05]}, {"time": 0.6333, "value": -3.05, "curve": [0.717, -3.05, 0.8, -54.25]}, {"time": 0.8833, "value": -54.25, "curve": [0.967, -54.25, 1.05, -3.05]}, {"time": 1.1333, "value": -3.05, "curve": [1.217, -3.05, 1.3, -54.25]}, {"time": 1.3833, "value": -54.25, "curve": [1.467, -54.25, 1.55, -3.05]}, {"time": 1.6333, "value": -3.05, "curve": [1.717, -3.05, 1.759, -21.85]}, {"time": 1.8333, "value": -37.43, "curve": [1.875, -46.18, 1.976, -23.5]}, {"time": 2, "value": -17.86}]}, "left hand2": {"rotate": [{"value": -36.56}]}, "bone": {"translate": [{"time": 0.25, "curve": [0.317, 0, 0.383, 0, 0.317, 30.37, 0.383, 45.23]}, {"time": 0.45, "y": 45.56, "curve": [0.533, 0, 0.667, 0, 0.593, 46.28, 0.698, 16.57]}, {"time": 0.75, "curve": [0.85, 0, 0.9, 0, 0.805, 13.46, 0.83, 51.11]}, {"time": 1, "y": 51.4, "curve": [1.083, 0, 1.167, 0, 1.155, 51.67, 1.216, 15.88]}, {"time": 1.25, "curve": [1.333, 0, 1.417, 0, 1.321, 20.86, 1.417, 51.4]}, {"time": 1.5, "y": 51.4}, {"time": 1.75}], "scale": [{"x": 0.96, "y": 0.98}]}, "Christmas tree 2": {"rotate": [{"curve": [0.067, 0, 0.133, -9.53]}, {"time": 0.2, "value": -9.53, "curve": [0.325, -9.53, 0.458, 7.11]}, {"time": 0.5833, "value": 7.11, "curve": [0.725, 7.11, 0.858, -11.23]}, {"time": 1, "value": -11.23, "curve": [1.192, -11.23, 1.392, 6.61]}, {"time": 1.5833, "value": 6.61, "curve": [1.667, 6.61, 1.75, -9.92]}, {"time": 1.8333, "value": -9.92, "curve": [1.892, -9.92, 1.942, 0]}, {"time": 2}]}, "left foot": {"rotate": [{"time": 0.25, "value": -3.65, "curve": [0.325, 12.12, 0.408, 43.66]}, {"time": 0.4833, "value": 43.66, "curve": [0.55, 43.66, 0.633, 11.55]}, {"time": 0.6833, "value": 0.16, "curve": [0.771, -19.66, 0.86, -45.39]}, {"time": 1, "value": -45.77, "curve": [1.083, -45.99, 1.2, -16.03]}, {"time": 1.25, "value": 1.33, "curve": [1.298, 18.11, 1.417, 41.69]}, {"time": 1.5, "value": 41.69, "curve": [1.583, 41.69, 1.667, 13.28]}, {"time": 1.75, "value": -0.92}]}, "left foot2": {"rotate": [{"time": 0.25, "value": -65.24}, {"time": 0.35, "value": -78.89}, {"time": 0.6, "value": -26.12}, {"time": 0.75, "value": -31.53}, {"time": 1.1333, "value": -24.56}, {"time": 1.2333, "value": -26.48}]}, "left foot3": {"rotate": [{"time": 0.25, "value": -2.26}], "translate": [{"time": 0.25, "x": 2.02, "y": 0.29}], "scale": [{"time": 0.25, "y": 0.679}]}, "right foot": {"rotate": [{"time": 0.25, "value": -22.67, "curve": [0.35, -22.67, 0.438, -12.96]}, {"time": 0.55, "value": -12.96, "curve": [0.617, -12.96, 0.728, -17.18]}, {"time": 0.75, "value": -32.33, "curve": [0.77, -45.91, 0.797, -66.26]}, {"time": 0.8833, "value": -85.73, "curve": [0.908, -91.22, 0.956, -95.04]}, {"time": 1.0333, "value": -95.04, "curve": [1.108, -95.04, 1.167, -63.88]}, {"time": 1.25, "value": -32.9, "curve": [1.277, -22.95, 1.356, -8.25]}, {"time": 1.5, "value": -7.94, "curve": [1.583, -7.77, 1.667, -14.47]}, {"time": 1.75, "value": -27.51}]}, "right foot2": {"rotate": [{"time": 0.25, "value": 50.34, "curve": [0.275, 27.77, 0.292, 17.51]}, {"time": 0.3333, "value": 16.49, "curve": [0.429, 14.1, 0.507, 31.42]}, {"time": 0.5833, "value": 39.26, "curve": [0.629, 44, 0.692, 48.48]}, {"time": 0.75, "value": 48.48, "curve": [0.784, 48.48, 0.824, 44.39]}, {"time": 0.85, "value": 41.88, "curve": [0.909, 36.11, 1.011, 27.54]}, {"time": 1.0833, "value": 39.09, "curve": [1.142, 48.41, 1.198, 53.26]}, {"time": 1.25, "value": 56.6, "curve": [1.292, 59.32, 1.317, 61.99]}, {"time": 1.3833, "value": 65.53, "curve": [1.467, 69.98, 1.519, 70.73]}, {"time": 1.5833, "value": 68.54, "curve": [1.647, 66.37, 1.692, 55.68]}, {"time": 1.75, "value": 55.68}]}, "Christmas tree 1": {"rotate": [{}, {"time": 0.3, "value": -17.07}, {"time": 0.65, "value": 12.83}, {"time": 1, "value": -6.38}, {"time": 1.3, "value": -11.74}, {"time": 1.6, "value": 7.23}, {"time": 2}]}}, "ik": {"chan p": [{"bendPositive": false, "curve": "stepped"}, {"time": 0.25, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 1.5333, "mix": 0, "bendPositive": false}, {"time": 1.75, "bendPositive": false}], "chan t": [{"curve": "stepped"}, {"time": 0.25, "mix": 0, "curve": "stepped"}, {"time": 1.5333, "mix": 0}, {"time": 1.75}]}}, "TreeDance_02": {"bones": {"chan p": {"translate": [{"x": 45.01, "curve": [0.089, 45.01, 0.192, 35.54, 0.072, 6.93, 0.178, 10.28]}, {"time": 0.2667, "x": 24.45, "y": 10.28, "curve": [0.324, 15.88, 0.422, 3.9, 0.344, 10.28, 0.416, 7.24]}, {"time": 0.5, "x": 3.9, "curve": "stepped"}, {"time": 1.5, "x": 3.9, "curve": [1.589, 3.9, 1.7, 14.74, 1.563, 9.13, 1.678, 16.6]}, {"time": 1.7667, "x": 24.45, "y": 16.6, "curve": [1.839, 34.94, 1.922, 45.01, 1.844, 16.6, 1.937, 11.02]}, {"time": 2, "x": 45.01}]}, "chan t": {"translate": [{"x": 11.66, "curve": "stepped"}, {"time": 0.5, "x": 11.66, "curve": [0.623, 11.79, 0.7, -13.58, 0.589, 5.01, 0.678, 15.02]}, {"time": 0.7667, "x": -26.11, "y": 15.02, "curve": [0.826, -37.31, 0.922, -39.52, 0.844, 15.02, 0.926, 8.07]}, {"time": 1, "x": -39.52, "curve": [1.089, -39.52, 1.198, -37.43, 1.075, 5.5, 1.178, 10.28]}, {"time": 1.2667, "x": -25.01, "y": 10.28, "curve": [1.338, -12.24, 1.422, 11.66, 1.344, 10.28, 1.422, 0]}, {"time": 1.5, "x": 11.66}]}, "ball1": {"rotate": [{"value": 1.57, "curve": [0.023, -0.63, 0.045, -2.11]}, {"time": 0.0667, "value": -2.11, "curve": [0.178, -2.11, 0.289, 29.22]}, {"time": 0.4, "value": 29.22, "curve": [0.511, 29.22, 0.622, -8.75]}, {"time": 0.7333, "value": -8.75, "curve": [0.844, -8.75, 0.956, 10.31]}, {"time": 1.0667, "value": 10.31, "curve": [1.178, 10.31, 1.289, 0]}, {"time": 1.4, "curve": [1.434, 0, 1.467, 2.93]}, {"time": 1.5, "value": 7.2, "curve": [1.579, 17.02, 1.656, 33.35]}, {"time": 1.7333, "value": 33.35, "curve": [1.823, 33.35, 1.912, 10.58]}, {"time": 2, "value": 1.57}]}, "ball 3": {"rotate": [{"value": 29.66, "curve": [0.09, 20.51, 0.178, -2.11]}, {"time": 0.2667, "value": -2.11, "curve": [0.378, -2.11, 0.489, 29.22]}, {"time": 0.6, "value": 29.22, "curve": [0.711, 29.22, 0.822, -8.75]}, {"time": 0.9333, "value": -8.75, "curve": [1.044, -8.75, 1.156, 10.31]}, {"time": 1.2667, "value": 10.31, "curve": [1.345, 10.31, 1.423, 5.25]}, {"time": 1.5, "value": 2.23, "curve": [1.534, 0.95, 1.567, 0]}, {"time": 1.6, "curve": [1.711, 0, 1.822, 33.35]}, {"time": 1.9333, "value": 33.35, "curve": [1.956, 33.35, 1.979, 32]}, {"time": 2, "value": 29.66}]}, "ball2": {"rotate": [{"value": 15.62, "curve": [0.056, 6.81, 0.111, -2.11]}, {"time": 0.1667, "value": -2.11, "curve": [0.278, -2.11, 0.389, 29.22]}, {"time": 0.5, "value": 29.22, "curve": [0.611, 29.22, 0.722, -8.75]}, {"time": 0.8333, "value": -8.75, "curve": [0.944, -8.75, 1.056, 10.31]}, {"time": 1.1667, "value": 10.31, "curve": [1.278, 10.31, 1.389, 0]}, {"time": 1.5, "curve": [1.611, 0, 1.722, 33.35]}, {"time": 1.8333, "value": 33.35, "curve": [1.889, 33.35, 1.945, 24.54]}, {"time": 2, "value": 15.62}]}, "right foot2": {"rotate": [{"value": 29.96}, {"time": 0.5, "value": 15.92}, {"time": 1}, {"time": 1.5, "value": 15.92}, {"time": 2, "value": 29.96}]}, "right foot": {"rotate": [{"value": -24.76}, {"time": 0.5, "value": -24.09}, {"time": 1}, {"time": 1.5, "value": -24.09}, {"time": 2, "value": -24.76}]}, "left foot2": {"rotate": [{"value": -28.37}, {"time": 0.5, "value": -14.99}, {"time": 1, "value": -3.06}, {"time": 1.5, "value": -14.99}, {"time": 2, "value": -28.37}]}, "left foot": {"rotate": [{"value": -11.09}, {"time": 0.5, "value": -11.5}, {"time": 1, "value": -32.89}, {"time": 1.5, "value": -11.5}, {"time": 2, "value": -11.09}]}, "right hand2": {"rotate": [{}, {"time": 0.5, "value": -73.3}, {"time": 1}, {"time": 1.5, "value": -73.3}, {"time": 2}]}, "right hand": {"rotate": [{}, {"time": 0.5, "value": 29.08}, {"time": 1}, {"time": 1.5, "value": 29.08}, {"time": 2}]}, "left hand2": {"rotate": [{}, {"time": 0.5, "value": 80.58}, {"time": 1}, {"time": 1.5, "value": 80.58}, {"time": 2}]}, "left hand": {"rotate": [{}, {"time": 0.5, "value": -33.96}, {"time": 1}, {"time": 1.5, "value": -33.96}, {"time": 2}]}, "Christmas tree 3": {"rotate": [{"value": -1.93, "curve": [0.036, -2.46, 0.079, -3.05]}, {"time": 0.1333, "value": -3.67, "curve": [0.296, -5.52, 0.661, -4.95]}, {"time": 0.8333, "value": -3.93, "curve": [0.991, -2.99, 1.11, 0.44]}, {"time": 1.2333, "value": 1.45, "curve": [1.334, 2.28, 1.339, 2.41]}, {"time": 1.5, "value": 2.52, "curve": [1.666, 2.45, 1.766, 1.79]}, {"time": 1.8333, "value": 0.81, "curve": [1.882, 0.1, 1.926, -0.81]}, {"time": 2, "value": -1.93}], "translate": [{"x": 28.32, "y": -6.16, "curve": [0.089, 28.32, 0.215, 22.65, 0.089, -6.16, 0.211, -18.81]}, {"time": 0.3, "x": 17.56, "y": -18.81, "curve": [0.375, 13.05, 0.432, 8.13, 0.378, -18.81, 0.422, -6.16]}, {"time": 0.5, "x": 1.6, "y": -6.16, "curve": [0.579, -6.04, 0.673, -13.49, 0.589, -6.16, 0.678, -25.13]}, {"time": 0.7667, "x": -17.44, "y": -25.13, "curve": [0.848, -20.85, 0.91, -19.9, 0.844, -25.13, 0.922, -6.16]}, {"time": 1, "x": -17.94, "y": -6.16, "curve": [1.094, -15.9, 1.189, -7.06, 1.089, -6.16, 1.178, -19.6]}, {"time": 1.2667, "x": -0.4, "y": -19.6, "curve": [1.344, 6.25, 1.38, 10.12, 1.344, -19.6, 1.422, -6.16]}, {"time": 1.5, "x": 18.01, "y": -6.16, "curve": [1.605, 24.48, 1.671, 27.21, 1.589, -6.16, 1.678, -18.02]}, {"time": 1.7667, "x": 28.95, "y": -18.02, "curve": [1.865, 30.72, 1.922, 28.32, 1.844, -18.02, 1.922, -6.16]}, {"time": 2, "x": 28.32, "y": -6.16}]}}}, "TreeDance_03": {"bones": {"Christmas tree 3": {"rotate": [{"value": 8.21, "curve": [0.083, 8.21, 0.167, 5.58]}, {"time": 0.25, "curve": [0.314, -4.31, 0.39, -9.74]}, {"time": 0.5, "value": -9.74, "curve": [0.609, -9.74, 0.679, -1.94]}, {"time": 0.75, "value": 1.8, "curve": [0.823, 5.64, 0.901, 8.21]}, {"time": 1, "value": 8.21}], "translatex": [{"value": -15.87, "curve": [0.167, -16.17, 0.278, 17.6]}, {"time": 0.5, "value": 17.29, "curve": [0.667, 17.07, 0.742, -15.42]}, {"time": 1, "value": -15.87}], "translatey": [{"value": 61.22, "curve": [0.083, 61.22, 0.19, 36.24]}, {"time": 0.25, "value": -14.61, "curve": [0.297, 20.97, 0.373, 65.41]}, {"time": 0.5, "value": 65.3, "curve": [0.607, 65.21, 0.697, 30.54]}, {"time": 0.75, "value": -14.61, "curve": [0.782, 29.47, 0.885, 61.22]}, {"time": 1, "value": 61.22}]}, "right hand": {"rotate": [{"value": 67.13, "curve": [0.079, 45.76, 0.149, -5.72]}, {"time": 0.1833, "value": -26.22, "curve": [0.213, -3.97, 0.319, 76.69]}, {"time": 0.4333, "value": 76.69, "curve": [0.555, 76.69, 0.624, -1.52]}, {"time": 0.6833, "value": -26.22, "curve": [0.738, 1.41, 0.836, 76.69]}, {"time": 0.9333, "value": 76.69, "curve": [0.956, 76.69, 0.979, 73.04]}, {"time": 1, "value": 67.13}]}, "left hand": {"rotate": [{"value": -56.97, "curve": [0.072, -40.7, 0.15, -5.75]}, {"time": 0.1833, "value": 11.5, "curve": [0.245, -11.75, 0.35, -65.76]}, {"time": 0.4333, "value": -65.76, "curve": [0.517, -65.76, 0.627, -7.79]}, {"time": 0.6833, "value": 11.5, "curve": [0.744, -9.58, 0.85, -65.76]}, {"time": 0.9333, "value": -65.76, "curve": [0.954, -65.76, 0.977, -62.3]}, {"time": 1, "value": -56.97}]}, "left hand2": {"rotate": [{"value": -46.3, "curve": [0.045, -46.06, 0.095, -44.23]}, {"time": 0.1333, "value": -39.13, "curve": [0.169, -34.42, 0.237, -29.32]}, {"time": 0.2833, "value": -28.99, "curve": [0.344, -28.56, 0.393, -34.31]}, {"time": 0.4333, "value": -42.35, "curve": [0.472, -50.07, 0.527, -53.88]}, {"time": 0.5833, "value": -52.48, "curve": [0.64, -51.05, 0.655, -47.18]}, {"time": 0.6833, "value": -41.96, "curve": [0.716, -36.07, 0.75, -32.16]}, {"time": 0.7833, "value": -32.16, "curve": [0.841, -32.16, 0.872, -41.94]}, {"time": 0.9333, "value": -45.52, "curve": [0.952, -46.1, 0.976, -46.44]}, {"time": 1, "value": -46.3}]}, "right hand2": {"rotate": [{"value": 50.49, "curve": [0.025, 49.97, 0.065, 47.34]}, {"time": 0.0833, "value": 43.95, "curve": [0.145, 32.48, 0.158, 21.76]}, {"time": 0.2333, "value": 17.84, "curve": [0.33, 12.83, 0.408, 23.44]}, {"time": 0.4833, "value": 39.68, "curve": [0.541, 52.1, 0.554, 58.13]}, {"time": 0.6, "value": 55.32, "curve": [0.682, 50.31, 0.696, 23.41]}, {"time": 0.7833, "value": 23.78, "curve": [0.839, 24.02, 0.876, 37.42]}, {"time": 0.9333, "value": 45.93, "curve": [0.955, 49.78, 0.979, 50.94]}, {"time": 1, "value": 50.49}]}, "chan t": {"translate": [{"x": -20.41, "y": 61.22}, {"time": 0.1833, "x": -7.84, "y": 38.81}, {"time": 0.25, "x": 9.52, "y": -1.36}, {"time": 0.45, "x": -36.73, "y": 52.32}, {"time": 0.5, "x": -36.73, "y": 117.62}, {"time": 0.6833}, {"time": 1, "y": 61.22}]}, "chan p": {"translate": [{"x": 42.17, "y": 122.59}, {"time": 0.1833, "x": 54.42, "y": 88.42}, {"time": 0.5, "y": 65.3}, {"time": 0.75}, {"time": 1, "y": 77.7}]}, "right foot": {"rotate": [{"value": -6.44, "curve": [0.078, -6.44, 0.214, -4.52]}, {"time": 0.2833, "value": -21.29, "curve": [0.327, -31.77, 0.337, -55.13]}, {"time": 0.3833, "value": -66.52, "curve": [0.45, -83, 0.679, -80.31]}, {"time": 0.7333, "value": -64.86, "curve": [0.764, -56.12, 0.767, -31.93]}, {"time": 0.8, "value": -21.55, "curve": [0.849, -6.21, 0.961, -6.44]}, {"time": 1, "value": -6.44}]}, "right foot2": {"rotate": [{"value": 4.88, "curve": [0.078, 4.88, 0.23, 2.71]}, {"time": 0.2833, "value": 28.09, "curve": [0.305, 38.5, 0.372, 71.68]}, {"time": 0.5, "value": 72.64, "curve": [0.67, 73.92, 0.808, 32.99]}, {"time": 0.8333, "value": 25.7, "curve": [0.91, 3.91, 0.961, 4.88]}, {"time": 1, "value": 4.88}]}, "left foot": {"rotate": [{"value": 29.97, "curve": [0.083, 29.97, 0.195, 30.29]}, {"time": 0.25, "value": 14.4, "curve": [0.294, 1.53, 0.293, -2.72]}, {"time": 0.3333, "value": -18.71, "curve": [0.394, -43.07, 0.651, -35.61]}, {"time": 0.7, "value": -17.7, "curve": [0.728, -7.7, 0.786, 8.77]}, {"time": 0.8333, "value": 17.7, "curve": [0.882, 26.87, 0.939, 29.97]}, {"time": 1, "value": 29.97}]}, "left foot2": {"rotate": [{"value": -88.64, "curve": [0.083, -88.64, 0.156, -86.44]}, {"time": 0.2333, "value": -72.6, "curve": [0.302, -60.18, 0.298, -15.13]}, {"time": 0.35, "value": -5.4, "curve": [0.416, 6.84, 0.672, 6.1]}, {"time": 0.7, "value": -13.72, "curve": [0.754, -51.69, 0.939, -88.64]}, {"time": 1, "value": -88.64}]}}, "ik": {"chan p": [{"mix": 0, "bendPositive": false}], "chan t": [{"mix": 0}]}}, "TreeDance_04": {"bones": {"Christmas tree 3": {"rotate": [{"value": -18.94, "curve": [0.167, -18.94, 0.333, 11.37]}, {"time": 0.5, "value": 11.37, "curve": [0.667, 11.37, 0.833, -18.94]}, {"time": 1, "value": -18.94}], "translate": [{"x": -13.88, "y": 3.79, "curve": [0.083, -13.88, 0.167, -13.88, 0.083, 3.79, 0.167, -12.62]}, {"time": 0.25, "x": -13.88, "y": -12.62, "curve": [0.333, -13.88, 0.417, -13.88, 0.333, -12.62, 0.417, 3.79]}, {"time": 0.5, "x": -13.88, "y": 3.79, "curve": [0.583, -13.88, 0.667, -13.88, 0.583, 3.79, 0.667, -12.62]}, {"time": 0.75, "x": -13.88, "y": -12.62, "curve": [0.833, -13.88, 0.917, -13.88, 0.833, -12.62, 0.917, 3.79]}, {"time": 1, "x": -13.88, "y": 3.79}]}, "chan t": {"translate": [{"x": -73.2, "y": 73.2, "curve": [0.083, -73.2, 0.167, -18.93, 0.083, 73.2, 0.167, -1.26]}, {"time": 0.25, "x": -18.93, "y": -1.26, "curve": "stepped"}, {"time": 0.75, "x": -18.93, "y": -1.26, "curve": [0.833, -18.93, 0.917, -73.2, 0.833, -1.26, 0.917, 73.2]}, {"time": 1, "x": -73.2, "y": 73.2}]}, "chan p": {"translate": [{"time": 0.25, "curve": [0.333, 0, 0.417, 50.48, 0.333, 0, 0.417, 70.67]}, {"time": 0.5, "x": 50.48, "y": 70.67, "curve": [0.583, 50.48, 0.667, 0, 0.583, 70.67, 0.667, 0]}, {"time": 0.75}]}, "left hand": {"rotate": [{"value": -66.3, "curve": [0.36, -66.3, 0.195, 13.7]}, {"time": 0.5, "value": 13.7, "curve": [0.789, 13.7, 0.632, -66.3]}, {"time": 1, "value": -66.3}]}, "left hand2": {"rotate": [{"value": -25.28, "curve": [0.025, -27.33, 0.052, -28.76]}, {"time": 0.0833, "value": -28.76, "curve": [0.348, -28.76, 0.316, 65.08]}, {"time": 0.5833, "value": 65.81, "curve": [0.788, 66.37, 0.801, -8.7]}, {"time": 1, "value": -25.28}]}, "right hand": {"rotate": [{"value": -8.84, "curve": [0.32, -8.84, 0.228, 58.53]}, {"time": 0.5, "value": 58.53, "curve": [0.779, 58.53, 0.69, -8.84]}, {"time": 1, "value": -8.84}]}, "right hand2": {"rotate": [{"value": -73.06, "curve": [0.025, -75.26, 0.052, -77.01]}, {"time": 0.0833, "value": -77.01, "curve": [0.355, -77.01, 0.331, 13.08]}, {"time": 0.5833, "value": 13.08, "curve": [0.816, 13.08, 0.819, -56.46]}, {"time": 1, "value": -73.06}]}, "left foot": {"rotate": [{"value": 4.13}, {"time": 0.5, "value": 41.66}, {"time": 1, "value": 4.13}]}, "left foot2": {"rotate": [{"value": -39.92}, {"time": 0.5, "value": -53.96}, {"time": 1, "value": -39.92}]}, "right foot": {"rotate": [{"value": -66.1}, {"time": 0.5, "value": -23.64}, {"time": 1, "value": -66.1}]}, "right foot2": {"rotate": [{"value": 48.04}, {"time": 0.5, "value": 28.84}, {"time": 1, "value": 48.04}]}}}, "TreeDance_05": {"bones": {"Christmas tree 3": {"translate": [{"curve": [0.083, 0, 0.167, 0, 0.083, 0, 0.189, 0.63]}, {"time": 0.25, "y": -20.04, "curve": [0.333, 0, 0.417, 0, 0.302, -4.35, 0.417, 0]}, {"time": 0.5}]}, "right hand": {"rotate": [{"value": 0.1, "curve": [0.025, -2.55, 0.034, 0]}, {"time": 0.05, "curve": [0.133, 0, 0.234, 25.17]}, {"time": 0.3, "value": 72.45, "curve": [0.335, 11.83, 0.424, 7.33]}, {"time": 0.5, "value": 0.1}]}, "left hand": {"rotate": [{"value": -0.49, "curve": [0.017, 1.5, 0.034, 0.91]}, {"time": 0.05, "curve": [0.132, -4.58, 0.235, -23.59]}, {"time": 0.3, "value": -61.57, "curve": [0.353, -21.68, 0.427, -6.52]}, {"time": 0.5, "value": -0.49}]}}}}}