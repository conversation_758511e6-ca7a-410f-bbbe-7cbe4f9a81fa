// Made with Amplify Shader Editor v1.9.1.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "ChangeColor_Gradiant"
{
	Properties
	{
		_Skin("Skin", 2D) = "white" {}
		_ColorHighlight("Color Highlight", Color) = (0,0,0,0)
		_Hightlight("Hightlight", 2D) = "white" {}
		_ColorBase("Color Base", Color) = (0,0,0,0)
		_Base("Base", 2D) = "white" {}
		_Emission("Emission", Range( 0 , 1)) = 0.5
		_Highlight("Highlight", Range( 0 , 1)) = 0.5
		_Float0("Float 0", Range( 0 , 1)) = 0.6
		[HideInInspector] _texcoord( "", 2D ) = "white" {}
		[HideInInspector] __dirty( "", Int ) = 1
	}

	SubShader
	{
		Tags{ "RenderType" = "Opaque"  "Queue" = "Geometry+0" "IsEmissive" = "true"  }
		Cull Off
		ZWrite On

		CGPROGRAM
		#pragma target 3.0
		#pragma surface surf Standard keepalpha addshadow fullforwardshadows 
		struct Input
		{
			float2 uv_texcoord;
		};

		uniform sampler2D _Skin;
		uniform float4 _Skin_ST;
		uniform float4 _ColorHighlight;
		uniform sampler2D _Hightlight;
		uniform float4 _Hightlight_ST;
		uniform float4 _ColorBase;
		uniform sampler2D _Base;
		uniform float4 _Base_ST;
		uniform float _Highlight;
		uniform float _Float0;
		uniform float _Emission;

		void surf( Input i , inout SurfaceOutputStandard o )
		{
			float2 uv_Skin = i.uv_texcoord * _Skin_ST.xy + _Skin_ST.zw;
			float4 tex2DNode1 = tex2D( _Skin, uv_Skin );
			float4 temp_output_12_0 = ( tex2DNode1 * tex2DNode1.a );
			float2 uv_Hightlight = i.uv_texcoord * _Hightlight_ST.xy + _Hightlight_ST.zw;
			float4 tex2DNode3 = tex2D( _Hightlight, uv_Hightlight );
			float4 temp_output_8_0 = ( _ColorHighlight * tex2DNode3 * tex2DNode3.a );
			float2 uv_Base = i.uv_texcoord * _Base_ST.xy + _Base_ST.zw;
			float4 tex2DNode2 = tex2D( _Base, uv_Base );
			float4 temp_output_5_0 = ( _ColorBase * tex2DNode2 * ( tex2DNode2.a - tex2DNode3.a ) );
			float4 temp_output_4_0 = ( temp_output_12_0 + temp_output_8_0 + temp_output_5_0 );
			o.Albedo = temp_output_4_0.rgb;
			o.Emission = ( ( temp_output_5_0 + ( temp_output_8_0 * _Highlight ) + ( temp_output_12_0 * _Float0 ) ) * _Emission ).rgb;
			o.Alpha = 1;
		}

		ENDCG
	}
	Fallback "Diffuse"
	CustomEditor "ASEMaterialInspector"
}
/*ASEBEGIN
Version=19102
Node;AmplifyShaderEditor.SamplerNode;1;-1045.944,-372.4379;Inherit;True;Property;_Skin;Skin;0;0;Create;True;0;0;0;False;0;False;-1;7c67c8e1f9053f64683cdd4cdb98ca11;7c67c8e1f9053f64683cdd4cdb98ca11;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ObjSpaceLightDirHlpNode;11;-788.5638,-812.4905;Inherit;True;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;9;-1093.287,-681.9954;Inherit;True;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;10;-127.5807,-402.4521;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;12;-619.5738,-313.7951;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;15;-32.81824,122.5191;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;16;-258.7564,264.861;Inherit;False;Property;_Emission;Emission;5;0;Create;True;0;0;0;False;0;False;0.5;0.5;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.StandardSurfaceOutputNode;0;203.1927,-40.02287;Float;False;True;-1;2;ASEMaterialInspector;0;0;Standard;ChangeColor_Gradiant;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;Off;0;False;;0;False;;False;0;False;;0;False;;False;0;Opaque;0.5;True;True;0;False;Opaque;;Geometry;All;12;all;True;True;True;True;0;False;;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;2;15;10;25;False;0.5;True;0;0;False;;0;False;;0;0;False;;0;False;;0;False;;0;False;;0;False;0;0,0,0,0;VertexOffset;True;False;Cylindrical;False;True;Relative;0;;-1;-1;-1;-1;0;False;0;0;False;;-1;0;False;;0;0;0;False;0.1;False;;0;False;;False;16;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT;0;False;9;FLOAT;0;False;10;FLOAT;0;False;13;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;12;FLOAT3;0,0,0;False;14;FLOAT4;0,0,0,0;False;15;FLOAT3;0,0,0;False;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;19;-219.8414,-143.0691;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;4;-489.0883,-186.4271;Inherit;True;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;20;-638.7922,54.92814;Inherit;False;Property;_Float0;Float 0;7;0;Create;True;0;0;0;False;0;False;0.6;0.6;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;14;-122.2814,-20.86615;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;17;-389.1288,165.4982;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;18;-624.3415,402.6768;Inherit;False;Property;_Highlight;Highlight;6;0;Create;True;0;0;0;False;0;False;0.5;0.5;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;2;-1350.931,51.70304;Inherit;True;Property;_Base;Base;4;0;Create;True;0;0;0;False;0;False;-1;f3366beed26670c44be90e1e6c9dffbf;f3366beed26670c44be90e1e6c9dffbf;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;6;-1289.439,-179.4872;Inherit;False;Property;_ColorBase;Color Base;3;0;Create;True;0;0;0;False;0;False;0,0,0,0;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;5;-768.4734,-125.9661;Inherit;True;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.ColorNode;7;-919.6062,280.6135;Inherit;False;Property;_ColorHighlight;Color Highlight;1;0;Create;True;0;0;0;False;0;False;0,0,0,0;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;8;-582.8762,252.2649;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SamplerNode;3;-1335.681,456.2569;Inherit;True;Property;_Hightlight;Hightlight;2;0;Create;True;0;0;0;False;0;False;-1;34f6b056acd10cb45918862ad07b46df;34f6b056acd10cb45918862ad07b46df;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleSubtractOpNode;21;-1005.259,87.23624;Inherit;True;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
WireConnection;10;0;4;0
WireConnection;12;0;1;0
WireConnection;12;1;1;4
WireConnection;15;0;14;0
WireConnection;15;1;16;0
WireConnection;0;0;4;0
WireConnection;0;2;15;0
WireConnection;19;0;12;0
WireConnection;19;1;20;0
WireConnection;4;0;12;0
WireConnection;4;1;8;0
WireConnection;4;2;5;0
WireConnection;14;0;5;0
WireConnection;14;1;17;0
WireConnection;14;2;19;0
WireConnection;17;0;8;0
WireConnection;17;1;18;0
WireConnection;5;0;6;0
WireConnection;5;1;2;0
WireConnection;5;2;21;0
WireConnection;8;0;7;0
WireConnection;8;1;3;0
WireConnection;8;2;3;4
WireConnection;21;0;2;4
WireConnection;21;1;3;4
ASEEND*/
//CHKSM=7F6A9FD58731145F2E28D03A73E945D86A408B6B