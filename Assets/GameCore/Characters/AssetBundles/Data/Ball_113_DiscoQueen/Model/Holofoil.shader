// Made with Amplify Shader Editor v1.9.1.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "InwaveEffect/Holofoil"
{
	Properties
	{
		_Color("Color", Color) = (0.5019608,0.5019608,0.5019608,0)
		_Texture("Texture", 2D) = "white" {}
		_Mask("Mask", 2D) = "white" {}
		_FoilAlpha("Foil Alpha", Range( 0 , 1)) = 1
		_FoilVibrance("Foil Vibrance", Range( 0 , 1)) = 1
		_RimplightColor("Rimplight Color", Color) = (0.3773585,0.3773585,0.3773585,1)
		_RimpPower("RimpPower", Float) = 1
		_RimpThreshole("RimpThreshole", Float) = 1
		_WarpTexture("Warp Texture", 2D) = "bump" {}
		_HoloLightTexture("HoloLight Texture", 2D) = "white" {}
		_MapScale("Map Scale", Float) = 1
		_MapLevel("Map Level", Float) = 2
		_MapOffset("Map Offset", Vector) = (0,0,0,0)
		_DistorsionDistance("Distorsion Distance", Float) = 1
		[HideInInspector] _texcoord( "", 2D ) = "white" {}

	}
	
	SubShader
	{
		
		
		Tags { "RenderType"="Opaque" }
	LOD 100

		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend Off
		AlphaToMask Off
		Cull Off
		ColorMask RGBA
		ZWrite On
		ZTest LEqual
		Offset 0 , 0
		
		
		
		Pass
		{
			Name "Unlit"

			CGPROGRAM

			

			#ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
			#endif
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#include "UnityStandardUtils.cginc"
			#include "UnityStandardBRDF.cginc"
			#define ASE_NEEDS_FRAG_WORLD_POSITION


			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
				float4 ase_texcoord : TEXCOORD0;
				float4 ase_tangent : TANGENT;
				float3 ase_normal : NORMAL;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 worldPos : TEXCOORD0;
				#endif
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				float4 ase_texcoord3 : TEXCOORD3;
				float4 ase_texcoord4 : TEXCOORD4;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			uniform float4 _Color;
			uniform sampler2D _HoloLightTexture;
			uniform sampler2D _WarpTexture;
			uniform float _MapScale;
			uniform float2 _MapOffset;
			uniform float _MapLevel;
			uniform float _DistorsionDistance;
			uniform sampler2D _Texture;
			uniform float4 _Texture_ST;
			uniform float _FoilVibrance;
			uniform float _FoilAlpha;
			uniform sampler2D _Mask;
			uniform float4 _Mask_ST;
			uniform float4 _RimplightColor;
			uniform float _RimpPower;
			uniform float _RimpThreshole;

			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float3 ase_worldTangent = UnityObjectToWorldDir(v.ase_tangent);
				o.ase_texcoord2.xyz = ase_worldTangent;
				float3 ase_worldNormal = UnityObjectToWorldNormal(v.ase_normal);
				o.ase_texcoord3.xyz = ase_worldNormal;
				float ase_vertexTangentSign = v.ase_tangent.w * ( unity_WorldTransformParams.w >= 0.0 ? 1.0 : -1.0 );
				float3 ase_worldBitangent = cross( ase_worldNormal, ase_worldTangent ) * ase_vertexTangentSign;
				o.ase_texcoord4.xyz = ase_worldBitangent;
				
				o.ase_texcoord1.xy = v.ase_texcoord.xy;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord1.zw = 0;
				o.ase_texcoord2.w = 0;
				o.ase_texcoord3.w = 0;
				o.ase_texcoord4.w = 0;
				float3 vertexValue = float3(0, 0, 0);
				#if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
				#endif
				vertexValue = vertexValue;
				#if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
				#else
				v.vertex.xyz += vertexValue;
				#endif
				o.vertex = UnityObjectToClipPos(v.vertex);

				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				#endif
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				float2 texCoord21_g18 = i.ase_texcoord1.xy * float2( 1,1 ) + float2( 0,0 );
				float3 ase_worldTangent = i.ase_texcoord2.xyz;
				float3 ase_worldNormal = i.ase_texcoord3.xyz;
				float3 ase_worldBitangent = i.ase_texcoord4.xyz;
				float3 tanToWorld0 = float3( ase_worldTangent.x, ase_worldBitangent.x, ase_worldNormal.x );
				float3 tanToWorld1 = float3( ase_worldTangent.y, ase_worldBitangent.y, ase_worldNormal.y );
				float3 tanToWorld2 = float3( ase_worldTangent.z, ase_worldBitangent.z, ase_worldNormal.z );
				float3 ase_worldViewDir = UnityWorldSpaceViewDir(WorldPosition);
				ase_worldViewDir = normalize(ase_worldViewDir);
				float3 ase_tanViewDir =  tanToWorld0 * ase_worldViewDir.x + tanToWorld1 * ase_worldViewDir.y  + tanToWorld2 * ase_worldViewDir.z;
				ase_tanViewDir = Unity_SafeNormalize( ase_tanViewDir );
				float4 temp_output_117_0 = tex2D( _HoloLightTexture, ( UnpackScaleNormal( tex2D( _WarpTexture, (texCoord21_g18*_MapScale + _MapOffset.x) ), ( _MapLevel * _Time.y ) ) + ( -_DistorsionDistance * ase_tanViewDir ) ).xy );
				float2 uv_Texture = i.ase_texcoord1.xy * _Texture_ST.xy + _Texture_ST.zw;
				float4 tex2DNode19 = tex2D( _Texture, uv_Texture );
				float2 uv_Mask = i.ase_texcoord1.xy * _Mask_ST.xy + _Mask_ST.zw;
				float4 temp_output_28_0 = ( _FoilAlpha * tex2D( _Mask, uv_Mask ) );
				float4 lerpResult31 = lerp( tex2DNode19 , temp_output_117_0 , ( _FoilVibrance * temp_output_28_0 ));
				float4 blendOpSrc26 = temp_output_117_0;
				float4 blendOpDest26 = lerpResult31;
				float4 lerpBlendMode26 = lerp(blendOpDest26,(( blendOpDest26 > 0.5 ) ? ( 1.0 - 2.0 * ( 1.0 - blendOpDest26 ) * ( 1.0 - blendOpSrc26 ) ) : ( 2.0 * blendOpDest26 * blendOpSrc26 ) ),( temp_output_28_0 * ( 1.0 - _FoilVibrance ) ).r);
				float fresnelNdotV45 = dot( ase_worldNormal, ase_worldViewDir );
				float fresnelNode45 = ( 0.0 + _RimpPower * pow( 1.0 - fresnelNdotV45, _RimpThreshole ) );
				
				
				finalColor = ( ( _Color * ( saturate( lerpBlendMode26 )) * tex2DNode19.a ) + ( _RimplightColor * fresnelNode45 ) );
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	Fallback Off
}
/*ASEBEGIN
Version=19102
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;30;-959.1089,402.8018;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.OneMinusNode;33;-1172.312,379.4021;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;28;-1136.97,516.9354;Inherit;False;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;34;-926.6085,508.1021;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;42;-470.9892,1.385093;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SamplerNode;19;-1318.646,100.3182;Inherit;True;Property;_Texture;Texture;1;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;43;-958.5685,-189.2382;Inherit;False;Property;_Color;Color;0;0;Create;True;0;0;0;False;0;False;0.5019608,0.5019608,0.5019608,0;0.5019608,0.5019608,0.5019608,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;0;-160.5879,120.776;Float;False;True;-1;2;ASEMaterialInspector;100;5;InwaveEffect/Holofoil;0770190933193b94aaa3065e307002fa;True;Unlit;0;0;Unlit;2;False;True;0;1;False;;0;False;;0;1;False;;0;False;;True;0;False;;0;False;;False;False;False;False;False;False;False;False;False;True;0;False;;True;True;2;False;;False;True;True;True;True;True;0;False;;False;False;False;False;False;False;False;True;False;0;False;;255;False;;255;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;0;False;;False;True;1;False;;True;3;False;;True;True;0;False;;0;False;;True;1;RenderType=Opaque=RenderType;True;2;False;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;0;;0;0;Standard;1;Vertex Position,InvertActionOnDeselection;1;0;0;1;True;False;;False;0
Node;AmplifyShaderEditor.SamplerNode;21;-1493.986,572.7789;Inherit;True;Property;_Mask;Mask;2;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;27;-1475.469,484.9362;Inherit;False;Property;_FoilAlpha;Foil Alpha;3;0;Create;True;0;0;0;False;0;False;1;1;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;29;-1489.509,389.8016;Inherit;False;Property;_FoilVibrance;Foil Vibrance;4;0;Create;True;0;0;0;False;0;False;1;1;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.BlendOpsNode;26;-684.8279,119.3291;Inherit;False;Overlay;True;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;1;False;1;COLOR;0
Node;AmplifyShaderEditor.LerpOp;31;-887.609,158.4019;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.RangedFloatNode;48;-1427.603,1156.724;Inherit;False;Property;_RimpThreshole;RimpThreshole;7;0;Create;True;0;0;0;False;0;False;1;1.05;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.FresnelNode;45;-1130.81,1088.135;Inherit;False;Standard;WorldNormal;ViewDir;False;False;5;0;FLOAT3;0,0,1;False;4;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;5;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;49;-1403.991,1064.222;Inherit;False;Property;_RimpPower;RimpPower;6;0;Create;True;0;0;0;False;0;False;1;0.36;0;0;0;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;46;-1115.401,899.8016;Inherit;False;Property;_RimplightColor;Rimplight Color;5;0;Create;True;0;0;0;False;0;False;0.3773585,0.3773585,0.3773585,1;1,0.2877358,0.8853827,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;47;-857.4466,900.7068;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleAddOpNode;44;-413.4541,275.1734;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.FunctionNode;117;-1306.207,-13.19829;Inherit;False;HolographicFunctionBasic;8;;18;3819d5d17093d804a9108ee954083af5;0;0;1;COLOR;0
WireConnection;30;0;29;0
WireConnection;30;1;28;0
WireConnection;33;0;29;0
WireConnection;28;0;27;0
WireConnection;28;1;21;0
WireConnection;34;0;28;0
WireConnection;34;1;33;0
WireConnection;42;0;43;0
WireConnection;42;1;26;0
WireConnection;42;2;19;4
WireConnection;0;0;44;0
WireConnection;26;0;117;0
WireConnection;26;1;31;0
WireConnection;26;2;34;0
WireConnection;31;0;19;0
WireConnection;31;1;117;0
WireConnection;31;2;30;0
WireConnection;45;2;49;0
WireConnection;45;3;48;0
WireConnection;47;0;46;0
WireConnection;47;1;45;0
WireConnection;44;0;42;0
WireConnection;44;1;47;0
ASEEND*/
//CHKSM=4A0AD86609CB750166C7E707DE4533A270BA3C83