using UnityEngine;

public class RandomAnimation : MonoBeh<PERSON>our {
    [SerializeField] private int      maxValue;
    [SerializeField] private Animator animator;
    [SerializeField] private string   paramName;

    private int _animationParamHash;

    private void Awake() {
        // paramName null lead to a ArgumentNullException
        if (paramName != null) {
            _animationParamHash = Animator.StringToHash(paramName);
        }
    }

    /// <summary>
    /// Trigger Animation Event
    /// </summary>
    public void UpdateRandomAnim() {
        if (paramName != null && animator != null) {
            animator.SetInteger(_animationParamHash, Util.GetBetterRandomInt(0, maxValue + 1));
        }
    }
}
