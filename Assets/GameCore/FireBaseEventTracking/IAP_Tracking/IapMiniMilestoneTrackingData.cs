namespace TilesHop.Cores.EventTracking {
    public struct IapMiniMilestoneTrackingData {
        /// <summary>
        /// Number of tokens/mushroom earned when completing some milestones (for endless offer only)
        /// </summary>
        public int milestoneEarned;

        /// <summary>
        /// The milestone achieved in the mini milestones
        /// </summary>
        public int milestoneAchieved;

        /// <summary>
        /// The reward amount that users receive when completing the final milestone
        /// </summary>
        public int milestomeRewardAmount;


        /// <summary>
        /// The reward type that users receive when completing the final milestone
        /// </summary>
        public string milestomeRewardType;

    }
}