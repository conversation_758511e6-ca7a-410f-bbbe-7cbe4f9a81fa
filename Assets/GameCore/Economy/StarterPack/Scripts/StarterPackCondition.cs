using System;

[Serializable]
public class StarterPackCondition {
    /// <summary>
    /// Condition: daydiff > After_daydiff.
    /// <para>Set After_daydiff = -1 to skip this condition.</para>
    /// </summary>
    public int After_daydiff;

    /// <summary>
    /// (NEW) Condition: session_id + 1 > After_session. session_id is 0-indexed. After_session is 1-indexed.
    /// <para>Set After_session = 0 will skip this condition.</para>
    /// <para>Ex: After_session = 1 means condition will be qualified if session_id is greater than 0 (2nd session or later)</para>
    /// </summary>
    public int After_session;

    public bool Active_Sub;

    /// <summary>
    /// Deal expiration time. Not used in Ver2
    /// </summary>
    public int Starter_pack_Time_limit;

    /// <summary>
    /// Limit the number of auto-shows each day. Replaced with session-based show logic in version 2
    /// </summary>
    public int Capping_per_day;

    /// <summary>
    /// Maximum number of auto-popups count (x): auto show in first {x} sessions
    /// </summary>
    public int MaxAutoPopupSession;

    /// <summary>
    /// Init for editor testing
    /// </summary>
    public StarterPackCondition() {
        After_daydiff = -1;
        Active_Sub = true;
        Starter_pack_Time_limit = 72;
        Capping_per_day = 3;
        MaxAutoPopupSession = 5;
    }
}