using System;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.EconomySystem {
    public class UIButtonStarterPack : MonoBehaviour {
        [SerializeField] private bool isOnTop = false;
        [SerializeField] private Text txtTimeEnd;

        private TimeSpan remainTime;
        private string   strRemainTime;
        private float    countTime;

        private void OnEnable() {
            remainTime = Configuration.instance.GetRemainTimeStarterPack();
            strRemainTime = LocalizationManager.instance.GetLocalizedValue("ENDS_IN");
            UpdateRemainTime();
            countTime = 0f;
        }

        private void Update() {
            countTime += Time.deltaTime;
            if (countTime > 1) {
                countTime -= 1;
                remainTime = remainTime.Subtract(Configuration.OneSecond);
                UpdateRemainTime();

                if (remainTime.TotalSeconds < 1 && HomeManager.instance.menuHome != null) {
                    //Hide starter pack entry point when the event ends
                    HomeManager.instance.menuHome.SetActiveButtonStarterPack(false);
                    EconomyIAPTracker.Track_StarterPackDisable();
                }
            }
        }

        private void UpdateRemainTime() {
            if (isOnTop) {
                txtTimeEnd.text = $"{strRemainTime} {UICountDown.GetStringFormatTime(remainTime)}";
            } else {
                txtTimeEnd.text = UICountDown.GetStringFormatTime(remainTime);
            }
        }
    }
}