using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngineInternal;

[Serializable]
public class SpecialOfferConfig {
    // public string idPack;
    public string idpack_android;
    public string idpack_ios;
    public string idPack_android_secondary;
    public string idPack_ios_secondary;
    
    public SpecialOfferCondition condition;
    public int                   sale = 50;
    public SpecialOfferBenefit   benefit;
    public SpecialOfferNoAdsConfig noAds;

    public SpecialOfferConfig() {
        // idPack = "special_offer";
        idpack_android = "diamond1";
        idpack_ios = "SmallDiamondPack";

        condition = new SpecialOfferCondition() {
            daydiff = 0,
            session = 0,
            activesub = true,
            wallet = 200,

            expiredtime = 60,

            autoshow_needgems_capping = 3,
            maxAutoShowOnResult = 5,
            autoshow_expired = 10,

            location = "Home;Result"
        };
        
        // config test for ver2
        benefit = new SpecialOfferBenefit() {
            gem = 600,
            unlimitedRevive = 0,
            noFsAds = true
        };

        noAds = new SpecialOfferNoAdsConfig() {
            variant = 1,
            trigger_android = 2,
            trigger_ios = 2
        };
    }

    [Serializable]
    public struct SpecialOfferCondition {
        public int  daydiff;// số ngày mà user bắt đầu đc thấy special offer. Set 0 to skip this condition
        public int  session;// [Ver2] condition to active SP: user's session ID >= config's session. Set 0 to skip this condition
        public bool activesub;// có hiển thị special offer khi user đã là vip hay k

        public int wallet;

        public int expiredtime; //Thời gian tồn tại ingame (đơn vị tính theo phút)

        public int autoshow_needgems_capping;// số lần tự động hiện popup tối đa khi hết gem
        public int maxAutoShowOnResult; // [Ver2] maximum number of auto-popup on result screen
        public int autoshow_expired;//

        public string location;// vị trí mà popup tự động hiện
    }

    [Serializable]
    public struct SpecialOfferBenefit {
        public int  gem;
        public int  unlimitedRevive; // set 0 to remove this benefit
        public bool noFsAds; // set false to remove this benefit
    }

    [Serializable]
    public struct SpecialOfferNoAdsConfig {
        public int variant;
		public int trigger_android;
		public int trigger_ios;
	}
}