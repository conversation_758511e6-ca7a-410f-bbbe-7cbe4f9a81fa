using System;

namespace TilesHop.EconomySystem {
    [Serializable]
    public class EconomyIAPRemoteData {
        public bool NoFsAds_Enable;
        public bool ModifiedSubscription_Enable;
        public int  Subscription_Revive_Free;

        public EconomyIAPRemoteData(int variant) {
            switch (variant) {
                case 1:
                    NoFsAds_Enable = true;
                    Subscription_Revive_Free = 0;
                    ModifiedSubscription_Enable = false;
                    break;

                case 2:
                    NoFsAds_Enable = false;
                    Subscription_Revive_Free = 0;
                    ModifiedSubscription_Enable = false;
                    break;

                case 3:
                    NoFsAds_Enable = true;
                    Subscription_Revive_Free = 3;
                    ModifiedSubscription_Enable = true;
                    break;

                case 4:
                    NoFsAds_Enable = false;
                    Subscription_Revive_Free = 3;
                    ModifiedSubscription_Enable = true;
                    break;

                case 5:
                    NoFsAds_Enable = false;
                    Subscription_Revive_Free = 0;
                    ModifiedSubscription_Enable = false;
                    break;
            }
        }
    }
}