using System;
using System.Collections;
using System.Globalization;
using UnityEngine;
using UnityEngine.UI;

public class ChallengeModeConfirm : PopupUI {
    [SerializeField] private GameObject objTitle;
    [SerializeField] private GameObject objTitleVN;

    [Space] [Header("Count down")]
    [SerializeField] private GameObject objCountDownTime;

    [SerializeField] private Image imgCountDownTimeFill;
    [SerializeField] private Image imgCountDownTimeFillBG;
    [SerializeField] private Text  txtCountDownTime;

    [Header("Buttons")]
    [SerializeField] private Button btnContinue;

    [SerializeField] private Button btnNo;

    private Coroutine _ieCountDown;
    private float     _totalTime;
    private float     _countDownTime;
    private bool      _isCounting;
    private bool      _isAutoClose;

    private ChallengeModeTracking.ResponseType         _response;
    private Action<ChallengeModeTracking.ResponseType> _callback;

    private void Awake() {
        bool isVietnamese = LocalizationManager.GetCurrentLanguageID().Equals(LocalizationManager.VietnameseID);
        objTitle.SetActive(!isVietnamese);
        objTitleVN.SetActive(isVietnamese);
    }

    protected override void OnEnable() {
        base.OnEnable();
        _totalTime = RemoteConfigBase.instance.ChallengeMode_Countdown;
        _isAutoClose = RemoteConfigBase.instance.ChallengeMode_AutoClose;
        _countDownTime = _totalTime;
        if (_totalTime > 0) {
            objCountDownTime.SetActive(true);
            StartCountDown();
        } else {
            objCountDownTime.SetActive(false);
        }
    }

    private void Start() {
        btnNo.onClick.AddListener(BtnNoClick);
        btnContinue.onClick.AddListener(BtnContinueClick);
    }

    public void Show(Action<ChallengeModeTracking.ResponseType> callback) {
        this._callback = callback;
        _response = ChallengeModeTracking.ResponseType.no;
        _isCounting = true;
    }

    private void StartCountDown() {
        if (_ieCountDown != null) {
            StopCoroutine(_ieCountDown);
        }

        _ieCountDown = StartCoroutine(IECountDown());
    }

    private IEnumerator IECountDown() {
        SoundManager.PlayRevive_BG();
        UIOverlay uiOverlay = UIOverlay.instance;
        bool existOverlay = uiOverlay != null;
        do {
            if (_isCounting) {
                float percent = 1f - (_countDownTime / _totalTime);
                imgCountDownTimeFill.fillAmount = 1f - percent;
                imgCountDownTimeFillBG.fillAmount = 1f - percent;

                UpdateCountdownText();

                if (existOverlay && uiOverlay.CheckShowAdFsReward()) {
                    yield return null;

                    continue;
                }

                _countDownTime -= Time.deltaTime;
            }

            yield return null;
        } while (_countDownTime >= 0);

        UpdateUIEndCountDown();
        yield return null;

        if (_isAutoClose) {
            TimeOut();
        }
    }

    private void UpdateCountdownText() {
        txtCountDownTime.text = Mathf.Ceil(_countDownTime).ToString(CultureInfo.InvariantCulture);
    }

    private void UpdateUIEndCountDown() {
        txtCountDownTime.text = "0";
        imgCountDownTimeFill.fillAmount = 0f;
        imgCountDownTimeFillBG.fillAmount = 0f;
    }

    private void BtnContinueClick() {
        _response = ChallengeModeTracking.ResponseType.play;
        Close();
    }

    private void BtnNoClick() {
        _response = ChallengeModeTracking.ResponseType.no;
        Close();
    }

    private void TimeOut() {
        _response = ChallengeModeTracking.ResponseType.expired;
        Close();
    }

    public override void Close() {
        base.Close();
        if (_ieCountDown != null) {
            StopCoroutine(_ieCountDown);
        }

        SoundManager.StopAll();
        SoundManager.PlayGameButton();
        _callback?.Invoke(_response);
    }
}