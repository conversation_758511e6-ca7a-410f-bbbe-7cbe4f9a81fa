using UnityEngine;
using UnityEngine.UI;

public class MissionNotice : <PERSON>upUI {
    public static MissionNotice instance;
	public Text title;
	public Text desc;
	private bool _opened = false;
	[SerializeField] private Button btnOpen;
	private void Awake()
	{
        instance = this;
        btnOpen.onClick.AddListener(OnBtnOpenClick);
		LocalizationManager.instance.UpdateFont(desc);
	}

	protected override void OnEnable() {
		AnalyticHelper.instance.SetCurrentPopup(LOCATION_NAME.MISSION_NOTICE);
		base.OnEnable();
	}

	public void SetMission(Mission mission){
		_opened = false;
        desc.text = Mission.GetTitle(mission.missionType, mission.total,mission.points,mission.genre);
	}

	public void OnBtnOpenClick(){
        if (!_opened)
        {
	        Util.ShowPopUp(PopupName.SevenDayMission);
	        _opened = true;
            Close();
        }
	}

	public override void Close() {
		base.Close();
		if (AnalyticHelper.instance) {
			AnalyticHelper.instance.SetPopupClose();
		}
	}
}
