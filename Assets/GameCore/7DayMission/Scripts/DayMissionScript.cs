using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// trungvt
/// </summary>
public class DayMissionScript : MonoBehaviour {
    enum MissionState {
        Lock,
        Inprogress,
        Complete,
        ReceivedReward,
    }

    #region Fields

    [SerializeField] private Image imgSelect;
    [SerializeField] private Image iconReward;
    [SerializeField] private Text  txtDay;
    [SerializeField] private Image iconLock;
    [SerializeField] private Image iconComplete;
    [SerializeField] private Image iconReceivedReward;

    [SerializeField] private Sprite     sprSong;
    [SerializeField] private Sprite     sprBall;
    [SerializeField] private Sprite     sprDiamond;
    [SerializeField] private Button     btnClick;
    [SerializeField] private MissionDay _missionDay;

    [SerializeField] private Text txtCountNoti;

    //private
    private Action<int> _onClick;
    private int         _indexDay;
    private int         _countClick;

    #endregion

    #region Unity Methods

    private void Awake() {
        btnClick.onClick.AddListener(() => {
            _onClick?.Invoke(_indexDay);

            if (Configuration.isAdmin && _indexDay > SevenDayMission.instanceSafe.currentMissionDay) {
                if (++_countClick >= 10) {
                    SevenDayMission.instanceSafe.currentMissionDay = _indexDay;
                    GetComponentInParent<SevenDayMissionUI>().LoadData();
                }
            }

        });
    }

    #endregion

    #region Methods

    #endregion

    public void SetData(int indexDay, MissionDay missionDay, int currentDay, Action<int> onClick) {
        _indexDay = indexDay;
        _missionDay = missionDay;
        _onClick = onClick;

        RefreshUI(currentDay);
    }

    public void RefreshUI(int currentDay) {
        MissionState missionState = GetMissionState(_indexDay, _missionDay, currentDay);
        iconLock.gameObject.SetActive(missionState == MissionState.Lock);
        iconComplete.gameObject.SetActive(missionState == MissionState.Complete);
        iconReceivedReward.gameObject.SetActive(missionState == MissionState.ReceivedReward);

        if (iconReward != null) {
            if (_missionDay.dayReward == MissionReward.Diamond) {
                iconReward.sprite = sprDiamond;
            } else if (_missionDay.dayReward == MissionReward.Ball || _missionDay.dayReward == MissionReward.Character) {
                iconReward.sprite = sprBall;
            } else if (_missionDay.dayReward == MissionReward.Song) {
                iconReward.sprite = sprSong;
            }
        }

        txtDay.text = LocalizationManager.instance.GetLocalizedValue("DAY") + " " + (_indexDay + 1);
        LocalizationManager.instance.UpdateFont(txtDay);
        SetSelected(currentDay);

        RefreshCountNoti();
    }

    public void RefreshCountNoti() {
        txtCountNoti.transform.parent.gameObject.SetActive(false);
        if (iconLock.gameObject.activeSelf)
            return;
        if (iconComplete.gameObject.activeSelf)
            return;

            int totalMissionComplete = GetTotalMissionComplete();
            if (totalMissionComplete != 0) {
                txtCountNoti.transform.parent.gameObject.SetActive(true);
                txtCountNoti.text = totalMissionComplete.ToString();
            }
        }

    private MissionState GetMissionState(int indexDay, MissionDay missionDay, int currentDay) {
        if (indexDay > currentDay) { //lock
            return MissionState.Lock;

        } else if (!missionDay.isComplete) { //inprogress
            return MissionState.Inprogress;

        } else if (missionDay.isComplete && !missionDay.isReceivedReward) { //chưa nhận thưởng
            return MissionState.Complete;

        } else if (missionDay.isComplete && missionDay.isReceivedReward) { //đã nhận thưởng
            return MissionState.ReceivedReward;
        }

        return MissionState.Lock;
    }

    public void SetSelected(int selectDay) {
        imgSelect.gameObject.SetActive(_indexDay == selectDay);
    }

    private int GetTotalMissionComplete() {
        int total = 0;
        foreach (Mission mission in _missionDay.missions) {
            if (mission.isComplete && !mission.isReceivedReward) {
                total++;
            }
        }

        return total;
    }
}