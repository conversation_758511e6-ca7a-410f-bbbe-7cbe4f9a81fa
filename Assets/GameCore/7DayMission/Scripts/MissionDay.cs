using System;
using System.Collections.Generic;

[Serializable]
public class MissionDay {
    #region Fields

    //main 
    public List<Mission> missions = new List<Mission>();
    public MissionReward dayReward;
    public int totalReward = 1;

    //Process
    public int indexMissionDay;
    public bool isComplete;
    public bool isReceivedReward;

    #endregion

    #region Unity Methods

    #endregion

    #region Methods

    #endregion
}

public class SevenDayMissionData {
    public List<MissionDay> missionDays;
}