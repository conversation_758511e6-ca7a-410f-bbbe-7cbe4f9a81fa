using System;
using System.Collections.Generic;
using Inwave;
using UnityEngine;
using Random = UnityEngine.Random;

public class SevenDayMission : FastSingleton<SevenDayMission>, IMissionManager {
    #region Fields

    private bool                _isLoadedConfig;
    private SevenDayMissionData _sevenDayMissionData;

    private List<MissionDay> _missionDays => _sevenDayMissionData?.missionDays;
    private int        _currentMissionDay       = -1;
    private Mission    _pickOneUpdateMission    = null;
    private MissionDay _pickOneUpdateMissionDay = null;
    private int dayLogin => currentMissionDay + RemoteConfigBase.instance.SevenDayMission_DayStart;

    public int currentMissionDay {
        get {
            if (_currentMissionDay < 0) {
                DateTime dayOpenGame = Configuration.GetFirstOpenTime();
                _currentMissionDay = DateTime.Now.DayOfYear - dayOpenGame.DayOfYear;
                if (_currentMissionDay < 0) {
                    _currentMissionDay += 365;
                }

                if (RemoteConfigBase.instance.SevenDayMission_DayStart >= 0) {
                    _currentMissionDay -= RemoteConfigBase.instance.SevenDayMission_DayStart;
                }
            }

            //return 1;
            return _currentMissionDay;
        }
        set => _currentMissionDay = value;
    }

    #endregion

    #region Unity Methods

    private void Start() {
        Util.WaitRemoteConfigDone(Init);
    }

    #endregion

    private void Init() {
        //Inwave.Utils.ShowTimeAction(GetType().Name, System.Reflection.MethodBase.GetCurrentMethod()?.Name);

        if (RemoteConfigBase.instance.SevenDayMission_IsEnable) {
            if (!PlayerPrefs.HasKey($"currentMissionDay_{currentMissionDay}")) {
                PlayerPrefs.SetInt($"currentMissionDay_{currentMissionDay}", currentMissionDay);
                MissionTrackingEvents.mission_login(dayLogin, currentMissionDay);
            }

            ScheduleNotification();
        }
    }

    public void DoMission(MissionType missionType, string value = null) {
        DoMission(missionType, currentMissionDay, value);
    }

    private void DoMission(MissionType missionType, int currentDay, string value) { //day = 0 1 2 3 4 5 6
        List<MissionDay> missionDays = GetMissionDays();
        if (missionDays.IsNullOrEmpty()) {
            return;
        }

        int dayProcess = RemoteConfigBase.instance.SevenDayMission_UpdateMissionAllDays ? 6 : currentDay;
        dayProcess = Mathf.Min(dayProcess, missionDays.Count - 1);
        for (int dayIndex = 0; dayIndex <= dayProcess; dayIndex++) {
            MissionDay missionDay = missionDays[dayIndex];
            UpdateMissionDay(missionType, missionDay, value);
        }

        SaveData();
    }

    private void UpdateMissionDay(MissionType missionType, MissionDay missionDay, string value) {
        if (missionDay.isComplete) {
            return;
        }

        int totalMissionComplete = 0;
        int totalMissions = missionDay.missions.Count;
        for (int index = 0; index < totalMissions; index++) {
            Mission mission = missionDay.missions[index];
            if (!mission.isComplete && mission.missionType == missionType) {
                mission.DoMission(value, previousBallId: 0);
                if (mission.isComplete && !mission.isReceivedReward) {
                    if (currentMissionDay >= missionDay.indexMissionDay) {
                        _pickOneUpdateMission = mission;
                    }

                    MissionTrackingEvents.mission_subcomplete(index, totalMissions, missionDay.indexMissionDay);
                }
            }

            if (mission.isComplete) {
                totalMissionComplete++;
            }
        }

        if (totalMissionComplete == totalMissions && !missionDay.isComplete && !missionDay.isReceivedReward) {
            missionDay.isComplete = true;
            MissionTrackingEvents.mission_daycomplete(missionDay.indexMissionDay, dayLogin);

            //Notifications.instance.CancelNotification(Notifications.id_SevenDayMission_noti_day1);
            LocalPushManager.Instance.Unschedule(NotificationInit.id_SevenDayMission_noti_day1);

            if (currentMissionDay >= missionDay.indexMissionDay) {
                _pickOneUpdateMissionDay = missionDay;
            }
        }

        if (_pickOneUpdateMission != null && _pickOneUpdateMission.isReceivedReward) {
            _pickOneUpdateMission = null;
        }

        if (_pickOneUpdateMissionDay != null && _pickOneUpdateMissionDay.isReceivedReward) {
            _pickOneUpdateMissionDay = null;
        }
    }

    private void LoadData() {
        if (_isLoadedConfig) {
            return;
        }

        string strMissionDays = PlayerPrefs.GetString("SevenDayMission");
        if (string.IsNullOrEmpty(strMissionDays)) {
            List<MissionDay> missionDays = new List<MissionDay>();
            string config = RemoteConfigBase.instance.SevenDayMission_MissionDays;
            if (!string.IsNullOrEmpty(config)) {
                string[,] grid = CSVReader.SplitCsvGrid(config.Replace("||", "\n"), true);
                missionDays = MissionProcessData.GetListMissionDays(grid);
                if (missionDays.Count > 0) {
                    _sevenDayMissionData = new SevenDayMissionData {
                        missionDays = missionDays
                    };
                    SaveData();
                } else {
                    CustomException.Fire("[SevenDayMission] LoadData", "Cannot get config from " + config);
                }
            }

        } else {
            _sevenDayMissionData = JsonUtility.FromJson<SevenDayMissionData>(strMissionDays);
        }

        _isLoadedConfig = true;
    }

    public void SaveData() {
        if (_sevenDayMissionData == null || _missionDays == null || _missionDays.Count == 0) {
            LoadData();
        }

        string strMissionDays = JsonUtility.ToJson(_sevenDayMissionData);
        PlayerPrefs.SetString("SevenDayMission", strMissionDays);
    }

    public List<MissionDay> GetMissionDays() {
        if (_sevenDayMissionData == null || _missionDays == null || _missionDays.Count == 0) {
            LoadData();
        }

        return _missionDays;
    }

    public int GetNotificationCount() {
        List<MissionDay> missionDays = GetMissionDays();
        if (missionDays.IsNullOrEmpty()) {
            return 0;
        }

        int count = 0;
        int indexDay = Mathf.Min(missionDays.Count - 1, currentMissionDay);
        for (int index = 0; index <= indexDay; index++) {
            MissionDay missionDay = missionDays[index];

            //Count day mission isComplete
            if (missionDay.isComplete && !missionDay.isReceivedReward) {
                count++;
            }

            //Count single mission isComplete
            List<Mission> missions = missionDay.missions;
            foreach (Mission mission in missions) {
                if (mission.isComplete && !mission.isReceivedReward) {
                    count++;
                }
            }
        }

        return count;
    }

    public void ShowPopupNotification(Action callbackClose = null, bool isShowSingleMission = true) {
        //Show single mission
        if (isShowSingleMission) {
            ShowSingleMission();
        }

        //Show day mission
        if (_pickOneUpdateMissionDay != null && VipMissionScript.instance == null) {
            SevenDayMissionUI.ShowRewardDayPopup(_pickOneUpdateMissionDay, true, null, callbackClose);
            _pickOneUpdateMissionDay = null;
        } else {
            // không show popup gì
            callbackClose?.Invoke();
        }
    }

    public void ShowSingleMission() {
        if (_pickOneUpdateMission != null && MissionNotice.instance == null) {
            GameObject popup = Util.ShowPopUp(PopupName.MissionNotice);
            popup.GetComponent<MissionNotice>().SetMission(_pickOneUpdateMission);
            _pickOneUpdateMission = null;
        }
    }

    public bool HasNotification() {
        return _pickOneUpdateMission != null || _pickOneUpdateMissionDay != null;
    }

    public bool IsEnable() {
        if (!RemoteConfigBase.isInstanced || !RemoteConfigBase.instance.SevenDayMission_IsEnable) {
            return false;
        }

        if (currentMissionDay < 0 || currentMissionDay > RemoteConfigBase.instance.SevenDayMission_DayEnd) {
            return false;
        }

        List<MissionDay> missionDays = GetMissionDays();
        return !missionDays.IsNullOrEmpty();
    }

    public void LogSongMission(SONG_STATUS songStatus, Song song) {
        try {
        if (!IsEnable() || song.isTutorialSong) {
            return;
        }

        switch (songStatus) {
            case SONG_STATUS.song_start:
                DoMission(MissionType.start_x_songs);
                bool enableEndless = Configuration.IsEndlessModeEnable(song.path);
                if (enableEndless) {
                    DoMission(MissionType.play_x_songs_in_endless_mode);
                }

                break;

            case SONG_STATUS.song_result:
                if (GameController.instance.stars >= 1) {
                    DoMission(MissionType.play_x_songs_and_get_at_least_1_star);
                }

                if (GameController.instance.stars >= 2) {
                    DoMission(MissionType.play_x_songs_and_get_at_least_2_stars);
                }

                DoMission(MissionType.get_x_stars, GameController.instance.stars.ToString());
                DoMission(MissionType.play_x_songs_in_genre_y, song.GetStrGenres());
                DoMission(MissionType.get_at_least_x_point, GameController.instance.Score.ToString());

                break;

            case SONG_STATUS.song_end:
                DoMission(MissionType.finish_x_songs);

                break;

            case SONG_STATUS.song_replay:
                DoMission(MissionType.replay_x_times);

                break;

            case SONG_STATUS.song_revive:
                DoMission(MissionType.continue_x_times);

                break;

            case SONG_STATUS.song_unlock:
                DoMission(MissionType.unlock_x_songs);
                break;
        }
        } catch (Exception e) {
            CustomException.Fire("[LogSongMission]", e.Message);
    }
    }

    private void ScheduleNotification() {
        if (!IsEnable()) {
            return;
        }

        int timePushNotiDay1 = RemoteConfigBase.instance.SevenDayMission_TimePushNotiDay1;
        string title = Application.productName;

        if (!PlayerPrefs.HasKey("7DayMission_noti_day1") && timePushNotiDay1 > 0) {
            PlayerPrefs.SetInt("7DayMission_noti_day1", 1);

            string body = "You haven’t finished Day 1 Mission. Come back and finish it to get reward!!!";
            //Notifications.instance.ScheduleNotification(DateTime.Now.AddHours(timePushNotiDay1), title, body, Notifications.id_SevenDayMission_noti_day1);
            NotificationInit.ScheduleNotification(DateTime.Now.AddHours(timePushNotiDay1), title, body,
                NotificationInit.id_SevenDayMission_noti_day1);
        }

        string missionNotiDay = "7DayMission_noti_day_2_4_7_" + Util.GetCurrentDay();
        if (!PlayerPrefs.HasKey(missionNotiDay)) {
            PlayerPrefs.SetInt(missionNotiDay, 1);

            // Notifications.instance.CancelNotification(Notifications.id_SevenDayMission_noti_day2);
            // Notifications.instance.CancelNotification(Notifications.id_SevenDayMission_noti_day4);
            // Notifications.instance.CancelNotification(Notifications.id_SevenDayMission_noti_day7);
            LocalPushManager.Instance.Unschedule(NotificationInit.id_SevenDayMission_noti_day2);
            LocalPushManager.Instance.Unschedule(NotificationInit.id_SevenDayMission_noti_day4);
            LocalPushManager.Instance.Unschedule(NotificationInit.id_SevenDayMission_noti_day7);

            List<string> push = new List<string>() {
                "7-Day Missions is still waiting for you, special rewards too",
                "The only chance to get special rewards by finishing 7-Day Missions",
                "Songs and special skins can be get only via 7-Day Missions. Let’s do it!"
            };

            string push1 = push[Random.Range(0, push.Count)];
            push.Remove(push1);
            string push2 = push[Random.Range(0, push.Count)];
            push.Remove(push2);
            string push3 = push[0];

            // Notifications.instance.ScheduleNotification(DateTime.Now.AddDays(1), title, push1, Notifications.id_SevenDayMission_noti_day2);
            // Notifications.instance.ScheduleNotification(DateTime.Now.AddDays(3), title, push2, Notifications.id_SevenDayMission_noti_day4);
            // Notifications.instance.ScheduleNotification(DateTime.Now.AddDays(6), title, push3, Notifications.id_SevenDayMission_noti_day7);
            NotificationInit.ScheduleNotification(DateTime.Now.AddDays(1), title, push1,
                NotificationInit.id_SevenDayMission_noti_day2);
            NotificationInit.ScheduleNotification(DateTime.Now.AddDays(3), title, push2,
                NotificationInit.id_SevenDayMission_noti_day4);
            NotificationInit.ScheduleNotification(DateTime.Now.AddDays(6), title, push3,
                NotificationInit.id_SevenDayMission_noti_day7);
        }
    }
}