using System;
using System.Collections;
using System.Threading.Tasks;
using TilesHop.Cores.Pooling;
using Unity.Collections;
using UnityEditor;
using UnityEngine;

namespace TilesHop.ToolScanSongInformation {
    public class SongCSVHandleJob {
        private SongCSVData[] result;

        private event Action<int, int> progress;
        private event Action           complete;

        private int    _total;

        private string _strError;
        private bool   _isSuccess;

        private Coroutine _coroutineScanData;

        public void Execute(ref SongCSVData[] result, Action<int, int> progress, Action complelte) {
            this.progress = progress;
            this.complete = complelte;
            this._total = result.Length;
            this.result = result;
            _coroutineScanData = Configuration.instance.StartCoroutine(IEScanData());
        }

        private IEnumerator IEScanData() {
            for (int i = 0; i < _total; i++) {
                yield return IEDownloadSong(i, result[i].song);
                yield return YieldPool.GetWaitForEndOfFrame();
                progress?.Invoke(i, _total);
            }

            complete?.Invoke();
        }


        private IEnumerator IEDownloadSong(int index, Song song) {
            //throw new NotImplementedException();
            bool newData = DownloadManager.instanceSafe.CheckDataAndDownload(song);
            _isSuccess = true;
            if (newData) {
                while (DownloadManager.instanceSafe.IsNotReady() && _isSuccess) {
                    _strError = DownloadManager.instanceSafe.GetError();
                    if (_strError != null) {
                        _isSuccess = false;
                        yield break;
                    }

                    yield return null;
                }
            } else {
                while (DownloadManager.instanceSafe.IsNotReady() && _isSuccess) {
                    yield return null;
                }
            }

            SuperpoweredSDK.instance.LoadMusic(song);
            while (!SuperpoweredSDK.instance.IsReady()) {
                yield return null;
            }
            result[index].Duration = SuperpoweredSDK.instance.musicLength;


            if (_isSuccess) {
                //Read data midi
                if (string.IsNullOrEmpty(song.midiLocalPath) || !NotesManager.instance.IsLoadedNoteData(song)) {
                    yield return NotesManager.instance.LoadNoteData(song);
                }

                if (NotesManager.instance.noteCount != 0) {
                    var analyzeData = NotesManager.instance.AnalyzeData();

                    result[index].MusicalizationType = analyzeData.midiType;
                    result[index].TotalNote = analyzeData.totalNote;

                    result[index].LongNote = analyzeData.longNote;
                    result[index].ShortNote = analyzeData.shortNote;
                    result[index].StrongNote = analyzeData.strongNote;
                    result[index].LongNoteDuration = analyzeData.longNoteDuration;
                    result[index].MoodChange = analyzeData.moodchange;
                    Logger.EditorLog("Element Note", $"is_new_element: {analyzeData.is_new_element}");
                    // Logger.EditorLogError("Scan",
                    //     $"total {analyzeData.totalNote} long {analyzeData.longNote} short {analyzeData.shortNote} strong {analyzeData.strongNote} ");
                } else {
                    Logger.EditorLogError("Scan", $"note data count == 0. song {index}.{song.name}");
                }
            } else {
                Logger.EditorLogError("Scan", $"can't download song. song {index}.{song.name}");
            }

            SuperpoweredSDK.instance.UnloadMusic();
            yield return null;
        }

        public void Cancel() {
            if (_coroutineScanData != null) {
                Configuration.instance.StopCoroutine(_coroutineScanData);
            }
        }
    }
}