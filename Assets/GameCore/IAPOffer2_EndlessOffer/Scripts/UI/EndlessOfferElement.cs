using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using TilesHop;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.EventTracking;
using UnityEngine;
using UnityEngine.UI;

namespace GameCore.EndlessOffer {
    public class EndlessOfferElement : Mono<PERSON>ehaviour, ITrackableIapStatus {
        [SerializeField] private Image                     bgNormal, bgHighlight;
        [SerializeField] private Button                    btnFree,  btnAds;
        [SerializeField] private Text                      txtAds;
        [SerializeField] private IapEndlessOfferBuyButton  btnBuy;
        [SerializeField] private Image                     iconLock;
        [SerializeField] private Image                     iconToken;
        [SerializeField] private Text                      txtToken;
        [SerializeField] private CanvasGroup               rewardGroup;
        [SerializeField] private GameObject                multipleRewardsHolder;
        [SerializeField] private EndlessOfferRewardItem[]  multipleRewards;
        [SerializeField] private EndlessOfferRewardItem    singleReward;
        [SerializeField] private EndlessOfferTooltipReward tooltip;
        [SerializeField] private GameObject                chestReward;
        [SerializeField] private Transform                 btnHolder;
        [SerializeField] private Transform                 iconTick;

        [SerializeField] private Image imgToken;

        [SerializeField] private Sprite spriteLockBg;
        [SerializeField] private Sprite spriteLockBgToken;
        [SerializeField] private Sprite spriteUnlockBg;
        [SerializeField] private Sprite spriteUnlockBgToken;

        private bool _hasToken;

        public Action<List<RewardItem>, EndlessOfferElement> onGotReward;
        public static Action           onClickLock;
        private       EndlessOfferPack _pack;
        private       List<RewardItem> _convertRewards = new();
        private       int              _index;
        private       List<int>        _listReplace = new();

        private IapStatusTrackingData statusTrackingData;

        public Transform btnFreeTransform => btnFree.transform;

        private bool _inited;

        private void Awake() {
            btnFree.onClick.AddListener(OnClickBtnFree);
            btnAds.onClick.AddListener(OnClickBtnAds);
        }

		private void OnEnable() {
            rewardGroup.alpha = 1;
            StartCoroutine(IEUpdateUIReward());
		}

		private void OnDisable() {
			IapBase.OnInited -= UpdateStatusTrackingData;
		}

		public void InitData(bool isForceUpdate ,int index, EndlessOfferPack pack, int step, Action<List<RewardItem>, EndlessOfferElement> onBought) {
            if (_pack == pack) {
                if (!isForceUpdate) {
                    return;
                }
            }
            _pack = pack;

            UpdateConvertedRewards();
            onGotReward = null;
            onGotReward = onBought;
            _hasToken = _pack.token > 0;

            statusTrackingData = new IapStatusTrackingData() {
                groupOfferId = EndlessOffer.instanceSafe.GetGroupOfferId(),
                packageId = null,
                stage = step.ToString(),
                claimType = TrackClaimType.free,
                location = EndlessOffer.instanceSafe.userData.popupLocation,
                value = 0,
                currency = null,
            };
            if (Enum.TryParse(pack.claimType, out ClaimType claimType)) {
                switch (claimType) {
                    case ClaimType.Free:
                        btnBuy.gameObject.SetActive(false);
                        btnFree.gameObject.SetActive(true);
                        btnAds.gameObject.SetActive(false);
                        statusTrackingData.claimType = TrackClaimType.free;
                        break;

                    case ClaimType.Ads:
                        txtAds.text = $"{EndlessOffer.instanceSafe.userData.currentWatchedAds}/{_pack.adCosting}";
                        btnBuy.gameObject.SetActive(false);
                        btnFree.gameObject.SetActive(false);
                        btnAds.gameObject.SetActive(true);
                        statusTrackingData.claimType = TrackClaimType.ads;
                        break;

                    case ClaimType.IAP:
                        btnBuy.Init(pack.productId, OnClickBtnIap, OnFailIap, OnSuccessIap);
                        btnBuy.gameObject.SetActive(true);
                        btnFree.gameObject.SetActive(false);
                        btnAds.gameObject.SetActive(false);

                        statusTrackingData.claimType = TrackClaimType.iap_purchase;
                        statusTrackingData.packageId = pack.productId;
						if (IapBase.IsInited || Application.isEditor) {
                            UpdateStatusTrackingData();
                        } else {
                            IapBase.OnInited += UpdateStatusTrackingData;
                        }

                        break;

                    default:
                        btnBuy.gameObject.SetActive(false);
                        btnFree.gameObject.SetActive(false);
                        btnAds.gameObject.SetActive(false);
                        txtAds.text = "Error!";
                        break;
                }
            } else {
                CustomException.Fire("Endless Offer", "");
            }

            TrackShow(EndlessOffer.DEAL_TYPE, statusTrackingData);
            _inited = true;
		}

        private void UpdateConvertedRewards() {
            _convertRewards.Clear();

            for (int i = 0; i < _pack.rewards.Count; i++) {
				var item = _pack.rewards[i].ConvertToRewardItem();
                if (item != null) {
                    switch (item.type) {
                        case RewardType.Theme:
                            ConvertThemeReward(item.valueStr);
                            break;

                        case RewardType.Skin:
                            ConvertBallReward(item.valueStr);
                            break;
                        case RewardType.Song:
                            ConvertSongReward(item.valueStr);
                            break;

                        default:
                            _convertRewards.Add(new RewardItem(item.type, item.valueStr, item.valueInt));
                            break;
					}
                }

			}
		}

        private void ConvertThemeReward(string value) {
			List<int> themeIDs = Helper.ConvertRewardIntPool(value);
			int selectedTheme = -1;
			for (int j = 0; j < themeIDs.Count; j++) {
                if (!Configuration.IsEarnTheme(themeIDs[j]) &&
                    !EndlessOffer.instanceSafe.currentThemes.Contains(themeIDs[j]) &&
                    ThemeManager.supportThemeList.ContainsKey(themeIDs[j])) {
					selectedTheme = themeIDs[j];
					break;
				}
			}

			if (selectedTheme == -1) {
				RewardItem itemGem = null;
				for (int i = 0; i < _convertRewards.Count; i++) {
					if (_convertRewards[i].type == RewardType.Gem) {
						itemGem = _convertRewards[i];
						break;
					}
				}
				if (itemGem != null) {
					itemGem.valueInt += RemoteConfig.instance.StarsJourney_ReplaceGem;
				} else {
					_convertRewards.Add(new RewardItem(RewardType.Gem, RemoteConfig.instance.StarsJourney_ReplaceGem));
				}
			} else {
				_convertRewards.Add(new RewardItem(RewardType.Theme, selectedTheme));
				EndlessOffer.instanceSafe.currentThemes.Add(selectedTheme);
			}
		}

        private void ConvertSongReward(string value) {
            List<string> songIDs = Helper.ConvertRewardStringPool(value);
            string selectedSong = string.Empty;
            Song song = null;
            for (int j = 0; j < songIDs.Count; j++) {
				song = SongManager.instance.GetSongByAcmId(songIDs[j]);
				if (song != null && song.savedType != SONGTYPE.OPEN && !EndlessOffer.instanceSafe.currentSongs.Contains(songIDs[j])){
                     selectedSong = songIDs[j];
                    break;
                }
            }

            if (string.IsNullOrEmpty(selectedSong)) {
                RewardItem itemGem = null;
                for (int i = 0; i < _convertRewards.Count; i++) {
                    if(_convertRewards[i].type == RewardType.Gem) {
                        itemGem = _convertRewards[i];
                        break;
                    }
                }

				if (itemGem != null) {
					itemGem.valueInt += RemoteConfig.instance.StarsJourney_ReplaceGem;
				} else {
					_convertRewards.Add(new RewardItem(RewardType.Gem, RemoteConfig.instance.StarsJourney_ReplaceGem));
				}
			} else {
                _convertRewards.Add(new RewardItem(RewardType.Song, selectedSong));
                EndlessOffer.instanceSafe.currentSongs.Add(selectedSong);
            }
        }

        private void ConvertBallReward(string value) {
			List<int> ballIDs = Helper.ConvertRewardIntPool(value);
			int selectedBall = -1;
			for (int j = 0; j < ballIDs.Count; j++) {
                if (!Configuration.IsEarnBall(ballIDs[j]) &&
                    !EndlessOffer.instanceSafe.currentBalls.Contains(ballIDs[j])) {
					selectedBall = ballIDs[j];
					break;
				}
			}

			if (selectedBall == -1) {
				RewardItem itemGem = null;
				for (int i = 0; i < _convertRewards.Count; i++) {
					if (_convertRewards[i].type == RewardType.Gem) {
						itemGem = _convertRewards[i];
						break;
					}
				}
				if (itemGem != null) {
                    itemGem.valueInt += RemoteConfig.instance.StarsJourney_ReplaceGem;
                } else {
					_convertRewards.Add(new RewardItem(RewardType.Gem, RemoteConfig.instance.StarsJourney_ReplaceGem));
				}
			} else {
				_convertRewards.Add(new RewardItem(RewardType.Skin, selectedBall));
				EndlessOffer.instanceSafe.currentBalls.Add(selectedBall);
			}
		}

        private void UpdateStatusTrackingData() {
            statusTrackingData.value = IapBase.GetPriceValue(statusTrackingData.packageId);
            statusTrackingData.currency = IapBase.GetPriceCode(statusTrackingData.packageId);
        }

        private void OnClickBtnIap() {
            TrackClick(EndlessOffer.DEAL_TYPE, statusTrackingData);
        }

        private void OnFailIap() {
            TrackFail(EndlessOffer.DEAL_TYPE, statusTrackingData);
        }

        private void OnSuccessIap() {
            TrackSuccess(EndlessOffer.DEAL_TYPE, statusTrackingData);
            EarnReward(CurrencyEarnSource.iap_endless_offer, BoosterEarnSource.iap_endless_offer);
            SoundManager.PlayBallPurchased();
            EndlessOffer.BoughtItem(_pack.productId);
        }

        public void UpdateStatus(int index) {
            bool isHighlight = EndlessOffer.instanceSafe.userData.curentPackIndex %
                EndlessOffer.instanceSafe.config.packs.Length == _pack.id;

            iconLock.gameObject.SetActive(!isHighlight);
            bgHighlight.gameObject.SetActive(isHighlight);
            bgHighlight.sprite = _hasToken ? spriteUnlockBgToken : spriteUnlockBg; 
            bgNormal.gameObject.SetActive(!isHighlight);
            bgNormal.sprite = _hasToken ? spriteLockBgToken : spriteLockBg;
            btnBuy.onClickLock = isHighlight ? null : onClickLock;

            txtAds.text = $"{EndlessOffer.instanceSafe.userData.currentWatchedAds}/{_pack.adCosting}";
            btnHolder.localScale = Vector3.one;
            iconTick.localScale = Vector3.zero;

            _index = index;
            switch (_index) {
                case 0:
                case 3:
                case 4:
                    tooltip.Reverse(false);
                    break;

                default:
                    tooltip.Reverse(true);
                    break;
            }
        }

        private IEnumerator IEUpdateUIReward() {
            while (!_inited) {
                yield return null;
            }
            if (_pack.token > 0) {
                txtToken.text = _pack.token.ToString();
                iconToken.gameObject.SetActive(true);
            } else {
                iconToken.gameObject.SetActive(false);
            }

            if (_convertRewards.Count == 1) {
                chestReward.SetActive(false);
                multipleRewardsHolder.SetActive(false);
                singleReward.gameObject.SetActive(true);
                singleReward.Init(_convertRewards[0]);
            } else if (_convertRewards.Count <= 3) {
                for (int i = 0; i < multipleRewards.Length; i++) {
                    if (i > _convertRewards.Count - 1) {
                        multipleRewards[i].gameObject.SetActive(false);
                    } else {
                        multipleRewards[i].Init(_convertRewards[i]);
                        multipleRewards[i].gameObject.SetActive(true);
                        multipleRewards[i].transform.SetScale(_convertRewards.Count == 2 ? 1.2f : 1f);
                    }
                }

                multipleRewardsHolder.SetActive(true);
                singleReward.gameObject.SetActive(false);
                chestReward.SetActive(false);
            } else {
                multipleRewardsHolder.SetActive(false);
                singleReward.gameObject.SetActive(false);
                chestReward.SetActive(true);
                tooltip.Init(_convertRewards);
            }
        }

        private void EarnReward(CurrencyEarnSource earnSource, string boosterSource) {
            // Handle data
            EndlessOffer.instanceSafe.userData.curentPackIndex++;
            EndlessOffer.instanceSafe.userData.currentWatchedAds = 0;

            _listReplace.Clear();
            for (int i = 0; i < _convertRewards.Count; i++) {
                var item = _convertRewards[i];
                IapRewardTrackingData rewardTrackingData = new IapRewardTrackingData(item);
                switch (item.type) {
                    case RewardType.Song:
                        Song song = null;
                        string acm_id = item.valueStr;
                        if (!string.IsNullOrEmpty(acm_id)) {
                            song = SongManager.instance.GetSongByAcmId(acm_id);
                        }

                        if (song == null || Configuration.instance.IsOpenSong(song.path)) {
                            _listReplace.Add(i);
                            Configuration.UpdateDiamond(RemoteConfig.instance.StarsJourney_ReplaceGem,
                                earnSource.ToString());
                        } else {
                            Configuration.instance.SetOpenSong(song, 0, true, SongUnlockType.endless_offer);
                        }

                        break;

                    case RewardType.Skin:
                        int ballId = item.valueInt;
                        if (ballId <= 0 || Configuration.IsEarnBall(ballId)) {
                            _listReplace.Add(i);
                            Configuration.UpdateDiamond(RemoteConfig.instance.StarsJourney_ReplaceGem,
                                earnSource.ToString());
                        } else {
                            Configuration.SetOpenBall(ballId, 0, earnSource.ToString(), true, false);
                        }

                        break;

                    case RewardType.Theme:
                        int idTheme = item.valueInt;
                        if (idTheme <= 0 || Configuration.IsEarnTheme(idTheme)) {
                            _listReplace.Add(i);
                            Configuration.UpdateDiamond(RemoteConfig.instance.StarsJourney_ReplaceGem,
                                earnSource.ToString());
                        } else {
                            ThemeManager.instance.SetOpenTheme(idTheme, 0);
                        }

                        break;

                    case RewardType.Gem:
                        Configuration.UpdateDiamond(item.valueInt, earnSource.ToString());
                        break;

                    case RewardType.Booster:
                        BoosterManager.AddItemAmount(
                            (BoosterType) Enum.Parse(typeof(BoosterType), _pack.rewards[i].valueStr), boosterSource,
                            item.valueInt);
                        break;

                    case RewardType.UnlimitedRevive:
                        UnlimitedReviveManager.IncreaseUnlimitedReviveTime(item.valueInt);
                        break;

                    default:
                        Logger.EditorLogError($"Unknown reward type: {item.type} to reward");
                        break;
                }

                TrackClaim(EndlessOffer.DEAL_TYPE, ref statusTrackingData, ref rewardTrackingData);
            }

            EndlessOffer.instanceSafe.SaveStateData();

            //effect
            if (singleReward.gameObject.activeInHierarchy) {
                if(singleReward.Type == RewardType.Gem || singleReward.Type == RewardType.Booster) {
					EndlessOfferRewardItem item = Instantiate(singleReward, transform.parent);
					if (_listReplace.Count > 0) {
						RewardItem reward = new RewardItem(RewardType.Gem, RemoteConfig.instance.StarsJourney_ReplaceGem);
						item.Init(reward);
					}

					item.transform.position = singleReward.transform.position + Vector3.up * 0.5f;
					item.FlyEffect();
				}
            } else if (multipleRewardsHolder.activeInHierarchy) {
                for (int i = 0; i < multipleRewards.Length; i++) {
                    if (multipleRewards[i].gameObject.activeInHierarchy) {
                        if (multipleRewards[i].Type == RewardType.Gem || multipleRewards[i].Type == RewardType.Booster) {
							EndlessOfferRewardItem item = Instantiate(multipleRewards[i], transform.parent);
							if (_listReplace.Contains(i)) {
								RewardItem reward = new RewardItem(RewardType.Gem,
									RemoteConfig.instance.StarsJourney_ReplaceGem);
								item.Init(reward);
							}

							item.transform.position = multipleRewards[i].transform.position + Vector3.up * 0.5f;
							item.FlyEffect();
						}
                    }
                }
            } else {
                List<EndlessOfferRewardItem> items = new();
                for (int i = 0; i < tooltip.items.Length; i++) {
                    if (tooltip.items[i].gameObject.activeSelf) {
                        if (tooltip.items[i].Type == RewardType.Gem || tooltip.items[i].Type == RewardType.Booster) {
							EndlessOfferRewardItem item = Instantiate(tooltip.items[i], transform.parent);
							if (_listReplace.Contains(i)) {
								RewardItem reward = new RewardItem(RewardType.Gem,
									RemoteConfig.instance.StarsJourney_ReplaceGem);
								item.Init(reward);
							}

							items.Add(item);
						}
                    }
                }

                float centerIndex = (items.Count - 1) / 2; 

                for (int i = 0; i < items.Count; i++) {
                    items[i].transform.position =
                        chestReward.transform.position + new Vector3((i - centerIndex) * 1, 1, 0);
                    items[i].FlyEffect();
                }
            }

            rewardGroup.DOFade(0, 0.5f);

			btnHolder.DOScale(0f, 0.3f).SetEase(Ease.OutBack);
			iconTick.DOScale(1f, 0.3f).SetEase(Ease.OutBack);

			onGotReward?.Invoke(_convertRewards, this);
		}

        public bool CheckEarnToken() {
			if (_pack.token > 0) {
				IapMiniMilestoneTrackingData milestone = new IapMiniMilestoneTrackingData() {
					milestoneEarned = _pack.token,
					milestoneAchieved = 0,
				};
				TrackClaimMiniMileStone(ref milestone);
				bool rewardMileStone = EndlessOffer.instanceSafe.IncreaseMilestoneProgress(_pack.token);
				if (rewardMileStone) {
					milestone.milestoneEarned = 0;
					milestone.milestoneAchieved = EndlessOffer.instanceSafe.userData.currentMilestone;
					var rewards =
						EndlessOffer.instanceSafe.GetRewardMileStone(
							EndlessOffer.instanceSafe.userData.currentMilestone - 1);
					foreach (var reward in rewards) {
						IapRewardTrackingData rewardTrackingData =
							new IapRewardTrackingData(reward.ConvertToRewardItem());
						milestone.milestomeRewardType = rewardTrackingData.rewardType;
						milestone.milestomeRewardAmount = rewardTrackingData.amount;
						TrackClaimMiniMileStone(ref milestone);
					}
				}

				EndlessOffer.instanceSafe.SaveStateData();

				if (iconToken.gameObject.activeInHierarchy) {
					GameObject token = Instantiate(iconToken, transform.parent).gameObject;
					iconToken.gameObject.SetActive(false);
					token.transform.position = iconToken.transform.position + Vector3.up * 0.5f;
					token.transform.DOScale(1.3f, 0.3f).SetEase(Ease.OutBack).From(0);
					token.transform.DOJump(EndlessOfferMilestone.tokenTransform.position, 1, 1, 0.3f).SetDelay(0.5f)
						.OnComplete(() => {
							SoundManager.PlayEndlessOfferCollectToken();
							Destroy(token);
						});
				}
				return true;
            } else {
                return false;
            }
		}

        private void OnClickBtnFree() {
            if (iconLock.gameObject.activeInHierarchy) {
                onClickLock?.Invoke();
                return;
            }

			SoundManager.PlayGameButton();
			TrackClick(EndlessOffer.DEAL_TYPE, statusTrackingData);
            TrackSuccess(EndlessOffer.DEAL_TYPE, statusTrackingData);
            EarnReward(CurrencyEarnSource.free_endless_offer, BoosterEarnSource.free_endless_offer);
        }

        private void OnClickBtnAds() {
            if (iconLock.gameObject.activeInHierarchy) {
                onClickLock?.Invoke();
                return;
            }

			SoundManager.PlayGameButton();
			TrackClick(EndlessOffer.DEAL_TYPE, statusTrackingData);

            var songReward = GetSongReward();
            
            AdsManager.instance.ShowRewardAds(VIDEOREWARD.endless_offer_reward, songReward, "", true, (isSuccess) => {
                if (isSuccess) {
                    EndlessOffer.instanceSafe.userData.currentWatchedAds++;
                    if (EndlessOffer.instanceSafe.userData.currentWatchedAds >= _pack.adCosting) {
                        TrackSuccess(EndlessOffer.DEAL_TYPE, statusTrackingData);
                        EarnReward(CurrencyEarnSource.ads_endless_offer, BoosterEarnSource.ads_endless_offer);
                    } else {
                        UpdateStatus(_index);
                    }
                } else {
                    TrackFail(EndlessOffer.DEAL_TYPE, statusTrackingData);
                }
            });
        }

        private Song GetSongReward() {
            List<Song> rewards = new List<Song>();
            foreach (RewardItem item in _convertRewards) {
                switch (item.type) {
                    case RewardType.Song:
                        Song song = null;
                        string acmID = item.valueStr;
                        if (string.IsNullOrEmpty(acmID)) {
                            break;
                        }
                        song = SongManager.instance.GetSongByAcmId(acmID);
                        if (song != null) {
                            rewards.Add(song);
                        }
                        break;
                }
            }

            return rewards.Count == 1 ? rewards[0] : null;
        }

        public void OnLoadedToken(Sprite sprite) {
            imgToken.sprite = sprite;
        }

        public void OnLoadedButton(Sprite sprite) {
            btnBuy.SetButton(sprite);
            btnFree.image.sprite = sprite;
            btnAds.image.sprite = sprite;
        }

        public void OnLoadedLockIcon(Sprite sprite) {
            iconLock.sprite = sprite;
        }

        public void OnLoadedLockedCell(Sprite sprite) {
            spriteLockBg = sprite;
            if (!_hasToken) {
                bgNormal.sprite = sprite;
            }
        }

        public void OnLoadedLockedCellToken(Sprite sprite) {
            spriteLockBgToken = sprite;
            if (_hasToken) {
                bgNormal.sprite = sprite;
            }
        }

        public void OnLoadedUnlockedCell(Sprite sprite) {
            spriteUnlockBg = sprite;
            if (!_hasToken) {
                bgHighlight.sprite = sprite;
            }
        }

        public void OnLoadedUnlockedCellToken(Sprite sprite) {
            spriteUnlockBgToken = sprite;
            if (_hasToken) {
                bgHighlight.sprite = sprite;
            }
        }

        public void OnLoadedChest(Sprite sprite) {
            if (chestReward.TryGetComponent(out Image image)) {
                image.sprite = sprite;
            }
        }

        #region Event Tracking

        public void TrackShow(string dealType, IapStatusTrackingData statusData) {
            BaseIapPackageTracking.TrackIapShow(dealType, statusData);
        }

        public void TrackClick(string dealType, IapStatusTrackingData statusData) {
            BaseIapPackageTracking.TrackIapClick(dealType, statusData);
        }

        public void TrackFail(string dealType, IapStatusTrackingData statusData) {
            BaseIapPackageTracking.TrackIapFail(dealType, statusData);
        }

        public void TrackSuccess(string dealType, IapStatusTrackingData statusData) {
            BaseIapPackageTracking.TrackIapSuccess(dealType, statusData);
        }

        public void TrackClaim(string dealType, ref IapStatusTrackingData statusData,
                               ref IapRewardTrackingData rewardData) {
            BaseIapPackageTracking.TrackIapClaim(dealType, ref statusData, ref rewardData);
        }

        public void TrackClaimMiniMileStone(ref IapMiniMilestoneTrackingData milestone) {
            BaseIapPackageTracking.TrackIapClaimMiniMileStone(ref milestone);
        }

        #endregion
    }
}