using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using TilesHop;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

namespace GameCore.EndlessOffer {
	public class EndlessOfferMilestone : MonoBehaviour {
		[SerializeField] private Image progressBg;
		[SerializeField] private Image progress;
		[SerializeField] private Text txtProgress;
		[SerializeField] private Image iconToken;
		[SerializeField] private EndlessOfferRewardItem rewardUI;
		[SerializeField] private GameObject chest;
		[SerializeField] private EndlessOfferTooltipReward tooltip;
		[SerializeField] private Transform rewardGroup;

		private EndlessOfferState userData => EndlessOffer.instanceSafe.userData;
		private EndlessOfferConfig _config => EndlessOffer.instanceSafe.config;

		private int _currentMilestone;
		private int _currentProgress;

		private Coroutine _coroutine;
		private List<RewardItemStr> _rewards;
		private List<RewardItem> _convertedRewards = new();

		public static Transform tokenTransform;

		private bool _needUpdateReward;

		public static Action<List<RewardItem>> onGotReward;

		private void Awake() {
			EndlessOfferRewardPopupUI.OnClose += CheckUpdateReward;
			if (!tokenTransform) {
				tokenTransform = iconToken.transform;
			}
		}

		private void OnEnable() {
			UpdateProgress();
			_currentMilestone = userData.currentMilestone;
			_currentProgress = userData.currentMilestoneProgress;
			_rewards = _config.miniMilestoneConfig.milestones[_currentMilestone % _config.miniMilestoneConfig.milestones.Count].rewards;

			UpdateRewardUI();
		}

		private void OnDisable() {
			DOTween.Kill(gameObject);
		}

		private void OnDestroy() {
			EndlessOfferRewardPopupUI.OnClose -= CheckUpdateReward;
		}

		private void UpdateRewardUI() {
			if (_rewards.Count == 1) {
				rewardUI.Init(_rewards[0].ConvertToRewardItem());
				chest.SetActive(false);
				rewardUI.gameObject.SetActive(true);
			} else {
				chest.SetActive(true);
				rewardUI.gameObject.SetActive(false);
				tooltip.Init(_rewards);
			}
		}

		private void UpdateProgress() {
			int currentNeedToken = _config.miniMilestoneConfig.milestones[userData.currentMilestone % _config.miniMilestoneConfig.milestones.Count].needToken;
			progress.fillAmount = userData.currentMilestoneProgress * 1f / currentNeedToken;
			txtProgress.text = $"{userData.currentMilestoneProgress}/{currentNeedToken}";
		}

		public IEnumerator IEIncreaseProgress() {
			if (userData.currentMilestone == _currentMilestone) {
				SoundManager.PlayEndlessOfferProgressUp();
				DOTween.To(() => _currentProgress, x => {
					_currentProgress = x;
					UpdateTextProgress(_currentProgress);
				}, userData.currentMilestoneProgress, 0.5f);

				progress.DOFillAmount(userData.currentMilestoneProgress * 1f / _config.miniMilestoneConfig.milestones[_currentMilestone % _config.miniMilestoneConfig.milestones.Count].needToken, 0.5f);
			} else {
				while(userData.currentMilestone > _currentMilestone) {
					SoundManager.PlayEndlessOfferProgressUp();
					DOTween.To(() => _currentProgress, x => {
						_currentProgress = x;
						UpdateTextProgress(_currentProgress);
					}, _config.miniMilestoneConfig.milestones[_currentMilestone % _config.miniMilestoneConfig.milestones.Count].needToken, 0.5f);

					progress.DOFillAmount(1f, 0.5f).OnComplete(EarnReward);
					yield return YieldPool.GetWaitForSeconds(0.5f);
					progress.DOFillAmount(0f, 0.05f);
					yield return YieldPool.GetWaitForSeconds(0.05f);
					_currentProgress = 0;
					_currentMilestone++;
					UpdateProgress();
				}

				if(userData.currentMilestoneProgress > 0) {
					SoundManager.PlayEndlessOfferProgressUp();
				}
				DOTween.To(() => _currentProgress, x => {
					_currentProgress = x;
					UpdateTextProgress(_currentProgress);
				}, userData.currentMilestoneProgress, 0.5f);
			}
		}

		private void UpdateTextProgress(float value) {
			txtProgress.text = $"{value}/{_config.miniMilestoneConfig.milestones[_currentMilestone % _config.miniMilestoneConfig.milestones.Count].needToken}";
		}

		private void EarnReward() {
			_needUpdateReward = true;
			var earnSource = CurrencyEarnSource.milestone_endless_offer;
			_convertedRewards.Clear();
			for (int i = 0; i < _rewards.Count; i++) {
				if (Enum.TryParse(_rewards[i].type, out RewardType type)) {
					switch (type) {
						case RewardType.Song:
							var song = SongManager.instance.GetSongByAcmId(_rewards[i].valueStr);
							Configuration.instance.SetOpenSong(song, 0, true, SongUnlockType.endless_offer);
							break;
						case RewardType.Skin:
							Configuration.SetOpenBall(int.Parse(_rewards[i].valueStr), 0, earnSource.ToString(), true, false);
							break;
						case RewardType.Theme:
							ThemeManager.instance.SetOpenTheme(int.Parse(_rewards[i].valueStr), 0);
							break;
						case RewardType.Gem:
							Configuration.UpdateDiamond(_rewards[i].valueInt, earnSource.ToString());
							break;
						case RewardType.Booster:
							BoosterManager.AddItemAmount((BoosterType)Enum.Parse(typeof(BoosterType), _rewards[i].valueStr), BoosterEarnSource.milestone_endless_offer, _rewards[i].valueInt);
							break;

						default:
							Logger.EditorLogError($"Unknown reward type {type}");
							break;
					}
					_convertedRewards.Add(_rewards[i].ConvertToRewardItem());
				}
			}

			if (chest.activeInHierarchy) {
				List<EndlessOfferRewardItem> items = new();
				for (int i = 0; i < tooltip.items.Length; i++) {
					if (tooltip.items[i].gameObject.activeSelf) {
						EndlessOfferRewardItem item = Instantiate(tooltip.items[i], transform);
						items.Add(item);
					}
				}

				float centerIndex = (items.Count - 1) / 2;

				for (int i = 0; i < items.Count; i++) {
					items[i].transform.position = chest.transform.position + new Vector3((i - centerIndex) * 0.5f, 0.5f, 0);
					items[i].FlyEffect();
				}
			} else {
				EndlessOfferRewardItem item = Instantiate(rewardUI, transform);
				item.transform.position = rewardUI.transform.position + Vector3.up * 0.5f;
				item.FlyEffect();
			}
			SoundManager.PlayEndlessOfferGetMilestoneReward();
			onGotReward?.Invoke(_convertedRewards);
		}

		private void CheckUpdateReward() {
			if (_needUpdateReward) {
				StartCoroutine(IEUpdateReward());
				_needUpdateReward = false;
			}
		}

		private IEnumerator IEUpdateReward() {
			rewardGroup.DOScale(0f, 0.5f).SetEase(Ease.OutBack);
			yield return YieldPool.GetWaitForSeconds(0.5f);
			_rewards = _config.miniMilestoneConfig.milestones[_currentMilestone % _config.miniMilestoneConfig.milestones.Count].rewards;

			UpdateRewardUI();
			rewardGroup.DOScale(1f, 0.75f).SetEase(Ease.OutBack);
		}
	}
}
