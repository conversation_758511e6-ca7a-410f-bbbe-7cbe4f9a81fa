using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

public class FeverUIBar : MonoBehaviour {
    [SerializeField] private Image         mainBar;
    [SerializeField] private Image         subBar;
    [SerializeField] private RectTransform point;

    [ShowInInspector, ReadOnly] private float _height;

    private const float Time = 0.15f;
    private       Tween _tweenPoint;
    private       Tween _tweenBar;
    private       Tween _tweenSubBar;

    public void Init(float height) {
        _height = height;
    }

    public void SetRatio(float ratio, bool isInstant, float time = 0f) {
        float high = _height * ratio;
        _tweenPoint?.Kill();
        _tweenBar.Kill();
        _tweenSubBar?.Kill();

        if (isInstant) {
            point.anchoredPosition = new Vector2(point.anchoredPosition.x, high);
            mainBar.fillAmount = ratio;
            subBar.fillAmount = ratio;
        } else {
            time = time == 0 ? Time : time;
            _tweenPoint = point.DOAnchorPosY(high, time);
            _tweenBar = mainBar.DOFillAmount(ratio, time);
            _tweenSubBar = subBar.DOFillAmount(ratio, time);
        }
    }

    public void SetActive(bool b) {
        gameObject.SetActive(b);
    }
}