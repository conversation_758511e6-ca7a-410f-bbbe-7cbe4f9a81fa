using System;
using System.Collections.Generic;
using System.Text;
using Sirenix.OdinInspector;

[Serializable]
public class StarsJourneyData {
    public int        ID;
    public int        Stars;
    public RewardType reward_1;
    public string     value_1;
    public RewardType reward_2;
    public string     value_2;
    public RewardType reward_3;
    public string     value_3;
    public RewardType reward_4;
    public string     value_4;

    [NonSerialized, ShowInInspector, ReadOnly]
    public int valueInt_1;

    [NonSerialized, ShowInInspector, ReadOnly]
    public int valueInt_2;

    [NonSerialized, ShowInInspector, ReadOnly]
    public int valueInt_3;

    [NonSerialized, ShowInInspector, ReadOnly]
    public int valueInt_4;

    public void ProcessData() {
        if (NeedConvertInt(reward_1)) {
            int.TryParse(value_1, out valueInt_1);
            if (valueInt_1 == 0) {
                Logger.EditorLogError($"Replace reward bcz wrong data:{ID} {reward_1} {value_1}");
                reward_1 = RewardType.Gem;
                valueInt_1 = RemoteConfig.instance.StarsJourney_ReplaceGem;
                value_1 = valueInt_1.ToString();
            }
        }

        if (NeedConvertInt(reward_2)) {
            int.TryParse(value_2, out valueInt_2);
            if (valueInt_2 == 0) {
                Logger.EditorLogError($"Replace reward bcz wrong data:{ID} {reward_2} {value_2}");
                reward_2 = RewardType.Gem;
                valueInt_2 = RemoteConfig.instance.StarsJourney_ReplaceGem;
                value_2 = valueInt_2.ToString();
            }
        }

        if (NeedConvertInt(reward_3)) {
            int.TryParse(value_3, out valueInt_3);
            if (valueInt_3 == 0) {
                Logger.EditorLogError($"Replace reward bcz wrong data:{ID} {reward_3} {value_3}");
                reward_3 = RewardType.Gem;
                valueInt_3 = RemoteConfig.instance.StarsJourney_ReplaceGem;
                value_3 = valueInt_3.ToString();
            }
        }

        if (NeedConvertInt(reward_4)) {
            int.TryParse(value_4, out valueInt_4);
            if (valueInt_4 == 0) {
                Logger.EditorLogError($"Replace reward bcz wrong data:{ID} {reward_4} {value_4}");
                reward_4 = RewardType.Gem;
                valueInt_4 = RemoteConfig.instance.StarsJourney_ReplaceGem;
                value_4 = valueInt_4.ToString();
            }
        }
    }

    private bool NeedConvertInt(RewardType type) {
        switch (type) {
            case RewardType.None:
            case RewardType.Song:
            case RewardType.Element:
            case RewardType.Booster:
                return false;
        }

        return true;
    }

    public string GetStrReward() {
        StringBuilder builder = new StringBuilder();
        if (reward_1 != RewardType.None) {
            builder.Append(reward_1);
            builder.Append(": ");
            builder.Append(value_1);
            builder.Append("; ");
        }

        if (reward_2 != RewardType.None) {
            builder.Append(reward_2);
            builder.Append(": ");
            builder.Append(value_2);
            builder.Append("; ");
        }

        if (reward_3 != RewardType.None) {
            builder.Append(reward_3);
            builder.Append(": ");
            builder.Append(value_3);
            builder.Append("; ");
        }

        if (reward_4 != RewardType.None) {
            builder.Append(reward_4);
            builder.Append(": ");
            builder.Append(value_4);
            builder.Append("; ");
        }

        return builder.ToString();
    }

    public void ValidateData() { }

    public NoteElementType GetElementReward() {
        if (reward_1 == RewardType.Element) {
            if (Enum.TryParse(value_1, out NoteElementType elementType)) {
                return elementType;
            }
        }

        if (reward_2 == RewardType.Element) {
            if (Enum.TryParse(value_2, out NoteElementType elementType)) {
                return elementType;
            }
        }

        if (reward_3 == RewardType.Element) {
            if (Enum.TryParse(value_3, out NoteElementType elementType)) {
                return elementType;
            }
        }

        if (reward_4 == RewardType.Element) {
            if (Enum.TryParse(value_4, out NoteElementType elementType)) {
                return elementType;
            }
        }

        return NoteElementType.None;
    }

    public bool ContainsRewardType(RewardType type) {
        return reward_1 == type || reward_2 == type || reward_3 == type || reward_4 == type;
    }

    public int GetGemReward() {
        int amount = 0;
        if (reward_1 == RewardType.Gem) {
            amount += valueInt_1;
        }

        if (reward_2 == RewardType.Gem) {
            amount += valueInt_2;
        }

        if (reward_3 == RewardType.Gem) {
            amount += valueInt_3;
        }

        if (reward_4 == RewardType.Gem) {
            amount += valueInt_4;
        }

        return amount;
    }

    public string GetTrackRewardInts(RewardType type) {
        List<int> listData = new List<int>();
        if (reward_1 == type) {
            listData.Add(valueInt_1);
        }

        if (reward_2 == type) {
            listData.Add(valueInt_2);
        }

        if (reward_3 == type) {
            listData.Add(valueInt_3);
        }

        if (reward_4 == type) {
            listData.Add(valueInt_4);
        }

        return string.Join(";", listData);
    }

    public string GetTrackRewardStrings(RewardType type) {
        List<string> listData = new List<string>();
        if (reward_1 == type) {
            listData.Add(value_1);
        }

        if (reward_2 == type) {
            listData.Add(value_2);
        }

        if (reward_3 == type) {
            listData.Add(value_3);
        }

        if (reward_4 == type) {
            listData.Add(value_4);
        }

        return string.Join(";", listData);
    }
}