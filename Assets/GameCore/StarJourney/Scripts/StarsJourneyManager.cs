using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Sirenix.OdinInspector;
using TilesHop.Cores.Pooling;
using TilesHop.GameCore.StarsJourney;
using UnityEngine;

[Serializable]
public class StarsJourneyManager : FastSingleton<StarsJourneyManager> {
    #region Fields

    private const string FILE_CONFIG_PATH = "starsjourney_config.dat";
    private const string FILE_DATA_PATH   = "starsjourney_data.dat";
    private const string HASH_PATH        = "starsjourney_config";

    private const string KEY_ONBOARD_UNLOCK_ELEMENT      = "starsjourney_onboard_unlock_element";
    private const string KEY_ONBOARD_UNLOCK_STAR_JOURNEY = "starsjourney_onboard_starjourney";
    
    public static  bool  isCompletedStarFlyEffect = false;
    
    private event Action OnLoadedStarsJourneyData;
    
    public int level;
    public int maxLevel;

    [SerializeField] private List<int> rewardedData;

    [ShowInInspector] private Dictionary<int, StarsJourneyData> _dictStarsJourney;
    private List<StarsJourneyData> _starsJourneyDataList;
    
    private bool _isValidConfig;
    
    private Dictionary<int, int> _listRewardBalls;
    private Dictionary<int, int> _listRewardThemes;

    #endregion

    #region Properties
    public bool ReachMax => level >= maxLevel;
    public bool isReady { get; private set; }
    public static bool isEnable => RemoteConfigBase.instance.StarsJourney_IsEnable;

    public Dictionary<int, int> rewardBalls => _listRewardBalls;
    public Dictionary<int, int> rewardThemes => _listRewardThemes;

    #endregion

    public async void DownloadServerConfig() {
        try {
            _dictStarsJourney = new Dictionary<int, StarsJourneyData>();
            rewardedData = new List<int>();
            SaveData.Load(FILE_DATA_PATH, ref rewardedData);
            level = rewardedData.Count;
            string url = RemoteConfigBase.instance.StarsJourney_ConfigUrl;
            string content;

            if (string.IsNullOrEmpty(url)) {
                _isValidConfig = false;
            } else {
                // Attempt to download data from the server; use cached data if request failed and cached data is available
                content = await DownloadManager.DownloadCsv(url);

                _isValidConfig = false;
                if (!string.IsNullOrEmpty(content)) {
                    ParseData(content, url);
                }
            }
        
            // can't download data from server and have no cached data => use default
            if (!_isValidConfig) {
                if (PlayerPrefs.HasKey(HASH_PATH)) { //exist old config
                    Logger.EditorLog("StarsJourney", "Load data from local cached!");
                    SaveData.Load(FILE_CONFIG_PATH, ref _dictStarsJourney);
                } else {
                    var defaultData = Resources.Load<TextAsset>("StarsJourney");
                    if (defaultData != null) {
                        content = defaultData.text;
                        ParseData(content, null);
                        Logger.EditorLog("StarsJourney", "load config from default!!!");
                    } else {
                        Logger.EditorLogError("StarsJourney", "Can't load data and no default config for it!!!");
                    }
                }
            }

            if (_dictStarsJourney != null) {
                foreach (var key in _dictStarsJourney.Keys) {
                    _dictStarsJourney[key].ProcessData();
                }

                maxLevel = _dictStarsJourney.Count;
            }
            
            _starsJourneyDataList = _dictStarsJourney?.Values.ToList() ?? new List<StarsJourneyData>();

            isReady = true;
            GetRewardBalls();
            OnLoadedStarsJourneyData?.Invoke();
        } catch (Exception e) {
            CustomException.Fire("[StarsJourneyManager][DownloadServerConfig]", e.Message);
        }
    }

    private void ParseData(string content, string url) {
        List<StarsJourneyData> listData = CSVReader.ProcessDataCSV<StarsJourneyData>(content, url);
        foreach (var starsJourneyData in listData) {
            int id = starsJourneyData.ID;
            _dictStarsJourney.TryAdd(id, starsJourneyData);
        }

        _isValidConfig = true;
    }

    public void GetRewardBalls() {
        _listRewardBalls = new ();
        _listRewardThemes = new ();
        foreach (StarsJourneyData data in _dictStarsJourney.Values) {
            AddBallAndThemeToListReward(data.reward_1, data.valueInt_1, data.ID);
            AddBallAndThemeToListReward(data.reward_2, data.valueInt_2, data.ID);
            AddBallAndThemeToListReward(data.reward_3, data.valueInt_3, data.ID);
            AddBallAndThemeToListReward(data.reward_4, data.valueInt_4, data.ID);
        }
    }

    private void AddBallAndThemeToListReward(RewardType type, int value, int level) {
        if (type == RewardType.Skin) {
            _listRewardBalls.TryAdd(value, level);
        } else if (type == RewardType.Theme) {
            _listRewardThemes.TryAdd(value, level);
        }
    }

    public void GotReward(int id) {
        if (rewardedData.Contains(id)) {
            Logger.EditorLogError("Error!!!");
            return;
        }

        rewardedData.Add(id);
        level = id;
        SaveData.Save(FILE_DATA_PATH, rewardedData);
    }

    public bool IsGotReward(int id) {
        return rewardedData.Contains(id);
    }

    public List<StarsJourneyData> GetData() {
        // return _dictStarsJourney.Values.ToList();
        return _starsJourneyDataList;
    }

    public StarsJourneyData GetStarJourneyData(int id) {
        return _dictStarsJourney.GetValueOrDefault(id);
    }

    public SjCheckPointState CheckStatus(StarsJourneyData journeyData, int currentStar) {
        if (IsGotReward(journeyData.ID)) {
            return SjCheckPointState.RECEIVED;
        } else {
            return currentStar >= journeyData.Stars ? SjCheckPointState.UNLOCK : SjCheckPointState.LOCK;
        }
    }

    public void WaitLoadStarsJourneyDone(Action action, bool isRunNextFrame = false) {
        if (action == null) {
            return;
        }

        if (isReady) {
            if (isRunNextFrame) {
                QueueRunNextFrame.instance.Enqueue(action);
            } else {
                action.Invoke();
            }
        } else {
            OnLoadedStarsJourneyData += () => {
                if (isRunNextFrame) {
                    QueueRunNextFrame.instance.Enqueue(action);
                } else {
                    action.Invoke();
                }
            };
        }
    }

    /// <summary>
    /// Lấy mốc đầu tiên mà user chưa nhận thưởng
    /// </summary>
    /// <param name="first" cref="StarJourneyBar">data ứng với point thứ nhất trong StarsJourneyBar</param>
    /// <param name="second" cref="StarJourneyBar">data ứng với point thứ hai trong StarsJourneyBar</param>
    /// <returns>TRUE: có mốc chưa nhận thưởng, FALSE: đã nhận thưởng hết. Nếu còn thưởng để nhận thì ở home sẽ có thêm hiệu ứng highlight ở second point</returns>
    public bool GetFirstNotYetRewardedMileStone(out StarsJourneyData first, out StarsJourneyData second) {
        var dataList = _starsJourneyDataList;
        int count = dataList.Count;
        for (int i = 0; i < count; i++) {
            if (!IsGotReward(dataList[i].ID)) {
                first = i == 0 ? null : dataList[i - 1];
                second = dataList[i];
                return true;
            }
        }

        // nếu đã nhận thưởng tất cả các mốc => hiển thị 2 mốc cuối nhưng không có highlight
        first = count > 1 ? dataList[^2] : null;
        second = dataList[^1];
        return false;
    }

    public List<int> GetListRewards(int currentStar) {
        List<int> result = new List<int>();
        var dataList = _starsJourneyDataList;
        int count = dataList.Count;
        for (int i = 0; i < count; i++) {
            if (currentStar < dataList[i].Stars)
                break;

            if (!IsGotReward(dataList[i].ID)) {
                result.Add(dataList[i].ID);
            }
        }

        return result;
    }

    public void Init() {
        DontDestroyOnLoad(gameObject);
        DownloadServerConfig();
    }

    public int GetStarRequireForNextLevel() {
        return _dictStarsJourney.ContainsKey(level + 1) ? _dictStarsJourney[level + 1].Stars : int.MaxValue;
    }

    private ElementConfig _elementConfig;

    public ElementConfig.ElementConfigData GetElementConfig(string valueStr) {
        return Enum.TryParse(valueStr, out NoteElementType elementType) ? GetElementConfig(elementType) : null;
    }

    public ElementConfig.ElementConfigData GetElementConfig(NoteElementType elementType) {
        if (_elementConfig == null) {
            _elementConfig = Resources.Load<ElementConfig>("Element Config");
        }

        return _elementConfig.GetConfig(elementType);
    }

    public NoteElementType CheckUnlockNewElement(int totalStar) {
        var earnElement = Configuration.GetEarnedElements();

        for (int i = level; i <= maxLevel; i++) {
            if (!_dictStarsJourney.ContainsKey(i)) {
                continue;
            }

            if (_dictStarsJourney[i].Stars > totalStar) {
                break;
            }

            var element = _dictStarsJourney[i].GetElementReward();
            if (element != NoteElementType.None && (earnElement == null || !earnElement.Contains(element))) {
                return element;
            }
        }

        return NoteElementType.None;
    }

    public static void TrackLevelProgression(Song song) {
        if (!isEnable)
            return;

        int bestPreviousStar = Configuration.GetBestStars(song.path);
        int starEarn = song.LastestStar - bestPreviousStar;
        if (starEarn <= 0)
            return;

        int current_level = Configuration.GetCurrentLevel();
        int total_previous_star = Configuration.instance.GetCurrentStars();
        int total_stars_earn = total_previous_star + starEarn;
        int song_unique_star_earned = song.LastestStar;
        int stars_required = instanceSafe.GetStarRequireForNextLevel();

        StarsJourneyTracking.Track_LevelProgress(current_level, starEarn, total_previous_star, total_stars_earn,
            song_unique_star_earned, bestPreviousStar, stars_required);
    }

    #region Data Tracking
    public static void TrackComplete(StarsJourneyData starJourneyData) {
        if (!isEnable)
            return;

        int current_level = Configuration.GetCurrentLevel();
        int before_level = current_level - 1;
        int total_stars_earn = Configuration.instance.GetCurrentStars();
        int stars_required = starJourneyData.Stars;

        StarsJourneyTracking.Track_LevelComplete(current_level, before_level, total_stars_earn, stars_required);
    }

    public static void TrackGrantReward(StarsJourneyData starJourneyData) {
        int current_level = Configuration.GetCurrentLevel();
        string type_ball = starJourneyData.GetTrackRewardInts(RewardType.Skin);
        int type_gem = starJourneyData.GetGemReward();
        string type_song =starJourneyData.GetTrackRewardStrings(RewardType.Song);
        string type_element = starJourneyData.GetTrackRewardStrings(RewardType.Element);
        string type_booster = starJourneyData.GetTrackRewardStrings(RewardType.Booster);
        StarsJourneyTracking.Track_RewardClaim(current_level, type_ball, type_gem, type_song,
            type_element, type_booster);
    }

    #endregion

    private static bool NeedShowOnboaringElement() {
        return !PlayerPrefs.HasKey(KEY_ONBOARD_UNLOCK_ELEMENT);
    }

    private static void DoneShowOnboardingElement() {
        PlayerPrefs.SetInt(KEY_ONBOARD_UNLOCK_ELEMENT, 1);
    }

    private static bool NeedShowOnboaringStarJourney() {
        return !PlayerPrefs.HasKey(KEY_ONBOARD_UNLOCK_STAR_JOURNEY);
    }

    private static void DoneShowOnboardingStarJourney() {
        PlayerPrefs.SetInt(KEY_ONBOARD_UNLOCK_STAR_JOURNEY, 1);
    }

    public IEnumerator IEShowOnboardingElement() {
        if (!isEnable) {
            yield break;
        }

        if (!NeedShowOnboaringElement()) {
            yield break;
        }

        var onBoardingUnlockElement =
            Util.ShowPopUp(PopupName.OnboardingElementUnlock).GetComponent<UIOnBoardingUnlockElement>();
        if (onBoardingUnlockElement != null) {
            DoneShowOnboardingElement();
        }

        bool isWaiting = true;
        onBoardingUnlockElement.Show(() => { isWaiting = false; });
        while (isWaiting) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
    }

    public IEnumerator IEShowOnboardingStarJourney() {
        if (!isEnable) {
            yield break;
        }

        if (!NeedShowOnboaringStarJourney()) {
            yield break;
        }

        yield return YieldPool.GetWaitForSeconds(1f);
        var onBoardingUnlockElement =
            Util.ShowPopUp(PopupName.OnboardingStarJourney).GetComponent<UIOnBoardingUnlockElement>();
        if (onBoardingUnlockElement != null) {
            DoneShowOnboardingStarJourney();
        }

        bool isWaiting = true;
        onBoardingUnlockElement.Show(() => { isWaiting = false; });
        while (isWaiting) {
            yield return YieldPool.GetWaitForEndOfFrame();
        }
    }
    
    public static bool CheckCompletedStarFly() {
        return isCompletedStarFlyEffect;
    }
}