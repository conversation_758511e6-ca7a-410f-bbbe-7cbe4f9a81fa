using System;
using UnityEngine;

namespace TilesHop.GameCore.StarsJourney {
    [CreateAssetMenu(fileName = "Element Config", menuName = "Config/Elements/Element Config")]
    public class ElementConfig : ScriptableObject {
        public ElementConfigData[] data;

        [Serializable]
        public class ElementConfigData {
            public string          name;
            public NoteElementType type;
            public Sprite          sprite;
            public int             idColor;
        }

        public ElementConfigData GetConfig(NoteElementType elementType) {
            foreach (var item in data) {
                if (item.type.Equals(elementType))
                    return item;
            }

            return null;
        }
    }
}