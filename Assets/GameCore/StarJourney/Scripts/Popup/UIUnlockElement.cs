using System;
using DG.Tweening;
using UnityEngine;

namespace TilesHop.GameCore.StarsJourney {
    public class UIUnlockElement : MonoBehaviour {
        [SerializeField] private UIRewardItemStarJourneyElement itemElement;

        [SerializeField] private CanvasGroup canvasGroup;
        [SerializeField] private Transform   vfxLight1;

        private Tween  _tween;
        //private Action _callback;

        private void OnEnable() {
            canvasGroup.alpha = 0;

            _tween = DOTween.Sequence().Append(canvasGroup.DOFade(1f, 0.3f)).AppendInterval(2f)
                .Append(canvasGroup.DOFade(0f, 0.3f)).AppendCallback(OnCompleteAction);
            _tween.Play();
        }

        // private void OnDisable() {
        //     _callback?.Invoke();
        // }

        private void LateUpdate() {
            vfxLight1?.Rotate(0, 0, .3f);
        }

        public void Show(ElementConfig.ElementConfigData elementConfigData) {
            // this._callback = callback;
            itemElement.SetData(elementConfigData);
            this.gameObject.SetActive(true);
            Configuration.EarnElement(elementConfigData.type);
        }

        private void OnCompleteAction() {
            this.gameObject.SetActive(false);
        }

        private void OnDestroy() {
            // Kill all DOTween instances to prevent memory leaks
            DOTween.Kill(gameObject);
        }
    }
}