using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.GameCore.StarsJourney {
    public class UIRewardItemStarJourneyElement : UIRewardItemStarJourney {
        [SerializeField] private Sprite[] bgs;
        [SerializeField] private Image    imgBG;
        [SerializeField] private Shadow   txtShadow;

        [Header("Click")]
        [SerializeField] private Button btnItem;

        private NoteElementType _noteType;

        public void SetData(ElementConfig.ElementConfigData data) {
            base.SetData(data.sprite, data.name);
            _noteType = data.type;
            imgBG.sprite = bgs[data.idColor];

            Color shadowColor = Color.black;
            switch (data.idColor) {
                case 0:
                    shadowColor = new Color(0.04f, 0.54f, 0.36f);
                    break;

                case 1:
                    shadowColor = new Color(0f, 0.32f, 0.64f);
                    break;

                case 2:
                    shadowColor = new Color(0.35f, 0.02f, 0.54f);
                    break;

                case 3:
                    shadowColor = new Color(0.46f, 0.15f, 0.24f);
                    break;

            }

            txtShadow.effectColor = shadowColor;

            btnItem.onClick.RemoveAllListeners();
            btnItem.onClick.AddListener(OnClick);
        }

        private void OnClick() {
            if (_noteType == NoteElementType.None) {
				return;
			}

			IntroducingTileElementsHolder.ShowIntroducing(_noteType);
		}
    }
}