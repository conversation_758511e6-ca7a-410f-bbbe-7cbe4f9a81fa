using System.Collections;
using System.Collections.Generic;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.GameCore.StarsJourney {
    public class UISJCheckPoint : OptimizedCellView {
        [Header("Rewards")] [SerializeField] private Transform                    rewardGroup;
        [SerializeField]                     private Transform                    extraGroup;
        [SerializeField]                     private UIRewardItemStarJourneyGem   rewardGem; //prefab
        [SerializeField]                     private UIRewardItemStarJourney      rewardSong; //prefab
        [SerializeField]                     private UIRewardItemStarJourney      rewardBall; //prefab
        [SerializeField]                     private UIRewardItemStarJourneyTheme rewardTheme; //prefab
        [SerializeField]                     private UIRewardItemStarJourney      rewardElement; //prefab
        [SerializeField]                     private UIRewardItemStarJourney      rewardBooster; //prefab

        [Space]
        [Header("Progress")]
        [SerializeField] private Image imgFill;

        [SerializeField] private RectTransform           groupProgress;
        public                   UIStarJourneyLevelPoint levelPoint;

        private StarsJourneyData  _data;
        private SjCheckPointState _state;
        public int ID => _data?.ID ?? 0;

        public int Stars => _data?.Stars ?? 0;

        private readonly List<UIRewardItemStarJourney> _listItemRewards = new();

        // private void OnDisable() {
        //     UnBindingMiddleLine();
        // }

        public void Init(int index, StarsJourneyData journeyData, SjCheckPointState state, float fillAmount) {
#if UNITY_EDITOR
            this.gameObject.name = $"Item {index}";
            levelPoint.gameObject.name = $"LevelPoint {index}";
            groupProgress.gameObject.name = $"Progress {index}";
#endif
            this._data = journeyData;
            this._state = state;
            this.imgFill.fillAmount = fillAmount;
            this.levelPoint.SetValue(journeyData.ID);
            ProcessData();
            UpdateState();
        }

        public void SetState(SjCheckPointState state) {
            this._state = state;
            UpdateState();
        }

        private void ProcessData() {
            CleanItems();
            _data.ValidateData();
            Transform firstParent = null;
            bool isSuccess;
            if (_data.reward_1 != RewardType.None) {
                firstParent = rewardGroup;
                var item = GetItemReward(_data.reward_1, firstParent);
                isSuccess = ProcessItem(item, _data.value_1, _data.valueInt_1, false);
                item.gameObject.SetActive(isSuccess);
            }

            if (_data.reward_2 != RewardType.None) {
                firstParent = rewardGroup;
                var item = GetItemReward(_data.reward_2, firstParent);
                isSuccess = ProcessItem(item, _data.value_2, _data.valueInt_2, false);
                item.gameObject.SetActive(isSuccess);
            }

            if (_data.reward_3 != RewardType.None) {
                firstParent = extraGroup;
                var item = GetItemReward(_data.reward_3, firstParent);
                isSuccess = ProcessItem(item, _data.value_3, _data.valueInt_3, true);
                item.gameObject.SetActive(isSuccess);
            }

            if (_data.reward_4 != RewardType.None) {
                firstParent = extraGroup;
                var item = GetItemReward(_data.reward_4, firstParent);
                isSuccess = ProcessItem(item, _data.value_4, _data.valueInt_4, true);
                item.gameObject.SetActive(isSuccess);
            }

            bool ProcessItem(UIRewardItemStarJourney item, string valueStr, int valueInt, bool isExtra) {
                item.SetLockState(_state, isExtra);
                switch (item.RewardType) {
                    case RewardType.Gem:
                        if (item is UIRewardItemStarJourneyGem itemGem) {
                            itemGem.SetData(valueInt,
                                $"+{valueInt} {LocalizationManager.instance.GetLocalizedValue("DIAMONDS")}");
                            return true;
                        }

                        break;

                    case RewardType.Skin:
                        var characterData = BallManager.instance.GetCharacterData(valueInt);
                        var ballConfig = BallManager.instance.GetBallConfig(valueInt);
                        if (characterData != null && ballConfig != null) {
                            item.SetData(characterData.sprIcon, ballConfig.name);
                            return true;
                        }

                        break;

                    case RewardType.Element:
                        var elementConfigData = StarsJourneyManager.instanceSafe.GetElementConfig(valueStr);
                        if (elementConfigData != null) {
                            if (item is UIRewardItemStarJourneyElement itemElement) {
                                itemElement.SetData(elementConfigData);
                                return true;
                            }
                        }

                        break;

                    case RewardType.Song:
                        var song = SongManager.instance.GetSongByAcmId(valueStr);
                        if (song != null) {
                            item.SetDataSong(song);
                            return true;
                        }

                        break;

                    case RewardType.Booster:
                        var booster = new BoosterReward(valueStr);
                        if (booster.type != BoosterType.None) {
                            item.SetDataBooster(booster);
                            return true;
                        }

                        break;

                    case RewardType.Theme:
                        var themeData = ThemeManager.instance.GetThemeConfig(valueInt, true);
                        if (themeData != null) {
                            if (item is UIRewardItemStarJourneyTheme itemTheme) {
                                itemTheme.SetData(valueInt, ThemeManager.instance.GetIcon(valueInt), themeData.name);
                                return true;
                            }
                        }

                        break;

                    default:
                        Logger.EditorLogError($"Null check!!! {item.RewardType}");
                        break;
                }

                return false;
            }
        }

        private void CleanItems() {
            foreach (var item in _listItemRewards) {
                item.gameObject.SetActive(false);
            }
        }

        private UIRewardItemStarJourney GetItemReward(RewardType rewardType, Transform parent) {
            foreach (var item in _listItemRewards) {
                if (item.gameObject.activeSelf)
                    continue;

                if (item.RewardType == rewardType) {
                    item.transform.SetParent(parent);
                    return item;
                }
            }

            var itemGem = Instantiate(GetItemPrefab(rewardType), parent);
            _listItemRewards.Add(itemGem);
            return itemGem;

            UIRewardItemStarJourney GetItemPrefab(RewardType rewardType) {
                switch (rewardType) {
                    case RewardType.Gem:
                        return rewardGem;

                    case RewardType.Skin:
                        return rewardBall;

                    case RewardType.Element:
                        return rewardElement;

                    case RewardType.Song:
                        return rewardSong;

                    case RewardType.Theme:
                        return rewardTheme;

                    case RewardType.Booster:
                        return rewardBooster;

                    default:
                        Logger.EditorLogError($"Null check!!! {rewardType}");
                        return null;
                }
            }
        }

        private void UpdateState() {

            // objLock.SetActive(_state == SjCheckPointState.LOCK);
            // objRewardLight.SetActive(_state == SjCheckPointState.UNLOCK);
        }

        public override void SetData(IData _data) { }

        public Vector3 GetPosition() {
            return transform.position;
        }

        public void BindingMiddleLine(UIMiddleLine middleLine) {
            StartCoroutine(IEBinding(middleLine));
        }

        private IEnumerator IEBinding(UIMiddleLine middleLine) {
            Transform levelPointTransform = levelPoint.transform;
            levelPointTransform.SetParent(groupProgress, true);
            levelPointTransform.localPosition = Vector3.zero;
            groupProgress.SetParent(this.transform, true);
            groupProgress.anchoredPosition = Vector3.zero;
            middleLine.UnBinding(this.transform);
            levelPoint.gameObject.SetActive(true);
            groupProgress.gameObject.SetActive(true);
            yield return YieldPool.GetWaitForEndOfFrame();

            if (_state == SjCheckPointState.LOCK) {
                levelPointTransform.localPosition = Vector3.zero;
                middleLine.Binding(this.transform, levelPointTransform);
                levelPoint.gameObject.SetActive(true);
            } else {
                groupProgress.anchoredPosition = Vector3.zero;
                middleLine.Binding(this.transform, groupProgress);
                groupProgress.gameObject.SetActive(true);
            }
        }

        private void UnBindingMiddleLine() {
            // levelPoint.gameObject.SetActive(false);
            // groupProgress.gameObject.SetActive(false);
        }

        public void ShowVFX(bool isShow) {
            foreach (var item in _listItemRewards) {
                item.ShowVFX(isShow);
            }
        }
    }
}