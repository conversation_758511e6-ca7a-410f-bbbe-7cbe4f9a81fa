using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.StarJourney {
    public class UIImageToggle : MonoBehaviour {
        [SerializeField] private GameObject on;

        private Image _imageOn;
        private bool  _isOn;

        public bool isOn {
            get { return isOn; }
            set {
                _isOn = value;
                on.SetActive(_isOn);
            }
        }

        public Image imageOn {
            get {
                return on.TryGetComponent(out _imageOn) ? _imageOn : null;
            }
        }

        public bool isActive => on.activeSelf;
    }
   
}