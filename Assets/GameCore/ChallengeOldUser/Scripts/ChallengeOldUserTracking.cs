using System.Collections.Generic;

namespace TilesHop.ChallengeOldUser {
    public class ChallengeOldUserTracking {
        private static string popup_challenge_status = "popup_challenge_status";

        public const string free_challenge = "free_challenge";

        public enum TRACK_EVENT {
            popup_challenge_impression,
            try_now_challenge_button_click,
            revive_keepgoing_impression,
            goodtry_impression_challenge,
            goodtry_giveup_click,
            try_now_giveup_click
        }

        public static void TrackPopupChallengeImpression(byte number) {
            string eventKey = TRACK_EVENT.popup_challenge_impression.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {popup_challenge_status, number}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void TrackTryNowChallengeButtonClick(byte number) {
            string eventKey = TRACK_EVENT.try_now_challenge_button_click.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {popup_challenge_status, number}
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void TrackTryNowGiveUpButtonClick(byte number) {
            string eventKey = TRACK_EVENT.try_now_giveup_click.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {popup_challenge_status, number}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void TrackReviveKeepGoingImpression() {
            string eventKey = TRACK_EVENT.revive_keepgoing_impression.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> { };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void TrackPopupGoodTryImpression(byte number) {
            string eventKey = TRACK_EVENT.goodtry_impression_challenge.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {popup_challenge_status, number}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void TrackPopupGoodTryGiveUpClick(byte number) {
            string eventKey = TRACK_EVENT.goodtry_giveup_click.ToString();
            Dictionary<string, object> param = new Dictionary<string, object> {
                {popup_challenge_status, number}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void AddSubParam(ref Dictionary<string, object> dictionary) {
            if (!ChallengeOldUserController.IsPlayingChallenge)
                return;

            if (dictionary.ContainsKey(TRACK_PARAM.source)) {
                dictionary[TRACK_PARAM.source] = free_challenge;
            } else {
                dictionary.Add(TRACK_PARAM.source, free_challenge);
            }
        }

        public static void AddAdsParam(ref Dictionary<string, object> dictionary) {
            if (!ChallengeOldUserController.IsPlayingChallenge)
                return;

            if (dictionary.ContainsKey(TRACK_PARAM.source)) {
                dictionary[TRACK_PARAM.source] = free_challenge;
            } else {
                dictionary.Add(TRACK_PARAM.source, free_challenge);
            }
        }
    }
}