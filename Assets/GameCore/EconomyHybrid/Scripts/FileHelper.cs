using System.IO;
using Newtonsoft.Json;
using UnityEngine;

namespace TilesHop.Cores.Hybrid {
    public class FileHelper {
        public static void Save<T>(string fileName, T map) {
            string json = JsonConvert.SerializeObject(map);
            Logger.EditorLog("FileHelper", $"Save: {json}");
            File.WriteAllText($"{Application.persistentDataPath}/{fileName}.json", json);
        }

        public static T Load<T>(string fileName){
            string path = $"{Application.persistentDataPath}/{fileName}.json";
            if (!File.Exists(path)) {
                return default;
            }

            string json = File.ReadAllText(path);
            Logger.EditorLog("FileHelper", $"Load: {json}");
            T loaded = JsonConvert.DeserializeObject<T>(json);
            return loaded;
        }
    }
}