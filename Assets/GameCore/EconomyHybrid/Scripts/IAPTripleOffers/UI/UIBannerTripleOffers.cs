using System;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.Hybrid.TripleOffer {
    public class UIBannerTripleOffers : MonoBehaviour {
        [SerializeField] private Image  imgBanner;
        [SerializeField] private Button btnExplore;

        private readonly string _location = LOCATION_NAME.revive.ToString();

        private void Start() {
            btnExplore.onClick.AddListener(OnBtnExploreClicked);
            UpdateUIStatic();
        }

        private void OnBtnExploreClicked() {
            StartCoroutine(IAPTripleOffers.ShowPopupAsync(_location, true));
        }

        private void UpdateUIStatic() {
            var data = IAPTripleOffers.instanceSafe.GetBalancyDataUI();
            if (data == null) {
                return;
            }

            data.BannerRevive?.LoadSprite(OnLoadDoneBanner);
            data.AssetButton?.LoadSprite(OnLoadDoneButton);
        }

        private void OnLoadDoneButton(Sprite sprite) {
            if (!sprite || !btnExplore) {
                return;
            }

            btnExplore.image.sprite = sprite;
        }

        private void OnLoadDoneBanner(Sprite sprite) {
            if (!sprite || !imgBanner) {
                return;
            }

            imgBanner.sprite = sprite;
        }
    }
}