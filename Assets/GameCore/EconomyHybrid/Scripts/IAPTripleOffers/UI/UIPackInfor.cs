using System;
using TilesHop.Cores.EventTracking;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.Hybrid.TripleOffer {
    public class UIPackInfor : MonoBehaviour {
        [SerializeField] private Image imgBG;

        [SerializeField] private <PERSON><PERSON>        btnBuy;
        [SerializeField] private Text          txtBuyPrice;
        [SerializeField] private GameObject    objPriceLoading;
        [SerializeField] private RectTransform rewardHolder;
        [SerializeField] private UIRewardItem  rewardPrefab;
        [SerializeField] private Sprite        imgGem;

        private IAPInfor        _infoPack;
        private IAPDefinitionId _id;
        private string          _location;

        private bool _updatedPrice;
        private bool _isWaitingCallback;
        private event Action _onClick;

        private void OnEnable() {
            if (!_updatedPrice && !_isWaitingCallback && _infoPack != null) {
                CheckUpdatePrice();
            }
        }

        private void OnDisable() {
            if (_isWaitingCallback) {
                _isWaitingCallback = false;
                IapBase.OnInited -= UpdatePrice;
            }
        }

        public void SetData(IAPDefinitionId id, IAPInfor info, Action onBtnBuyFistPackage, string location) {
            _id = id;
            _infoPack = info;
            _location = location;
            _onClick = onBtnBuyFistPackage;
            var rewardData = info.rewardData;
            foreach (var reward in rewardData.rewards) {
                var item = Instantiate(rewardPrefab, rewardHolder);
                item.SetIconGem(imgGem);
                item.SetData(reward);
            }

            CheckUpdatePrice();
        }

        private void CheckUpdatePrice() {
            if (Application.isEditor) {
                _updatedPrice = true;
                txtBuyPrice.text = _infoPack.price.ToString("f2");
                TrackShowIAPPack();
            } else if (IapBase.IsInited) {
                objPriceLoading?.SetActive(true);
                UpdatePrice();
            } else {
                txtBuyPrice.text = string.Empty;
                objPriceLoading?.SetActive(true);
                _isWaitingCallback = true;
                IapBase.OnInited += UpdatePrice;
            }
        }

        private void UpdatePrice() {
            if (_isWaitingCallback) {
                _isWaitingCallback = false;
                IapBase.OnInited -= UpdatePrice;
            }

            if (txtBuyPrice) {
                string price = IapBase.GetPriceString(_id);
                if (string.IsNullOrEmpty(price)) {
                    txtBuyPrice.text = string.Empty;
                    objPriceLoading?.SetActive(true);
                } else {
                    _updatedPrice = true;
                    objPriceLoading?.SetActive(false);
                    txtBuyPrice.text = price;
                    Logger.Log(IAPTripleOffers.FEATURE_NAME, $"Update price {_id}: {price}");
                }
            }
            TrackShowIAPPack();
        }

        private void Start() {
            btnBuy.onClick.AddListener(BtnBuyClick);
        }

        private void BtnBuyClick() {
            _onClick?.Invoke();
        }

        public void SetBG(Sprite sprite) {
            imgBG.sprite = sprite;
        }

        public void SetButton(Sprite sprite) {
            if (!btnBuy)
                return;

            btnBuy.image.sprite = sprite;
        }
        
        private void TrackShowIAPPack() {
            if (_infoPack == null)
                return;

            string packageId = _infoPack.GetNamePack();
            if (string.IsNullOrEmpty(packageId)) {
                return;
            }

            IapStatusTrackingData statusData = new() {
                claimType = TrackClaimType.iap_purchase,
                groupOfferId = IAPTripleOffers.instanceSafe?.GetGroupOfferId(),
                location = _location,
                stage = "1",
                packageId = packageId,
                value = IapBase.GetPriceValue(packageId),
                currency = IapBase.GetPriceCode(packageId),
            };

            BaseIapPackageTracking.TrackIapShow(IAPTripleOffers.PACK_NAME, statusData);
        }
    }
}