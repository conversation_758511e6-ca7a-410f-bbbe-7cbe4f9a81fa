using anzu;
using UnityEngine;

public class AnzuAnalytic : MonoBehaviour {
    private const string NativeAds_OnPlaybackStarted = "NativeAds_OnPlaybackStarted";
    private const string NativeAds_OnPlaybackCompleted = "NativeAds_OnPlaybackCompleted";
    private const string NativeAds_ChannelImpressionCallback = "NativeAds_ChannelImpressionCallback";
    private const string NativeAds_ImpressionCallback = "NativeAds_ImpressionCallback";
    private const string NativeAds_OnPlaybackEmpty = "NativeAds_OnPlaybackEmpty";
    //private const string NativeAds_GetPlaybackFullness = "NativeAds_GetPlaybackFullness";

    public static void InitCallBack() {
		//AnzuCore.AddChannelImpressionCallback(
		//	at => { AnalyticHelper.FireString(NativeAds_ChannelImpressionCallback); });
        //AnzuCore.AddImpressionCallback(at => { AnalyticHelper.FireString(NativeAds_ImpressionCallback); });
    }

    public static void OnPlaybackEmpty() {
        AnalyticHelper.FireString(NativeAds_OnPlaybackEmpty);
    }

    public static void OnPlaybackCompleted() {
        AnalyticHelper.FireString(NativeAds_OnPlaybackCompleted);
    }

    public static void OnPlaybackStarted() {
        AnalyticHelper.FireString(NativeAds_OnPlaybackStarted);
    }
}
