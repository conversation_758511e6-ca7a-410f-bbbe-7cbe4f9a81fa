using System;
using anzu;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Serialization;

public class AnzuElement : MonoBehaviour {
    public enum Type {
        T_4_3_R = 1,
        T_4_3_L = 2,
        Both    = 3
    }

    public Type   type = Type.T_4_3_R;
    public AnzuAd anzuAd;

    [SerializeField] MeshRenderer meshRender;

    [SerializeField] private Transform tfBorder;
    [SerializeField] private Transform tfPlinth;

    private Vector3 _originalScaleBanner;
    private Vector3 _originalScaleBorder;
    private Vector3 _originalScalePlinth;
    private float   _originalRatio       = 0.75f;
    private float   _scaleEffectDuration = 0;
    private float   _scalePower          = 0.4f;

    private float _scaleStaticBanner       = 1.5f;
    private float _scaleStaticBannerBonus  = 1.15f;
    private float _scaleStaticBannerMoving = 8;

    public bool shrinkToFitAspectRatio {
        get => anzuAd.AnzuChannel.ChannelData.AllowShrinkToFit;
        set => anzuAd.AnzuChannel.ChannelData.AllowShrinkToFit = value;
    }

    private Transform _tfMain;

    private void Awake() {
        if (AnzuManager.instance == null || !AnzuManager.instance.IsEnableAds(true)) {
            Destroy(gameObject);
            return;
        }

        Song song = Configuration.instance.GetCurrentSong();
        if (song != null && AnzuManager.instance != null && AnzuManager.instance.IsExcludeSongName(song)) {
            Destroy(gameObject);
            return;
        }

        /* Register events */
        anzuAd.OnPlaybackStarted += OnPlaybackStarted;
        anzuAd.OnPlaybackComplete += OnPlaybackCompleted;
        anzuAd.OnChannelEmpty += OnPlaybackEmpty;
        //animatedTexture.OnPlaybackMediaInfo += OnPlaybackInfoChanged;

        _tfMain = transform;
    }

    public void InitValues() {
        if (_tfMain == null) {
            _tfMain = transform;
        }

        if (!AnzuManager.instance.IsStaticBanner()) {
            //_tfMain.SetScale(0.1f); // Prepare for scale effect
            _originalScaleBanner = Vector3.one * RemoteConfig.instance.scaleAnzuBanner_4_3;
        } else {
            _originalScaleBanner = _tfMain.localScale;
        }

        _scaleEffectDuration = RemoteConfig.instance.scaleEffectDuration;
        _scalePower = RemoteConfig.instance.scalePower;
        Hide();
    }

    private void Hide() {
        if (RemoteConfig.instance.showAnzuBannerOnPlayback) {
            //anzuAd.defaultClip = null;
            meshRender.enabled = false;
            tfBorder.gameObject.SetActive(false);
            tfPlinth.gameObject.SetActive(false);
        }
    }

    private void Show() {
        if (RemoteConfig.instance.showAnzuBannerOnPlayback) {
            meshRender.enabled = true;
            if (Spawner.s.themeId != ThemeManager.ThemeIdGLU2) {
                tfBorder.gameObject.SetActive(true);
                tfPlinth.gameObject.SetActive(true);
            }
        }
    }

    private void OnPlaybackStarted() {
        isPlaybackEmpty = false;
        Show();
        AnzuAnalytic.OnPlaybackStarted();
        UpdateBannerScale();

        if (RemoteConfigBase.instance.NativeAds_Style == 1 && AdvertyManager.instance.IsAdPlacementActivated()) {
            AdvertyManager.instance.HideBanner();
        }
    }

    private void OnPlaybackCompleted() {
        AnzuAnalytic.OnPlaybackCompleted();
    }

    public bool isPlaybackEmpty;

    private void OnPlaybackEmpty() {
        isPlaybackEmpty = true;
        Hide();
        AnzuAnalytic.OnPlaybackEmpty();

        if (RemoteConfigBase.instance.NativeAds_Style == 1 && AdvertyManager.instance.IsAdPlacementActivated()) {
            if (AnzuManager.isInstanced && AnzuManager.instance.IsPlaybackEmpty()) {
                AdvertyManager.instance.ShowBanner();
            }
        }
    }

    private void UpdateBannerScale() {
        Vector3 localScale = anzuAd.transform.localScale;

        if (Math.Abs(localScale.y - 1) > 0.01f) {
            float scale = 1f / localScale.y;
            localScale *= scale;
            anzuAd.transform.localScale = localScale;
        }

        if (_scaleEffectDuration > 0) {
            tfPlinth.DOScale(localScale, _scaleEffectDuration);
        } else {
            tfPlinth.localScale = localScale;
        }

        if (_scaleEffectDuration > 0) {
            tfBorder.DOScale(localScale, _scaleEffectDuration);
        } else {
            tfBorder.localScale = localScale;
        }

        float currentRatio = localScale.z / localScale.x;
        float ratioDiff = currentRatio / _originalRatio;
        SetBannerScale(ratioDiff);
    }

    /// <summary>
    /// Update Scale for Main Banner
    /// </summary>
    /// <param name="ratioDiff">The difference between Current Ratio and The Next Ratio</param>    
    void SetBannerScale(float ratioDiff) {
        float increaseScale = Mathf.Pow(ratioDiff, _scalePower);
        if (ratioDiff < 1) {
            increaseScale = 1 / increaseScale;
        }

        Vector3 tmpScale = _originalScaleBanner * increaseScale;

        if (AnzuManager.instance.IsStaticBanner()) {
            float x = 0;
            if (increaseScale > _scaleStaticBanner) {
                x = _tfMain.position.x > 0 ? -_scaleStaticBannerMoving : _scaleStaticBannerMoving;
                tmpScale *= _scaleStaticBannerBonus;
            }

            tfBorder.SetLocalX(x);
            tfPlinth.SetLocalX(x);
            meshRender.transform.SetLocalX(x);
        }

        if (_scaleEffectDuration > 0) {
            _tfMain.DOScale(tmpScale, _scaleEffectDuration);
        } else {
            _tfMain.localScale = tmpScale;
        }

        //Debug.LogError((1f/(_originalRatio*ratioDiff))+ "-"+ increaseScale + "--"+ tmpScale.x);
    }
}