using System;
using UnityEngine.Serialization;

namespace TilesHop.Cores.UserProgression {
    [Serializable]
    public class SongPackData : IComparable<SongPackData> {
        public int                Pack_Level;
        public string             AcmId;
        //public string             Song_Name;
        public SongPackUnlockType Song_Status;
        public int                Song_Status_Time;
        public int                Song_Status_Gem;
        public int                Song_Status_Ad;
        public string             Reward_Type; // for Grand Prize
        public int                Reward_Value = 0; // for Grand Prize
        public string             RewardForUnlock_Type; // user can get reward when unlock song pack
        public int                RewardForUnlock_Value = 0;
        public int                ThemeId;

        [NonSerialized] public int    order;
        [NonSerialized] public string SongPath;

        public SongPackData() {
            Pack_Level = int.MaxValue;
            AcmId = string.Empty;
            Song_Status = SongPackUnlockType.Free;
            Song_Status_Time = int.MaxValue;
            Song_Status_Gem = int.MaxValue;
            Song_Status_Ad = int.MaxValue;
        }

        public SongPackData(Song song) {
            AcmId = song.acm_id_v3;
            SongPath = song.path;
            Song_Status = SongPackUnlockType.Gem;
            Song_Status_Time = 0;
            Song_Status_Gem = song.diamonds;
            Song_Status_Ad = song.ads;
        }

        public int CompareTo(SongPackData other) {
            if (Song_Status != other.Song_Status)
                return ((int) Song_Status).CompareTo((int) other.Song_Status);
            if (Song_Status_Ad != other.Song_Status_Ad)
                return Song_Status_Ad.CompareTo(other.Song_Status_Ad);
            if (Song_Status_Gem != other.Song_Status_Gem)
                return Song_Status_Gem.CompareTo(other.Song_Status_Gem);

            return Song_Status_Time.CompareTo(other.Song_Status_Time);
        }

        public bool HasReward() {
            return !string.IsNullOrEmpty(Reward_Type) && Reward_Value != 0;
        }

        public bool HasRewardForUnlock() {
            return !string.IsNullOrEmpty(RewardForUnlock_Type) && RewardForUnlock_Value != 0;
        }
    }
}