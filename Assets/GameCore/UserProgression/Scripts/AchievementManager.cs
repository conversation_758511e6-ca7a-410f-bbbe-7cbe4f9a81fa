using System;
using System.Collections;
using System.Collections.Generic;
using Music.ACM;
using Sirenix.OdinInspector;
using TilesHop.Cores.UserProgression;
using UnityEngine;
using UnityEngine.Networking;

namespace TilesHop.Cores.Hybrid {
    [Serializable]
    public class AchievementManager: FastSingleton<AchievementManager> {
        [SerializeField] private AchievementData[] listAchievementData;

        [ShowInInspector] private Dictionary<AchievementType, List<AchievementData>> _dictAchievement;
        [ShowInInspector] private Dictionary<AchievementType, Sprite> _dictAchievementIcon;

        private                   bool _isInit = false;
        private                   bool _isValidConfig;
        private                   bool _isUpdateAchievement;
        [ShowInInspector] private bool _isLoadedConfig = false;

        private readonly string _filePath = "hybrid_achievement_config.dat";
        private readonly string _hashPath = "hybrid_achievement_config";

        private UserDataCached _userDataCached;

        protected override void Awake() {
            base.Awake();
            if (instance == this) {
                DontDestroyOnLoad(this.gameObject);
            }
        }
        public void Init() {
            if (_isInit)
                return;
            _isInit = true;
            StartCoroutine(DownloadServerConfig(null));
        }
        public IEnumerator DownloadServerConfig(Action callback) {
            string url = RemoteConfigBase.instance.Hybrid_Achievement_ConfigUrl;

            UnityWebRequest request = UnityWebRequest.Get(url);
            yield return request.SendWebRequest();

            _isValidConfig = false;
            string content = string.Empty;
            if (request.result == UnityWebRequest.Result.Success) {
                content = request.downloadHandler.text;
                ParseData(content, url);
            } else {
                if (Utils.IsInternetReachable) {
                    CustomException.Fire("Hybrid-Achievement", $"Cant download config from url:{url}");
                } else {
                  Logger.LogError("Hybrid-Achievement:[Offline] Cant download config from url:{url}");
                }
            }

            if (_isValidConfig) {
                string oldHash = PlayerPrefs.GetString(_hashPath);
                string newHash = Util.ComputeHash(content);
                if (!newHash.Equals(oldHash)) {
                    SaveData.Save(_filePath, listAchievementData);
                    PlayerPrefs.SetString(_hashPath, newHash);
                }
            } else {
                if (PlayerPrefs.HasKey(_hashPath)) { //exist old config
                    //load exist
                    Logger.EditorLog("Hybrid-Achievement", "Load Song pack from local cached!");
                    SaveData.Load(_filePath, ref listAchievementData);

                    if (listAchievementData != null) {
                        //process listAchievementData
                        //TODO: cache "content" thay vì cache "listAchievementData" để tránh thiếu các bước xử lý trong ParseData()
                        for (var i = 0; i < listAchievementData.Length; i++) {
                            var item = listAchievementData[i];
                            item.Process();
                        }
                        _isValidConfig = true;   
                    } else {
                        LoadDefaultData();
                    }
                } else {
                    LoadDefaultData();
                }
            }

            _dictAchievement = new Dictionary<AchievementType, List<AchievementData>>();
            _dictAchievementIcon = new Dictionary<AchievementType, Sprite>();
            if (listAchievementData != null) {
                foreach (var achie in listAchievementData) {
                    var type = achie.AchivementType;
                    if (type.Equals(AchievementType.NONE))
                        continue;

                    if (!_dictAchievementIcon.ContainsKey(type)) {
                        _dictAchievementIcon.Add(type, CoreData.instance.GetIconAchievement(type));
                    }

                    achie.SetAvatar(_dictAchievementIcon[type]);

                    if (_dictAchievement.ContainsKey(type)) {
                        _dictAchievement[type].Add(achie);
                    } else {
                        _dictAchievement.Add(type, new List<AchievementData>() { achie });
                    }
                }
            }

            DoneLoadConfig();
            callback?.Invoke();

            WaitSongListInitDone(() => {
                Util.WaitBallManagerInitDone(() => {
                    Util.WaitThemeManagerInitDone(() => {
                        if (!_isUpdateAchievement) {
                            UpdateAchievement();
                            if (Util.IsHomeScene()) {
                                HomeManager.instance.UpdateAchievementBadgeCount();
                            }
                        }
                    });
                });
            });
        }

        private void LoadDefaultData() {
            //load default
            if (Configuration.instance.defaultUserProgressAchievementData != null) {
                var content = Configuration.instance.defaultUserProgressAchievementData.text;
                ParseData(content,null);
            } else {
                Logger.EditorLogError("Hybrid-Achievement: Can't load data and no default config for it!!!");
            }
        }

        private void WaitSongListInitDone(Action callback) {
            if (UserProgressionController.EnableFeature) {
                UserProgressionController.WaitUpdateSongListDone(callback);
            } else {
               Util.WaitSongListDone(callback);
            }
        }
        
        private void ParseData(string content, string url) {
            var listData = CSVReader.ProcessDataCSV<AchievementData>(content,url);
            if (listData != null && listData.Count != 0) {
                listAchievementData = new AchievementData[listData.Count];
                for (var i = 0; i < listData.Count; i++) {
                    var item = listData[i];
                    item.Process();
                    //item.CheckStatus();
                    listAchievementData[i] = item;
                }

                _isValidConfig = true;
            }
        }


        private List<Action> _waitInitDone = new List<Action>();

        public void WaitInitDone(Action onDone) {
            if (_isLoadedConfig) {
                onDone?.Invoke();
            } else {
                _waitInitDone.Add(onDone);
            }
        }

        private void DoneLoadConfig() {
            _isLoadedConfig = true;
            Logger.EditorLog("Hybrid-Achievement", "Loaded config");

            foreach (var item in _waitInitDone) {
                item?.Invoke();
            }
        }

        public List<AchievementObj> GetAchievementList() {
            if (!_isLoadedConfig) {
                Logger.EditorLogError("Hybrid-Achievement", "Not loaded configs yet!!!!");
                return null;
            }

            if (!_isUpdateAchievement) {
                UpdateAchievement();
            }

            List<AchievementObj> tempList = new List<AchievementObj>();
            foreach (var item in listAchievementData) {
                if (item.AchievementObject == null) {
                    continue;
                }
                tempList.Add(item.AchievementObject);
            }

            return tempList;
        }

        public void UpdateAchievement() {
            if (!_isLoadedConfig) return;
            _isUpdateAchievement = true;

            if (_userDataCached == null) {
                _userDataCached = Configuration.instance.userDataCached;
                _userDataCached.UpdateStatus();
            }

            UpdateForAchievement(AchievementType.Reach1Star, _userDataCached.Count1StarSong);
            UpdateForAchievement(AchievementType.Reach2Star, _userDataCached.Count2StarSong);
            UpdateForAchievement(AchievementType.Reach3Star, _userDataCached.Count3StarSong);
            IncreaseCountUnlockSong(0);
            IncreaseCountPlaySong(0);

            UpdateForAchievement(AchievementType.Replay, _userDataCached.CountReplay);

            UpdateForAchievement(AchievementType.UnlockBall, _userDataCached.CountUnlockBall);
            UpdateForAchievement(AchievementType.UnlockTheme, _userDataCached.CountUnlockTheme);
        }

        public void IncreaseCountUnlockBall(int count) {
            if (!_isLoadedConfig) return;
            UpdateForAchievement(AchievementType.UnlockBall, count);
        }

        public void IncreaseCountUnlockTheme(int count) {
            if (!_isLoadedConfig) return;
            UpdateForAchievement(AchievementType.UnlockTheme, count);
        }

        public void UpdateAchievementAfterPlaySong() {
            if (!_isLoadedConfig) return;
            if (_userDataCached == null) return;
            UpdateForAchievement(AchievementType.Reach1Star, _userDataCached.Count1StarSong);
            UpdateForAchievement(AchievementType.Reach2Star, _userDataCached.Count2StarSong);
            UpdateForAchievement(AchievementType.Reach3Star, _userDataCached.Count3StarSong);
            IncreaseCountPlaySong(0);
            UpdateForAchievement(AchievementType.Replay, _userDataCached.CountReplay);
        }

        public void IncreaseCountUnlockSong(int count) {
            if (!_isLoadedConfig) return;
            if (_userDataCached == null) return;
            UpdateForAchievement(AchievementType.UnlockSongByAds, _userDataCached.CountUnlockAdsSong);
            UpdateForAchievement(AchievementType.UnlockSongByGem, _userDataCached.CountUnlockGemSong);

            UpdateForAchievement(AchievementType.UnlockRockSong, CountUnlockSong(AchievementType.UnlockRockSong));
            UpdateForAchievement(AchievementType.UnlockPopSong, CountUnlockSong(AchievementType.UnlockPopSong));
            UpdateForAchievement(AchievementType.UnlockHipHopSong, CountUnlockSong(AchievementType.UnlockHipHopSong));
            UpdateForAchievement(AchievementType.UnlockEDMSong, CountUnlockSong(AchievementType.UnlockEDMSong));
            UpdateForAchievement(AchievementType.UnlockBalladSong, CountUnlockSong(AchievementType.UnlockBalladSong));
            UpdateForAchievement(AchievementType.UnlockCountrySong, CountUnlockSong(AchievementType.UnlockCountrySong));
            UpdateForAchievement(AchievementType.UnlockRnBSong, CountUnlockSong(AchievementType.UnlockRnBSong));
        }

        public void IncreaseCountPlaySong(int count) {
            if (!_isLoadedConfig) return;
            UpdateForAchievement(AchievementType.PlaySong, _userDataCached.CountPlaySong);

            UpdateForAchievement(AchievementType.PlayRockSong, CountPlaySong(AchievementType.PlayRockSong));
            UpdateForAchievement(AchievementType.PlayPopSong, CountPlaySong(AchievementType.PlayPopSong));
            UpdateForAchievement(AchievementType.PlayHipHopSong, CountPlaySong(AchievementType.PlayHipHopSong));
            UpdateForAchievement(AchievementType.PlayEDMSong, CountPlaySong(AchievementType.PlayEDMSong));
            UpdateForAchievement(AchievementType.PlayBalladSong, CountPlaySong(AchievementType.PlayBalladSong));
            UpdateForAchievement(AchievementType.PlayCountrySong, CountPlaySong(AchievementType.PlayCountrySong));
            UpdateForAchievement(AchievementType.PlayRnBSong, CountPlaySong(AchievementType.PlayRnBSong));
        }


        private int CountUnlockSong(AchievementType type) {
            string genre = GetGenreByType(type);
            return _userDataCached.CountUnlockGenre(genre);
        }

        private int CountPlaySong(AchievementType type) {
            string genre = GetGenreByType(type);
            return _userDataCached.CountPlayGenre(genre);
        }

        public void IncreaseCountReplay(int count) {
            UpdateForAchievement(AchievementType.Replay, count);
        }

        private void UpdateForAchievement(AchievementType type, int value) {
            if (_dictAchievement == null) {
                return;
            }

            if (!_dictAchievement.ContainsKey(type)) {
                return;
            }

            var data = _dictAchievement[type];
            for (int i = 0; i < data.Count; i++) {
                AchievementObj achievement = data[i].AchievementObject;
                AchievementStatus status =
                    (AchievementStatus)PlayerPrefs.GetInt(Util.BuildString(string.Empty, AchievementObj.PRE_STATUS, data[i].ID), 0);
                if (status == AchievementStatus.RECEIVED) {
                    data[i].SetStatusOnly(status);
                    continue;
                }

                if (achievement.status == AchievementStatus.LOCK) {
                    if (data[i].AchievementObject.current == value) continue;
                    data[i].SetCurrent(value);
                    if (achievement.status == AchievementStatus.UNLOCK) {
                        if (AchievementPopUp.instance == null && CanShowAchievementPopup(achievement.type) &&
                            !SevenDayMission.instanceSafe.HasNotification()) {
                            GameObject popup = Util.ShowPopUp(PopupName.AchievementPopUp);
                            AchievementPopUp.instance = popup.GetComponent<AchievementPopUp>();
                            AchievementPopUp.instance.SetUserProgressionAchievement(achievement, data[i]);
                            AnalyticHelper.FB_Standard_Unlock_Achievement(achievement.type.ToString());
                        }
                    }
                }
            }
            
            if (Util.IsHomeScene()) { //UI1
                HomeManager.instance.UpdateAchievementBadgeCount();
            }
        }

        private bool CanShowAchievementPopup(AchievementType achievementType) {
            switch (achievementType) {
                case AchievementType.UnlockBall:
                case AchievementType.UnlockTheme:
                case AchievementType.UnlockSongByAds:
                case AchievementType.UnlockSongByGem:
                case AchievementType.UnlockRockSong:
                case AchievementType.UnlockPopSong:
                case AchievementType.UnlockHipHopSong:
                case AchievementType.UnlockEDMSong:
                case AchievementType.UnlockBalladSong:
                case AchievementType.UnlockCountrySong:
                case AchievementType.UnlockRnBSong:
                    return false;
            }

            return true;
        }

        private string GetGenreByType(AchievementType type) {
            switch (type) {
                case AchievementType.PlayRockSong:
                case AchievementType.UnlockRockSong:
                    return "ROCK";
                case AchievementType.PlayPopSong:
                case AchievementType.UnlockPopSong:
                    return "POP";
                case AchievementType.PlayHipHopSong:
                case AchievementType.UnlockHipHopSong:
                    return "HIPHOP";
                case AchievementType.PlayEDMSong:
                case AchievementType.UnlockEDMSong:
                    return "EDM";
                case AchievementType.PlayBalladSong:
                case AchievementType.UnlockBalladSong:
                    return "Ballad";
                case AchievementType.PlayCountrySong:
                case AchievementType.UnlockCountrySong:
                    return "Country";
                case AchievementType.PlayRnBSong:
                case AchievementType.UnlockRnBSong:
                    return "RNB";
                default:
                    return string.Empty;
            }
        }
    }
}