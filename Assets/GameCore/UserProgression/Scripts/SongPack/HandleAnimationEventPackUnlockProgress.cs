using UnityEngine;

namespace TilesHop.Cores.UserProgression {
    public class HandleAnimationEventPackUnlockProgress : MonoBehaviour {
        [SerializeField] private SongPackUnlockProgress packUnlockProgress;

        /// <summary>
        /// animation event handler
        /// </summary>
        public void HandleClosePackProgress() {
            packUnlockProgress.StartShowSongsAndAutoClose();
        }

        public void HandleRefreshUnlockedPack() {
            packUnlockProgress.ShowUnlocked();
        }
    }
}