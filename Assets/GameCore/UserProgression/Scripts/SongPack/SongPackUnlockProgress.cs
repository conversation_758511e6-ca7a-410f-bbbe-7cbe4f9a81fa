using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using DG.Tweening;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.UserProgression {
    public class SongPackUnlockProgress : PopupUI {
        private const  int                    MAX_PREVIEW_SONGS_COUNT = 6;
        private const  string                 POPUP_NAME              = "[UP2] ProgressPackUnlocked";
        private static SongPackUnlockProgress _instancedPopup;
        private event Action ActionClose;

        [SerializeField] private Image      imgPreviewTheme;
        [SerializeField] private Image      imgPreviewUnlockedTheme;
        [SerializeField] private Text       txtPackId;
        [SerializeField] private Text       txtPrevPackId;
        [SerializeField] private GameObject objAnimation;

        [Space]
        [SerializeField] private SongItemSmall prefSongItem;
        [SerializeField] private Transform songsLine1;
        [SerializeField] private Transform songsLine2;

        private List<CanvasGroup> _songPreviewItemCanvasGroups;

        private void OnValidate() {
            destroyOnClose = true;
        }

        private void Awake() {
            objAnimation.SetActive(false);
        }

        private void OnDestroy() {
            SongList.instance.SongScroller.OnSetDataDone -= ShowUnlockedAfterSongListSetData;
            
            // Kill all DOTween instances to prevent memory leaks
            DOTween.Kill(gameObject);
        }

        public static void Show(int packId, int prevPackId, Action onClose) {
            if (_instancedPopup == null) {
                var popup = Util.ShowPopUpCanvasHeight(POPUP_NAME);
                if (popup.TryGetComponent(out _instancedPopup)) {
                    _instancedPopup.Setup(packId, prevPackId, onClose);
                }
            } else {
                _instancedPopup.gameObject.SetActive(true);
                _instancedPopup.Setup(packId, prevPackId, onClose);
            }
        }

        private void Setup(int packId, int prevPackId, Action onClose) {
            ActionClose = onClose;
            int themeId = UserProgressionController.instanceSafe.GetIdTheme(packId);
            int prevThemeId = UserProgressionController.instanceSafe.GetIdTheme(prevPackId);

            var info = UserProgressionController.instanceSafe.packThemeData.GetInfo(themeId);
            var prevInfo = UserProgressionController.instanceSafe.packThemeData.GetInfo(prevThemeId);

            if (info == null || prevInfo == null) {
                Close();
                return;
            }

            // set theme avatars
            imgPreviewTheme.sprite = info.previewTheme;
            imgPreviewUnlockedTheme.sprite = prevInfo.previewTheme;

            // set text pack ID
            txtPrevPackId.text = $"{prevPackId} - {prevInfo.themeName.ToUpper()}";
            txtPackId.text = $"{packId} - {info.themeName.ToUpper()}";

            // setup list song items
            _songPreviewItemCanvasGroups = new List<CanvasGroup>(MAX_PREVIEW_SONGS_COUNT);
            
            var songPackGroup = UserProgressionController.instanceSafe.GetSongPackGroup(packId);
            var songs = SongManager.instance.GetSongsBySongPackData(songPackGroup.songs);

            if (prefSongItem != null) {
                int songIndex = 0;
                foreach (Song song in songs) {
                    if (song.IsHideSong()) {
                        continue;
                    }

                    Transform parent = songIndex < 3? songsLine1 : songsLine2;
                    songIndex++;
                    if (songIndex == MAX_PREVIEW_SONGS_COUNT) {
                        break;
                    }
                    
                    SongItemSmall songItem = Instantiate(prefSongItem, parent);
                    songItem.SetSong(song, string.Empty);
                    var itemCanvasGroup = songItem.gameObject.AddComponent<CanvasGroup>();
                    itemCanvasGroup.alpha = 0;
                    _songPreviewItemCanvasGroups.Add(itemCanvasGroup);
                }
            }

            // active animation
            objAnimation.SetActive(true);
            
            TrackingComplete();

            void TrackingComplete() {
                string songPackName = info.themeName;
                string lastSongPackName = prevInfo.themeName;
                StringBuilder allSongStr = new StringBuilder();
                foreach (var song in songs) {
                    if (allSongStr.Length != 0) {
                        allSongStr.Append(";");
                    }

                    allSongStr.Append(song.name);
                }

                UserProgressionTracking.Track_LevelUpPopup(Configuration.GetCurrentLevel(), packId, songPackName,
                    lastSongPackName,allSongStr.ToString(),songs.Count);
            }
        }

        protected override void OnDisable() {
            base.OnDisable();
            ActionClose?.Invoke();
            objAnimation.SetActive(false);
        }

        public void StartShowSongsAndAutoClose() {
            StartCoroutine(IEShowSongsAndAutoClose());
        }

        private IEnumerator IEShowSongsAndAutoClose() {
            // Apply fade-in to song items
            for (int i = 0; i < _songPreviewItemCanvasGroups.Count; i++) {
                _songPreviewItemCanvasGroups[i].DOFade(1f, 0.5f).SetEase(Ease.Linear).SetDelay(i * 0.4f);
            }

            yield return new WaitForSeconds(_songPreviewItemCanvasGroups.Count * 0.4f + 0.5f);
            
            // delay to close
            yield return YieldPool.GetWaitForSeconds(2f);
            Close();
        }
        
        public void ShowUnlocked() {
            UserProgressionController.instanceSafe.lastLevel++;
            SongList.instance.SongScroller.OnSetDataDone += ShowUnlockedAfterSongListSetData;
            StartCoroutine(IEFadeBuildList());
        }

        private IEnumerator IEFadeBuildList() {
            yield return SongList.instance.IEFadeSongList(0f, 0.5f);
            SongList.instance.BuildList();
            yield return SongList.instance.IEFadeSongList(1f, 0.5f);
        }

        private void ShowUnlockedAfterSongListSetData() {
            SongList.instance.SongScroller.ScrollerAdapter.ResetTopHeader();
            SongList.instance.SongScroller.userExpProgressBar.RestoreToMarkPosition();
        }

        public override bool HandleEventBack() {
            return false;
        }
    }
}
