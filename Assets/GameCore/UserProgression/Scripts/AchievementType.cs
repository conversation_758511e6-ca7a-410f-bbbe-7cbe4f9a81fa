// using System;
// namespace TileHop.Cores.UserProgression {
//     [Serializable]
//     public enum AchievementType {
//         None = 0,
//         PlaySong          = 1,
//         Reach1Star        = 2,
//         Reach2Star        = 3,
//         Reach3Star        = 4,
//         UnlockSongByAds   = 5,
//         UnlockSongByGem   = 6,
//         UnlockBall        = 7,
//         Replay            = 8,
//         UnlockTheme       = 9,
//         PlayRockSong      = 10,
//         PlayPopSong       = 11,
//         PlayHipHopSong    = 12,
//         PlayEDMSong       = 13,
//         PlayBalladSong    = 14,
//         PlayCountrySong   = 15,
//         PlayRnBSong       = 16,
//         UnlockRockSong    = 17,
//         UnlockPopSong     = 18,
//         UnlockHipHopSong  = 19,
//         UnlockEDMSong     = 20,
//         UnlockBalladSong  = 21,
//         UnlockCountrySong = 22,
//         UnlockRnBSong       = 23,
//     }
// }