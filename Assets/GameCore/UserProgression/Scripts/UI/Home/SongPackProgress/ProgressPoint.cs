using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.UserProgression {
    public class ProgressPoint : MonoBehaviour {
        [SerializeField] private GameObject icTick;
        [SerializeField] private GameObject icLock;
        [SerializeField] private Image backgroundTick;
        [SerializeField] private Image backgroundLock;

        private bool _isLocked;

        public void SetLock(bool isLocked) {
            _isLocked = isLocked;
        
            if (isLocked) {
                icTick.SetActive(false);
                icLock.SetActive(true);
            } else {
                icTick.SetActive(true);
                icLock.SetActive(false);
            }
        }
        
        public void SetColor(Color color) {
            if (_isLocked) {
                backgroundLock.color = color;    
            } else {
                backgroundTick.color = color;
            }
        }
    }   
}
