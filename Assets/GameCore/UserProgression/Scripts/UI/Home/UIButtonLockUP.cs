using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

public class UIButtonLockUP : MonoBehaviour {
    [SerializeField] private Button btnMain;

    public  UnityEvent onClick;
    public  string     locationName;
    private int        _packId;

    public string message => Util.BuildString(". ", _packId, locationName);
    
    private void Start() {
        btnMain.onClick.AddListener(OnBtnMainClick);
    }

    private void OnBtnMainClick() {
        onClick?.Invoke();
    }

    public void SetPackId(int id) {
        _packId = id;
    }
}
