using System.Collections;
using Com.TheFallenGames.OSA.Core.CustomScroller.SongScroller;
using DG.Tweening;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.UserProgression;
using UnityEngine;
using UnityEngine.UI;

public class SongPackEndingHeaderItem : SongPackHeaderBaseItem {
    [SerializeField] protected Button      btnDiscover;
    [SerializeField] protected CanvasGroup hintCanvasGroup;

    private Coroutine _ieShowHint;
    private bool      _isLocked;
    
    #if UNITY_EDITOR
    private void OnValidate() {
        if (btnDiscover) {
            btnDiscover.gameObject.SetActive(false);
        }
    }
    #endif

    protected override void Awake() {
        base.Awake();
        btnDiscover.onClick.AddListener(GoToSongDiscovery);
    }

    public override void SetData(IData data) {
        if (data is SongPackEndingHeaderData packHeader) {
            if (packHeader.isLocked) {
                hintText.gameObject.SetActive(true);
            } else {
                hintText.gameObject.SetActive(false);
                btnDiscover.gameObject.SetActive(true);
            }
            
            SetupData(packHeader.packLevel);
            SetupTheme();

            _isLocked = packHeader.isLocked;

            if (hintCanvasGroup != null) {
                hintCanvasGroup.alpha = 0f;
            }
        }
    }

    private void GoToSongDiscovery() {
        AnalyticHelper.Button_ClickDiscover("song_pack");
        HomeManager.instance.BtnSearchOnClick();
    }

    public override void OnHeaderClick() {
        base.OnHeaderClick();
        ShowHint();
    }

    protected override void SetupTheme() { }

    private void ShowHint() {
        if (hintCanvasGroup == null || !_isLocked) {
            return;
        }

        if (_ieShowHint != null) {
            StopCoroutine(_ieShowHint);
        }

        hintCanvasGroup.DOKill();
        _ieShowHint = StartCoroutine(IEShowHint());
    }

    private IEnumerator IEShowHint() {
        float duration = SongPackLockedHeaderItem.HINT_FADE_DURATION;
        float waitDuration = SongPackLockedHeaderItem.HINT_SHOW_TIME + duration;
        hintCanvasGroup.DOFade(1f, duration);
        yield return YieldPool.GetWaitForSeconds(waitDuration);
        hintCanvasGroup.DOFade(0f, duration);
    }

    private void OnDestroy() {
        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }
}
