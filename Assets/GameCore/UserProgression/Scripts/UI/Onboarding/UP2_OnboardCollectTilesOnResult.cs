using System.Collections;
using TilesHop.Cores.Pooling;
using TMPro;
using UnityEngine;

// ReSharper disable once InconsistentNaming
public class UP2_OnboardCollectTilesOnResult : BubbleChatOnboadingItem {
    [SerializeField] private TextMeshProUGUI valueText;
    [SerializeField] private GameObject      tapToContinueObject;

    private void OnEnable() {
        StartCoroutine(ShowTapToContinue());
    }

    protected override IEnumerator Start() {
        if (!Util.IsGameResultScene()) {
            Close();
            yield break;
        }
        
        valueText.text = $"<b>{Spawner.jumpCount}</b> <sprite=0> {LocalizationManager.instance.GetLocalizedValue("TILES")}";
        delayToClose = 2f;
        yield return base.Start();
    }

    private IEnumerator ShowTapToContinue() {
        tapToContinueObject.SetActive(false);
        yield return YieldPool.GetWaitForSeconds(2f);
        tapToContinueObject.SetActive(true);
    }
}
