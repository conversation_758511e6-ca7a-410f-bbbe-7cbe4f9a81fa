using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Music.ACM;
using Sirenix.OdinInspector;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.Serialization;

namespace TilesHop.Cores.UserProgression {
    [Serializable]
    public class SongPacksManager {
        [SerializeField]  public int                                levelSongPackRewarded;
        [ShowInInspector] public Dictionary<int, SongPackDataGroup> dictSongPacks;

        public  int  MaxPack;
        public  int  TotalStar;
        private bool _isValidConfig;
        public  int  IdLastSongPack;
        public  bool inited;

        [ShowInInspector] private List<int> _rewardPack = new();

        private readonly string _fileConfigPath   = "userprogression_songpacks_config.dat";
        private readonly string _fileUserDataPath = "userprogression_songpacks_userdata.dat";
        private readonly string _hashConfigPath   = "userprogression_songpacks_config";

        public SongPacksManager() {
            levelSongPackRewarded = -1;
        }

        public IEnumerator DownloadServerConfig(Action callback) {
            dictSongPacks = new Dictionary<int, SongPackDataGroup>();
            SceneFader.instance.ShowOverlay();
            string url = RemoteConfigBase.instance.UserProgression_SongPack_ConfigUrl;

            UnityWebRequest request = UnityWebRequest.Get(url);
            yield return request.SendWebRequest();

            _isValidConfig = false;
            string content = string.Empty;
            if (request.result == UnityWebRequest.Result.Success) {
                content = request.downloadHandler.text;
                ParseData(content, url);
            } else {
                if (Music.ACM.Utils.IsInternetReachable) {
                    CustomException.Fire("UserProgression-SongPack-Config", $"Cant download config from url:{url}");
                } else {
                    Logger.LogError(
                        "[Offline] UserProgression-SongPack-Config" + $"Cant download config from url:{url}");
                }
            }

            // nếu parse data từ request thành công => cache data
            if (_isValidConfig) {
                string oldHash = PlayerPrefs.GetString(_hashConfigPath);
                string newHash = Util.ComputeHash(content);
                if (!newHash.Equals(oldHash)) {
                    PlayerPrefs.SetString(_hashConfigPath, newHash);
                    SaveData.Save(_fileConfigPath, content);
                }
            } else {
                // nếu không lấy được data từ remote, sử dụng data đã cache (nếu có) hoặc data mặc định
                if (PlayerPrefs.HasKey(_hashConfigPath)) { //exist old config
                    Logger.EditorLog("UserProgression", "Load Song pack from local cached!");

                    string cachedContent = string.Empty;
                    SaveData.Load(_fileConfigPath, ref cachedContent);

                    ParseData(cachedContent, _fileConfigPath);
                } else {
                    if (Configuration.instance.defaultUserProgressSongPackData != null) {
                        content = Configuration.instance.defaultUserProgressSongPackData.text;
                        ParseData(content, null);
                    } else {
                        Logger.EditorLogError("Can't load data and no default config for it!!!");
                    }
                }
            }

            Util.WaitSongListDone(() => {
                ValidateSongPacks(1);
                SceneFader.instance.HideOverlay();
                callback?.Invoke();
                inited = true;
            });
        }

        private void ParseData(string content, string url) {
            if (!string.IsNullOrEmpty(content)) {
                int order = 0;
                List<SongPackData> listDatas = CSVReader.ProcessDataCSV<SongPackData>(content, url);
                foreach (var songData in listDatas) {
                    int level = songData.Pack_Level;
                    MaxPack = Mathf.Max(MaxPack, level);
                    if (!dictSongPacks.ContainsKey(level)) {
                        dictSongPacks.Add(level, new SongPackDataGroup(level, false));
                    }

                    dictSongPacks[level].Add(songData);
                    songData.order = order;
                    order++;
                    _isValidConfig = true;
                }

                foreach (var group in dictSongPacks.Values) {
                    group.songs.Sort();
                }
            }

            LoadUserData();
            if (dictSongPacks != null) {
                foreach (var reward in _rewardPack) {
                    if (dictSongPacks.ContainsKey(reward)) {
                        dictSongPacks[reward].SetGotReward();
                    }
                }
            }
        }

        /// <summary>
        /// When user upgrade level
        /// </summary>
        /// <param name="level">Current level of user</param>
        /// <returns>List of all songpack data rewards</returns>
        public List<SongPackData> GetMultipleRewards(int level) {
            if (levelSongPackRewarded >= level)
                return null;

            List<SongPackData> songs = new List<SongPackData>();
            for (int i = levelSongPackRewarded + 1; i <= level; i++) {
                if (dictSongPacks.ContainsKey(i)) {
                    songs.AddRange(dictSongPacks[i].songs);
                }
            }

            return songs;
        }

        public bool IsReachUnlockAll(int level) {
            return level > MaxPack;
        }

        public List<SongPackData> GetAllSongs() {
            List<SongPackData> songs = new List<SongPackData>();
            foreach (var group in dictSongPacks) {
                if (group.Key == MaxPack + 1)
                    continue;

                songs.AddRange(group.Value.songs);
            }

            return songs;
        }

        public void ValidateSongPacks(int source) {
            int maxStar;
            int curStar;
            byte star;
            int previousSong;
            StringBuilder wantedList = new StringBuilder();
            StringBuilder realList = new StringBuilder();
            StringBuilder missingList = new StringBuilder();
            List<Song> songsInPack = new();
            if (dictSongPacks == null || dictSongPacks.Count < 1) {
                CustomException.Fire("User Progression", $"[{source}] DictSongPack null!");
                return;
            }

            foreach (var pack in dictSongPacks.Values) {
                if (pack.isLastPack)
                    continue;

                curStar = 0;
                maxStar = 0;
                previousSong = pack.songs.Count;
                wantedList.Clear();
                realList.Clear();
                for (int i = pack.songs.Count - 1; i >= 0; i--) {
                    var songData = pack.songs[i];
                    wantedList.Append(songData.AcmId ?? "NULL");
                    wantedList.Append(";");
                    var song = SongManager.instance.GetSongByAcmId(songData.AcmId);
                    if (song != null && !song.IsHideSong()) {
                        //need recalculate
                        star = (byte) Configuration.GetBestStars(song.path);
                        curStar += star;
                        maxStar += 3;
                        songData.SongPath = song.path;
                        songsInPack.Add(song);
                        song.songPackId = pack.level;
                        realList.Append(song.acm_id_v3);
                        realList.Append(";");
                    } else {
                        pack.songs.RemoveAt(i);
                        missingList.Append(songData.AcmId ?? "NULL");
                        missingList.Append(";");
                    }
                }

                pack.SetStar(curStar, maxStar);
                TotalStar += curStar;

                if (maxStar < 3 * previousSong) {
                    if (maxStar == 0) {
                        CustomException.Fire("User Progression",
                            $"[{source}] Song Pack {pack.level} is empty! {previousSong}. {wantedList}");
                    } else {
                        CustomException.Fire("User Progression",
                            $"[{source}] Song Pack {pack.level} is missing song! {previousSong}. {missingList}");
                    }
                }
            }

            IdLastSongPack = MaxPack + 1;
            if (dictSongPacks.ContainsKey(IdLastSongPack))
                return; // already added

            dictSongPacks.Add(IdLastSongPack, new SongPackDataGroup(IdLastSongPack, true));
            curStar = 0;
            maxStar = 0;
            List<Song> valueCollection = SongManager.instance.GetSongsAsList();
            foreach (var song in valueCollection) {
                if (songsInPack.Contains(song))
                    continue;

                dictSongPacks[IdLastSongPack].Add(new SongPackData(song) {
                    Pack_Level = IdLastSongPack
                });
                star = (byte) Configuration.GetBestStars(song.path);
                curStar += star;
                maxStar += 3;

            }

            dictSongPacks[IdLastSongPack].SetStar(curStar, maxStar);
            TotalStar += curStar;
        }

        public void SetLevelReward(int level) {
            levelSongPackRewarded = level;
        }

        public SongPackData GetSongPackData(string acmid) {
            foreach (SongPackDataGroup group in dictSongPacks.Values) {
                foreach (SongPackData song in group.songs) {
                    if (song.AcmId.Equals(acmid)) {
                        return song;
                    }
                }
            }

            return null;
        }

        public SongPackDataGroup GetSongPackGroup(Song song) {
            if (song == null) {
                return null;
            }

            if (song.acm_id_v3.IsNullOrEmpty()) {
                return null;
            }

            if (song.songPackId != null && dictSongPacks.ContainsKey((int) song.songPackId)) {
                return dictSongPacks[(int) song.songPackId];
            }

            foreach (SongPackDataGroup group in dictSongPacks.Values) {
                if (group.isLastPack)
                    continue;

                foreach (SongPackData songData in group.songs) {
                    if (songData.AcmId.Equals(song.acm_id_v3)) {
                        return group;
                    }
                }
            }

            return null;
        }

        public SongPackDataGroup GetSongPackGroup(int idPack) {
            return dictSongPacks.ContainsKey(idPack) ? dictSongPacks[idPack] : null;
        }

        private void LoadUserData() {
            _rewardPack = new List<int>();
            SaveData.Load(_fileUserDataPath, ref _rewardPack);
        }

        private void SaveUserData() {
            global::SaveData.Save(_fileUserDataPath, _rewardPack);
        }

        public (int idPack, bool hasReward) CheckCollectStar(string acmid, int amount) {
            foreach (var pack in dictSongPacks.Values) {
                if (pack.IsBelongToPack(acmid)) {
                    TotalStar += amount;
                    return (pack.level, pack.UpdateStar(amount));
                }
            }

            return (-1, false);
        }

        public RewardItem GetRewardPack(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack)) {
                return null;
            }

            return dictSongPacks[idPack].reward;
        }
        
        public RewardItem GetRewardForUnlockPack(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack)) {
                return null;
            }

            return dictSongPacks[idPack].rewardForUnlock;
        }

        public void ReceiveRewardPack(int idPack) {
            dictSongPacks[idPack].SetGotReward();
            _rewardPack.Add(idPack);
            SaveUserData();
        }

        public int GetThemePack(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack))
                return -1;

            return dictSongPacks[idPack].idTheme;
        }

        public int GetStarRequired(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack))
                return -1;

            return dictSongPacks[idPack].requiredStar;
        }

        public int GetPrizeId(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack))
                return -1;

            return dictSongPacks[idPack].reward?.valueInt ?? -1;
        }

        public string GetPrizeBall(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack)) {
                return null;
            }

            RewardItem rewardItem = dictSongPacks[idPack].reward;
            if (rewardItem != null && rewardItem.type == RewardType.Skin) {
                return BallManager.instance.GetBallName(rewardItem.valueInt);
            }

            return string.Empty;
        }

        public int GetPrizeGem(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack)) {
                return 0;
            }

            RewardItem rewardItem = dictSongPacks[idPack].reward;
            if (rewardItem != null && rewardItem.type == RewardType.Gem) {
                return rewardItem.valueInt;
            }

            return 0;
        }

        public bool ContainThisSong(Song song) {
            return song.songPackId != null;
        }

        public int GetNumberSongOfPack(int idPack) {
            if (!dictSongPacks.ContainsKey(idPack))
                return -1;

            return dictSongPacks[idPack].songs.Count;
        }
    }
}