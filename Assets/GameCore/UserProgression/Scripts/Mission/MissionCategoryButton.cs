using System;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace TilesHop.Cores.Hybrid {
    public class MissionCategoryButton : MonoBehaviour {
        public static Action<MissionCategoryButton> OnBtnCategoryClicked;

        [SerializeField] private Color           colorOn;
        [SerializeField] private Color           colorOff;
        [SerializeField] private GameObject      selectIndicator;
        [SerializeField] private MissionCategory missionCategory;
        [SerializeField] private Button          btn;
        [SerializeField] private Image[]         images;
        [SerializeField] private Text            titleText;

        public MissionCategory MissionCategory => missionCategory;
        private void Start() {
            btn.onClick.AddListener(OnBtnClick);
        }

        private void OnBtnClick() {
            OnBtnCategoryClicked?.Invoke(this);
        }

        public void DeActive() {
            selectIndicator.gameObject.SetActive(false);
            foreach (var img in images) {
                if (img == null) {
                    continue;
                }

                img.color = colorOff;
            }

            titleText.color = colorOff;
        }

        public void Active() {
            selectIndicator.gameObject.SetActive(true);
            foreach (var img in images) {
                if (img == null) {
                    continue;
                }

                img.color = colorOn;
            }

            titleText.color = colorOn;
        }
    }
}