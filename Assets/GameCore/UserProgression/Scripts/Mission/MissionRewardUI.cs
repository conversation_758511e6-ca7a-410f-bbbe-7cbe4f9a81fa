using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.Hybrid {
    public class MissionRewardUI : PopupUI {
        [SerializeField] private RewardGem     uiRewardGem;
        [SerializeField] private RewardBall    uiRewardBall;
        [SerializeField] private Reward<PERSON>ong    uiRewardSong;
        [SerializeField] private RewardTheme   uiRewardTheme;
        [SerializeField] private RectTransform parentHolder;
        [SerializeField] private Button        claimBtn;

        private int        _rewardGem;
        private List<int>  _rewardBalls  = new List<int>();
        private List<Song> _rewardSongs  = new List<Song>();
        private List<int>  _rewardThemes = new List<int>();

        private ItemMissionScriptV2 _itemMission;
        private MissionCategory     _missionCategory;
        private bool                _claimed;

        private readonly string _location = "mission";

        private void Start() {
            claimBtn.onClick.AddListener(GetReward);
        }

        public void SetData(ItemMissionScriptV2 mission, MissionCategory category, int gemCount = 0, int ballCount = 0,
                            int songCount = 0, int themeCount = 0) {
            _itemMission = mission;
            _missionCategory = category;
            _rewardGem = gemCount;

            for (int i = 0; i < ballCount; i++) {
                var ballId = RemoteConfigBase.instance.Hybrid_Mission_RewardBall_Pool_IsEnable
                    ? BallManager.GetRewardBallFromPool(RemoteConfigBase.instance.Hybrid_Mission_RewardBall_Random,
                        BallRewardSource.Mission)
                    : BallManager.GetRewardBall(RemoteConfigBase.instance.Hybrid_Mission_RewardBall_Random);
                if (ballId >= 0) {
                    _rewardBalls.Add(ballId);
                }
            }

            for (int i = 0; i < songCount; i++) {
                var song = SongManager.instance.GetRewardSong();
                if (song != null) {
                    _rewardSongs.Add(song);
                }
            }

            for (int i = 0; i < themeCount; i++) {
                var themeId = ThemeManager.instance.GetRewardTheme();
                if (themeId >= 0) {
                    _rewardThemes.Add(themeId);
                }
            }

            CreateView();
        }

        private void CreateView() {
            int index = 0;
            if (_rewardGem > 0) {
                uiRewardGem.SetData(_rewardGem);
                uiRewardGem.gameObject.SetActive(true);
                index++;
            } else {
                uiRewardGem.gameObject.SetActive(false);
            }

            if (_rewardBalls.Count != 0) {
                uiRewardBall.SetData(_rewardBalls[0]);
                uiRewardBall.gameObject.SetActive(true);
                index++;
                for (int i = 1; i < _rewardBalls.Count; i++) {
                    if (Configuration.IsEarnBall(_rewardBalls[i]))
                        continue;

                    var cloneBall = Instantiate(uiRewardBall, parentHolder);
                    cloneBall.transform.SetSiblingIndex(index);
                    cloneBall.SetData(_rewardBalls[i]);
                    index++;
                }
            } else {
                uiRewardBall.gameObject.SetActive(false);
            }

            if (_rewardSongs.Count != 0) {
                uiRewardSong.SetData(_rewardSongs[0], _location);
                uiRewardSong.gameObject.SetActive(true);
                index++;
                for (int i = 1; i < _rewardSongs.Count; i++) {
                    var cloneSong = Instantiate(uiRewardSong, parentHolder);
                    cloneSong.transform.SetSiblingIndex(index);
                    cloneSong.SetData(_rewardSongs[i], _location);
                    index++;
                }
            } else {
                uiRewardSong.gameObject.SetActive(false);
            }

            if (_rewardThemes.Count != 0) {
                uiRewardTheme.SetData(_rewardThemes[0], _location);
                uiRewardTheme.gameObject.SetActive(true);
                index++;
                for (int i = 1; i < _rewardThemes.Count; i++) {
                    if (Configuration.IsEarnTheme(_rewardThemes[i]))
                        continue;

                    var cloneTheme = Instantiate(uiRewardTheme, parentHolder);
                    cloneTheme.transform.SetSiblingIndex(index);
                    cloneTheme.SetData(_rewardThemes[i], _location);
                    index++;
                }
            } else {
                uiRewardTheme.gameObject.SetActive(false);
            }
        }

        private void GetReward() {
            if (_claimed)
                return;

            _claimed = true;

            _itemMission.Missionv2.Claim(_rewardGem, _rewardBalls);
            if (_rewardGem > 0) {
                Configuration.UpdateDiamond(_rewardGem, GetIncomeType());
            }

            if (_rewardBalls.Count > 0) {
                foreach (var rewardBall in _rewardBalls) {
                    if (Configuration.IsEarnBall(rewardBall))
                        continue;

                    Configuration.SetOpenBall(rewardBall, 0, _location, true, false);
                }
            }

            if (_rewardSongs.Count > 0) {
                foreach (var rewardSong in _rewardSongs) {
                    Configuration.instance.SetOpenSong(rewardSong, 0, true, SongUnlockType.mission);
                }
            }

            if (_rewardThemes.Count > 0) {
                foreach (var rewardTheme in _rewardThemes) {
                    if (Configuration.IsEarnTheme(rewardTheme))
                        continue;

                    ThemeManager.instance.SetOpenTheme(rewardTheme, 0);
                }
            }

            _itemMission.UpdateBtnState();
            MissionManager.instanceSafe.ValidateAndUpdateInstantMission(_itemMission.Missionv2);
            MissionManager.instanceSafe.SaveData();
            this.Close();
        }
        private string GetIncomeType() {
            switch (_missionCategory) {
                case MissionCategory.Daily:
                    return CurrencyEarnSource.mission_daily.ToString();

                case MissionCategory.Weekly:
                    return CurrencyEarnSource.mission_weekly.ToString();

                case MissionCategory.Instant:
                    return CurrencyEarnSource.mission_instant.ToString();
            }

            return CurrencyEarnSource.NONE.ToString();
        }
    }
}