using System.Collections.Generic;
using UnityEngine;

namespace TilesHop.Cores.UserProgression {
    public static class UserProgressionTracking {
        private const string level_progress         = "level_progress";
        private const string level_complete         = "level_complete";
        private const string level_up_popup         = "level_up_popup";
        private const string level_info_click       = "level_info_click";
        private const string prize_progress         = "prize_progress";
        private const string prize_complete         = "prize_complete";
        private const string prize_info_click       = "prize_info_click";
        private const string dual_unlock_impression = "dual_unlock_impression";

        #region Level Progression

        public static void Track_LevelProgress(int tilesEarn, int songPackID, int currentLevel, int totalTilesEarn,
                                               int tilesRequired) {
            Dictionary<string, object> param = new() {
                {"tiles_earn", tilesEarn},
                {"songpack_id", songPackID},
                {TRACK_PARAM.current_level, currentLevel},
                {"total_tiles_earn", totalTilesEarn},
                {"tiles_required", tilesRequired},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, level_progress);
            AnalyticHelper.LogEvent(level_progress, param);
        }

        public static void Track_LevelComplete(int currentLevel, int beforeLevel, int songPackID, int lastSongPackID,
                                               int totalTilesEarn, int tilesRequired) {
            Dictionary<string, object> param = new() {
                {TRACK_PARAM.current_level, currentLevel},
                {"before_level", beforeLevel},
                {"songpack_id", songPackID},
                {"last_songpack_id", lastSongPackID},
                {"total_tiles_earn", totalTilesEarn},
                {"tiles_required", tilesRequired},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, level_complete);
            AnalyticHelper.LogEvent(level_complete, param);
        }

        public static void Track_LevelUpPopup(int currentLevel, int songPackID, string songPackName,
                                              string lastSongPackName, string available_song_name,
                                              int number_available_song_name) {
            Dictionary<string, object> param = new() {
                {TRACK_PARAM.current_level, currentLevel},
                {"songpack_id", songPackID},
                {"songpack_name", songPackName},
                {"last_songpack_name", lastSongPackName},
                {"available_song_name", available_song_name},
                {"number_available_song_name", number_available_song_name},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, level_up_popup);
            AnalyticHelper.LogEvent(level_up_popup, param);
        }

        public static void Track_LevelInfoClick(string name, int current_level, int songpack_id) {
            Dictionary<string, object> param = new() {
                {"name", name},
                {TRACK_PARAM.current_level, current_level},
                {"songpack_id", songpack_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, level_info_click);
            AnalyticHelper.LogEvent(level_info_click, param);
        }

        #endregion

        #region Prize Progression

        public static void Track_PriceProcess(int songPackID, string prizeBall, int prizeGem, int starsEarn,
                                              int currentLevel, int totalStarsEarn, int starsRequired) {
            Dictionary<string, object> param = new() {

                {"name", "grand_prize"},
                {"songpack_id", songPackID},
                {"stars_earn", starsEarn},
                {TRACK_PARAM.current_level, currentLevel},
                {"total_stars_earn", totalStarsEarn},
                {TRACK_PARAM.stars_required, starsRequired},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };

            if (prizeGem > 0) {
                param.Add("prize_gem", prizeGem);
            }

            if (!string.IsNullOrEmpty(prizeBall)) {
                param.Add("prize_ball", prizeBall);
            }

            AnalyticHelper.UpdateParamsAccumulatedCount(param, prize_progress);
            AnalyticHelper.LogEvent(prize_progress, param);
        }

        public static void Track_PriceComplete(int currentLevel, int songPackID, string prizeBall, int prizeGem,
                                               int totalStarsEarn, int starsRequired) {
            Dictionary<string, object> param = new() {
                {"name", "grand_prize"},
                {TRACK_PARAM.current_level, currentLevel},
                {"songpack_id", songPackID},
                {"prize_ball", prizeBall},
                {"prize_gem", prizeGem},
                {"total_stars_earn", totalStarsEarn},
                {TRACK_PARAM.stars_required, starsRequired},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, prize_complete);
            AnalyticHelper.LogEvent(prize_complete, param);
        }

        public static void Track_PriceInfoClick(int currentLevel, int songPackID, string prizeBall, int prizeGem) {
            Dictionary<string, object> param = new() {
                {"name", "grand_prize"},
                {"prize_ball", prizeBall},
                {"prize_gem", prizeGem},
                {TRACK_PARAM.current_level, currentLevel},
                {"songpack_id", songPackID},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.UpdateParamsAccumulatedCount(param, prize_info_click);
            AnalyticHelper.LogEvent(prize_info_click, param);
        }

        #endregion

        #region Onboarding

        #endregion

        #region Dual Unlock

        private const string KEY_DUAL_UNLOCK_IMPRESSION = "dual_unlock_impression_song_key";

        public static void Track_DualUnlockImpression(Song song, int current_balance, int gemRequired) {
            if (song == null || string.IsNullOrEmpty(song.acm_id_v3))
                return;

            string listSongFired = PlayerPrefs.GetString(KEY_DUAL_UNLOCK_IMPRESSION);
            string key = $";{song.acm_id_v3};";
            if (listSongFired.Contains(key))
                return;

            string eventName = dual_unlock_impression;
            Dictionary<string, object> param = new() {
                {TRACK_PARAM.songpack_id, song.songPackId},
                {TRACK_NAME.song_acm_id, song.acm_id_v3},
                {TRACK_NAME.song_name, song.name},
                {TRACK_PARAM.current_level, Configuration.GetCurrentLevel()},
                {TRACK_PARAM.current_balance, current_balance},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount},
                {"gems_required", gemRequired}
            };
            if (RemoteConfig.instance.DifficultyTag_IsEnable) {
                param.TryAdd(TRACK_NAME.difficulty_tag, song.GetDifficultyTag().ToString().ToLower());
            }

            AnalyticHelper.UpdateParamsAccumulatedCount(param, eventName);
            AnalyticHelper.LogEvent(eventName, param);

            listSongFired += key;
            PlayerPrefs.GetString(KEY_DUAL_UNLOCK_IMPRESSION, listSongFired);
        }

        #endregion
    }
}