using System.Collections;
using System.Collections.Generic;
using Inwave;
using TilesHop.Cores.Pooling;
using UnityEngine;

namespace TilesHop.Cores.UserProgression {

    public class UIAchievementTab : MonoBehaviour {

        [Header("Popup Achievements")] [SerializeField]
        private StandardScrollerAdapter scrollerAdapter;

        [SerializeField] private OptimizedCellView prefItem;

        [SerializeField] private Sprite           iconTwitter;

        //private

        private List<IData> _datas;
        private Transform   _cachedDiamondTargetPosition;

        #region Unity Methods
        private void Awake() {
            prefItem = Resources.Load<OptimizedCellView>("UI/AchievementItem2");
            scrollerAdapter.Init(prefItem.transform as RectTransform);
        }
        private void OnEnable() {
            if (scrollerAdapter.IsInitialized) {
                scrollerAdapter.SetNormalizedPosition(1f); // reset top position
            }
            scrollerAdapter.OnItemVisible += ScrollerAdapterOnOnItemVisible;
            BuildList();
            AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.ACHIEVEMENT_POPUP);
        }
        private void OnDisable() {
            scrollerAdapter.OnItemVisible -= ScrollerAdapterOnOnItemVisible;
        }
        #endregion
        private void BuildList() {
            _datas = new List<IData> {
                new AchievementObj() {
                    type = AchievementType.TWITTER_SHARE,
                    icon = iconTwitter
                },
            };

            List<AchievementObj> achievementList = AchievementCenter.GetAchievementList();
            if (!achievementList.IsNullOrEmpty()) {
                achievementList.Sort();
                for (int i = achievementList.Count - 1; i > 0; i--) {
                    if (achievementList[i].type == achievementList[i - 1].type) {
                        if (achievementList[i - 1].status == AchievementStatus.LOCK &&
                            achievementList[i].status == AchievementStatus.LOCK) {
                            achievementList.RemoveAt(i);
                        }
                    }
                }
                _datas.AddRange(achievementList);
            }

            Configuration.instance.StartCoroutine(SetData());
        }

        private void ScrollerAdapterOnOnItemVisible(StandardItemViewsHolder item) {
            if (_datas.Count <= item.ItemIndex) {
                return;
            }

            if (item.cellView is AchievementItem view) {
                view.SetValues(_datas[item.ItemIndex] as AchievementObj);
            }
        }

        private IEnumerator SetData() {
            while (!scrollerAdapter.IsInitialized) {
                yield return YieldPool.GetWaitForEndOfFrame();
            }

            scrollerAdapter.SetItems(_datas);
        }
    }
}