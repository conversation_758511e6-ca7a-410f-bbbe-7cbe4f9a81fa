using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace TilesHop.LiveEvent {
    [Serializable]
    public class SongGroupData : IData {
        [NonSerialized] public byte Id; // bắt đầu từ 0
        public string Name; // tên của group
        public string BgUrl; // đường dẫn ảnh BG nếu có
        public string SongIds; // danh sách acm_id_v3 các bài hát trong group
        public string GiftType; // loại gift: hiện chưa dùng, đang mặc định là ball, sau thêm loại khác cần sửa thêm
        public string GiftValue; // quà tặng ball nếu có
        public byte Order; // thứ tự xuất hiện trong list

        [NonSerialized] public  List<Song> songList;
        [NonSerialized] private bool       isInited = false;

        public int IdGift { get; private set; }

        public void Init(byte id, bool useToken, bool isReinit) {
            if (isInited && !isReinit) {
                return; // k cần init lại nữa
            }

            Id = id;
            isInited = true;
            songList = new List<Song>();
            IdGift = -1;
            if (int.TryParse(GiftValue, out int tempId)) {
                IdGift = tempId;
            }

            string[] songs = SongIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            var results = SongManager.instance.GetSongsByIDsKeepOrder(songs.ToList());
            if (results != null) {
                foreach (var song in results) {
                    song.IsBelongToLiveEvent = true;
                    if (useToken) {
                        song.type = SONGTYPE_STRING.EVENT;
                        if (song.savedType == SONGTYPE.OPEN &&
                            PlayerPrefs.GetString(song.path) == SONGTYPE_STRING.OPEN) {
                            // bài hát này đã được unlock rồi
                        } else {
                            song.savedType = SONGTYPE.EVENT; // Nếu chưa unlock bài hát => đưa bài hát thành type Event
                        }
                    }

                    songList.Add(song);
                }
            }
        }

        public Song GetSong(string acmIDV3) {
            return songList.FirstOrDefault(song => song.acm_id_v3.Equals(acmIDV3));
        }

        public List<string> GetSongIds() {
            if (string.IsNullOrEmpty(SongIds)) {
                return null;
            }

            string[] strings = SongIds.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
            return strings.ToList();
        }

        public bool CanUnlock(int joinedDay) {
            // if (joinedDay > Id) return true;
            // else return false;
            return true;
        }

        public void ReplaceSong(List<Song> oldSongs, List<Song> replaceSongs, bool useToken) {
            for (byte i = 0; i < oldSongs.Count; i++) {
                var song = oldSongs[i];
                var replaceSong = replaceSongs[i];

                //replace string data
                SongIds = SongIds.Replace(song.acm_id_v3, replaceSong.acm_id_v3);

                //remove liveEventdata from old song
                song.IsBelongToLiveEvent = false;
                if (useToken) {
                    song.type = SONGTYPE_STRING.VIDEO;
                }

                //add liveEventData to new song
                replaceSong.IsBelongToLiveEvent = true;
                if (useToken) {
                    replaceSong.type = SONGTYPE_STRING.EVENT;
                    replaceSong.savedType = SONGTYPE.EVENT; // Nếu chưa unlock bài hát => đưa bài hát thành type Event
                }

                //replace new song to old song
                int index = songList.IndexOf(song);
                if (index >= 0) {
                    songList[index] = replaceSong;
                }
            }
        }

        public void ReplaceGift(int newBall) {
            GiftValue = newBall.ToString();
            IdGift = newBall;
        }
    }
}