using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Music.ACM;
using UnityEngine;
using UnityEngine.Analytics;

// ReSharper disable InconsistentNaming

namespace TilesHop.LiveEvent {
    [Serializable]
    public class LiveEventData : IData {
        #region Serialize Data

        public                   int    Id; // id của event
        public                   string Type; // type của event. Hiện chưa dùng
        public                   string Title; // title của event
        public                   string EventUrl; // đường dẫn của event
        public                   string BannerUrl; // đường dẫn của banner event
        [SerializeField] private string StartTime; // thời gian bắt đầu event
        [SerializeField] private string EndTime; // thời gian kết thúc event

        // Nếu Event EndTime kết thúc mà số ngày chơi của user tính từ lúc active vẫn chưa đủ 7 ngày thì được gia hạn cho đủ
        public int    MinTotalDay;
        public string Data;

        #endregion

        [NonSerialized] public Sprite IconToken; // icon của token

        private DateTime _startTime;

        public DateTime TimeStart {
            get {
                if (_startTime.Ticks != 0) return _startTime;
                if (DateTime.TryParse(StartTime, out _startTime)) return _startTime;
                _startTime = DateTime.MinValue.AddDays(1); // add thêm 1 day để tránh Ticks = 0
                CustomException.Fire("[LiveEvent]",
                    $"Wrong DateTime format StartTime of event {Id} | {Title} | {StartTime}");
                return _startTime;
            }
        }

        private DateTime _endTime;

        public DateTime TimeEnd {
            get {
                if (_endTime.Ticks == 0) {
                    if (DateTime.TryParse(EndTime, out DateTime _value)) {
                        _endTime = _value.Add(new TimeSpan(23, 59, 59));
                    } else {
                        _endTime = DateTime.MinValue.AddDays(5);
                        CustomException.Fire("[LiveEvent]",
                            $"Wrong DateTime format EndTime of event {Id} | {Title} | {EndTime}");
                    }
                }

                return _endTime;
            }
            set => _endTime = value;
        }


        private bool          parseEventTye = false;
        private LiveEventType _liveEventType;

        public LiveEventType liveEventType {
            get {
                if (!parseEventTye) {
                    if (!Enum.TryParse(Type, out _liveEventType)) {
                        _liveEventType = LiveEventType.SkinQuest;
                    }

                    parseEventTye = true;
                }

                return _liveEventType;
            }
        }


        [NonSerialized] public LiveEventStatus status;
        [NonSerialized] public TimeSpan        RemainTime;
        public event Action<TimeSpan>          OnTick;

        public void Tick() {
            switch (status) {
                case LiveEventStatus.PAST: // event qua rồi thì k cần countdown nữa
                    break;
                case LiveEventStatus.FUTURE:
                    RemainTime = RemainTime.Subtract(Configuration.OneSecond);
                    if (RemainTime.TotalSeconds < 1) {
                        status = LiveEventStatus.ACTIVE;
                        RemainTime = TimeEnd - DateTime.Now;
                        Logger.Log($"LiveEvents activate event {Title} from ticks");
                    }

                    break;

                case LiveEventStatus.ACTIVE:
                    RemainTime = RemainTime.Subtract(Configuration.OneSecond);
                    if (RemainTime.TotalSeconds < 1) {
                        status = LiveEventStatus.PAST;
                        RemainTime = TimeSpan.Zero;
                        Logger.Log($"LiveEvents deactivate event {Title} from ticks");
                    }

                    OnTick?.Invoke(RemainTime);
                    break;
            }
        }
    }
}