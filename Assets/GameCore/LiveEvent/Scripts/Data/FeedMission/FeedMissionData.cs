namespace TilesHop.LiveEvent {
    public class FeedMissionData : LiveEventDetailData {

        private bool _isValidData;
        public FeedMissionData(string data) {
            Logger.EditorLog("Live Event", $"Chưa làm parse data này: {data}");
        }

        public string GetTokenName() {
            return string.Empty;
        }

        public int GetSongPrice() {
            return 0;
        }

        public bool IsValidData() {
            return _isValidData;
        }
    }
}