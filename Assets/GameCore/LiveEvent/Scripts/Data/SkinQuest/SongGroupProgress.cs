using System;
using System.Collections.Generic;

namespace TilesHop.LiveEvent {
    [Serializable]
    public class SongGroupProgress {
        public int  CountTotalSong; // tổng số bài hát
        public int  CountUnlockSong; // tổng số bài hát đã unlock
        public bool GotReward; // đã nhận reward hay chưa

        public bool   CompleteUnlockProgress => (CountTotalSong != 0 && CountUnlockSong >= CountTotalSong);
        public bool   HaveReward => CompleteUnlockProgress && !GotReward;
        public int    CountRemainSong => CountTotalSong - CountUnlockSong;
        public string Status => CompleteUnlockProgress ? $"completed - {CountTotalSong}" : CountUnlockSong.ToString();

        public void SetData(List<Song> songList) {
            CountTotalSong = 0;
            CountUnlockSong = 0;
            foreach (var item in songList) {
                if (item.savedType == SONGTYPE.OPEN) {
                    CountUnlockSong++;
                }

                CountTotalSong++;
            }
        }
    }
}