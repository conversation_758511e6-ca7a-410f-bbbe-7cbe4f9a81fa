using System;
using System.Collections.Generic;
using UnityEngine;

namespace TilesHop.LiveEvent {
    public class SkinQuestProgress : LiveEventProgress {
        public List<SongGroupProgress> groups;

        public override void Init(LiveEventData eventData) {
            base.Init(eventData);
            groups = new List<SongGroupProgress>();
            if (eventData.liveEventType != LiveEventType.SkinQuest) {
                Logger.EditorLogError("Live Event", "Init Wrong progress");
                return;
            }

            var detailData = new SkinQuestData(eventData.Id, eventData.Data);
            foreach (var group in detailData.Groups) {
                var groupProgress = new SongGroupProgress {
                    CountTotalSong = @group != null
                        ? @group.SongIds.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Length
                        : 0
                };
                groups.Add(groupProgress);
            }
        }

        public void UnlockSong(int idGroup, List<Song> list) {
            if (groups != null && groups.Count > idGroup) {
                groups[idGroup].SetData(list);
            } else {
                Logger.EditorLogError("Live Event", $"Invalid group Id {idGroup}");
            }
        }

        private void CheckCompleteEvent() {
            bool complete = true;
            foreach (var group in groups) {
                if (!group.CompleteUnlockProgress) {
                    complete = false;
                    break;
                }
            }

            CompleteEvent = complete;
        }

        public override bool HasNotice(int songPrice) {
            foreach (var group in groups) {
                if (group.HaveReward) {
                    return true; // khi có phần thưởng chưa nhận
                }
            }

            if (songPrice > 0) {
                foreach (var group in groups) {
                    if (!group.CompleteUnlockProgress && TokenAmount >= songPrice) {
                        // đủ điều kiện unlock song
                        return true;
                    }
                }
            }

            return false;
        }

        public override bool HaveReward() {
            foreach (var group in groups) {
                if (group.HaveReward)
                    return true;
            }
            return false;
        }

        public override int GetGroupHaveReward() {
            for (var index = 0; index < groups.Count; index++) {
                if (groups[index].HaveReward)
                    return index;
            }

            return 0;
        }

        public bool IsUnlockGroup(int idGroup) {
            if (idGroup < 0) {
                Logger.EditorLogError("LiveEvent",$"Invalid id Group: {idGroup}");
                return false;
            }

            if (idGroup >= groups.Count) {
                Logger.EditorLogError("LiveEvent",$"Invalid id Group: {idGroup}");
                return false;
            }

            return groups[idGroup].CompleteUnlockProgress;
        }
    }
}