using System;
using System.Collections;
using System.Collections.Generic;
using Com.TheFallenGames.OSA.Core;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace TilesHop.LiveEvent {
    public class UIScrollGroupReward : MonoBehaviour {
        //public
        [SerializeField] private UIItemCharacterReward rewardPrefab;
        [SerializeField] private StandardScrollerAdapter   scrollerAdapter;
        [SerializeField] private GameObject            objProgressBar;
        [SerializeField] private Text                  txtProgress;
        [SerializeField] private Image                 imgBar;
        [SerializeField] private Button                btnEquip;

        [SerializeField] private Button btnEquipped;
        // [SerializeField] private Text                  txtEquip;

        //private

        private List<UIItemCharacterReward> _listItem;

        private SkinQuestItem _skinQuestItem;

        private bool        _isInit     = false;
        private bool        _isInitDone = false;
        private List<int>   _listBallRewards;
        private List<IData> _datas;

        private int                 _middleVHItemIndex;
        private int                 _indexSelected = -1;
        private int                 _orderSelected = -1;
        private StandardItemViewsHolder _currentViewHolder;

        private string _location = LOCATION_NAME.liveEvent.ToString();

        //Dragging
        private bool _isNavigating;

        public int                           IndexSelected => _indexSelected;
        public static event Action<int, int> OnSelectGroup;

        #region Unity Methods

        private void Awake() {
            scrollerAdapter.Init(rewardPrefab.transform as RectTransform);
            btnEquip.onClick.AddListener(BtnEquipOnClick);
        }

        private void OnEnable() {
            ResetVariables();
            scrollerAdapter.OnItemVisible += ScrollerAdapterOnOnItemVisible;
            scrollerAdapter.ScrollPositionChanged += ScrollerAdapterOnScrollPositionChanged;
            scrollerAdapter.OnBeginDragEvent += ScrollerAdapterOnOnBeginDragEvent;
            scrollerAdapter.OnEndDragEvent += ScrollerAdapterOnOnEndDragEvent;
            UIItemCharacterReward.OnClickItem += UIItemCharacterRewardOnOnClickItem;
            this._skinQuestItem =
                LiveEventManager.instance.GetLiveEvent(LiveEventManager.IdCurrentEvent) as SkinQuestItem;
            Initialization();
        }

        private void OnDisable() {
            scrollerAdapter.OnItemVisible -= ScrollerAdapterOnOnItemVisible;
            scrollerAdapter.ScrollPositionChanged -= ScrollerAdapterOnScrollPositionChanged;
            scrollerAdapter.OnEndDragEvent -= ScrollerAdapterOnOnEndDragEvent;
            scrollerAdapter.OnBeginDragEvent -= ScrollerAdapterOnOnBeginDragEvent;
            UIItemCharacterReward.OnClickItem -= UIItemCharacterRewardOnOnClickItem;
        }

        #endregion

        /// <summary>
        /// Reset lại các biến khi enable popup
        /// </summary>
        private void ResetVariables() {
            _isNavigating = false;
        }

        private void Initialization() {
            if (_isInit) {
                //update lại state của UI trong trường hợp user đã đổi equip ball ở shopball
                StartCoroutine(UpdateRewardUI(_skinQuestItem.IdCurrentGroup));
                return;
            }

            _isInit = true;
            _listBallRewards = new List<int>();

            int totalGroup = _skinQuestItem._skinQuestData.TotalOrders;
            for (int i = 0; i < totalGroup; i++) {

                int indexGroup = _skinQuestItem._skinQuestData.OrderToIndex(i);
                var group = _skinQuestItem._skinQuestData.Groups[indexGroup];
                if (group.songList.Count != 0) {
                    int indexBall = group.IdGift;
                    if (indexBall >= 0) {
                        _listBallRewards.Add(indexBall);
                    }
                }
            }

            if (_listBallRewards.Count == 0) {
                Debug.LogError("Không có item thưởng nào!!!!!");
            } else {
                _datas = new List<IData>();
                foreach (var index in _listBallRewards) {
                    _datas.Add(new HeaderData(index.ToString()));
                }

                StartCoroutine(IESetData());
            }
        }

        #region Scroller Adapter

        private bool isNeedScrollTo;

        private void ScrollerAdapterOnOnBeginDragEvent(PointerEventData obj) {
            if (_isNavigating) return;
            isNeedScrollTo = true;
        }

        private void ScrollerAdapterOnOnEndDragEvent(PointerEventData obj) {
            scrollerAdapter.Parameters.DragEnabled = true;
            if (isNeedScrollTo) {
                CheckChangeIndex();
                ScrollTo(_orderSelected, 0.2f);
            } else {
                ScrollTo(_orderSelected, 0.2f);
            }
        }

        private bool CheckChangeIndex() {
            _currentViewHolder =
                scrollerAdapter.Parameters.Snapper.GetMiddleVH(out float distanceToTarget) as StandardItemViewsHolder;
            if (_currentViewHolder != null) {
                _middleVHItemIndex = _currentViewHolder.ItemIndex;
                if (_middleVHItemIndex != _orderSelected) {
                    _orderSelected = _middleVHItemIndex;
                    _indexSelected = _skinQuestItem._skinQuestData.OrderToIndex(_orderSelected);
                    OnSelectGroup?.Invoke(_orderSelected, _indexSelected);
                    return true;
                }
            }

            return false;
        }

        private void ScrollerAdapterOnScrollPositionChanged(double obj) {
            if (!_isInitDone) return;
            if (CheckChangeIndex()) {
                if (isNeedScrollTo && scrollerAdapter.IsDragging) {
                    scrollerAdapter.Parameters.DragEnabled = false;
                    scrollerAdapter.Velocity = Vector2.zero;
                    isNeedScrollTo = false;
                    ScrollTo(_orderSelected);
                }
            }
        }

        private void ScrollerAdapterOnOnItemVisible(StandardItemViewsHolder item) {
            if (_datas.Count <= item.ItemIndex)
                return;

            if (item.cellView is UIItemCharacterReward view) {
                int groupId = _skinQuestItem._skinQuestData.OrderToIndex(item.ItemIndex);
                view.SetData(item.ItemIndex, _skinQuestItem, groupId, this);
            }
        }

        private IEnumerator IESetData() {
            while (!scrollerAdapter.IsInitialized) {
                yield return new WaitForEndOfFrame();
            }

            scrollerAdapter.SetItems(_datas);

            // giá trị mặc định là 1 khi chưa chọn current group
            _indexSelected = _skinQuestItem.IdCurrentGroup;
            _orderSelected = _skinQuestItem._skinQuestData.IndexToOrder(_skinQuestItem.IdCurrentGroup);
            if (_orderSelected < 0) {
                _orderSelected = _skinQuestItem.DEFAULT_ORDER_GROUP;
                _indexSelected = _skinQuestItem._skinQuestData.OrderToIndex(_orderSelected);
            }

            scrollerAdapter.ScrollTo(_orderSelected, 0.5f, 0.5f);

            _isInitDone = true;
            OnSelectGroup?.Invoke(_orderSelected, _indexSelected);
            _currentViewHolder =
                scrollerAdapter.Parameters.Snapper.GetMiddleVH(out float distanceToTarget) as StandardItemViewsHolder;
        }

        #endregion

        /// <summary>
        /// Func call when a song is unlocked
        /// </summary>
        public void RefreshData(Action<bool> onComplete) {
            if (_currentViewHolder == null) {
                Debug.LogError("Why _currentViewHolder is NULL!!!!");
                onComplete?.Invoke(false);
                return;
            } else {
                if (_currentViewHolder.cellView is UIItemCharacterReward itemCharacterReward) {
                    int order = _currentViewHolder.ItemIndex;
                    int index = _skinQuestItem._skinQuestData.OrderToIndex(order);
                    SongGroupProgress progress = _skinQuestItem._skinQuestProgress.groups[index];
                    itemCharacterReward.UpdateData(progress, result => {
                        onComplete?.Invoke(result);
                    });
                    float targetFillAmount =_songGroupProgress.GotReward ? 1f : progress.CountUnlockSong / (float)progress.CountTotalSong;
                    UpdateUIByProgress(_songGroupProgress.CountTotalSong, targetFillAmount);
                } else {
                    Debug.LogError("Why _currentViewHolder.cellView is not type of UIItemCharacterReward!!!!");
                    onComplete?.Invoke(false);
                    return;
                }
            }
        }

        private void BtnEquipOnClick() {
            SoundManager.PlayGameButton();
            Configuration.SetOpenBall(_indexBall, -1, location: this._location, true, false); // unlock ball
            Configuration.SetSelectedBall(_indexBall, isForce: false, location: this._location); // update ball
            // txtEquip.text = LocalizationManager.instance.GetLocalizedValue("USING");
            btnEquip.gameObject.SetActive(false);
            btnEquipped.gameObject.SetActive(true);

            LiveEventTracker.Track_GroupEquipped(_skinQuestItem.eventConfig.Id, _skinQuestItem.eventConfig.Title,
                _groupData.Id + 1, _groupData.Order,
                _groupData.GiftValue, _groupData.GiftType, (_groupData.Id + 1).ToString(),
                TrackingLocation.event_screen.ToString());
        }

        private SongGroupData     _groupData;
        private SongGroupProgress _songGroupProgress;

        public IEnumerator UpdateRewardUI(int idGroupSelected) {
            while (_skinQuestItem._skinQuestProgress == null) {
                yield return null;
            }

            _songGroupProgress = _skinQuestItem._skinQuestProgress.groups[idGroupSelected];
            _groupData = _skinQuestItem._skinQuestData.Groups[idGroupSelected];

            ParseData();
            float currentFillAmount = _songGroupProgress.GotReward ? 1f :  _songGroupProgress.CountUnlockSong / (float)_songGroupProgress.CountTotalSong;
            UpdateUIByProgress(_songGroupProgress.CountTotalSong, currentFillAmount);
        }

        private int _indexBall;

        private void ParseData() {
            switch (_groupData.GiftType) {
                case "Ball":
                    if (int.TryParse(_groupData.GiftValue, out int temp)) {
                        _indexBall = temp;
                        if (Configuration.IsOpenBall(_indexBall)) {
                            _songGroupProgress.GotReward = true;
                        }
                    } else {
                        Debug.LogError("error!!!");
                    }

                    break;

                default:
                    Debug.LogError("Chưa handle");
                    break;
            }
        }

        public void UpdateUIByProgress(int totalSong, float currentFillAmount) {
            if (totalSong == 0) {
                // No song in this group
                objProgressBar.SetActive(false);
                btnEquip.gameObject.SetActive(false);
                btnEquipped.gameObject.SetActive(false);
            } else {
                if (currentFillAmount >= 1) { //done
                    objProgressBar.SetActive(false);
                    int selectedBall = Configuration.GetSelectedBall();
                    bool isShowEquip = selectedBall != _indexBall;
                    btnEquip.gameObject.SetActive(isShowEquip);
                    btnEquipped.gameObject.SetActive(!isShowEquip);
                } else {
                    btnEquip.gameObject.SetActive(false);
                    btnEquipped.gameObject.SetActive(false);
                    objProgressBar.SetActive(true);

                    txtProgress.text =
                        $"{_songGroupProgress.CountUnlockSong.ToString()}/{_songGroupProgress.CountTotalSong.ToString()}";
                    imgBar.fillAmount = currentFillAmount;
                }
            }
        }

        public void ScrollTo(int index, float duration = 0.3f, Action onDone = null) {
            if (scrollerAdapter.IsInitialized && !_isNavigating) {
                _isNavigating = true;
                scrollerAdapter.Parameters.DragEnabled = false;
                scrollerAdapter.SmoothScrollTo(index, duration, 0.5f, 0.5f, null, () => {
                    _isNavigating = false;
                    onDone?.Invoke();
                    scrollerAdapter.Parameters.DragEnabled = true;
                }, true);
            } else {
                onDone?.Invoke();
            }
        }
        private void UIItemCharacterRewardOnOnClickItem(int index) {
            ScrollTo(index);
        }

        public void Lock() {
            objProgressBar.gameObject.SetActive(false);
            btnEquip.gameObject.SetActive(false);
            btnEquipped.gameObject.SetActive(false);
        }

        public int GetSelectedBallId(int indexGroup) {
            if (_listBallRewards.Count <= indexGroup)
                return 0;
            return _listBallRewards[indexGroup];
        }
    }
}