using System;
using System.Collections;
using System.Collections.Generic;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.U2D;

namespace TilesHop.LiveEvent {
    public class UIListEvent : MonoBehaviour {
        [SerializeField] private StandardScrollerAdapter scrollListAdapter;
        [SerializeField] private OptimizedCellView   prefItemEvent;

        private List<IData> _dataList;

        public static event Action<int> OnSelectEvent;

        #region Unity Methods

        protected void Awake() {
            scrollListAdapter.Init(prefItemEvent.transform as RectTransform);
        }

        private void OnEnable() {
            UIItemEvent.OnClickBtn += UIItemEventOnOnClickBtn;
            scrollListAdapter.OnItemVisible += ScrollListAdapterOnOnItemVisible;
        }

        private void OnDisable() {
            UIItemEvent.OnClickBtn -= UIItemEventOnOnClickBtn;
            scrollListAdapter.OnItemVisible -= ScrollListAdapterOnOnItemVisible;
        }

        #endregion

        public void Show(LiveEventManager manager) {
            _dataList = new List<IData>();
            _dataList.AddRange(manager.GetAllEvent());
            this.gameObject.SetActive(true);
            StartCoroutine(IESetEventList());
        }

        #region SCroller Adapter

        private void ScrollListAdapterOnOnItemVisible(StandardItemViewsHolder item) {
            if (_dataList.Count < item.ItemIndex) return;
            UIItemEvent itemEvent = item.cellView as UIItemEvent;
            if (itemEvent != null) {
                int idEvent = ((LiveEventData)_dataList[item.ItemIndex]).Id;
                Sprite banner = Resources.Load<Sprite>($"icons/banner_event_{idEvent}");
                itemEvent.SetBanner(banner);
            }
        }

        private IEnumerator IESetEventList() {
            while (!scrollListAdapter.IsInitialized) {
                yield return YieldPool.GetWaitForEndOfFrame();
            }

            scrollListAdapter.SetItems(_dataList);
        }

        #endregion

        private void UIItemEventOnOnClickBtn(int idEvent) {
            OnSelectEvent?.Invoke(idEvent);
        }
    }
}