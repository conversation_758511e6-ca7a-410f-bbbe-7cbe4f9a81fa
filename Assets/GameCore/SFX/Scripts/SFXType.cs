// ReSharper disable InconsistentNaming
namespace TilesHop.Sound {
    public enum SFXType {
        gameButton = 0,
        gameCompleted = 1,
        star = 2,
        die = 3,
        select = 4,
        coins = 5,
        impactExtend = 6,
        buy = 7,
        checkPoint = 8,
        diamond= 9,

        // New SFX TH-1955
        start_song       = 10,
        ingame_star      = 11,
        revive_bgm       = 12,
        revive_countdown = 13,
        btnclick         = 14,
        revived          = 15,
        result_star      = 16,
        result_gem       = 17,
        result_bgm       = 18,
        popup_open       = 19,
        popup_close      = 20,
        button_tap       = 21,
        gem_count        = 22,
        open_shop        = 23,
        character_select = 24,

        //New SFX TH-3985
        result_tile_collect = 25,
        result_reward_popup = 26,
        home_tile_collect = 27,
        home_pack_unlock = 28,
        home_onboarding = 29,
        ingame_diamond_collect = 30,

		//New SFX TH-4486
		result_disk_popin = 31,
		result_small_star_popin = 32,
		home_sj_star_flying_to = 33,
		home_sj_new_level = 34,
		sj_feature_progress_line_slide = 35,
		sj_feature_unlock_reward_extra = 36,
        home_small_star = 37,
		popup_slide = 38,
        sj_gift_drop = 39,
        sj_gift_item_pop = 40,
		sj_gift_open = 41,

        tooltip_reward = 50,

		//New SFX TH-4187
		gameplay_strong_note = 100,
		gameplay_moodchange = 101,
		scroll_tick = 102,
		list_menu_open = 103,
		list_menu_close = 104,
		gem_purchased = 105,
		shop_ball_tap = 106,
		shop_ball_purchased = 107,
		free_gift_collect = 108,
		shop_select_char = 109,
		song_card_preview = 110,

		//New SFX TH-4577
		booster_turnoff = 120,

		booster_shield_layer1_turnon = 130,
        booster_shield_layer2_turnon = 131,
        booster_shield_base_active = 132,

        booster_tiletidy_active = 140,

		booster_magnet_active = 150,

		//New SFX TH-4796
        thunder_quest_tutorial_pop = 160,
        thunder_quest_counting_member = 161,
        thunder_quest_counting_finish = 162,
        thunder_quest_player_jump = 163,
        thunder_quest_bots_jump = 164,
        thunder_quest_thunder_impact = 165,
        thunder_quest_die = 166,
        thunder_quest_popup_revive = 167,
        thunder_quest_popup_step_reward = 168,
        thunder_quest_popup_final_reward = 169,
        thunder_quest_revive = 170,
        thunder_quest_revive_action = 171,

        //TH-4928
        mystery_door_drill = 180,
        mystery_door_no_drill = 181,
        mystery_door_collect_key = 182,
        mystery_door_active_key = 183,
        mystery_door_open_door = 184,
        mystery_door_reward = 185,
        mystery_door_close_door = 186,
        mystery_door_drill_ingame = 187,
        mystery_door_drill_counting = 188,

        //Endless Offer
        endless_offer_lock_click = 190,
        endless_offer_cards_move = 191,
        endless_offer_collect_milestone = 192,
        endless_offer_progress_up = 193,
        endless_offer_milestone_reward = 194,

		//LE3
		beat_sahur_popup_bg = 200,
		beat_sahur_tooltip_open = 201,
        beat_sahur_eat_item = 202,
		beat_sahur_popup_open = 203,
		beat_sahur_popup_close = 204,
		beat_sahur_popup_onboard_open = 205,
		beat_sahur_btn_home = 206,
	}
}