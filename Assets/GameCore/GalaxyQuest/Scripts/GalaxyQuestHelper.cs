using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using TilesHop.Cores.Boosters;
using TilesHop.Cores.Pooling;
using UnityEngine;

namespace TilesHop.LiveEvent.GalaxyQuest {
    public static class GalaxyQuestHelper {
        public static readonly System.Random randomGenerator = new();

        public static bool IsInternetAvailable() {
            return System.Net.NetworkInformation.NetworkInterface.GetIsNetworkAvailable() &&
                   Application.internetReachability != NetworkReachability.NotReachable;
        }
        
        
        /// <summary>
        /// Randomly split a total number into a certain number of blocks (no block is empty)
        /// </summary>
        /// <param name="total"></param>
        /// <param name="blockCount"></param>
        /// <returns></returns>
        public static int[] SplitTotalRandomly(int total, int blockCount) {
            if (total < blockCount) {
                return null;
            }

            const int minPerBlock = 1;
            int remaining = total - blockCount * minPerBlock;

            int[] result = new int[blockCount];
            List<int> cuts = new();
            System.Random rand = new();

            // Tạo 6 đường cắt ngẫu nhiên từ 0 đến remaining
            for (int i = 0; i < blockCount - 1; i++) {
                cuts.Add(rand.Next(0, remaining + 1));
            }

            cuts.Sort();

            // Tính giá trị giữa các đoạn
            result[0] = cuts[0];
            for (int i = 1; i < blockCount - 1; i++) {
                result[i] = cuts[i] - cuts[i - 1];
            }

            result[blockCount - 1] = remaining - cuts[blockCount - 2];

            // Cộng lại minPerBlock để đảm bảo mỗi phần ≥ 1
            for (int i = 0; i < blockCount; i++) {
                result[i] += minPerBlock;
            }

            return result;
        }

        #region Process Avatars

        public static List<string> ProcessAvatarsRaw(string result) {
            string[,] grid = CSVReader.SplitCsvGrid(result, true);
            int totalRows = grid.GetUpperBound(1);
            if (totalRows != 0) {
                ValidateAvatarRaw(ref grid, totalRows);
            }

            List<string> listPhotoUrl = new();
            for (int index = 1; index <= totalRows; index++) { //start from 1 to ignore first row
                string username = grid[0, index];
                if (string.IsNullOrEmpty(username) || username == CONFIG_STRING.DBKey_total_user) {
                    continue;
                }

                string avatarUrl = grid[1, index];
                listPhotoUrl.Add(avatarUrl);
            }

            return listPhotoUrl;
        }

        private static void ValidateAvatarRaw(ref string[,] grid, int totalRow) {
            byte columns = (byte) grid.GetLength(0); // retrieve the number of columns (dimension 1)
            if (columns < 4)
                return;

            for (int row = 1; row <= totalRow; row++) {
                for (byte col = (byte) (columns - 1); col >= 3; col--) {
                    if (!string.IsNullOrEmpty(grid[col, row])) { // data eror!!!!
                        string score = grid[col, row];
                        string avatar = grid[col - 1, row];
                        string name = string.Empty;

                        for (byte i = 0; i < col - 2; i++) {
                            name += grid[i, row] + FileHelper.Split;
                        }

                        name += grid[col - 2, row];

                        grid[0, row] = name;
                        grid[1, row] = avatar;
                        grid[2, row] = score;
                        break;
                    }
                }
            }
        }

        #endregion
        
        public static void Shuffle<T>(IList<T> list) {
            int n = list.Count;
            while (n > 1) {
                n--;
                int k = randomGenerator.Next(n + 1);
                T value = list[k];
                list[k] = list[n];
                list[n] = value;
            }
        }

        //TODO: customize how to instantiate popup from Resources
        public static GameObject ShowPopup(string popupName, Transform parent = null) {
            return Util.ShowPopUp(popupName, parent);
        }

        public static IEnumerator ShowPopupAsync<T>(string popupName, Transform parent = null, Action<T> callback = null) where T : Component {
            yield return Util.ShowPopupAsync(popupName, parent, callback);
        }
        
        //TODO: customeize how to get a Yield Instruction
        public static WaitForSeconds GetWaitForSeconds(float time) {
            return YieldPool.GetWaitForSeconds(time);
        }

        /// <summary>
        /// Generate an interger between min and max value that better than UnityEngine.Random
        /// + System.Random Faster and less predictable than UnityEngine.Random
        /// </summary>
        /// <param name="min"></param>
        /// <param name="max"></param>
        /// <returns></returns>
        public static int GetBetterRandomInt(int min, int max) {
            return randomGenerator.Next(min, max);
        }
        
        public static float GetRandomUnit(HashSet<int> hashSetPreventOverlap = null) {
            int rand = UnityEngine.Random.Range(1, 101);
            if (hashSetPreventOverlap != null) {
                int attempt = -1;
                do {
                    rand = UnityEngine.Random.Range(1, 101);
                    attempt++;
                } while(attempt < 10 && hashSetPreventOverlap.Contains(rand));

                hashSetPreventOverlap.Add(rand);
            }
            
            return rand * 0.01f;
        }

        public static Sprite GetIconReward(RewardInfo info) {
            Sprite icon = null;
            var types = info.type.Split(';');
            string rewardTypeRaw = null;
            string boosterTypeRaw = null;
            
            if (types.Length > 1) {
                rewardTypeRaw = types[0];
                boosterTypeRaw = types[1];
            } else {
                rewardTypeRaw = info.type;
            }

            if (!Enum.TryParse(rewardTypeRaw, out RewardType rewardType)) {
                return null;
            }
            
            string path;
			switch (rewardType) {
				case RewardType.GEM:
					path = $"Sprites/{RewardType.GEM.ToString()}";
					icon = Resources.Load<Sprite>(path);
                    break;
                
				case RewardType.BOOSTER:
                    if (Enum.TryParse(boosterTypeRaw, out BoosterType boosterType)) {
                        // path = $"Sprites/BOOSTER_{(int)boosterType}";
                        // icon = Resources.Load<Sprite>(path);
                        var boosterConfig = BoosterManager.GetBoosterConfig(boosterType);
                        if (boosterConfig != null) {
                            icon = boosterConfig.icon;
                        }
                    } else {
                        return null;
                    }
					break;
			}

            return icon;
		}
    }
}