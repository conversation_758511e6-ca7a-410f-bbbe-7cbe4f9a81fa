using System;
using System.Collections.Generic;
using UnityEngine.Serialization;

namespace TilesHop.LiveEvent.GalaxyQuest {
    [Serializable]
    public class GalaxyQuestStateData {
        /// <summary>
        /// The timestamp when the event is first offered to the user
        /// </summary>
        public int startTime;

        /// <summary>
        /// The timestamp when the user accepts the challenge by clicking Start button
        /// </summary>
        public int triggerTime = -1;

        /// <summary>
        /// The time at which the event will end
        /// </summary>
        public int endTime;

        /// <summary>
        /// is the user playing in the event (clicked Start button)
        /// </summary>
        public bool isJoining;
        
        /// <summary>
        /// This flag indicates whether the main player might fail the quest,
        /// but the result hasn't been processed yet
        /// </summary>
        public bool isLikelyToEnd;

        /// <summary>
        /// this flag indicates whether the main player has started the quest or not
        /// </summary>
        public bool isFirstActive = true;
        
        /// <summary>
        /// Indicates whether the user was requested to join the quest or not
        /// </summary>
        public bool isRemindedJoinQuest;

        /// <summary>
        /// the main user is standing at this 0-based step
        /// </summary>
        public int currentStep = 0;

        /// <summary>
        /// flag to determine which kind of event, 5-step or 7-step
        /// </summary>
        public bool isLongQuest = false;

        /// <summary>
        /// The number of times the main player is allowed to revive after failing our quest
        /// </summary>
        public int remainReviveCount = 3;
        
        /// <summary>
        /// The number of revives (aka recoveries) that the main user has purchased
        /// </summary>
        public int paidReviveNumber;

        /// <summary>
        /// the number of remaining bot players
        /// </summary>
        public int remainBotsCount = 99;

        /// <summary>
        /// Bot avatar URL pool
        /// </summary>
        public List<string> remainBotsPhotoUrl = new();

        /// <summary>
        /// pre-calculate drop count each step
        /// </summary>
        public int[] dropPerStep;
    }
}