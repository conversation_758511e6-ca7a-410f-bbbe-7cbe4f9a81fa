using System;

namespace TilesHop.LiveEvent.GalaxyQuest {
	[Serializable]
	public class GalaxyQuestEventData {

		/// <summary>
		/// Tracks how many times the [event name] has occurred
		/// </summary>
		public int eventOrderNumber;
		
		public bool isWinQuest = false;

		/// <summary>
		/// flag to determine whether the event is active or counting down.
		/// </summary>
		public bool isOpening;

		/// <summary>
		/// Time to open event after cooldown
		/// </summary>
		public int reopenTime;
		
		public bool isFirstShowRevive = true;
		
		/// <summary>
		/// The total number of revives the user used in the quest
		/// </summary>
		public int reviveNumber;

		/// <summary>
		/// flag to determine whether the player is participating in the event.
		/// </summary>
		public bool isJoinedEvent;
		
		public bool isFreeReviveAvailable = true;
	}
}
