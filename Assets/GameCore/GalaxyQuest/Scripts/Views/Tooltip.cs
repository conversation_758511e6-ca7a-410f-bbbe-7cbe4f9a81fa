using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.LiveEvent.GalaxyQuest {
	public class Tooltip : MonoBehaviour {
		private const string Tooltip_NoReward = "GQ_TOOLTIP_2";
		private const string Tooltip_HasReward = "GQ_TOOLTIP_3";
		private const string Tooltip_CurrentStep = "GQ_TOOLTIP_5";

		[SerializeField] Text text;
		float _timeActive;

		private void OnDestroy() {
			JumpBlock.OnShowTooltip -= Show;
		}

		private void Update() {
			if (gameObject.activeInHierarchy) {
				_timeActive -= Time.deltaTime;
				if (Input.GetMouseButtonUp(0) || _timeActive <= 0) {
					Hide();
				}
			}
		}

		public void Show(Tooltip tooltip, bool isCurrentStep, bool hasReward) {
			if (tooltip != this) {
				Hide();
				return;
			}

			if (text) {
				string content;

				if (isCurrentStep) {
					content = string.Format(LocalizationManager.instance.GetLocalizedValue(Tooltip_CurrentStep), GalaxyQuest.GetStateData().remainBotsCount + 1);
				} else {

					content = hasReward ? LocalizationManager.instance.GetLocalizedValue(Tooltip_HasReward) : LocalizationManager.instance.GetLocalizedValue(Tooltip_NoReward);
				}
				text.text = content;
			}

			SoundManager.PlayOpenTooltipReward();
			gameObject.SetActive(true);
			_timeActive = 3;
			transform.DOScale(1, 0.5f).SetEase(Ease.OutBack).From(0.3f);
		}

		private void Hide() {
			DOTween.Kill(gameObject);
			gameObject.SetActive(false);
		}
	}
}

