using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.LiveEvent.GalaxyQuest {
	public class GalaxyQuestRewardElement : MonoBehaviour {
		[SerializeField] private Image icon;
		[SerializeField] Text textAmount;

		public void Config(RewardInfo reward) {
			icon.sprite = GalaxyQuestHelper.GetIconReward(reward);
			textAmount.text = reward.value > 1? reward.value.ToString() : string.Empty;
		}
	}
}
