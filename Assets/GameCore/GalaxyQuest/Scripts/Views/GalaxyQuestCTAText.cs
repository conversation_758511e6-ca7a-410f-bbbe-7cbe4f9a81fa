using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.LiveEvent.GalaxyQuest {
	public class GalaxyQuestCTAText : MonoBehaviour {
		private const string key = "BACK_TO_HOME_AND_COMPLETE_SONGS";
		[SerializeField] private Text txt;

		private void OnEnable() {
			GalaxyQuestStateData stateData = GalaxyQuest.GetStateData();
			int songCount = stateData.isLongQuest ? 7 : 5 - stateData.currentStep;
			txt.text = string.Format(LocalizationManager.instance.GetLocalizedValue(key), songCount);
			LocalizationManager.instance.UpdateFont(txt);
		}
	}
}
