using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using TilesHop.Cores.Pooling;
using TilesHop.GameCore;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;
using Random = UnityEngine.Random;

namespace TilesHop.LiveEvent.GalaxyQuest {
    public class GalaxyQuestMainPopup : PopupUI {
        #region Fields
        private const float JUMP_BIAS_Z = -200f;
        
        [Header("Sound")]
        [SerializeField] private AudioSource bgm;
        
        [SerializeField] private AudioSource bgSFX;
        [Header("UI")]
        [SerializeField] private Transform canvasTransform;
        [SerializeField] private Text       txtLevel;
        [SerializeField] private Text       txtPlayer;
        [SerializeField] private Text       txtTime;
        [SerializeField] private Text       txtDescription;
        [SerializeField] private Text       txtDescriptionBig;
        [SerializeField] private Text       txtGemReward;
        [SerializeField] private GameObject questInfoContainer;
        [SerializeField] private Button     btnInfo;

        [Space]
        [Header("Jump Specifications")]
        public float spaceRowZ = 30f;

        public float spaceRowY    = 10f;
        public float spaceColumn  = 25;
        public float noise        = 4f;
        public float jumpPower    = 1.5f;
        public float jumpDuration = 0.5f;

        [Space]
        [SerializeField] private JumpBlock   startPoint;
        [SerializeField] private Transform   winPoint;
        [SerializeField] private JumpBlock[] pointsShortPath;
        [SerializeField] private JumpBlock[] pointsLongPath;
        [SerializeField] private GameObject  shortPathContainer;
        [SerializeField] private GameObject  longPathContainer;
        
        [SerializeField] private Transform playersContainer;
        [SerializeField] private Button    btnClose;
        [SerializeField] private Button    btnTapToContinue;
        [SerializeField] private Transform mainQuestRoot;

        [Space]
        [SerializeField] private Ease easeNext = Ease.InOutSine;
        [SerializeField] private Ease easeOut = Ease.InSine;

        private Coroutine _countdownCoroutine;
        
        private PlayerAvatarItem     _mainPlayer;
        private PlayerAvatarItem     _prefabPlayerItem;
        private List<UserAvatarItem> _playerAvatarItems;
        private int                  _avatarsDropCurrentStep;
        private bool                 _isLongPath;
        private int                  _lastPlayersCount;
        private Transform            _mainPlayerItem;
        private ParticleSystem       _thunderVfx;
        private ParticleSystem       _thunderVfxPrefab;
        private GameObject           _instancedStepRewardPopup;
        private GameObject           _instancedFinalRewardPopup;

        private int _previousStep;
        public int step;
        
        private AudioSource _bgm2;

        private UIOnBoardingUnlockElement _onboardingPopup;
        private GameObject                _ctaDialog;
        
        #endregion

        #region Properties

        public bool isShowing { get; private set; }
        
        // Chọn giữa short path (5 steps) và long path (7 steps)
        public JumpBlock[] path => _isLongPath ? pointsLongPath : pointsShortPath;

        #endregion
        
        #region Unity Events

        private void Awake() {
            btnClose.onClick.AddListener(Close);
            btnTapToContinue.onClick.AddListener(Close);
            btnClose.gameObject.SetActive(false);
            btnTapToContinue.gameObject.SetActive(false);
            btnInfo.onClick.AddListener(OnClickButtonInfo);
            
            UpdateText();
            GeneratePlayerItems();
            SetPlayersPositions();
            SetupPathBlocks();
        }
        
        protected override void OnEnable() {
            base.OnEnable();
            
            if (!_hasSetAvatars) {
                // set bots' avatars
                if (GalaxyQuest.GetStateData().remainBotsPhotoUrl.IsNullOrEmpty()) {
                    GalaxyQuest.GetBotAvatars(SetBotsAvatars);
                } else {
                    SetBotsAvatars();
                }
            }
            
            if (bgm&& bgm.clip) {
                if (Configuration.instance.MusicIsOn()) {
                    GroundMusic.instance.StopMusic();
                    PreviewSongController.instanceSafe.PausePreviewMusic();
                    StartCoroutine(IEPlayBGM());
                }
            }
			if (bgSFX && bgSFX.clip) {
				if (Configuration.instance.SoundIsOn()) {
					bgSFX.Play();
				}
			}
			UpdateText();
            
            if (_countdownCoroutine != null) {
                StopCoroutine(_countdownCoroutine);
            }
            _countdownCoroutine = StartCoroutine(IECountdown());
            
            _mainPlayer.SetAvatar(GalaxyQuest.userPhotoUrl);
            _mainPlayer.Appear(1f);
            foreach (UserAvatarItem item in _playerAvatarItems) {
                item.playerItem.Appear(1f);
            }

            questInfoContainer.SetActive(true);
            
            // at result screen
            if (GalaxyQuest.canJumpInQuest) {
                _previousStep = step;
                UpdateCurrentStep(false);
                step++;
                var stateData = GalaxyQuest.GetStateData();
                stateData.currentStep = step;
                
                int dropInPlayers = stateData.dropPerStep[_previousStep];
                stateData.remainBotsCount -= dropInPlayers;
                
                GalaxyQuest.CheckFinish();
                StartCoroutine(IEStartJump());
                UpdateInfos();
            } else { // at home screen
                btnClose.gameObject.SetActive(true);
                ToggleDescriptionTextes(true);
                txtDescription.text = GalaxyQuest.GetSongsMoreText();
                
                _mainPlayer.PlayIdle();
            }
            isShowing = true;

            txtGemReward.text = GalaxyQuest.GetFinalRewardGemAmount().ToString();
        }

        private IEnumerator IEPlayBGM() {
            if (!_bgm2) {
                _bgm2 = gameObject.AddComponent<AudioSource>();
                _bgm2.loop = false;
                _bgm2.playOnAwake = false;
            }
            _bgm2.clip = bgm.clip;
            bgm.loop = false;
            int temp = 0;
            AudioSource current;
            do {
                current = temp % 2 == 0 ? bgm : _bgm2;
                current.Play();
                yield return YieldPool.GetWaitForSeconds(64f);
                temp++;
            }while(true);
        }

        protected override void OnDisable() {
            base.OnDisable();
            if (bgm&& bgm.clip) {
                if (Configuration.instance && Configuration.instance.MusicIsOn()) {
                    bgm.Stop();
                    PreviewSongController.instanceSafe.ResumePreviewMusic();
                }
            }
			if (bgSFX && bgSFX.clip) {
				if (Configuration.instance && Configuration.instance.SoundIsOn()) {
					bgSFX.Stop();
				}
			}
		}

        private IEnumerator Start() {
            if (!GalaxyQuest.canJumpInQuest) {
                if (GalaxyQuest.GetStateData().isFirstActive) {
                    var jumpTime = _mainPlayer.AppearJumpDown(_mainPlayerItem.position);
                    for (int index = 0; index < _playerAvatarItems.Count; index++) {
                        UserAvatarItem item = _playerAvatarItems[index];
                        item.itemTransform.localScale = Vector3.zero;
                        item.playerItem.AppearJumpDown(item.itemTransform.position, Random.Range(0.05f, 0.2f));
                    }

                    GalaxyQuest.GetStateData().isFirstActive = false;
                    GalaxyQuest.SaveStateData();

                    yield return GalaxyQuestHelper.GetWaitForSeconds(jumpTime);   
                }
                
                yield return GalaxyQuestHelper.GetWaitForSeconds(4f);
                _ctaDialog = GalaxyQuest.ShowDialog(mainQuestRoot.parent);
                if (_ctaDialog != null) {
                    GalaxyQuestAudioHelper.PlayPopSfx();
                    var dialogBtn = _ctaDialog.GetComponentInChildren<Button>();
                    if(dialogBtn) {
                        dialogBtn.onClick.AddListener(Close);
                    }
                }
            }
        }

        private void UpdateInfos() {
            if (!GalaxyQuest.isMainUserInWinStreak) {
                ToggleDescriptionTextes(false);
                txtDescriptionBig.text = GalaxyQuest.GetFailedQuestText();
            } else {
                ToggleDescriptionTextes(true);
                if (step >= GalaxyQuest.maxStep) {
                    txtDescription.text = GalaxyQuest.GetCongratulationCompleteQuestText();
                } else {
                    int random = Random.Range(0, 2);
                    if (random == 0) { 
                        txtDescription.text =  GalaxyQuest.GetCongratulationStepText();
                    } else {
                        txtDescription.text = GalaxyQuest.GetSongsMoreText();
                    }   
                }
            }
        }

        private void ToggleDescriptionTextes(bool isShowDescript) {
            if (isShowDescript) {
                txtDescriptionBig.gameObject.SetActive(false);
                questInfoContainer.SetActive(true);
                txtDescription.gameObject.SetActive(true);   
            } else {
                txtDescriptionBig.gameObject.SetActive(true);
                questInfoContainer.SetActive(false);
                txtDescription.gameObject.SetActive(false);
            }
        }

        #endregion

        #region UI/UX
        
        public override void Close() {
			GalaxyQuestAudioHelper.PlayGameButtonSFX();
			var stateData = GalaxyQuest.GetStateData();
            if (stateData.isLikelyToEnd) {
                //event tracking
                if (!GalaxyQuest.GetEventData().isWinQuest) {
                    GalaxyQuestAnalysis.EmitQuestEnd(GalaxyQuest.timeElapsedSinceOfferEvent, QuestEndReason.FAIL);
                }
                GalaxyQuest.EndEvent();
            }
            
            isShowing = false;
            
            if (GalaxyQuest.canJumpInQuest || !Util.IsHomeScene()) {
                Destroy(gameObject);
            } else {
                gameObject.SetActive(false);
            }
            
            GalaxyQuest.canJumpInQuest = false;

            if (_ctaDialog != null) {
                _ctaDialog.SetActive(false);
            }
        }

        private IEnumerator IECountdown() {
            var delay = GalaxyQuestHelper.GetWaitForSeconds(1f);
            int timeLeft = GalaxyQuest.GetEndTime() - GalaxyQuestTimeHelper.instanceSafe.RealTime;
            while (timeLeft > 0) {
                {
                    timeLeft = GalaxyQuest.GetEndTime() - GalaxyQuestTimeHelper.instanceSafe.RealTime;
                    GalaxyQuestTimeHelper.FormatTimeToDisplay(timeLeft, txtTime, timeLeft, "000000");
                    yield return delay;
                }
            }

            Close();
        }
        
        private void UpdateText() {
            GalaxyQuestStateData stateData = GalaxyQuest.GetStateData();
            _lastPlayersCount = stateData.remainBotsCount + 1;

            txtLevel.text = $"{stateData.currentStep}/{(stateData.isLongQuest ? "7" : "5")}";
            txtPlayer.text = $"{_lastPlayersCount}/100";
        }
        
        private void UpdateTextLevel() {
            GalaxyQuestStateData stateData = GalaxyQuest.GetStateData();
            txtLevel.text = $"{stateData.currentStep}/{(stateData.isLongQuest ? "7" : "5")}";
        }
        
        private void UpdateTextPlayersCount() {
            GalaxyQuestStateData stateData = GalaxyQuest.GetStateData();
            
            DOTween.To(() => _lastPlayersCount, value => {
                txtPlayer.text = $"{value}/100";
            }, stateData.remainBotsCount + 1, 1f);
        }
        
        public void ShowRevivePopup() {
            var asset = Resources.Load<GalaxyQuestRevivePopup>("Popups/GalaxyQuestRevive");
            if (asset != null) {
                var questRevive = Instantiate(asset, mainQuestRoot);
                var rectTransform = questRevive.transform as RectTransform;
                if (rectTransform != null) {
                    rectTransform.offsetMax = Vector2.zero;
                    rectTransform.offsetMin = Vector2.zero;
                }

                questRevive.onRevive = Revive;
                questRevive.onCloseWithoutRevive = SkipRevive;
            }
            
            // event tracking
            var eventData = GalaxyQuest.GetEventData();
            var stateData = GalaxyQuest.GetStateData();
            GalaxyQuestAnalysis.EmitQuestReviveOffered(eventData.eventOrderNumber, GalaxyQuest.currentSongId,
                stateData.remainReviveCount);
        }
        
        /// <summary>
        /// <para>Case 1: main player completed current song</para>
        /// <para>1.1: Get reward on the quest's platform</para>
        /// <para>1.2: Get last reward</para>
        /// <para>Case 2: main player failed the song and:</para>
        /// <para>2.1: Remain revives</para>
        /// <para>2.1: Cannot revive</para>
        /// </summary>
        private void ShowCloseButtons() {
            btnClose.gameObject.SetActive(true);
            btnTapToContinue.gameObject.SetActive(true);
        }
        
        /// <summary>
        /// prevent users from closing this popup using their device's back (Escapse) button
        /// </summary>
        /// <returns></returns>
        public override bool HandleEventBack() {
            return false;
        }

        private void OnClickButtonInfo() {
            GalaxyQuestAudioHelper.PlayGameButtonSFX();
            if (!_onboardingPopup) {
                StartCoroutine(
                    GalaxyQuestHelper.ShowPopupAsync<UIOnBoardingUnlockElement>(
                        "GalaxyQuestOnboarding",
                        canvasTransform, 
                        HandleOnDoneShowOnboardingPopup));
            }
        }

        private void HandleOnDoneShowOnboardingPopup(UIOnBoardingUnlockElement onboarding) {
            _onboardingPopup = onboarding;
            if (_onboardingPopup != null) {
                _onboardingPopup.Show(null);
            }
        }

        #endregion

        #region Players

        private void CheckLongPath() {
            _isLongPath = false;
                
            var stateData = GalaxyQuest.GetStateData();
            if (stateData.isLongQuest) {
                _isLongPath = true;
            }
                
            shortPathContainer.SetActive(!_isLongPath);
            longPathContainer.SetActive(_isLongPath);
        }
        
        private void SetPlayersPositions() {
            CheckLongPath();
            
            _mainPlayerItem.position = step == 0
                ? startPoint.transform.position
                : path[Mathf.Min(step, path.Length) - 1].transform.position;
            
            var sum = _playerAvatarItems.Count + 1;
            GalaxyQuestHelper.Shuffle(_playerAvatarItems);
            
            var sqrt = Mathf.Sqrt(sum);
            var maxInRowRaw = Mathf.Ceil(sqrt);
            var maxInRow = (int) maxInRowRaw;
            int rowId = 1;
            int numInRow = 2;
            int remain = sum - 1;
            Vector3 startPos = _mainPlayerItem.localPosition;
            HashSet<int> preventOverlapHashSet = HashSetPool<int>.Get();
            
            while (remain > 0) {
                preventOverlapHashSet.Clear();
                Vector3 pos = Vector3.zero;
                pos.z = startPos.z + rowId * spaceRowZ;
                pos.y = startPos.y + rowId * spaceRowY;

                float offset = (int) (numInRow / 2);
                for (int i = 0; i < numInRow; i++) {
                    pos.x = startPos.x + (i - offset) * spaceColumn + (numInRow % 2 == 0 ? spaceColumn * 0.5f : 0f);
                    if (remain == 0) {
                        break;
                    }

                    var randX = GalaxyQuestHelper.GetRandomUnit();
                    var randY = GalaxyQuestHelper.GetRandomUnit();
                    var randZ = GalaxyQuestHelper.GetRandomUnit(preventOverlapHashSet);
                    var noiseOffsetNormalized = new Vector3(randX, randY, randZ);
                    _playerAvatarItems[remain - 1].itemTransform.localPosition = pos + noiseOffsetNormalized * noise;
                    remain--;
                }

                if (rowId + 1 >= maxInRow) {
                    numInRow--;
                } else {
                    numInRow++;
                }

                rowId++;
            }
            HashSetPool<int>.Release(preventOverlapHashSet);
        }
        
        private void GeneratePlayerItems() {
            if (_prefabPlayerItem == null) {
                _prefabPlayerItem = Resources.Load<PlayerAvatarItem>("user_item");
                if (_prefabPlayerItem == null) {
                    return;
                }
            }

            _mainPlayer = Instantiate(_prefabPlayerItem, playersContainer);
            _mainPlayerItem = _mainPlayer.transform;
            _mainPlayer.SetFrame(true);
            //_mainPlayer.SetAvatar(GalaxyQuest.userPhotoUrl);
            
            var stateData = GalaxyQuest.GetStateData();
            _previousStep = step = stateData.currentStep;
            UpdateText();
            _playerAvatarItems = new List<UserAvatarItem>();
            
            // tính truớc số lượng avatar fall
            int remainBotsCount = stateData.remainBotsCount;
            int botCountAfter = Mathf.Max(remainBotsCount - stateData.dropPerStep[step], 0);
            int dropInPlayers = stateData.dropPerStep[step];
            _avatarsDropCurrentStep = dropInPlayers > 3? 3 : Random.Range(dropInPlayers, 0);
            
            // dựa vào số bot còn lại để set số avatar
            int numberOfAvatars = botCountAfter switch {
                <= 5 => botCountAfter,
                <= 10 => 5,
                <= 20 => 7,
                <= 50 => 9,
                <= 80 => 11,
                _ => 12
            };
            numberOfAvatars += _avatarsDropCurrentStep;

            // init list bots
            for (int i = 0; i < numberOfAvatars; i++) {
                var player = Instantiate(_prefabPlayerItem, playersContainer);
                player.SetFrame(false);
                _playerAvatarItems.Add(new UserAvatarItem(player));
#if UNITY_EDITOR
                player.gameObject.name = $"Player_{i}";
#endif
            }
        }

        private bool _hasSetAvatars;
        private void SetBotsAvatars() {
            if (_playerAvatarItems == null) {
                _hasSetAvatars = false;
                return;
            }

            var stateDat = GalaxyQuest.GetStateData();
            int photoCount = stateDat.remainBotsPhotoUrl.Count;
            int userCount = _playerAvatarItems.Count;
            for (int i = 0; i < userCount; i++) {
                if (i >= photoCount) {
                    break;
                }
                var player = _playerAvatarItems[i];
                player.playerItem.SetAvatar(stateDat.remainBotsPhotoUrl[i]);
            }
            _hasSetAvatars = true;
        }
        
        private void CheckStepReward() {
            // If the main player reaches the final step
            if (step - 1 >= path.Length) {
                ShowFinalRewardPopup(GetFinalRewards());
                return;
            }
            
            RewardInfo reward = path[step - 1].GetReward();

            if (reward != null) {
                path[step - 1].HideReward();
                ShowStepRewardPopup(reward);
            } else {
                ShowCloseButtons();
            }
        }

        private void ShowStepRewardPopup(RewardInfo reward) {
            if (!_instancedStepRewardPopup) {
                _instancedStepRewardPopup = GalaxyQuestHelper.ShowPopup("GalaxyQuestStepReward", mainQuestRoot);
            }

            if (_instancedStepRewardPopup &&
                _instancedStepRewardPopup.TryGetComponent(out GalaxyQuestStepRewardPopup stepRewardPopup)) {
                stepRewardPopup.Show(reward, ShowCloseButtons);
            }

            GalaxyQuestAnalysis.EmitQuestRewarded($"milestone_{reward.id}", reward.type, reward.value);
        }

		private void ShowFinalRewardPopup(RewardInfo[] rewards) {
			if (!_instancedFinalRewardPopup) {
				_instancedFinalRewardPopup = GalaxyQuestHelper.ShowPopup("GalaxyQuestFinalReward", mainQuestRoot);
			}

            if (_instancedFinalRewardPopup &&
                _instancedFinalRewardPopup.TryGetComponent(out GalaxyQuestFinalRewardPopup finalRewardPopup)) {
                finalRewardPopup.Show(rewards, _playerAvatarItems, ShowCloseButtons);
            }
        }

        private RewardInfo[] GetFinalRewards() {
            RewardInfo[] rewards = GalaxyQuest.config.finalRewards.ToArray();
            for (int i = 0; i < rewards.Length; i++) {
                if (rewards[i].type == RewardType.GEM.ToString()) {
                    var finalReward = new RewardInfo(rewards[i]);
                    finalReward.value /= GalaxyQuest.GetStateData().remainBotsCount + 1;
                    rewards[i] = finalReward;
                }
                
                //tracking
                RewardInfo reward = rewards[i];
                GalaxyQuestAnalysis.EmitQuestRewarded("final_reward", reward.type, reward.value);
            }

            return rewards;
        }

		private void SetupPathBlocks() {
            var stateData = GalaxyQuest.GetStateData();
            RewardInfo[] rewards = stateData.isLongQuest
                ? GalaxyQuest.config.longQuestRewards
                : GalaxyQuest.config.shortQuestRewards;

            int milestoneId = 1;
            for (int i = 0; i < path.Length; i++) {
				path[i].ConfigReward(rewards[i], ref milestoneId);
                if(i < step - 1) {
                    path[i].Hide();
                }
                if(i == step - 1) {
                    path[i].IsCurrentStep = true;
                    path[i].HideReward();
                }
            }

            if (step > 0) {
                startPoint.Hide();
                if (step - 1 < path.Length) {
                    path[step - 1].HideReward();
                }   
            }
        }

        #endregion

        #region Jump

#if UNITY_EDITOR
        private void Update() {
            if (Input.GetKeyDown(KeyCode.A)) {
                Jump();
                _previousStep++;
                step++;
            }
        }
#endif

        private IEnumerator IEStartJump() {
            yield return YieldPool.GetWaitForSeconds(1.5f);

            while (UIOverlay.instance.IsShowingAdFsReward()) {
                yield return null;
            }
            
            Jump();
        }

        private void Jump() {
            if (_previousStep >= path.Length + 1) {
                return;
            }
            
            var nextPoint = _previousStep >= path.Length ? winPoint : path[_previousStep].transform;
            var currentBlock = _previousStep == 0? startPoint : path[_previousStep - 1];

            var distance = nextPoint.position - _mainPlayerItem.position;
            _mainPlayerItem.SetLocalZ(JUMP_BIAS_Z);
            
            // cho main user nhảy tiếp nếu đang trong win streak
            if (GalaxyQuest.isMainUserInWinStreak) {
                StartCoroutine(_mainPlayer.JumpNextCoroutine(nextPoint.position, jumpPower, jumpDuration, 0));
				DOVirtual.DelayedCall(0.5f, () => SoundManager.PlayThunderQuestPlayJump());
			}

            int last = _playerAvatarItems.Count - 1;
            var stateData = GalaxyQuest.GetStateData();
            
            float maxDelay = 0;
            // jump to next step
            for (int i = last; i >= _avatarsDropCurrentStep; i--) {
                var player = _playerAvatarItems[i].playerItem;
                var item = _playerAvatarItems[i].itemTransform;
                var start = item.localPosition;
                var end = item.position + distance;
                start.z += JUMP_BIAS_Z;
                item.localPosition = start;
                float delay = jumpDuration + (last - i) * 0.05f + Random.Range(-0.05f, 0.05f);
                maxDelay = Mathf.Max(maxDelay, delay);
                if (step == GalaxyQuest.maxStep) {
                    end.x *= 3;
                }
                
                StartCoroutine(player.JumpNextCoroutine(end, jumpPower, jumpDuration, easeNext, delay));
            }
            DOVirtual.DelayedCall(1f, () => SoundManager.PlayThunderQuestBotsJump());
            DOVirtual.DelayedCall(jumpDuration, UpdateTextLevel);
            
            maxDelay += jumpDuration + 0.5f;
            GalaxyQuestHelper.Shuffle(currentBlock.jumpOutPoints);
            
            // play thunder vfx
            LocateThunderVFX(currentBlock.transform, maxDelay);
            
            // avatars fall out
            for (int i = 0; i < _avatarsDropCurrentStep; i++) {
                var item = _playerAvatarItems[i].itemTransform;
                var player = _playerAvatarItems[i].playerItem;
                var start = item.localPosition;
                
                // chọn điểm rơi
                Vector3 end;
                if (i < currentBlock.jumpOutPoints.Length) {
                    end = currentBlock.jumpOutPoints[i].position;
                } else {
                    end = item.position + (Vector3)Random.insideUnitCircle * 1.5f;
                }
                
                start.z += JUMP_BIAS_Z;
                item.localPosition = start;
                
                player.JumpOut(end, jumpPower, jumpDuration, isDisappear: true, easeOut, maxDelay);
            }
            
            currentBlock.Disappear(maxDelay);

            DOVirtual.DelayedCall(maxDelay, UpdateTextPlayersCount);

            // nếu user break win streak thì xử thua
            if (!GalaxyQuest.isMainUserInWinStreak) {
                Vector3 end;
                if (_avatarsDropCurrentStep < currentBlock.jumpOutPoints.Length) {
                    end = currentBlock.jumpOutPoints[_avatarsDropCurrentStep].position;
                } else {
                    end = _mainPlayerItem.position + (Vector3)Random.insideUnitCircle * 1.5f;
                }

                bool isReviveAvailable = stateData.remainReviveCount == 0;
                _mainPlayer.JumpOut(end, jumpPower, jumpDuration, isDisappear: isReviveAvailable, easeOut, maxDelay);
                DOVirtual.DelayedCall(maxDelay + jumpDuration, () => SoundManager.PlayThunderQuestPlayerDie());
            }
            
            // handle on complete
            DOVirtual.DelayedCall(maxDelay + jumpDuration + 2.5f, HandleCompleteJump);

            List<string> removedPhotos = new();
            for (int i = 0; i < _avatarsDropCurrentStep; i++) {
                var url = _playerAvatarItems[0].playerItem.avatarPhotoUrl;
                if (!string.IsNullOrEmpty(url)) {
                    removedPhotos.Add(url);
                }
                _playerAvatarItems.RemoveAt(0);
            }
            
			UpdateCurrentStep(true);
			stateData.currentStep = step;
            GalaxyQuest.RemovePhotos(removedPhotos);
        }
        
        private void HandleCompleteJump() {
			if (!GalaxyQuest.isMainUserInWinStreak) {
                // When the user is likely to fail the quest, offer them a revive if it's still available
                var stateData = GalaxyQuest.GetStateData();
                if (stateData.remainReviveCount > 0) {
                    ShowRevivePopup();
                } else {
                    ShowCloseButtons();
                }
            } else {
                // Otherwise, check if user can get a reward on the platform
                CheckStepReward();
            }

			GalaxyQuest.SaveAllData();
		}

        private void UpdateCurrentStep(bool isCurrentStep) {
            if(step < 2 || step > path.Length) {
                return;
            }

			path[step - 2].IsCurrentStep = isCurrentStep;
		}

        #endregion

        #region Revive Quest
        
        private void Revive() { 
            GalaxyQuest.ReviveEvent();
            ToggleDescriptionTextes(true);
            UpdateInfos();
            
            if (_mainPlayer == null) {
                return;
            }
            
            var prevStep = step - 1;
            var nextPoint = prevStep >= path.Length ? winPoint : path[prevStep].transform;
            var fallTime = _mainPlayer.AppearJumpDown(nextPoint.position);
            mainQuestRoot?.DOShakePosition(0.5f, Vector3.one * 7f, 25).SetDelay(fallTime);
            DOVirtual.DelayedCall(0.75f, () => SoundManager.PlayThunderQuestReviveAction());
            
            DOVirtual.DelayedCall(fallTime + 0.5f, CheckStepReward);
        }

        private void SkipRevive() {
            _mainPlayer.Shock(0.5f, 1f);
            _mainPlayer.Disappear(1.25f);
            
            ShowCloseButtons();
        }

        #endregion
        
        #region VFX

        private float LocateThunderVFX(Transform block, float delay) {
            if (_previousStep == 0) {
                _thunderVfxPrefab = Resources.Load<ParticleSystem>("VFX/thunder_vfx_yellow");
            } else {
                var currentBlock = path[_previousStep - 1];
                if (currentBlock.GetReward() != null) {
                    _thunderVfxPrefab = Resources.Load<ParticleSystem>("VFX/thunder_vfx_yellow");
                } else {
                    _thunderVfxPrefab = Resources.Load<ParticleSystem>("VFX/thunder_vfx_blue");
                }   
            }
            
            if (_thunderVfxPrefab != null) {
                StartCoroutine(IEPlayThunderVFX(block, delay));
                //return 0.1f;
            }
            
            return 0;
        }
        
        
        private IEnumerator IEPlayThunderVFX(Transform target, float delay) {
            if (_thunderVfxPrefab == null) {
                yield break;
            }
            
            if (delay > 0) {
                yield return GalaxyQuestHelper.GetWaitForSeconds(delay);
            }

            if (_thunderVfx == null) {
                _thunderVfx = Instantiate(_thunderVfxPrefab, target.position, Quaternion.identity, mainQuestRoot);
            } else {
                _thunderVfx.transform.position = target.position;
            }
            _thunderVfx.Play();
            SoundManager.PlayThunderQuestThunderImpact();
        }

        #endregion
        
    }

    [Serializable]
    public struct UserAvatarItem {
        public Transform        itemTransform;
        public PlayerAvatarItem playerItem;

        public UserAvatarItem(PlayerAvatarItem playerItem) {
            this.playerItem = playerItem;
            itemTransform = playerItem.transform;
        }
    }
}
