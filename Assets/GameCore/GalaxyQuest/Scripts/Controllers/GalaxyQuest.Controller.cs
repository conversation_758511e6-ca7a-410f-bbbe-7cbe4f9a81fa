using System;
using System.Collections.Generic;
using TilesHop.Cores.Boosters;
using UnityEngine;

namespace TilesHop.LiveEvent.GalaxyQuest {
    public partial class GalaxyQuest : FastSingleton<GalaxyQuest> {

        #region Fields

        public static GalaxyQuestConfig config;
        private GalaxyQuestMainPopup _instancedMainPopup;
        private GalaxyQuestStartPopup _instancedStartPopup;
        private GalaxyQuestCountdownPopup _instancedCountDownPopup;
        
        // for event tracking
        private static HashSet<SONG_STATUS> _listSongStatusToOverridePlayType;

        #endregion
        
        #region Properties

        public static bool isActive => eventOrderNumber > 0;

        public static int eventOrderNumber {
            get {
                var eventData = GetEventData();
                return eventData.eventOrderNumber;
            }
        }

        public static bool isJoiningEvent => _stateData != null && _stateData.isJoining;
        

        /// <summary>
        /// Decide whether to play the jumping animation for players' avatars in Quest popup or not
        /// </summary>
        public static bool canJumpInQuest { get; set; } = false;

        public bool isShowingMainQuest => _instancedMainPopup?.isShowing ?? isLoadingPopup;
        public bool isShowingStartupPopup => _instancedStartPopup != null && _instancedStartPopup.gameObject.activeSelf;
        public bool isShowingQuestPopups => isShowingMainQuest || isShowingStartupPopup || GalaxyQuestMatching.isShowing;
        
        #endregion
        
        #region Init Singleton and DontDestroyOnLoad
        
        private void InitManager() {
            GetStateData();
            GetEventData();
            DontDestroyOnLoad(gameObject);
        }
        
        private static void LoadConfig() {
            string rawData = GetRemoteGalaxyQuestConfig();

            if (string.IsNullOrEmpty(rawData)) {
                rawData = Resources.Load<TextAsset>("Configs/GalaxyQuestConfig").text;
            }

            try {
                config = JsonUtility.FromJson<GalaxyQuestConfig>(rawData);

                var unlockConfig = config.unlock_conditions;
                var standardCondition = new StandardUnlockConditionConfig(
                    unlockConfig.stars_earn, 
                    unlockConfig.song_start,
                    unlockConfig.further_star, 
                    unlockConfig.further_song_start, 
                    unlockConfig.version);

                FeatureUnlockManager.instanceSafe.LoadConditions(FeatureKey.GALAXY_QUEST, standardCondition);
            } catch (Exception e) {
                Debug.LogError(e);
            }
        }
        
        #endregion

        public static bool CheckCanOverrideSongPlayType(SONG_STATUS songStatus) {
            if (_stateData == null || !_stateData.isJoining) {
                return false;
            }
            
            _listSongStatusToOverridePlayType ??= new HashSet<SONG_STATUS>() {
                SONG_STATUS.song_start,
                SONG_STATUS.song_end,
                SONG_STATUS.song_result,
                SONG_STATUS.song_ap,
                SONG_STATUS.me_start,
                SONG_STATUS.me_result,
            };

            return _listSongStatusToOverridePlayType.Contains(songStatus);
        }
        
        
        /// <summary>
        /// Check if the main player jump to last step of fall out to finish the event
        /// </summary>
        public static void CheckFinish() {
            if (_stateData == null) {
                return;
            }
            
            if (!isMainUserInWinStreak) {
                MarkFinishEvent(false);
            } else if(_stateData.currentStep >= maxStep){
                MarkFinishEvent(true);
            }
            SaveStateData();
        }
        
        public static bool CheckUnlockConditions() {
            FeatureUnlockCondition unlockCondition = new ();
            bool isSongStartEnough;
            bool isHasStarEnough;

            int currentSongStartCount = GetSongStartQuantity();
            int currentStars = GetTotalStar();
            
            if (FeatureUnlockManager.instanceSafe.GetUnlockCondition(FeatureKey.GALAXY_QUEST, ref unlockCondition)) {
                if (unlockCondition.isLocked) {
                    isSongStartEnough = currentSongStartCount >= unlockCondition.totalSongStart &&
                                        currentSongStartCount >= unlockCondition.furtherTotalSongStart;

                    isHasStarEnough = currentStars >= unlockCondition.totalStars &&
                                      currentStars >= unlockCondition.furtherTotalStars;   
                } else {
                    isSongStartEnough = true;
                    isHasStarEnough = true;
                }
            } else {
                isSongStartEnough = GetSongStartQuantity() >= config.unlock_conditions.song_start;
                isHasStarEnough = GetTotalStar() >= config.unlock_conditions.stars_earn;
            }
            
            bool isPlayedTimeEnough = Time.realtimeSinceStartup >= config.unlock_conditions.time_session;
            bool isUnlock = isSongStartEnough && isHasStarEnough && isPlayedTimeEnough;

            if (unlockCondition != null && isUnlock == unlockCondition.isLocked) {
                unlockCondition.isLocked = !isUnlock;
                FeatureUnlockManager.instanceSafe.SaveUnlockConditionData(FeatureKey.GALAXY_QUEST, unlockCondition);
            }
            
            return isUnlock;
        }
        
        
        /// <summary>
        /// When the main player finish event,
        /// set type (7-step or 5-step) of the next event based on whether the user failed or completed
        /// </summary>
        /// <param name="isWinQuest"></param>
        public static void MarkFinishEvent(bool isWinQuest) {
            _eventData.isWinQuest = isWinQuest;
            _stateData.isJoining = false; 
            _stateData.isLikelyToEnd = true;
            
            // event tracking
            if (isWinQuest) {
                float elapsedTime = timeElapsedSinceTriggerEvent;
                GalaxyQuestAnalysis.EmitQuestWin(elapsedTime);
                GalaxyQuestAnalysis.EmitQuestEnd(elapsedTime, QuestEndReason.WIN);
            }
            
            SaveEventData();
            SaveStateData();
        }
        
        
        /// <summary>
        /// Revive our main player when they failed the win streak.
        /// The player will continue at the nextstep like the others (bots)
        /// </summary>
        public static void ReviveEvent() {
            isMainUserInWinStreak = true;
            _stateData.isJoining = true;
            _stateData.remainReviveCount--;
            _stateData.isLikelyToEnd = false;
            CheckFinish();
        }

        public static int GetRevivePrice() {
            var paidReviveNumber = GetStateData().paidReviveNumber;
            var prices = config.revivePrices;

            if (prices == null || prices.Length == 0) {
                return 1;
            }
            
            if (paidReviveNumber >= prices.Length) {
                return prices[0];
            }
            
            return prices[paidReviveNumber];
        }

        #region Reward

        public static int GetFinalRewardGemAmount() {
            for (int i = 0; i < config.finalRewards.Length; i++) {
                if(config.finalRewards[i].type == RewardType.GEM.ToString()) {
                    return config.finalRewards[i].value;
                }
            }

            return 0;
        }

        public static void GiveReward(RewardInfo rewardInfo) {
            string questRewardType = rewardInfo.type;
            var rewardTypes = questRewardType.Split(';');
            if (rewardTypes.Length > 1) {
                if (Enum.TryParse(rewardTypes[0], out RewardType type) && type == RewardType.BOOSTER) {
                    if (Enum.TryParse(rewardTypes[1], out BoosterType boosterType)) {
                        ClaimBooster(boosterType, rewardInfo.value);
                    }
                    return;
                }
                questRewardType = rewardTypes[0];
            }
			
            if (Enum.TryParse(questRewardType, true, out RewardType rewardType)) {
                if (rewardType == RewardType.GEM) {
                    ClaimGems(rewardInfo.value);
                }
            }
        }

        #endregion

        #region Auto Show

        private static bool CheckQualified() {
            var canActive = GalaxyQuestTimeHelper.instanceSafe.RealTime >= config.time_start;
            return canActive && CheckUnlockConditions();
        }

        private static bool TryAutoShowStartPopup() {
            if (!CheckQualified()) {
                return false;
            }
            
            var stateData = GetStateData();
            if (stateData.isRemindedJoinQuest) {
                return false;
            }
            
            // if it's in the wait-for-reopen phase
            int timeToReopenEvent = reopenTime - GalaxyQuestTimeHelper.instanceSafe.RealTime;
            if (timeToReopenEvent > 0) {
                return false;
            }

            ActiveEvent();
            instanceSafe.ShowStartPopup();
            return true;
        }

        #endregion
    }
}
