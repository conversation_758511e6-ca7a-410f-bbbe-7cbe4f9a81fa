using System;
using TilesHop.Cores.UserProgression;
using UnityEngine;
using UnityEngine.U2D;
using UnityEngine.UI;
using Object = UnityEngine.Object;
using Random = UnityEngine.Random;

public static class UIHomeDecorationController {
    public static readonly string assetBundlePath                    = "gamecore/homedecor/assetbundles";
    private const          string DECOR_USER_PROFILE_ASSET_NAME      = "DecorXmas_UserProfile";
    private const          string DECOR_SPECIAL_PARTICLES_ASSET_NAME = "SnowFlowerEffect";
    private const          string DECOR_HOME_ATLAS_NAME              = "HomeDecor_Xmas";

    // random in pool
    private static readonly string[] _decorSongcardsAssetNames = {
        "decor_snow_1",
        "decor_snow_2",
    };

    private static Sprite _diskSprite;
    
    static AssetBundle _decorAssetBundle;
    static Action      _onLoadDone;

    public static void TryDecorUserProfile(Transform container, Vector3 localPosition) {
        if (UserProgressionController.EnableFeature) {
            return;
        }
        
        WaitLoadDone(() => {
            if (_decorAssetBundle == null) {
                return;
            }

            GameObject asset = _decorAssetBundle.LoadAsset<GameObject>(DECOR_USER_PROFILE_ASSET_NAME);
            if (asset) {
                GameObject decor = Object.Instantiate(asset, container);
                decor.transform.localPosition = localPosition;
                decor.transform.SetSiblingIndex(1);
            } else {
                Logger.LogWarning($"[TryDecorUserProfile]: asset {DECOR_USER_PROFILE_ASSET_NAME} not found");
            }
        });
    }

    static bool _isLoaded = false;

    private static void WaitLoadDone(Action action) {
        if (_decorAssetBundle != null) {
            action?.Invoke();
        } else {
            _onLoadDone += action;

            if (!_isLoaded) {
                _isLoaded = true;
                LoadDecorAssets();
            }
        }
    }

    private static void LoadDecorAssets() {
        Configuration.instance.StartCoroutine(AssetBundleManager.LoadAsset<AssetBundle>(assetBundlePath, String.Empty,
            asset => {
                _decorAssetBundle = asset;
                _onLoadDone?.Invoke();
            }));
    }

    public static void TryDecorWithSpecialParticles(Transform container, Vector3 localPosition) {
        WaitLoadDone(() => {
            if (_decorAssetBundle == null) {
                return;
            }

            GameObject asset = _decorAssetBundle.LoadAsset<GameObject>(DECOR_SPECIAL_PARTICLES_ASSET_NAME);
            if (asset) {
                GameObject vfx = Object.Instantiate(asset, container);
                vfx.transform.localPosition = localPosition;
            } else {
                Logger.LogWarning(
                    $"[TryDecorWithSpecialParticles]: asset {DECOR_SPECIAL_PARTICLES_ASSET_NAME} not found");
            }
        });
    }

    public static void TryDecorBottomMenuButton(Image decalImage) {
        if (!RemoteConfigBase.instance.HomeDecor_BottomMenuDecal) {
            return;
        }

        WaitLoadDone(() => {
            if (_decorAssetBundle == null) {
                return;
            }

            SpriteAtlas asset = _decorAssetBundle.LoadAsset<SpriteAtlas>(DECOR_HOME_ATLAS_NAME);
            if (asset != null) {
                decalImage.sprite = asset.GetSprite("buttonDecal");
                decalImage.enabled = true;
            } else {
                decalImage.enabled = false;
                Logger.LogWarning($"[TryDecorBottomMenuButton]: asset {DECOR_HOME_ATLAS_NAME} not found");
            }
        });
    }

    public static void TryDecorBottomMenu(Image decalImage) {
        if (!RemoteConfigBase.instance.HomeDecor_BottomMenuDecal) {
            return;
        }

        WaitLoadDone(() => {
            if (_decorAssetBundle == null) {
                return;
            }

            SpriteAtlas asset = _decorAssetBundle.LoadAsset<SpriteAtlas>(DECOR_HOME_ATLAS_NAME);
            if (asset != null) {
                decalImage.sprite = asset.GetSprite("menuDecal");
                decalImage.enabled = true;
            } else {
                decalImage.enabled = false;
                Logger.LogWarning($"[TryDecorBottomMenu]: asset {DECOR_HOME_ATLAS_NAME} not found");
            }
        });
    }

    public static void TryDecorSongCardButton(Transform container, Vector3 localPosition) {
        if (UserProgressionController.EnableFeature || RemoteConfig.instance.SongItem_StyleIndex != 1) {
            return;
        }

        WaitLoadDone(() => {
            if (_decorAssetBundle == null) {
                return;
            }

            string assetName = _decorSongcardsAssetNames[Random.Range(0, 2)];
            GameObject asset = _decorAssetBundle.LoadAsset<GameObject>(assetName);
            if (asset) {
                var decor = Object.Instantiate(asset, container);
                decor.transform.localPosition = localPosition;
            } else {
                Logger.LogWarning($"[TryDecorSongCardButton]: asset {assetName} not found");
            }
        });
    }

    public static void TryGetDecorDiskSprite(Action<Sprite> onResult) {
        if (!RemoteConfigBase.instance.HomeDecor_SongDisk) {
            onResult?.Invoke(null);
            return;
        }

        if (_diskSprite != null) {
            onResult?.Invoke(_diskSprite);
            return;
        }

        WaitLoadDone(() => {
            if (_decorAssetBundle == null) {
                onResult?.Invoke(null);
                return;
            }

            SpriteAtlas asset = _decorAssetBundle.LoadAsset<SpriteAtlas>(DECOR_HOME_ATLAS_NAME);
            if (asset != null) {
                _diskSprite = asset.GetSprite("xmas_song_disk");
            } else {
                _diskSprite = null;
                Logger.LogWarning($"[TryDecorBottomMenu]: asset {DECOR_HOME_ATLAS_NAME} not found");
            }

            onResult?.Invoke(_diskSprite);
        });
    }
}