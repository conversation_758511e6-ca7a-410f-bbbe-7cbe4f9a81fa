using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class RarityImageListenter : RarityUIListenter {
    [SerializeField] private Image[] rarityImages;
    private                  int     nextIndex;
    private                  int     curIndex;
    private                  Tween   fadeOut;

    protected override void OnEnable() {
        curIndex = -1;
        base.OnEnable();
    }

    public override void OnChangeRarity(BallRarity rarity) {
        if (!RemoteConfig.instance.RarityBall_IsEnable)
            return;

        if (!isTransfer || curIndex == -1) {
            curIndex = (int) rarity;
            for (int i = 0; i < rarityImages.Length; i++) {

                rarityImages[i].gameObject.SetActive(i == curIndex);
            }

            Color curColor = rarityImages[curIndex].color;
            curColor.a = 1f;
            rarityImages[curIndex].color = curColor;
            return;
        }

        if ((int) rarity == curIndex)
            return;

        if (fadeOut != null) {
            fadeOut.Kill();
            rarityImages[curIndex].gameObject.SetActive(false);
            curIndex = nextIndex;
        }

        nextIndex = (int) rarity;

        rarityImages[curIndex].transform.SetAsFirstSibling();
        fadeOut = rarityImages[curIndex].DOFade(0f, timeTransfer).SetEase(Ease.Linear).OnComplete(() => {
            rarityImages[curIndex].gameObject.SetActive(false);
            curIndex = nextIndex;
            fadeOut = null;
        });

        var color = rarityImages[nextIndex].color;
        color.a = 0f;
        rarityImages[nextIndex].color = color;
        rarityImages[nextIndex].gameObject.SetActive(true);
        rarityImages[nextIndex].DOFade(1f, timeTransfer).SetEase(Ease.Linear);
    }

    public void SetRarity(BallRarity rarity) {
        int index = (int) rarity;
        for (int i = 0; i < rarityImages.Length; i++) {
            rarityImages[i].gameObject.SetActive(i == index);
        }

        Color curColor = rarityImages[index].color;
        curColor.a = 1f;
        rarityImages[index].color = curColor;
    }
}