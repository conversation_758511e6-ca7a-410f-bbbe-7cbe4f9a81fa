using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using GameCore;
using TilesHop.Cores.Pooling;
using TilesHop.Cores.UserProgression;
using TilesHop.EconomySystem;
using TilesHop.GameCore.StarsJourney;
using TilesHop.LiveEvent;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class ShopScript : TabScript, IEscapeHandler {
    public enum PreviousLocation {
        home          = 0,
        offer_skin    = 1,
        unlocked_skin = 2,
        ingame        = 3,
        subscription  = 4,
    }

    #region Fields

    public enum Tabs {
        Ball      = 0,
        Character = 1,
    }

    public static ShopScript instance;

    [SerializeField] private Button      btnClose;
    [SerializeField] private Button      btnCloseByCharacter;
    [SerializeField] private Button      btnBalls;
    [SerializeField] private Button      btnCharacters;
    [SerializeField] private Transform   tfHighlight;
    private                  CanvasGroup btnBallGroup;
    private                  CanvasGroup btnCharacterGroup;

    [SerializeField] private BallListScroll ballsScroll;
    [SerializeField] private BallListScroll charactersScroll;

    [Space] [SerializeField] private Button btnEquip;
    [SerializeField]         private Button btnEquipped;
    [SerializeField]         private Button btnAdBuy;
    [SerializeField]         private Button btnCoinBuy;
    [SerializeField]         private Button btnVipBuy;
    [SerializeField]         private Button btnMoreAds;
    [SerializeField]         private Button btnShowIAPPack;

    [SerializeField] private Text   txtLabelVip;
    [SerializeField] private Text   txtCost;
    [SerializeField] private Text   txtShowAd;
    [SerializeField] private Text   txtShowMoreAds;
    [SerializeField] private Button btnEvent;

    [Space] [Header("Sale Off")] [SerializeField]
    private GameObject groupSaleOff;

    [SerializeField] private Button btnDiamondSaleOff;
    [SerializeField] private Button btnAdsSaleOff;
    [SerializeField] private Text   txtDiamondOldPrice;
    [SerializeField] private Text   txtDiamondSalePrice;
    [SerializeField] private Text   txtAdsOldPrice;
    [SerializeField] private Text   txtAdsSalePrice;

    [Space] [SerializeField] private Image imgBg;

    [SerializeField] private GameObject groupUnlockMixed;
    [SerializeField] private Button     btnUnlockDiamond;
    [SerializeField] private Text       txtUnlockDiamond;
    [SerializeField] private Button     btnUnlockVideo;
    [SerializeField] private Text       txtUnlockVideo;
    [SerializeField] private Text       txtSkinName;

    public CharacterPreviewPopup previewCharacter;

    [SerializeField] private ParticleSystem clickedVfx;
    //public                   GameObject            topMenu;

    [Space] [SerializeField] private ShopScriptDragArea clickRotateArea;
    [SerializeField]         private ShopScriptDragArea dragRotateArea;
    [SerializeField]         private RectTransform      rtfContent;
    [SerializeField]         private GameObject         loadingIndicator;
    [SerializeField]         private GameObject         collectionHeader;

    //private
    [HideInInspector] public int selectedBallID; //Select Ball
     public int clickBallID = -1; //click item in shop

    private bool _useTryBallFromAlbum;
    private bool _hasChangeBall;

    public static BallRarity currentBallRarity = BallRarity.C;
    public static event Action<BallRarity> OnChangeRarityEvent;
    public static event Action<BallRarity, bool> OnFireRarityEvent;

    private bool          _isDoAnimation;
    private RectTransform _myRectTransform;
    private float         _cameraWidth = 6;

    private Tabs _currentTab = Tabs.Ball;
    private bool isHomeScene => HomeManager.instance != null;
    private bool isPlayScene => Follow.CheckInstanced() && Ball.b != null;
    private int                       _priceBall;
    private bool                      _isItemsHumanCached;
    private ShopBallTracking.Location _locationScreen;
    private string                    _location;
    private RewardBallByVideoScript   _rewardBallByVideo;
    private RemoteConfig remoteConfig => RemoteConfigBase.instance;

    private bool _isWatchedAds;

    private bool _isShopOpened = false;
    public bool IsShopOpened => _isShopOpened;
    private event Action<bool> _onClose;
    public event Action OnHide;
    public static event Action<bool> OnResetAnimation;

    private GameObject              _labelBallUnavailable;
    private GameObject              _labelStarJourneyRequired;
    private UIStarJourneyLevelPoint _starJourneyLevelRequired;

    private const string KeyOnBoardingShopBallRotate = "shopball_rotate_onboarding";

    private bool _isResizedContent;
    private bool _isTabForm;
    private bool _isSetupLoadingIndicator;
    private int _idBallViewAds;

    #endregion

    public bool isTabForm => _isTabForm;

    #region Unity Method

    protected override void Awake() {
        base.Awake();
        instance = this;

        _myRectTransform = (RectTransform) this.transform;
        var mainCamera = Camera.main;
        if (mainCamera != null) {
            float height = 2f * mainCamera.orthographicSize;
            _cameraWidth = height * mainCamera.aspect;
        }

        btnBallGroup = btnBalls.GetComponent<CanvasGroup>();
        btnCharacterGroup = btnCharacters.GetComponent<CanvasGroup>();

        if (BottomMenuScript.isShowBallTabForm && Util.IsHomeScene()) {
            btnClose.gameObject.SetActive(false);
            _isTabForm = true;
        } else {
            btnClose.onClick.AddListener(BtnCloseOnClick);
        }

        if (btnCloseByCharacter != null) {
            btnCloseByCharacter.onClick.AddListener(BtnCloseOnClick);
        }

        ActiveLoadingIndicator(false);

        SubscriptionController.OnChange += OnBuySubs;
        btnEquip.onClick.AddListener(btnEquipOnClick);
        btnEquipped.onClick.AddListener(btnEquippedOnClick);
        btnAdBuy.onClick.AddListener(BtnAdBuyOnClick);
        btnCoinBuy.onClick.AddListener(btnCoinBuyOnClick);
        btnVipBuy.onClick.AddListener(btnVipBuyOnClick);
        btnMoreAds.onClick.AddListener(BtnAdBuyOnClick);
        btnEvent.onClick.AddListener(btnEventOnClick);

        // button unlock new
        btnUnlockDiamond.onClick.AddListener(btnCoinBuyOnClick);
        btnUnlockVideo.onClick.AddListener(BtnAdBuyOnClick);

        //rotate
        if (clickRotateArea) {
            if (remoteConfig.ShopBall_Rotate_IsEnable) {
                clickRotateArea.Init(DragRotateEvent, ClickRotateEvent);
                clickRotateArea.gameObject.SetActive(true);
            } else {
                clickRotateArea.gameObject.SetActive(false);
            }
        }

        if (dragRotateArea) {
            if (remoteConfig.ShopBall_Rotate_IsEnable) {
                dragRotateArea.Init(DragRotateEvent, null);
                dragRotateArea.gameObject.SetActive(true);
            } else {
                dragRotateArea.gameObject.SetActive(false);
            }
        }

        // button unlock sale off
        if (btnDiamondSaleOff && btnAdsSaleOff) {
            btnDiamondSaleOff.onClick.AddListener(btnCoinBuyOnClick);
            btnAdsSaleOff.onClick.AddListener(BtnAdBuyOnClick);
        }

        btnBalls.onClick.AddListener(() => btnBallsOnClick());
        btnCharacters.onClick.AddListener(() => btnCharactersOnClick());

        if (_rewardBallByVideo != null) {
            _rewardBallByVideo.gameObject.SetActive(false);
        }

        LocalizationManager.instance.UpdateFont(txtCost);
        LocalizationManager.instance.UpdateFont(txtLabelVip);
        LocalizationManager.instance.UpdateFont(txtShowAd);
        LocalizationManager.instance.UpdateFont(txtShowMoreAds);
        LocalizationManager.instance.UpdateFont(txtUnlockDiamond);
        LocalizationManager.instance.UpdateFont(txtUnlockVideo);
        LocalizationManager.instance.UpdateFont(txtDiamondOldPrice);
        LocalizationManager.instance.UpdateFont(txtDiamondSalePrice);
        LocalizationManager.instance.UpdateFont(txtAdsOldPrice);
        LocalizationManager.instance.UpdateFont(txtAdsSalePrice);
    }

    private void OnEnable() {
        _hasChangeBall = false;
        _location = isHomeScene ? "home_skinshop" : "gameplay_skinshop";
        _locationScreen = isHomeScene ? ShopBallTracking.Location.home : ShopBallTracking.Location.ingame;
        if (isPlayScene && imgBg != null) {
            Color fireColor = Spawner.s.GetCurrentSkin().fireColor;
            fireColor.a = 1;
            imgBg.color = fireColor;
        }

        skip = false;
        EventEscapeManager.Push(this);
        Configuration.OnChangeDiamond += Configuration_OnChangeDiamond;

        SoundManager.PlaySFX_PopupOpen();
        _isShopOpened = true;

        // nếu user mua pack: close popup > update buy button
        if (SeasonalPackManager.isInstanced) {
            SeasonalPackManager.instanceSafe.onEventDisable.AddListener(UpdateBuyButtonOnEventDisableIAPPackPopup);
        }

        StartViewRotate();

        if (!_isTabForm && Util.IsHomeScene()) {
            HomeManager.instance.RegisterFullScreenPopupShowing(GetInstanceID());
        }
    }

    protected override void OnDisable() {
        EndViewRotate();
        Configuration.OnChangeDiamond -= Configuration_OnChangeDiamond;
        base.OnDisable();

        EventEscapeManager.Pop(this);
        this._onClose?.Invoke(_isWatchedAds);
        _isShopOpened = false;

        if (SeasonalPackManager.isInstanced) {
            SeasonalPackManager.instanceSafe.onEventDisable.RemoveListener(UpdateBuyButtonOnEventDisableIAPPackPopup);
        }

        if (!_isTabForm && Util.IsHomeScene()) {
            HomeManager.instance.UnregisterFullScreenPopup(GetInstanceID());
        }
    }

    private void OnDestroy() {
        SubscriptionController.OnChange -= OnBuySubs;

        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }

    #endregion

    #region Escape Handlers

    public bool CanHandleEventBack() {
        if (_rewardBallByVideo != null && _rewardBallByVideo.gameObject.activeSelf)
            return false;

        return gameObject.activeInHierarchy;
    }

    public bool HandleEventBack() {
        if (_isDoAnimation)
            return false;

        if (BottomMenuScript.isShowBallTabForm && Util.IsHomeScene() && _isTabForm) {
            HomeManager.instance.OnBtnHomeClicked();
            return true;
        }

        BtnSkip();
        return true;
    }

    #endregion

    public void OnSelectCharacterItem(CharacterItem characterItem) {
        try {
            if (characterItem == null)
                return;

            if (remoteConfig.PlayNewSFX_Enable) {
                SoundManager.PlaySFX_SelectCharacter();
            } else {
                SoundManager.PlayGameButton();
            }

            _hasChangeBall = true;
            if (clickBallID == characterItem.idBall) {
                if (characterItem.characterData.IsOpenBall() && clickBallID != selectedBallID) {
                    btnEquipOnClick();
                }

                if (remoteConfig.RarityBall_IsEnable || remoteConfig.UseVFXCommonOnChangeSkin) {
                    var ballConfig = BallManager.instance.GetBallConfig(clickBallID);
                    if (ballConfig != null)
                        OnFireRarityEvent?.Invoke(ballConfig.rarity, ballConfig.category == BallCategory.B);
                }
            } else {
                //last click ball
                CharacterItem lastClickBall = FindActiveCharacterItemByID(clickBallID);
                if (lastClickBall != null) {
                    lastClickBall.SetClicked(false);
                }

                //new click ball
                clickBallID = characterItem.idBall;

                if (txtSkinName != null)
                    txtSkinName.text = BallManager.instance.GetBallConfig(clickBallID)?.name;

                UpdateBuyButtons(clickBallID);

                if (isHomeScene) {
                    HomeManager.instance.UpdateCharactersInHome(clickBallID, true);
                    HomeManager.instance.UpdateCharactersPreview(GetDefaultAnimation(clickBallID));
                } else if (isPlayScene) {
                    SetupLoadingIndicatorPosition();

                    Ball.b.ChangeBall(clickBallID, timeout: 10);
                    //ResetAnim();
                    Ball.b.ShowInShop(clickBallID, true, 0);
                    UIController.ui.UpdateCharacterPreview(clickBallID); //trying new skin
                }

                BallConfig ballConfig = BallManager.instance.GetBallConfig(clickBallID);
                if (ballConfig != null) {
                    if (remoteConfig.RarityBall_IsEnable || remoteConfig.UseVFXCommonOnChangeSkin) {
                        if (currentBallRarity != ballConfig.rarity) {
                            currentBallRarity = ballConfig.rarity;
                            OnChangeRarityEvent?.Invoke(ballConfig.rarity);
                        }

                        OnFireRarityEvent?.Invoke(ballConfig.rarity, ballConfig.category == BallCategory.B);
                    }

                    if (clickBallID != selectedBallID) {
                        EventShopBallItemClick(ballConfig);
                    }
                }
            }

            if (remoteConfig.ShopBall_VerticleScroll) {
                // tự động scroll lên khi user click chọn item ở sát mép dưới (bị che mất một phần)
                ballsScroll.CheckToScrollToView(clickBallID);
            }

        } catch (Exception e) {
            CustomException.Fire($"[OnSelectCharacterItem] {clickBallID}",
                $"Message: {e.Message}\nStackTrace:{e.StackTrace}");
        }
    }

    private void SetupLoadingIndicatorPosition() {
        if (_isSetupLoadingIndicator) {
            return;
        }

        _isSetupLoadingIndicator = true;

        Camera mainCamera = Camera.main;
        if (mainCamera == null) {
            return;
        }

        var mainCanvas = _myRectTransform.GetComponentInParent<Canvas>();
        float planeDistance = mainCanvas != null ? mainCanvas.planeDistance : 5f;

        Vector2 screenPoint = RectTransformUtility.WorldToScreenPoint(mainCamera, Ball.b.transCache.position);
        Vector3 worldPosition = mainCamera.ScreenToWorldPoint(new Vector3(screenPoint.x, screenPoint.y,
            mainCamera.nearClipPlane + planeDistance));

        loadingIndicator.transform.position = worldPosition;
    }

    private Coroutine _ieActiveLoadingIndicator;

    public void ActiveLoadingIndicator(bool isLoading) {
        if (loadingIndicator == null || !gameObject.activeSelf) {
            return;
        }

        if (_ieActiveLoadingIndicator != null) {
            StopCoroutine(_ieActiveLoadingIndicator);
        }

        if (isLoading) {
            _ieActiveLoadingIndicator = StartCoroutine(IEShowLoadingIndicator());
        } else {
            loadingIndicator.SetActive(false);
        }
    }

    private IEnumerator IEShowLoadingIndicator() {
        // show indicator when loading ball asset pretty slow
        yield return YieldPool.GetWaitForSeconds(0.5f);

        loadingIndicator.SetActive(true);
    }

    public void ResetAnim() {
        OnResetAnimation?.Invoke(GetDefaultAnimation(clickBallID) != CharacterAnimator.StartAnim);
    }

    private int GetDefaultAnimation(int id) {
        return BallManager.GetDefaultShopIdleAnimState(id);
    }

    private void OnPopupSubsClose() {
        if (this.gameObject /*&& !gameObject.activeInHierarchy*/ && !SubscriptionController.IsSubscriptionVip()) {
            ShowPopup(Direction.Bottom, PreviousLocation.subscription);
            ShowPreviewBall(true);
        }
    }

    private CharacterItem FindActiveCharacterItemByID(int id) {
        //ball
        int countActive = ballsScroll.VisibleItemsCount;
        for (int i = 0; i < countActive; i++) {
            ColumnCharacterItem activeCellView = (ColumnCharacterItem) ballsScroll.GetItemViewsHolder(i).cellView;
            if (activeCellView != null) {
                CharacterItem ballData = activeCellView.FindID(id);
                if (ballData != null) {
                    return ballData;
                }
            }
        }

        //character
        countActive = charactersScroll.VisibleItemsCount;
        for (int i = 0; i < countActive; i++) {
            ColumnCharacterItem activeCellView = (ColumnCharacterItem) charactersScroll.GetItemViewsHolder(i).cellView;
            if (activeCellView != null) {
                CharacterItem ballData = activeCellView.FindID(id);
                if (ballData != null) {
                    return ballData;
                }
            }
        }

        return null;
    }

    private void LoadData() {
        if (BallManager.instance == null) {
            return;
        }

        BallConfig ballConfig = BallManager.instance.GetBallConfig(selectedBallID);
        if (ballConfig == null) {
            selectedBallID = BallManager.DefaultBall;
            Configuration.SetSelectedBall(selectedBallID, isForce: false, location: null);
            ballConfig = BallManager.instance.GetBallConfig(selectedBallID);
            if (ballConfig == null) {
                CustomException.Fire("BallManager",
                    $"Ball config still null after change to default ball!" +
                    $" {BallManager.instance.ConfigCount}. {remoteConfig.urlBallConfig}");
            }
        }

        clickBallID = selectedBallID;

        currentBallRarity = (ballConfig != null && remoteConfig != null && remoteConfig.RarityBall_IsEnable)
            ? ballConfig.rarity
            : BallRarity.E;
        OnChangeRarityEvent?.Invoke(currentBallRarity);

        _useTryBallFromAlbum = false;
        if (isHomeScene) {

            // only hide bottom menu if not in UP because shop ball is a Tab in UP
            if (BottomMenuScript.isShowBallTabForm) {
                HomeManager.instance.menuHome.SetActiveSongList(false);
                HomeManager.instance.menuHome.SetActive(false);
                HomeManager.instance.mainHomeScript.songScroller.Show(false);
            } else {
                HomeManager.instance.SetActiveSongList(false);
            }

            HomeManager.instance.ShowCharactersInHome(true, true);
            HomeManager.instance.UpdateCharactersInHome(clickBallID, true);
            HomeManager.instance.UpdateCharactersPreview(GetDefaultAnimation(clickBallID));
        } else if (isPlayScene) {
            Vector3 newPos = new Vector3(0, -5.5f, -5.5f);
            Follow.instance.MoveCamera(newPos, timeAnimationMove);
            Ball.b.ToggleAnimShopPreview(true);

            if (TopBar.instance != null) {
                if (remoteConfig != null && remoteConfig.ShopBall_UseCategoryLayout) {
                    TopBar.instance.ToggleUIWhenShowShop(false);
                } else {
                    TopBar.instance.SetActive(false);
                }
            }

            if (Ball.b.UsingForceBallAlbum()) {
                _useTryBallFromAlbum = true;
                clickBallID = Ball.b.BallId;
            } else if (clickBallID != selectedBallID) {
                Ball.b.ChangeBall(clickBallID);
            }

            OnResetAnimation?.Invoke(GetDefaultAnimation(clickBallID) != CharacterAnimator.StartAnim);

            Ball.b.ShowInShop(clickBallID, true, timeAnimationMove);
        }

        _currentTab = GetTab(clickBallID);
        if (_currentTab == Tabs.Ball) {
            btnBallsOnClick(false);
        } else {
            btnCharactersOnClick(false);
        }

        UpdateBuyButtons(clickBallID);

        if (txtSkinName != null) {
            if (BallManager.instance == null) {
                txtSkinName.text = string.Empty;
            } else {
                var config = BallManager.instance.GetBallConfig(clickBallID);
                txtSkinName.text = config != null ? config.name : string.Empty;
            }
        }
    }

    public int GetIdBallFirstShow() {
        if (_useTryBallFromAlbum) {
            return clickBallID;
        }

        return selectedBallID;
    }

    private void OnRewardUnlockBall(bool isCompleted) {
        if (isCompleted) {
            _isWatchedAds = true;
            UnlockBallByAd();
            AirfluxTracker.TrackRewardAdsImpression();
        } else {
            BallConfig ballConfig = BallManager.instance.GetBallConfig(clickBallID);
            Dictionary<string, object> param = new Dictionary<string, object> {
                {"item_id", clickBallID},
                {"unlock_type", GetUnlockType(ballConfig)},
                {"class_type", ballConfig.rarity.ToString()},
                {"diamond_price", ballConfig.DiamondPrice},
                {"unlock_status", "fail"},
                {"location", _location}
            };
            AnalyticHelper.ShopBallItemBuy(param);
        }
    }

    private void UnlockBallByAd() {
        if (clickBallID != _idBallViewAds) {
            clickBallID = _idBallViewAds;
            CustomException.Fire("ShopBall", $"Differrent ball id before view ads {_idBallViewAds} vs after {clickBallID}");
        }
        int showAdOfBall = BallManager.GetShowAdOfBall(clickBallID) + 1;
        BallManager.SetShowAdOfBall(clickBallID, showAdOfBall);

        Sprite sprIcon = remoteConfig.ShopBall_UseCategoryLayout
            ? BallManager.instance.GetCharacterData(clickBallID).sprIcon
            : GetBallDataByID(clickBallID).sprIcon;

        BallConfig ballConfig = BallManager.instance.GetBallConfig(clickBallID);
        bool isCanGetReward = showAdOfBall >= ballConfig.AdsPrice;
        if (isCanGetReward) {
            UnlockBall(0);
        } else {
            if (ballConfig.unlockType == UnlockType.Video) {
                UpdateUIButtonAds(ballConfig, showAdOfBall);
            } else if (ballConfig.unlockType == UnlockType.Lock) {
                if (GetOldPriceBall(ballConfig) == 0 || !remoteConfig.ShopBall_SaleOff_IsEnable) {
                    if (ballConfig.AdsPrice - showAdOfBall == 1) {
                        txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                    } else {
                        txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD") + "\n" +
                                              showAdOfBall + "/" + ballConfig.AdsPrice;
                    }
                } else {
                    UpdateUIButtonAds(ballConfig, showAdOfBall);
                }
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                {"item_id", clickBallID},
                {"unlock_type", GetUnlockType(ballConfig)},
                {"class_type", ballConfig.rarity.ToString()},
                {"diamond_price", ballConfig.DiamondPrice},
                {"unlock_status", $"ads_order_{showAdOfBall}_per_{ballConfig.AdsPrice}"},
                {"location", _location}
            };
            AnalyticHelper.ShopBallItemBuy(param);
        }

        ShowPreviewBall(false);

        if (_rewardBallByVideo == null) {
            _rewardBallByVideo = Util.ShowPopUp(PopupName.RewardBallByVideo).GetComponent<RewardBallByVideoScript>();
        }

        _rewardBallByVideo.Init(isCanGetReward, ballConfig, sprIcon, this);
        _rewardBallByVideo.Show(OnCloseRewardBallByVideo);
    }

    private void OnCloseRewardBallByVideo() {
        ShowPreviewBall(true);
    }

    private void UnlockBall(int price = 0) {
        bool valid = Configuration.SetOpenBall(clickBallID, price, location: _location);
        if (!valid)
            return;

        ShopBallTracking.FireEvent(_locationScreen, _location, ShopBallTracking.EventName.unlock, clickBallID);

        SetSelectedBall(clickBallID);
        ReloadData();

        var ballConfig = BallManager.instance.GetBallConfig(clickBallID);
        Dictionary<string, object> param = new Dictionary<string, object> {
            {"item_id", clickBallID},
            {"unlock_type", GetUnlockType(ballConfig)},
            {"class_type", ballConfig.rarity.ToString()},
            {"diamond_price", ballConfig.DiamondPrice},
            {"unlock_status", "success"},
            {"location", _location}
        };
        AnalyticHelper.ShopBallItemBuy(param);
    }

    private void OnBuySubs(bool issuccess) {
        if (issuccess) {
            if (clickBallID < 0) {
                return;
            }

            BallConfig ballConfig = BallManager.instance.GetBallConfig(clickBallID);

            if (ballConfig == null) {
                return;
            }

            if (ballConfig.unlockType == UnlockType.Vip) {
                SetSelectedBall(clickBallID);
            } else if (ballConfig.unlockType == UnlockType.VipMission) {
                BtnCloseOnClick();
                HomeManager.ShowCharactersPreview(false);
                Util.ShowPopUp(PopupName.VipMission);
            }

            ReloadData();
        }
    }

    private void SetSelectedBall(int clickBallId) {
        ShopBallTracking.FireEvent(_locationScreen, _location, ShopBallTracking.EventName.select, clickBallID);
        selectedBallID = clickBallId;
        Configuration.SetSelectedBall(clickBallId, isForce: false, location: null);
        if (isPlayScene && !this.gameObject.activeInHierarchy) {
            Ball.b.ChangeBall(clickBallID); // update ball khi unlock VIP
            OnResetAnimation?.Invoke(true);
            Ball.b.ShowInShop(clickBallID, true, 0);
        }
    }

    private void UpdateBuyButtons(int clickID) {
        HideAllButton();

        CharacterData ballData = remoteConfig.ShopBall_UseCategoryLayout
            ? BallManager.instance.GetCharacterData(clickBallID)
            : GetBallDataByID(clickID);

        if (ballData == null) {
            return;
        }

        bool isOpenBall = ballData.IsOpenBall();
        if (isOpenBall) { //open ball
            bool isEquipped = clickID == selectedBallID;
            if (isEquipped) {
                btnEquipped.gameObject.SetActive(true);
            } else {
                btnEquip.gameObject.SetActive(true);
            }
        } else { //lock ball
            BallConfig ballConfig = BallManager.instance.GetBallConfig(clickID);
            UnlockType unlockType = ballData.GetUnlockType();

            switch (unlockType) {
                case UnlockType.Video:
                    UpdateUIButtonAds(ballConfig, BallManager.GetShowAdOfBall(clickID));

                    break;

                case UnlockType.Vip:
                case UnlockType.VipMission:
                    btnVipBuy.gameObject.SetActive(true);

                    // sử dụng text khác cho button buy VIP đối với Shop v2
                    string textVip = ballData.IsVipBall()
                        ? (remoteConfig.ShopBall_UseCategoryLayout ? "FREE_FOR_VIP" : "TRY_NOW")
                        : "VIP_MISSION";

                    txtLabelVip.text = LocalizationManager.instance.GetLocalizedValue(textVip);
                    break;

                case UnlockType.Diamond:
                    _priceBall = GetPriceBall(ballConfig);
                    int oldPrice = GetOldPriceBall(ballConfig);

                    if (oldPrice == 0 || !remoteConfig.ShopBall_SaleOff_IsEnable) {
                        btnCoinBuy.gameObject.SetActive(true);
                        txtCost.text = _priceBall.ToString();
                        UpdateUIButtonDiamond(btnCoinBuy);
                    } else {
                        ShowOldDiamondPrice(oldPrice);
                    }

                    break;

                case UnlockType.Lock:
                    if (SubscriptionController.IsSubscriptionVip()) { // VIP thì không hiển thị buy bằng ads nữa
                        btnCoinBuy.gameObject.SetActive(true);

                        _priceBall = GetPriceBall(ballConfig);
                        txtCost.text = _priceBall.ToString();
                    } else {
                        _priceBall = GetPriceBall(ballConfig);
                        oldPrice = GetOldPriceBall(ballConfig);

                        if (oldPrice == 0 || !remoteConfig.ShopBall_SaleOff_IsEnable) {
                            txtUnlockDiamond.text = _priceBall.ToString();
                            groupUnlockMixed.SetActive(true);

                            if (ballConfig != null) {
                                int adOfBall2 = BallManager.GetShowAdOfBall(clickID);
                                if (ballConfig.AdsPrice - adOfBall2 == 1) {
                                    txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                                } else {
                                    txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD") +
                                                          "\n" + adOfBall2 + "/" + ballConfig.AdsPrice;
                                }
                            } else {
                                txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                            }

                            UpdateUIButtonDiamond(Configuration.instance.GetDiamonds());
                        } else {
                            ShowOldDiamondPrice(oldPrice);
                            UpdateUIButtonAds(ballConfig, BallManager.GetShowAdOfBall(clickID));
                        }
                    }

                    break;

                case UnlockType.Event:
                    // chỉ hiển thị btnEvent tại màn home
                    //if (isHomeScene) {
                    // chỉ hiển thị btnEvent khi LiveEvent đang active
                    if (LiveEventManager.instance != null && LiveEventManager.instance.IsActiveEvent) {
                        btnEvent.gameObject.SetActive(true);
                    }

                    //}
                    break;

                case UnlockType.SeasonalPack:
                    if (Configuration.IsNoAllAds()) { // Seasonal balls is opened to VIP
                        if (clickID == selectedBallID) {
                            btnEquipped.gameObject.SetActive(true);
                        } else {
                            btnEquip.gameObject.SetActive(true);
                        }

                        break;
                    }

                    // TH3070: Trường hợp hết thời gian sự kiện khi user đang ở trong shop ball (chưa mở popup IAP),
                    // nếu sau đó user chọn ball trong pack => không hiện nút mua.
                    if (btnShowIAPPack != null && SeasonalPackManager.isEnable && !SeasonalPackManager.isEndEvent) {
                        btnShowIAPPack.gameObject.SetActive(true);

                        // setup listener để button onclick dẫn đến popup IAP pack cụ thể
                        btnShowIAPPack.onClick.RemoveAllListeners();
                        btnShowIAPPack.onClick.AddListener(() => {
                            PlayClickVfx();
                            SeasonalPackPopup.ShowPopup(false, EconomyIAPTracker.TRACK_LOCATION.shop_ball);

                            // fire event
                            EconomyIAPTracker.TrackIAP_PackEntryClick(SeasonalPackManager.packId,
                                EconomyIAPTracker.TRACK_LOCATION.shop_ball, Configuration.instance.GetDiamonds(),
                                SeasonalPackPopup.timeElapsed);
                        });
                    }

                    break;

                case UnlockType.Hidden:
                    ShowUnavailableLabel();
                    break;

                case UnlockType.StarJourney:
                    ShowStarJourneyUnavailableLabel(ballConfig);
                    break;
            }
        }
    }

    private void ShowOldDiamondPrice(int oldPrice) {
        groupSaleOff.SetActive(true);
        btnDiamondSaleOff.gameObject.SetActive(true);
        txtDiamondOldPrice.text = oldPrice.ToString();
        txtDiamondSalePrice.text = _priceBall.ToString();
        UpdateUIButtonDiamond(btnDiamondSaleOff);
    }

    private void ShowOldAdsPrice(BallConfig ballConfig, int showedAdOfBall) {
        // hiển thị giá sale off và old price
        groupSaleOff.SetActive(true);
        btnAdsSaleOff.gameObject.SetActive(true);
        txtAdsOldPrice.text = Util.BuildString('/', showedAdOfBall, ballConfig.OldAdsPrice);
        txtAdsSalePrice.text = Util.BuildString('/', showedAdOfBall, ballConfig.AdsPrice);
    }

    private void UpdateBuyButtonOnEventDisableIAPPackPopup() {
        if (!SeasonalPackManager.isPurchased) {
            // TH-3070: popup trong shopball kết thúc đếm ngược => auto close => refresh list ball,
            // nếu user chưa mua pack: ẩn 4 ball trong pack => show ball equipped
            selectedBallID = Configuration.GetSelectedBall();
            OnSelectCharacterItem(FindActiveCharacterItemByID(selectedBallID));
            clickBallID = selectedBallID;
        } else {
            OnSelectCharacterItem(FindActiveCharacterItemByID(clickBallID));
            selectedBallID = clickBallID;
        }

        ReloadData();
        SeasonalPackManager.instanceSafe.onEventDisable.RemoveListener(UpdateBuyButtonOnEventDisableIAPPackPopup);
    }

    private int GetPriceBall(BallConfig ballConfig) {
        int priceBall = ballConfig?.DiamondPrice ?? remoteConfig.Price_Ball;
        if (priceBall == 0) {
            priceBall = remoteConfig.Price_Ball;
        }

        return priceBall;
    }

    private int GetOldPriceBall(BallConfig ballConfig) {
        return ballConfig?.OldDiamondPrice ?? 0;
    }

    private void HideAllButton() {
        btnEquipped.gameObject.SetActive(false);
        btnEquip.gameObject.SetActive(false);
        btnAdBuy.gameObject.SetActive(false);
        btnVipBuy.gameObject.SetActive(false);
        btnCoinBuy.gameObject.SetActive(false);
        btnMoreAds.gameObject.SetActive(false);
        btnEvent.gameObject.SetActive(false);
        btnShowIAPPack.gameObject.SetActive(false);

        if (btnDiamondSaleOff && btnAdsSaleOff && groupSaleOff) {
            btnDiamondSaleOff.gameObject.SetActive(false);
            btnAdsSaleOff.gameObject.SetActive(false);
            groupSaleOff.SetActive(false);
        }

        groupUnlockMixed.SetActive(false);

        if (_labelBallUnavailable != null) {
            _labelBallUnavailable.SetActive(false);
        }

        if (_labelStarJourneyRequired != null) {
            _labelStarJourneyRequired.SetActive(false);
        }
    }

    private void ShowUnavailableLabel() {
        if (_labelBallUnavailable == null) {
            var resource = Resources.Load<GameObject>("BallEventHintLabel");
            if (resource == null) {
                return;
            }

            _labelBallUnavailable = Instantiate(resource, btnEquip.transform.parent);
            if (remoteConfig.ShopBall_VerticleScroll) {
                _labelBallUnavailable.transform.localPosition = new Vector3(0, 15f, 0);
            } else {
                _labelBallUnavailable.transform.localPosition = Vector3.zero;
            }
        } else {
            _labelBallUnavailable.SetActive(true);
        }

        if (_labelStarJourneyRequired != null) {
            _labelStarJourneyRequired.SetActive(false);
        }
    }

    private void ShowStarJourneyUnavailableLabel(BallConfig ballConfig) {
        if (_labelStarJourneyRequired == null) {
            var resource = Resources.Load<GameObject>("BallStarJourneyHintLabel");
            if (resource == null) {
                return;
            }

            _labelStarJourneyRequired = Instantiate(resource, btnEquip.transform.parent);

            if (remoteConfig.ShopBall_VerticleScroll) {
                _labelStarJourneyRequired.transform.localPosition = new Vector3(0, 15f, 0);
            } else {
                _labelStarJourneyRequired.transform.localPosition = Vector3.zero;
            }
        } else {
            _labelStarJourneyRequired.SetActive(true);
        }

        _starJourneyLevelRequired = _labelStarJourneyRequired.GetComponentInChildren<UIStarJourneyLevelPoint>();
        if (_starJourneyLevelRequired != null) {
            var starJourneyRewardBalls = StarsJourneyManager.instanceSafe.rewardBalls;
            if (starJourneyRewardBalls != null && starJourneyRewardBalls.TryGetValue(ballConfig.id, out int level)) {
                _starJourneyLevelRequired.SetValue(level);
            }
        }

        if (_labelBallUnavailable != null) {
            _labelBallUnavailable.SetActive(false);
        }
    }

    private CharacterData GetBallDataByID(int idBall) {
        CharacterData ballData = ballsScroll.GetBallDataByID(idBall);
        if (ballData != null) {
            return ballData;
        }

        ballData = charactersScroll.GetBallDataByID(idBall);
        if (ballData != null) {
            return ballData;
        }

        return null;
    }

    #region Buttons

    private void btnBallsOnClick(bool isCheck = true) {
        if (!_isDoAnimation && (!isCheck || (isCheck && _currentTab != Tabs.Ball))) {
            _isDoAnimation = true;
            _currentTab = Tabs.Ball;

            bool isNeedTransition = !(BottomMenuScript.isShowBallTabForm && Util.IsHomeScene());
            ballsScroll.Show(isNeedTransition ? Direction.Left : Direction.None, this, true, _useTryBallFromAlbum);
            charactersScroll.Hide(isNeedTransition ? Direction.Right : Direction.None);

            tfHighlight.DOLocalMoveX(btnBalls.transform.GetLocalX(), timeAnimationMove).OnComplete(CompleteShowShop);

            btnBallGroup.DOFade(1f, timeAnimationMove);
            btnCharacterGroup.DOFade(0.3f, timeAnimationMove);
        }
    }

    private void btnCharactersOnClick(bool isCheck = true) {
        if (!_isDoAnimation && (!isCheck || (isCheck && _currentTab != Tabs.Character))) {
            _isDoAnimation = true;
            _currentTab = Tabs.Character;
            ballsScroll.Hide(Direction.Left);
            charactersScroll.Show(Direction.Right, this, false, _useTryBallFromAlbum);

            tfHighlight.DOLocalMoveX(btnCharacters.transform.GetLocalX(), timeAnimationMove)
                .OnComplete(CompleteShowShop);

            btnBallGroup.DOFade(0.3f, timeAnimationMove);
            btnCharacterGroup.DOFade(1f, timeAnimationMove);
        }
    }

    private void CompleteShowShop() {
        _isDoAnimation = false;

        CheckShowOnboardingRotate();
    }

    private void CheckShowOnboardingRotate() {
        if (!remoteConfig.ShopBall_Rotate_IsEnable) {
            return;
        }

        if (!remoteConfig.ShopBall_Rotate_Onboarding) {
            return;
        }

        if (PlayerPrefs.HasKey(KeyOnBoardingShopBallRotate)) {
            return;
        }

        Util.ShowPopUp(ResourcesPath.ShopBall_Rotate_Onboarding);
        PlayerPrefs.SetInt(KeyOnBoardingShopBallRotate, 1);
    }

    private void btnEquipOnClick() {
        CharacterItem characterItem = FindActiveCharacterItemByID(clickBallID);

        // khi sử dụng layout category, có trường hợp không có ball đang chọn trong list
        if (characterItem == null && !remoteConfig.ShopBall_UseCategoryLayout) {
            return;
        }

        //reset old selected ball
        CharacterItem oldCharacterItem = FindActiveCharacterItemByID(selectedBallID);
        if (oldCharacterItem != null) {
            oldCharacterItem.SetSelected(false);
        }

        selectedBallID = clickBallID;
        SoundManager.PlayShopSelectChar();
        PlayClickVfx();

        // int ballId = characterItem == null ? selectedBallID : characterItem.characterData.id;
        ShopBallTracking.FireEvent(_locationScreen, _location, ShopBallTracking.EventName.@select, selectedBallID);
        Configuration.SetSelectedBall(selectedBallID, isForce: false, location: null);

        //UI
        UpdateBuyButtons(clickBallID);

        if (characterItem != null) {
            characterItem.SetSelected(true);
        }

        //ResetCharacter();

        LiveEventTracker.ShopBall_EquipBall(clickBallID);
    }

    private void btnEquippedOnClick() {
        PlayClickVfx();
    }

    public void btnEventOnClick() {
        PlayClickVfx();
        if (isHomeScene) {
            HomeManager.instance.NavigateLiveEvent(TrackingLocation.shop_ball.ToString());
        }
    }

    public void BtnAdBuyOnClick() {
        PlayClickVfx();
        _idBallViewAds = clickBallID;
        AdsManager.instance.ShowRewardAds(VIDEOREWARD.item, null, location: _location.ToString(), true,
            OnRewardUnlockBall);
    }

    private void btnCoinBuyOnClick() {
        PlayClickVfx();
        if (Configuration.instance.GetDiamonds() >= _priceBall) {
            UnlockBall(_priceBall);
            SoundManager.PlayBallPurchased();
        } else {
            //Hide(Direction.None);

            bool isHuman = BallManager.itemsHuman.Contains(clickBallID);
            DisableCallbackUI popupNeedMore =
                Util.ShowNeedMore(isHuman ? LOCATION_NAME.unlock_char : LOCATION_NAME.unlock_ball, _priceBall,
                    VIDEOREWARD.item, default, true);
            if (popupNeedMore) {
                ShowPreviewBall(false);
                popupNeedMore.Show(() => { ShowPreviewBall(true); });
            }

            var ballConfig = BallManager.instance.GetBallConfig(clickBallID);
            Dictionary<string, object> param = new Dictionary<string, object> {
                {"item_id", clickBallID},
                {"unlock_type", GetUnlockType(ballConfig)},
                {"class_type", ballConfig.rarity.ToString()},
                {"diamond_price", ballConfig.DiamondPrice},
                {"unlock_status", "fail"},
                {"location", _location}
            };
            AnalyticHelper.ShopBallItemBuy(param);
        }
    }

    private void btnVipBuyOnClick() {
        PlayClickVfx();
        ShopBallTracking.FireEvent(_locationScreen, _location, ShopBallTracking.EventName.vip_click, clickBallID);

        BallConfig ballConfig = BallManager.instance.GetBallConfig(clickBallID);
        if (remoteConfig.WeeklyMission_IsEnable && ballConfig != null &&
            ballConfig.unlockType == UnlockType.VipMission && SubscriptionController.IsSubscriptionVip()) {
            Util.ShowPopUp(PopupName.VipMission);
        } else {
            SUBSCRIPTION_SCREEN subscriptionSource = GetSubscriptionScreen(ballConfig);
            bool showSubscription =
                SubscriptionController.ShowSubscription(subscriptionSource.ToString(), userOpen: true);
            if (showSubscription) {
                ShowPreviewBall(false);
                SubscriptionController.OnPopupClose = OnPopupSubsClose;
            }
        }
    }

    public void PlayClickVfx() {
        if (clickedVfx) {
            clickedVfx.transform.position = EventSystem.current.currentSelectedGameObject.transform.position;
            clickedVfx.Stop();
            clickedVfx.Play();
        } else {
            SoundManager.PlayGameButton();
        }
    }

    private SUBSCRIPTION_SCREEN GetSubscriptionScreen(BallConfig ballConfig) {
        SUBSCRIPTION_SCREEN subscriptionSource;
        bool isClickItemMission = ballConfig != null && ballConfig.unlockType == UnlockType.VipMission;
        if (_locationScreen == ShopBallTracking.Location.home) {
            subscriptionSource = isClickItemMission
                ? SUBSCRIPTION_SCREEN.shop_ball_inhome_mission
                : SUBSCRIPTION_SCREEN.shop_ball_inhome;
        } else {
            subscriptionSource = isClickItemMission
                ? SUBSCRIPTION_SCREEN.shop_ball_ingame_mission
                : SUBSCRIPTION_SCREEN.shop_ball_ingame;
        }

        return subscriptionSource;
    }

    #endregion

    public void ShowPopup(Direction direction, PreviousLocation previousLocation, int index = -1, bool value = false,
                          Action<bool> onClose = null) {
        if (isUITabMoving) {
            onClose?.Invoke(value);
            return;
        }

        // cần check null vì khi close popup sub, vô tình làm mất onClose do OnPopupSubsClose()
        if (onClose != null) {
            _onClose = onClose;
        }

        _isWatchedAds = value;

        Show(direction);
        if (collectionHeader != null) {
            collectionHeader.SetActive(true);
        }

        if (index >= 0) {
            selectedBallID = index;
        } else if ((previousLocation == PreviousLocation.offer_skin ||
                    previousLocation == PreviousLocation.unlocked_skin) && Ball.b.tryBallId >= 0) {
            selectedBallID = Ball.b.tryBallId;
        } else {
            selectedBallID = Configuration.GetSelectedBall();
        }

        _isItemsHumanCached = BallManager.itemsHuman.Contains(selectedBallID);

        ShopBallTracking.FireEvent(_locationScreen, _location, ShopBallTracking.EventName.impression, selectedBallID,
            previousLocation.ToString());

        LoadData();

        if (!BottomMenuScript.isShowBallTabForm) {
            DOVirtual.DelayedCall(timeAnimationMove, OnShowPopupDone);
        }
    }

    private void OnShowPopupDone() {
        btnClose.gameObject.SetActive(true);
    }

    private bool skip = false;

    public void BtnSkip() {
        skip = true;
        BtnCloseOnClick();
    }

    public override void Hide(Direction fromDirection, Action onHideDone = null) {
        base.Hide(fromDirection, onHideDone);
        OnHide?.Invoke();
        if (collectionHeader != null) {
            collectionHeader.SetActive(false);
        }
    }

    private void BtnCloseOnClick() {
        SoundManager.PlayGameButton();
        if (isUITabMoving && !skip) {
            return;
        }

        _isShopOpened = false;

        SoundManager.PlaySFX_PopupClose();
        // when in UP, shop ball is a sub-tab, so it doesn't need to transition
        Hide(BottomMenuScript.isShowBallTabForm ? Direction.None : Direction.Bottom);
        OnClose();
    }

    public void HideImmediately() {
        _isShopOpened = false;

        Hide(Direction.None);
        OnClose();
    }

    private void OnClose() {
        if (isHomeScene) { //In home scene
            HomeManager.instance.ShowCharactersInHome(remoteConfig.isShowCharactersInHome);
            HomeManager.instance.SetActiveSongList(true);
            HomeManager.instance.UpdateCharactersInHome(selectedBallID);
            HomeManager.instance.UpdateCharactersPreview(CharacterAnimator.StartAnim);
            AnalyticHelper.LogEventShopBallBackButtonImpression(Configuration.instance.GetDiamonds(),
                _currentTab.ToString());
        } else if (isPlayScene) { //In game play scene
            Follow.instance.ResetPosCamera(timeAnimationMove);
            Ball.b.ToggleAnimShopPreview(false);

            if (remoteConfig.ShopBall_UseCategoryLayout) {
                TopBar.instance.ToggleUIWhenShowShop(true);
            } else {
                TopBar.instance.SetActive(true);
            }

            if (Ball.b.UsingForceBallAlbum() && !_hasChangeBall) {
                //
            } else {
                Ball.b.StopDownloadBallFromAssetBundle();
                int idSelectBall = Configuration.GetSelectedBall();
                Ball.b.UpdateBall(idSelectBall, timeout: 3);
                Ball.b.ShowInShop(clickBallID, false, timeAnimationMove);
                NotesManager.instance.UpdateLastNoteData(idSelectBall);

                bool isItemsHuman = BallManager.itemsHuman.Contains(idSelectBall);
                if (isItemsHuman != _isItemsHumanCached && Spawner.s.GetDiamondCount() > 0) {
                    Spawner.s.ClearDiamondLine();
                }
            }

            if (Ball.b.tryBallId > 0) {
                ShowPreviewBall(false);
                TopBar.instance.ShowButtonShopInGame(false);
            } else {
                GameItems.instance.InitTrySkin();
                UIController.ui.gameui.ShowHandUI(true);
                AnalyticHelper.instance.SetCurrentLocation(LOCATION_NAME.PREPARE_GAME);
            }
        }

        btnClose.gameObject.SetActive(false);
    }

    private void ReloadData() {
        if (_currentTab == Tabs.Ball) {
            ballsScroll.ReloadPopup();
        } else {
            charactersScroll.ReloadPopup();
        }

        UpdateBuyButtons(clickBallID);
    }

    private Tabs GetTab(int idBall) {
        Logger.EditorLog($"idBall {idBall} in list {ballsScroll.GetIndexOf(idBall)}");
        if (ballsScroll.GetIndexOf(idBall) >= 0) {
            return Tabs.Ball;
        }

        if (remoteConfig.ShopBall_EntryPointV2_IsEnable) {
            return Tabs.Ball;
        }

        return Tabs.Character;
    }

    public static string GetUnlockType(BallConfig ballConfig) {
        switch (ballConfig.unlockType) {
            case UnlockType.Lock:
                return ($"both_{ballConfig.diamond}_and_{ballConfig.ads}");

            case UnlockType.Video:
                return $"ads_{ballConfig.AdsPrice}";

            case UnlockType.Diamond:
                return $"diamond_{ballConfig.DiamondPrice}";

            case UnlockType.Open:
                return "free";

            case UnlockType.Vip:
                return "vip";

            case UnlockType.VipMission:
                return "mission";

            case UnlockType.Event:
                return "event";

            case UnlockType.FreeChallenge:
                return "free_challenge";

            default:
                return ballConfig.unlockType.ToString();
        }
    }

    private void Configuration_OnChangeDiamond(int amount) {
        UpdateUIButtonDiamond(amount);
    }

    private void UpdateUIButtonDiamond(int amount) {
        bool canBuy = amount >= _priceBall;
        if (btnCoinBuy.gameObject.activeInHierarchy) {
            btnCoinBuy.image.color =
                canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;
        }

        if (btnUnlockDiamond.gameObject.activeInHierarchy) {
            btnUnlockDiamond.image.color =
                canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;
        }

        if (btnDiamondSaleOff && btnDiamondSaleOff.gameObject.activeInHierarchy) {
            btnDiamondSaleOff.image.color =
                canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;
        }
    }

    private void UpdateUIButtonDiamond(params Button[] buyButtons) {
        bool canBuy = Configuration.instance.GetDiamonds() >= _priceBall;
        foreach (Button buyButton in buyButtons) {
            buyButton.image.color =
                canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;
        }
    }

    private void UpdateUIButtonAds(BallConfig ballConfig, int showedAdOfBall) {
        if (ballConfig.OldAdsPrice == 0 || !remoteConfig.ShopBall_SaleOff_IsEnable) {
            if (ballConfig.AdsPrice - showedAdOfBall == 1) {
                btnAdBuy.gameObject.SetActive(true);
                btnMoreAds.gameObject.SetActive(false);
                txtShowAd.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
            } else {
                btnAdBuy.gameObject.SetActive(false);
                btnMoreAds.gameObject.SetActive(true);
                txtShowMoreAds.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD") + "\n" +
                                      showedAdOfBall + "/" + ballConfig.AdsPrice;
            }
        } else {
            ShowOldAdsPrice(ballConfig, showedAdOfBall);
        }
    }

    public void ShowPreviewBall(bool enable) {
        if (this.gameObject.activeInHierarchy && previewCharacter != null) {
            previewCharacter.gameObject.SetActive(enable);
            if (enable) {
                previewCharacter.UpdateCharacterPreview(clickBallID);
            }
        }
    }

    private void EventShopBallItemClick(BallConfig ballConfig) {
        Dictionary<string, object> param = new Dictionary<string, object>();
        param.Add("item_id", clickBallID);
        param.Add("unlock_type", GetUnlockType(ballConfig));
        param.Add("class_type", ballConfig.rarity.ToString());
        param.Add("diamond_price", ballConfig.DiamondPrice);
        bool isOpenBall = GetBallDataByID(clickBallID).IsOpenBall();
        if (isOpenBall) {
            param.Add("unlock_status", "unlocked");
        } else {
            if (ballConfig.unlockType == UnlockType.Lock || ballConfig.unlockType == UnlockType.Video) {
                int showAdOfBall = BallManager.GetShowAdOfBall(clickBallID);
                param.Add("unlock_status", $"ads_order_{showAdOfBall}_per_{ballConfig.AdsPrice}");
            } else {
                param.Add("unlock_status", "dia_locked");
            }
        }

        param.Add("location", this._location);
        AnalyticHelper.ShopBallItemClick(param);
    }

    #region Rotate

    private void StartViewRotate() {
        if (!remoteConfig.ShopBall_Rotate_IsEnable) {
            return;
        }

        if (isHomeScene) {
            // HomeManager.instance.charactersPreview.Rotate(angle);
        } else if (Ball.b != null) {
            Ball.b.StartViewRotate();
        }
    }

    private void EndViewRotate() {
        if (remoteConfig == null || !remoteConfig.ShopBall_Rotate_IsEnable) {
            return;
        }

        if (isHomeScene) {
            // HomeManager.instance.charactersPreview.Rotate(angle);
        } else if (Ball.b != null) {
            Ball.b.EndViewRotate();
        }
    }

    private void ClickRotateEvent() {
        if (isHomeScene) {
            HomeManager.instance.charactersPreview.PlayRandomAnimation();
        } else if (Ball.b != null) {
            Ball.b.PlayRandomAnimation();
        }
    }

    private void DragRotateEvent(Vector2 delta) {
        float deltaScreen = delta.x;
        float pixelPerUnit = _myRectTransform.rect.width / _cameraWidth;
        float deltaWorld = deltaScreen / pixelPerUnit;
        float angle = -deltaWorld * remoteConfig.ShopBall_Rotate_Sensitive;

        if (isHomeScene) {
            HomeManager.instance.charactersPreview.Rotate(angle);
        } else if (Ball.b != null) {
            Ball.b.ShopRotate(angle);
        }
    }

    #endregion

    public void ResizeContent() {
        if (_isResizedContent || rtfContent == null) {
            return;
        }

        float offset;
        if (remoteConfig.ShopBall_VerticleScroll) {
            offset = BottomMenuScript.heightBottomMenu + 20;
        } else {
            offset = Mathf.Max(BottomMenuScript.heightBottomMenu, 95F);
        }

        rtfContent.SetSizeDeltaY(rtfContent.sizeDelta.y - offset);
        _isResizedContent = true;
    }
}