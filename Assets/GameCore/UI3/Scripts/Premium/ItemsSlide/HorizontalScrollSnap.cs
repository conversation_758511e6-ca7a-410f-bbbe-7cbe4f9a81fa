using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace Assets.SimpleSlider.Scripts {
    /// <summary>
    /// Performs center/focus on child and swipe features.
    /// </summary>
    [RequireComponent(typeof(ScrollRect))]
    public class HorizontalScrollSnap : MonoBehaviour, IBeginDragHandler, IDragHandler, IEndDragHandler {
        //public
        [Header("Config")] [SerializeField] private float     timeShow       = 2;
        [SerializeField]                    private int       swipeThreshold = 50;
        [SerializeField]                    private float     swipeTime      = 0.5f;
        [SerializeField]                    private bool      isAutoSwitch   = true;
        [SerializeField]                    private Transform tfGrid;

        [Header("Components")] [SerializeField]
        private ScrollRect scrollRect;

        [FormerlySerializedAs("Pagination")] [SerializeField]
        private GameObject pagination;

        //private
        public  List<Toggle> _pageToggles;
        private bool         _drag;
        private bool         _lerp;
        private int          _page;
        private float        _dragTime;

        private WaitForSeconds _wait;
        private int            _totalPage;

        /// <summary>
        /// Awake
        /// </summary>
        private void Awake() {
            _wait = new WaitForSeconds(timeShow);
        }

        /// <summary>
        /// Initializes scroll rect and paginator.
        /// </summary>
        /// <param name="random"></param>
        public void Initialize(bool random = false) {
            _totalPage = _pageToggles.Count;

            scrollRect.horizontalNormalizedPosition = 0;
            if (pagination != null) {
                //_pageToggles = pagination.GetComponentsInChildren<Toggle>(true);
                foreach (Toggle toggle in _pageToggles) {
                    toggle.onValueChanged.AddListener((isOn) => {
                        if (isOn && !_lerp) {
                            int i = int.Parse(toggle.name);
                            bool isSeted = SetPage(i);
                            if (isSeted) {
                                StopAutoSwitch();
                            }
                        }
                    });
                }

            }

            if (random) {
                ShowRandom();
            }

            UpdatePaginator(_page);
            enabled = true;

            if (startAutoSwitch != null) {
                StopCoroutine(startAutoSwitch);
            }

            startAutoSwitch = StartCoroutine(AutoSwitch());
            SetPage(0, true);
        }

        /// <summary>
        /// StopAutoSwitch
        /// </summary>
        public void StopAutoSwitch() {
            if (startAutoSwitch != null) {
                StopCoroutine(startAutoSwitch);
            }

            isAutoSwitch = false;
        }

        /// <summary>
        /// Performs focusing on target page.
        /// </summary>
        private void OnEnable() {
            Initialize();
        }

        public void Update() {
            if (!_lerp || _drag)
                return;

            if (pagination) {
                var page = GetCurrentPage();

                if (!_pageToggles[page].isOn) {
                    UpdatePaginator(page);
                }
            }

            float horizontalNormalizedPosition = (float) _page / (_totalPage - 1);

            scrollRect.horizontalNormalizedPosition = Mathf.Lerp(scrollRect.horizontalNormalizedPosition,
                horizontalNormalizedPosition, 5 * Time.deltaTime);

            if (Math.Abs(scrollRect.horizontalNormalizedPosition - horizontalNormalizedPosition) < 0.0005f) {
                scrollRect.horizontalNormalizedPosition = horizontalNormalizedPosition;
                _lerp = false;

                if (startAutoSwitch != null) {
                    StopCoroutine(startAutoSwitch);
                }

                if (isAutoSwitch) {
                    startAutoSwitch = StartCoroutine(AutoSwitch());
                }
            }
        }

        Coroutine startAutoSwitch;

        /// <summary>
        /// AutoSwitch
        /// </summary>
        /// <returns></returns>
        private IEnumerator AutoSwitch() {
            yield return null;

            if (isAutoSwitch) {
                yield return _wait;

                if (_page == _totalPage - 1) {
                    SetPage(0);
                } else {
                    SlideNext();
                }
            }
        }

        /// <summary>
        /// Show random banner (immediately).
        /// </summary>
        public void ShowRandom() {
            if (_totalPage <= 1)
                return;

            int page;

            do {
                page = UnityEngine.Random.Range(0, _totalPage);
            } while (page == _page);

            _lerp = false;
            _page = page;
            scrollRect.horizontalNormalizedPosition = (float) _page / (_totalPage - 1);
        }

        /// <summary>
        /// Show next page.
        /// </summary>
        public void SlideNext() {
            Slide(1);
        }

        /// <summary>
        /// Show prev page.
        /// </summary>
        public void SlidePrev() {
            Slide(-1);
        }

        /// <summary>
        /// Slide
        /// </summary>
        /// <param name="direction"></param>
        private void Slide(int direction) {
            direction = Math.Sign(direction);

            if (_page == 0 && direction == -1 || _page == _totalPage - 1 && direction == 1)
                return;

            _lerp = true;
            _page += direction;
        }

        /// <summary>
        /// SetPage
        /// </summary>
        /// <param name="page"></param>
        /// <param name="isForce"></param>
        /// <returns></returns>
        public bool SetPage(int page, bool isForce = false) {
            if (_page != page || isForce) {
                page = Mathf.Clamp(page, 0, _totalPage - 1);
                _lerp = true;
                _page = page;
                return true;
            } else {
                return false;
            }
        }

        /// <summary>
        /// GetCurrentPage
        /// </summary>
        /// <returns></returns>
        public int GetCurrentPage() {
            var childCount = _totalPage;
            int index = Mathf.RoundToInt(scrollRect.horizontalNormalizedPosition * (childCount - 1));
            index = Mathf.Clamp(index, 0, childCount - 1);
            return index;
        }

        /// <summary>
        /// UpdatePaginator
        /// </summary>
        /// <param name="page"></param>
        private void UpdatePaginator(int page) {
            if (SubsciptionOnboarding.instance != null)
                CheckEvent(page);
            if (pagination) {
                _pageToggles[page].isOn = true;
            }
        }

        /// <summary>
        /// On Begin Drag
        /// </summary>
        /// <param name="eventData"></param>
        public void OnBeginDrag(PointerEventData eventData) {
            _drag = true;
            _dragTime = Time.time;
        }

        /// <summary>
        /// On Drag
        /// </summary>
        /// <param name="eventData"></param>
        public void OnDrag(PointerEventData eventData) {
            var page = GetCurrentPage();

            if (page != _page) {
                _page = page;
                UpdatePaginator(page);
            }
        }

        /// <summary>
        /// On End Drag
        /// </summary>
        /// <param name="eventData"></param>
        public void OnEndDrag(PointerEventData eventData) {
            var delta = eventData.pressPosition.x - eventData.position.x;

            if (Mathf.Abs(delta) > swipeThreshold && Time.time - _dragTime < swipeTime) {
                var direction = Math.Sign(delta);

                Slide(direction);
                //StopAutoSwitch();
            }

            _drag = false;
            _lerp = true;
        }

        private string[] pages = new string[6] { "skins", "mission", "revive", "songs", "no_ad", "diamond" };

        void CheckEvent(int page) {
            var param = AnalyticHelper.GetDefaultParam(string.Empty);
            param.Add(TRACK_NAME.page_benefit, pages[page]);
            param.Add(TRACK_NAME.open_method, _drag ? "manual" : "automatic");
            AnalyticHelper.LogEvent(SUBSCRIPTION_ONBOARDING_EVENT.sub_onboarding_page_open.ToString(), param);
        }

        public void RemoveIndexPage(int indexPage) {
            if (indexPage > _pageToggles.Count) {
                return;
            }

            _pageToggles[indexPage].gameObject.SetActive(false);
            _pageToggles.RemoveAt(indexPage);

            tfGrid.GetChild(indexPage).gameObject.SetActive(false);
        }
    }
}