using System;
using UnityEngine;
using UnityEngine.UI;

namespace Inwave {
    /// <summary>
    /// trungvt
    /// </summary>
    [DisallowMultipleComponent]
    [RequireComponent(typeof(Toggle))]
    public class InToggle : MonoBehaviour {
        #region Fields

        //public
        [SerializeField] private Sprite spriteOn;
        [SerializeField] private Sprite spriteOff;

        public Action<bool> onValueChanged;

        //private
        private Toggle _toggle;
        private Image _imageSwitch;

        public bool isOn {
            get {
                if (!_toggle) {
                    Awake();
                }

                return _toggle.isOn;

            }
            set {
                if (!_toggle) {
                    Awake();
                }

                _toggle.isOn = value;

            }
        }

        #endregion

        #region Unity Method

        private void Awake() {
            Reset();
        }

        protected void Reset() {
            if (!_toggle) {
                //Toggle
                _toggle = GetComponent<Toggle>();
                if (!_toggle) {
                    _toggle = gameObject.AddComponent<Toggle>();
                }

                _toggle.transition = Selectable.Transition.None;
                _toggle.onValueChanged.AddListener(ToggleOnValueChanged);

                //Image
                Transform on = transform.Find("Image");
                if (!on) {
                    GameObject imgOn = new GameObject("Image");
                    imgOn.AddComponent<RectTransform>();
                    imgOn.transform.SetParent(transform, false);

                    _imageSwitch = imgOn.AddComponent<Image>();
                } else {
                    _imageSwitch = on.GetComponent<Image>();
                }
            }
        }

        #endregion

        private void ToggleOnValueChanged(bool value) {
            _imageSwitch.sprite = value ? spriteOn : spriteOff;

            onValueChanged?.Invoke(value);
        }
    }

#if UNITY_EDITOR
    public static class ToggleEditor {
        [UnityEditor.MenuItem("GameObject/Inwave/Toggle", false, 99)]
        static void AddToggle() {

            Transform activeTransform = UnityEditor.Selection.activeTransform;
            if (!activeTransform) {
                Debug.Log("no transform selected");
                return;
            }

            Debug.Log(activeTransform.name);
            GameObject toggle = new GameObject("Toggle");
            toggle.AddComponent<RectTransform>();
            toggle.transform.SetParent(activeTransform, false);

            toggle.AddComponent<InToggle>();
        }
    }

#endif
}