using System;
using System.Collections.Generic;

[Serializable]
public class GenreSelected {
    public DataGenre genrePop = new DataGenre();
    public DataGenre genreEDM = new DataGenre();
    public DataGenre genreHipHop = new DataGenre();
    public DataGenre genreClassical = new DataGenre();
    public DataGenre genreRock = new DataGenre();
    public DataGenre genreRB = new DataGenre();
    public DataGenre genrePopular = new DataGenre();
    public DataGenre genreChrismas = new DataGenre();
    public DataGenre genreOther = new DataGenre();

    public List<string> GetGenresName() {
        List<string> listGenre = new List<string>();

        if (genrePop.isSelected) {listGenre.Add(GenreType.POP);}
        if (genreEDM.isSelected) {listGenre.Add(GenreType.EDM);}
        if (genreHipHop.isSelected) {listGenre.Add(GenreType.HIPHOP);}
        if (genreClassical.isSelected) {listGenre.Add(GenreType.CLASSICAL);}
        if (genreRock.isSelected) {listGenre.Add(GenreType.ROCK);}
        if (genreChrismas.isSelected) { listGenre.Add(GenreType.XMAS); }
        if (genreRB.isSelected) { listGenre.Add(GenreType.RNB); }
        if (genreOther.isSelected) { listGenre.Add(GenreType.OTHERS); }
        if (genrePopular.isSelected) { listGenre.Add(GenreType.POPULAR); }
        return listGenre;
    }
}