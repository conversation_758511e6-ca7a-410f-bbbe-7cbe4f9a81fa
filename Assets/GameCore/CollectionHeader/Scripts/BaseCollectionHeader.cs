using UnityEngine;
using UnityEngine.UI;

public abstract class BaseCollectionHeader : MonoBehaviour {
    [SerializeField] protected Text  vietnameseTitle;
    [SerializeField] protected Image englishTitle;
    [SerializeField] protected Text  countText;

    protected virtual void OnEnable() {
        SetupLocalizedText();
        UpdateCollectionCount();
        LocalizationManager.instance.OnLanguageChange += SetupLocalizedText;
        RegisterChangeCount();
    }
    
    protected virtual void OnDisable() {
        LocalizationManager.instance.OnLanguageChange -= SetupLocalizedText;
        UnregisterChangeCount();
    }

    private void SetupLocalizedText() {
        bool isVietnamese = LocalizationManager.GetCurrentLanguageID() == LocalizationManager.VietnameseID;
        if (englishTitle != null) {
            englishTitle.enabled = !isVietnamese;
        }

        if (vietnameseTitle != null) {
            vietnameseTitle.enabled = isVietnamese;
        }
    }
    
    protected abstract void UpdateCollectionCount();
    protected abstract void RegisterChangeCount();
    protected abstract void UnregisterChangeCount();
}
