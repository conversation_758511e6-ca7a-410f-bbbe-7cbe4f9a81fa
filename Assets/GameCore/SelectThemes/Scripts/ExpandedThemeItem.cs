using System;
using System.Collections.Generic;
using TilesHop.GameCore.StarsJourney;
using TilesHop.LiveEvent;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class ExpandedThemeItem : ThemeItem {
    [SerializeField] private Text themeTitle;

    [Header("Localized Button Labels")] [SerializeField]
    private Text txtCost;

    [SerializeField] private Text txtLabelVip;
    [SerializeField] private Text txtShowAd;
    [SerializeField] private Text txtUnlockDiamond;
    [SerializeField] private Text txtUnlockVideo;

    [Header("Buttons")] [SerializeField] private Button     btnAdBuy;
    [SerializeField]                     private But<PERSON>     btnCoinBuy;
    [SerializeField]                     private Button     btnVipBuy;
    [SerializeField]                     private Button     btnEquip;
    [SerializeField]                     private Button     btnUnlockDiamond;
    [SerializeField]                     private Button     btnUnlockVideo;
    [SerializeField]                     private Button     btnEvent;
    [SerializeField]                     private Button     btnProgress;
    [SerializeField]                     private GameObject groupUnlockMixed;
    [SerializeField]                     private GameObject starsRequireInfo;
    [SerializeField]                     private GameObject unlockViaEventLabel;
    [SerializeField]                     private Text       starsRequireText;

    [SerializeField] private UIStarJourneyLevelPoint starJourneyHint;

    private   int           _priceTheme;
    private   int           _selectedThemeID; //Select Theme
    protected LOCATION_NAME location = LOCATION_NAME.expanded_theme;

    private void Start() {
        btnAdBuy.onClick.AddListener(OnClickBtnAdBuy);
        btnCoinBuy.onClick.AddListener(OnClickBtnDiamondBuy);
        btnVipBuy.onClick.AddListener(OnClickBtnVip);
        btnEquip.onClick.AddListener(OnClickBtnEquip);
        btnUnlockDiamond.onClick.AddListener(OnClickBtnDiamondBuy);
        btnUnlockVideo.onClick.AddListener(OnClickBtnAdBuy);
        btnEvent.onClick.AddListener(OnClickBtnEvent);

        SubscriptionController.OnChange += OnBuySubs;
    }

    private void OnDestroy() {
        SubscriptionController.OnChange -= OnBuySubs;
    }

    private void OnEnable() {
        Configuration.OnChangeDiamond += UpdateUIButtonDiamond;
        SelectThemes.OnClickOnePreviewThemeItem += OnClickItemPreview;
        SelectThemes.OnBuySubCompleted += UpdateItemState;
    }

    private void OnDisable() {
        Configuration.OnChangeDiamond -= UpdateUIButtonDiamond;
        SelectThemes.OnClickOnePreviewThemeItem -= OnClickItemPreview;
        SelectThemes.OnBuySubCompleted -= UpdateItemState;
    }

    private void OnClickBtnEquip() {
        SelectThemes.instance.clickThemeID = data.id;
        SelectThemes.clickViewID = data.viewId;
        SelectThemes.instance.selectedThemeID = data.id;
        NotesManager.instance.song.SetSelectedTheme(data.id);
        var unlockType = TrackingSelectTheme.GetThemeUnlockParam(ThemeManager.instance.GetThemeConfig(data.id));
        TrackingSelectTheme.LogEventEquip(data.id, SelectThemeParam.see_all, unlockType);
        SelectThemes.instance.LoadTheme(SelectThemes.instance.selectedThemeID, false);
    }

    private void OnClickBtnAdBuy() {
        SelectThemes.instance.clickThemeID = data.id;
        SelectThemes.clickViewID = data.viewId;
        SoundManager.PlayGameButton();
        AdsManager.instance.ShowRewardAds(VIDEOREWARD.item, null, location.ToString(), true, OnRewardUnlockTheme);
    }

    void OnRewardUnlockTheme(bool isCompleted) {
        if (isCompleted) {
            UnlockThemeByAd();
            AirfluxTracker.TrackRewardAdsImpression();
        }
    }

    private void UnlockThemeByAd() {
        int showAdOfTheme = ThemeManager.GetShowAdOfTheme(data.id) + 1;
        ThemeManager.SetShowAdOfTheme(data.id, showAdOfTheme);
        ThemeConfig themeConfig = ThemeManager.instance.GetThemeConfig(data.id);
        bool isCanGetReward = showAdOfTheme >= themeConfig.ads;
        if (isCanGetReward) {
            UnlockTheme();
            var unlockType = TrackingSelectTheme.GetThemeUnlockParam(ThemeManager.instance.GetThemeConfig(data.id));
            TrackingSelectTheme.LogEventEquip(data.id, SelectThemeParam.see_all, unlockType);
            SelectThemes.instance.LoadTheme(SelectThemes.instance.selectedThemeID, false);
        } else {
            if (themeConfig.unlockType == UnlockType.Video) {
                if (themeConfig.ads - showAdOfTheme == 1) {
                    txtShowAd.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                } else {
                    txtShowAd.text = showAdOfTheme + "/" + themeConfig.ads;
                }
            } else if (themeConfig.unlockType == UnlockType.Lock) {
                if (themeConfig.ads - showAdOfTheme == 1) {
                    txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                } else {
                    txtUnlockVideo.text = showAdOfTheme + "/" + themeConfig.ads;
                }
            }
        }
    }

    private void UnlockTheme() {
        ThemeManager.instance.SetOpenTheme(data.id, 0);
        SelectThemes.instance.selectedThemeID = data.id;
        NotesManager.instance.song.SetSelectedTheme(data.id);
    }

    private void OnClickBtnDiamondBuy() {
        SoundManager.PlayGameButton();
        SelectThemes.instance.clickThemeID = data.id;
        SelectThemes.clickViewID = data.viewId;
        if (Configuration.instance.GetDiamonds() >= _priceTheme) {
            UnlockTheme();
            var unlockType = TrackingSelectTheme.GetThemeUnlockParam(ThemeManager.instance.GetThemeConfig(data.id));
            TrackingSelectTheme.LogEventEquip(data.id, SelectThemeParam.see_all, unlockType);
            SelectThemes.instance.LoadTheme(SelectThemes.instance.selectedThemeID, false);
        } else {
            DisableCallbackUI popupNeedMore =
                Util.ShowNeedMore(LOCATION_NAME.unlock_theme, _priceTheme,
                    VIDEOREWARD.item, default, true);
            if (popupNeedMore) {
                // ShowPreviewTheme(false);
                // popupNeedMore.Show(() => { ShowPreviewTheme(true); });
            }

            var clickThemeID = SelectThemes.instance.clickThemeID;
            ThemeConfig themeConfig = ThemeManager.instance.GetThemeConfig(clickThemeID);
            Dictionary<string, object> param = new Dictionary<string, object> {
                { "item_id", clickThemeID },
                { "unlock_type", themeConfig.unlockType },
                { "diamond_price", themeConfig.diamond },
                { "unlock_status", "fail" },
            };
            //AnalyticHelper.ShopThemeItemBuy(param);
        }
    }

    private void OnClickBtnVip() {
        SelectThemes.instance.clickThemeID = data.id;
        SelectThemes.clickViewID = data.viewId;
        SelectThemes.instance.btnVipBuyOnClick();
    }

    void OnBuySubs(bool isSuccess) {
        if (isSuccess && SelectThemes.instance.clickThemeID == data.id) {
            ThemeConfig themeConfig = ThemeManager.instance.GetThemeConfig(SelectThemes.instance.clickThemeID);

            if (themeConfig == null) {
                return;
            }

            if (themeConfig.unlockType == UnlockType.Vip || themeConfig.unlockType == UnlockType.VipMission) {
                SelectThemes.instance.selectedThemeID = data.id;
                NotesManager.instance.song.SetSelectedTheme(data.id);
                var unlockType = TrackingSelectTheme.GetThemeUnlockParam(ThemeManager.instance.GetThemeConfig(data.id));
                TrackingSelectTheme.LogEventEquip(data.id, SelectThemeParam.see_all, unlockType);
                SelectThemes.instance.LoadTheme(SelectThemes.instance.selectedThemeID, false);
            }
        }
    }

    private void OnClickBtnEvent() {
        SelectThemes.instance.btnEventOnClick();
    }

    public override void Init(ThemeData themeData, int selectedID, int clickThemeID, int clickViewID, Action<ThemeItem> onSelect) {
        base.Init(themeData, selectedID, clickThemeID, clickViewID, onSelect);
        _selectedThemeID = selectedID;

        LocalizationManager.instance.UpdateFont(txtCost);
        LocalizationManager.instance.UpdateFont(txtLabelVip);
        LocalizationManager.instance.UpdateFont(txtShowAd);
        LocalizationManager.instance.UpdateFont(txtUnlockDiamond);
        LocalizationManager.instance.UpdateFont(txtUnlockVideo);

        var themeConfig = ThemeManager.instance.GetThemeConfig(data.id);
        if (themeConfig != null) {
            themeTitle.text = themeConfig.name.ToUpper();
        }
        UpdateItemState();
    }

    private void OnClickItemPreview(int id) {
        SetClicked(data.id == id);
    }

    public override void UpdateScaleSelected(int clickID) {
    }

    private void UpdateItemState() {
        HideAllButton();

        if (data == null) {
            return;
        }

        bool isTrialDefaultThemeSelected = IsDefaultTheme() && data.viewId < 0;
        bool isOpenTheme = data.IsOpenTheme();
        
        objSelected.SetActive(isOpenTheme);
        
        starJourneyHint.gameObject.SetActive(false);
        
        if (isOpenTheme || isTrialDefaultThemeSelected) {
            bool isEquipped = data.id == _selectedThemeID;
            btnEquip.gameObject.SetActive(!isEquipped);
        } else { //lock
            ThemeConfig themeConfig = ThemeManager.instance.GetThemeConfig(data.id);
            UnlockType unlockType = data.GetUnlockType();

            switch (unlockType) {
                case UnlockType.Video:
                    int adOfTheme = ThemeManager.GetShowAdOfTheme(data.id);
                    if (themeConfig.ads - adOfTheme == 1) {
                        txtShowAd.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                    } else {
                        txtShowAd.text = adOfTheme + "/" + themeConfig.ads;
                    }

                    btnAdBuy.gameObject.SetActive(true);
                    break;

                case UnlockType.Vip:
                case UnlockType.VipMission:
                    btnVipBuy.gameObject.SetActive(true);
                    string textVip = "TRY_NOW";
                    txtLabelVip.text = LocalizationManager.instance.GetLocalizedValue(textVip);
                    break;

                case UnlockType.Diamond:
                    btnCoinBuy.gameObject.SetActive(true);

                    _priceTheme = GetPriceTheme(themeConfig);
                    txtCost.text = _priceTheme.ToString();
                    UpdateUIButtonDiamond(Configuration.instance.GetDiamonds());
                    break;

                case UnlockType.Lock:
                    if (SubscriptionController.IsSubscriptionVip()) { // VIP thì không hiển thị buy bằng ads nữa
                        btnCoinBuy.gameObject.SetActive(true);

                        _priceTheme = GetPriceTheme(themeConfig);
                        txtCost.text = _priceTheme.ToString();
                    } else {
                        groupUnlockMixed.SetActive(true);

                        _priceTheme = GetPriceTheme(themeConfig);
                        txtUnlockDiamond.text = _priceTheme.ToString();

                        if (themeConfig != null) {
                            int adOfTheme2 = ThemeManager.GetShowAdOfTheme(data.id);
                            if (themeConfig.ads - adOfTheme2 == 1) {
                                txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                            } else {
                                txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD") +
                                                      "\n" + adOfTheme2 + "/" + themeConfig.ads;
                            }
                        } else {
                            txtUnlockVideo.text = LocalizationManager.instance.GetLocalizedValue("WATCH_AD");
                        }

                        UpdateUIButtonDiamond(Configuration.instance.GetDiamonds());
                    }

                    break;

                case UnlockType.Event:
                    if (LiveEventManager.instance != null && LiveEventManager.instance.IsActiveEvent) {
                        btnEvent.gameObject.SetActive(true);
                    }
                    break;

                case UnlockType.Progression:
                    btnProgress.gameObject.SetActive(true);
                    break;
                
                case UnlockType.Star:
                    starsRequireInfo.SetActive(true);
                    starsRequireText.text = themeConfig.unlockValue.ToString();
                    break;
                
                case UnlockType.StarJourney:
                    starJourneyHint.gameObject.SetActive(true);
                    var sjRewardThemes = StarsJourneyManager.instanceSafe.rewardThemes;
                    if (sjRewardThemes != null && sjRewardThemes.TryGetValue(data.id, out int level)) {
                        starJourneyHint.SetValue(level);
                    } else {
                        starJourneyHint.gameObject.SetActive(false);
                    }
                    break;
                
                case UnlockType.MilestoneEvent:
                    unlockViaEventLabel.SetActive(true);
                    break;
            }
        }
    }

    private void HideAllButton() {
        btnEquip.gameObject.SetActive(false);
        btnAdBuy.gameObject.SetActive(false);
        btnVipBuy.gameObject.SetActive(false);
        btnCoinBuy.gameObject.SetActive(false);
        btnEvent.gameObject.SetActive(false);
        groupUnlockMixed.SetActive(false);
        btnProgress.gameObject.SetActive(false);
        starsRequireInfo.SetActive(false);
        unlockViaEventLabel.SetActive(false);
    }

    private int GetPriceTheme(ThemeConfig themeConfig) {
        int priceTheme = themeConfig?.diamond ?? 999;
        return priceTheme;
    }

    private void UpdateUIButtonDiamond(int amount) {
        bool canBuy = amount >= _priceTheme;
        if (btnCoinBuy.gameObject.activeInHierarchy) {
            btnCoinBuy.image.color =
                canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;
        }

        if (btnUnlockDiamond.gameObject.activeInHierarchy) {
            btnUnlockDiamond.image.color =
                canBuy ? GameConstant._buttonDiamondEnableColor : GameConstant._buttonDiamondDisableColor;
        }
    }
}