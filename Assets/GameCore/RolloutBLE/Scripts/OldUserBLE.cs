using System;
using UnityEngine;

/// <summary>
/// Special treatment to old users who engage to Booster and Live Events features 
/// </summary>
public class OldUserBLE {
    public static (bool showPopup, bool rewardBall) CheckBalanceOldUser() {
        if (!RemoteConfigBase.instance.OldUserBLE_Enable || IsApplied()) {
            return (false, false);
        }

        if (!IsOldUser()) {
            ApplyConfig(1);
            return (false, false);
        }

        string marks, targets;
        if (IsFreeUser()) {
            marks = RemoteConfigBase.instance.OldUserBLE_GemsMark_FreeUser;
            targets = RemoteConfigBase.instance.OldUserBLE_GemsTarget_FreeUser;
        } else {
            marks = RemoteConfigBase.instance.OldUserBLE_GemsMark_PaidUser;
            targets = RemoteConfigBase.instance.OldUserBLE_GemsTarget_PaidUser;
        }

        int beforeDiamond = Configuration.instance.GetDiamonds();
        bool isRewardBall = BalanceGemWalletAndDecideReward(marks, targets);
        int afterDiamond = Configuration.instance.GetDiamonds();
        int totalStar = Configuration.instance.GetCurrentStars();
        RolloutBLETracking.Track_GemWalletBalance(beforeDiamond, afterDiamond, totalStar);
        return (true, isRewardBall || !IsFreeUser());
    }

    private static bool BalanceGemWalletAndDecideReward(string marks, string targets) {
        var (count, markRanges) = StringExtension.SplitStringZeroAllocation(marks);
        var (targetsCount, targetsRanges) = StringExtension.SplitStringZeroAllocation(targets);

        if (targetsCount <= count) {
            return false;
        }

        bool isReward;

        for (int i = 0; i < count; i++) {
            var (offset, length) = markRanges[i];
            var (targetOffset, targetLength) = targetsRanges[i];
            string mark = marks[offset..length];
            string target = targets[targetOffset..targetLength];

            if (!int.TryParse(mark, out int markValue)) {
                continue;
            }

            if (Configuration.instance.GetDiamonds() < markValue) {
                if (target.Equals("_", StringComparison.Ordinal)) {
                    return false;
                }

                if (!int.TryParse(target, out int targetValue)) {
                    continue;
                }

                isReward = Configuration.instance.GetDiamonds() > targetValue;
                Configuration.instance.SetDiamonds(targetValue);
                return isReward;
            }
        }

        var (lastOffset, lastLength) = targetsRanges[count];
        string lastTarget = targets[lastOffset..lastLength];
        if (lastTarget.Equals("_", StringComparison.Ordinal)) {
            return false;
        }

        if (int.TryParse(lastTarget, out int lastTargetValue)) {
            int lastDiamond = Configuration.instance.GetDiamonds();
            isReward = lastDiamond > lastTargetValue;
            Configuration.instance.SetDiamonds(lastTargetValue);

            return isReward;
        }

        return false;
    }

    private static bool IsOldUser() {
        return Configuration.instance.GetCurrentStars() != 0;
    }

    public static int GetBallIdReward() {
        int[] poolBall = RemoteConfigBase.instance.OldUserBLE_PoolBallReward;
        if (!poolBall.IsNullOrEmpty()) {
            poolBall.Shuffle();
            foreach (int ball in poolBall) {
                if (BallManager.CanEarnBall(ball))
                    return ball;
            }
        }

        int defaultReward = RemoteConfigBase.instance.OldUserBLE_DefaultBallReward;
        if (BallManager.CanEarnBall(defaultReward))
            return defaultReward;

        return -1;
    }

    /// <summary>
    /// check if bonus for old user
    /// </summary>
    /// <returns></returns>
    public static bool IsApplied() {
        return PlayerPrefs.GetInt("apply_ble_olduser", 0) != 0;
    }

    /// <summary>
    /// check if bonus for old user
    /// </summary>
    /// <returns></returns>
    public static void ApplyConfig(int value) {
        PlayerPrefs.SetInt("apply_ble_olduser", value);

    }

    /// <summary>
    /// Check if user subscribed
    /// </summary>
    /// <returns></returns>
    public static bool IsSubUser() {
        return SubscriptionController.IsSubscriptionVip();
    }

    /// <summary>
    /// Check if free user
    /// </summary>
    /// <returns></returns>
    public static bool IsFreeUser() {
        if (IsSubUser())
            return false;

        return AnalyticHelper.CountEvent(TRACK_NAME.iapshop_success) == 0;
    }

    /// <summary>
    /// Check if user qualified reward
    /// </summary>
    /// <returns></returns>
    public static bool IsQualifiedReward(int amount) {
        if (IsSubUser())
            return true;

        return Configuration.instance.GetDiamonds() < amount;
    }
}