using System;
using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.PlaytimeReward {
    [RequireComponent(typeof(Button))]
    public class UIButtonPlaytimeReward : MonoBehaviour {
        [SerializeField] private GameObject objBadge;
        [SerializeField] private Image      imgProgress;
        [SerializeField] private Transform  circleLightPointer;
        [SerializeField] private GameObject toolTip;


        private const float MinRatio = 0.128f;
        private const float MaxRatio = 0.872f;

        private const int MinAngle = 134;
        private const int MaxAngle = -134;

        private Transform _rootParent;
        private int       _rootIndex;
        private Button    btnReward;

        private bool _isShowingTooltip;

        #region Unity Medthods

        private void Start() {
            btnReward = GetComponent<Button>();
            btnReward.onClick.AddListener(OnBtnTimeplayRewardClick);
        }

        private void OnEnable() {
            if (!PlaytimeReward.instance.IsActive) {
                this.gameObject.SetActive(false);
                return;
            }
            imgProgress.fillAmount = MinRatio;
            circleLightPointer.localRotation = Quaternion.Euler(0, 0, MinAngle);

            PlaytimeReward.OnChangeValueReward += PlaytimeRewardOnOnChangeValueReward;

            var rewardData = PlaytimeReward.instance.GetCurrentReward();
            PlaytimeRewardOnOnChangeValueReward(rewardData);
        }

        private void OnDisable() {
            PlaytimeReward.OnChangeValueReward -= PlaytimeRewardOnOnChangeValueReward;
        }

        #endregion

        private void PlaytimeRewardOnOnChangeValueReward(RewardData reward) {
            bool isFull = reward.amount >= reward.max;
            objBadge.SetActive(isFull);
            float ratio = isFull ? 1f : reward.ratio;
            if (ratio < 0.05f) {
                imgProgress.fillAmount = 0f;
                circleLightPointer.gameObject.SetActive(false);
            } else {
                circleLightPointer.gameObject.SetActive(true);
                imgProgress.fillAmount = MinRatio + (MaxRatio - MinRatio) * ratio;
                circleLightPointer.localRotation = Quaternion.Euler(0, 0, MinAngle + (MaxAngle - MinAngle) * ratio);
            }
        }

        #region Tooltip

        public void ShowToolTip() {
            StartCoroutine(IEShowToolTip());
        }

        private IEnumerator IEShowToolTip() {
            yield return new WaitForSeconds(0.2f);
            HomeManager.instance.blackBG.SetActive(true);
            toolTip.SetActive(true);
            _rootParent = transform.parent;
            _rootIndex = transform.GetSiblingIndex();
            this.transform.SetParent(HomeManager.instance.blackBG.transform, true);
            toolTip.transform.SetParent(HomeManager.instance.blackBG.transform, true);
            _isShowingTooltip = true;
            PlaytimeReward.instance.IsShownTooltipButton = true;
            PlaytimeRewardTracker.KnobOnboardingShow(OnboardingStep.Button_At_Home);
            BlackBGTooltip.OnClickAction += BlackBgTooltipOnOnClick;
        }

        private void BlackBgTooltipOnOnClick() {
            HideToolTip();
        }

        private bool HideToolTip(bool isTrackingClose = true) {
            if (_isShowingTooltip) {
                _isShowingTooltip = false;
                HomeManager.instance.blackBG.SetActive(false);
                toolTip.transform.SetParent(this.transform, true);
                toolTip.SetActive(false);
                this.transform.SetParent(_rootParent, true);
                this.transform.SetSiblingIndex(_rootIndex);
                if (isTrackingClose) {
                    PlaytimeRewardTracker.KnobOnboardingClose(OnboardingStep.Button_At_Home);
                }

                return true;
            }

            return false;
        }

        #endregion

        #region Button Action

        private void OnBtnTimeplayRewardClick() {
            SoundManager.PlayGameButton();
            AnalyticHelper.Button_Click(BUTTON_NAME.PlaytimeReward);
            if (HideToolTip(isTrackingClose: false)) {
                PlaytimeRewardTracker.KnobOnboardingClick(OnboardingStep.Button_At_Home);
            }

            Util.ShowPopUp(PopupName.PlaytimeReward);
            var data = PlaytimeReward.instance.GetCurrentReward();
            PlaytimeRewardTracker.KnobEnter(LOCATION_NAME.home.ToString(), Configuration.instance.GetDiamonds(),
                data.ratio * 100, data.amount);
        }

        #endregion
    }
}