using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class RateUsToast : MonoBehaviour {
    [SerializeField] private Image imgPopup;
    [SerializeField] private Image imgIcon;
    [SerializeField] private Text  txtThanks;
    [SerializeField] private float timeAnim   = 0.2f;
    [SerializeField] private float timeAppear = 1.5f;
    private Sequence _fadeSequence;

    private void Start() {
        var color = imgPopup.color;
        color.a = 0;
        imgPopup.color = color;
        color = imgIcon.color;
        color.a = 0;
        imgIcon.color = color;
        color = txtThanks.color;
        color.a = 0;
        txtThanks.color = color;

        _fadeSequence = DOTween.Sequence();
        _fadeSequence.Append(imgPopup.DOFade(1f, timeAnim));
        _fadeSequence.Join(imgIcon.DOFade(1f, timeAnim));
        _fadeSequence.Join(txtThanks.DOFade(1f, timeAnim));
        _fadeSequence.AppendInterval(timeAppear);
        _fadeSequence.Append(imgPopup.DOFade(0f, timeAnim));
        _fadeSequence.Join(imgIcon.DOFade(0f, timeAnim));
        _fadeSequence.Join(txtThanks.DOFade(0f, timeAnim));
        _fadeSequence.OnComplete(() => {
            if (this != null && gameObject != null) {
                Destroy(this.gameObject);
            }
        });
    }

    private void OnDestroy() {
        if (_fadeSequence != null && _fadeSequence.IsActive()) {
            _fadeSequence.Kill();
        }
    }
}