using System;
using System.Collections.Generic;
using Com.TheFallenGames.OSA.Core.CustomScroller.SongScroller;
using UnityEngine;
using UnityEngine.UI;

public class UIMIDIMultipleVersion : PopupUI {
    [SerializeField] private Text     _txtSongName;
    [SerializeField] private Text     _txtArtist;
    [SerializeField] private Text     _txtAcmId;
    [SerializeField] private Dropdown _drdSelectLevelDesign;
    [SerializeField] private Text     _txtTag;
    [SerializeField] private Text     _txtForceFollowMidi;
    [SerializeField] private Button   _btnPlay;
    [SerializeField] private Button   _btnCancel;

    private List<MidiMultipleItem> _slotIds;

    private Action<string, bool> _onBtnPlayClicked;
    private string               _location = LOCATION_NAME.MidiMultipleVersion.ToString();

    private void Awake() {
        _btnPlay.onClick.AddListener(OnBtnPlayClicked);
        _btnCancel.onClick.AddListener(OnBtnCancelClicked);
        _drdSelectLevelDesign.onValueChanged.AddListener(OnChangeValueLevelDesign);
    }

    /// <summary>
    /// Load information of the current song.
    /// Formerly Show(), renamed to avoid confusion with base class' Show()
    /// </summary>
    public void FetchData(string acmIdv3, List<MidiMultipleItem> slotIds, Action<string, bool> onBtnPlayClicked) {
        _onBtnPlayClicked = onBtnPlayClicked;
        _slotIds = slotIds;

        _drdSelectLevelDesign.ClearOptions();

        var song = SongManager.instance.GetSongByAcmId(acmIdv3);
        if (song != null) {
            _txtSongName.text = song.name;
            _txtArtist.text = song.artist;
        }


        _txtAcmId.text = acmIdv3;

        for (int i = 0; i < slotIds.Count; i++) {
            Dropdown.OptionData optionData = new Dropdown.OptionData {
                text = slotIds[i].slotID
            };

            _drdSelectLevelDesign.options.Add(optionData);
        }

        _drdSelectLevelDesign.value = 0;
        _drdSelectLevelDesign.RefreshShownValue();
        UpdateTextByOption();
    }

    private void OnBtnCancelClicked() {
        Close();
    }


    private void OnChangeValueLevelDesign(int arg0) {
        UpdateTextByOption();
    }

    private void UpdateTextByOption() {
        _txtTag.text = _slotIds[_drdSelectLevelDesign.value].VersionTag;
        _txtForceFollowMidi.text = _slotIds[_drdSelectLevelDesign.value].MidiControl ? "TRUE" : "FALSE";
    }

    private void OnBtnPlayClicked() {
        string levelDesignContentId = string.Empty;
        bool isForceUseMidi = false;
        if (_drdSelectLevelDesign.options.Count > 0) {
            levelDesignContentId = _slotIds[_drdSelectLevelDesign.value].slotID;
            isForceUseMidi = _slotIds[_drdSelectLevelDesign.value].MidiControl;
        }

        _onBtnPlayClicked?.Invoke(levelDesignContentId, isForceUseMidi);
    }
}