using System.Collections.Generic;
using System.IO;
using Spine;
using Spine.Unity;
using UnityEngine;
using Event = Spine.Event;

[RequireComponent(typeof(AudioSource))]
public class SkeletonVoicePlayer : MonoBehaviour {
    [SerializeField] private SkeletonGraphic skeletonGraphic;

    private AudioSource                   _audioSource;
    private Dictionary<string, AudioClip> _loadedAudioClip;

    private void Awake() {
        skeletonGraphic.AnimationState.Event += HandleSkeletonEvents;
        _audioSource = GetComponent<AudioSource>();
    }

    private void OnEnable() {
        if (_audioSource) {
            _audioSource.enabled = Configuration.instance.SoundIsOn();
        }
    }

    private void HandleSkeletonEvents(TrackEntry trackentry, Event e) {
        string audioPath = e.Data.AudioPath;

        if (string.IsNullOrEmpty(audioPath)) {
            return;
        }

        // remove file extension
        audioPath = audioPath.Remove(audioPath.LastIndexOf('.'));
        _loadedAudioClip ??= new Dictionary<string, AudioClip>();

        if (_loadedAudioClip.ContainsKey(audioPath)) {
            _audioSource.PlayOneShot(_loadedAudioClip[audioPath]);
        } else {
            var audioClip = Resources.Load<AudioClip>(audioPath);
            if (audioClip != null) {
                _loadedAudioClip[audioPath] = audioClip;
                _audioSource.PlayOneShot(audioClip);
            }
        }
    }
}