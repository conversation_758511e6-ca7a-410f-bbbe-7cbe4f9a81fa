using TilesHop.EconomySystem;
using UnityEngine;
using UnityEngine.UI;

// ReSharper disable once InconsistentNaming
public class IAP_HalloweenPackShopItem : IAP_HalloweenPackCaller {
    [SerializeField] private Button               btnExplore;
    [SerializeField] private IapSeasonalBuyButton btnIapBuy;
    [SerializeField] private SeasonalAdsBuyButton btnAdsBuy;
    [Space]
    [SerializeField] protected Button btnInfo;

    protected override void Start() {
        base.Start();
        if (btnInfo) {
            btnInfo.onClick.AddListener(ShowInfoPanel);
        }

        if (btnExplore) {
            btnExplore.onClick.AddListener(OnClick);
        }
    }

    protected override void OnEnable() {
        base.OnEnable();
        UpdateButtons();
        btnAdsBuy.SetLocation(location);
        btnIapBuy.SetLocation(location);
    }

    protected override void HandleEventContinue() {
        base.HandleEventContinue();
        UpdateButtons();
    }

    protected void UpdateButtons() {
        (bool iapIsEnable, bool adsIsEnable) = SeasonalPackManager.GetBuyButtonsState();
        if (iapIsEnable && adsIsEnable) {
            btnExplore.gameObject.SetActive(true);
            btnAdsBuy.gameObject.SetActive(false);
            btnIapBuy.gameObject.SetActive(false);
        } else {
            btnExplore.gameObject.SetActive(false);
            btnAdsBuy.gameObject.SetActive(adsIsEnable);
            btnIapBuy.gameObject.SetActive(iapIsEnable);
        }
    }
    
    public void ShowInfoPanel() {
        IAPHalloweenPackPopup.NavigateToInfoPanel(location);
    }
}
