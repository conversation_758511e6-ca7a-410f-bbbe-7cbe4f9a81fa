using System;
using System.Collections;
using System.Collections.Generic;
using System.Net.Http;
using System.Linq;
using Balancy;
using Balancy.API.Payments;
using Balancy.Data.SmartObjects;
using Balancy.Models;
using Balancy.Models.SmartObjects;
using GameCore.EndlessOffer;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using TilesHop.Cores.Hybrid.TripleOffer;
using TilesHop.Cores.Pooling;
using UnityEngine;

namespace GameCore.BHBalancy {
    public class OfferGroupManager : MonoBehaviour {
        private const string activeOfferList = "activeOffers";

        [ShowInInspector]
        private Dictionary<int, OfferGroupInfo> offerGroupInfos = new Dictionary<int, OfferGroupInfo>();

        [ShowInInspector] private List<GameOfferGroup> activeGroups = new List<GameOfferGroup>();
        public                    GameEventsManager    _gameEventsManager;

        private List<int> _activeOfferIds = new();

        public List<GameOfferGroup> ActiveGroup => activeGroups;

        private void OnEnable() {
            SmartObjectsEvents.NewOfferGroupActivated += OnNewOfferGroupActivated;
            SmartObjectsEvents.OfferGroupDeactivated += OnOfferGroupDeactivated;
            SmartObjectsEvents.SmartObjectsInitialized += OnSmartObjectsInitialized;
        }

        private void OnDisable() {
            SmartObjectsEvents.NewOfferGroupActivated -= OnNewOfferGroupActivated;
            SmartObjectsEvents.OfferGroupDeactivated -= OnOfferGroupDeactivated;
            SmartObjectsEvents.SmartObjectsInitialized -= OnSmartObjectsInitialized;
        }

        private void OnSmartObjectsInitialized() {
            StartCoroutine(IEPreInit());
        }

        public void SetComponent(GameEventsManager gameEventsManager) {
            _gameEventsManager = gameEventsManager;
        }

        private void OnNewOfferGroupActivated(OfferGroupInfo offerInfo) {
            Init();
        }

        /// <summary>
        /// Get all active offers từ balancy và active từng cái 1
        /// </summary>
        /// <returns></returns>
        private IEnumerator IEPreInit() {
            LoadLastActiveOffers();
            if (_gameEventsManager != null) {
                while (!_gameEventsManager.initDone) {
                    Logger.EditorLog(BalancyManager.KeyFeature, "Wait _gameEventsManager init done!");
                    yield return YieldPool.GetWaitForEndOfFrame();
        }
            }

            Init();
        }

        private void Init() {
            Logger.EditorLog(BalancyManager.KeyFeature, "Initializing OfferGroup");
            var groups = LiveOps.GameOffers.GetActiveOfferGroups().ToList();
            if (!groups.IsNullOrEmpty()) {
                List<OfferGroupInfo> sortedList = SortOffers(groups);
                foreach (var group in sortedList) {
                    Logger.EditorLog(BalancyManager.KeyFeature, $"[Group Offer] Active {group.GameOfferGroup.IntUnnyId} {group.GameOfferGroup.Name}");
                    ActiveOfferGroup(group);
                }
            }
        }

        /// <summary>
        /// Thực hiện sắp xếp các offers theo thứ tự ưu tiên:
        /// 1. Ưu tiên các offer đang active
        /// 2. Ưu tiên các offer có game event priority cao hơn
        /// 3. default
        /// </summary>
        /// <param name="groups"> Danh sách các offers đang active</param>
        /// <returns> Danh sách đã đuợc sắp xếp lại</returns>
        private List<OfferGroupInfo> SortOffers(List<OfferGroupInfo> groups) {
            List<OfferGroupInfo> sortedList = new List<OfferGroupInfo>();

            // 1. Ưu tiên các offer đang active
            foreach (OfferGroupInfo group in groups) {
                if (_activeOfferIds.Contains(group.GameOfferGroupIntUnnyId)) {
                    sortedList.Add(group);
                }
            }

            // 2. Ưu tiên các offer có game event priority cao hơn
            List<int> activeEventIds = _gameEventsManager?.GetOfferGroupsByPriority();
            if (activeEventIds != null) {
                foreach (OfferGroupInfo group in groups) {
                    if (sortedList.Contains(group))
                        continue;

                    if (activeEventIds.Contains(group.GameEventIntUnnyId)) {
                        sortedList.Add(group);
                    }
                }
            }

            // 3. default
            foreach (OfferGroupInfo group in groups) {
                if (!sortedList.Contains(group)) {
                    sortedList.Add(group);
                }
            }

            return sortedList;
        }

        private void LoadLastActiveOffers() {
            string lastOfferList = PlayerPrefs.GetString(activeOfferList, string.Empty);
            if (!string.IsNullOrEmpty(lastOfferList)) {
                string[] lastOfferArray = lastOfferList.Split(";", StringSplitOptions.RemoveEmptyEntries);
                foreach (string str in lastOfferArray) {
                    if (int.TryParse(str, out int offer)) {
                        _activeOfferIds.Add(offer);
                    }
                }
            }
        }

        private void SaveLastActiveOffer() {
            PlayerPrefs.SetString(activeOfferList,
                _activeOfferIds.IsNullOrEmpty() ? string.Empty : string.Join(";", _activeOfferIds));
        }

        private void ActiveOfferGroup(OfferGroupInfo offerInfo) {
            if (!offerGroupInfos.TryAdd(offerInfo.GameOfferGroupIntUnnyId, offerInfo)) {
                return;
            }

            ActiveOfferGroup(offerInfo.GameOfferGroupIntUnnyId, offerInfo.GameOfferGroup);
        }

        private void ActiveOfferGroup(int id, GameOfferGroup group) {
            Logger.EditorLog(BalancyManager.KeyFeature,
                $"[Group Offer] Call active group: {group.IntUnnyId} {group.Name}");
            activeGroups.Add(group);
            switch (group.Type) {
                case OfferGroupType.OnePurchase:
                    if (IAPTripleOffers.IsActive) {
                        break;
                    }

                    IAPTripleOffersConfig configTripleOffers = ProcessTripleOffersConfig(offerGroupInfos[id]);
                    int remainTimeTriple = GetRemainTime(offerGroupInfos[id]);
                    IAPTripleOffers.instanceSafe.InitByBalancy(id, configTripleOffers, remainTimeTriple,
                        PurchaseTripleOffer);
                    break;

                case OfferGroupType.ChainDeals:
                    if (EndlessOffer.EndlessOffer.IsActive) {
                        break;
                    }

                    EndlessOfferConfig configEndlessOffer = ProcessEndlessOffersConfig(offerGroupInfos[id]);
                    int remainTimeEndless = GetRemainTime(offerGroupInfos[id]);
                    EndlessOffer.EndlessOffer.instanceSafe.Init(id, configEndlessOffer, remainTimeEndless,
                        out bool newActive, out bool endActive, PurchaseOffer);
                    if (newActive) {
                        if (!_activeOfferIds.Contains(id)) {
                            _activeOfferIds.Add(id);
                        }
                    }

                    if (endActive) {
                        if (_activeOfferIds.Contains(id)) {
                            _activeOfferIds.Remove(id);
                        }
                    }

                    SaveLastActiveOffer();
                    break;
            }
        }

        private void OnOfferGroupDeactivated(OfferGroupInfo offerInfo, bool wasPurchased) {
            offerGroupInfos.Remove(offerInfo.GameOfferGroupIntUnnyId);
            DeActiveOfferGroup(offerInfo.GameOfferGroup, wasPurchased);
        }

        private void DeActiveOfferGroup(GameOfferGroup group, bool wasPurchased) {
            if (group == null)
                return;

            Logger.EditorLog(BalancyManager.KeyFeature,
                $"[Group Offer] Call DeActive group: {group.IntUnnyId} {group.Name}");
            activeGroups?.Remove(group);
            switch (group.Type) {
                case OfferGroupType.OnePurchase:
                    if (wasPurchased) {
                        IAPTripleOffers.instanceSafe.BoughtFeature();
                    } else {
                        IAPTripleOffers.instanceSafe.CloseFeature();
                    }

                    break;

                case OfferGroupType.ChainDeals:
                    break;
            }
        }

        private IAPTripleOffersConfig ProcessTripleOffersConfig(OfferGroupInfo offerGroupInfo) {
            GameOfferGroup group = offerGroupInfo.GameOfferGroup;
            if (group == null) {
                return null;
            }

            if (group.StoreItems.IsNullOrEmpty()) {
                return null;
            }

            if (group.StoreItems.Length < 4)
                return null;

            IAPTripleOffersConfig config = new IAPTripleOffersConfig();
            config.activeTime = GetOfferDuration(offerGroupInfo);

            config.pack1 = new IAPInfor() {
                reward = new RewardDataStr(),
            };
            var priceInfo1 = ProcesssPriceInfo((StoreItemCustomize) group.StoreItems[0]);
            config.pack1.SetIAPInfo(priceInfo1.android, priceInfo1.ios, priceInfo1.price);
            config.pack1.AddRewards(ProcessRewards(group.StoreItems[0]));

            config.pack2 = new IAPInfor() {
                reward = new RewardDataStr(),
            };
            var priceInfo2 = ProcesssPriceInfo((StoreItemCustomize) group.StoreItems[1]);
            config.pack2.SetIAPInfo(priceInfo2.android, priceInfo2.ios, priceInfo2.price);
            config.pack2.AddRewards(ProcessRewards(group.StoreItems[1]));

            config.pack3 = new IAPInfor() {
                reward = new RewardDataStr(),
            };
            var priceInfo3 = ProcesssPriceInfo((StoreItemCustomize) group.StoreItems[2]);
            config.pack3.SetIAPInfo(priceInfo3.android, priceInfo3.ios, priceInfo3.price);
            config.pack3.AddRewards(ProcessRewards(group.StoreItems[2]));

            config.all = new IAPInfor() {
                reward = new RewardDataStr(),
            };
            var priceInfo4 = ProcesssPriceInfo((StoreItemCustomize) group.StoreItems[3]);
            config.all.SetIAPInfo(priceInfo4.android, priceInfo4.ios, priceInfo4.price);
            config.all.AddRewards(ProcessRewards(group.StoreItems[3]));

            return config;
        }

        private (string android, string ios, float price) ProcesssPriceInfo(StoreItemCustomize groupStoreItem) {
            string android = string.Empty;
            string ios = string.Empty;
            float price = 0;
            if (groupStoreItem.Price?.Product != null) {
                android = groupStoreItem.Price.Product.ProductId;
                ios = groupStoreItem.Price.Product.ProductId;
                price = groupStoreItem.Price.Product.Price;
            }

            if (groupStoreItem.IOSPackageID is {ProductId: not null, Price: > 0}) {
                ios = groupStoreItem.IOSPackageID.ProductId;
                price = groupStoreItem.IOSPackageID.Price;
                if (string.IsNullOrEmpty(android)) {
                    android = ios;
                }
            }

            return (android, ios, price);
        }

        private EndlessOfferConfig ProcessEndlessOffersConfig(OfferGroupInfo offerGroupInfo) {
            GroupOfferCustomize group = (GroupOfferCustomize) offerGroupInfo.GameOfferGroup;
            EndlessOfferConfig config = new EndlessOfferConfig();
            config.duration = GetOfferDuration(offerGroupInfo);
            config.packs = new EndlessOfferPack[group.StoreItems.Length];
            for (int i = 0; i < group.StoreItems.Length; i++) {
                var storeItem = group.StoreItems[i];
                var offerPack = new EndlessOfferPack();
                offerPack.id = i;

                var productInfo = ProcesssPriceInfo((StoreItemCustomize) storeItem);
                var price = storeItem.Price;
                if (price.IsFree()) {
                    offerPack.claimType = "Free";
                } else if (price.IsAdsWatching()) {
                    offerPack.claimType = "Ads";
                    offerPack.adCosting = price.Ads;
                } else if (price.IsInApp()) {
                    offerPack.claimType = "IAP";
                    offerPack.productId = Inwave.Utils.IsAndroid() ? productInfo.android : productInfo.ios;
                }

                offerPack.rewards = ProcessRewards(storeItem);
                if (!offerPack.rewards.IsNullOrEmpty()) {
                    var token = offerPack.GetReward(RewardType.EndlessOfferToken);
                    if (token != null) {
                        offerPack.token += token.valueInt;
                        offerPack.RemoveReward(RewardType.EndlessOfferToken);
                    }
                }

                config.packs[i] = offerPack;
            }

            Balancy.Models.GroupOffer.EndlessOffer groupOfferType =
                (Balancy.Models.GroupOffer.EndlessOffer) (group).GroupOfferType;
            config.miniMilestoneConfig = new MiniMilestoneConfig();
            config.miniMilestoneConfig.milestones =
                new List<MiniMilestone>(capacity: groupOfferType.DataTokenMilestone.Length);
            foreach (var item in groupOfferType.DataTokenMilestone) {
                var miniMilestone = new MiniMilestone();
                miniMilestone.needToken = item.Milestone;
                miniMilestone.rewards = new List<RewardItemStr>(item.Reward.Length);
                foreach (var itemReward in item.Reward) {
                    var reward = BalancyConverter.ProcessRewardItem(itemReward);
                    miniMilestone.rewards.Add(reward);
                }

                config.miniMilestoneConfig.milestones.Add(miniMilestone);
            }

            return config;
        }

        private EventInfo GetGameEvent(int unnyIdGameEvent) {
            var gameEvents = LiveOps.GameEvents.GetActiveEvents();
            foreach (EventInfo eventInfo in gameEvents) {
                var gameEvent = eventInfo.GameEvent;
                if (gameEvent.IntUnnyId == unnyIdGameEvent) {
                    return eventInfo;
                }
            }

            return null;
        }

        private int GetOfferDuration(OfferGroupInfo offerGroupInfo) {
            if (offerGroupInfo.GameOfferGroup.Duration > 0)
                return offerGroupInfo.GameOfferGroup.Duration;

            var gameEvent = GetGameEvent(offerGroupInfo.GameEventIntUnnyId);
            if (gameEvent != null && gameEvent.GameEvent.FinishType == EventFinishType.Duration) {
                return gameEvent.GameEvent.Duration;
            }

            return 0;
        }

        private int GetRemainTime(OfferGroupInfo offerGroupInfo) {
            if (offerGroupInfo.GameOfferGroup.Duration > 0)
                return offerGroupInfo.GetSecondsLeftBeforeDeactivation();

            var gameEvent = GetGameEvent(offerGroupInfo.GameEventIntUnnyId);
            if (gameEvent != null && gameEvent.GameEvent.FinishType == EventFinishType.Duration) {
                return gameEvent.GameEvent.GetSecondsLeftBeforeDeactivation();
            }

            return int.MaxValue;
        }

        private void PurchaseTripleOffer(int idGroupOffer, IAPDefinitionId definitionId, PackType packType) {
            try {
                if (!offerGroupInfos.TryGetValue(idGroupOffer, out OfferGroupInfo groupInfo)) {
                    return;
                }

                var storeItem = groupInfo.GameOfferGroup.StoreItems[(int) packType];
                string productId = IapBase.GetProductID(definitionId);
                BalancyPurchase.PurchaseItemOffer(productId, storeItem, groupInfo);
            } catch (Exception e) { }
        }

        private void PurchaseOffer(int idGroupOffer, string productId) {
            try {
                if (!offerGroupInfos.TryGetValue(idGroupOffer, out OfferGroupInfo groupInfo)) {
                    return;
                }

                StoreItem storeItem = null;
                foreach (var item in groupInfo.GameOfferGroup.StoreItems) {
                    if (!item.IsInApp())
                        continue;

                    var productInfo = ProcesssPriceInfo((StoreItemCustomize) storeItem);
                    if (Inwave.Utils.IsAndroid() && productInfo.android.Equals(productId)) {
                        storeItem = item;
                        break;
                    }

                    if (!Inwave.Utils.IsAndroid() && productInfo.ios.Equals(productId)) {
                        storeItem = item;
                        break;
                    }

                }

                if (storeItem == null) {
                    return;
                }

                BalancyPurchase.PurchaseItemOffer(productId, storeItem, groupInfo);
            } catch (Exception e) { }
        }

        public OfferGroupInfo GetDataGroupOffer(int idGroupOffer) {
            return offerGroupInfos[idGroupOffer];
        }

        private List<RewardItemStr> ProcessRewards(StoreItem storeItem) {
            if (storeItem == null)
                return null;

            List<RewardItemStr> rewards = new List<RewardItemStr>();
            Dictionary<RewardType, int> extendRewards = new Dictionary<RewardType, int>();

            if (storeItem.Reward != null && storeItem.Reward.Items.Length > 0) {
                foreach (var item in storeItem.Reward.Items) {
                    var reward = BalancyConverter.ProcessRewardItem(item);
                    if (reward != null) {
                        var type = reward.ConvertToRewardItem().type;
                        if (type is RewardType.BallByAmount or RewardType.ThemeByAmount) {
                            if (extendRewards.ContainsKey(type)) {
                                extendRewards[type] += reward.valueInt;
                            } else {
                                extendRewards.Add(type, reward.valueInt);
                            }
                        } else {
                            rewards.Add(reward);
                        }
                    } else {
                        Logger.EditorLogError(BalancyManager.KeyFeature,
                            $"Cant convert to reward: {JsonConvert.SerializeObject(item)}");
                    }
                }
            }

            if (storeItem is StoreItemCustomize storeItemCustomize) {
                if (storeItemCustomize.ExtraRewardWithID is {Length: > 0}) {
                    foreach (var item in storeItemCustomize.ExtraRewardWithID) {
                        var reward = BalancyConverter.ProcessRewardItem(item);
                        if (reward != null) {
                            if (reward.ConvertToRewardItem().type == RewardType.Skin) {
                                //có pool
                                int ballAmount = GetExtendAmount(RewardType.BallByAmount);
                                for (int i = 0; i < ballAmount; i++) {
                                    rewards.Add(reward);
                                }
                            } else if (reward.ConvertToRewardItem().type == RewardType.Theme) {
                                int themeAmount = GetExtendAmount(RewardType.ThemeByAmount);
                                for (int i = 0; i < themeAmount; i++) {
                                    rewards.Add(reward);
                                }
                            } else {
                                rewards.Add(reward);
                            }
                        } else {
                            Logger.EditorLogError(BalancyManager.KeyFeature,
                                $"Cant convert to reward: {JsonConvert.SerializeObject(item)}");
                        }
                    }
                }
            }

            return rewards;

            int GetExtendAmount(RewardType type) {
                return extendRewards.GetValueOrDefault(type, 0);
            }
        }
    }
}