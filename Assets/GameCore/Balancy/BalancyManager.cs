using System;
using System.Linq;
using Balancy;
using Balancy.Data.SmartObjects;
using Balancy.Interfaces;
using Balancy.SmartObjects;
using GameCore.BHBalancy;
using UnityEngine;

public partial class BalancyManager : FastSingleton<BalancyManager> {
    public const string GAME_ID    = "8fa19b9c-e82b-11ef-bb95-1fec53a055ba"; //Tiles Hop (BH)
    public const string PUBLIC_KEY = "NzlmMjJiMDE4YmVjNDE4NjA4MTJiYj"; //Tiles Hop (BH)

    private const string SAVED_ENVIRONMENT = "balancy_environment";
    private const string SAVED_BRANCH      = "balancy_branch";
    public const  string KeyFeature        = "Balancy";

    public static bool isEnable = false;
    public static bool isInitialized { get; private set; } = false;
    public static bool SmartObjectsInitialized { get; private set; } = false;
    public static bool calledInit { get; private set; } = false;

    public static Action onInitCompleted;

    public Constants.Environment environment;
    public string                branch;

    private ISmartObjectsEvents _smartObjects;
    private IStoreEvents        _storeEvents;

    private GameEventsManager _gameEventsManager;
    private OffersManager     _offerManager;
    private OfferGroupManager _offerGroupManager;

    private Action<string> _callbackInit;

    private const string CAN_ACTIVE_OFFER_KEY = "can_active_offer";
    public static bool canActiveOffer {
        get {
            return PlayerPrefs.GetInt(CAN_ACTIVE_OFFER_KEY, 0) == 1;
        }
        set {
            if (value) {
                PlayerPrefs.SetInt(CAN_ACTIVE_OFFER_KEY, 1);
            }
        }
    }

    private void OnEnable() {
        SmartObjectsEvents.SmartObjectsInitialized += OnSmartObjectsInitialized;
    }

    private void OnSmartObjectsInitialized() {
        SmartObjectsInitialized = true;
    }

    public void Init() {
        calledInit = true;
        if (!RemoteConfigBase.instance.Balancy_enable) {
            return;
        }

        isEnable = true;
        SetupSmartObjects();
        branch = GetBranch(true);
        environment = GetEnvironment(true);
        Init(environment, branch, false);
    }

    private void SetupSmartObjects() {
        _gameEventsManager = gameObject.AddComponent<GameEventsManager>();
        _offerManager = gameObject.AddComponent<OffersManager>();
        _offerGroupManager = gameObject.AddComponent<OfferGroupManager>();
        _gameEventsManager.SetComponent(_offerGroupManager, _offerManager);
        _offerGroupManager.SetComponent(_gameEventsManager);

        _smartObjects = new SmartObjectsEvents();
        ExternalEvents.RegisterSmartObjectsListener(_smartObjects);
        _storeEvents = new LiveOpsStoreEvents();
        ExternalEvents.RegisterLiveOpsListener(_storeEvents);
    }

    public static string GetBranch(bool useCustom) {
        string branch = string.Empty;
#if UNITY_EDITOR
        branch = "dev";
#endif
        if (RemoteConfigBase.isInstanced && !string.IsNullOrWhiteSpace(RemoteConfigBase.instance.Balancy_branch)) {
            branch = RemoteConfigBase.instance.Balancy_branch;
        }

        if (useCustom && PlayerPrefs.HasKey(SAVED_BRANCH)) {
            branch = PlayerPrefs.GetString(SAVED_BRANCH);
        }

        return branch;
    }

    public static Constants.Environment GetEnvironment(bool useCustom) {
        string env = string.Empty;
#if UNITY_EDITOR
        env = "dev";
#endif
        if (RemoteConfigBase.isInstanced && !string.IsNullOrWhiteSpace(RemoteConfigBase.instance.Balancy_enviroment)) {
            env = RemoteConfigBase.instance.Balancy_enviroment;
        }

        if (useCustom && PlayerPrefs.HasKey(SAVED_ENVIRONMENT)) {
            env = PlayerPrefs.GetString(SAVED_ENVIRONMENT);
        }

        if (env.Contains("dev", StringComparison.OrdinalIgnoreCase)) {
            return Constants.Environment.Development;
        }

        if (env.Contains("stag", StringComparison.OrdinalIgnoreCase)) {
            return Constants.Environment.Stage;
        }

        if (env.Contains("prod", StringComparison.OrdinalIgnoreCase)) {
            return Constants.Environment.Production;
        }

        return Constants.Environment.Production;
    }

    public void Init(Constants.Environment environmentTarget, string branchName, bool setEnvironment,
                     Action<string> callback = null) {
        if (!RemoteConfigBase.instance.Balancy_enable) {
            return;
        }

        _callbackInit = callback;
        if (setEnvironment) {
            environment = environmentTarget;
            branch = branchName;
            PlayerPrefs.SetString(SAVED_ENVIRONMENT, environment.ToString());
            PlayerPrefs.SetString(SAVED_BRANCH, branchName);
        }

        Logger.EditorLog(KeyFeature, $"Balancy Manager initialized! envi: {environmentTarget}. branch: {branchName}.");
        Main.Init(new AppConfig {
            ApiGameId = GAME_ID,
            PublicKey = PUBLIC_KEY,
            BranchName = branchName,
            Environment = environment,
            PreInit = PreInitType.LoadStaticData,
            OnInitProgress = OnInitProgress,
            OnContentUpdateCallback = OnContentUpdateCallback,
            OnReadyCallback = OnReadyCallback,
        });
    }

    private void OnInitProgress(InitProgress progress) {
        Logger.EditorLog(KeyFeature, $"***=> STATUS {progress.Status}");
        switch (progress.Status) {
            case BalancyInitStatus.PreInitFromResourcesOrCache:
                //CMS, loaded from resource or cache is ready, invoked only if PreInit >= PreInitType.LoadStaticData
                break;

            case BalancyInitStatus.PreInitLocalProfile:
                //Local profile is loaded, invoked only if PreInit >= PreInitType.LoadStaticDataAndLocalProfile
                break;

            case BalancyInitStatus.DictionariesReady:
                //CMS is updated and ready

                break;

            case BalancyInitStatus.Finished:
                //All systems are ready
                break;

            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    private void OnContentUpdateCallback(LoaderResponseData updateResponse) {
        Logger.EditorLog(KeyFeature, "Content Updated " + updateResponse.AffectedDictionaries.Length);
    }

    private void OnReadyCallback(InitializedResponseData response) {
        Logger.Log(KeyFeature, "Balancy | Optimization | Init Completed");
        Logger.Log(KeyFeature,
            $"Init Complete: {response.Success}, deploy version = {response.DeployVersion} - UserId {Auth.GetUserId()}");
        isInitialized = true;
        onInitCompleted?.Invoke();
        LiveOps.Profile.GeneralInfo.Level = Configuration.GetCurrentLevel();
        SetupLocalizedText();
        if (LocalizationManager.instance) {
            LocalizationManager.instance.OnLanguageChange += SetupLocalizedText;
        }

        if (response.Success) {
            _callbackInit?.Invoke($"Success. Deploy version: {response.DeployVersion}");
        } else {
            _callbackInit?.Invoke(Controller.GetErrors().Last().Error.Message);
        }
    }

    private void SetupLocalizedText() {
        if (!isInitialized) {
            return;
        }

        string currentLanguage = LocalizationManager.GetCurrentLanguageID();
        string localizationCode = Inwave.Utils.LanguageToLocalizationCode(currentLanguage);
        Balancy.Localization.Manager.ChangeLocalization(localizationCode);
    }

    public OfferGroupInfo GetDataGroupOffer(int idGroupOffer) {
        return _offerGroupManager?.GetDataGroupOffer(idGroupOffer);
    }

    public void LogOut(Constants.Environment oldEnvironment, string oldBranch, Action callback) {
        Auth.SignOut(GAME_ID, oldEnvironment, callback);
    }

    public static void ClearAdminConfig() {
        PlayerPrefs.DeleteKey(SAVED_ENVIRONMENT);
        PlayerPrefs.DeleteKey(SAVED_BRANCH);
    }
}