using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.UserProgression {
    public enum OnboardingFlowName {
        none,
        onboard_home,
        onboard_home_with_starjourney,
        onboard_level_up,
    }

    [System.Serializable]
    public struct OnboardStepData {
        public GameObject[] items;
        public bool isBlockRaycast;

        public void Show(bool isActive) {
            foreach (var item in items) {
                item.SetActive(isActive);
            }
        }
    }

    public class BaseOnboardingStepFlow : PopupUI {
        [SerializeField] private OnboardStepData[] steps;

        [Space] [SerializeField] private OnboardingFlowName onboardingFlowId;
        [SerializeField] private bool isSaveOnBoardProgress;
        [SerializeField] private bool isCompleteOnboardingWhenShowLastStep = false;
        [SerializeField] private bool isClosableByEventBack = false;
        [SerializeField] private Button btnTapToNextStep;
        [SerializeField] private Image raycastBlocker;
        [SerializeField] private GameObject objTapToNextText;

        private int _maxStep;
        public int CurrentStep { get; private set; } = -1;


        private void Start() {
            if (onboardingFlowId != OnboardingFlowName.none &&
                PlayerPrefs.GetInt(onboardingFlowId.ToString(), 0) == 1) {
                Destroy(gameObject);
                return;
            }

            if (steps == null) return;

            HideAllStepItems();
            
            if (btnTapToNextStep) {
                btnTapToNextStep.onClick.AddListener(ShowNextStep);
            }

            _maxStep = steps.Length;

            if (isSaveOnBoardProgress && onboardingFlowId != OnboardingFlowName.none) {
                CurrentStep = PlayerPrefs.GetInt(Util.BuildString('_', onboardingFlowId, "step"), 0);

                if (steps == null || CurrentStep < 0 || CurrentStep > _maxStep - 1) {
                    Destroy(gameObject);
                    return;
                }
            }

            ShowNextStep();
        }

        protected override void OnDisable() {
            base.OnDisable();
            SaveOnboardProgress();

            if (isCompleteOnboardingWhenShowLastStep && CurrentStep == _maxStep - 1) {
                PlayerPrefs.SetInt(onboardingFlowId.ToString(), 1);
            }
        }

        private void OnApplicationPause(bool pause) {
            if (pause) {
                SaveOnboardProgress();
            }
        }

        private void SaveOnboardProgress() {
            if (isSaveOnBoardProgress && onboardingFlowId != OnboardingFlowName.none) {
                PlayerPrefs.GetInt(onboardingFlowId + "_step", CurrentStep);
            }
        }

        private void HideAllStepItems() {
            foreach (OnboardStepData step in steps) {
                step.Show(false);
            }
        }

        public void ShowNextStep() {
            if(CurrentStep == _maxStep - 1) {
                PlayerPrefs.SetInt(onboardingFlowId.ToString(), 1);
                Close();
                return;
            }

            if (CurrentStep > -1) {
                steps[CurrentStep].Show(false);
            }

            CurrentStep++;
            steps[CurrentStep].Show(true);

            if (raycastBlocker) {
                raycastBlocker.raycastTarget = steps[CurrentStep].isBlockRaycast;
            }
            if (objTapToNextText) {
                objTapToNextText.SetActive(steps[CurrentStep].isBlockRaycast);
            }
        }

        public override bool HandleEventBack() {
            if (!isClosableByEventBack)
                return false;
            
            return base.HandleEventBack();
        }
    }
}