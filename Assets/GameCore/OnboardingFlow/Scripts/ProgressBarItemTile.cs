using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class ProgressBarItemTile : MonoBehaviour {
    [SerializeField] private Text txtValue;

    private Transform _tranform;
    private Tween     _tween;

    private void Awake() {
        _tranform = this.transform;
    }

    private void OnEnable() {
        Spawner.s.OnChangeJumpCount += UpdateJumpCount;
        UpdateJumpCount();
    }

    private void OnDisable() {
        Spawner.s.OnChangeJumpCount -= UpdateJumpCount;
    }

    private void UpdateJumpCount() {
        txtValue.text = GameController.instance.GetCurrentExp().ToString();
        if (GameController.instance.ReachMaxTile()) {
            _tween?.Kill();
            _tween = DOTween.Sequence().Append(_tranform.DOScale(1.1f, 0.15f)).Append(_tranform.DOScale(1f, 0.15f));
            _tween.Play();
        }
    }

    private void OnDestroy() {
        // Kill all DOTween instances to prevent memory leaks
        DOTween.Kill(gameObject);
    }
}