using System;
using System.Collections.Generic;

public class FeatureUnlockManager : FastSingleton<FeatureUnlockManager> {
    private Dictionary<FeatureKey, FeatureUnlockCondition> _featureUnlockConditions;

    private Dictionary<FeatureKey, FeatureUnlockCondition> featureUnlockConditions =>
        _featureUnlockConditions ??= new Dictionary<FeatureKey, FeatureUnlockCondition>();
    
    public void Init() {
        _featureUnlockConditions = new Dictionary<FeatureKey, FeatureUnlockCondition>();
    }

    private string GetFileName(FeatureKey featureKey) {
        return Util.BuildString("_", featureKey.ToString(), "unlock");
    }
    
    public void SaveUnlockConditionData(FeatureKey featureKey, FeatureUnlockCondition featureUnlockCondition) {
        featureUnlockConditions[featureKey] = featureUnlockCondition;
        SaveData.Save(GetFileName(featureKey), featureUnlockCondition);
    }
    
    /// <summary>
    /// Load saved condition when a feature is enabled,
    /// determine whether the saved data needs to be reset to match the latest config
    /// </summary>
    /// <param name="featureKey"></param>
    /// <param name="config"></param>
    public FeatureUnlockCondition LoadConditions(FeatureKey featureKey, StandardUnlockConditionConfig config) {
        FeatureUnlockCondition condition = null;
        bool isBrandNew = CheckToUpdateConfig(featureKey, ref condition);
        bool isForceReset = condition != null && config.version != condition.furtherConditionVersion;
        
        if (isBrandNew || isForceReset) {
            condition ??= new FeatureUnlockCondition();
            condition.Reset(config);
            SaveUnlockConditionData(featureKey, condition);
        }

        return condition;
    }

    
    /// <summary>
    /// Mark the feature as disabled to ensure the latest data is reset on the next enable
    /// </summary>
    /// <param name="featureKey"></param>
    public void TurnOffFeature(FeatureKey featureKey) {
        if (featureUnlockConditions.TryGetValue(featureKey, out var unlockCondition)) {
            unlockCondition.TurnOff();
            SaveUnlockConditionData(featureKey, unlockCondition);
            return;
        }
        
        if (SaveData.TryLoad(GetFileName(featureKey), ref unlockCondition) && unlockCondition.isEnabled) {
            unlockCondition.TurnOff();
            SaveUnlockConditionData(featureKey, unlockCondition);
        }
    }

    
    /// <summary>
    /// Get the saved unlock conditions and check if they need to be updated using the latest remote configs
    /// </summary>
    /// <param name="featureKey"></param>
    /// <param name="unlockCondition"></param>
    /// <returns>Need to update further milestones</returns>
    public bool CheckToUpdateConfig(FeatureKey featureKey, ref FeatureUnlockCondition unlockCondition) {
        if (!featureUnlockConditions.TryGetValue(featureKey, out unlockCondition)) {
            if (SaveData.TryLoad(GetFileName(featureKey), ref unlockCondition)) {
                // The feature's data was saved but hasn't been loaded into the manager at runtime
                featureUnlockConditions[featureKey] = unlockCondition;
                return !unlockCondition.isEnabled;
            }
            
            // Failure to load from file indicates this is the first time the feature has been enabled
            return true;
        }
        
        // Need to update to the latest data if the feature was re-enabled
        return !unlockCondition.isEnabled;
    }
    
    public bool GetUnlockCondition(FeatureKey featureKey, ref FeatureUnlockCondition unlockCondition) {
        if (featureUnlockConditions.TryGetValue(featureKey, out unlockCondition)) {
            return true;
        }
        
        if (SaveData.TryLoad(GetFileName(featureKey), ref unlockCondition)) {
            featureUnlockConditions[featureKey] = unlockCondition;
            return true;
        }
        
        return false;
    }

    public void ProcessRawUnlockConfigs(FeatureKey featureKey, ref FeatureUnlockCondition result, string starConfig,
                                                         string songStarConfig, string versionConfig) {
        int star = 0;
        int songStart = 0;
        int furtherStar = 0;
        int furtherSongStart = 0;
        bool isError = false;

        // Validate input parameters
        if (string.IsNullOrEmpty(starConfig) || string.IsNullOrEmpty(songStarConfig)) {
            // Use fallback values for error config
            star = int.MaxValue;
            songStart = int.MaxValue;
            furtherStar = int.MaxValue;
            furtherSongStart = int.MaxValue;
            isError = true;
        } else {
            string[] starCondition = starConfig.Split(';');
            string[] songStartCondition = songStarConfig.Split(';');
            
            // Validate and parse star conditions
            int configSplitLength = starCondition.Length;
            if (configSplitLength > 1) {
                if (!int.TryParse(starCondition[0], out star) || star < 0) {
                    star = int.MaxValue;
                    isError = true;
                }
                
                if (!int.TryParse(starCondition[1], out furtherStar) || furtherStar < 0) {
                    furtherStar = int.MaxValue;
                    isError = true;
                }
            } else if (configSplitLength == 1) {
                if (!int.TryParse(starCondition[0], out star) || star < 0) {
                    star = int.MaxValue;
                    isError = true;
                }
                
                furtherStar = 0;
            } else {
                star = int.MaxValue;
                furtherStar = int.MaxValue;
                isError = true;
            }

            // Validate and parse song start conditions
            configSplitLength = songStartCondition.Length;
            if (configSplitLength > 1) {
                if (!int.TryParse(songStartCondition[0], out songStart) || songStart < 0) {
                    songStart = int.MaxValue;
                    isError = true;
                }
                if (!int.TryParse(songStartCondition[1], out furtherSongStart) || furtherSongStart < 0) {
                    furtherSongStart = int.MaxValue;
                    isError = true;
                }
            } else if (configSplitLength == 1) {
                if (!int.TryParse(songStartCondition[0], out songStart) || songStart < 0) {
                    songStart = int.MaxValue;
                    isError = true;
                }

                furtherSongStart = 0;
            } else {
                songStart = int.MaxValue;
                furtherSongStart = int.MaxValue;
                isError = true;
            }
        }

        if (isError) {
            // set a special condition version to label error ver and reset
            // calculated data to newest when remote configs updated
            versionConfig = "config-error";
            Logger.LogError($"[FeatureUnlock] error occurs when processing raw config of {featureKey}");
        }
        
        StandardUnlockConditionConfig config = new(star, songStart, furtherStar, furtherSongStart, versionConfig);
        result = LoadConditions(featureKey, config);
    }
}

public sealed class StandardUnlockConditionConfig {
    public int    star;
    public int    songStart;
    public int    furtherStars;
    public int    furtherSongStart;
    public string version;

    public StandardUnlockConditionConfig(int star, int songStart, int furtherStars, int furtherSongStart, string version) {
        this.star = star;
        this.songStart = songStart;
        this.furtherStars = furtherStars;
        this.furtherSongStart = furtherSongStart;
        this.version = version;
    }
}

[Serializable]
public class FeatureUnlockCondition {
    /// <summary>
    /// flag to determine whether the feature is locked (optional)
    /// </summary>
    public bool isLocked = true;
    
    /// <summary>
    /// // If the feature was off in the previous session, then the next time it is turned on,
    /// we need to reset the further star milestones
    /// </summary>
    public bool isEnabled;
    
    /// <summary>
    /// The stars milestones that the user must reach
    /// </summary>
    public int totalStars;
    
    /// <summary>
    /// The star milestone calculated by adding the extra stars required when switching the feature from off to on
    /// </summary>
    public int furtherTotalStars;

    /// <summary>
    /// The number of song_start that the user must reach
    /// </summary>
    public int totalSongStart;
    
    /// <summary>
    /// The song_start milestone calculated by adding the extra number of song_start required when switching
    /// the feature from off to on
    /// </summary>
    public int furtherTotalSongStart;

    /// <summary>
    /// version to determine whether the further unlock conditions need to update
    /// </summary>
    public string furtherConditionVersion;
}

public static class FeatureUnlockExtension{
    public static void Reset(this FeatureUnlockCondition condition, StandardUnlockConditionConfig config) {
        if (condition == null) {
            Logger.EditorLog("[FeatureUnlockExtension]", "Condition is null, cannot reset");
            return;
        }
        
        if (config == null) {
            Logger.EditorLog("[FeatureUnlockExtension]", "Config is null, cannot reset");
            return;
        }
        
        condition.isEnabled = true;
        condition.totalStars = config.star;
        condition.totalSongStart = config.songStart;
        condition.furtherConditionVersion = config.version ?? "unknown";
        
        if (condition.isLocked) {
            // Safely get current values with fallbacks
            int currentStars = Configuration.instance?.GetCurrentStars() ?? 0;
            int currentSongStart = UserProperties.GetPropertyInt(UserProperties.song_start);
            
            condition.furtherTotalStars = currentStars + config.furtherStars;
            condition.furtherTotalSongStart = currentSongStart + config.furtherSongStart;
        }
    }

    public static void TurnOff(this FeatureUnlockCondition condition) {
        condition.isEnabled = false;
    }
}

public enum FeatureKey : byte {
    GALAXY_QUEST,
    BOOSTER_LIFE_SAVER,
    BOOSTER_TILE_TIDY,
    BOOSTER_HYPER_STAR,
    POWER_CUBE,
    MILESTONE_EVENT,
}
