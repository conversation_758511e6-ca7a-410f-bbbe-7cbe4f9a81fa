using System;
using Sirenix.OdinInspector;
using UnityEngine;

namespace TilesHop.Cores.Boosters {
    [Serializable]
    public abstract class BoosterItem : IComparable {
        public BoosterType type;
        public int         amount;
        public bool        isTemporary = false;

        // protected int songStartToUnlock;
        // protected int starToUnlock;
        
        protected FeatureUnlockCondition unlockCondition;
        
        public    int onboardAmount = 0;

        [ShowInInspector, NonSerialized] public  bool                        isLocked = true;
        [ShowInInspector]                private BoosterStorage.OnboardState _state = BoosterStorage.OnboardState.None;

        public BoosterStorage.OnboardState State {
            get => _state;
            set {
                _state = value;
                BoosterStorage.SetOnboard(type, value);
            }
        }

        public virtual void UpdateState(int songStart, int star) {
            _state = BoosterStorage.GetOnboardState(type);
        }

        public virtual void ApplyUnlockedResult() {
            State = BoosterStorage.OnboardState.Unlocked;
        }

        public void ApplyForceSelectForOnboardDone() {
            State = BoosterStorage.OnboardState.DoneForceUse;
        }

        public abstract int GetPriceExchange();

        public void ApplySelected() {
            if (!isTemporary) {
                amount -= 1;
            }
            if (amount < 0) {
                Logger.EditorLogError(BoosterManager.KEY_FEATURE, $"Not enough amount {type.ToString()}");
                return;
            }

            BoosterStorage.SetItemAmount(type, amount);

            if (_state == BoosterStorage.OnboardState.DoneForceUse) {
                SetCompletedOnboard();
            }
        }

        public void SetCompletedOnboard() {
            State = BoosterStorage.OnboardState.Complete;
        }

        public void AddAmount(string source, int increase = 1) {
            increase = Mathf.Abs(increase);
            amount += increase;
            BoosterStorage.SetItemAmount(type, amount);
            BoosterTracking.BoosterReceive(type, source);
        }

        
        /// <summary>
        /// Compare method used to sort booster items based on their unlock condition values.
        /// For brand-new users, unlock conditions are based on star and song_start milestones
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int CompareTo(object obj) {
            if (obj == null) return 1;
            
            if (obj is not BoosterItem other) {
                throw new ArgumentException($"Object is not a BoosterItem: {obj.GetType()}", nameof(obj));
            }
            
            // Null safety checks for unlock conditions
            if (unlockCondition == null && other.unlockCondition == null) return 0;
            if (unlockCondition == null) return -1;
            if (other.unlockCondition == null) return 1;
            
            int star = unlockCondition.totalStars;
            int songStart = unlockCondition.totalSongStart;
            int otherStar = other.unlockCondition.totalStars;
            int otherSongStart = other.unlockCondition.totalSongStart;
            
            return star == otherStar ? songStart.CompareTo(otherSongStart) : star.CompareTo(otherStar);
        }

        public int GetStarToUnlock() {
            if (unlockCondition == null) {
                Logger.EditorLog("[BoosterItem]", $"Unlock condition is null for {type}");
                return int.MaxValue; // Ensure it's locked if no condition exists
            }
            return Mathf.Max(unlockCondition.totalStars, unlockCondition.furtherTotalStars);
        }
    }
}