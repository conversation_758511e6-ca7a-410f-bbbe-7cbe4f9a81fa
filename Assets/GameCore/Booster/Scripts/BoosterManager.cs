using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace TilesHop.Cores.Boosters {
    public partial class BoosterManager : FastSingleton<BoosterManager>, FeatureService {
        public const string KEY_FEATURE = "Booster";
        public       bool   initDone    = false;

        [ShowInInspector] private bool isInited;

        [ReadOnly] public bool usingShield;
        [ReadOnly] public bool usingTidyTile;
        [ReadOnly] public bool usingHyperStar;

        [ReadOnly] public bool usingShieldStreak;
        [ReadOnly] public bool usingMagnet;
        [ReadOnly] public bool usingGemRain;

        private UISelectBooster                _uiSelectBooster;
        private BoosterMagnet                  _magnet;
        private BoosterGemRain                 _gemRain;
        private BallFailurePreTriggerDetection _shieldDeathTrigger;
        private BoosterHyperBoost              _hyperBoost;

        [ShowInInspector] private List<BoosterItem>                    _listBoosterItems;
        private                   Dictionary<BoosterType, BoosterItem> _dictBoosterItems;

        [ShowInInspector] private List<BoosterItem> _selectedBoosterTypes;
        [ShowInInspector] private List<BoosterType> _streakTypes = new List<BoosterType>();

        [ShowInInspector] private List<BoosterType> _onboardTypes;

        private bool   _applySelectedBooster;
        private string _strUsedItem;
        public  int    suggestIndexWhenNotEnoughGems;

        public UISelectBooster uiSelectBooster => _uiSelectBooster;
        public bool isShowingBoosterSelector => _uiSelectBooster != null && _uiSelectBooster.gameObject.activeSelf;

        protected override void Awake() {
            base.Awake();
            DontDestroyOnLoad(this.gameObject);
        }

        public void Init() {
            if (_listBoosterItems == null) {
                InitItems();
                UpdateStatusBooster();
            }
        }

        public void Init(Song song) {
            if (isInited) {
                return;
            }

            initDone = false;
            isInited = true;

            if (_listBoosterItems == null) {
                InitItems();
            }

            UpdateStatusBooster();
            ResetBooster();
            ClearUIIngameBooster();
            if (CanSelectBooster(song)) {
                if (ChallengeMode.IsAcceptFromNormal) {
                    //when change from normal to challenge mode, no need to show popup select again. Just restore unused booster/streak
                    OnRecycleBooster();
                } else {
                    if (!_uiSelectBooster) {
                        _uiSelectBooster = Util.ShowPopUp(PopupName.SelectBooster).GetComponent<UISelectBooster>();
                    }

                    if (_uiSelectBooster) {
                        _uiSelectBooster.Show(OnSelectBoosterDone);
                    } else {
                        Logger.LogError("Cannot find UISelectBooster!");
                    }
                }
            } else {
                initDone = true;
            }
        }

        private void InitItems() {
            _listBoosterItems = new List<BoosterItem>();
            _dictBoosterItems = new Dictionary<BoosterType, BoosterItem>();
            var shield = new BoosterItemShield();
            _listBoosterItems.Add(shield);
            _dictBoosterItems.Add(BoosterType.Shield, shield);

            var tidyTile = new BoosterItemTidyTile();
            _listBoosterItems.Add(tidyTile);
            _dictBoosterItems.Add(BoosterType.TileTidy, tidyTile);

            var hyperStar = new BoosterItemHyperStar();
            _listBoosterItems.Add(hyperStar);
            _dictBoosterItems.Add(BoosterType.HyperStar, hyperStar);

            //sort by unlock condition!!!!
            _listBoosterItems.Sort();
        }

        private void UpdateStatusBooster() {
            if (_listBoosterItems.IsNullOrEmpty())
                return;

            int songStart = UserProperties.GetPropertyInt(UserProperties.song_start);
            int star = Configuration.GetTotalStar();
            foreach (BoosterItem boosterItem in _listBoosterItems) {
                if (!boosterItem.isLocked)
                    continue;

                boosterItem.UpdateState(songStart, star);
            }
        }

        private bool CanSelectBooster(Song song) {
            if (song.isTutorialSong)
                return false;

            if (!_listBoosterItems.IsNullOrEmpty()) {
                foreach (var item in _listBoosterItems) {
                    if (!item.isLocked)
                        return true;
                }
            }

            if (PowerCubeManager.isActive && PowerCubeManager.isUnlock) {
                return true;
            }

            return false;
        }

        private void ResetBooster() {
            //booster
            usingShield = false;
            usingTidyTile = false;
            usingHyperStar = false;

            //power cube
            usingShieldStreak = false;
            usingMagnet = false;
            usingGemRain = false;

            _strUsedItem = null;
        }

        public void ActiveBoostersInActionPhase(List<BoosterItem> items) {
            _applySelectedBooster = false;

            foreach (BoosterItem item in items) {
                if (boosterTypes.TryGetValue(item.type, out var current)) {
                    boosterTypes[item.type] = (byte) (current + item.amount);
                } else {
                    boosterTypes[item.type] = (byte) item.amount;
                }
            }

            ResetBooster();
            foreach (var item in boosterTypes) {
                switch (item.Key) {
                    case BoosterType.Shield:
                        usingShield = true;
                        break;

                    case BoosterType.HyperStar:
                        usingHyperStar = true;
                        break;

                    case BoosterType.TileTidy:
                        usingTidyTile = true;
                        break;

                    case BoosterType.GemRain:
                        usingGemRain = true;
                        break;

                    case BoosterType.Magnet:
                        usingMagnet = true;
                        break;
                }
            }

            InitAllBoosters();
        }

        private void OnSelectBoosterDone(List<BoosterItem> selectedBoosterTypes, List<BoosterType> onboardList) {
            _applySelectedBooster = false;
            _selectedBoosterTypes = selectedBoosterTypes;
            _onboardTypes = onboardList;
            ResetBooster();
            if (!_selectedBoosterTypes.IsNullOrEmpty()) {
                foreach (var item in _selectedBoosterTypes) {
                    Logger.EditorLog(KEY_FEATURE, $"Using {item.type}");
                    switch (item.type) {
                        case BoosterType.Shield:
                            usingShield = true;
                            break;

                        case BoosterType.HyperStar:
                            usingHyperStar = true;
                            break;

                        case BoosterType.TileTidy:
                            usingTidyTile = true;
                            break;

                        default:
                            Logger.EditorLogError(KEY_FEATURE, $"Not handle this type: {item.type}");
                            break;

                    }
                }
            }

            if (PowerCubeManager.isActive) {
                _streakTypes = PowerCubeManager.instance.GetRewardStreak();
                if (!_streakTypes.IsNullOrEmpty()) {
                    foreach (var type in _streakTypes) {
                        Logger.EditorLog(KEY_FEATURE, $"Power cube {type}");
                        switch (type) {
                            case BoosterType.Shield:
                                usingShieldStreak = true;
                                break;

                            case BoosterType.Magnet:
                                usingMagnet = true;
                                break;

                            case BoosterType.GemRain:
                                usingGemRain = true;
                                break;

                            default:
                                Logger.EditorLogError(KEY_FEATURE, $"Not handle this type: {type}");
                                break;
                        }
                    }
                }
            } else {
                _streakTypes?.Clear();
            }

            InitAllBoosters();
            if (_coroutineSelectedBooster != null) {
                StopCoroutine(_coroutineSelectedBooster);
            }

            _coroutineSelectedBooster = StartCoroutine(IECheckShowOnboardSelectedBooster());
        }

        private void OnRecycleBooster() {
            ResetBooster();
            _applySelectedBooster = false;
            _onboardTypes ??= new List<BoosterType>();
            _onboardTypes.Clear();
            _selectedBoosterTypes = new List<BoosterItem>();
            _streakTypes = new List<BoosterType>();
            if (boosterTypes != null) {
                foreach (var pair in boosterTypes) {
                    switch (pair.Key) {
                        case BoosterType.Shield:
                            var tempStreak = PowerCubeManager.instance?.GetRewardStreak();
                            if (tempStreak != null && tempStreak.Contains(BoosterType.Shield)) {
                                //belong to streak
                                _streakTypes.Add(BoosterType.Shield);
                                usingShieldStreak = true;
                                if (pair.Value == 2) {
                                    _selectedBoosterTypes.Add(_dictBoosterItems[BoosterType.Shield]);
                                    usingShield = true;
                                }
                            } else {
                                _selectedBoosterTypes.Add(_dictBoosterItems[BoosterType.Shield]);
                                usingShield = true;
                            }

                            break;

                        case BoosterType.TileTidy:
                            _selectedBoosterTypes.Add(_dictBoosterItems[BoosterType.TileTidy]);
                            usingTidyTile = true;
                            break;

                        case BoosterType.HyperStar:
                            _selectedBoosterTypes.Add(_dictBoosterItems[BoosterType.HyperStar]);
                            usingHyperStar = true;
                            break;

                        case BoosterType.GemRain:
                            usingGemRain = true;
                            _streakTypes.Add(BoosterType.GemRain);
                            break;

                        case BoosterType.Magnet:
                            _streakTypes.Add(BoosterType.Magnet);
                            usingMagnet = true;
                            break;

                        default:
                            Logger.EditorLogError(KEY_FEATURE, $"Unhandled booster type: {pair.Key}");
                            break;
                    }
                }
            }

            InitAllBoosters();
            if (_coroutineSelectedBooster != null) {
                StopCoroutine(_coroutineSelectedBooster);
            }

            _coroutineSelectedBooster = StartCoroutine(IECheckShowOnboardSelectedBooster());
        }

        private void InitAllBoosters() {
            InitShieldBooster();
            InitGemRainVFX();
            InitMagnetVFX();
            InitHyperBoost();

            if (usingTidyTile) {
                Spawner.s.platformManager.ShowTidySigns(true);
            }
        }

        public List<BoosterType> GetOnboardList() {
            return _onboardTypes;
        }

        private void InitHyperBoost() {
            if (usingHyperStar) {
                if (!_hyperBoost) {
                    _hyperBoost = gameObject.AddComponent<BoosterHyperBoost>();
                    _hyperBoost.Init();
                } else {
                    _hyperBoost.enabled = true;
                }
            } else {
                if (_hyperBoost != null) {
                    _hyperBoost.enabled = false;
                }
            }
        }

        private void InitShieldBooster() {
            byte amount = 0;
            if (usingShield) {
                amount++;
            }

            if (usingShieldStreak) {
                amount++;
            }

            if (amount != 0) {
                if (!_shieldDeathTrigger) {
                    Transform deathTriggerTransform =
                        Follow.instance.transform.GetComponentInChildren<DeathTrigger>().transform;
                    _shieldDeathTrigger =
                        Instantiate(Resources.Load<BallFailurePreTriggerDetection>("Booster/ShieldFailDetector"),
                            deathTriggerTransform);
                }

                _shieldDeathTrigger.Active();

                Ball.b.SetInvincible(amount);
            } else {
                if (_shieldDeathTrigger) {
                    _shieldDeathTrigger.Deactive();
                }

                Ball.b.SetVincible();
            }
        }

        private void InitMagnetVFX() {
            if (usingMagnet) {
                if (_magnet == null) {
                    Transform parent = Ball.b != null ? Ball.b.transCache : null;
                    _magnet = Instantiate(Resources.Load<BoosterMagnet>("Booster/BoosterMagnet"), parent);
                }

                _magnet.Active();
            } else if (_magnet) {
                _magnet.gameObject.SetActive(false);
            }
        }

        private void InitGemRainVFX() {
            if (usingGemRain) {
                if (!_gemRain) {
                    Transform ballCache = Ball.b ? Ball.b.transCache : null;
                    _gemRain = Instantiate(Resources.Load<BoosterGemRain>("Booster/BoosterGemRain"), null);
                    _gemRain.Init(ballCache);
                }

                _gemRain.ActiveFeature();
                _gemRain.gameObject.SetActive(true);
            } else {
                if (_gemRain) {
                    _gemRain.gameObject.SetActive(false);
                }
            }
        }

        public static void ResetInit() {
            if (!instance)
                return;

            instance.isInited = false;
        }

        public void GamePrepare() {
            InitAllBoosters();
        }

        public void GameStart(bool firstStart) {
            ResetInit();

            if (!_applySelectedBooster) {
                _applySelectedBooster = true;
                ApplyBoosterToPlayer();
                PowerCubeManager.SetupReward();
            }

            if (usingMagnet) {
                if (_magnet == null) {
                    InitMagnetVFX();
                }

                _magnet.GameStart();
            }

            if (usingHyperStar && firstStart) {
                if (_hyperBoost == null) {
                    InitHyperBoost();
                }

                _hyperBoost.GameStart();
            }
        }

        private void ApplyBoosterToPlayer() {
            if (ChallengeMode.IsAcceptFromNormal) {
                return;
            }

            if (_selectedBoosterTypes == null) {
                return;
            }

            var song = GameController.instance._song;
            foreach (var item in _selectedBoosterTypes) {
                item.ApplySelected();
                BoosterTracking.BoosterActive(item.type, song?.name, song?.acm_id_v3);
                if (item.type == BoosterType.TileTidy) { //auto use
                    float time = Spawner.s.GetTimeAppearFirstStar();
                    UseItem(BoosterType.TileTidy, time);
                }
            }
        }

        public static BoosterItem GetBoosterByType(BoosterType type) {
            if (instance == null) {
                return null;
            }

            if (instance._dictBoosterItems == null) {
                instance.InitItems();
            }

            return instance._dictBoosterItems.GetValueOrDefault(type, null);
        }

        public void GameStop() {
            if (usingMagnet) {
                _magnet?.GameStop();
            }

            if (usingGemRain) {
                _gemRain?.GameStop();
            }
        }

        public void GameComplete() {
            UpdateStatusBooster();
            if (usingGemRain) {
                _gemRain?.GameStop();
            }

            if (usingMagnet) {
                _magnet?.GameStop();
            }
        }

        public List<BoosterItem> GetListBoosterItems() {
            return _listBoosterItems;
        }

        public void GetHyperBoostData(out int start, out int end) {
            start = _hyperBoost.startNote;
            end = _hyperBoost.endNote;
        }

        public void ResetHyperBoostData() {
            _hyperBoost.Reset();
        }

        public int GetHyperBoostValue() {
            return _hyperBoost.DiamondValue;
        }

        private static BoosterItem GetItemByOnboardState(BoosterStorage.OnboardState onboardState) {
            if (!isInstanced || instance._listBoosterItems == null) {
                return null;
            }

            foreach (var item in instance._listBoosterItems) {
                if (item.isLocked)
                    continue;

                if (item.State == onboardState) {
                    return item;
                }
            }

            return null;
        }

        private static byte GetTotalItemUnlocked() {
            if (!isInstanced || instance._listBoosterItems == null) {
                return 0;
            }

            byte amount = 0;
            foreach (var item in instance._listBoosterItems) {
                if (item.isLocked)
                    continue;

                amount++;
            }

            return amount;
        }

        private static BoosterItem GetItemToUnlock() {
            return GetItemByOnboardState(BoosterStorage.OnboardState.None);
        }

        private static BoosterItem GetItemToOnboard() {
            return GetItemByOnboardState(BoosterStorage.OnboardState.DoneForceUse);
        }
    }
}