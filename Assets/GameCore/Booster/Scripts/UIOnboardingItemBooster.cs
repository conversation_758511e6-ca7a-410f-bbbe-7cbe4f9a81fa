using System;
using System.Collections;
using TilesHop.Cores.Pooling;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.Boosters {
    public class UIOnboardingItemBooster : PopupUI {
        [SerializeField] private Button          btnTryNow;
        [SerializeField] private Text            txtClaim;
        [SerializeField] private Image           boosterIcon;
        [SerializeField] private TextMeshP<PERSON>UGUI boosterName;
        [SerializeField] private Text            boosterDes;
        
        [Space]
        [SerializeField] private Transform animContainer;
        [SerializeField] private GameObject loadingIndicator;

        private BoosterType _boosterType;
        private Animator _boosterDemo;

        public bool isShowing { get; private set; }

        protected override void OnEnable() {
            base.OnEnable();
            btnTryNow.gameObject.SetActive(false);
            StartCoroutine(IEShowTryNow());
            isShowing = true;
        }

        private IEnumerator Start() {
            btnTryNow.onClick.AddListener(OnBtnTryNowClick);
            var timer = Time.time;
            bool isShowLoadingIndicator = false;
            
            var animAssetRequest = Resources.LoadAsync<GameObject>("AnimatedDemo_Booster");
            while (!animAssetRequest.isDone) {
                // only show loading indicator when loading time is over 0.1 sec
                if (!isShowLoadingIndicator && Time.time - timer > 0.1f) {
                    loadingIndicator.SetActive(true);
                    isShowLoadingIndicator = true;
                }
                yield return null;
            }

            if (animAssetRequest.asset == null) {
                CustomException.Fire("[Onboarding Booster]", "Can't load resource");
                yield break;
            }

            loadingIndicator.SetActive(false);
            var animatedObject = Instantiate(animAssetRequest.asset as GameObject, animContainer);
            if (animatedObject.TryGetComponent(out _boosterDemo)) {
                ShowCore();
            }
            
        }
        
        protected override void OnDisable() {
            base.OnDisable();
            isShowing = false;
        }

        public override bool HandleEventBack() {
            return false;
        }

        private void OnBtnTryNowClick() {
            gameObject.SetActive(false);
        }

        public void Show(BoosterItem booster) {
            _boosterType = booster.type;
            ShowCore();
            txtClaim.text = LocalizationManager.instance.GetLocalizedValue("OK");
        }

        public void Show(BoosterType boosterType) {
            _boosterType = boosterType;
            ShowCore();
            txtClaim.text = LocalizationManager.instance.GetLocalizedValue("OK");
        }


        private IEnumerator IEShowTryNow() {
            yield return YieldPool.GetWaitForSeconds(2f);
            btnTryNow.gameObject.SetActive(true);
        }
        
        private void ShowCore() {
            var boosterConfig = BoosterManager.GetBoosterConfig(_boosterType);
            boosterIcon.sprite = boosterConfig.icon;
            boosterName.text = boosterConfig.name;
            boosterDes.text = LocalizationManager.instance.GetLocalizedValue($"BOOSTER_{_boosterType}_DES");

            if (_boosterDemo != null) {
                _boosterDemo.Play(_boosterType.ToString());
            }
        }
    }
}