using System;
using UnityEngine;

namespace TilesHop.Cores.Boosters {
    [CreateAssetMenu(fileName = "Booster", menuName = "ScriptableObjects/BoosterData", order = 1)]
    public class BoosterData : ScriptableObject {
        public BoosterConfig[] resources;

        public BoosterConfig GetConfig(BoosterType boosterType) {
            foreach (BoosterConfig config in resources) {
                if (config.type == boosterType)
                    return config;
            }

            return null;
        }
    }

    [Serializable]
    public class BoosterConfig {
        public BoosterType type;
        public string      name;
        public Sprite      icon;
    }
}