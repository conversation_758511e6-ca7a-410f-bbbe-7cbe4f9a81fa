using UnityEngine;

namespace TilesHop.Cores.Boosters {
    public partial class BoosterManager {
        private static BoosterData _boosterData;
        private static bool        _loadBoosterData;

        public static BoosterConfig GetBoosterConfig(BoosterType boosterType) {
            if (!_loadBoosterData) {
                _loadBoosterData = true;
                _boosterData = Resources.Load<BoosterData>("Data/Booster");
            }

            if (_boosterData) {
                return _boosterData.GetConfig(boosterType);
            }

            Logger.EditorLogError(KEY_FEATURE, "Not found booster config");
            return null;
        }

        public static void AddItemAmount(BoosterType type, string source, int amount) {
            Logger.EditorLog($"Add Booster {type} amount {amount} from {source}");
            bool found = false;
            if (isInstanced && instance._listBoosterItems != null) {
                foreach (var booster in instance._listBoosterItems) {
                    if (booster.type.Equals(type)) {
                        booster.AddAmount(source, amount);
                        found = true;
                        break;
                    }
                }
            }

            if (!found) {
                BoosterStorage.AddItemAmount(type, amount);
            }
        }

        public static string BoosterTypeToName(BoosterType boosterType) {
            switch (boosterType) {
                case BoosterType.Shield:
                    return "life_saver";

                case BoosterType.GemRain:
                    return "gem_rain";

                case BoosterType.TileTidy:
                    return "tile_tidy";

                case BoosterType.Magnet:
                    return "magnet";

                case BoosterType.HyperStar:
                    return "hyper_star";

                default:
                    Logger.EditorLogError("BoosterType: " + boosterType);
                    return "unknow";
            }
        }
    }
}