using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.Boosters {
    public class UIOnboardingBoosterText : PopupUI {
        [SerializeField] private Text txtMessage;
        [SerializeField] private Image boosterIcon;
        public void SetType(BoosterType onboardItem) {
            var config = BoosterManager.GetBoosterConfig(onboardItem);
            if (config.type == BoosterType.HyperStar) {
                txtMessage.text = string.Format(LocalizationManager.instance.GetLocalizedValue("BOOSTER_IS_ACTIVATED"), config.name.ToUpper());
            } else {
                txtMessage.text = string.Format(LocalizationManager.instance.GetLocalizedValue("BOOSTER_READY"), config.name.ToUpper());
            }
            boosterIcon.sprite = config.icon;
        }
    }
}