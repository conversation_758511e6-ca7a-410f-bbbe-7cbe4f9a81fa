using System;
using System.Collections;
using DG.Tweening;
using Sirenix.OdinInspector;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.Boosters {
    public class UIItemIngameBooster : MonoBehaviour {
        [SerializeField]            private RectTransform rootTransform;
        [SerializeField]            private Image         iconBooster;
        [SerializeField]            private Image         background;
        [SerializeField]            private GameObject    usageGroup;
        [SerializeField]            private Image         usageFill;
        [SerializeField]            private Text          txtAmount;
        [ShowInInspector, ReadOnly] private BoosterType   _type;
        [ShowInInspector, ReadOnly] private byte          _amount;
        [ShowInInspector, ReadOnly] private bool          _using;
        public BoosterType type => _type;
        public byte amount => _amount;

        private float _timeActive;
        private float _countTime;
        private event Action _callback;
        private bool _isShowed      = false;
        private bool _listenGemRain = false;

        private void OnDisable() {
            if (_listenGemRain && _type == BoosterType.GemRain) {
                _listenGemRain = false;
                BoosterGemRain.OnDone -= BoosterGemRainOnOnDone;
            }
        }

        private void LateUpdate() {
            if (_using) {
                if (GameController.instance.game == GameStatus.LIVE) {
                    _countTime -= Time.deltaTime;
                    if (_countTime > 0) {
                        usageFill.fillAmount = _countTime / _timeActive;
                    } else {
                        _using = false;
                        usageGroup.SetActive(false);
                        _callback?.Invoke();
                    }
                }

            }
        }

        public void Init(BoosterType boosterType, byte amount) {
            _using = false;
            _type = boosterType;
            _amount = amount;

            usageGroup.SetActive(false);
            var boosterConfig = BoosterManager.GetBoosterConfig(boosterType);
            if (boosterConfig == null) {
                iconBooster.gameObject.SetActive(false);
            } else {
                iconBooster.sprite = boosterConfig.icon;
                iconBooster.material = null;
                background.material = null;
                iconBooster.gameObject.SetActive(true);
            }

            gameObject.SetActive(true);
            rootTransform.DOLocalMoveX(0, 0f);
            transform.localScale = new Vector3(1, 0, 1);
            _isShowed = false;

            if (_type == BoosterType.GemRain) {
                _listenGemRain = true;
                BoosterGemRain.OnDone += BoosterGemRainOnOnDone;
            }
        }

        private void BoosterGemRainOnOnDone(bool active) {
            if (!active) {
                if (_using) {
                    _using = false;
                    usageGroup.SetActive(false);
                    _callback?.Invoke();
                }
            }
        }

        public void Show() {
            if (_isShowed) {
                ShowCountText();
                return;
            }

            if (_amount < 2) {
                ShowCountText();
            }

            rootTransform.DOLocalMoveX(0, 0.3f);
            transform.DOScaleY(1, 0.3f);
            _isShowed = true;
        }

        public void Active(float time, Action callback) {
            if (time <= 0) {
                callback?.Invoke();
                return;
            }

            _using = true;
            _timeActive = time;
            _countTime = time;
            _callback = callback;
            usageFill.fillAmount = _countTime / _timeActive;
            usageGroup.SetActive(true);
        }

        private void Remove() {
            rootTransform.DOLocalMoveX(rootTransform.rect.width, 0.3f);
            transform.DOScaleY(0, 0.3f).OnComplete(SetDeActive);
        }

        private void Gray() {
            iconBooster.material = Configuration.instance.grayMaterial;
            background.material = Configuration.instance.grayMaterial;
        }

        public void SetDeActive() {
            this.gameObject.SetActive(false);
        }

        public void Reduce() {
            _amount -= 1;
            if (_amount <= 0) {
                if (RemoteConfigBase.instance.Booster_UI_DisappearWhenUse) {
                    Remove();
                } else {
                    Gray();
                }
            } else {
                ShowCountText();
            }
        }

        private void ShowCountText() {
            txtAmount.text = _amount > 1 ? Util.BuildString(string.Empty, "x", _amount) : string.Empty;
        }
    }
}