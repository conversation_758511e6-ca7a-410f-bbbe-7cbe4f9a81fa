using System.Collections.Generic;

namespace TilesHop.Cores.Boosters {
    public static class BoosterTracking {
        public enum OnboardState {
            unlock,
            selection,
            setup,
            ready
        }

        public static void BoosterOnboarding(BoosterType boosterType, OnboardState state) {
            string eventKey = string.Empty;
            switch (state) {
                case OnboardState.unlock:
                    eventKey = "booster_onboarding_unlock";
                    break;

                case OnboardState.selection:
                    eventKey = "booster_onboarding_selection";
                    break;

                case OnboardState.setup:
                    eventKey = "booster_onboarding_setup";
                    break;

                case OnboardState.ready:
                    eventKey = "booster_onboarding_ready";
                    break;
            }

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, BoosterManager.BoosterTypeToName(boosterType)},
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterSelection(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_selection";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, BoosterManager.BoosterTypeToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterImpression(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_impression";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, BoosterManager.BoosterTypeToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterUse(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_use";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, BoosterManager.BoosterTypeToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            // AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterActive(BoosterType boosterType, string song_name, string song_acm_id) {
            string eventKey = "booster_active";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.booster_name, BoosterManager.BoosterTypeToName(boosterType)},
                {TRACK_NAME.song_name, song_name},
                {TRACK_NAME.song_acm_id, song_acm_id},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            // AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }

        public static void BoosterReceive(BoosterType boosterType, string source) {
            string eventKey = "booster_receive";

            Dictionary<string, object> param = new Dictionary<string, object> {
                {TRACK_PARAM.source, source},
                {TRACK_PARAM.booster_name, BoosterManager.BoosterTypeToName(boosterType)},
                {TRACK_PARAM.gameplay_count, GameController.gameplayCount}
            };
            if (Util.IsGameScene()) {
                if (GameController.isInstanced) {
                    var song = GameController.instance._song;
                    param.TryAdd(TRACK_NAME.song_name, song.name);
                    param.TryAdd(TRACK_NAME.song_acm_id, song?.acm_id_v3);
                }
            }

            // AnalyticHelper.UpdateParamsAccumulatedCount(param, eventKey);
            AnalyticHelper.LogEvent(eventKey, param);
        }
    }
}