using System;
using System.Collections;
using TilesHop.Cores.Pooling;
using UnityEngine;
using UnityEngine.UI;

namespace TilesHop.Cores.Boosters {
    public class UIPowerCube : MonoBehaviour {
        private const string KEY_UNLOCK                 = "STREAKBOX_STAR_UNLOCK";
        private const string KEY_NOT_FOR_COMPLETED_SONG = "NOT_ACTIVE_FOR_COMPLETED_SONG";
        private const string KEY_NOT_FOR_ENDLESS_MODE   = "NOT_ACTIVE_ENDLESS_MODE";
        private const string KEY_NOT_IN_STREAK          = "NON_STREAK";
        private const string KEY_WIN_REQUIRED           = "STREAKBOX_WIN_REQUIRED";
        
        [Header("Lock")]
        [SerializeField] private GameObject objLock;

        [SerializeField] private Text txtLockMessage;
        [SerializeField] private Text txtNotAvailableMessage;

        [Header("Unlock")]
        [SerializeField] private GameObject objUnlock;
        [SerializeField] private GameObject objUnlockInfor;

        [SerializeField] private Image imgUnlockedReward;

        [SerializeField] private Sprite[] sprtUnlockedRewards;

        [SerializeField] private Text txtActiveTime;

        [SerializeField] private Image progressImg;
        [SerializeField] private Text txtStreak;

        [SerializeField] private GameObject objDeactive;
        [SerializeField] private Text       txtDeactive;

        [SerializeField]
        public Button btnInfor;

        private TimeSpan  _timeSpan;
        private Coroutine _coroutine;

        public event Action OnShowInfor;

		private void Start() {
            btnInfor.onClick.AddListener(OnBtnInforClicked);
        }

        private void OnEnable() {
            PowerCubeManager.OnChangeState += UpdateState;
            UpdateState();
        }

        private void OnDisable() {
            PowerCubeManager.OnChangeState -= UpdateState;
        }

        private void OnBtnInforClicked() {
			Util.ShowPopUp(PopupName.PowerCubeReward).GetComponent<PopupUI>();
			OnShowInfor?.Invoke();
        }
        
        private void UpdateState() {
            if (_coroutine != null) {
                StopCoroutine(_coroutine);
            }

            if (!PowerCubeManager.isEnable) {
				txtNotAvailableMessage.text = "Feature is coming soon!";
				txtNotAvailableMessage.gameObject.SetActive(true);
				objUnlockInfor.SetActive(false);
				objLock.SetActive(true);
                objUnlock.SetActive(false);
                objDeactive.SetActive(false);
            } else {
                if (PowerCubeManager.instanceSafe.isLocked) {
                    int currentStar = Configuration.instance.GetCurrentStars();
                    int starRequired = PowerCubeManager.instanceSafe.GetStarRequired();

                    if (currentStar >= starRequired) {
                        txtNotAvailableMessage.text = LocalizationManager.instance.GetLocalizedValue(KEY_WIN_REQUIRED);
						LocalizationManager.instance.UpdateFont(txtNotAvailableMessage);
						objUnlockInfor.SetActive(false);
                        txtNotAvailableMessage.gameObject.SetActive(true);
					} else {
                        txtLockMessage.text = string.Format(LocalizationManager.instance.GetLocalizedValue(KEY_UNLOCK), starRequired);
						LocalizationManager.instance.UpdateFont(txtLockMessage);
						objUnlockInfor.SetActive(true);
                        txtNotAvailableMessage.gameObject.SetActive(false);
                    }
                    
					objLock.SetActive(true);
                    objUnlock.SetActive(false);
                    objDeactive.SetActive(false);

                    StartCoroutine(IEForceUpdateLockUILayout());
				} else if (!PowerCubeManager.isActive) {
                    if (GameController.isInstanced && GameController.instance.isAlreadyCompletedSong) {
                        txtNotAvailableMessage.text = LocalizationManager.instance.GetLocalizedValue(KEY_NOT_FOR_COMPLETED_SONG);
                    } else {
                        txtNotAvailableMessage.text = LocalizationManager.instance.GetLocalizedValue(KEY_NOT_FOR_ENDLESS_MODE);
                    }
					LocalizationManager.instance.UpdateFont(txtNotAvailableMessage);
					txtNotAvailableMessage.gameObject.SetActive(true);
					objUnlockInfor.SetActive(false);
					objLock.SetActive(true);
                    objUnlock.SetActive(false);
                    objDeactive.SetActive(false);
                } else {
                    int streak = PowerCubeManager.instanceSafe.streak;

                    progressImg.fillAmount = streak / 3f;
                    txtStreak.text = Util.BuildString("/", streak, 3);
                    if (streak == 0) {
						objLock.SetActive(false);
						objUnlock.SetActive(false);
                        objDeactive.SetActive(true);
                        txtDeactive.text = LocalizationManager.instance.GetLocalizedValue(KEY_NOT_IN_STREAK);
						LocalizationManager.instance.UpdateFont(txtDeactive);
						return;
					}
                    
                    // if (GameController.enableEndless) {
                    //     imgUnlockedReward.sprite = sprtUnlockedRewards[streak - 1];
                    //     imgUnlockedReward.color = Color.gray;
                    //     imgUnlockedReward.gameObject.SetActive(true);
                    // } else {
                        imgUnlockedReward.sprite = sprtUnlockedRewards[streak - 1];
                        imgUnlockedReward.color = Color.white;
                        imgUnlockedReward.gameObject.SetActive(true);
                        _timeSpan = PowerCubeManager.instanceSafe.GetActiveTime();
                        txtActiveTime.text = _timeSpan.ToString(@"hh\:mm\:ss");
                        txtActiveTime.gameObject.SetActive(true);
                        _coroutine = StartCoroutine(StartCountdown());
                    // }
                    objLock.SetActive(false);
                    objUnlock.SetActive(true);
                    objDeactive.SetActive(false);
                }
            }
        }

        private IEnumerator IEForceUpdateLockUILayout() {
            yield return YieldPool.GetWaitForEndOfFrame();
			LayoutRebuilder.ForceRebuildLayoutImmediate(objUnlockInfor.GetComponent<RectTransform>());
		}

        private IEnumerator StartCountdown() {
            int remainingTime = (int) _timeSpan.TotalSeconds;

            while (remainingTime >= 0) {
                _timeSpan -= Configuration.OneSecond;
                txtActiveTime.text = _timeSpan.ToString(@"hh\:mm\:ss");
                yield return YieldPool.GetWaitForSeconds(1);

                remainingTime--;
            }
        }

    }
}