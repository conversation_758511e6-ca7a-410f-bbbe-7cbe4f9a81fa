using System;
using System.Collections.Generic;
using DG.Tweening;
using Sirenix.OdinInspector;
using TilesHop.Cores.Boosters;
using UnityEngine;

public class PowerCubeManager : FastSingleton<PowerCubeManager>, FeatureService {
    public static bool isEnable = false;
    public static bool isActive = false; // active when feature is available

    public const  string KEY_FEATURE_DEBUGGER    = "Power Cube";
    private const string KEY_FEATURE_STREAK      = "powercube_streak";
    private const string KEY_FEATURE_UNLOCK      = "powercube_streak_unlock";
    private const string KEY_FEATURE_ONBOARD     = "powercube_streak_onboard";
    private const string KEY_FEATURE_ACTIVE_TIME = "powercube_active_time";
    private const byte   MaxStreak               = 3;

    private                              float             _timeActivePerStreak;
    [ShowInInspector, ReadOnly] private  DateTime          _limitTime;
    [ShowInInspector, ReadOnly] internal bool              isLocked = true;
    [ShowInInspector, ReadOnly] private  byte              _currentStreak;
    [ShowInInspector, ReadOnly] private  List<BoosterType> _reward1;
    [ShowInInspector, ReadOnly] private  List<BoosterType> _reward2;
    [ShowInInspector, ReadOnly] private  List<BoosterType> _reward3;

    private Tween _tweenCountDown;
    public static event Action OnChangeState;
    public byte streak => _currentStreak;
    public static bool isUnlock => instance && !instance.isLocked;

    private bool _inOnboarding;

    private FeatureUnlockCondition _unlockCondition;

    private DateTime TimeNow => DateTime.Now;

    
    public void Init() {
        isEnable = RemoteConfigBase.instance.PowerCube_IsEnable;

        if (!isEnable) {
            return;
        }

        _timeActivePerStreak = RemoteConfigBase.instance.PowerCube_ActiveTime_Minute;
        if (_timeActivePerStreak <= 0) {
            _timeActivePerStreak = 15;
        }

        isLocked = PlayerPrefs.GetInt(KEY_FEATURE_UNLOCK, 0) == 0;

        if (isLocked) {
            FeatureUnlockManager.instanceSafe.ProcessRawUnlockConfigs(FeatureKey.POWER_CUBE, ref _unlockCondition,
                RemoteConfigBase.instance.PowerCube_Unlock_Star, RemoteConfigBase.instance.PowerCube_Unlock_SongStart,
                RemoteConfigBase.instance.PowerCube_UnlockVersion);
            return;
        }

        _currentStreak = (byte) PlayerPrefs.GetInt(KEY_FEATURE_STREAK, 0);
        if (_currentStreak != 0) {
            if (PlayerPrefs.HasKey(KEY_FEATURE_ACTIVE_TIME)) {
                string savedDate = PlayerPrefs.GetString(KEY_FEATURE_ACTIVE_TIME);
                DateTime timeNow = TimeNow;
                if (long.TryParse(savedDate, out long ticks)) {
                    _limitTime = new DateTime(ticks);
                    if (timeNow > _limitTime) {
                        //var totalMinutes = (timeNow - _limitTime).TotalMinutes;
                        //int lostStar = (int) (totalMinutes / _timeActivePerStreak) + 1;
                        byte lostStar = _currentStreak;
                        byte beforeStreak = _currentStreak;

                        _currentStreak -= lostStar;
                        PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
                        if (_currentStreak <= 0) {
                            ResetStreak(); //reset when too far from the time active
                        } else {
                            _limitTime = _limitTime.Add(TimeSpan.FromMinutes(_timeActivePerStreak * lostStar));
                            PlayerPrefs.SetString(KEY_FEATURE_ACTIVE_TIME, _limitTime.Ticks.ToString());
                            var remain = _limitTime - timeNow;
                            _tweenCountDown?.Kill();
                            _tweenCountDown = DOVirtual.DelayedCall((float) remain.TotalSeconds, ReduceStreak);
                            OnChangeState?.Invoke();
                        }

                        PowerCubeTracking.StreakLose(PowerCubeLoseRationale.by_time_up, beforeStreak, _currentStreak);
                    } else {
                        var totalMinutes = (_limitTime - timeNow).TotalMinutes;
                        if (totalMinutes >= _timeActivePerStreak) {
                            ResetStreak(); //reset when wrong data before
                        } else {
                            var remain = _limitTime - timeNow;
                            _tweenCountDown?.Kill();
                            _tweenCountDown = DOVirtual.DelayedCall((float) remain.TotalSeconds, ReduceStreak);
                            OnChangeState?.Invoke();
                        }
                    }
                } else {
                    ResetStreak(); //reset when wrong data before
                }
            } else {
                ResetStreak(); //reset when no data before
            }
        }
    }

    public static void SetActive(bool active) {
        isActive = isEnable && active;
    }

    public static int GetCurrentStreak() {
        if (!isEnable)
            return 0;
        if (!isInstanced)
            return 0;
        if (instance.isLocked)
            return 0;

        return instance._currentStreak;
    }

    public static void GameComplete(bool isWin) {
        if (!isEnable)
            return;
        if (!isInstanced)
            return;

        if (instance.isLocked && isWin) {
            instance.UpdateStatus();
            if (!instance.isLocked) {
                instance.ResetStreak(); //reset when first unlock
            }
        }

        if (instance.isLocked)
            return;

        instance.UpdateStreak(isWin);
    }

    private void UpdateStreak(bool isWin) {
        try {
            if (isWin) {
                _currentStreak++;
                if (_currentStreak > MaxStreak) {
                    _currentStreak = MaxStreak;
                }

                PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
                ResetTimeActive(); //when increase streak
                OnChangeState?.Invoke();
                PowerCubeTracking.StreakKeep(_currentStreak);
            } else {
                PowerCubeLoseRationale rationale = PowerCubeLoseRationale.by_song_fail;
                if (ChallengeMode.IsActive) {
                    rationale = GameController.enableEndless
                        ? PowerCubeLoseRationale.challenge_mode_fail
                        : PowerCubeLoseRationale.normal_mode_fail;
                }

                PowerCubeTracking.StreakLose(rationale, _currentStreak, 0);

                ResetStreak(); //reset when lose
            }

            //Logger.EditorLog(KEY_FEATURE, $"Update streak : {_currentStreak}");
        } catch (Exception e) {
            CustomException.Fire("[UpdateStreak]", "UpdateStreak => " + e.Message);
        }
    }

    private void ResetTimeActive() {
        _limitTime = TimeNow + TimeSpan.FromMinutes(_timeActivePerStreak);
        PlayerPrefs.SetString(KEY_FEATURE_ACTIVE_TIME, _limitTime.Ticks.ToString());
        _tweenCountDown?.Kill();
        _tweenCountDown = DOVirtual.DelayedCall(_timeActivePerStreak * 60, ReduceStreak);
    }

    private void ReduceStreak() {
        int beforeStreak = _currentStreak;
        _currentStreak = 0;
        if (_currentStreak <= 0) {
            _currentStreak = 0;
            PlayerPrefs.DeleteKey(KEY_FEATURE_ACTIVE_TIME);
        } else {
            ResetTimeActive(); //when decrease streak
        }

        PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
        PowerCubeTracking.StreakLose(PowerCubeLoseRationale.by_time_up, beforeStreak, _currentStreak);
        OnChangeState?.Invoke();
    }
    private void SetStreak(byte value) {
        _currentStreak = value;
        ResetTimeActive();
        PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
        OnChangeState?.Invoke();
    }

    private void ResetStreak() {
        _currentStreak = 0;
        _limitTime = TimeNow + TimeSpan.FromMinutes(_timeActivePerStreak);
        _tweenCountDown?.Kill();
        PlayerPrefs.SetInt(KEY_FEATURE_STREAK, _currentStreak);
        PlayerPrefs.DeleteKey(KEY_FEATURE_ACTIVE_TIME);
        OnChangeState?.Invoke();
    }

    private void UpdateStatus() {
        if (!isLocked || _unlockCondition == null)
            return;

        int songStart = UserProperties.GetPropertyInt(UserProperties.song_start);
        int star = Configuration.GetTotalStar();

        isLocked = star < _unlockCondition.totalStars || star < _unlockCondition.furtherTotalStars ||
                   songStart < _unlockCondition.totalSongStart || songStart < _unlockCondition.furtherTotalSongStart;

        if (!isLocked) {
            PlayerPrefs.SetInt(KEY_FEATURE_UNLOCK, 1);
        }
    }

    public List<BoosterType> GetRewardStreak() {
        return GetRewardStreak(_currentStreak);
    }

    public List<BoosterType> GetRewardStreak(int streak) {
        if (!isEnable)
            return null;

        return streak switch {
            0 => null,
            1 => _reward1.IsNullOrEmpty() ? ParseReward(RemoteConfigBase.instance.PowerCube_Streak1_Reward) : _reward1,
            2 => _reward2.IsNullOrEmpty() ? ParseReward(RemoteConfigBase.instance.PowerCube_Streak2_Reward) : _reward2,
            _ => _reward3.IsNullOrEmpty() ? ParseReward(RemoteConfigBase.instance.PowerCube_Streak3_Reward) : _reward3
        };
    }

    private List<BoosterType> ParseReward(string strReward) {
        List<BoosterType> result = new();
        if (string.IsNullOrEmpty(strReward))
            return result;

        var dataReward = strReward.Split(';');
        foreach (var data in dataReward) {
            if (string.IsNullOrEmpty(data))
                continue;

            if (Enum.TryParse(data, true, out BoosterType booster)) {
                result.Add(booster);
            }
        }

        return result;
    }
    public TimeSpan GetActiveTime() {
        return _limitTime - TimeNow;
    }

    public static BoosterType GetBoosterTypeToUnlock() {
        return GetBoosterByOnboardState(BoosterStorage.OnboardState.None);
    }

    public static BoosterType GetBoosterTypeToOnboard() {
        return GetBoosterByOnboardState(BoosterStorage.OnboardState.Unlocked);
    }

    public static BoosterType GetBoosterByOnboardState(BoosterStorage.OnboardState targetState) {
        if (!isInstanced) {
            return BoosterType.None;
        }

        var reward = instance.GetRewardStreak();
        if (reward != null) {
            foreach (var boosterType in reward) {
                var state = BoosterStorage.GetOnboardState(boosterType);
                if (state == targetState) {
                    return boosterType;
                }
            }
        }

        return BoosterType.None;
    }

    
    public static bool NeedOnboard() {
        if (!isInstanced) {
            return false;
        }

        if (RemoteConfigBase.instance.PowerCube_SkipTutorial) {
            return false;
        }

        if (!isEnable)
            return false;
        if (!isActive)
            return false;
        if (instance.isLocked)
            return false;
        if (instance.streak == 0)
            return false;

        return !PlayerPrefs.HasKey(KEY_FEATURE_ONBOARD);
    }

    public static void Onboard() {
        PlayerPrefs.SetInt(KEY_FEATURE_ONBOARD, 1);
        if (instance) {
            instance._inOnboarding = true;
        }
    }

    public static bool HasToast() {
        return instance && instance._inOnboarding;
    }

    public static void DoneShowToast() {
        PowerCubeTracking.StreakOnboarding(PowerCubeTracking.OnboardState.selection);
    }

    public static void SetupReward() {
        if (!isActive) {
            return;
        }

        List<BoosterType> reward = instance.GetRewardStreak();
        if (reward.IsNullOrEmpty()) {
            return;
        }

        foreach (var type in reward) {
            PowerCubeTracking.StreakReceive(type);
        }

        if (instance._inOnboarding) {
            instance._inOnboarding = false;
            PowerCubeTracking.StreakOnboarding(PowerCubeTracking.OnboardState.setup);
        }
    }

    public static void ForceStreak(byte streak) {
        if (!isEnable)
            return;
        if (!isActive)
            return;
        if (!isInstanced)
            return;

        if (streak == 0) {
            instance.ResetStreak();
        } else {
            instance.SetStreak(streak);
        }
    }

    public int GetStarRequired() {
        return Mathf.Max(_unlockCondition.totalStars, _unlockCondition.furtherTotalStars);
    }
}