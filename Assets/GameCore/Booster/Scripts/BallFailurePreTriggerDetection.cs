using UnityEngine;

public class BallFailurePreTriggerDetection : MonoBehaviour {
    private int _prevNodeId = -1;

    private void OnTriggerEnter(Collider other) {
        if (Ball.b == null || !Ball.b.isInvincible) {
            return;
        }

        if (other.CompareTag(GAMETAG.Ball)) {
            var ball = Ball.b;
            if (GameController.instance.game == GameStatus.LIVE) {
                var block = ball.destinationBlock;

                if (block.noteID == _prevNodeId) {
                    return;
                }

                _prevNodeId = block.noteID;
                ball.ProcessJumpOnAir(_prevNodeId);
                ball.CheckEndOfRoad(block, ball.CheckHumanEnd(_prevNodeId));
            }
        }
    }

    public void Active() {
        gameObject.SetActive(true);
    }

    public void Deactive() {
        gameObject.SetActive(false);
    }
}