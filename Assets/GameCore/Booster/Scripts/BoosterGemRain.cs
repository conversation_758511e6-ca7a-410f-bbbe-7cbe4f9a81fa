using System;
using DG.Tweening;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TilesHop.Cores.Boosters {
    public class BoosterGemRain : MonoBehaviour {
        [SerializeField] private Transform      transCache;
        [SerializeField] private ParticleSystem vfxGemRain;
        [SerializeField] private Animator       cloudAnimatorPrefab;
        [SerializeField] private Vector3        offsetDefault = new(0, 10, 30);

        [ShowInInspector] private float _duration;
        [ShowInInspector] private int   _maxTimeAppear = 1;
        [ShowInInspector] private int   _value         = 1;
        [ShowInInspector] private int   _maxAmount     = 1;

        [ShowInInspector] private float _dropInterval;
        [ShowInInspector] private float _halfDropInterval;
        [ShowInInspector] private int   _appearMoodChange;
        [ShowInInspector] private State _state = State.DeActive;
        [ShowInInspector] private float _countTime;
        [ShowInInspector] private float _targetTime;

        private Transform _targetFollow;
        private Vector3   _currentPos;
        private float     _spawnTime;
        private float     _rangeX;

        private                             int _curAmount;
        private                             int _moodCount;
        [ShowInInspector, ReadOnly] private int _dropAmount;

        private Animator _cloudFollowCamAnimator;

        public static event Action<bool> OnDone;

        private enum State {
            FadeIn,
            Active,
            FadeOut,
            DeActive,
            Disable
        }

        private void OnEnable() {
            Spawner.OnChangeMood += OnMoodChange;
        }

        private void OnDisable() {
            Spawner.OnChangeMood -= OnMoodChange;
        }

        private void Start() {
            _currentPos = offsetDefault;
        }

        private void Update() {
            if (GameController.instance.game == GameStatus.LIVE) {
                if (_state != State.DeActive && _state != State.Disable) {
                    _currentPos.z = _targetFollow.position.z + offsetDefault.z;
                    transCache.position = _currentPos;
                }

                if (_state == State.Active) {
                    CheckSpawnDiamond();
                    if (_countTime >= _targetTime || _dropAmount >= _maxAmount) {
                        Logger.EditorLog("Gemrain", $"Total gem gen: {_dropAmount} / {_maxAmount}");
                        OnDone?.Invoke(false);
                        ChangeState();
                    }

                    _countTime += Time.deltaTime;
                }
            }
        }

        private void OnMoodChange(int arg1, SkinSet arg2) {
            if (GameController.instance.game != GameStatus.LIVE)
                return;

            _moodCount++;
            if (_moodCount > _appearMoodChange) {
                if (_state == State.DeActive && _curAmount < _maxTimeAppear) {
                    OnDone?.Invoke(true);
                    ChangeState();
                }
            }
        }

        public void Init(Transform ballCache) {
            this._targetFollow = ballCache;
            _duration = RemoteConfigBase.instance.Booster_GemRain_Duration;
            _appearMoodChange = RemoteConfigBase.instance.Booster_GemRain_MoodChange;
            _value = RemoteConfigBase.instance.Booster_GemRain_ValueGem;
            _maxAmount = RemoteConfigBase.instance.Booster_GemRain_AmountGem;
            if (_maxAmount <= 0) {
                _maxAmount = 20;
            }

            if (_value <= 0) {
                _value = 1;
            }

            _dropInterval = _duration / _maxAmount;
            _halfDropInterval = _dropInterval / 2;

            _rangeX = RemoteConfigBase.instance.GetTileMaxPositionX();
        }

        public void ActiveFeature() {
            _moodCount = 1;
            _curAmount = 0;
            _currentPos.z = _targetFollow.position.z + offsetDefault.z;
            transCache.position = _currentPos;
            vfxGemRain.Stop();

            if (cloudAnimatorPrefab && !_cloudFollowCamAnimator) {
                _cloudFollowCamAnimator = Instantiate(cloudAnimatorPrefab, Follow.instance.transformCache);
            }

            if (_curAmount >= _maxTimeAppear) {
                _state = State.Disable;
            } else {
                _state = State.DeActive;
                _countTime = 0f;
                _targetTime = 20;
            }
        }

        private void CheckSpawnDiamond() {
            if (_spawnTime <= 0f) {
                SpawnDiamond();
                if (Random.value < 0.5f) {
                    _spawnTime += _dropInterval;
                } else {
                    _spawnTime += _halfDropInterval;
                }
            } else {
                _spawnTime -= Time.deltaTime;
            }
        }

        private bool SpawnDiamond() {
            if (_dropAmount >= _maxAmount) {
                return false;
            }

            float zPos = transCache.position.z;
            if (CreateDiamondAtPos(zPos, false)) {
                return true;
            }

            if (CreateDiamondAtPos(zPos + 3, false)) {
                return true;
            }

            if (CreateDiamondAtPos(zPos + 5, false)) {
                return true;
            }

            return CreateDiamondAtPos(zPos + Random.value * 12, true);
        }

        private bool CreateDiamondAtPos(float zPos, bool canFallOutSide) {
            Platform tileTarget = Spawner.s.CheckSpawnInTile(zPos);
            Diamond diamond = null;
            if (tileTarget) {
                diamond = Spawner.s.CreateDiamond(_value, CurrencyEarnSource.booster, tileTarget, zPos);
            } else if (canFallOutSide) {
                Vector3 initPos = Vector3.zero;
                initPos.x = (0.5f - Random.value) * _rangeX * 2.5f;
                initPos.y = -5;
                initPos.z = transCache.position.z + Random.value * 5;

                diamond = Spawner.s.CreateDiamond(_value, CurrencyEarnSource.booster, initPos);
            }

            if (diamond) {
                float timeMove = offsetDefault.z / (Ball.b.GetBalLSpeed() * Ball.b.timeScale) * 0.6f;
                // float timeMove = Ball.b.timeScale > 1 ? 0.2f / Ball.b.trustTime : 0.2f;
                diamond.DropFromCloud(offsetDefault.y, timeMove);
                _dropAmount += 1;
#if UNITY_EDITOR
                diamond.gameObject.name = $"GemRain_{_dropAmount.ToString()}";
#endif
                return true;
            } else {
                return false;
            }
        }

        private void ChangeState() {
            _countTime = 0f;
            switch (_state) {
                case State.Active:
                    _state = State.FadeOut;
                    _targetTime = 0.3f;
                    vfxGemRain.Stop();
                    DOVirtual.DelayedCall(_targetTime, ChangeState);
                    break;

                case State.FadeOut:
                    // vfxCloud.Stop();
                    ActivateCloudFollowCamAnimation(false);

                    if (_curAmount >= _maxTimeAppear) {
                        _state = State.Disable;
                    } else {
                        _state = State.DeActive;
                        _targetTime = 20f;
                    }

                    break;

                case State.DeActive:
                    _state = State.FadeIn;
                    _targetTime = 0.3f;
                    ActivateCloudFollowCamAnimation(true);
                    DOVirtual.DelayedCall(_targetTime, ChangeState);
                    break;

                case State.FadeIn:
                    vfxGemRain.Play();
                    _state = State.Active;
                    _targetTime = _duration;
                    _spawnTime = 0;
                    _curAmount++;
                    _dropAmount = 0;
                    Use();
                    break;
            }
        }

        private void Use() {
            //No longer is booster
            BoosterManager.UseItem(BoosterType.GemRain, _duration, false);
        }

        public void GameStop() {
            vfxGemRain?.Stop();
            ActivateCloudFollowCamAnimation(false);
            if(_cloudFollowCamAnimator != null) {
                Destroy(_cloudFollowCamAnimator.gameObject);
            }
            _cloudFollowCamAnimator = null;

            if (_curAmount >= _maxTimeAppear) {
                _state = State.Disable;
            } else {
                _state = State.DeActive;
                _countTime = 0f;
                _targetTime = 20f;
            }
        }

        private void ActivateCloudFollowCamAnimation(bool isShow) {
            _cloudFollowCamAnimator?.SetTrigger(Animator.StringToHash(isShow ? "start" : "end"));
        }
    }
}