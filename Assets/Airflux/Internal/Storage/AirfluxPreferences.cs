using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using JetBrains.Annotations;

namespace AirfluxSDK
{
    internal sealed class AirfluxPreferences : SharedPreferences, IAirfluxPreferences
    {
        protected override string PrefsName => "airflux-prefs";

        public AirfluxPreferences()
        {
            Init();
        }

        #region Key

        private const string KEY_EXTERNAL_USER_ID = "external_user_id";
        private const string KEY_LAST_ECPM_VALUE = "last_ecpm_value";
        private const string KEY_LAST_ECPM_CURRENCY = "last_ecpm_currency";
        private const string KEY_EVENT_COUNTS = "event_counts";
        private const string KEY_SESSION_SUM_SEC = "session_sum_sec";
        private const string KEY_SESSION_MAX_SEC = "session_max_sec";
        private const string KEY_EVENT_IAA_INTERSTITIAL_COUNTS = "event_iaa_interstitial_counts";
        private const string KEY_LAST_EVENT_IAA_INTERSTITIAL_TIMESTAMP = "last_event_iaa_interstitial_timestamp";
        private const string KEY_EVENT_IAA_REWARD_COUNTS = "event_iaa_reward_counts";
        private const string KEY_LAST_EVENT_IAA_REWARD_TIMESTAMP = "last_event_iaa_reward_timestamp";
        private const string KEY_EVENT_IAA_COUNTS = "event_iaa_counts";
        private const string KEY_LAST_EVENT_IAA_TIMESTAMP = "last_event_iaa_timestamp";
        private const string KEY_EVENT_IAP_COUNTS = "event_iap_counts";
        private const string KEY_LAST_EVENT_IAP_TIMESTAMP = "last_event_iap_timestamp";
        private const string KEY_SESSION_COUNTS = "session_counts";
        private const string KEY_MAX_LEVEL = "max_level";
        private const string KEY_CURRENT_LEVEL = "current_level";
        private const string KEY_CURRENT_HARD_CURRENCIES = "current_hard_currencies";
        private const string KEY_CURRENT_SOFT_CURRENCIES = "current_soft_currencies";
        private const string KEY_INSTALL_TIMESTAMP = "install_timestamp";
        private const string KEY_LAST_EVENT_TIMESTAMP = "last_event_timestamp";
        private const string KEY_CURRENT_SESSION_START_TIMESTAMP = "current_session_start_timestamp";
        private const string KEY_CURRENT_SESSION_STOP_TIMESTAMP = "current_session_stop_timestamp";
        private const string KEY_INFERENCE_ATTRIBUTES = "inferences_attributes";

        #endregion

        private const string EMPTY_JSON_STRING = "{}";
        private const string REGEX_KEY_STRING = "^[a-zA-Z_][a-zA-Z0-9_]*$";

        private void Init()
        {
            // 이미 초기화된 경우 중복 실행 방지
            if (Prefs.Count > 0) return;

            AirfluxLogger.Debug("Initialize started", this);

            // 초기화 시점의 UNIX 타임스탬프(밀리초 단위)
            string currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

            Save(() =>
            {
                Prefs[KEY_INSTALL_TIMESTAMP] = currentTimestamp;
                Prefs[KEY_CURRENT_SESSION_START_TIMESTAMP] = currentTimestamp;
                return true;
            });
        }

        /// <summary>
        /// 사용자의 External User Id
        /// </summary>
        [CanBeNull]
        public string ExternalUserId => GetString(KEY_EXTERNAL_USER_ID);

        public bool SetUserID([CanBeNull] string id)
        {
            return Save(() =>
            {
                if (id != null && id.Length > 1024)
                {
                    AirfluxLogger.Error($"SetUserID is ignored because of invalid id: id={{{id}}}", this);
                    return false;
                }

                Prefs[KEY_EXTERNAL_USER_ID] = id;
                return true;
            });
        }

        /// <summary>
        /// 마지막 광고 노출(eCPM) 수익
        /// </summary>
        public double? LastEcpmValue
        {
            get => GetDouble(KEY_LAST_ECPM_VALUE);
            set => SetDouble(KEY_LAST_ECPM_VALUE, value);
        }

        /// <summary>
        /// 마지막 광고 노출(eCPM) 수익의 통화
        /// </summary>
        [CanBeNull]
        public string LastEcpmCurrency
        {
            get => GetString(KEY_LAST_ECPM_CURRENCY);
            set => SetString(KEY_LAST_ECPM_CURRENCY, value);
        }

        /// <summary>
        /// 전체 이벤트 발생 횟수
        /// </summary>
        public long EventCounts
        {
            get => GetLong(KEY_EVENT_COUNTS, 0);
            set => SetLong(KEY_EVENT_COUNTS, value);
        }

        /// <summary>
        /// 누적 플레이 타임(초)
        /// </summary>
        public long SessionSumSec
        {
            get => GetLong(KEY_SESSION_SUM_SEC, 0);
            set => SetLong(KEY_SESSION_SUM_SEC, value);
        }

        /// <summary>
        /// 최대 세션 플레이 타임(초)	
        /// </summary>
        public long SessionMaxSec
        {
            get => GetLong(KEY_SESSION_MAX_SEC, 0);
            set => SetLong(KEY_SESSION_MAX_SEC, value);
        }

        /// <summary>
        /// In‑App Ad 이벤트 발생 횟수 (중간광고)
        /// </summary>
        public long EventIaaInterstitialCounts
        {
            get => GetLong(KEY_EVENT_IAA_INTERSTITIAL_COUNTS, 0);
            set => SetLong(KEY_EVENT_IAA_INTERSTITIAL_COUNTS, value);
        }

        /// <summary>
        /// 마지막 In‑App Ad 이벤트 발생 시간 (중간광고)
        /// </summary>
        public long? LastEventIaaInterstitialTimestamp
        {
            get => GetLong(KEY_LAST_EVENT_IAA_INTERSTITIAL_TIMESTAMP);
            set => SetLong(KEY_LAST_EVENT_IAA_INTERSTITIAL_TIMESTAMP, value);
        }

        /// <summary>
        /// In‑App Ad 이벤트 발생 횟수 (리워드 광고)
        /// </summary>
        public long EventIaaRewardCounts
        {
            get => GetLong(KEY_EVENT_IAA_REWARD_COUNTS, 0);
            set => SetLong(KEY_EVENT_IAA_REWARD_COUNTS, value);
        }

        /// <summary>
        /// 마지막 In‑App Ad 이벤트 발생 시간 (리워드 광고)
        /// </summary>
        public long? LastEventIaaRewardTimestamp
        {
            get => GetLong(KEY_LAST_EVENT_IAA_REWARD_TIMESTAMP);
            set => SetLong(KEY_LAST_EVENT_IAA_REWARD_TIMESTAMP, value);
        }

        /// <summary>
        /// In‑App Ad 이벤트 발생 횟수
        /// </summary>
        public long EventIaaCounts
        {
            get => GetLong(KEY_EVENT_IAA_COUNTS, 0);
            set => SetLong(KEY_EVENT_IAA_COUNTS, value);
        }

        /// <summary>
        /// 마지막 In‑App Ad 이벤트 발생 시간
        /// </summary>
        public long? LastEventIaaTimestamp
        {
            get => GetLong(KEY_LAST_EVENT_IAA_TIMESTAMP);
            set => SetLong(KEY_LAST_EVENT_IAA_TIMESTAMP, value);
        }

        /// <summary>
        /// In‑App Purchase 이벤트 발생 횟수	
        /// </summary>
        public long EventIapCounts
        {
            get => GetLong(KEY_EVENT_IAP_COUNTS, 0);
            set => SetLong(KEY_EVENT_IAP_COUNTS, value);
        }

        /// <summary>
        /// 마지막 In‑App Purchase 이벤트 발생 시간
        /// </summary>
        public long? LastEventIapTimestamp
        {
            get => GetLong(KEY_LAST_EVENT_IAP_TIMESTAMP);
            set => SetLong(KEY_LAST_EVENT_IAP_TIMESTAMP, value);
        }

        /// <summary>
        /// 세션이 몇번 진행되었는지	
        /// </summary>
        public long SessionCounts
        {
            get => GetLong(KEY_SESSION_COUNTS, 0);
            set => SetLong(KEY_SESSION_COUNTS, value);
        }

        /// <summary>
        /// 최대 레벨	
        /// </summary>
        public long? MaxLevel
        {
            get => GetLong(KEY_MAX_LEVEL);
            set => SetLong(KEY_MAX_LEVEL, value);
        }

        /// <summary>
        /// 현재 게임 진행 레벨	
        /// </summary>
        public long? CurrentLevel
        {
            get => GetLong(KEY_CURRENT_LEVEL);
            set => SetLong(KEY_CURRENT_LEVEL, value);
        }

        /// <summary>
        /// 현재 보유 하드커런시(유료 게임머니)
        /// </summary>
        public Dictionary<string, object> CurrentHardCurrencies =>
            GetDictionary(KEY_CURRENT_HARD_CURRENCIES, new Dictionary<string, object>());

        public bool SetHardCurrency(string name, string balance)
        {
            return Save(() =>
            {
                if (name.Length > 128 || new Regex(REGEX_KEY_STRING).IsMatch(name) == false)
                {
                    AirfluxLogger.Error(
                        $"SetHardCurrency is ignored because of invalid key: name={{{name}}}, balance={{{balance}}}",
                        this);
                    return false;
                }

                var cachedCurrentHardCurrencies = CurrentHardCurrencies;

                if (cachedCurrentHardCurrencies.Count >= 100)
                {
                    AirfluxLogger.Error(
                        $"SetHardCurrency is ignored because maximum count is exceed: name={{{name}}}, balance={{{balance}}}",
                        this);
                    return false;
                }

                cachedCurrentHardCurrencies[name] = balance;
                Prefs[KEY_CURRENT_HARD_CURRENCIES] = AirbridgeJson.Serialize(cachedCurrentHardCurrencies);
                return true;
            });
        }

        public void RemoveHardCurrency(string name)
        {
            Save(() =>
            {
                var cached = CurrentHardCurrencies;
                if (!cached.Remove(name)) return false;

                Prefs[KEY_CURRENT_HARD_CURRENCIES] = AirbridgeJson.Serialize(cached);
                return true;
            });
        }

        public void ClearHardCurrencies()
        {
            Save(() =>
            {
                Prefs[KEY_CURRENT_HARD_CURRENCIES] = EMPTY_JSON_STRING;
                return true;
            });
        }

        /// <summary>
        /// 현재 보유 소프트커런시(무료 게임머니)
        /// </summary>
        public Dictionary<string, object> CurrentSoftCurrencies =>
            GetDictionary(KEY_CURRENT_SOFT_CURRENCIES, new Dictionary<string, object>());

        public bool SetSoftCurrency(string name, string balance)
        {
            return Save(() =>
            {
                if (name.Length > 128 || new Regex(REGEX_KEY_STRING).IsMatch(name) == false)
                {
                    AirfluxLogger.Error(
                        $"SetSoftCurrency is ignored because of invalid key: name={{{name}}}, balance={{{balance}}}",
                        this);
                    return false;
                }

                var cachedCurrentSoftCurrencies = CurrentSoftCurrencies;

                if (cachedCurrentSoftCurrencies.Count >= 100)
                {
                    AirfluxLogger.Error(
                        $"SetSoftCurrency is ignored because maximum count is exceed: name={{{name}}}, balance={{{balance}}}",
                        this);
                    return false;
                }

                cachedCurrentSoftCurrencies[name] = balance;
                Prefs[KEY_CURRENT_SOFT_CURRENCIES] = AirbridgeJson.Serialize(cachedCurrentSoftCurrencies);
                return true;
            });
        }

        public void RemoveSoftCurrency(string name)
        {
            Save(() =>
            {
                var cached = CurrentSoftCurrencies;
                if (!cached.Remove(name)) return false;

                Prefs[KEY_CURRENT_SOFT_CURRENCIES] = AirbridgeJson.Serialize(cached);
                return true;
            });
        }

        public void ClearSoftCurrencies()
        {
            Save(() =>
            {
                Prefs[KEY_CURRENT_SOFT_CURRENCIES] = EMPTY_JSON_STRING;
                return true;
            });
        }

        /// <summary>
        /// 사용자 최초 유입/설치 시간대
        /// </summary>
        public long InstallTimestamp
        {
            get => GetLong(KEY_INSTALL_TIMESTAMP, DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
            set => SetLong(KEY_INSTALL_TIMESTAMP, value);
        }

        /// <summary>
        /// 마지막 이벤트 발생 시간
        /// </summary>
        public long? LastEventTimestamp
        {
            get => GetLong(KEY_LAST_EVENT_TIMESTAMP);
            set => SetLong(KEY_LAST_EVENT_TIMESTAMP, value);
        }

        public long CurrentSessionStartTimestamp
        {
            get => GetLong(KEY_CURRENT_SESSION_START_TIMESTAMP, DateTimeOffset.UtcNow.ToUnixTimeMilliseconds());
            set => SetLong(KEY_CURRENT_SESSION_START_TIMESTAMP, value);
        }

        public long? CurrentSessionStopTimestamp
        {
            get => GetLong(KEY_CURRENT_SESSION_STOP_TIMESTAMP);
            set => SetLong(KEY_CURRENT_SESSION_STOP_TIMESTAMP, value);
        }

        /// <summary>
        /// 추가 Attributed 정보
        /// </summary>
        public Dictionary<string, object> InferenceAttributes =>
            GetDictionary(KEY_INFERENCE_ATTRIBUTES, new Dictionary<string, object>());

        public bool SetInferenceAttribute(string key, object value)
        {
            return Save(() =>
            {
                if (key.Length > 128 || new Regex(REGEX_KEY_STRING).IsMatch(key) == false)
                {
                    AirfluxLogger.Error(
                        $"SetInferenceAttribute is ignored because of invalid key: key={{{key}}}, value={{{value}}}",
                        this);
                    return false;
                }

                if ((
                        (value is string stringValue && stringValue.Length <= 1024) ||
                        value.IsNumericType() ||
                        value is bool
                    ) == false)
                {
                    AirfluxLogger.Error(
                        $"SetInferenceAttribute is ignored because of invalid value: key={{{key}}}, value={{{value}}}",
                        this);
                    return false;
                }

                var cachedInferenceAttributes = InferenceAttributes;

                if (cachedInferenceAttributes.Count >= 100)
                {
                    AirfluxLogger.Error(
                        $"SetInferenceAttribute is ignored because maximum count is exceed: key={{{key}}}, value={{{value}}}",
                        this);
                    return false;
                }

                cachedInferenceAttributes[key] = value;
                Prefs[KEY_INFERENCE_ATTRIBUTES] = AirbridgeJson.Serialize(cachedInferenceAttributes);
                return true;
            });
        }

        public void RemoveInferenceAttribute(string key)
        {
            Save(() =>
            {
                var cached = InferenceAttributes;
                if (!cached.Remove(key)) return false;

                Prefs[KEY_INFERENCE_ATTRIBUTES] = AirbridgeJson.Serialize(cached);
                return true;
            });
        }

        public void ClearInferenceAttributes()
        {
            Save(() =>
            {
                Prefs[KEY_INFERENCE_ATTRIBUTES] = EMPTY_JSON_STRING;
                return true;
            });
        }
    }
}