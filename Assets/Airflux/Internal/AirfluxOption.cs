using System.Collections.Generic;

namespace AirfluxSDK
{
	// Exclude a class from the document
	/// @cond HIDDEN_SYMBOLS
	public class AirfluxOption
	{
		internal static readonly string Token = "d46f60d7360e488fab4eda2fcc1c7423";
		internal static readonly long SessionTimeoutSeconds = 300;
		internal static readonly bool CountryAllowAll = true;
		internal static readonly List<string> CountryAllowlist = new List<string>() {  };
	}
	// ReSharper disable once InvalidXmlDocComment
	/// @endcond
}
