using System.Collections.Generic;
using UnityEngine;

namespace AirfluxSDK
{
    internal class AirfluxGameObjectManager
    {
        public enum GameObjectType
        {
            Default
        }

        private static readonly Dictionary<GameObjectType, GameObject> GameObjectDictionary = new Dictionary<GameObjectType, GameObject>();

        public static bool ContainsGameObject(GameObjectType gameObjectType)
        {
            return GameObjectDictionary.ContainsKey(gameObjectType);
        }

        public static GameObject GetGameObject(GameObjectType gameObjectType)
        {
            return ContainsGameObject(gameObjectType)
                ? GameObjectDictionary[gameObjectType].gameObject
                : CreateGameObject(gameObjectType);
        }

        private static GameObject CreateGameObject(GameObjectType gameObjectType)
        {
            GameObject gameObject = new GameObject();
            gameObject.name = $"Airflux-{gameObjectType.ToString()}";
            GameObject.DontDestroyOnLoad(gameObject);
            GameObjectDictionary.Add(gameObjectType, gameObject);

            return gameObject;
        }
    }
}