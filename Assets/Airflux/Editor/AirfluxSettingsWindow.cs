#if UNITY_EDITOR

using System;
using System.IO;
using AirfluxSDK;
using UnityEditor;
using UnityEngine;

class AirfluxSettingsWindow : EditorWindow
{
    private readonly AirbridgeArrayDataHandler _countryAllowlistHandler = new AirbridgeArrayDataHandler(
        label: ArrayData.COUNTRY_ALLOWLIST_LABEL,
        elementLabel: ArrayData.COUNTRY_ALLOWLIST_ELEMENT_LABEL,
        separator: ArrayData.COUNTRY_ALLOWLIST_SEPARATOR
    );
    
    [MenuItem("Airflux/Airflux Settings")]
    public static void ShowWindow()
    {
        EditorWindow window = GetWindow(typeof(AirfluxSettingsWindow), true, "Airflux Settings");

        // Set minimum size
        // width: 740, height: 200
        window.minSize = new Vector2(740, 200);
    }

    /// <summary>
    /// Modify Airbridge Settings
    /// </summary>
    private void OnEnable()
    {
        // 아래 옵션은 고정값으로 에어브릿지 옵션에 바이패스
        AirbridgeData.GetInstance().iOSTrackingAuthorizeTimeoutSeconds = 0;

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    private void OnGUI()
    {
        SerializedObject airbridgeSerializedObject = new SerializedObject(AirbridgeData.GetInstance());
        SerializedObject airfluxSerializedObject = new SerializedObject(AirfluxData.GetInstance());
        
        EditorGUIUtility.labelWidth = 400;
        EditorGUIUtility.fieldWidth = 800;

        #region Airbridge

        SerializedProperty appNameProperty = airbridgeSerializedObject.FindProperty(AirbridgeProperty.APP_NAME);
        EditorGUILayout.PropertyField(appNameProperty, new GUILayoutOption[] { });

        SerializedProperty appTokenProperty = airbridgeSerializedObject.FindProperty(AirbridgeProperty.APP_TOKEN);
        EditorGUILayout.PropertyField(appTokenProperty, new GUILayoutOption[] { });

        SerializedProperty logLevel = airbridgeSerializedObject.FindProperty(AirbridgeProperty.LOG_LEVEL);
        logLevel.intValue = EditorGUILayout.Popup("Log Level", logLevel.intValue, AirbridgeLogLevel.LogLevel);

        SerializedProperty sessionTimeoutSecondsProperty = airbridgeSerializedObject.FindProperty(AirbridgeProperty.SESSION_TIMEOUT_SECONDS);
        EditorGUILayout.PropertyField(sessionTimeoutSecondsProperty, new GUILayoutOption[] { });

        SerializedProperty autoStartTrackingEnabledProperty = airbridgeSerializedObject.FindProperty(AirbridgeProperty.AUTO_START_TRACKING_ENABLED);
        EditorGUILayout.PropertyField(autoStartTrackingEnabledProperty, new GUILayoutOption[] { });

        SerializedProperty sdkEnabledProperty = airbridgeSerializedObject.FindProperty(AirbridgeProperty.SDK_ENABLED);
        EditorGUILayout.PropertyField(sdkEnabledProperty, new GUIContent("SDK Enabled"), new GUILayoutOption[] { });

        #endregion

        #region Airflux

        GUI.SetNextControlName(AirfluxProperty.COUNTRY_ALLOW_ALL);
        SerializedProperty countryAllowAllProperty = airfluxSerializedObject.FindProperty(AirfluxProperty.COUNTRY_ALLOW_ALL);
        EditorGUILayout.PropertyField(countryAllowAllProperty, new GUILayoutOption[] { });

        DrawCountryAllowlist(airfluxSerializedObject, countryAllowAllProperty.boolValue);

        #endregion
        
        EditorGUILayout.Space();

        if (GUI.changed)
        {
            airbridgeSerializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(AirbridgeData.GetInstance());

            airfluxSerializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(AirfluxData.GetInstance());

            AssetDatabase.SaveAssets();
        }
    }

    private void DrawCountryAllowlist(SerializedObject serializedObject, bool countryAllowAll)
    {
        EditorGUI.BeginDisabledGroup(countryAllowAll);

        SerializedProperty countryAllowlistProperty = serializedObject.FindProperty(AirfluxProperty.COUNTRY_ALLOWLIST_IN_STRING);
        _countryAllowlistHandler.SetProperty(countryAllowlistProperty);
        _countryAllowlistHandler.Draw();

        EditorGUI.EndDisabledGroup();

        if (countryAllowAll)
        {
            EditorGUILayout.HelpBox(
                "Cannot edit Country Allowlist when 'Country Allow All' is enabled.",
                MessageType.Warning
            );
        }
    }

    internal static void UpdateAirfluxSettings()
    {
        try
        {
            string settingsPath = Path.Combine(
                AirbridgeUtils.GetUnityPackageAssetsPath(),
                "Airflux/Internal",
                "AirfluxOption.cs"
            );

            if (!File.Exists(settingsPath))
            {
                File.Create(settingsPath).Dispose();
            }

            string content =
                "using System.Collections.Generic;\n"
                + "\n"
                + "namespace AirfluxSDK\n"
                + "{\n"
                + "\t// Exclude a class from the document\n"
                + "\t/// @cond HIDDEN_SYMBOLS\n"
                + "\tpublic class AirfluxOption\n"
                + "\t{\n"
                + "\t\tinternal static readonly string Token = \""
                + AirbridgeData.GetInstance().appToken + "\";\n"
                + "\t\tinternal static readonly long SessionTimeoutSeconds = "
                + AirbridgeData.GetInstance().sessionTimeoutSeconds + ";\n"
                + "\t\tinternal static readonly bool CountryAllowAll = "
                + AirfluxData.GetInstance().countryAllowAll.ToString().ToLower() + ";\n"
                + "\t\tinternal static readonly List<string> CountryAllowlist = "
                + AirfluxEditorUtil.ListToString(AirfluxEditorUtil.MergeArrayData(
                    str: AirfluxData.GetInstance().countryAllowlistInString,
                    list: AirfluxData.GetInstance().countryAllowlist,
                    separator: ArrayData.COUNTRY_ALLOWLIST_SEPARATOR
                ))
                + "\t}\n"
                + "\t// ReSharper disable once InvalidXmlDocComment\n"
                + "\t/// @endcond\n"
                + "}\n";

            File.WriteAllText(settingsPath, content);

            Debug.Log("Updated Airflux settings (AirfluxOption.cs)");
        }
        catch (Exception exception)
        {
            Debug.LogErrorFormat("Something broken while updating Airflux settings file : {0}", exception);
        }
    }
}

#endif