using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

#if UNITY_EDITOR

partial class AirfluxEditorUtil
{
    internal static string SerializeString(string str)
    {
        StringBuilder builder = new StringBuilder();
        builder.Append('\"');

        char[] charArray = str.ToCharArray();
        foreach (var c in charArray)
        {
            switch (c)
            {
                case '"':
                    builder.Append("\\\"");
                    break;
                case '\\':
                    builder.Append("\\\\");
                    break;
                case '\b':
                    builder.Append("\\b");
                    break;
                case '\f':
                    builder.Append("\\f");
                    break;
                case '\n':
                    builder.Append("\\n");
                    break;
                case '\r':
                    builder.Append("\\r");
                    break;
                case '\t':
                    builder.Append("\\t");
                    break;
                default:
                    int codepoint = Convert.ToInt32(c);
                    if ((codepoint >= 32) && (codepoint <= 126))
                    {
                        builder.Append(c);
                    }
                    else
                    {
                        builder.Append("\\u");
                        builder.Append(codepoint.ToString("x4"));
                    }

                    break;
            }
        }

        builder.Append('\"');
        return builder.ToString();
    }
    
    internal static string ListToString(List<string> list)
    {
        StringBuilder builder = new StringBuilder();
        builder.Append("new List<string>() { ");
        builder.Append(string.Join(", ", list.Select(SerializeString)));
        builder.Append(" };\n");
        return builder.ToString();
    }
}

#endif