using Newtonsoft.Json;
using System;
using Balancy.Localization;

namespace Balancy.Models.GroupOffer
{
#pragma warning disable 649

	public class EndlessOffer : GroupOfferType
	{

		[JsonProperty]
		private string unnyIdDisplayHomeIcon;
		[JsonProperty]
		private string[] unnyIdDataTokenMilestone;


		[JsonProperty("assetLine")]
		public readonly UnnyObject AssetLine;

		[JsonProperty("assetButton")]
		public readonly UnnyObject AssetButton;

		[JsonProperty("assetLockIcon")]
		public readonly UnnyObject AssetLockIcon;

		[JsonProperty("assetChest")]
		public readonly UnnyObject AssetChest;

		[JsonProperty("cellAnimaion")]
		public readonly UnnyObject CellAnimaion;

		[JsonIgnore]
		public Models.HomeIcon DisplayHomeIcon => DataEditor.GetModelByUnnyId<Models.HomeIcon>(unnyIdDisplayHomeIcon);

		[JsonProperty("assetTitle")]
		public readonly UnnyObject AssetTitle;

		[JsonProperty("assetBar")]
		public readonly UnnyObject AssetBar;

		[JsonProperty("assetBarBG")]
		public readonly UnnyObject AssetBarBG;

		[JsonProperty("assetCountdown")]
		public readonly UnnyObject AssetCountdown;

		[JsonProperty("banner")]
		public readonly UnnyObject Banner;

		[JsonProperty("assetArrow")]
		public readonly UnnyObject AssetArrow;

		[JsonIgnore]
		public Models.MilestoneWithReward[] DataTokenMilestone
		{
			get
			{
				if (unnyIdDataTokenMilestone == null)
					return new Models.MilestoneWithReward[0];
				var dataTokenMilestone = new Models.MilestoneWithReward[unnyIdDataTokenMilestone.Length];
				for (int i = 0;i < unnyIdDataTokenMilestone.Length;i++)
					dataTokenMilestone[i] = DataEditor.GetModelByUnnyId<Models.MilestoneWithReward>(unnyIdDataTokenMilestone[i]);
				return dataTokenMilestone;
			}
		}

		[JsonProperty("subText"), JsonConverter(typeof(LocalizedStringConverter))]
		public readonly LocalizedString SubText;

		[JsonProperty("claimPreviousText"), JsonConverter(typeof(LocalizedStringConverter))]
		public readonly LocalizedString ClaimPreviousText;

		[JsonProperty("assetLockedCell")]
		public readonly UnnyObject AssetLockedCell;

		[JsonProperty("assetToken")]
		public readonly UnnyObject AssetToken;

		[JsonProperty("assetUnlockedCell")]
		public readonly UnnyObject AssetUnlockedCell;

		[JsonProperty("assetLockedCellToken")]
		public readonly UnnyObject AssetLockedCellToken;

		[JsonProperty("assetCountdownBg")]
		public readonly UnnyObject AssetCountdownBg;

		[JsonProperty("assetUnlockedCellToken")]
		public readonly UnnyObject AssetUnlockedCellToken;

	}
#pragma warning restore 649
}