using Newtonsoft.Json;
using System;

namespace Balancy.Models
{
#pragma warning disable 649

	public class HomeIcon : BaseModel
	{



		[Json<PERSON>roperty("homeIconAsset")]
		public readonly UnnyObject HomeIconAsset;

		[JsonProperty("homeIconAnimation")]
		public readonly UnnyObject HomeIconAnimation;

		[JsonProperty("displayCountdown")]
		public readonly bool DisplayCountdown;

		[JsonProperty("position")]
		public readonly Models.HomeIconPosition Position;

	}
#pragma warning restore 649
}